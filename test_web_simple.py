# -*- coding: utf-8 -*-
"""
Web管理界面简化测试

测试修复后的Web管理界面基本功能
"""

import sys
import os
import time
import logging
import requests
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_web_interface():
    """测试Web管理界面基本功能"""
    logger.info("开始Web管理界面简化测试")
    
    # 启动Web服务器
    try:
        from qlib_trading_system.web.app import app
        import threading
        import uvicorn
        
        def run_server():
            uvicorn.run(app, host="127.0.0.1", port=8001, log_level="error")
        
        server_thread = threading.Thread(target=run_server, daemon=True)
        server_thread.start()
        
        logger.info("Web服务器启动成功")
        time.sleep(3)  # 等待服务器启动
        
    except Exception as e:
        logger.error(f"启动Web服务器失败: {e}")
        return False
    
    base_url = "http://127.0.0.1:8001"
    session = requests.Session()
    
    try:
        # 测试登录功能
        logger.info("测试用户登录功能...")
        login_data = {"username": "admin", "password": "admin123"}
        response = session.post(f"{base_url}/auth/login", data=login_data)
        
        if response.status_code == 200:
            result = response.json()
            logger.info(f"登录成功: {result.get('message', '')}")
            
            # 测试获取用户信息
            logger.info("测试获取用户信息...")
            response = session.get(f"{base_url}/auth/me")
            if response.status_code == 200:
                user_info = response.json()
                logger.info(f"用户信息获取成功: {user_info['user']['username']}")
            else:
                logger.error(f"获取用户信息失败: {response.status_code}")
            
            # 测试配置API
            logger.info("测试配置管理API...")
            response = session.get(f"{base_url}/api/config/all")
            if response.status_code == 200:
                logger.info("配置获取成功")
            else:
                logger.error(f"配置获取失败: {response.status_code}")
            
            # 测试交易API
            logger.info("测试交易监控API...")
            response = session.get(f"{base_url}/api/trading/status")
            if response.status_code == 200:
                logger.info("交易状态获取成功")
            else:
                logger.error(f"交易状态获取失败: {response.status_code}")
            
            # 测试数据查询API
            logger.info("测试数据查询API...")
            response = session.get(f"{base_url}/api/data/market/overview")
            if response.status_code == 200:
                logger.info("市场概览获取成功")
            else:
                logger.error(f"市场概览获取失败: {response.status_code}")
            
            # 测试监控API
            logger.info("测试系统监控API...")
            response = session.get(f"{base_url}/api/monitoring/system")
            if response.status_code == 200:
                logger.info("系统监控数据获取成功")
            else:
                logger.error(f"系统监控数据获取失败: {response.status_code}")
            
            # 测试登出
            logger.info("测试用户登出...")
            response = session.post(f"{base_url}/auth/logout")
            if response.status_code == 200:
                logger.info("登出成功")
            else:
                logger.error(f"登出失败: {response.status_code}")
            
            logger.info("Web管理界面基本功能测试完成")
            return True
            
        else:
            logger.error(f"登录失败: {response.status_code}")
            if response.status_code == 500:
                try:
                    error_detail = response.json()
                    logger.error(f"错误详情: {error_detail}")
                except:
                    logger.error(f"响应内容: {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        return False
    finally:
        session.close()

if __name__ == "__main__":
    success = test_web_interface()
    if success:
        print("✅ Web管理界面测试通过")
    else:
        print("❌ Web管理界面测试失败")