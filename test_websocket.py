"""
WebSocket功能测试
WebSocket Functionality Test

测试WebSocket实时通信功能
"""

import asyncio
import json
import logging
import websockets
from datetime import datetime

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_websocket_connection():
    """测试WebSocket连接"""
    logger.info("测试WebSocket连接")
    
    try:
        # 连接WebSocket
        uri = "ws://localhost:8000/ws"
        
        async with websockets.connect(uri) as websocket:
            logger.info("WebSocket连接建立成功")
            
            # 发送测试消息
            test_message = {
                "type": "heartbeat",
                "data": {"message": "test heartbeat"}
            }
            
            await websocket.send(json.dumps(test_message))
            logger.info("发送测试消息成功")
            
            # 接收响应
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                response_data = json.loads(response)
                logger.info(f"收到响应: {response_data}")
                
                # 验证响应格式
                assert "type" in response_data, "响应缺少type字段"
                assert "timestamp" in response_data, "响应缺少timestamp字段"
                
                logger.info("✅ WebSocket连接测试通过")
                return True
                
            except asyncio.TimeoutError:
                logger.warning("WebSocket响应超时，但连接成功")
                return True
                
    except Exception as e:
        logger.error(f"❌ WebSocket连接测试失败: {e}")
        return False


async def main():
    """主函数"""
    logger.info("开始WebSocket功能测试")
    
    # 测试WebSocket连接
    websocket_result = await test_websocket_connection()
    
    # 生成测试报告
    report = {
        "websocket_test": {
            "passed": websocket_result,
            "timestamp": datetime.now().isoformat()
        }
    }
    
    with open("websocket_test_report.json", 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    logger.info("=" * 50)
    logger.info("WebSocket测试报告")
    logger.info("=" * 50)
    logger.info(f"WebSocket连接测试: {'通过' if websocket_result else '失败'}")
    logger.info("=" * 50)


if __name__ == "__main__":
    asyncio.run(main())
