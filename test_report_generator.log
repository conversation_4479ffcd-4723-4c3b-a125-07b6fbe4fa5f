2025-07-31 10:25:18,571 - qlib_trading_system.monitoring.report_generator - INFO - 报告生成器初始化完成
2025-07-31 10:25:18,577 - qlib_trading_system.monitoring.report_generator - INFO - 报告生成器初始化完成
2025-07-31 10:25:18,577 - qlib_trading_system.monitoring.report_generator - INFO - 生成日度报告: 2024-01-15
2025-07-31 10:25:18,578 - __main__ - ERROR - 日度报告生成测试失败: 'ReportGenerator' object has no attribute '_generate_daily_charts'
Traceback (most recent call last):
  File "E:\work\test\ai\test_report_generator.py", line 172, in test_daily_report
    report_paths = generator.generate_daily_report(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\work\test\ai\qlib_trading_system\monitoring\report_generator.py", line 200, in generate_daily_report
    chart_paths = self._generate_daily_charts(daily_data, report_date)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'ReportGenerator' object has no attribute '_generate_daily_charts'
2025-07-31 10:25:18,582 - qlib_trading_system.monitoring.report_generator - INFO - 报告生成器初始化完成
2025-07-31 10:25:18,584 - qlib_trading_system.monitoring.report_generator - INFO - 生成周度报告: 2024-01-15 至 2024-01-21
2025-07-31 10:25:18,584 - __main__ - ERROR - 周度报告生成测试失败: 'ReportGenerator' object has no attribute '_process_weekly_data'
Traceback (most recent call last):
  File "E:\work\test\ai\test_report_generator.py", line 210, in test_weekly_report
    report_paths = generator.generate_weekly_report(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\work\test\ai\qlib_trading_system\monitoring\report_generator.py", line 245, in generate_weekly_report
    weekly_data = self._process_weekly_data(week_start, week_end)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'ReportGenerator' object has no attribute '_process_weekly_data'
2025-07-31 10:25:18,588 - qlib_trading_system.monitoring.report_generator - INFO - 报告生成器初始化完成
2025-07-31 10:25:18,588 - qlib_trading_system.monitoring.report_generator - INFO - 生成月度报告: 2024年1月
2025-07-31 10:25:18,589 - __main__ - ERROR - 月度报告生成测试失败: 'ReportGenerator' object has no attribute '_process_monthly_data'
Traceback (most recent call last):
  File "E:\work\test\ai\test_report_generator.py", line 245, in test_monthly_report
    report_paths = generator.generate_monthly_report(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\work\test\ai\qlib_trading_system\monitoring\report_generator.py", line 293, in generate_monthly_report
    monthly_data = self._process_monthly_data(year, month)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'ReportGenerator' object has no attribute '_process_monthly_data'
2025-07-31 10:25:18,591 - qlib_trading_system.monitoring.report_generator - INFO - 报告生成器初始化完成
2025-07-31 10:25:18,593 - qlib_trading_system.monitoring.report_generator - INFO - 生成策略执行报告: explosive_stock_ai_v1 (2024-01-01 至 2024-01-31)
2025-07-31 10:25:18,593 - __main__ - ERROR - 策略执行报告生成测试失败: 'ReportGenerator' object has no attribute '_process_strategy_data'
Traceback (most recent call last):
  File "E:\work\test\ai\test_report_generator.py", line 280, in test_strategy_report
    report_paths = generator.generate_strategy_execution_report(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\work\test\ai\qlib_trading_system\monitoring\report_generator.py", line 343, in generate_strategy_execution_report
    strategy_data = self._process_strategy_data(strategy_id, start_date, end_date)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'ReportGenerator' object has no attribute '_process_strategy_data'
2025-07-31 10:25:18,597 - qlib_trading_system.monitoring.report_generator - INFO - 报告生成器初始化完成
2025-07-31 10:25:18,597 - __main__ - ERROR - 报告模板配置测试失败: 'ReportGenerator' object has no attribute 'get_report_template_config'
Traceback (most recent call last):
  File "E:\work\test\ai\test_report_generator.py", line 313, in test_report_template_config
    template_config = generator.get_report_template_config()
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'ReportGenerator' object has no attribute 'get_report_template_config'
2025-07-31 10:25:18,601 - qlib_trading_system.monitoring.report_generator - INFO - 报告生成器初始化完成
2025-07-31 10:25:18,601 - qlib_trading_system.monitoring.report_generator - INFO - 生成日度报告: 2024-01-15
2025-07-31 10:25:18,602 - __main__ - ERROR - 数据导出测试失败: 'ReportGenerator' object has no attribute '_generate_daily_charts'
Traceback (most recent call last):
  File "E:\work\test\ai\test_report_generator.py", line 343, in test_data_export
    report_paths = generator.generate_daily_report(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\work\test\ai\qlib_trading_system\monitoring\report_generator.py", line 200, in generate_daily_report
    chart_paths = self._generate_daily_charts(daily_data, report_date)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'ReportGenerator' object has no attribute '_generate_daily_charts'
2025-07-31 11:22:11,790 - qlib_trading_system.monitoring.report_generator_system - INFO - 报告生成器初始化完成
2025-07-31 11:22:11,795 - qlib_trading_system.monitoring.report_generator_system - INFO - 报告生成器初始化完成
2025-07-31 11:22:11,795 - qlib_trading_system.monitoring.report_generator_system - INFO - 生成日度报告: 2024-01-15
2025-07-31 11:22:13,346 - qlib_trading_system.monitoring.report_generator_system - INFO - 日度图表生成完成: test_reports/charts/daily_analysis_20240115.png
2025-07-31 11:22:13,347 - __main__ - ERROR - 日度报告生成测试失败: 'ReportGenerator' object has no attribute '_generate_daily_html_report'
Traceback (most recent call last):
  File "E:\work\test\ai\test_report_generator.py", line 173, in test_daily_report
    report_paths = generator.generate_daily_report(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\work\test\ai\qlib_trading_system\monitoring\report_generator_system.py", line 903, in generate_daily_report
    report_paths['html'] = self._generate_daily_html_report(daily_data, chart_paths, report_date)
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'ReportGenerator' object has no attribute '_generate_daily_html_report'
2025-07-31 11:22:13,354 - qlib_trading_system.monitoring.report_generator_system - INFO - 报告生成器初始化完成
2025-07-31 11:22:13,354 - qlib_trading_system.monitoring.report_generator_system - INFO - 生成周度报告: 2024-01-15 至 2024-01-21
2025-07-31 11:22:13,355 - __main__ - ERROR - 周度报告生成测试失败: 'ReportGenerator' object has no attribute '_generate_weekly_charts'
Traceback (most recent call last):
  File "E:\work\test\ai\test_report_generator.py", line 211, in test_weekly_report
    report_paths = generator.generate_weekly_report(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\work\test\ai\qlib_trading_system\monitoring\report_generator_system.py", line 946, in generate_weekly_report
    chart_paths = self._generate_weekly_charts(weekly_data, week_start, week_end)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'ReportGenerator' object has no attribute '_generate_weekly_charts'
2025-07-31 11:22:13,360 - qlib_trading_system.monitoring.report_generator_system - INFO - 报告生成器初始化完成
2025-07-31 11:22:13,360 - qlib_trading_system.monitoring.report_generator_system - INFO - 生成月度报告: 2024年1月
2025-07-31 11:22:13,361 - __main__ - ERROR - 月度报告生成测试失败: 'ReportGenerator' object has no attribute '_generate_monthly_charts'
Traceback (most recent call last):
  File "E:\work\test\ai\test_report_generator.py", line 246, in test_monthly_report
    report_paths = generator.generate_monthly_report(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\work\test\ai\qlib_trading_system\monitoring\report_generator_system.py", line 994, in generate_monthly_report
    chart_paths = self._generate_monthly_charts(monthly_data, year, month)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'ReportGenerator' object has no attribute '_generate_monthly_charts'
2025-07-31 11:22:13,366 - qlib_trading_system.monitoring.report_generator_system - INFO - 报告生成器初始化完成
2025-07-31 11:22:13,366 - qlib_trading_system.monitoring.report_generator_system - INFO - 生成策略执行报告: explosive_stock_ai_v1 (2024-01-01 至 2024-01-31)
2025-07-31 11:22:13,367 - __main__ - ERROR - 策略执行报告生成测试失败: 'ReportGenerator' object has no attribute '_generate_strategy_charts'
Traceback (most recent call last):
  File "E:\work\test\ai\test_report_generator.py", line 281, in test_strategy_report
    report_paths = generator.generate_strategy_execution_report(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\work\test\ai\qlib_trading_system\monitoring\report_generator_system.py", line 1044, in generate_strategy_execution_report
    chart_paths = self._generate_strategy_charts(strategy_data, strategy_id, start_date, end_date)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'ReportGenerator' object has no attribute '_generate_strategy_charts'
2025-07-31 11:22:13,377 - qlib_trading_system.monitoring.report_generator_system - INFO - 报告生成器初始化完成
2025-07-31 11:22:13,377 - __main__ - ERROR - 报告模板配置测试失败: 'ReportGenerator' object has no attribute 'get_report_template_config'
Traceback (most recent call last):
  File "E:\work\test\ai\test_report_generator.py", line 314, in test_report_template_config
    template_config = generator.get_report_template_config()
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'ReportGenerator' object has no attribute 'get_report_template_config'
2025-07-31 11:22:13,382 - qlib_trading_system.monitoring.report_generator_system - INFO - 报告生成器初始化完成
2025-07-31 11:22:13,383 - qlib_trading_system.monitoring.report_generator_system - INFO - 生成日度报告: 2024-01-15
2025-07-31 11:22:14,578 - qlib_trading_system.monitoring.report_generator_system - INFO - 日度图表生成完成: test_reports/charts/daily_analysis_20240115.png
2025-07-31 11:22:14,578 - __main__ - ERROR - 数据导出测试失败: 'ReportGenerator' object has no attribute '_generate_daily_html_report'
Traceback (most recent call last):
  File "E:\work\test\ai\test_report_generator.py", line 505, in test_data_export
    report_paths = generator.generate_daily_report(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\work\test\ai\qlib_trading_system\monitoring\report_generator_system.py", line 903, in generate_daily_report
    report_paths['html'] = self._generate_daily_html_report(daily_data, chart_paths, report_date)
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'ReportGenerator' object has no attribute '_generate_daily_html_report'
2025-07-31 11:22:14,586 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 增强版报告生成器初始化完成
2025-07-31 11:22:14,588 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义模板创建成功: custom_20250731_112214
2025-07-31 11:22:16,277 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义图表生成完成: 2个图表
2025-07-31 11:22:16,283 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义HTML报告生成完成: test_reports_enhanced/custom/custom_report_custom_20250731_112214_20250731_112216.html
2025-07-31 11:22:16,284 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义JSON报告生成完成: test_reports_enhanced/custom/custom_report_custom_20250731_112214_20250731_112216.json
2025-07-31 11:22:16,284 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义报告生成完成: custom_20250731_112214, 格式: ['html', 'json']
2025-07-31 11:22:16,287 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 导出配置创建成功: export_20250731_112216
2025-07-31 11:22:16,291 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 加载了 1 个自定义模板
2025-07-31 11:22:16,292 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 增强版报告生成器初始化完成
2025-07-31 11:22:16,293 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义模板创建成功: custom_20250731_112216
2025-07-31 11:22:16,294 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义模板更新成功: custom_20250731_112216
2025-07-31 11:22:16,296 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义模板删除成功: custom_20250731_112216
2025-07-31 11:22:16,302 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 加载了 1 个自定义模板
2025-07-31 11:22:16,343 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 增强版报告生成器初始化完成
2025-07-31 11:22:16,356 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 报告汇总生成完成: 2024-01-01 至 2024-01-31
2025-07-31 11:24:03,668 - qlib_trading_system.monitoring.report_generator_system - INFO - 报告生成器初始化完成
2025-07-31 11:24:03,673 - qlib_trading_system.monitoring.report_generator_system - INFO - 报告生成器初始化完成
2025-07-31 11:24:03,674 - qlib_trading_system.monitoring.report_generator_system - INFO - 生成日度报告: 2024-01-15
2025-07-31 11:24:05,080 - qlib_trading_system.monitoring.report_generator_system - INFO - 日度图表生成完成: test_reports/charts/daily_analysis_20240115.png
2025-07-31 11:24:05,081 - __main__ - ERROR - 日度报告生成测试失败: 'ReportGenerator' object has no attribute '_generate_daily_html_report'
Traceback (most recent call last):
  File "E:\work\test\ai\test_report_generator.py", line 173, in test_daily_report
    report_paths = generator.generate_daily_report(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\work\test\ai\qlib_trading_system\monitoring\report_generator_system.py", line 903, in generate_daily_report
    report_paths['html'] = self._generate_daily_html_report(daily_data, chart_paths, report_date)
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'ReportGenerator' object has no attribute '_generate_daily_html_report'
2025-07-31 11:24:05,087 - qlib_trading_system.monitoring.report_generator_system - INFO - 报告生成器初始化完成
2025-07-31 11:24:05,087 - qlib_trading_system.monitoring.report_generator_system - INFO - 生成周度报告: 2024-01-15 至 2024-01-21
2025-07-31 11:24:05,088 - __main__ - ERROR - 周度报告生成测试失败: 'ReportGenerator' object has no attribute '_generate_weekly_charts'
Traceback (most recent call last):
  File "E:\work\test\ai\test_report_generator.py", line 211, in test_weekly_report
    report_paths = generator.generate_weekly_report(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\work\test\ai\qlib_trading_system\monitoring\report_generator_system.py", line 946, in generate_weekly_report
    chart_paths = self._generate_weekly_charts(weekly_data, week_start, week_end)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'ReportGenerator' object has no attribute '_generate_weekly_charts'
2025-07-31 11:24:05,096 - qlib_trading_system.monitoring.report_generator_system - INFO - 报告生成器初始化完成
2025-07-31 11:24:05,096 - qlib_trading_system.monitoring.report_generator_system - INFO - 生成月度报告: 2024年1月
2025-07-31 11:24:05,097 - __main__ - ERROR - 月度报告生成测试失败: 'ReportGenerator' object has no attribute '_generate_monthly_charts'
Traceback (most recent call last):
  File "E:\work\test\ai\test_report_generator.py", line 246, in test_monthly_report
    report_paths = generator.generate_monthly_report(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\work\test\ai\qlib_trading_system\monitoring\report_generator_system.py", line 994, in generate_monthly_report
    chart_paths = self._generate_monthly_charts(monthly_data, year, month)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'ReportGenerator' object has no attribute '_generate_monthly_charts'
2025-07-31 11:24:05,102 - qlib_trading_system.monitoring.report_generator_system - INFO - 报告生成器初始化完成
2025-07-31 11:24:05,102 - qlib_trading_system.monitoring.report_generator_system - INFO - 生成策略执行报告: explosive_stock_ai_v1 (2024-01-01 至 2024-01-31)
2025-07-31 11:24:05,103 - __main__ - ERROR - 策略执行报告生成测试失败: 'ReportGenerator' object has no attribute '_generate_strategy_charts'
Traceback (most recent call last):
  File "E:\work\test\ai\test_report_generator.py", line 281, in test_strategy_report
    report_paths = generator.generate_strategy_execution_report(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\work\test\ai\qlib_trading_system\monitoring\report_generator_system.py", line 1044, in generate_strategy_execution_report
    chart_paths = self._generate_strategy_charts(strategy_data, strategy_id, start_date, end_date)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'ReportGenerator' object has no attribute '_generate_strategy_charts'
2025-07-31 11:24:05,113 - qlib_trading_system.monitoring.report_generator_system - INFO - 报告生成器初始化完成
2025-07-31 11:24:05,114 - __main__ - ERROR - 报告模板配置测试失败: 'ReportGenerator' object has no attribute 'get_report_template_config'
Traceback (most recent call last):
  File "E:\work\test\ai\test_report_generator.py", line 314, in test_report_template_config
    template_config = generator.get_report_template_config()
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'ReportGenerator' object has no attribute 'get_report_template_config'
2025-07-31 11:24:05,119 - qlib_trading_system.monitoring.report_generator_system - INFO - 报告生成器初始化完成
2025-07-31 11:24:05,119 - qlib_trading_system.monitoring.report_generator_system - INFO - 生成日度报告: 2024-01-15
2025-07-31 11:24:06,151 - qlib_trading_system.monitoring.report_generator_system - INFO - 日度图表生成完成: test_reports/charts/daily_analysis_20240115.png
2025-07-31 11:24:06,152 - __main__ - ERROR - 数据导出测试失败: 'ReportGenerator' object has no attribute '_generate_daily_html_report'
Traceback (most recent call last):
  File "E:\work\test\ai\test_report_generator.py", line 505, in test_data_export
    report_paths = generator.generate_daily_report(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\work\test\ai\qlib_trading_system\monitoring\report_generator_system.py", line 903, in generate_daily_report
    report_paths['html'] = self._generate_daily_html_report(daily_data, chart_paths, report_date)
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'ReportGenerator' object has no attribute '_generate_daily_html_report'
2025-07-31 11:24:06,157 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 加载了 1 个自定义模板
2025-07-31 11:24:06,158 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 增强版报告生成器初始化完成
2025-07-31 11:24:06,160 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义模板创建成功: custom_20250731_112406
2025-07-31 11:24:07,697 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义图表生成完成: 2个图表
2025-07-31 11:24:07,703 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义HTML报告生成完成: test_reports_enhanced/custom/custom_report_custom_20250731_112406_20250731_112407.html
2025-07-31 11:24:07,704 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义JSON报告生成完成: test_reports_enhanced/custom/custom_report_custom_20250731_112406_20250731_112407.json
2025-07-31 11:24:07,705 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义报告生成完成: custom_20250731_112406, 格式: ['html', 'json']
2025-07-31 11:24:07,707 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 导出配置创建成功: export_20250731_112407
2025-07-31 11:24:07,712 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 加载了 2 个自定义模板
2025-07-31 11:24:07,712 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 增强版报告生成器初始化完成
2025-07-31 11:24:07,714 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义模板创建成功: custom_20250731_112407
2025-07-31 11:24:07,716 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义模板更新成功: custom_20250731_112407
2025-07-31 11:24:07,718 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义模板删除成功: custom_20250731_112407
2025-07-31 11:24:07,723 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 加载了 2 个自定义模板
2025-07-31 11:24:07,723 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 增强版报告生成器初始化完成
2025-07-31 11:24:07,725 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 报告汇总生成完成: 2024-01-01 至 2024-01-31
2025-07-31 11:39:24,251 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 增强版报告生成器初始化完成
2025-07-31 11:39:24,255 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 增强版报告生成器初始化完成
2025-07-31 11:39:24,257 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义模板创建成功: custom_20250731_113924
2025-07-31 11:39:26,540 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义图表生成完成: 2个图表
2025-07-31 11:39:26,546 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义HTML报告生成完成: test_reports/custom/daily_report_20240115.html
2025-07-31 11:39:26,560 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义Excel报告生成完成: test_reports/custom/daily_report_20240115.xlsx
2025-07-31 11:39:26,561 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义JSON报告生成完成: test_reports/custom/daily_report_20240115.json
2025-07-31 11:39:26,561 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义报告生成完成: custom_20250731_113924, 格式: ['html', 'excel', 'json']
2025-07-31 11:39:26,567 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 加载了 1 个自定义模板
2025-07-31 11:39:26,567 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 增强版报告生成器初始化完成
2025-07-31 11:39:26,569 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义模板创建成功: custom_20250731_113926
2025-07-31 11:39:28,181 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义图表生成完成: 2个图表
2025-07-31 11:39:28,185 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义HTML报告生成完成: test_reports/custom/weekly_report_20240115_20240121.html
2025-07-31 11:39:28,197 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义Excel报告生成完成: test_reports/custom/weekly_report_20240115_20240121.xlsx
2025-07-31 11:39:28,197 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义JSON报告生成完成: test_reports/custom/weekly_report_20240115_20240121.json
2025-07-31 11:39:28,198 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义报告生成完成: custom_20250731_113926, 格式: ['html', 'excel', 'json']
2025-07-31 11:39:28,204 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 加载了 2 个自定义模板
2025-07-31 11:39:28,204 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 增强版报告生成器初始化完成
2025-07-31 11:39:28,207 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义模板创建成功: custom_20250731_113928
2025-07-31 11:39:30,321 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义图表生成完成: 3个图表
2025-07-31 11:39:30,326 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义HTML报告生成完成: test_reports/custom/monthly_report_202401.html
2025-07-31 11:39:30,337 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义Excel报告生成完成: test_reports/custom/monthly_report_202401.xlsx
2025-07-31 11:39:30,338 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义JSON报告生成完成: test_reports/custom/monthly_report_202401.json
2025-07-31 11:39:30,339 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义报告生成完成: custom_20250731_113928, 格式: ['html', 'excel', 'json']
2025-07-31 11:39:30,344 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 加载了 3 个自定义模板
2025-07-31 11:39:30,344 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 增强版报告生成器初始化完成
2025-07-31 11:39:30,347 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义模板创建成功: custom_20250731_113930
2025-07-31 11:39:32,488 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义图表生成完成: 3个图表
2025-07-31 11:39:32,492 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义HTML报告生成完成: test_reports/custom/strategy_report_explosive_stock_ai_v1.html
2025-07-31 11:39:32,504 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义Excel报告生成完成: test_reports/custom/strategy_report_explosive_stock_ai_v1.xlsx
2025-07-31 11:39:32,505 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义JSON报告生成完成: test_reports/custom/strategy_report_explosive_stock_ai_v1.json
2025-07-31 11:39:32,505 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义报告生成完成: custom_20250731_113930, 格式: ['html', 'excel', 'json']
2025-07-31 11:39:32,512 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 增强版报告生成器初始化完成
2025-07-31 11:39:32,518 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 加载了 4 个自定义模板
2025-07-31 11:39:32,518 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 增强版报告生成器初始化完成
2025-07-31 11:39:32,518 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 导出配置创建成功: export_20250731_113932
2025-07-31 11:39:32,521 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义模板创建成功: custom_20250731_113932
2025-07-31 11:39:33,333 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义图表生成完成: 1个图表
2025-07-31 11:39:33,337 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义HTML报告生成完成: test_reports/custom/export_test_report.html
2025-07-31 11:39:33,340 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义JSON报告生成完成: test_reports/custom/export_test_report.json
2025-07-31 11:39:33,340 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义报告生成完成: custom_20250731_113932, 格式: ['html', 'json']
2025-07-31 11:39:33,342 - qlib_trading_system.monitoring.enhanced_report_generator - ERROR - 批量导出报告时发生错误: 不支持的批量导出格式: json
2025-07-31 11:39:33,348 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 加载了 3 个自定义模板
2025-07-31 11:39:33,348 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 增强版报告生成器初始化完成
2025-07-31 11:39:33,350 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义模板创建成功: custom_20250731_113933
2025-07-31 11:39:35,111 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义图表生成完成: 2个图表
2025-07-31 11:39:35,116 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义HTML报告生成完成: test_reports_enhanced/custom/custom_report_custom_20250731_113933_20250731_113935.html
2025-07-31 11:39:35,116 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义JSON报告生成完成: test_reports_enhanced/custom/custom_report_custom_20250731_113933_20250731_113935.json
2025-07-31 11:39:35,117 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义报告生成完成: custom_20250731_113933, 格式: ['html', 'json']
2025-07-31 11:39:35,119 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 导出配置创建成功: export_20250731_113935
2025-07-31 11:39:35,124 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 加载了 4 个自定义模板
2025-07-31 11:39:35,125 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 增强版报告生成器初始化完成
2025-07-31 11:39:35,127 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义模板创建成功: custom_20250731_113935
2025-07-31 11:39:35,129 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义模板更新成功: custom_20250731_113935
2025-07-31 11:39:35,132 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 自定义模板删除成功: custom_20250731_113935
2025-07-31 11:39:35,137 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 加载了 4 个自定义模板
2025-07-31 11:39:35,137 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 增强版报告生成器初始化完成
2025-07-31 11:39:35,138 - qlib_trading_system.monitoring.enhanced_report_generator - INFO - 报告汇总生成完成: 2024-01-01 至 2024-01-31
