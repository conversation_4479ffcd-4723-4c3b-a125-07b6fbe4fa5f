"""
系统配置文件
"""
import os
from typing import Dict, List, Optional
from pydantic_settings import BaseSettings
from enum import Enum


class CapitalMode(str, Enum):
    """资金模式"""
    SMALL = "small"      # <50万
    MEDIUM = "medium"    # 50-500万
    LARGE = "large"      # >500万


class TradingConfig(BaseSettings):
    """交易配置"""
    
    # 基础配置
    CAPITAL_MODE: CapitalMode = CapitalMode(os.getenv("CAPITAL_MODE", "small"))
    TOTAL_CAPITAL: float = float(os.getenv("TOTAL_CAPITAL", "100000"))  # 总资金
    
    # 仓位配置
    BASE_POSITION_RATIO: float = float(os.getenv("BASE_POSITION_RATIO", "0.75"))  # 底仓比例
    T_POSITION_RATIO: float = float(os.getenv("T_POSITION_RATIO", "0.20"))        # 做T仓位比例
    CASH_RESERVE_RATIO: float = float(os.getenv("CASH_RESERVE_RATIO", "0.05"))    # 现金储备比例
    
    # 股票筛选配置
    MAX_STOCKS: int = int(os.getenv("MAX_STOCKS", "1"))  # 最大持股数量
    EXPLOSIVE_THRESHOLD: float = float(os.getenv("EXPLOSIVE_THRESHOLD", "0.7"))  # 爆发力阈值
    
    # 风险控制配置
    MAX_SINGLE_LOSS: float = float(os.getenv("MAX_SINGLE_LOSS", "0.02"))      # 单股最大亏损2%
    MAX_DAILY_LOSS: float = float(os.getenv("MAX_DAILY_LOSS", "0.01"))        # 日最大亏损1%
    MAX_DRAWDOWN: float = float(os.getenv("MAX_DRAWDOWN", "0.30"))            # 最大回撤30%
    
    # 交易时间配置
    TRADING_START_TIME: str = os.getenv("TRADING_START_TIME", "09:30:00")
    TRADING_END_TIME: str = os.getenv("TRADING_END_TIME", "15:00:00")
    LUNCH_BREAK_START: str = os.getenv("LUNCH_BREAK_START", "11:30:00")
    LUNCH_BREAK_END: str = os.getenv("LUNCH_BREAK_END", "13:00:00")


class DataSourceConfig(BaseSettings):
    """数据源配置"""
    
    # 主要数据源配置
    PRIMARY_DATA_SOURCE: str = os.getenv("PRIMARY_DATA_SOURCE", "iTick")
    
    # iTick配置
    ITICK_API_KEY: str = os.getenv("ITICK_API_KEY", "")
    ITICK_SECRET_KEY: str = os.getenv("ITICK_SECRET_KEY", "")
    
    # 聚宽配置
    JOINQUANT_USERNAME: str = os.getenv("JOINQUANT_USERNAME", "")
    JOINQUANT_PASSWORD: str = os.getenv("JOINQUANT_PASSWORD", "")
    
    # 米筐配置
    RICEQUANT_API_KEY: str = os.getenv("RICEQUANT_API_KEY", "")
    RICEQUANT_SECRET_KEY: str = os.getenv("RICEQUANT_SECRET_KEY", "")


class ModelConfig(BaseSettings):
    """模型配置"""
    
    # 股票筛选模型配置
    STOCK_SELECTION_MODEL_PATH: str = os.getenv("STOCK_SELECTION_MODEL_PATH", "models/stock_selection")
    RETRAIN_FREQUENCY: str = os.getenv("RETRAIN_FREQUENCY", "daily")  # daily, weekly
    
    # 日内交易模型配置
    INTRADAY_MODEL_PATH: str = os.getenv("INTRADAY_MODEL_PATH", "models/intraday_trading")
    PREDICTION_TIMEFRAME: int = int(os.getenv("PREDICTION_TIMEFRAME", "30"))  # 预测时间框架(分钟)
    
    # 特征工程配置
    FEATURE_DIMENSIONS: List[str] = [
        "fundamental",  # 基本面
        "valuation",    # 估值
        "technical",    # 技术面
        "sentiment",    # 情绪
        "risk",         # 风险
        "market"        # 大盘
    ]


class SystemConfig(BaseSettings):
    """系统配置"""
    
    # 环境配置
    ENVIRONMENT: str = os.getenv("ENVIRONMENT", "development")  # development, production
    DEBUG: bool = os.getenv("DEBUG", "True").lower() == "true"
    
    # API配置
    API_HOST: str = os.getenv("API_HOST", "0.0.0.0")
    API_PORT: int = int(os.getenv("API_PORT", "8000"))
    
    # 日志配置
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    LOG_FILE_PATH: str = os.getenv("LOG_FILE_PATH", "logs/trading_system.log")
    
    # 监控配置
    ENABLE_MONITORING: bool = os.getenv("ENABLE_MONITORING", "True").lower() == "true"
    PROMETHEUS_PORT: int = int(os.getenv("PROMETHEUS_PORT", "9090"))


# 全局配置实例
trading_config = TradingConfig()
data_source_config = DataSourceConfig()
model_config = ModelConfig()
system_config = SystemConfig()