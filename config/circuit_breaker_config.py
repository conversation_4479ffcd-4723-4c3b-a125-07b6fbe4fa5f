"""
熔断机制配置文件
定义熔断机制的各种参数和阈值
"""

from typing import Dict, Any, Optional
from dataclasses import dataclass, field
import os


@dataclass
class CircuitBreakerSettings:
    """熔断机制设置"""
    
    # 价格异常阈值设置
    price_thresholds: Dict[str, float] = field(default_factory=lambda: {
        "single_stock_warning": -0.05,      # 单股跌幅5%警告
        "single_stock_danger": -0.08,       # 单股跌幅8%危险
        "single_stock_critical": -0.15,     # 单股跌幅15%严重
        "market_warning": -0.02,            # 大盘跌幅2%警告
        "market_danger": -0.03,             # 大盘跌幅3%危险
        "market_critical": -0.05,           # 大盘跌幅5%严重
        "limit_down_threshold": -0.095      # 跌停阈值
    })
    
    # 成交量异常阈值设置
    volume_thresholds: Dict[str, float] = field(default_factory=lambda: {
        "surge_warning": 2.0,               # 成交量放大2倍警告
        "surge_danger": 3.0,                # 成交量放大3倍危险
        "surge_critical": 5.0,              # 成交量放大5倍严重
        "dry_warning": 0.5,                 # 成交量萎缩至50%警告
        "dry_danger": 0.3,                  # 成交量萎缩至30%危险
        "dry_critical": 0.1                 # 成交量萎缩至10%严重
    })
    
    # 波动率异常阈值设置
    volatility_thresholds: Dict[str, float] = field(default_factory=lambda: {
        "spike_warning": 1.5,               # 波动率激增1.5倍警告
        "spike_danger": 2.0,                # 波动率激增2倍危险
        "spike_critical": 3.0               # 波动率激增3倍严重
    })
    
    # 风险控制阈值设置
    risk_thresholds: Dict[str, float] = field(default_factory=lambda: {
        "max_single_position": 0.3,         # 最大单仓位30%
        "max_total_position": 0.95,         # 最大总仓位95%
        "max_drawdown": 0.2,                # 最大回撤20%
        "max_daily_loss": 0.05,             # 最大日亏损5%
        "max_leverage": 2.0,                # 最大杠杆2倍
        "min_cash_reserve": 0.05            # 最小现金储备5%
    })
    
    # 连续亏损设置
    consecutive_loss_settings: Dict[str, Any] = field(default_factory=lambda: {
        "max_consecutive_days": 3,           # 最大连续亏损天数
        "daily_loss_threshold": -0.01,      # 日亏损阈值1%
        "position_reduction_ratio": 0.3,    # 仓位削减比例30%
        "frequency_reduction_ratio": 0.5    # 交易频率削减比例50%
    })
    
    # 时间窗口设置
    time_windows: Dict[str, int] = field(default_factory=lambda: {
        "anomaly_detection": 300,            # 异常检测窗口5分钟
        "circuit_breaker_cooldown": 1800,   # 熔断冷却时间30分钟
        "emergency_exit_timeout": 60,       # 紧急退出超时1分钟
        "data_staleness_limit": 30,         # 数据过期限制30秒
        "monitoring_interval": 1             # 监控间隔1秒
    })
    
    # 黑天鹅事件关键词
    black_swan_keywords: Dict[str, list] = field(default_factory=lambda: {
        "negative_news": [
            "立案调查", "ST", "退市", "减持", "财务造假", "监管处罚",
            "停牌", "重大违法", "欺诈", "内幕交易", "操纵市场", "暂停上市",
            "特别处理", "风险警示", "违规担保", "资金占用", "业绩造假"
        ],
        "market_risk": [
            "熔断", "暴跌", "崩盘", "恐慌", "流动性危机", "系统性风险",
            "金融危机", "经济衰退", "市场崩溃", "大规模抛售", "踩踏事件",
            "信用违约", "债务危机", "银行挤兑", "货币贬值"
        ],
        "policy_risk": [
            "政策收紧", "监管加强", "限制措施", "禁止交易", "调控政策",
            "税收政策", "利率上调", "准备金率", "汇率管制", "资本管制"
        ]
    })
    
    # 防御策略配置
    defense_strategies: Dict[str, Dict[str, Any]] = field(default_factory=lambda: {
        "CONSERVATIVE": {
            "max_position_ratio": 0.5,
            "stop_loss_ratio": 0.02,
            "take_profit_ratio": 0.03,
            "trading_frequency_limit": 0.3
        },
        "MODERATE": {
            "max_position_ratio": 0.7,
            "stop_loss_ratio": 0.03,
            "take_profit_ratio": 0.05,
            "trading_frequency_limit": 0.5
        },
        "AGGRESSIVE": {
            "max_position_ratio": 0.9,
            "stop_loss_ratio": 0.05,
            "take_profit_ratio": 0.08,
            "trading_frequency_limit": 0.8
        },
        "EMERGENCY": {
            "max_position_ratio": 0.1,
            "stop_loss_ratio": 0.01,
            "take_profit_ratio": 0.02,
            "trading_frequency_limit": 0.1
        }
    })
    
    # 通知设置
    notification_settings: Dict[str, Any] = field(default_factory=lambda: {
        "enable_email": True,
        "enable_sms": False,
        "enable_webhook": True,
        "email_recipients": ["<EMAIL>"],
        "sms_recipients": [],
        "webhook_url": "http://localhost:8080/webhook/alert",
        "notification_levels": ["CRITICAL", "EMERGENCY"]
    })


class CircuitBreakerConfigManager:
    """熔断机制配置管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or "circuit_breaker.json"
        self.settings = CircuitBreakerSettings()
        self._load_config()
    
    def _load_config(self) -> None:
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                import json
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    self._update_settings(config_data)
        except Exception as e:
            print(f"加载配置文件失败: {e}")
    
    def _update_settings(self, config_data: Dict[str, Any]) -> None:
        """更新设置"""
        for key, value in config_data.items():
            if hasattr(self.settings, key):
                setattr(self.settings, key, value)
    
    def save_config(self) -> None:
        """保存配置文件"""
        try:
            import json
            from dataclasses import asdict
            
            config_data = asdict(self.settings)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def get_price_threshold(self, threshold_type: str) -> float:
        """获取价格阈值"""
        return self.settings.price_thresholds.get(threshold_type, 0.0)
    
    def get_volume_threshold(self, threshold_type: str) -> float:
        """获取成交量阈值"""
        return self.settings.volume_thresholds.get(threshold_type, 1.0)
    
    def get_risk_threshold(self, threshold_type: str) -> float:
        """获取风险阈值"""
        return self.settings.risk_thresholds.get(threshold_type, 0.0)
    
    def get_defense_strategy(self, strategy_name: str) -> Dict[str, Any]:
        """获取防御策略"""
        return self.settings.defense_strategies.get(strategy_name, {})
    
    def get_black_swan_keywords(self, category: str) -> list:
        """获取黑天鹅关键词"""
        return self.settings.black_swan_keywords.get(category, [])
    
    def update_threshold(self, category: str, threshold_type: str, value: float) -> None:
        """更新阈值"""
        if category == "price":
            self.settings.price_thresholds[threshold_type] = value
        elif category == "volume":
            self.settings.volume_thresholds[threshold_type] = value
        elif category == "risk":
            self.settings.risk_thresholds[threshold_type] = value
        elif category == "volatility":
            self.settings.volatility_thresholds[threshold_type] = value
    
    def get_time_window(self, window_type: str) -> int:
        """获取时间窗口"""
        return self.settings.time_windows.get(window_type, 300)
    
    def is_notification_enabled(self, notification_type: str) -> bool:
        """检查通知是否启用"""
        return self.settings.notification_settings.get(f"enable_{notification_type}", False)
    
    def get_notification_recipients(self, notification_type: str) -> list:
        """获取通知接收者"""
        return self.settings.notification_settings.get(f"{notification_type}_recipients", [])


# 全局配置管理器实例
_config_manager = None


def get_circuit_breaker_config(config_file: Optional[str] = None) -> CircuitBreakerConfigManager:
    """获取全局配置管理器实例"""
    global _config_manager
    
    if _config_manager is None:
        _config_manager = CircuitBreakerConfigManager(config_file)
    
    return _config_manager


def load_circuit_breaker_config(config_file: str) -> CircuitBreakerConfigManager:
    """加载熔断机制配置"""
    global _config_manager
    _config_manager = CircuitBreakerConfigManager(config_file)
    return _config_manager


# 默认配置实例
DEFAULT_CONFIG = CircuitBreakerSettings()