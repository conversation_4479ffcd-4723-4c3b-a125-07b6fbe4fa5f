#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资金管理系统集成测试 - Capital Management System Integration Test

测试小资金专用配置管理、全仓单股策略风险控制、资金使用效率优化算法和资金流水成本分析系统

Author: Qlib Trading System
Date: 2025-01-30
"""

import sys
import os
import json
import logging
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from qlib_trading_system.trading.capital_manager_simple import (
    CapitalManager, CapitalConfig, CapitalMode, PositionType, TransactionType
)
from qlib_trading_system.trading.small_capital_config import (
    SmallCapitalConfigManager, SmallCapitalConfig, RiskLevel
)
from qlib_trading_system.trading.single_stock_risk_controller import (
    SingleStockRiskController, SingleStockRiskConfig, RiskEvent
)
from qlib_trading_system.trading.fund_flow_analyzer import (
    FundFlowAnalyzer, FlowType, CostType
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('capital_management_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


def test_capital_management_system():
    """测试资金管理系统"""
    logger.info("=== 资金管理系统集成测试开始 ===")
    
    test_account_id = "TEST_ACCOUNT_001"
    test_capital = 100000.0  # 10万测试资金
    test_symbol = "000001.SZ"
    
    try:
        # 1. 测试小资金专用配置管理
        logger.info("1. 测试小资金专用配置管理")
        config_manager = SmallCapitalConfigManager("test_config/small_capital")
        
        config = config_manager.create_config(
            account_id=test_account_id,
            total_capital=test_capital,
            risk_level=RiskLevel.AGGRESSIVE
        )
        
        logger.info(f"✓ 创建小资金配置成功: {config.account_id}")
        logger.info(f"  - 总资金: {config.total_capital:,.2f}")
        logger.info(f"  - 风险等级: {config.risk_level.value}")
        logger.info(f"  - 底仓比例: {config.base_position_ratio:.1%}")
        logger.info(f"  - T仓比例: {config.t_position_ratio:.1%}")
        
        # 2. 测试资金管理器
        logger.info("2. 测试资金管理器")
        capital_config = CapitalConfig(
            total_capital=config.total_capital,
            mode=CapitalMode.SMALL,
            max_stocks=1,
            base_position_ratio=config.base_position_ratio,
            t_position_ratio=config.t_position_ratio,
            cash_reserve_ratio=config.cash_reserve_ratio,
            max_single_stock_ratio=1.0,
            max_daily_loss_ratio=config.max_daily_loss_ratio,
            max_drawdown_ratio=config.max_drawdown_ratio,
            leverage_ratio=1.0
        )
        
        capital_manager = CapitalManager(capital_config)
        logger.info("✓ 资金管理器创建成功")
        
        # 测试买入操作
        allocation = capital_manager.get_capital_allocation()
        buy_price = 10.00
        buy_quantity = int(allocation['base_allocation'] / buy_price / 100) * 100
        
        success = capital_manager.execute_buy(
            test_symbol, buy_quantity, buy_price, PositionType.BASE, "建立底仓"
        )
        
        if success:
            logger.info(f"✓ 买入成功: {test_symbol} {buy_quantity}股 @{buy_price:.2f}")
        else:
            logger.error("✗ 买入失败")
            return False        

        # 3. 测试风险控制器
        logger.info("3. 测试全仓单股策略风险控制")
        risk_config = SingleStockRiskConfig(
            max_position_loss_pct=0.05,
            max_daily_loss_pct=0.03,
            max_drawdown_pct=0.15,
            stop_loss_pct=0.08
        )
        
        risk_controller = SingleStockRiskController(risk_config)
        logger.info("✓ 风险控制器创建成功")
        
        # 模拟持仓更新
        risk_controller.update_position(
            symbol=test_symbol,
            current_price=10.20,
            position_value=75000,
            cost_basis=10.00,
            daily_volume=80000000
        )
        
        risk_status = risk_controller.get_current_risk_status()
        logger.info(f"✓ 风险状态: {risk_status['overall_risk_level']}")
        
        # 4. 测试资金流水分析器
        logger.info("4. 测试资金流水和成本分析系统")
        flow_analyzer = FundFlowAnalyzer("test_data/fund_flow")
        logger.info("✓ 资金流水分析器创建成功")
        
        # 添加流水记录
        flow_analyzer.add_flow_record(
            test_symbol, FlowType.OUTFLOW, 75000, 100000, 25000, "买入底仓"
        )
        flow_analyzer.add_flow_record(
            test_symbol, FlowType.OUTFLOW, 9950, 25000, 15050, "T+0买入"
        )
        flow_analyzer.add_flow_record(
            test_symbol, FlowType.INFLOW, 10050, 15050, 25100, "T+0卖出"
        )
        
        logger.info("✓ 流水记录添加完成")
        
        # 生成流水报告
        flow_report = flow_analyzer.generate_flow_report()
        logger.info(f"✓ 流水报告生成成功，总记录数: {flow_report['summary']['total_records']}")
        
        # 5. 测试资金使用效率优化
        logger.info("5. 测试资金使用效率优化算法")
        efficiency = capital_manager.calculate_capital_efficiency()
        logger.info(f"✓ 资金效率评分: {efficiency['efficiency_score']:.1f}")
        logger.info(f"  - 资金利用率: {efficiency['capital_utilization']:.1f}%")
        logger.info(f"  - 现金闲置率: {efficiency['cash_idle_rate']:.1f}%")
        
        # 测试资金分配优化
        target_stocks = ["000001.SZ", "000002.SZ"]
        stock_scores = {"000001.SZ": 0.85, "000002.SZ": 0.78}
        
        optimization = capital_manager.optimize_capital_allocation(target_stocks, stock_scores)
        logger.info(f"✓ 资金分配优化完成，策略: {optimization['strategy']}")
        
        # 6. 生成综合测试报告
        logger.info("6. 生成综合测试报告")
        
        portfolio = capital_manager.get_portfolio_summary()
        cost_analysis = flow_analyzer.calculate_cost_analysis(test_symbol)
        
        test_report = {
            "test_time": datetime.now().isoformat(),
            "test_account": test_account_id,
            "test_capital": test_capital,
            "results": {
                "config_created": True,
                "capital_manager_working": True,
                "risk_controller_working": True,
                "flow_analyzer_working": True,
                "efficiency_optimization_working": True
            },
            "metrics": {
                "portfolio_value": portfolio["portfolio_value"],
                "total_return_pct": portfolio["total_return_pct"],
                "efficiency_score": efficiency["efficiency_score"],
                "risk_level": risk_status["overall_risk_level"],
                "cost_efficiency_score": cost_analysis.cost_efficiency_score
            }
        }
        
        # 保存测试报告
        report_file = f"capital_management_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(test_report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"✓ 测试报告已保存: {report_file}")
        
        # 清理测试数据
        config_manager.delete_config(test_account_id)
        logger.info("✓ 测试数据清理完成")
        
        logger.info("🎉 资金管理系统集成测试全部通过!")
        return True
        
    except Exception as e:
        logger.error(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    try:
        success = test_capital_management_system()
        
        if success:
            print("\n🎉 资金管理系统集成测试成功!")
            return 0
        else:
            print("\n❌ 资金管理系统集成测试失败!")
            return 1
            
    except Exception as e:
        print(f"\n💥 测试执行异常: {e}")
        return 1


if __name__ == "__main__":
    exit(main())