#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
报告生成系统测试
Test Report Generation System
"""

import sys
import os
import json
import logging
from datetime import datetime, date, timedelta
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from qlib_trading_system.monitoring.enhanced_report_generator import EnhancedReportGenerator as ReportGenerator
from qlib_trading_system.monitoring.enhanced_report_generator import EnhancedReportGenerator, DEFAULT_CUSTOM_TEMPLATE

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_report_generator.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


def create_mock_trading_data():
    """创建模拟交易数据"""
    return {
        'equity_curve': [
            {
                'timestamp': '2024-01-15T09:30:00',
                'total_value': 1000000,
                'daily_pnl': 0,
                'cash': 300000
            },
            {
                'timestamp': '2024-01-15T10:30:00',
                'total_value': 1025000,
                'daily_pnl': 25000,
                'cash': 275000
            },
            {
                'timestamp': '2024-01-15T15:00:00',
                'total_value': 1080000,
                'daily_pnl': 80000,
                'cash': 250000
            }
        ],
        'trades': [
            {
                'timestamp': '2024-01-15T09:35:00',
                'side': 'buy',
                'symbol': '000001.SZ',
                'quantity': 2000,
                'price': 12.50,
                'amount': 25000,
                'is_t_trade': False,
                't_profit': 0
            },
            {
                'timestamp': '2024-01-15T10:15:00',
                'side': 'buy',
                'symbol': '000002.SZ',
                'quantity': 1500,
                'price': 18.80,
                'amount': 28200,
                'is_t_trade': False,
                't_profit': 0
            },
            {
                'timestamp': '2024-01-15T11:30:00',
                'side': 'sell',
                'symbol': '000001.SZ',
                'quantity': 1000,
                'price': 13.20,
                'amount': 13200,
                'is_t_trade': True,
                't_profit': 700
            },
            {
                'timestamp': '2024-01-15T14:20:00',
                'side': 'sell',
                'symbol': '000002.SZ',
                'quantity': 800,
                'price': 19.50,
                'amount': 15600,
                'is_t_trade': True,
                't_profit': 560
            },
            {
                'timestamp': '2024-01-15T14:45:00',
                'side': 'buy',
                'symbol': '000003.SZ',
                'quantity': 3000,
                'price': 8.90,
                'amount': 26700,
                'is_t_trade': False,
                't_profit': 0
            }
        ],
        'positions': {
            '000001.SZ': {
                'symbol': '000001.SZ',
                'quantity': 1000,
                'avg_price': 12.50,
                'market_price': 13.80,
                'market_value': 13800,
                'unrealized_pnl': 1300
            },
            '000002.SZ': {
                'symbol': '000002.SZ',
                'quantity': 700,
                'avg_price': 18.80,
                'market_price': 20.10,
                'market_value': 14070,
                'unrealized_pnl': 910
            },
            '000003.SZ': {
                'symbol': '000003.SZ',
                'quantity': 3000,
                'avg_price': 8.90,
                'market_price': 9.20,
                'market_value': 27600,
                'unrealized_pnl': 900
            }
        },
        'alerts': [
            {
                'timestamp': '2024-01-15T10:00:00',
                'type': 'risk_warning',
                'level': 'warning',
                'message': '单股持仓比例超过30%',
                'symbol': '000003.SZ'
            },
            {
                'timestamp': '2024-01-15T13:30:00',
                'type': 'system_alert',
                'level': 'info',
                'message': '做T交易执行成功',
                'symbol': '000001.SZ'
            }
        ]
    }


def test_daily_report():
    """测试日度报告生成"""
    print("\n" + "="*50)
    print("测试日度报告生成")
    print("="*50)
    
    try:
        # 初始化报告生成器
        generator = ReportGenerator({
            'report_dir': 'test_reports',
            'enable_charts': True,
            'chart_format': 'png'
        })
        
        # 创建自定义模板
        template_id = generator.create_custom_template(
            template_name="日度交易报告",
            template_type="daily",
            sections=["基础指标", "交易统计", "持仓信息", "风险指标"],
            charts=["performance_trend", "risk_analysis"],
            template_content=DEFAULT_CUSTOM_TEMPLATE
        )
        
        # 创建模拟数据
        trading_data = create_mock_trading_data()
        
        # 转换数据格式
        report_data = {
            "日期": "2024-01-15",
            "总收益": f"¥{trading_data['equity_curve'][-1]['total_value'] - 1000000:,.0f}",
            "日度收益": f"¥{trading_data['equity_curve'][-1]['daily_pnl']:,.0f}",
            "交易次数": len(trading_data['trades']),
            "持仓数量": len(trading_data['positions'])
        }
        
        # 生成日度报告
        report_paths = generator.generate_custom_report(
            template_id=template_id,
            data=report_data,
            output_filename="daily_report_20240115",
            export_formats=['html', 'excel', 'json']
        )
        
        print(f"✅ 日度报告生成成功:")
        for format_type, path in report_paths.items():
            print(f"   {format_type.upper()}: {path}")
            if os.path.exists(path):
                print(f"   ✓ 文件已创建: {os.path.getsize(path)} bytes")
            else:
                print(f"   ✗ 文件未找到")
        
        return True
        
    except Exception as e:
        print(f"❌ 日度报告生成失败: {e}")
        logger.error(f"日度报告生成测试失败: {e}", exc_info=True)
        return False


def test_weekly_report():
    """测试周度报告生成"""
    print("\n" + "="*50)
    print("测试周度报告生成")
    print("="*50)
    
    try:
        generator = ReportGenerator({
            'report_dir': 'test_reports',
            'enable_charts': True
        })
        
        # 创建周度报告模板
        template_id = generator.create_custom_template(
            template_name="周度绩效报告",
            template_type="weekly",
            sections=["收益表现", "交易统计", "策略表现", "风险管理"],
            charts=["performance_trend", "risk_analysis"],
            template_content=DEFAULT_CUSTOM_TEMPLATE
        )
        
        # 模拟周度数据
        weekly_data = {
            "周期": "2024-01-15 至 2024-01-21",
            "周度收益率": "3.2%",
            "累计收益率": "15.8%",
            "最佳单日收益": "1.8%",
            "最差单日收益": "-0.5%",
            "总交易次数": 85,
            "胜率": "68.2%",
            "夏普比率": "1.45"
        }
        
        # 生成周度报告
        report_paths = generator.generate_custom_report(
            template_id=template_id,
            data=weekly_data,
            output_filename="weekly_report_20240115_20240121",
            export_formats=['html', 'excel', 'json']
        )
        
        print(f"✅ 周度报告生成成功:")
        for format_type, path in report_paths.items():
            print(f"   {format_type.upper()}: {path}")
            if os.path.exists(path):
                print(f"   ✓ 文件已创建: {os.path.getsize(path)} bytes")
            else:
                print(f"   ✗ 文件未找到")
        
        return True
        
    except Exception as e:
        print(f"❌ 周度报告生成失败: {e}")
        logger.error(f"周度报告生成测试失败: {e}", exc_info=True)
        return False


def test_monthly_report():
    """测试月度报告生成"""
    print("\n" + "="*50)
    print("测试月度报告生成")
    print("="*50)
    
    try:
        generator = ReportGenerator({
            'report_dir': 'test_reports',
            'enable_charts': True
        })
        
        # 创建月度报告模板
        template_id = generator.create_custom_template(
            template_name="月度绩效分析报告",
            template_type="monthly",
            sections=["月度收益表现", "策略分析", "风险调整收益", "收益归因分析"],
            charts=["performance_trend", "risk_analysis", "trading_volume"],
            template_content=DEFAULT_CUSTOM_TEMPLATE
        )
        
        # 模拟月度数据
        monthly_data = {
            "月份": "2024年1月",
            "月度收益率": "12.5%",
            "累计收益率": "28.3%",
            "爆发股命中率": "45.2%",
            "平均爆发收益": "25.8%",
            "做T交易效率": "72.1%",
            "夏普比率": "2.15",
            "索提诺比率": "2.68",
            "最大回撤": "6.8%"
        }
        
        # 生成月度报告
        report_paths = generator.generate_custom_report(
            template_id=template_id,
            data=monthly_data,
            output_filename="monthly_report_202401",
            export_formats=['html', 'excel', 'json']
        )
        
        print(f"✅ 月度报告生成成功:")
        for format_type, path in report_paths.items():
            print(f"   {format_type.upper()}: {path}")
            if os.path.exists(path):
                print(f"   ✓ 文件已创建: {os.path.getsize(path)} bytes")
            else:
                print(f"   ✗ 文件未找到")
        
        return True
        
    except Exception as e:
        print(f"❌ 月度报告生成失败: {e}")
        logger.error(f"月度报告生成测试失败: {e}", exc_info=True)
        return False


def test_strategy_report():
    """测试策略执行报告生成"""
    print("\n" + "="*50)
    print("测试策略执行报告生成")
    print("="*50)
    
    try:
        generator = ReportGenerator({
            'report_dir': 'test_reports',
            'enable_charts': True
        })
        
        # 创建策略执行报告模板
        template_id = generator.create_custom_template(
            template_name="策略执行详情报告",
            template_type="strategy",
            sections=["收益表现", "交易统计", "执行详情", "风险指标"],
            charts=["performance_trend", "risk_analysis", "trading_volume"],
            template_content=DEFAULT_CUSTOM_TEMPLATE
        )
        
        # 模拟策略数据
        strategy_data = {
            "策略ID": "explosive_stock_ai_v1",
            "执行周期": "2024-01-01 至 2024-01-31",
            "总收益率": "22.8%",
            "年化收益率": "35.6%",
            "最大回撤": "8.5%",
            "夏普比率": "2.45",
            "总交易次数": 156,
            "胜率": "65.4%",
            "盈利因子": "1.85",
            "平均滑点": "0.08%",
            "成交率": "97.2%",
            "平均延迟": "42ms"
        }
        
        # 生成策略执行报告
        report_paths = generator.generate_custom_report(
            template_id=template_id,
            data=strategy_data,
            output_filename="strategy_report_explosive_stock_ai_v1",
            export_formats=['html', 'excel', 'json']
        )
        
        print(f"✅ 策略执行报告生成成功:")
        for format_type, path in report_paths.items():
            print(f"   {format_type.upper()}: {path}")
            if os.path.exists(path):
                print(f"   ✓ 文件已创建: {os.path.getsize(path)} bytes")
            else:
                print(f"   ✗ 文件未找到")
        
        return True
        
    except Exception as e:
        print(f"❌ 策略执行报告生成失败: {e}")
        logger.error(f"策略执行报告生成测试失败: {e}", exc_info=True)
        return False


def test_report_template_config():
    """测试报告模板配置"""
    print("\n" + "="*50)
    print("测试报告模板配置")
    print("="*50)
    
    try:
        generator = ReportGenerator()
        
        # 列出自定义模板
        templates = generator.list_custom_templates()
        
        print("✅ 自定义模板配置:")
        for template in templates:
            print(f"   {template['template_name']}:")
            print(f"     ID: {template['template_id']}")
            print(f"     类型: {template['template_type']}")
            print(f"     章节数: {template['sections_count']}")
            print(f"     图表数: {template['charts_count']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 报告模板配置测试失败: {e}")
        logger.error(f"报告模板配置测试失败: {e}", exc_info=True)
        return False


def test_enhanced_report_generator():
    """测试增强版报告生成器"""
    print("\n" + "="*50)
    print("测试增强版报告生成器")
    print("="*50)
    
    try:
        # 初始化增强版报告生成器
        enhanced_generator = EnhancedReportGenerator({
            'report_dir': 'test_reports_enhanced',
            'enable_charts': True,
            'enable_interactive_charts': True
        })
        
        # 测试自定义模板创建
        template_id = enhanced_generator.create_custom_template(
            template_name="测试自定义模板",
            template_type="custom",
            sections=["数据概览", "图表分析", "总结"],
            charts=["performance_trend", "risk_analysis"],
            template_content=DEFAULT_CUSTOM_TEMPLATE,
            variables={"custom_var": "测试变量"}
        )
        
        print(f"✅ 自定义模板创建成功: {template_id}")
        
        # 测试自定义报告生成
        test_data = {
            "总收益": "¥50,000",
            "收益率": "15.2%",
            "交易次数": 125,
            "胜率": "68.5%",
            "最大回撤": "8.3%"
        }
        
        report_paths = enhanced_generator.generate_custom_report(
            template_id=template_id,
            data=test_data,
            export_formats=['html', 'json']
        )
        
        print(f"✅ 自定义报告生成成功:")
        for format_type, path in report_paths.items():
            print(f"   {format_type.upper()}: {path}")
            if os.path.exists(path):
                print(f"   ✓ 文件已创建: {os.path.getsize(path)} bytes")
            else:
                print(f"   ✗ 文件未找到")
        
        # 测试模板列表
        templates_list = enhanced_generator.list_custom_templates()
        print(f"✅ 自定义模板列表: {len(templates_list)} 个模板")
        
        # 测试导出配置
        export_config_id = enhanced_generator.create_export_config(
            export_name="测试导出配置",
            export_format="zip",
            include_charts=True,
            include_raw_data=True,
            compression_level=6
        )
        
        print(f"✅ 导出配置创建成功: {export_config_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ 增强版报告生成器测试失败: {e}")
        logger.error(f"增强版报告生成器测试失败: {e}", exc_info=True)
        return False


def test_custom_template_management():
    """测试自定义模板管理"""
    print("\n" + "="*50)
    print("测试自定义模板管理")
    print("="*50)
    
    try:
        enhanced_generator = EnhancedReportGenerator({
            'report_dir': 'test_reports_enhanced'
        })
        
        # 创建测试模板
        template_id = enhanced_generator.create_custom_template(
            template_name="模板管理测试",
            template_type="daily",
            sections=["基础指标", "风险分析"],
            charts=["trading_volume"],
            template_content="<html><body><h1>{{ template_name }}</h1></body></html>"
        )
        
        print(f"✅ 测试模板创建: {template_id}")
        
        # 更新模板
        update_success = enhanced_generator.update_custom_template(
            template_id=template_id,
            template_name="更新后的模板名称",
            sections=["基础指标", "风险分析", "新增章节"]
        )
        
        print(f"✅ 模板更新: {'成功' if update_success else '失败'}")
        
        # 列出模板
        templates = enhanced_generator.list_custom_templates()
        print(f"✅ 模板列表: {len(templates)} 个模板")
        for template in templates:
            print(f"   - {template['template_name']} ({template['template_id']})")
        
        # 删除模板
        delete_success = enhanced_generator.delete_custom_template(template_id)
        print(f"✅ 模板删除: {'成功' if delete_success else '失败'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 自定义模板管理测试失败: {e}")
        logger.error(f"自定义模板管理测试失败: {e}", exc_info=True)
        return False


def test_report_summary_generation():
    """测试报告汇总生成"""
    print("\n" + "="*50)
    print("测试报告汇总生成")
    print("="*50)
    
    try:
        enhanced_generator = EnhancedReportGenerator({
            'report_dir': 'test_reports_enhanced'
        })
        
        # 生成报告汇总
        summary = enhanced_generator.generate_report_summary(
            start_date='2024-01-01',
            end_date='2024-01-31',
            report_types=['daily', 'weekly', 'monthly']
        )
        
        print("✅ 报告汇总生成成功:")
        print(f"   时间范围: {summary['period']['start_date']} 至 {summary['period']['end_date']}")
        print(f"   总天数: {summary['period']['total_days']}")
        print(f"   生成的报告:")
        for report_type, count in summary['reports_generated'].items():
            print(f"     {report_type}: {count} 个")
        
        # 保存汇总报告
        summary_path = f"test_reports_enhanced/summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"✅ 汇总报告已保存: {summary_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 报告汇总生成测试失败: {e}")
        logger.error(f"报告汇总生成测试失败: {e}", exc_info=True)
        return False


def test_data_export():
    """测试数据导出功能"""
    print("\n" + "="*50)
    print("测试数据导出功能")
    print("="*50)
    
    try:
        generator = ReportGenerator({
            'report_dir': 'test_reports'
        })
        
        # 创建导出配置
        export_config_id = generator.create_export_config(
            export_name="测试导出配置",
            export_format="json",
            include_charts=True,
            include_raw_data=True
        )
        
        # 创建模拟数据
        test_data = {
            "日期": "2024-01-15",
            "总收益": "¥85,000",
            "交易次数": 45,
            "胜率": "68.5%"
        }
        
        # 创建临时模板并生成报告
        template_id = generator.create_custom_template(
            template_name="导出测试模板",
            template_type="daily",
            sections=["基础指标"],
            charts=["performance_trend"],
            template_content=DEFAULT_CUSTOM_TEMPLATE
        )
        
        report_paths = generator.generate_custom_report(
            template_id=template_id,
            data=test_data,
            output_filename="export_test_report",
            export_formats=['html', 'json']
        )
        
        if report_paths:
            print(f"✅ 报告生成成功:")
            for format_type, path in report_paths.items():
                if os.path.exists(path):
                    print(f"   {format_type.upper()}: {path} ({os.path.getsize(path)} bytes)")
                else:
                    print(f"   ❌ {format_type.upper()}: 文件未找到")
            
            # 测试批量导出
            try:
                batch_export_path = generator.export_reports_batch(
                    report_paths=list(report_paths.values()),
                    export_config_id=export_config_id,
                    output_path="test_reports/exports/batch_export_test"
                )
                
                if os.path.exists(batch_export_path):
                    print(f"✅ 批量导出成功: {batch_export_path}")
                    print(f"   文件大小: {os.path.getsize(batch_export_path)} bytes")
                else:
                    print(f"❌ 批量导出文件未找到: {batch_export_path}")
                
            except Exception as e:
                print(f"⚠️ 批量导出测试跳过: {e}")
            
            return True
        else:
            print("❌ 报告生成失败")
            return False
        
    except Exception as e:
        print(f"❌ 数据导出测试失败: {e}")
        logger.error(f"数据导出测试失败: {e}", exc_info=True)
        return False


def test_report_directory_structure():
    """测试报告目录结构"""
    print("\n" + "="*50)
    print("测试报告目录结构")
    print("="*50)
    
    try:
        generator = ReportGenerator({
            'report_dir': 'test_reports'
        })
        
        # 检查目录结构
        expected_dirs = [
            'test_reports',
            'test_reports/daily',
            'test_reports/weekly', 
            'test_reports/monthly',
            'test_reports/charts',
            'test_reports/exports'
        ]
        
        print("✅ 报告目录结构检查:")
        all_exist = True
        for dir_path in expected_dirs:
            if os.path.exists(dir_path):
                print(f"   ✓ {dir_path}")
            else:
                print(f"   ✗ {dir_path} (缺失)")
                all_exist = False
        
        return all_exist
        
    except Exception as e:
        print(f"❌ 目录结构测试失败: {e}")
        logger.error(f"目录结构测试失败: {e}", exc_info=True)
        return False


def run_comprehensive_test():
    """运行综合测试"""
    print("🚀 开始报告生成系统综合测试")
    print("="*60)
    
    test_results = []
    
    # 运行各项测试
    tests = [
        ("报告目录结构", test_report_directory_structure),
        ("日度报告生成", test_daily_report),
        ("周度报告生成", test_weekly_report),
        ("月度报告生成", test_monthly_report),
        ("策略执行报告生成", test_strategy_report),
        ("报告模板配置", test_report_template_config),
        ("数据导出功能", test_data_export),
        ("增强版报告生成器", test_enhanced_report_generator),
        ("自定义模板管理", test_custom_template_management),
        ("报告汇总生成", test_report_summary_generation)
    ]
    
    for test_name, test_func in tests:
        print(f"\n🧪 执行测试: {test_name}")
        try:
            result = test_func()
            test_results.append((test_name, result))
            if result:
                print(f"✅ {test_name} - 通过")
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
            test_results.append((test_name, False))
            logger.error(f"测试 {test_name} 发生异常: {e}", exc_info=True)
    
    # 汇总测试结果
    print("\n" + "="*60)
    print("📊 测试结果汇总")
    print("="*60)
    
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name:<25} {status}")
    
    print(f"\n📈 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！报告生成系统运行正常。")
        return True
    else:
        print(f"⚠️  有 {total - passed} 项测试失败，请检查日志。")
        return False


if __name__ == "__main__":
    # 设置中文编码
    import locale
    try:
        locale.setlocale(locale.LC_ALL, 'zh_CN.UTF-8')
    except:
        pass
    
    # 运行综合测试
    success = run_comprehensive_test()
    
    # 退出码
    sys.exit(0 if success else 1)