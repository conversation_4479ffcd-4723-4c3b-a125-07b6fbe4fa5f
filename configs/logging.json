{"version": 1, "disable_existing_loggers": false, "formatters": {"standard": {"format": "%(asctime)s [%(levelname)s] %(name)s: %(message)s"}, "detailed": {"format": "%(asctime)s [%(levelname)s] %(name)s:%(lineno)d: %(message)s"}}, "handlers": {"console": {"class": "logging.StreamHandler", "level": "INFO", "formatter": "standard", "stream": "ext://sys.stdout"}, "file": {"class": "logging.handlers.RotatingFileHandler", "level": "DEBUG", "formatter": "detailed", "filename": "logs/trading_system.log", "maxBytes": 10485760, "backupCount": 5}}, "loggers": {"qlib_trading_system": {"level": "DEBUG", "handlers": ["console", "file"], "propagate": false}}, "root": {"level": "INFO", "handlers": ["console"]}}