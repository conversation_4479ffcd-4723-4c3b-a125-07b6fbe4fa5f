#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试时间显示功能
"""

import threading
import time
import requests
from qlib_trading_system.monitoring import DashboardManager

def test_time_display():
    """测试时间显示功能"""
    print("🚀 测试仪表板时间显示功能")
    
    # 创建仪表板管理器
    config = {
        'dashboard': {'host': '127.0.0.1', 'port': 8080},
        'metrics': {'collection_interval': 1.0}
    }
    manager = DashboardManager(config)
    
    # 在单独线程中启动服务器
    def start_server():
        try:
            manager.start()
        except Exception as e:
            print(f"服务器启动失败: {e}")
    
    server_thread = threading.Thread(target=start_server, daemon=True)
    server_thread.start()
    
    # 等待服务器启动
    time.sleep(3)
    
    try:
        # 测试API端点
        print("测试API端点...")
        response = requests.get('http://127.0.0.1:8080/api/metrics/current', timeout=5)
        if response.status_code == 200:
            print("✅ API端点正常工作")
            data = response.json()
            print(f"当前时间戳: {data.get('timestamp', 'N/A')}")
        else:
            print(f"❌ API端点返回错误: {response.status_code}")
        
        print("\n仪表板服务器已启动: http://127.0.0.1:8080")
        print("请在浏览器中访问查看最后更新时间是否正常显示")
        print("服务器将运行30秒...")
        
        # 运行30秒供测试
        time.sleep(30)
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
    finally:
        print("停止服务器...")
        manager.stop()
        print("测试完成")

if __name__ == "__main__":
    test_time_display()