# -*- coding: utf-8 -*-
"""
Web管理界面完整功能测试

测试所有Web管理界面的功能，包括：
1. 用户登录和权限管理系统
2. 策略配置和参数管理界面
3. 交易监控和控制面板
4. 数据查询和分析工具界面
"""

import sys
import os
import time
import logging
import asyncio
import requests
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/web_interface_complete_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class WebInterfaceCompleteTest:
    """Web管理界面完整功能测试类"""
    
    def __init__(self):
        self.base_url = "http://127.0.0.1:8000"
        self.session = requests.Session()
        self.test_results = {
            "auth_tests": [],
            "config_tests": [],
            "trading_tests": [],
            "data_tests": [],
            "monitoring_tests": []
        }
        
        # 确保日志目录存在
        os.makedirs("logs", exist_ok=True)
    
    def run_all_tests(self):
        """运行所有测试"""
        logger.info("开始Web管理界面完整功能测试")
        
        try:
            # 启动Web服务器
            self.start_web_server()
            
            # 等待服务器启动
            time.sleep(3)
            
            # 运行各模块测试
            self.test_authentication_system()
            self.test_configuration_management()
            self.test_trading_interface()
            self.test_data_query_tools()
            self.test_monitoring_system()
            
            # 生成测试报告
            self.generate_test_report()
            
        except Exception as e:
            logger.error(f"测试过程中发生错误: {e}")
        finally:
            self.cleanup()
    
    def start_web_server(self):
        """启动Web服务器"""
        logger.info("启动Web服务器...")
        
        try:
            # 导入并启动Web应用
            from qlib_trading_system.web.app import app
            import threading
            import uvicorn
            
            def run_server():
                uvicorn.run(app, host="127.0.0.1", port=8000, log_level="error")
            
            server_thread = threading.Thread(target=run_server, daemon=True)
            server_thread.start()
            
            logger.info("Web服务器启动成功")
            
        except Exception as e:
            logger.error(f"启动Web服务器失败: {e}")
            raise
    
    def test_authentication_system(self):
        """测试用户认证和权限管理系统"""
        logger.info("测试用户认证和权限管理系统...")
        
        test_cases = [
            {
                "name": "管理员登录测试",
                "username": "admin",
                "password": "admin123",
                "expected_role": "administrator"
            },
            {
                "name": "交易员登录测试",
                "username": "trader",
                "password": "trader123",
                "expected_role": "trader"
            },
            {
                "name": "分析师登录测试",
                "username": "analyst",
                "password": "analyst123",
                "expected_role": "analyst"
            },
            {
                "name": "错误密码登录测试",
                "username": "admin",
                "password": "wrong_password",
                "expected_role": None
            }
        ]
        
        for test_case in test_cases:
            try:
                # 测试登录
                login_data = {
                    "username": test_case["username"],
                    "password": test_case["password"]
                }
                
                response = self.session.post(
                    f"{self.base_url}/auth/login",
                    data=login_data
                )
                
                if test_case["expected_role"]:
                    # 期望登录成功
                    if response.status_code == 200:
                        result = response.json()
                        if result.get("user", {}).get("role") == test_case["expected_role"]:
                            self.test_results["auth_tests"].append({
                                "test": test_case["name"],
                                "status": "PASS",
                                "message": f"登录成功，角色: {result['user']['role']}"
                            })
                            
                            # 测试权限检查
                            self.test_user_permissions(test_case["expected_role"])
                        else:
                            self.test_results["auth_tests"].append({
                                "test": test_case["name"],
                                "status": "FAIL",
                                "message": f"角色不匹配，期望: {test_case['expected_role']}, 实际: {result.get('user', {}).get('role')}"
                            })
                    else:
                        self.test_results["auth_tests"].append({
                            "test": test_case["name"],
                            "status": "FAIL",
                            "message": f"登录失败，状态码: {response.status_code}"
                        })
                else:
                    # 期望登录失败
                    if response.status_code != 200:
                        self.test_results["auth_tests"].append({
                            "test": test_case["name"],
                            "status": "PASS",
                            "message": "正确拒绝了错误的登录凭据"
                        })
                    else:
                        self.test_results["auth_tests"].append({
                            "test": test_case["name"],
                            "status": "FAIL",
                            "message": "错误地接受了错误的登录凭据"
                        })
                
            except Exception as e:
                self.test_results["auth_tests"].append({
                    "test": test_case["name"],
                    "status": "ERROR",
                    "message": f"测试异常: {str(e)}"
                })
        
        # 测试登出功能
        try:
            response = self.session.post(f"{self.base_url}/auth/logout")
            if response.status_code == 200:
                self.test_results["auth_tests"].append({
                    "test": "用户登出测试",
                    "status": "PASS",
                    "message": "登出成功"
                })
            else:
                self.test_results["auth_tests"].append({
                    "test": "用户登出测试",
                    "status": "FAIL",
                    "message": f"登出失败，状态码: {response.status_code}"
                })
        except Exception as e:
            self.test_results["auth_tests"].append({
                "test": "用户登出测试",
                "status": "ERROR",
                "message": f"测试异常: {str(e)}"
            })
    
    def test_user_permissions(self, role):
        """测试用户权限"""
        permission_tests = [
            {
                "endpoint": "/api/config/all",
                "method": "GET",
                "required_permission": "view_config"
            },
            {
                "endpoint": "/api/trading/status",
                "method": "GET",
                "required_permission": "view_dashboard"
            },
            {
                "endpoint": "/api/monitoring/system",
                "method": "GET",
                "required_permission": "view_monitoring"
            }
        ]
        
        for test in permission_tests:
            try:
                if test["method"] == "GET":
                    response = self.session.get(f"{self.base_url}{test['endpoint']}")
                
                # 根据角色判断是否应该有权限
                should_have_permission = self.check_role_permission(role, test["required_permission"])
                
                if should_have_permission and response.status_code == 200:
                    self.test_results["auth_tests"].append({
                        "test": f"权限测试 - {test['endpoint']}",
                        "status": "PASS",
                        "message": f"角色 {role} 正确拥有权限"
                    })
                elif not should_have_permission and response.status_code == 403:
                    self.test_results["auth_tests"].append({
                        "test": f"权限测试 - {test['endpoint']}",
                        "status": "PASS",
                        "message": f"角色 {role} 正确被拒绝访问"
                    })
                else:
                    self.test_results["auth_tests"].append({
                        "test": f"权限测试 - {test['endpoint']}",
                        "status": "FAIL",
                        "message": f"权限检查失败，状态码: {response.status_code}"
                    })
                    
            except Exception as e:
                self.test_results["auth_tests"].append({
                    "test": f"权限测试 - {test['endpoint']}",
                    "status": "ERROR",
                    "message": f"测试异常: {str(e)}"
                })
    
    def check_role_permission(self, role, permission):
        """检查角色是否有特定权限"""
        role_permissions = {
            "administrator": [
                "view_dashboard", "manage_trading", "manage_config",
                "view_monitoring", "manage_data", "manage_users"
            ],
            "trader": [
                "view_dashboard", "manage_trading", "view_monitoring", "view_data"
            ],
            "analyst": [
                "view_dashboard", "view_monitoring", "manage_data", "view_config"
            ]
        }
        
        return permission in role_permissions.get(role, [])
    
    def test_configuration_management(self):
        """测试策略配置和参数管理界面"""
        logger.info("测试策略配置和参数管理界面...")
        
        # 先登录管理员账户
        self.login_as_admin()
        
        config_tests = [
            {
                "name": "获取所有配置",
                "endpoint": "/api/config/all",
                "method": "GET"
            },
            {
                "name": "获取交易配置",
                "endpoint": "/api/config/trading",
                "method": "GET"
            },
            {
                "name": "获取数据源配置",
                "endpoint": "/api/config/data-source",
                "method": "GET"
            },
            {
                "name": "获取模型配置",
                "endpoint": "/api/config/model",
                "method": "GET"
            },
            {
                "name": "获取系统配置",
                "endpoint": "/api/config/system",
                "method": "GET"
            }
        ]
        
        for test in config_tests:
            try:
                if test["method"] == "GET":
                    response = self.session.get(f"{self.base_url}{test['endpoint']}")
                
                if response.status_code == 200:
                    result = response.json()
                    if "config" in result or "configs" in result:
                        self.test_results["config_tests"].append({
                            "test": test["name"],
                            "status": "PASS",
                            "message": "配置获取成功"
                        })
                    else:
                        self.test_results["config_tests"].append({
                            "test": test["name"],
                            "status": "FAIL",
                            "message": "配置数据格式错误"
                        })
                else:
                    self.test_results["config_tests"].append({
                        "test": test["name"],
                        "status": "FAIL",
                        "message": f"请求失败，状态码: {response.status_code}"
                    })
                    
            except Exception as e:
                self.test_results["config_tests"].append({
                    "test": test["name"],
                    "status": "ERROR",
                    "message": f"测试异常: {str(e)}"
                })
        
        # 测试配置更新
        self.test_config_update()
    
    def test_config_update(self):
        """测试配置更新功能"""
        try:
            # 测试更新交易配置
            update_data = {
                "capital_mode": "small",
                "total_capital": 100000.0,
                "max_position_size": 0.3,
                "max_single_stock_ratio": 1.0,
                "max_daily_loss_pct": 0.01,
                "max_drawdown_pct": 0.30,
                "stop_loss_pct": 0.02,
                "position_size_limit": 1,
                "enable_t_plus_zero": True,
                "base_position_ratio": 0.75,
                "t_position_ratio": 0.20,
                "cash_reserve_ratio": 0.05,
                "stock_selection_threshold": 0.7,
                "intraday_confidence_threshold": 0.6,
                "min_profit_expectation": 0.01
            }
            
            response = self.session.put(
                f"{self.base_url}/api/config/trading",
                json=update_data
            )
            
            if response.status_code == 200:
                self.test_results["config_tests"].append({
                    "test": "交易配置更新测试",
                    "status": "PASS",
                    "message": "配置更新成功"
                })
            else:
                self.test_results["config_tests"].append({
                    "test": "交易配置更新测试",
                    "status": "FAIL",
                    "message": f"配置更新失败，状态码: {response.status_code}"
                })
                
        except Exception as e:
            self.test_results["config_tests"].append({
                "test": "交易配置更新测试",
                "status": "ERROR",
                "message": f"测试异常: {str(e)}"
            })
    
    def test_trading_interface(self):
        """测试交易监控和控制面板"""
        logger.info("测试交易监控和控制面板...")
        
        # 先登录交易员账户
        self.login_as_trader()
        
        trading_tests = [
            {
                "name": "获取交易状态",
                "endpoint": "/api/trading/status",
                "method": "GET"
            },
            {
                "name": "获取持仓信息",
                "endpoint": "/api/trading/positions",
                "method": "GET"
            },
            {
                "name": "获取订单列表",
                "endpoint": "/api/trading/orders",
                "method": "GET"
            },
            {
                "name": "获取实时数据",
                "endpoint": "/api/trading/realtime",
                "method": "GET"
            }
        ]
        
        for test in trading_tests:
            try:
                if test["method"] == "GET":
                    response = self.session.get(f"{self.base_url}{test['endpoint']}")
                
                if response.status_code == 200:
                    result = response.json()
                    self.test_results["trading_tests"].append({
                        "test": test["name"],
                        "status": "PASS",
                        "message": "数据获取成功"
                    })
                else:
                    self.test_results["trading_tests"].append({
                        "test": test["name"],
                        "status": "FAIL",
                        "message": f"请求失败，状态码: {response.status_code}"
                    })
                    
            except Exception as e:
                self.test_results["trading_tests"].append({
                    "test": test["name"],
                    "status": "ERROR",
                    "message": f"测试异常: {str(e)}"
                })
        
        # 测试交易操作
        self.test_trading_operations()
    
    def test_trading_operations(self):
        """测试交易操作功能"""
        try:
            # 测试下单
            order_data = {
                "symbol": "000001.SZ",
                "side": "buy",
                "quantity": 100,
                "order_type": "market"
            }
            
            response = self.session.post(
                f"{self.base_url}/api/trading/orders",
                json=order_data
            )
            
            if response.status_code == 200:
                result = response.json()
                order_id = result.get("order_id")
                
                self.test_results["trading_tests"].append({
                    "test": "下单功能测试",
                    "status": "PASS",
                    "message": f"下单成功，订单号: {order_id}"
                })
                
                # 测试撤单
                if order_id:
                    cancel_response = self.session.delete(
                        f"{self.base_url}/api/trading/orders/{order_id}"
                    )
                    
                    if cancel_response.status_code == 200:
                        self.test_results["trading_tests"].append({
                            "test": "撤单功能测试",
                            "status": "PASS",
                            "message": "撤单成功"
                        })
                    else:
                        self.test_results["trading_tests"].append({
                            "test": "撤单功能测试",
                            "status": "FAIL",
                            "message": f"撤单失败，状态码: {cancel_response.status_code}"
                        })
            else:
                self.test_results["trading_tests"].append({
                    "test": "下单功能测试",
                    "status": "FAIL",
                    "message": f"下单失败，状态码: {response.status_code}"
                })
                
        except Exception as e:
            self.test_results["trading_tests"].append({
                "test": "交易操作测试",
                "status": "ERROR",
                "message": f"测试异常: {str(e)}"
            })
    
    def test_data_query_tools(self):
        """测试数据查询和分析工具界面"""
        logger.info("测试数据查询和分析工具界面...")
        
        # 先登录分析师账户
        self.login_as_analyst()
        
        data_tests = [
            {
                "name": "股票搜索功能",
                "endpoint": "/api/data/stocks/search?keyword=平安银行",
                "method": "GET"
            },
            {
                "name": "获取股票信息",
                "endpoint": "/api/data/stock/000001.SZ",
                "method": "GET"
            },
            {
                "name": "获取市场概览",
                "endpoint": "/api/data/market/overview",
                "method": "GET"
            },
            {
                "name": "获取热门股票",
                "endpoint": "/api/data/stocks/hot?list_type=gainers&limit=10",
                "method": "GET"
            }
        ]
        
        for test in data_tests:
            try:
                if test["method"] == "GET":
                    response = self.session.get(f"{self.base_url}{test['endpoint']}")
                
                if response.status_code == 200:
                    result = response.json()
                    self.test_results["data_tests"].append({
                        "test": test["name"],
                        "status": "PASS",
                        "message": "数据查询成功"
                    })
                else:
                    self.test_results["data_tests"].append({
                        "test": test["name"],
                        "status": "FAIL",
                        "message": f"请求失败，状态码: {response.status_code}"
                    })
                    
            except Exception as e:
                self.test_results["data_tests"].append({
                    "test": test["name"],
                    "status": "ERROR",
                    "message": f"测试异常: {str(e)}"
                })
        
        # 测试历史数据查询
        self.test_historical_data_query()
    
    def test_historical_data_query(self):
        """测试历史数据查询功能"""
        try:
            # 测试价格数据查询
            start_date = "2024-01-01"
            end_date = "2024-01-31"
            
            response = self.session.get(
                f"{self.base_url}/api/data/price/000001.SZ?start_date={start_date}&end_date={end_date}"
            )
            
            if response.status_code == 200:
                result = response.json()
                if "price_data" in result:
                    self.test_results["data_tests"].append({
                        "test": "历史价格数据查询",
                        "status": "PASS",
                        "message": f"获取到 {result.get('total', 0)} 条价格数据"
                    })
                else:
                    self.test_results["data_tests"].append({
                        "test": "历史价格数据查询",
                        "status": "FAIL",
                        "message": "价格数据格式错误"
                    })
            else:
                self.test_results["data_tests"].append({
                    "test": "历史价格数据查询",
                    "status": "FAIL",
                    "message": f"查询失败，状态码: {response.status_code}"
                })
            
            # 测试技术指标查询
            response = self.session.get(
                f"{self.base_url}/api/data/indicators/000001.SZ?indicator_type=ma&period=20"
            )
            
            if response.status_code == 200:
                result = response.json()
                if "indicators" in result:
                    self.test_results["data_tests"].append({
                        "test": "技术指标数据查询",
                        "status": "PASS",
                        "message": f"获取到 {result.get('total', 0)} 条指标数据"
                    })
                else:
                    self.test_results["data_tests"].append({
                        "test": "技术指标数据查询",
                        "status": "FAIL",
                        "message": "指标数据格式错误"
                    })
            else:
                self.test_results["data_tests"].append({
                    "test": "技术指标数据查询",
                    "status": "FAIL",
                    "message": f"查询失败，状态码: {response.status_code}"
                })
                
        except Exception as e:
            self.test_results["data_tests"].append({
                "test": "历史数据查询测试",
                "status": "ERROR",
                "message": f"测试异常: {str(e)}"
            })
    
    def test_monitoring_system(self):
        """测试系统监控功能"""
        logger.info("测试系统监控功能...")
        
        # 先登录管理员账户
        self.login_as_admin()
        
        monitoring_tests = [
            {
                "name": "获取系统指标",
                "endpoint": "/api/monitoring/system",
                "method": "GET"
            },
            {
                "name": "获取交易指标",
                "endpoint": "/api/monitoring/trading",
                "method": "GET"
            },
            {
                "name": "获取告警信息",
                "endpoint": "/api/monitoring/alerts?limit=10",
                "method": "GET"
            },
            {
                "name": "获取系统日志",
                "endpoint": "/api/monitoring/logs?limit=50",
                "method": "GET"
            },
            {
                "name": "获取系统健康状态",
                "endpoint": "/api/monitoring/health",
                "method": "GET"
            }
        ]
        
        for test in monitoring_tests:
            try:
                if test["method"] == "GET":
                    response = self.session.get(f"{self.base_url}{test['endpoint']}")
                
                if response.status_code == 200:
                    result = response.json()
                    self.test_results["monitoring_tests"].append({
                        "test": test["name"],
                        "status": "PASS",
                        "message": "监控数据获取成功"
                    })
                else:
                    self.test_results["monitoring_tests"].append({
                        "test": test["name"],
                        "status": "FAIL",
                        "message": f"请求失败，状态码: {response.status_code}"
                    })
                    
            except Exception as e:
                self.test_results["monitoring_tests"].append({
                    "test": test["name"],
                    "status": "ERROR",
                    "message": f"测试异常: {str(e)}"
                })
        
        # 测试告警管理功能
        self.test_alert_management()
    
    def test_alert_management(self):
        """测试告警管理功能"""
        try:
            # 测试添加告警
            alert_data = {
                "level": "INFO",
                "message": "Web界面测试告警",
                "source": "web_test"
            }
            
            response = self.session.post(
                f"{self.base_url}/api/monitoring/alerts",
                json=alert_data
            )
            
            if response.status_code == 200:
                self.test_results["monitoring_tests"].append({
                    "test": "添加告警功能",
                    "status": "PASS",
                    "message": "告警添加成功"
                })
                
                # 测试解决告警
                resolve_response = self.session.put(
                    f"{self.base_url}/api/monitoring/alerts/0/resolve"
                )
                
                if resolve_response.status_code == 200:
                    self.test_results["monitoring_tests"].append({
                        "test": "解决告警功能",
                        "status": "PASS",
                        "message": "告警解决成功"
                    })
                else:
                    self.test_results["monitoring_tests"].append({
                        "test": "解决告警功能",
                        "status": "FAIL",
                        "message": f"告警解决失败，状态码: {resolve_response.status_code}"
                    })
            else:
                self.test_results["monitoring_tests"].append({
                    "test": "添加告警功能",
                    "status": "FAIL",
                    "message": f"告警添加失败，状态码: {response.status_code}"
                })
                
        except Exception as e:
            self.test_results["monitoring_tests"].append({
                "test": "告警管理测试",
                "status": "ERROR",
                "message": f"测试异常: {str(e)}"
            })
    
    def login_as_admin(self):
        """以管理员身份登录"""
        login_data = {"username": "admin", "password": "admin123"}
        self.session.post(f"{self.base_url}/auth/login", data=login_data)
    
    def login_as_trader(self):
        """以交易员身份登录"""
        login_data = {"username": "trader", "password": "trader123"}
        self.session.post(f"{self.base_url}/auth/login", data=login_data)
    
    def login_as_analyst(self):
        """以分析师身份登录"""
        login_data = {"username": "analyst", "password": "analyst123"}
        self.session.post(f"{self.base_url}/auth/login", data=login_data)
    
    def generate_test_report(self):
        """生成测试报告"""
        logger.info("生成测试报告...")
        
        report = {
            "test_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "summary": {},
            "details": self.test_results
        }
        
        # 计算各模块测试统计
        for module, tests in self.test_results.items():
            total = len(tests)
            passed = len([t for t in tests if t["status"] == "PASS"])
            failed = len([t for t in tests if t["status"] == "FAIL"])
            errors = len([t for t in tests if t["status"] == "ERROR"])
            
            report["summary"][module] = {
                "total": total,
                "passed": passed,
                "failed": failed,
                "errors": errors,
                "pass_rate": f"{(passed/total*100):.1f}%" if total > 0 else "0%"
            }
        
        # 计算总体统计
        total_tests = sum(len(tests) for tests in self.test_results.values())
        total_passed = sum(len([t for t in tests if t["status"] == "PASS"]) for tests in self.test_results.values())
        total_failed = sum(len([t for t in tests if t["status"] == "FAIL"]) for tests in self.test_results.values())
        total_errors = sum(len([t for t in tests if t["status"] == "ERROR"]) for tests in self.test_results.values())
        
        report["summary"]["overall"] = {
            "total": total_tests,
            "passed": total_passed,
            "failed": total_failed,
            "errors": total_errors,
            "pass_rate": f"{(total_passed/total_tests*100):.1f}%" if total_tests > 0 else "0%"
        }
        
        # 保存报告
        report_file = f"logs/web_interface_complete_test_report_{int(time.time())}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        # 打印报告摘要
        logger.info("=" * 60)
        logger.info("Web管理界面完整功能测试报告")
        logger.info("=" * 60)
        logger.info(f"测试时间: {report['test_time']}")
        logger.info(f"总体统计: 总计 {total_tests} 个测试，通过 {total_passed} 个，失败 {total_failed} 个，错误 {total_errors} 个")
        logger.info(f"总体通过率: {report['summary']['overall']['pass_rate']}")
        logger.info("")
        
        for module, stats in report["summary"].items():
            if module != "overall":
                logger.info(f"{module}: {stats['passed']}/{stats['total']} 通过 ({stats['pass_rate']})")
        
        logger.info("")
        logger.info(f"详细报告已保存到: {report_file}")
        logger.info("=" * 60)
        
        return report
    
    def cleanup(self):
        """清理资源"""
        logger.info("清理测试资源...")
        
        try:
            # 关闭会话
            self.session.close()
            
        except Exception as e:
            logger.error(f"清理资源时发生错误: {e}")

def main():
    """主函数"""
    print("Qlib交易系统Web管理界面完整功能测试")
    print("=" * 60)
    
    tester = WebInterfaceCompleteTest()
    tester.run_all_tests()

if __name__ == "__main__":
    main()