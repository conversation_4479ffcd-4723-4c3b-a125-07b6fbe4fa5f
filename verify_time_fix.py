#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
验证时间显示修复
"""

import threading
import time
import webbrowser
import os
from qlib_trading_system.monitoring import DashboardManager

def verify_time_fix():
    """验证时间显示修复"""
    print("🔧 验证仪表板时间显示修复")
    
    # 创建仪表板管理器
    config = {
        'dashboard': {'host': '127.0.0.1', 'port': 8080},
        'metrics': {'collection_interval': 1.0}
    }
    manager = DashboardManager(config)
    
    # 在单独线程中启动服务器
    def start_server():
        try:
            manager.start()
        except Exception as e:
            print(f"服务器启动失败: {e}")
    
    server_thread = threading.Thread(target=start_server, daemon=True)
    server_thread.start()
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    time.sleep(3)
    
    try:
        print("\n✅ 仪表板服务器已启动")
        print("🌐 仪表板地址: http://127.0.0.1:8080")
        print("🧪 测试页面: file://" + os.path.abspath("test_time_fix.html"))
        
        print("\n📋 修复内容:")
        print("  1. ✅ 在HTML模板中添加了 updateLastUpdateTime() 函数")
        print("  2. ✅ 在 updateMetrics() 函数中调用时间更新")
        print("  3. ✅ 时间格式包含小时:分钟:秒")
        
        print("\n🔍 验证步骤:")
        print("  1. 打开浏览器访问仪表板")
        print("  2. 检查右上角'最后更新'时间是否正常显示")
        print("  3. 观察时间是否会自动更新")
        
        # 尝试自动打开浏览器
        try:
            print("\n🚀 正在打开浏览器...")
            webbrowser.open('http://127.0.0.1:8080')
            time.sleep(2)
            webbrowser.open('file://' + os.path.abspath("test_time_fix.html"))
        except Exception as e:
            print(f"⚠️ 无法自动打开浏览器: {e}")
            print("请手动打开浏览器访问上述地址")
        
        print("\n⏰ 服务器将运行60秒供验证...")
        print("按 Ctrl+C 可提前停止")
        
        # 运行60秒供验证
        for i in range(60, 0, -1):
            print(f"\r⏱️ 剩余时间: {i:2d}秒", end="", flush=True)
            time.sleep(1)
        
        print("\n")
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断验证")
    except Exception as e:
        print(f"\n❌ 验证过程中发生错误: {e}")
    finally:
        print("🛑 停止服务器...")
        manager.stop()
        print("✅ 验证完成")

if __name__ == "__main__":
    verify_time_fix()