"""
熔断机制集成测试
测试极端行情熔断机制的完整功能
"""

import sys
import os
import time
import json
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from qlib_trading_system.risk.circuit_breaker import (
    CircuitBreaker, CircuitBreakerConfig, MarketAnomalyEvent, AlertLevel
)
from qlib_trading_system.risk.circuit_breaker_manager import (
    CircuitBreakerManager, get_circuit_breaker_manager
)
from qlib_trading_system.risk.anomaly_trading_detector import (
    AnomalousTradeDetector, TradingAnomalyEvent
)
from config.circuit_breaker_config import (
    CircuitBreakerConfigManager, get_circuit_breaker_config
)


class CircuitBreakerIntegrationTest:
    """熔断机制集成测试类"""
    
    def __init__(self):
        self.test_results = []
        self.config_manager = get_circuit_breaker_config()
        self.circuit_breaker_manager = None
        self.anomaly_detector = None
        
        print("=" * 60)
        print("熔断机制集成测试开始")
        print("=" * 60)
    
    def setup_test_environment(self) -> None:
        """设置测试环境"""
        print("\n1. 设置测试环境...")
        
        # 创建测试配置
        test_config = CircuitBreakerConfig(
            single_stock_drop_threshold=-0.05,
            single_stock_crash_threshold=-0.10,
            market_drop_threshold=-0.02,
            market_crash_threshold=-0.03,
            volume_surge_threshold=2.0,
            volume_dry_threshold=0.5,
            consecutive_loss_days=2,
            daily_loss_threshold=-0.01
        )
        
        # 初始化熔断管理器
        self.circuit_breaker_manager = CircuitBreakerManager(test_config)
        
        # 初始化异常检测器
        risk_limits = {
            'account_value': 100000,
            'max_position_ratio': 0.3,
            'max_drawdown': 0.15,
            'max_leverage': 2.0
        }
        self.anomaly_detector = AnomalousTradeDetector(risk_limits)
        
        # 注册回调函数
        self.circuit_breaker_manager.register_callback(
            "on_anomaly_detected", self._on_anomaly_detected
        )
        self.circuit_breaker_manager.register_callback(
            "on_circuit_breaker_triggered", self._on_circuit_breaker_triggered
        )
        self.circuit_breaker_manager.register_callback(
            "on_emergency_exit", self._on_emergency_exit
        )
        
        # 启动监控
        self.circuit_breaker_manager.start_monitoring()
        
        print("✓ 测试环境设置完成")
        time.sleep(1)
    
    def test_price_anomaly_detection(self) -> bool:
        """测试价格异常检测"""
        print("\n2. 测试价格异常检测...")
        
        try:
            # 模拟正常价格数据
            normal_data = {
                "market": {"index_change": -0.01, "volume_ratio": 1.0},
                "stocks": {
                    "TEST001": {
                        "current_price": 9.8,
                        "reference_price": 10.0,
                        "volume": 500000,
                        "avg_volume": 500000
                    }
                }
            }
            
            self.circuit_breaker_manager.update_market_data(normal_data)
            time.sleep(2)
            
            # 模拟价格异常数据
            crash_data = {
                "market": {"index_change": -0.04, "volume_ratio": 1.5},
                "stocks": {
                    "TEST001": {
                        "current_price": 8.5,
                        "reference_price": 10.0,
                        "volume": 1000000,
                        "avg_volume": 500000
                    }
                }
            }
            
            self.circuit_breaker_manager.update_market_data(crash_data)
            time.sleep(3)
            
            # 检查是否检测到异常
            status = self.circuit_breaker_manager.get_current_status()
            circuit_breaker_triggered = status["circuit_breaker_status"]["circuit_breaker_triggered"]
            
            if circuit_breaker_triggered:
                print("✓ 价格异常检测成功，熔断机制已触发")
                return True
            else:
                print("✗ 价格异常检测失败，熔断机制未触发")
                return False
                
        except Exception as e:
            print(f"✗ 价格异常检测测试出错: {e}")
            return False
    
    def test_volume_anomaly_detection(self) -> bool:
        """测试成交量异常检测"""
        print("\n3. 测试成交量异常检测...")
        
        try:
            # 重置熔断器
            self.circuit_breaker_manager.reset_circuit_breaker()
            time.sleep(1)
            
            # 模拟成交量异常数据
            volume_surge_data = {
                "market": {"index_change": -0.01, "volume_ratio": 1.0},
                "stocks": {
                    "TEST002": {
                        "current_price": 9.9,
                        "reference_price": 10.0,
                        "volume": 2500000,  # 5倍成交量
                        "avg_volume": 500000
                    }
                }
            }
            
            self.circuit_breaker_manager.update_market_data(volume_surge_data)
            time.sleep(2)
            
            # 检查防御策略是否激活
            status = self.circuit_breaker_manager.get_current_status()
            active_defenses = status["circuit_breaker_status"]["active_defenses"]
            
            if active_defenses:
                print("✓ 成交量异常检测成功，防御策略已激活")
                return True
            else:
                print("✗ 成交量异常检测失败，防御策略未激活")
                return False
                
        except Exception as e:
            print(f"✗ 成交量异常检测测试出错: {e}")
            return False
    
    def test_black_swan_detection(self) -> bool:
        """测试黑天鹅事件检测"""
        print("\n4. 测试黑天鹅事件检测...")
        
        try:
            # 重置熔断器
            self.circuit_breaker_manager.reset_circuit_breaker()
            time.sleep(1)
            
            # 模拟黑天鹅事件数据
            black_swan_data = {
                "market": {"index_change": -0.01, "volume_ratio": 1.0},
                "stocks": {
                    "TEST003": {
                        "current_price": 9.5,
                        "reference_price": 10.0,
                        "volume": 800000,
                        "avg_volume": 500000
                    }
                },
                "news": [
                    "某公司被立案调查",
                    "监管部门发布重大处罚决定",
                    "市场出现恐慌情绪"
                ]
            }
            
            self.circuit_breaker_manager.update_market_data(black_swan_data)
            time.sleep(3)
            
            # 检查是否检测到黑天鹅事件
            status = self.circuit_breaker_manager.get_current_status()
            circuit_breaker_triggered = status["circuit_breaker_status"]["circuit_breaker_triggered"]
            
            if circuit_breaker_triggered:
                print("✓ 黑天鹅事件检测成功，熔断机制已触发")
                return True
            else:
                print("✗ 黑天鹅事件检测失败，熔断机制未触发")
                return False
                
        except Exception as e:
            print(f"✗ 黑天鹅事件检测测试出错: {e}")
            return False
    
    def test_consecutive_loss_detection(self) -> bool:
        """测试连续亏损检测"""
        print("\n5. 测试连续亏损检测...")
        
        try:
            # 重置熔断器
            self.circuit_breaker_manager.reset_circuit_breaker()
            time.sleep(1)
            
            # 模拟连续亏损数据
            loss_position_data = {
                "daily_pnl": -0.015,  # 1.5%亏损
                "positions": {
                    "TEST004": {
                        "quantity": 1000,
                        "market_value": 9000,
                        "unrealized_pnl_pct": -0.1
                    }
                }
            }
            
            # 模拟连续两天亏损
            for day in range(2):
                self.circuit_breaker_manager.update_position_data(loss_position_data)
                # 手动触发连续亏损检查
                anomaly = self.circuit_breaker_manager.circuit_breaker.check_consecutive_losses(-0.015)
                if anomaly:
                    result = self.circuit_breaker_manager.circuit_breaker.process_anomalies([anomaly])
                    break
                time.sleep(1)
            
            # 检查是否检测到连续亏损
            status = self.circuit_breaker_manager.get_current_status()
            consecutive_loss_days = status["circuit_breaker_status"]["consecutive_loss_days"]
            
            if consecutive_loss_days >= 2:
                print("✓ 连续亏损检测成功，已记录连续亏损天数")
                return True
            else:
                print("✗ 连续亏损检测失败，未正确记录亏损天数")
                return False
                
        except Exception as e:
            print(f"✗ 连续亏损检测测试出错: {e}")
            return False
    
    def test_anomalous_trading_detection(self) -> bool:
        """测试异常交易行为检测"""
        print("\n6. 测试异常交易行为检测...")
        
        try:
            # 构造异常交易数据
            trading_data = {
                "trades": [
                    {
                        "timestamp": datetime.now() - timedelta(minutes=i),
                        "symbol": "TEST005",
                        "quantity": 1000 if i < 10 else 5000,  # 最近交易规模异常
                        "price": 10.0,
                        "side": "BUY"
                    }
                    for i in range(20)
                ],
                "positions": {
                    "TEST005": {
                        "quantity": 10000,
                        "market_value": 100000,  # 超过30%限制
                        "unrealized_pnl_pct": -0.05
                    }
                },
                "account": {
                    "total_value": 100000,
                    "max_drawdown": 0.18,  # 接近20%限制
                    "leverage_ratio": 1.5
                },
                "error_logs": [],
                "response_times": [50, 60, 55, 200, 180, 150]  # 响应时间变慢
            }
            
            # 执行异常检测
            anomalies = self.anomaly_detector.detect_all_anomalies(trading_data)
            
            if anomalies:
                print(f"✓ 异常交易行为检测成功，发现{len(anomalies)}个异常:")
                for anomaly in anomalies:
                    print(f"  - {anomaly.anomaly_type.value}: {anomaly.description}")
                return True
            else:
                print("✗ 异常交易行为检测失败，未发现异常")
                return False
                
        except Exception as e:
            print(f"✗ 异常交易行为检测测试出错: {e}")
            return False
    
    def test_emergency_stop_loss(self) -> bool:
        """测试紧急止损功能"""
        print("\n7. 测试紧急止损功能...")
        
        try:
            # 重置熔断器
            self.circuit_breaker_manager.reset_circuit_breaker()
            time.sleep(1)
            
            # 模拟紧急情况数据
            emergency_data = {
                "market": {"index_change": -0.08, "volume_ratio": 0.2},  # 市场崩盘
                "stocks": {
                    "TEST006": {
                        "current_price": 8.0,
                        "reference_price": 10.0,
                        "volume": 100000,
                        "avg_volume": 500000
                    }
                }
            }
            
            # 模拟持仓数据
            position_data = {
                "positions": {
                    "TEST006": {
                        "quantity": 1000,
                        "market_value": 8000,
                        "unrealized_pnl_pct": -0.2
                    }
                }
            }
            
            self.circuit_breaker_manager.update_market_data(emergency_data)
            self.circuit_breaker_manager.update_position_data(position_data)
            time.sleep(3)
            
            # 检查是否触发紧急止损
            status = self.circuit_breaker_manager.get_current_status()
            circuit_breaker_triggered = status["circuit_breaker_status"]["circuit_breaker_triggered"]
            
            if circuit_breaker_triggered:
                print("✓ 紧急止损功能测试成功，熔断机制已触发")
                return True
            else:
                print("✗ 紧急止损功能测试失败，熔断机制未触发")
                return False
                
        except Exception as e:
            print(f"✗ 紧急止损功能测试出错: {e}")
            return False
    
    def test_defense_strategy_activation(self) -> bool:
        """测试防御策略激活"""
        print("\n8. 测试防御策略激活...")
        
        try:
            # 重置熔断器
            self.circuit_breaker_manager.reset_circuit_breaker()
            time.sleep(1)
            
            # 获取防御建议
            recommendations = self.circuit_breaker_manager.get_defense_recommendations("TEST007")
            
            # 模拟中等风险情况
            moderate_risk_data = {
                "market": {"index_change": -0.025, "volume_ratio": 1.2},
                "stocks": {
                    "TEST007": {
                        "current_price": 9.2,
                        "reference_price": 10.0,
                        "volume": 600000,
                        "avg_volume": 500000
                    }
                }
            }
            
            self.circuit_breaker_manager.update_market_data(moderate_risk_data)
            time.sleep(2)
            
            # 再次获取防御建议
            updated_recommendations = self.circuit_breaker_manager.get_defense_recommendations("TEST007")
            
            if updated_recommendations["risk_level"] != "LOW":
                print(f"✓ 防御策略激活成功，风险级别: {updated_recommendations['risk_level']}")
                print(f"  建议动作: {updated_recommendations['suggested_actions']}")
                return True
            else:
                print("✗ 防御策略激活失败，风险级别仍为LOW")
                return False
                
        except Exception as e:
            print(f"✗ 防御策略激活测试出错: {e}")
            return False
    
    def test_circuit_breaker_reset(self) -> bool:
        """测试熔断器重置功能"""
        print("\n9. 测试熔断器重置功能...")
        
        try:
            # 确保熔断器已触发
            if not self.circuit_breaker_manager.circuit_breaker.circuit_breaker_triggered:
                # 手动触发熔断
                self.circuit_breaker_manager.manual_trigger_circuit_breaker("测试重置功能")
                time.sleep(1)
            
            # 尝试重置（应该失败，因为冷却时间未到）
            reset_result = self.circuit_breaker_manager.reset_circuit_breaker()
            
            if not reset_result:
                print("✓ 熔断器冷却机制正常工作")
                
                # 修改配置以缩短冷却时间进行测试
                self.circuit_breaker_manager.circuit_breaker.config.circuit_breaker_cooldown = 1
                time.sleep(2)
                
                # 再次尝试重置
                reset_result = self.circuit_breaker_manager.reset_circuit_breaker()
                
                if reset_result:
                    print("✓ 熔断器重置功能测试成功")
                    return True
                else:
                    print("✗ 熔断器重置功能测试失败")
                    return False
            else:
                print("✗ 熔断器冷却机制异常")
                return False
                
        except Exception as e:
            print(f"✗ 熔断器重置功能测试出错: {e}")
            return False
    
    def _on_anomaly_detected(self, data: Dict[str, Any]) -> None:
        """异常检测回调"""
        anomalies = data.get("anomalies", [])
        print(f"[回调] 检测到{len(anomalies)}个异常")
        for anomaly in anomalies:
            print(f"  - {anomaly.event_type}: {anomaly.description}")
    
    def _on_circuit_breaker_triggered(self, data: Dict[str, Any]) -> None:
        """熔断触发回调"""
        trigger_anomaly = data.get("trigger_anomaly")
        print(f"[回调] 熔断机制触发: {trigger_anomaly.event_type}")
    
    def _on_emergency_exit(self, data: Dict[str, Any]) -> None:
        """紧急退出回调"""
        liquidation_orders = data.get("liquidation_orders", [])
        print(f"[回调] 紧急退出，清算{len(liquidation_orders)}个持仓")
    
    def run_all_tests(self) -> None:
        """运行所有测试"""
        test_methods = [
            ("设置测试环境", self.setup_test_environment),
            ("价格异常检测", self.test_price_anomaly_detection),
            ("成交量异常检测", self.test_volume_anomaly_detection),
            ("黑天鹅事件检测", self.test_black_swan_detection),
            ("连续亏损检测", self.test_consecutive_loss_detection),
            ("异常交易行为检测", self.test_anomalous_trading_detection),
            ("紧急止损功能", self.test_emergency_stop_loss),
            ("防御策略激活", self.test_defense_strategy_activation),
            ("熔断器重置功能", self.test_circuit_breaker_reset)
        ]
        
        passed_tests = 0
        total_tests = len(test_methods) - 1  # 排除setup
        
        for test_name, test_method in test_methods:
            try:
                if test_name == "设置测试环境":
                    test_method()
                else:
                    result = test_method()
                    if result:
                        passed_tests += 1
                        self.test_results.append({"test": test_name, "result": "PASS"})
                    else:
                        self.test_results.append({"test": test_name, "result": "FAIL"})
            except Exception as e:
                print(f"✗ {test_name}测试异常: {e}")
                self.test_results.append({"test": test_name, "result": "ERROR", "error": str(e)})
        
        # 清理测试环境
        self.cleanup_test_environment()
        
        # 输出测试结果
        self.print_test_summary(passed_tests, total_tests)
    
    def cleanup_test_environment(self) -> None:
        """清理测试环境"""
        print("\n10. 清理测试环境...")
        
        if self.circuit_breaker_manager:
            self.circuit_breaker_manager.stop_monitoring()
        
        print("✓ 测试环境清理完成")
    
    def print_test_summary(self, passed_tests: int, total_tests: int) -> None:
        """打印测试摘要"""
        print("\n" + "=" * 60)
        print("测试结果摘要")
        print("=" * 60)
        
        for result in self.test_results:
            status_symbol = "✓" if result["result"] == "PASS" else "✗"
            print(f"{status_symbol} {result['test']}: {result['result']}")
            if "error" in result:
                print(f"  错误: {result['error']}")
        
        print(f"\n通过测试: {passed_tests}/{total_tests}")
        print(f"成功率: {passed_tests/total_tests*100:.1f}%")
        
        if passed_tests == total_tests:
            print("\n🎉 所有测试通过！熔断机制功能正常。")
        else:
            print(f"\n⚠️  有{total_tests - passed_tests}个测试失败，请检查相关功能。")
        
        print("=" * 60)


def main():
    """主函数"""
    try:
        # 创建并运行测试
        test_runner = CircuitBreakerIntegrationTest()
        test_runner.run_all_tests()
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"测试运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()