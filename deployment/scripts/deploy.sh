#!/bin/bash
# 自动化部署脚本
# 用法: ./deploy.sh [environment] [version] [services...]

set -e  # 遇到错误立即退出

# 配置变量
APP_NAME="qlib-trading-system"
ENVIRONMENT=${1:-staging}
VERSION=${2:-latest}
SERVICES=${@:3}  # 从第3个参数开始的所有参数作为服务列表
DOCKER_REGISTRY="localhost:5000"
NAMESPACE="qlib-trading"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [environment] [version] [services...]"
    echo ""
    echo "参数:"
    echo "  environment  部署环境 (development|staging|production) [默认: staging]"
    echo "  version      版本标签 [默认: latest]"
    echo "  services     要部署的服务列表 [默认: 所有服务]"
    echo ""
    echo "示例:"
    echo "  $0 production v1.0.0"
    echo "  $0 staging latest api data-processor"
    echo "  $0 development"
    echo ""
    echo "环境变量:"
    echo "  DOCKER_REGISTRY  Docker镜像仓库地址 [默认: localhost:5000]"
    echo "  NAMESPACE        Kubernetes命名空间 [默认: qlib-trading]"
}

# 检查参数
if [[ "$1" == "-h" || "$1" == "--help" ]]; then
    show_help
    exit 0
fi

# 验证环境参数
if [[ ! "$ENVIRONMENT" =~ ^(development|staging|production)$ ]]; then
    log_error "无效的环境: $ENVIRONMENT"
    log_info "支持的环境: development, staging, production"
    exit 1
fi

log_info "开始部署 $APP_NAME 到 $ENVIRONMENT 环境，版本: $VERSION"

# 1. 验证部署环境
validate_environment() {
    log_info "验证部署环境..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装或不在PATH中"
        exit 1
    fi
    
    # 检查Docker daemon
    if ! docker info &> /dev/null; then
        log_error "Docker daemon未运行"
        exit 1
    fi
    
    # 检查kubectl连接
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl未安装或不在PATH中"
        exit 1
    fi
    
    if ! kubectl cluster-info &> /dev/null; then
        log_error "无法连接到Kubernetes集群"
        exit 1
    fi
    
    # 检查命名空间
    if ! kubectl get namespace $NAMESPACE &> /dev/null; then
        log_info "创建命名空间: $NAMESPACE"
        kubectl create namespace $NAMESPACE
    fi
    
    # 检查Docker registry连接
    if ! docker pull hello-world &> /dev/null; then
        log_warning "Docker registry连接可能有问题"
    fi
    
    log_success "环境验证完成"
}

# 2. 构建和推送镜像
build_and_push_images() {
    log_info "构建和推送Docker镜像..."
    
    # 如果没有指定服务，使用默认服务列表
    if [ -z "$SERVICES" ]; then
        SERVICES="api data-processor model-server risk-monitor"
    fi
    
    # 创建临时构建目录
    BUILD_DIR="deployment/build"
    mkdir -p $BUILD_DIR
    
    for service in $SERVICES; do
        log_info "构建服务: $service"
        
        IMAGE_TAG="$DOCKER_REGISTRY/$APP_NAME-$service:$VERSION"
        
        # 创建服务专用的Dockerfile
        cat > $BUILD_DIR/Dockerfile.$service << EOF
FROM python:3.9-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \\
    gcc g++ && \\
    rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY qlib_trading_system/ ./qlib_trading_system/
COPY config/ ./config/
COPY main.py .

# 创建非root用户
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \\
    CMD python -c "import requests; requests.get('http://localhost:8000/health')"

# 启动命令
CMD ["python", "main.py", "--service", "$service"]
EOF
        
        # 构建镜像
        if docker build -t $IMAGE_TAG -f $BUILD_DIR/Dockerfile.$service .; then
            log_success "镜像构建成功: $IMAGE_TAG"
        else
            log_error "镜像构建失败: $service"
            exit 1
        fi
        
        # 推送镜像
        if docker push $IMAGE_TAG; then
            log_success "镜像推送成功: $IMAGE_TAG"
        else
            log_error "镜像推送失败: $service"
            exit 1
        fi
    done
    
    # 清理临时文件
    rm -rf $BUILD_DIR
    
    log_success "所有镜像构建和推送完成"
}

# 3. 更新Kubernetes配置
update_k8s_config() {
    log_info "更新Kubernetes配置..."
    
    K8S_DIR="deployment/k8s"
    mkdir -p $K8S_DIR
    
    for service in $SERVICES; do
        log_info "生成 $service 的Kubernetes配置"
        
        # 根据环境设置资源限制
        case $ENVIRONMENT in
            "development")
                REPLICAS=1
                CPU_REQUEST="100m"
                CPU_LIMIT="200m"
                MEMORY_REQUEST="128Mi"
                MEMORY_LIMIT="256Mi"
                ;;
            "staging")
                REPLICAS=2
                CPU_REQUEST="200m"
                CPU_LIMIT="500m"
                MEMORY_REQUEST="256Mi"
                MEMORY_LIMIT="512Mi"
                ;;
            "production")
                REPLICAS=3
                CPU_REQUEST="500m"
                CPU_LIMIT="1000m"
                MEMORY_REQUEST="512Mi"
                MEMORY_LIMIT="1Gi"
                ;;
        esac
        
        # 生成Deployment配置
        cat > $K8S_DIR/$service-deployment.yaml << EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  name: $service-deployment
  namespace: $NAMESPACE
  labels:
    app: $service
    component: $APP_NAME
    environment: $ENVIRONMENT
spec:
  replicas: $REPLICAS
  selector:
    matchLabels:
      app: $service
  template:
    metadata:
      labels:
        app: $service
        component: $APP_NAME
        environment: $ENVIRONMENT
    spec:
      containers:
      - name: $service
        image: $DOCKER_REGISTRY/$APP_NAME-$service:$VERSION
        imagePullPolicy: Always
        ports:
        - containerPort: 8000
          name: http
        env:
        - name: SERVICE_NAME
          value: $service
        - name: ENVIRONMENT
          value: $ENVIRONMENT
        - name: LOG_LEVEL
          value: INFO
        resources:
          requests:
            cpu: $CPU_REQUEST
            memory: $MEMORY_REQUEST
          limits:
            cpu: $CPU_LIMIT
            memory: $MEMORY_LIMIT
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
      restartPolicy: Always
EOF
        
        # 生成Service配置
        cat > $K8S_DIR/$service-service.yaml << EOF
apiVersion: v1
kind: Service
metadata:
  name: $service-service
  namespace: $NAMESPACE
  labels:
    app: $service
    component: $APP_NAME
spec:
  selector:
    app: $service
  ports:
  - port: 8000
    targetPort: 8000
    protocol: TCP
    name: http
  type: ClusterIP
EOF
        
        # 应用配置
        if kubectl apply -f $K8S_DIR/$service-deployment.yaml; then
            log_success "$service Deployment配置应用成功"
        else
            log_error "$service Deployment配置应用失败"
            exit 1
        fi
        
        if kubectl apply -f $K8S_DIR/$service-service.yaml; then
            log_success "$service Service配置应用成功"
        else
            log_error "$service Service配置应用失败"
            exit 1
        fi
    done
    
    log_success "Kubernetes配置更新完成"
}

# 4. 等待部署完成
wait_for_deployment() {
    log_info "等待部署完成..."
    
    for service in $SERVICES; do
        log_info "等待 $service 部署完成..."
        
        if kubectl rollout status deployment/$service-deployment -n $NAMESPACE --timeout=300s; then
            log_success "$service 部署完成"
        else
            log_error "$service 部署超时或失败"
            exit 1
        fi
    done
    
    log_success "所有服务部署完成"
}

# 5. 健康检查
health_check() {
    log_info "执行健康检查..."
    
    for service in $SERVICES; do
        log_info "检查 $service 健康状态..."
        
        # 获取服务端点
        SERVICE_IP=$(kubectl get service $service-service -n $NAMESPACE -o jsonpath='{.spec.clusterIP}')
        
        # 执行健康检查
        max_retries=30
        retry_count=0
        
        while [ $retry_count -lt $max_retries ]; do
            if kubectl run health-check-$service --rm -i --restart=Never --image=curlimages/curl -- curl -f http://$SERVICE_IP:8000/health; then
                log_success "$service 健康检查通过"
                break
            else
                retry_count=$((retry_count + 1))
                log_info "$service 健康检查失败，重试 $retry_count/$max_retries"
                sleep 10
            fi
        done
        
        if [ $retry_count -eq $max_retries ]; then
            log_error "$service 健康检查失败"
            exit 1
        fi
    done
    
    log_success "所有服务健康检查通过"
}

# 6. 部署后验证
post_deployment_verification() {
    log_info "执行部署后验证..."
    
    # 检查Pod状态
    log_info "检查Pod状态..."
    kubectl get pods -n $NAMESPACE -l component=$APP_NAME
    
    # 检查Service状态
    log_info "检查Service状态..."
    kubectl get services -n $NAMESPACE -l component=$APP_NAME
    
    # 检查Deployment状态
    log_info "检查Deployment状态..."
    kubectl get deployments -n $NAMESPACE -l component=$APP_NAME
    
    log_success "部署后验证完成"
}

# 7. 清理函数
cleanup() {
    log_info "清理临时资源..."
    
    # 清理临时文件
    rm -rf deployment/build
    
    # 清理未使用的Docker镜像
    docker image prune -f
    
    log_success "清理完成"
}

# 主执行流程
main() {
    log_info "========================================="
    log_info "开始部署 $APP_NAME"
    log_info "环境: $ENVIRONMENT"
    log_info "版本: $VERSION"
    log_info "服务: ${SERVICES:-所有服务}"
    log_info "========================================="
    
    # 设置错误处理
    trap cleanup EXIT
    
    # 执行部署步骤
    validate_environment
    build_and_push_images
    update_k8s_config
    wait_for_deployment
    health_check
    post_deployment_verification
    
    log_success "========================================="
    log_success "部署完成！"
    log_success "环境: $ENVIRONMENT"
    log_success "版本: $VERSION"
    log_success "时间: $(date)"
    log_success "========================================="
}

# 执行主函数
main
