# 自动化部署脚本 (PowerShell版本)
# 用法: .\deploy.ps1 [environment] [version] [services...]

param(
    [string]$Environment = "staging",
    [string]$Version = "latest",
    [string[]]$Services = @(),
    [switch]$Help
)

# 配置变量
$APP_NAME = "qlib-trading-system"
$DOCKER_REGISTRY = "localhost:5000"
$NAMESPACE = "qlib-trading"

# 显示帮助信息
if ($Help) {
    Write-Host "用法: .\deploy.ps1 [参数]"
    Write-Host ""
    Write-Host "参数:"
    Write-Host "  -Environment   部署环境 (development|staging|production) [默认: staging]"
    Write-Host "  -Version       版本标签 [默认: latest]"
    Write-Host "  -Services      要部署的服务列表 [默认: 所有服务]"
    Write-Host "  -Help          显示此帮助信息"
    Write-Host ""
    Write-Host "示例:"
    Write-Host "  .\deploy.ps1 -Environment production -Version v1.0.0"
    Write-Host "  .\deploy.ps1 -Environment staging -Version latest -Services @('api', 'data-processor')"
    Write-Host "  .\deploy.ps1 -Environment development"
    exit 0
}

# 日志函数
function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# 验证环境参数
if ($Environment -notin @("development", "staging", "production")) {
    Write-Error "无效的环境: $Environment"
    Write-Info "支持的环境: development, staging, production"
    exit 1
}

Write-Info "开始部署 $APP_NAME 到 $Environment 环境，版本: $Version"

# 1. 验证部署环境
function Test-Environment {
    Write-Info "验证部署环境..."
    
    # 检查Docker
    try {
        $dockerVersion = docker --version
        Write-Info "Docker版本: $dockerVersion"
    }
    catch {
        Write-Error "Docker未安装或不在PATH中"
        exit 1
    }
    
    # 检查Docker daemon
    try {
        docker info | Out-Null
        Write-Success "Docker daemon运行正常"
    }
    catch {
        Write-Error "Docker daemon未运行"
        exit 1
    }
    
    # 检查kubectl
    try {
        $kubectlVersion = kubectl version --client --short
        Write-Info "kubectl版本: $kubectlVersion"
    }
    catch {
        Write-Error "kubectl未安装或不在PATH中"
        exit 1
    }
    
    # 检查kubectl连接
    try {
        kubectl cluster-info | Out-Null
        Write-Success "Kubernetes集群连接正常"
    }
    catch {
        Write-Error "无法连接到Kubernetes集群"
        exit 1
    }
    
    # 检查命名空间
    try {
        kubectl get namespace $NAMESPACE | Out-Null
        Write-Info "命名空间 $NAMESPACE 已存在"
    }
    catch {
        Write-Info "创建命名空间: $NAMESPACE"
        kubectl create namespace $NAMESPACE
    }
    
    Write-Success "环境验证完成"
}

# 2. 构建和推送镜像
function Build-Images {
    Write-Info "构建和推送Docker镜像..."
    
    # 如果没有指定服务，使用默认服务列表
    if ($Services.Count -eq 0) {
        $Services = @("api", "data-processor", "model-server", "risk-monitor")
    }
    
    # 创建临时构建目录
    $BUILD_DIR = "deployment\build"
    New-Item -ItemType Directory -Path $BUILD_DIR -Force | Out-Null
    
    foreach ($service in $Services) {
        Write-Info "构建服务: $service"
        
        $IMAGE_TAG = "$DOCKER_REGISTRY/$APP_NAME-$service`:$Version"
        
        # 创建服务专用的Dockerfile
        $dockerfileContent = @"
FROM python:3.9-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc g++ && \
    rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY qlib_trading_system/ ./qlib_trading_system/
COPY config/ ./config/
COPY main.py .

# 创建非root用户
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD python -c "import requests; requests.get('http://localhost:8000/health')"

# 启动命令
CMD ["python", "main.py", "--service", "$service"]
"@
        
        $dockerfilePath = "$BUILD_DIR\Dockerfile.$service"
        $dockerfileContent | Out-File -FilePath $dockerfilePath -Encoding UTF8
        
        # 构建镜像
        try {
            docker build -t $IMAGE_TAG -f $dockerfilePath .
            Write-Success "镜像构建成功: $IMAGE_TAG"
        }
        catch {
            Write-Error "镜像构建失败: $service"
            exit 1
        }
        
        # 推送镜像
        try {
            docker push $IMAGE_TAG
            Write-Success "镜像推送成功: $IMAGE_TAG"
        }
        catch {
            Write-Error "镜像推送失败: $service"
            exit 1
        }
    }
    
    # 清理临时文件
    Remove-Item -Path $BUILD_DIR -Recurse -Force
    
    Write-Success "所有镜像构建和推送完成"
}

# 3. 更新Kubernetes配置
function Update-K8sConfig {
    Write-Info "更新Kubernetes配置..."
    
    $K8S_DIR = "deployment\k8s"
    New-Item -ItemType Directory -Path $K8S_DIR -Force | Out-Null
    
    foreach ($service in $Services) {
        Write-Info "生成 $service 的Kubernetes配置"
        
        # 根据环境设置资源限制
        switch ($Environment) {
            "development" {
                $REPLICAS = 1
                $CPU_REQUEST = "100m"
                $CPU_LIMIT = "200m"
                $MEMORY_REQUEST = "128Mi"
                $MEMORY_LIMIT = "256Mi"
            }
            "staging" {
                $REPLICAS = 2
                $CPU_REQUEST = "200m"
                $CPU_LIMIT = "500m"
                $MEMORY_REQUEST = "256Mi"
                $MEMORY_LIMIT = "512Mi"
            }
            "production" {
                $REPLICAS = 3
                $CPU_REQUEST = "500m"
                $CPU_LIMIT = "1000m"
                $MEMORY_REQUEST = "512Mi"
                $MEMORY_LIMIT = "1Gi"
            }
        }
        
        # 生成Deployment配置
        $deploymentConfig = @"
apiVersion: apps/v1
kind: Deployment
metadata:
  name: $service-deployment
  namespace: $NAMESPACE
  labels:
    app: $service
    component: $APP_NAME
    environment: $Environment
spec:
  replicas: $REPLICAS
  selector:
    matchLabels:
      app: $service
  template:
    metadata:
      labels:
        app: $service
        component: $APP_NAME
        environment: $Environment
    spec:
      containers:
      - name: $service
        image: $DOCKER_REGISTRY/$APP_NAME-$service`:$Version
        imagePullPolicy: Always
        ports:
        - containerPort: 8000
          name: http
        env:
        - name: SERVICE_NAME
          value: $service
        - name: ENVIRONMENT
          value: $Environment
        - name: LOG_LEVEL
          value: INFO
        resources:
          requests:
            cpu: $CPU_REQUEST
            memory: $MEMORY_REQUEST
          limits:
            cpu: $CPU_LIMIT
            memory: $MEMORY_LIMIT
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
      restartPolicy: Always
"@
        
        # 生成Service配置
        $serviceConfig = @"
apiVersion: v1
kind: Service
metadata:
  name: $service-service
  namespace: $NAMESPACE
  labels:
    app: $service
    component: $APP_NAME
spec:
  selector:
    app: $service
  ports:
  - port: 8000
    targetPort: 8000
    protocol: TCP
    name: http
  type: ClusterIP
"@
        
        # 保存配置文件
        $deploymentPath = "$K8S_DIR\$service-deployment.yaml"
        $servicePath = "$K8S_DIR\$service-service.yaml"
        
        $deploymentConfig | Out-File -FilePath $deploymentPath -Encoding UTF8
        $serviceConfig | Out-File -FilePath $servicePath -Encoding UTF8
        
        # 应用配置
        try {
            kubectl apply -f $deploymentPath
            Write-Success "$service Deployment配置应用成功"
        }
        catch {
            Write-Error "$service Deployment配置应用失败"
            exit 1
        }
        
        try {
            kubectl apply -f $servicePath
            Write-Success "$service Service配置应用成功"
        }
        catch {
            Write-Error "$service Service配置应用失败"
            exit 1
        }
    }
    
    Write-Success "Kubernetes配置更新完成"
}

# 4. 等待部署完成
function Wait-ForDeployment {
    Write-Info "等待部署完成..."
    
    foreach ($service in $Services) {
        Write-Info "等待 $service 部署完成..."
        
        try {
            kubectl rollout status deployment/$service-deployment -n $NAMESPACE --timeout=300s
            Write-Success "$service 部署完成"
        }
        catch {
            Write-Error "$service 部署超时或失败"
            exit 1
        }
    }
    
    Write-Success "所有服务部署完成"
}

# 5. 健康检查
function Test-Health {
    Write-Info "执行健康检查..."
    
    foreach ($service in $Services) {
        Write-Info "检查 $service 健康状态..."
        
        # 获取Pod信息进行健康检查
        $maxRetries = 30
        $retryCount = 0
        $healthy = $false
        
        while ($retryCount -lt $maxRetries -and -not $healthy) {
            try {
                $pods = kubectl get pods -n $NAMESPACE -l app=$service -o jsonpath='{.items[*].status.phase}'
                if ($pods -match "Running") {
                    Write-Success "$service 健康检查通过"
                    $healthy = $true
                }
                else {
                    $retryCount++
                    Write-Info "$service 健康检查失败，重试 $retryCount/$maxRetries"
                    Start-Sleep -Seconds 10
                }
            }
            catch {
                $retryCount++
                Write-Info "$service 健康检查失败，重试 $retryCount/$maxRetries"
                Start-Sleep -Seconds 10
            }
        }
        
        if (-not $healthy) {
            Write-Error "$service 健康检查失败"
            exit 1
        }
    }
    
    Write-Success "所有服务健康检查通过"
}

# 6. 部署后验证
function Test-PostDeployment {
    Write-Info "执行部署后验证..."
    
    # 检查Pod状态
    Write-Info "检查Pod状态..."
    kubectl get pods -n $NAMESPACE -l component=$APP_NAME
    
    # 检查Service状态
    Write-Info "检查Service状态..."
    kubectl get services -n $NAMESPACE -l component=$APP_NAME
    
    # 检查Deployment状态
    Write-Info "检查Deployment状态..."
    kubectl get deployments -n $NAMESPACE -l component=$APP_NAME
    
    Write-Success "部署后验证完成"
}

# 7. 清理函数
function Clear-Resources {
    Write-Info "清理临时资源..."
    
    # 清理临时文件
    if (Test-Path "deployment\build") {
        Remove-Item -Path "deployment\build" -Recurse -Force
    }
    
    # 清理未使用的Docker镜像
    try {
        docker image prune -f
    }
    catch {
        Write-Warning "Docker镜像清理失败"
    }
    
    Write-Success "清理完成"
}

# 主执行流程
function Main {
    Write-Info "========================================="
    Write-Info "开始部署 $APP_NAME"
    Write-Info "环境: $Environment"
    Write-Info "版本: $Version"
    Write-Info "服务: $(if ($Services.Count -eq 0) { '所有服务' } else { $Services -join ', ' })"
    Write-Info "========================================="
    
    try {
        # 执行部署步骤
        Test-Environment
        Build-Images
        Update-K8sConfig
        Wait-ForDeployment
        Test-Health
        Test-PostDeployment
        
        Write-Success "========================================="
        Write-Success "部署完成！"
        Write-Success "环境: $Environment"
        Write-Success "版本: $Version"
        Write-Success "时间: $(Get-Date)"
        Write-Success "========================================="
    }
    catch {
        Write-Error "部署过程中发生错误: $_"
        exit 1
    }
    finally {
        Clear-Resources
    }
}

# 执行主函数
Main
