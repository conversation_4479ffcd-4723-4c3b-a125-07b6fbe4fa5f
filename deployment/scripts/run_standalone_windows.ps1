# Windows 11 单机运行脚本
# 用于在Windows 11环境下快速启动qlib交易系统
# 用法: .\run_standalone_windows.ps1 [参数]

param(
    [string]$Mode = "development",
    [string]$Port = "8000",
    [string]$LogLevel = "INFO",
    [switch]$InstallDeps,
    [switch]$SetupDB,
    [switch]$Help,
    [switch]$Stop,
    [switch]$Status,
    [switch]$Clean
)

# 配置变量
$APP_NAME = "qlib-trading-system"
$PROJECT_DIR = Split-Path -Parent (Split-Path -Parent $PSScriptRoot)
$VENV_DIR = "$PROJECT_DIR\venv"
$LOG_DIR = "$PROJECT_DIR\logs"
$DATA_DIR = "$PROJECT_DIR\data"
$CONFIG_DIR = "$PROJECT_DIR\config"
$PID_FILE = "$PROJECT_DIR\qlib_trading.pid"

# 显示帮助信息
if ($Help) {
    Write-Host "qlib交易系统 Windows 11 单机运行脚本" -ForegroundColor Green
    Write-Host ""
    Write-Host "用法: .\run_standalone_windows.ps1 [参数]"
    Write-Host ""
    Write-Host "参数:"
    Write-Host "  -Mode         运行模式 (development|production) [默认: development]"
    Write-Host "  -Port         服务端口 [默认: 8000]"
    Write-Host "  -LogLevel     日志级别 (DEBUG|INFO|WARNING|ERROR) [默认: INFO]"
    Write-Host "  -InstallDeps  安装Python依赖"
    Write-Host "  -SetupDB      初始化数据库"
    Write-Host "  -Stop         停止服务"
    Write-Host "  -Status       查看服务状态"
    Write-Host "  -Clean        清理临时文件"
    Write-Host "  -Help         显示此帮助信息"
    Write-Host ""
    Write-Host "示例:"
    Write-Host "  .\run_standalone_windows.ps1                    # 启动开发模式"
    Write-Host "  .\run_standalone_windows.ps1 -Mode production   # 启动生产模式"
    Write-Host "  .\run_standalone_windows.ps1 -InstallDeps       # 安装依赖"
    Write-Host "  .\run_standalone_windows.ps1 -SetupDB           # 初始化数据库"
    Write-Host "  .\run_standalone_windows.ps1 -Stop              # 停止服务"
    Write-Host "  .\run_standalone_windows.ps1 -Status            # 查看状态"
    Write-Host ""
    Write-Host "环境要求:"
    Write-Host "  - Windows 11"
    Write-Host "  - Python 3.9+"
    Write-Host "  - Git"
    exit 0
}

# 日志函数
function Write-Info {
    param([string]$Message)
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] [INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] [SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] [WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] [ERROR] $Message" -ForegroundColor Red
}

# 检查系统环境
function Test-SystemRequirements {
    Write-Info "检查系统环境..."
    
    # 检查Windows版本
    $osVersion = [System.Environment]::OSVersion.Version
    if ($osVersion.Major -lt 10) {
        Write-Error "需要Windows 10或更高版本"
        exit 1
    }
    Write-Success "Windows版本检查通过"
    
    # 检查Python
    try {
        $pythonVersion = python --version 2>&1
        if ($pythonVersion -match "Python 3\.([9-9]|[1-9][0-9])") {
            Write-Success "Python版本检查通过: $pythonVersion"
        } else {
            Write-Error "需要Python 3.9或更高版本，当前版本: $pythonVersion"
            exit 1
        }
    }
    catch {
        Write-Error "Python未安装或不在PATH中"
        Write-Info "请从 https://python.org 下载并安装Python 3.9+"
        exit 1
    }
    
    # 检查Git
    try {
        $gitVersion = git --version 2>&1
        Write-Success "Git版本检查通过: $gitVersion"
    }
    catch {
        Write-Warning "Git未安装，某些功能可能不可用"
    }
    
    Write-Success "系统环境检查完成"
}

# 创建目录结构
function New-DirectoryStructure {
    Write-Info "创建目录结构..."
    
    $directories = @($LOG_DIR, $DATA_DIR, "$DATA_DIR\models", "$DATA_DIR\processed", "$DATA_DIR\raw", "temp", "reports")
    
    foreach ($dir in $directories) {
        if (!(Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
            Write-Info "创建目录: $dir"
        }
    }
    
    Write-Success "目录结构创建完成"
}

# 创建虚拟环境
function New-VirtualEnvironment {
    Write-Info "创建Python虚拟环境..."
    
    if (!(Test-Path $VENV_DIR)) {
        python -m venv $VENV_DIR
        Write-Success "虚拟环境创建完成: $VENV_DIR"
    } else {
        Write-Info "虚拟环境已存在: $VENV_DIR"
    }
}

# 激活虚拟环境
function Enable-VirtualEnvironment {
    $activateScript = "$VENV_DIR\Scripts\Activate.ps1"
    if (Test-Path $activateScript) {
        & $activateScript
        Write-Success "虚拟环境已激活"
    } else {
        Write-Error "虚拟环境激活脚本不存在: $activateScript"
        exit 1
    }
}

# 安装依赖
function Install-Dependencies {
    Write-Info "安装Python依赖..."
    
    if (Test-Path "$PROJECT_DIR\requirements.txt") {
        pip install --upgrade pip
        pip install -r "$PROJECT_DIR\requirements.txt"
        Write-Success "依赖安装完成"
    } else {
        Write-Error "requirements.txt文件不存在"
        exit 1
    }
}

# 初始化数据库
function Initialize-Database {
    Write-Info "初始化数据库..."
    
    try {
        # 创建SQLite数据库文件
        $dbFile = "$DATA_DIR\qlib_trading.db"
        if (!(Test-Path $dbFile)) {
            # 这里可以添加数据库初始化脚本
            New-Item -ItemType File -Path $dbFile -Force | Out-Null
            Write-Success "数据库文件创建完成: $dbFile"
        }
        
        # 运行数据库迁移脚本（如果存在）
        if (Test-Path "$PROJECT_DIR\scripts\init_db.py") {
            python "$PROJECT_DIR\scripts\init_db.py"
            Write-Success "数据库初始化完成"
        } else {
            Write-Info "数据库初始化脚本不存在，跳过"
        }
    }
    catch {
        Write-Error "数据库初始化失败: $_"
        exit 1
    }
}

# 创建配置文件
function New-ConfigFiles {
    Write-Info "创建配置文件..."
    
    # 创建.env文件
    $envFile = "$PROJECT_DIR\.env"
    if (!(Test-Path $envFile)) {
        $envContent = @"
# qlib交易系统配置文件
ENVIRONMENT=$Mode
DEBUG=$($Mode -eq "development")
LOG_LEVEL=$LogLevel
PORT=$Port

# 数据库配置
DATABASE_URL=sqlite:///$($DATA_DIR.Replace('\', '/'))/qlib_trading.db

# Redis配置（如果使用）
REDIS_URL=redis://localhost:6379/0

# 日志配置
LOG_DIR=$($LOG_DIR.Replace('\', '/'))

# 数据目录
DATA_DIR=$($DATA_DIR.Replace('\', '/'))
"@
        $envContent | Out-File -FilePath $envFile -Encoding UTF8
        Write-Success "配置文件创建完成: $envFile"
    } else {
        Write-Info "配置文件已存在: $envFile"
    }
}

# 启动服务
function Start-Service {
    Write-Info "启动qlib交易系统..."
    
    # 检查是否已经运行
    if (Test-Path $PID_FILE) {
        $pid = Get-Content $PID_FILE
        if (Get-Process -Id $pid -ErrorAction SilentlyContinue) {
            Write-Warning "服务已在运行，PID: $pid"
            return
        } else {
            Remove-Item $PID_FILE -Force
        }
    }
    
    # 设置环境变量
    $env:PYTHONPATH = $PROJECT_DIR
    $env:ENVIRONMENT = $Mode
    $env:LOG_LEVEL = $LogLevel
    $env:PORT = $Port
    
    # 启动主程序
    try {
        $process = Start-Process -FilePath "python" -ArgumentList "$PROJECT_DIR\main.py" -PassThru -WindowStyle Hidden
        $process.Id | Out-File -FilePath $PID_FILE -Encoding UTF8
        
        # 等待服务启动
        Start-Sleep -Seconds 3
        
        # 检查服务是否正常启动
        if (Get-Process -Id $process.Id -ErrorAction SilentlyContinue) {
            Write-Success "服务启动成功！"
            Write-Info "PID: $($process.Id)"
            Write-Info "端口: $Port"
            Write-Info "模式: $Mode"
            Write-Info "日志级别: $LogLevel"
            Write-Info ""
            Write-Info "访问地址: http://localhost:$Port"
            Write-Info "API文档: http://localhost:$Port/docs"
            Write-Info ""
            Write-Info "使用 '.\run_standalone_windows.ps1 -Status' 查看状态"
            Write-Info "使用 '.\run_standalone_windows.ps1 -Stop' 停止服务"
        } else {
            Write-Error "服务启动失败"
            Remove-Item $PID_FILE -Force -ErrorAction SilentlyContinue
            exit 1
        }
    }
    catch {
        Write-Error "服务启动失败: $_"
        exit 1
    }
}

# 停止服务
function Stop-Service {
    Write-Info "停止qlib交易系统..."
    
    if (Test-Path $PID_FILE) {
        $pid = Get-Content $PID_FILE
        try {
            $process = Get-Process -Id $pid -ErrorAction SilentlyContinue
            if ($process) {
                Stop-Process -Id $pid -Force
                Write-Success "服务已停止，PID: $pid"
            } else {
                Write-Warning "进程不存在，PID: $pid"
            }
            Remove-Item $PID_FILE -Force
        }
        catch {
            Write-Error "停止服务失败: $_"
        }
    } else {
        Write-Warning "PID文件不存在，服务可能未运行"
    }
}

# 查看服务状态
function Get-ServiceStatus {
    Write-Info "查看服务状态..."
    
    if (Test-Path $PID_FILE) {
        $pid = Get-Content $PID_FILE
        $process = Get-Process -Id $pid -ErrorAction SilentlyContinue
        
        if ($process) {
            Write-Success "服务正在运行"
            Write-Info "PID: $pid"
            Write-Info "进程名: $($process.ProcessName)"
            Write-Info "启动时间: $($process.StartTime)"
            Write-Info "CPU使用: $($process.CPU)%"
            Write-Info "内存使用: $([math]::Round($process.WorkingSet64/1MB, 2)) MB"
            
            # 检查端口是否监听
            try {
                $response = Invoke-WebRequest -Uri "http://localhost:$Port/health" -TimeoutSec 5 -ErrorAction SilentlyContinue
                if ($response.StatusCode -eq 200) {
                    Write-Success "服务健康检查通过"
                    Write-Info "访问地址: http://localhost:$Port"
                } else {
                    Write-Warning "服务健康检查失败"
                }
            }
            catch {
                Write-Warning "无法连接到服务端口: $Port"
            }
        } else {
            Write-Warning "PID文件存在但进程不存在，清理PID文件"
            Remove-Item $PID_FILE -Force
        }
    } else {
        Write-Info "服务未运行"
    }
}

# 清理临时文件
function Clear-TempFiles {
    Write-Info "清理临时文件..."
    
    $tempDirs = @("temp", "__pycache__", "*.pyc", "*.pyo", "*.log")
    
    foreach ($pattern in $tempDirs) {
        $items = Get-ChildItem -Path $PROJECT_DIR -Recurse -Name $pattern -ErrorAction SilentlyContinue
        foreach ($item in $items) {
            Remove-Item "$PROJECT_DIR\$item" -Recurse -Force -ErrorAction SilentlyContinue
        }
    }
    
    Write-Success "临时文件清理完成"
}

# 主执行流程
function Main {
    Write-Info "========================================="
    Write-Info "qlib交易系统 Windows 11 单机启动器"
    Write-Info "========================================="
    
    # 切换到项目目录
    Set-Location $PROJECT_DIR
    
    try {
        if ($Stop) {
            Stop-Service
            return
        }
        
        if ($Status) {
            Get-ServiceStatus
            return
        }
        
        if ($Clean) {
            Clear-TempFiles
            return
        }
        
        # 检查系统环境
        Test-SystemRequirements
        
        # 创建目录结构
        New-DirectoryStructure
        
        # 创建虚拟环境
        New-VirtualEnvironment
        
        # 激活虚拟环境
        Enable-VirtualEnvironment
        
        # 安装依赖
        if ($InstallDeps) {
            Install-Dependencies
        }
        
        # 初始化数据库
        if ($SetupDB) {
            Initialize-Database
        }
        
        # 创建配置文件
        New-ConfigFiles
        
        # 启动服务
        if (!$InstallDeps -and !$SetupDB) {
            Start-Service
        }
        
    }
    catch {
        Write-Error "执行失败: $_"
        exit 1
    }
}

# 执行主函数
Main
