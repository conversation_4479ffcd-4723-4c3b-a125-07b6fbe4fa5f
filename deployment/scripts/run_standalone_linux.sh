#!/bin/bash
# Linux 单机运行脚本
# 用于在Linux环境下快速启动qlib交易系统
# 用法: ./run_standalone_linux.sh [参数]

set -e  # 遇到错误立即退出

# 默认参数
MODE="development"
PORT="8000"
LOG_LEVEL="INFO"
INSTALL_DEPS=false
SETUP_DB=false
STOP_SERVICE=false
CHECK_STATUS=false
CLEAN_FILES=false

# 配置变量
APP_NAME="qlib-trading-system"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$(dirname "$SCRIPT_DIR")")"
VENV_DIR="$PROJECT_DIR/venv"
LOG_DIR="$PROJECT_DIR/logs"
DATA_DIR="$PROJECT_DIR/data"
CONFIG_DIR="$PROJECT_DIR/config"
PID_FILE="$PROJECT_DIR/qlib_trading.pid"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')] [INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] [SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] [WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo -e "${GREEN}qlib交易系统 Linux 单机运行脚本${NC}"
    echo ""
    echo "用法: $0 [参数]"
    echo ""
    echo "参数:"
    echo "  -m, --mode MODE       运行模式 (development|production) [默认: development]"
    echo "  -p, --port PORT       服务端口 [默认: 8000]"
    echo "  -l, --log-level LEVEL 日志级别 (DEBUG|INFO|WARNING|ERROR) [默认: INFO]"
    echo "  --install-deps        安装Python依赖"
    echo "  --setup-db            初始化数据库"
    echo "  --stop                停止服务"
    echo "  --status              查看服务状态"
    echo "  --clean               清理临时文件"
    echo "  -h, --help            显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                              # 启动开发模式"
    echo "  $0 -m production                # 启动生产模式"
    echo "  $0 --install-deps               # 安装依赖"
    echo "  $0 --setup-db                   # 初始化数据库"
    echo "  $0 --stop                       # 停止服务"
    echo "  $0 --status                     # 查看状态"
    echo ""
    echo "环境要求:"
    echo "  - Linux (Ubuntu 18.04+, CentOS 7+, 或其他主流发行版)"
    echo "  - Python 3.9+"
    echo "  - Git"
    echo "  - curl (用于健康检查)"
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -m|--mode)
                MODE="$2"
                shift 2
                ;;
            -p|--port)
                PORT="$2"
                shift 2
                ;;
            -l|--log-level)
                LOG_LEVEL="$2"
                shift 2
                ;;
            --install-deps)
                INSTALL_DEPS=true
                shift
                ;;
            --setup-db)
                SETUP_DB=true
                shift
                ;;
            --stop)
                STOP_SERVICE=true
                shift
                ;;
            --status)
                CHECK_STATUS=true
                shift
                ;;
            --clean)
                CLEAN_FILES=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 验证模式参数
    if [[ ! "$MODE" =~ ^(development|production)$ ]]; then
        log_error "无效的运行模式: $MODE"
        log_info "支持的模式: development, production"
        exit 1
    fi
}

# 检查系统环境
check_system_requirements() {
    log_info "检查系统环境..."
    
    # 检查Linux发行版
    if [[ -f /etc/os-release ]]; then
        . /etc/os-release
        log_success "Linux发行版: $NAME $VERSION"
    else
        log_warning "无法确定Linux发行版"
    fi
    
    # 检查Python
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version 2>&1)
        if [[ $PYTHON_VERSION =~ Python\ 3\.([9-9]|[1-9][0-9]) ]]; then
            log_success "Python版本检查通过: $PYTHON_VERSION"
            PYTHON_CMD="python3"
        else
            log_error "需要Python 3.9或更高版本，当前版本: $PYTHON_VERSION"
            exit 1
        fi
    elif command -v python &> /dev/null; then
        PYTHON_VERSION=$(python --version 2>&1)
        if [[ $PYTHON_VERSION =~ Python\ 3\.([9-9]|[1-9][0-9]) ]]; then
            log_success "Python版本检查通过: $PYTHON_VERSION"
            PYTHON_CMD="python"
        else
            log_error "需要Python 3.9或更高版本，当前版本: $PYTHON_VERSION"
            exit 1
        fi
    else
        log_error "Python未安装或不在PATH中"
        log_info "请安装Python 3.9+:"
        log_info "  Ubuntu/Debian: sudo apt update && sudo apt install python3 python3-pip python3-venv"
        log_info "  CentOS/RHEL: sudo yum install python3 python3-pip"
        log_info "  Fedora: sudo dnf install python3 python3-pip"
        exit 1
    fi
    
    # 检查pip
    if ! command -v pip3 &> /dev/null && ! command -v pip &> /dev/null; then
        log_error "pip未安装"
        log_info "请安装pip:"
        log_info "  Ubuntu/Debian: sudo apt install python3-pip"
        log_info "  CentOS/RHEL: sudo yum install python3-pip"
        exit 1
    fi
    
    # 检查Git
    if command -v git &> /dev/null; then
        GIT_VERSION=$(git --version)
        log_success "Git版本检查通过: $GIT_VERSION"
    else
        log_warning "Git未安装，某些功能可能不可用"
    fi
    
    # 检查curl
    if command -v curl &> /dev/null; then
        log_success "curl检查通过"
    else
        log_warning "curl未安装，健康检查功能可能不可用"
    fi
    
    log_success "系统环境检查完成"
}

# 创建目录结构
create_directory_structure() {
    log_info "创建目录结构..."
    
    local directories=("$LOG_DIR" "$DATA_DIR" "$DATA_DIR/models" "$DATA_DIR/processed" "$DATA_DIR/raw" "temp" "reports")
    
    for dir in "${directories[@]}"; do
        if [[ ! -d "$dir" ]]; then
            mkdir -p "$dir"
            log_info "创建目录: $dir"
        fi
    done
    
    log_success "目录结构创建完成"
}

# 创建虚拟环境
create_virtual_environment() {
    log_info "创建Python虚拟环境..."
    
    if [[ ! -d "$VENV_DIR" ]]; then
        $PYTHON_CMD -m venv "$VENV_DIR"
        log_success "虚拟环境创建完成: $VENV_DIR"
    else
        log_info "虚拟环境已存在: $VENV_DIR"
    fi
}

# 激活虚拟环境
activate_virtual_environment() {
    if [[ -f "$VENV_DIR/bin/activate" ]]; then
        source "$VENV_DIR/bin/activate"
        log_success "虚拟环境已激活"
    else
        log_error "虚拟环境激活脚本不存在: $VENV_DIR/bin/activate"
        exit 1
    fi
}

# 安装依赖
install_dependencies() {
    log_info "安装Python依赖..."
    
    if [[ -f "$PROJECT_DIR/requirements.txt" ]]; then
        pip install --upgrade pip
        pip install -r "$PROJECT_DIR/requirements.txt"
        log_success "依赖安装完成"
    else
        log_error "requirements.txt文件不存在"
        exit 1
    fi
}

# 初始化数据库
initialize_database() {
    log_info "初始化数据库..."
    
    # 创建SQLite数据库文件
    local db_file="$DATA_DIR/qlib_trading.db"
    if [[ ! -f "$db_file" ]]; then
        touch "$db_file"
        log_success "数据库文件创建完成: $db_file"
    fi
    
    # 运行数据库迁移脚本（如果存在）
    if [[ -f "$PROJECT_DIR/scripts/init_db.py" ]]; then
        $PYTHON_CMD "$PROJECT_DIR/scripts/init_db.py"
        log_success "数据库初始化完成"
    else
        log_info "数据库初始化脚本不存在，跳过"
    fi
}

# 创建配置文件
create_config_files() {
    log_info "创建配置文件..."
    
    # 创建.env文件
    local env_file="$PROJECT_DIR/.env"
    if [[ ! -f "$env_file" ]]; then
        cat > "$env_file" << EOF
# qlib交易系统配置文件
ENVIRONMENT=$MODE
DEBUG=$([[ "$MODE" == "development" ]] && echo "True" || echo "False")
LOG_LEVEL=$LOG_LEVEL
PORT=$PORT

# 数据库配置
DATABASE_URL=sqlite:///$DATA_DIR/qlib_trading.db

# Redis配置（如果使用）
REDIS_URL=redis://localhost:6379/0

# 日志配置
LOG_DIR=$LOG_DIR

# 数据目录
DATA_DIR=$DATA_DIR
EOF
        log_success "配置文件创建完成: $env_file"
    else
        log_info "配置文件已存在: $env_file"
    fi
}

# 启动服务
start_service() {
    log_info "启动qlib交易系统..."
    
    # 检查是否已经运行
    if [[ -f "$PID_FILE" ]]; then
        local pid=$(cat "$PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            log_warning "服务已在运行，PID: $pid"
            return
        else
            rm -f "$PID_FILE"
        fi
    fi
    
    # 设置环境变量
    export PYTHONPATH="$PROJECT_DIR"
    export ENVIRONMENT="$MODE"
    export LOG_LEVEL="$LOG_LEVEL"
    export PORT="$PORT"
    
    # 启动主程序
    cd "$PROJECT_DIR"
    nohup $PYTHON_CMD main.py > "$LOG_DIR/qlib_trading.log" 2>&1 &
    local pid=$!
    echo $pid > "$PID_FILE"
    
    # 等待服务启动
    sleep 3
    
    # 检查服务是否正常启动
    if kill -0 "$pid" 2>/dev/null; then
        log_success "服务启动成功！"
        log_info "PID: $pid"
        log_info "端口: $PORT"
        log_info "模式: $MODE"
        log_info "日志级别: $LOG_LEVEL"
        log_info "日志文件: $LOG_DIR/qlib_trading.log"
        log_info ""
        log_info "访问地址: http://localhost:$PORT"
        log_info "API文档: http://localhost:$PORT/docs"
        log_info ""
        log_info "使用 '$0 --status' 查看状态"
        log_info "使用 '$0 --stop' 停止服务"
        log_info "使用 'tail -f $LOG_DIR/qlib_trading.log' 查看日志"
    else
        log_error "服务启动失败"
        rm -f "$PID_FILE"
        exit 1
    fi
}

# 停止服务
stop_service() {
    log_info "停止qlib交易系统..."
    
    if [[ -f "$PID_FILE" ]]; then
        local pid=$(cat "$PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            kill "$pid"
            sleep 2
            
            # 如果进程仍在运行，强制杀死
            if kill -0 "$pid" 2>/dev/null; then
                kill -9 "$pid"
                log_warning "强制停止服务，PID: $pid"
            else
                log_success "服务已停止，PID: $pid"
            fi
        else
            log_warning "进程不存在，PID: $pid"
        fi
        rm -f "$PID_FILE"
    else
        log_warning "PID文件不存在，服务可能未运行"
    fi
}

# 查看服务状态
check_service_status() {
    log_info "查看服务状态..."
    
    if [[ -f "$PID_FILE" ]]; then
        local pid=$(cat "$PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            log_success "服务正在运行"
            log_info "PID: $pid"
            
            # 获取进程信息
            if command -v ps &> /dev/null; then
                local process_info=$(ps -p "$pid" -o pid,ppid,cmd,etime,pcpu,pmem --no-headers 2>/dev/null)
                if [[ -n "$process_info" ]]; then
                    log_info "进程信息: $process_info"
                fi
            fi
            
            # 检查端口是否监听
            if command -v curl &> /dev/null; then
                if curl -s "http://localhost:$PORT/health" > /dev/null 2>&1; then
                    log_success "服务健康检查通过"
                    log_info "访问地址: http://localhost:$PORT"
                else
                    log_warning "服务健康检查失败"
                fi
            else
                log_warning "curl未安装，无法进行健康检查"
            fi
        else
            log_warning "PID文件存在但进程不存在，清理PID文件"
            rm -f "$PID_FILE"
        fi
    else
        log_info "服务未运行"
    fi
}

# 清理临时文件
clean_temp_files() {
    log_info "清理临时文件..."
    
    cd "$PROJECT_DIR"
    
    # 清理Python缓存文件
    find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
    find . -name "*.pyc" -delete 2>/dev/null || true
    find . -name "*.pyo" -delete 2>/dev/null || true
    
    # 清理临时目录
    rm -rf temp/* 2>/dev/null || true
    
    # 清理日志文件（保留最近的）
    if [[ -d "$LOG_DIR" ]]; then
        find "$LOG_DIR" -name "*.log" -mtime +7 -delete 2>/dev/null || true
    fi
    
    log_success "临时文件清理完成"
}

# 主执行流程
main() {
    log_info "========================================="
    log_info "qlib交易系统 Linux 单机启动器"
    log_info "========================================="
    
    # 解析命令行参数
    parse_args "$@"
    
    # 切换到项目目录
    cd "$PROJECT_DIR"
    
    # 处理特殊操作
    if [[ "$STOP_SERVICE" == true ]]; then
        stop_service
        return
    fi
    
    if [[ "$CHECK_STATUS" == true ]]; then
        check_service_status
        return
    fi
    
    if [[ "$CLEAN_FILES" == true ]]; then
        clean_temp_files
        return
    fi
    
    # 检查系统环境
    check_system_requirements
    
    # 创建目录结构
    create_directory_structure
    
    # 创建虚拟环境
    create_virtual_environment
    
    # 激活虚拟环境
    activate_virtual_environment
    
    # 安装依赖
    if [[ "$INSTALL_DEPS" == true ]]; then
        install_dependencies
    fi
    
    # 初始化数据库
    if [[ "$SETUP_DB" == true ]]; then
        initialize_database
    fi
    
    # 创建配置文件
    create_config_files
    
    # 启动服务
    if [[ "$INSTALL_DEPS" == false && "$SETUP_DB" == false ]]; then
        start_service
    fi
}

# 执行主函数
main "$@"
