{"test_name": "test_load_balancing_algorithms", "start_time": "2025-07-31T16:16:34.181314", "status": "completed", "details": {"load_balancing": {"algorithms_tested": ["round_robin", "weighted_random", "ip_hash"], "test_instances": 3, "results": {"round_robin": {"selections": ["api-001", "api-002", "api-003", "api-001", "api-002", "api-003", "api-001", "api-002", "api-003", "api-001"], "unique_selections": 3, "total_selections": 10}, "weighted_random": {"selections": ["api-003", "api-002", "api-002", "api-002", "api-001", "api-002", "api-002", "api-003", "api-002", "api-002"], "unique_selections": 3, "total_selections": 10}, "ip_hash": {"selections": ["api-003", "api-003", "api-003", "api-003", "api-003", "api-003", "api-003", "api-003", "api-003", "api-003"], "unique_selections": 1, "total_selections": 10}}}}, "end_time": "2025-07-31T16:16:34.183451", "duration": 0.002137}