apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: data-processor
    component: qlib-trading-system
  name: data-processor-deployment
  namespace: qlib-trading
spec:
  replicas: 1
  selector:
    matchLabels:
      app: data-processor
  template:
    metadata:
      labels:
        app: data-processor
        component: qlib-trading-system
    spec:
      containers:
      - env:
        - name: SERVICE_NAME
          value: data-processor
        - name: SERVICE_PORT
          value: '8001'
        - name: ENVIRONMENT
          value: production
        - name: LOG_LEVEL
          value: INFO
        image: localhost:5000/qlib-trading-data-processor:latest
        imagePullPolicy: Always
        livenessProbe:
          failureThreshold: 3
          httpGet:
            path: /health
            port: 8001
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
        name: data-processor
        ports:
        - containerPort: 8001
          name: http
        readinessProbe:
          failureThreshold: 3
          httpGet:
            path: /health
            port: 8001
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
        resources:
          limits:
            cpu: 500m
            memory: 1Gi
          requests:
            cpu: 100m
            memory: 256Mi
      restartPolicy: Always
