apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: risk-monitor
    component: qlib-trading-system
  name: risk-monitor-deployment
  namespace: qlib-trading
spec:
  replicas: 1
  selector:
    matchLabels:
      app: risk-monitor
  template:
    metadata:
      labels:
        app: risk-monitor
        component: qlib-trading-system
    spec:
      containers:
      - env:
        - name: SERVICE_NAME
          value: risk-monitor
        - name: SERVICE_PORT
          value: '8003'
        - name: ENVIRONMENT
          value: production
        - name: LOG_LEVEL
          value: INFO
        image: localhost:5000/qlib-trading-risk-monitor:latest
        imagePullPolicy: Always
        livenessProbe:
          failureThreshold: 3
          httpGet:
            path: /health
            port: 8003
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
        name: risk-monitor
        ports:
        - containerPort: 8003
          name: http
        readinessProbe:
          failureThreshold: 3
          httpGet:
            path: /health
            port: 8003
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
        resources:
          limits:
            cpu: 500m
            memory: 1Gi
          requests:
            cpu: 100m
            memory: 256Mi
      restartPolicy: Always
