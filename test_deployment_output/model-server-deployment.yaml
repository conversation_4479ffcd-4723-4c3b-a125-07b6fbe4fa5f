apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: model-server
    component: qlib-trading-system
  name: model-server-deployment
  namespace: qlib-trading
spec:
  replicas: 1
  selector:
    matchLabels:
      app: model-server
  template:
    metadata:
      labels:
        app: model-server
        component: qlib-trading-system
    spec:
      containers:
      - env:
        - name: SERVICE_NAME
          value: model-server
        - name: SERVICE_PORT
          value: '8002'
        - name: ENVIRONMENT
          value: production
        - name: LOG_LEVEL
          value: INFO
        image: localhost:5000/qlib-trading-model-server:latest
        imagePullPolicy: Always
        livenessProbe:
          failureThreshold: 3
          httpGet:
            path: /health
            port: 8002
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
        name: model-server
        ports:
        - containerPort: 8002
          name: http
        readinessProbe:
          failureThreshold: 3
          httpGet:
            path: /health
            port: 8002
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
        resources:
          limits:
            cpu: 500m
            memory: 1Gi
          requests:
            cpu: 100m
            memory: 256Mi
      restartPolicy: Always
