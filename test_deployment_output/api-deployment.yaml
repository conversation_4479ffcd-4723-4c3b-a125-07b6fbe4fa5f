apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: api
    component: qlib-trading-system
  name: api-deployment
  namespace: qlib-trading
spec:
  replicas: 2
  selector:
    matchLabels:
      app: api
  template:
    metadata:
      labels:
        app: api
        component: qlib-trading-system
    spec:
      containers:
      - env:
        - name: SERVICE_NAME
          value: api
        - name: SERVICE_PORT
          value: '8000'
        - name: ENVIRONMENT
          value: production
        - name: LOG_LEVEL
          value: INFO
        image: localhost:5000/qlib-trading-api:latest
        imagePullPolicy: Always
        livenessProbe:
          failureThreshold: 3
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
        name: api
        ports:
        - containerPort: 8000
          name: http
        readinessProbe:
          failureThreshold: 3
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
        resources:
          limits:
            cpu: 500m
            memory: 1Gi
          requests:
            cpu: 100m
            memory: 256Mi
      restartPolicy: Always
