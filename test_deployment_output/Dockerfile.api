# api 服务 Dockerfile
FROM python:3.9-slim

WORKDIR /app

ENV PYTHONPATH=/app
ENV SERVICE_NAME=api
ENV SERVICE_PORT=8000

RUN apt-get update && apt-get install -y gcc g++ && rm -rf /var/lib/apt/lists/*

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY qlib_trading_system/ ./qlib_trading_system/
COPY config/ ./config/
COPY main.py .

RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

EXPOSE 8000

HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD python -c "import requests; requests.get('http://localhost:8000/health')"

CMD ["python", "main.py", "--service", "api", "--port", "8000"]
