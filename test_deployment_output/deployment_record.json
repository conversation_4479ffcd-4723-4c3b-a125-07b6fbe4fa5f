{"deployment_id": "deploy-test-001", "pipeline_name": "test-pipeline", "environment": "staging", "version": "v1.0.0", "status": "success", "start_time": "2025-07-31T16:16:34.111232", "end_time": "2025-07-31T16:16:34.111232", "stages": {"build": {"status": "success", "start_time": "2025-07-31T16:16:34.111232", "logs": ["开始执行 build 阶段"], "end_time": "2025-07-31T16:16:34.111232", "duration": 0.0}, "test": {"status": "success", "start_time": "2025-07-31T16:16:34.111232", "logs": ["开始执行 test 阶段"], "end_time": "2025-07-31T16:16:34.111232", "duration": 0.0}, "deploy": {"status": "success", "start_time": "2025-07-31T16:16:34.111232", "logs": ["开始执行 deploy 阶段"], "end_time": "2025-07-31T16:16:34.111232", "duration": 0.0}, "verify": {"status": "success", "start_time": "2025-07-31T16:16:34.111232", "logs": ["开始执行 verify 阶段"], "end_time": "2025-07-31T16:16:34.111232", "duration": 0.0}}, "logs": ["build 阶段执行成功", "test 阶段执行成功", "deploy 阶段执行成功", "verify 阶段执行成功"]}