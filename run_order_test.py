#!/usr/bin/env python3
"""
运行订单管理系统测试
"""

import sys
import asyncio
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from qlib_trading_system.trading.orders.test_order_management import OrderManagementSystemTest

async def main():
    test_system = OrderManagementSystemTest()
    await test_system.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())