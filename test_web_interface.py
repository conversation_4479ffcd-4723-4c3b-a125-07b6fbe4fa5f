#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Qlib交易系统Web管理界面测试脚本

测试Web界面的各项功能
"""

import os
import sys
import time
import requests
import json
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/web_interface_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class WebInterfaceTest:
    """Web界面测试类"""
    
    def __init__(self, base_url="http://127.0.0.1:8000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
    
    def test_login(self):
        """测试用户登录功能"""
        logger.info("测试用户登录功能...")
        
        try:
            # 测试管理员登录
            login_data = {
                'username': 'admin',
                'password': 'admin123'
            }
            
            response = self.session.post(f"{self.base_url}/auth/login", data=login_data)
            
            if response.status_code == 200:
                result = response.json()
                if 'user' in result and result['user']['username'] == 'admin':
                    logger.info("✓ 管理员登录测试通过")
                    self.test_results.append(("用户登录", "PASS", "管理员登录成功"))
                    return True
                else:
                    logger.error("✗ 管理员登录测试失败: 返回数据格式错误")
                    self.test_results.append(("用户登录", "FAIL", "返回数据格式错误"))
                    return False
            else:
                logger.error(f"✗ 管理员登录测试失败: HTTP {response.status_code}")
                self.test_results.append(("用户登录", "FAIL", f"HTTP {response.status_code}"))
                return False
                
        except Exception as e:
            logger.error(f"✗ 用户登录测试异常: {e}")
            self.test_results.append(("用户登录", "ERROR", str(e)))
            return False
    
    def test_user_info(self):
        """测试获取用户信息"""
        logger.info("测试获取用户信息...")
        
        try:
            response = self.session.get(f"{self.base_url}/auth/me")
            
            if response.status_code == 200:
                result = response.json()
                if 'user' in result:
                    logger.info("✓ 获取用户信息测试通过")
                    self.test_results.append(("获取用户信息", "PASS", "用户信息获取成功"))
                    return True
                else:
                    logger.error("✗ 获取用户信息测试失败: 返回数据格式错误")
                    self.test_results.append(("获取用户信息", "FAIL", "返回数据格式错误"))
                    return False
            else:
                logger.error(f"✗ 获取用户信息测试失败: HTTP {response.status_code}")
                self.test_results.append(("获取用户信息", "FAIL", f"HTTP {response.status_code}"))
                return False
                
        except Exception as e:
            logger.error(f"✗ 获取用户信息测试异常: {e}")
            self.test_results.append(("获取用户信息", "ERROR", str(e)))
            return False
    
    def test_trading_status(self):
        """测试交易状态获取"""
        logger.info("测试交易状态获取...")
        
        try:
            response = self.session.get(f"{self.base_url}/api/trading/status")
            
            if response.status_code == 200:
                result = response.json()
                if 'status' in result:
                    logger.info("✓ 交易状态获取测试通过")
                    self.test_results.append(("交易状态获取", "PASS", "交易状态获取成功"))
                    return True
                else:
                    logger.error("✗ 交易状态获取测试失败: 返回数据格式错误")
                    self.test_results.append(("交易状态获取", "FAIL", "返回数据格式错误"))
                    return False
            else:
                logger.error(f"✗ 交易状态获取测试失败: HTTP {response.status_code}")
                self.test_results.append(("交易状态获取", "FAIL", f"HTTP {response.status_code}"))
                return False
                
        except Exception as e:
            logger.error(f"✗ 交易状态获取测试异常: {e}")
            self.test_results.append(("交易状态获取", "ERROR", str(e)))
            return False
    
    def test_positions(self):
        """测试持仓信息获取"""
        logger.info("测试持仓信息获取...")
        
        try:
            response = self.session.get(f"{self.base_url}/api/trading/positions")
            
            if response.status_code == 200:
                result = response.json()
                if 'positions' in result:
                    logger.info("✓ 持仓信息获取测试通过")
                    self.test_results.append(("持仓信息获取", "PASS", "持仓信息获取成功"))
                    return True
                else:
                    logger.error("✗ 持仓信息获取测试失败: 返回数据格式错误")
                    self.test_results.append(("持仓信息获取", "FAIL", "返回数据格式错误"))
                    return False
            else:
                logger.error(f"✗ 持仓信息获取测试失败: HTTP {response.status_code}")
                self.test_results.append(("持仓信息获取", "FAIL", f"HTTP {response.status_code}"))
                return False
                
        except Exception as e:
            logger.error(f"✗ 持仓信息获取测试异常: {e}")
            self.test_results.append(("持仓信息获取", "ERROR", str(e)))
            return False
    
    def test_orders(self):
        """测试订单信息获取"""
        logger.info("测试订单信息获取...")
        
        try:
            response = self.session.get(f"{self.base_url}/api/trading/orders")
            
            if response.status_code == 200:
                result = response.json()
                if 'orders' in result:
                    logger.info("✓ 订单信息获取测试通过")
                    self.test_results.append(("订单信息获取", "PASS", "订单信息获取成功"))
                    return True
                else:
                    logger.error("✗ 订单信息获取测试失败: 返回数据格式错误")
                    self.test_results.append(("订单信息获取", "FAIL", "返回数据格式错误"))
                    return False
            else:
                logger.error(f"✗ 订单信息获取测试失败: HTTP {response.status_code}")
                self.test_results.append(("订单信息获取", "FAIL", f"HTTP {response.status_code}"))
                return False
                
        except Exception as e:
            logger.error(f"✗ 订单信息获取测试异常: {e}")
            self.test_results.append(("订单信息获取", "ERROR", str(e)))
            return False
    
    def test_config_management(self):
        """测试配置管理功能"""
        logger.info("测试配置管理功能...")
        
        try:
            # 获取所有配置
            response = self.session.get(f"{self.base_url}/api/config/all")
            
            if response.status_code == 200:
                result = response.json()
                if 'configs' in result:
                    logger.info("✓ 配置获取测试通过")
                    
                    # 测试更新交易配置
                    trading_config = result['configs']['trading']
                    trading_config['total_capital'] = 200000.0  # 修改总资金
                    
                    update_response = self.session.put(
                        f"{self.base_url}/api/config/trading",
                        json=trading_config,
                        headers={'Content-Type': 'application/json'}
                    )
                    
                    if update_response.status_code == 200:
                        logger.info("✓ 配置更新测试通过")
                        self.test_results.append(("配置管理", "PASS", "配置获取和更新成功"))
                        return True
                    else:
                        logger.error(f"✗ 配置更新测试失败: HTTP {update_response.status_code}")
                        self.test_results.append(("配置管理", "FAIL", f"配置更新失败 HTTP {update_response.status_code}"))
                        return False
                else:
                    logger.error("✗ 配置获取测试失败: 返回数据格式错误")
                    self.test_results.append(("配置管理", "FAIL", "返回数据格式错误"))
                    return False
            else:
                logger.error(f"✗ 配置获取测试失败: HTTP {response.status_code}")
                self.test_results.append(("配置管理", "FAIL", f"HTTP {response.status_code}"))
                return False
                
        except Exception as e:
            logger.error(f"✗ 配置管理测试异常: {e}")
            self.test_results.append(("配置管理", "ERROR", str(e)))
            return False
    
    def test_data_query(self):
        """测试数据查询功能"""
        logger.info("测试数据查询功能...")
        
        try:
            # 测试股票信息查询
            response = self.session.get(f"{self.base_url}/api/data/stock/000001.SZ")
            
            if response.status_code == 200:
                result = response.json()
                if 'stock_info' in result:
                    logger.info("✓ 股票信息查询测试通过")
                    
                    # 测试市场概览
                    overview_response = self.session.get(f"{self.base_url}/api/data/market/overview")
                    
                    if overview_response.status_code == 200:
                        overview_result = overview_response.json()
                        if 'market_overview' in overview_result:
                            logger.info("✓ 市场概览查询测试通过")
                            self.test_results.append(("数据查询", "PASS", "股票信息和市场概览查询成功"))
                            return True
                        else:
                            logger.error("✗ 市场概览查询测试失败: 返回数据格式错误")
                            self.test_results.append(("数据查询", "FAIL", "市场概览返回数据格式错误"))
                            return False
                    else:
                        logger.error(f"✗ 市场概览查询测试失败: HTTP {overview_response.status_code}")
                        self.test_results.append(("数据查询", "FAIL", f"市场概览查询失败 HTTP {overview_response.status_code}"))
                        return False
                else:
                    logger.error("✗ 股票信息查询测试失败: 返回数据格式错误")
                    self.test_results.append(("数据查询", "FAIL", "股票信息返回数据格式错误"))
                    return False
            else:
                logger.error(f"✗ 股票信息查询测试失败: HTTP {response.status_code}")
                self.test_results.append(("数据查询", "FAIL", f"HTTP {response.status_code}"))
                return False
                
        except Exception as e:
            logger.error(f"✗ 数据查询测试异常: {e}")
            self.test_results.append(("数据查询", "ERROR", str(e)))
            return False
    
    def test_monitoring(self):
        """测试系统监控功能"""
        logger.info("测试系统监控功能...")
        
        try:
            # 测试系统指标获取
            response = self.session.get(f"{self.base_url}/api/monitoring/system")
            
            if response.status_code == 200:
                result = response.json()
                if 'metrics' in result:
                    logger.info("✓ 系统指标获取测试通过")
                    
                    # 测试告警信息获取
                    alerts_response = self.session.get(f"{self.base_url}/api/monitoring/alerts")
                    
                    if alerts_response.status_code == 200:
                        alerts_result = alerts_response.json()
                        if 'alerts' in alerts_result:
                            logger.info("✓ 告警信息获取测试通过")
                            self.test_results.append(("系统监控", "PASS", "系统指标和告警信息获取成功"))
                            return True
                        else:
                            logger.error("✗ 告警信息获取测试失败: 返回数据格式错误")
                            self.test_results.append(("系统监控", "FAIL", "告警信息返回数据格式错误"))
                            return False
                    else:
                        logger.error(f"✗ 告警信息获取测试失败: HTTP {alerts_response.status_code}")
                        self.test_results.append(("系统监控", "FAIL", f"告警信息获取失败 HTTP {alerts_response.status_code}"))
                        return False
                else:
                    logger.error("✗ 系统指标获取测试失败: 返回数据格式错误")
                    self.test_results.append(("系统监控", "FAIL", "系统指标返回数据格式错误"))
                    return False
            else:
                logger.error(f"✗ 系统指标获取测试失败: HTTP {response.status_code}")
                self.test_results.append(("系统监控", "FAIL", f"HTTP {response.status_code}"))
                return False
                
        except Exception as e:
            logger.error(f"✗ 系统监控测试异常: {e}")
            self.test_results.append(("系统监控", "ERROR", str(e)))
            return False
    
    def test_logout(self):
        """测试用户登出功能"""
        logger.info("测试用户登出功能...")
        
        try:
            response = self.session.post(f"{self.base_url}/auth/logout")
            
            if response.status_code == 200:
                result = response.json()
                if 'message' in result:
                    logger.info("✓ 用户登出测试通过")
                    self.test_results.append(("用户登出", "PASS", "用户登出成功"))
                    return True
                else:
                    logger.error("✗ 用户登出测试失败: 返回数据格式错误")
                    self.test_results.append(("用户登出", "FAIL", "返回数据格式错误"))
                    return False
            else:
                logger.error(f"✗ 用户登出测试失败: HTTP {response.status_code}")
                self.test_results.append(("用户登出", "FAIL", f"HTTP {response.status_code}"))
                return False
                
        except Exception as e:
            logger.error(f"✗ 用户登出测试异常: {e}")
            self.test_results.append(("用户登出", "ERROR", str(e)))
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        logger.info("开始运行Web界面功能测试...")
        
        # 等待服务器启动
        logger.info("等待Web服务器启动...")
        time.sleep(3)
        
        # 检查服务器是否可访问
        try:
            response = requests.get(f"{self.base_url}/", timeout=5)
            logger.info(f"Web服务器响应状态: {response.status_code}")
        except Exception as e:
            logger.error(f"无法连接到Web服务器: {e}")
            logger.error("请确保Web服务器已启动 (运行 python run_web_app.py)")
            return False
        
        # 运行测试
        tests = [
            self.test_login,
            self.test_user_info,
            self.test_trading_status,
            self.test_positions,
            self.test_orders,
            self.test_config_management,
            self.test_data_query,
            self.test_monitoring,
            self.test_logout
        ]
        
        passed = 0
        failed = 0
        errors = 0
        
        for test in tests:
            try:
                if test():
                    passed += 1
                else:
                    failed += 1
            except Exception as e:
                logger.error(f"测试执行异常: {e}")
                errors += 1
        
        # 生成测试报告
        self.generate_test_report(passed, failed, errors)
        
        return failed == 0 and errors == 0
    
    def generate_test_report(self, passed, failed, errors):
        """生成测试报告"""
        logger.info("生成测试报告...")
        
        total = passed + failed + errors
        
        report = {
            "test_summary": {
                "total_tests": total,
                "passed": passed,
                "failed": failed,
                "errors": errors,
                "success_rate": f"{(passed/total*100):.1f}%" if total > 0 else "0%"
            },
            "test_results": self.test_results,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        
        # 保存测试报告
        report_file = "logs/web_interface_test_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        # 打印测试总结
        logger.info("=" * 60)
        logger.info("Web界面功能测试总结")
        logger.info("=" * 60)
        logger.info(f"总测试数: {total}")
        logger.info(f"通过: {passed}")
        logger.info(f"失败: {failed}")
        logger.info(f"错误: {errors}")
        logger.info(f"成功率: {(passed/total*100):.1f}%" if total > 0 else "0%")
        logger.info("=" * 60)
        
        if failed > 0 or errors > 0:
            logger.warning("部分测试未通过，请检查日志了解详情")
        else:
            logger.info("所有测试通过！Web界面功能正常")
        
        logger.info(f"详细测试报告已保存到: {report_file}")

def main():
    """主函数"""
    # 创建日志目录
    os.makedirs("logs", exist_ok=True)
    
    logger.info("启动Qlib交易系统Web界面功能测试")
    
    # 创建测试实例
    tester = WebInterfaceTest()
    
    # 运行所有测试
    success = tester.run_all_tests()
    
    if success:
        logger.info("Web界面功能测试完成，所有测试通过")
        sys.exit(0)
    else:
        logger.error("Web界面功能测试完成，存在失败的测试")
        sys.exit(1)

if __name__ == "__main__":
    main()