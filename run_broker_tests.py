#!/usr/bin/env python3
"""
券商接口适配层测试运行脚本
解决模块导入问题，确保测试可以正确运行
"""

import sys
import os
import subprocess
from pathlib import Path

def setup_python_path():
    """设置Python路径"""
    # 获取项目根目录
    project_root = Path(__file__).parent.absolute()
    
    # 添加到Python路径
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))
    
    # 设置环境变量
    os.environ['PYTHONPATH'] = str(project_root)
    
    return project_root

def run_simple_test():
    """运行简化测试"""
    print("=" * 60)
    print("运行券商接口适配层简化测试")
    print("=" * 60)
    
    try:
        # 运行简化测试脚本
        result = subprocess.run([
            sys.executable, 'test_broker_adapter_simple.py'
        ], capture_output=True, text=True, timeout=120)
        
        print("测试输出:")
        print(result.stdout)
        
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("❌ 测试超时")
        return False
    except Exception as e:
        print(f"❌ 运行测试时发生异常: {e}")
        return False

def run_integration_test():
    """运行集成测试"""
    print("=" * 60)
    print("运行券商接口适配层集成测试")
    print("=" * 60)
    
    try:
        # 直接导入并运行测试
        from qlib_trading_system.trading.execution.test_broker_integration import main
        return main() == 0
        
    except ImportError as e:
        print(f"❌ 导入测试模块失败: {e}")
        print("尝试使用subprocess运行...")
        
        try:
            # 使用subprocess运行
            test_file = Path("qlib_trading_system/trading/execution/test_broker_integration.py")
            result = subprocess.run([
                sys.executable, str(test_file)
            ], capture_output=True, text=True, timeout=180)
            
            print("测试输出:")
            print(result.stdout)
            
            if result.stderr:
                print("错误输出:")
                print(result.stderr)
            
            return result.returncode == 0
            
        except subprocess.TimeoutExpired:
            print("❌ 集成测试超时")
            return False
        except Exception as e:
            print(f"❌ 运行集成测试时发生异常: {e}")
            return False
    
    except Exception as e:
        print(f"❌ 运行集成测试时发生异常: {e}")
        return False

def check_dependencies():
    """检查依赖"""
    print("检查项目结构和依赖...")
    
    required_files = [
        "qlib_trading_system/__init__.py",
        "qlib_trading_system/trading/__init__.py", 
        "qlib_trading_system/trading/execution/__init__.py",
        "qlib_trading_system/trading/execution/broker_interface.py",
        "qlib_trading_system/trading/execution/broker_adapter.py",
        "qlib_trading_system/trading/execution/order_router.py",
        "qlib_trading_system/trading/execution/trading_monitor.py",
        "qlib_trading_system/trading/execution/retry_handler.py",
        "qlib_trading_system/trading/execution/mock_broker.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ 缺少以下文件:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        return False
    
    print("✅ 所有必需文件都存在")
    return True

def create_missing_init_files():
    """创建缺失的__init__.py文件"""
    init_files = [
        "qlib_trading_system/__init__.py",
        "qlib_trading_system/trading/__init__.py"
    ]
    
    for init_file in init_files:
        init_path = Path(init_file)
        if not init_path.exists():
            print(f"创建缺失的 {init_file}")
            init_path.parent.mkdir(parents=True, exist_ok=True)
            init_path.write_text('"""Package initialization"""', encoding='utf-8')

def main():
    """主函数"""
    print("券商接口适配层测试运行器")
    print("=" * 60)
    
    # 设置Python路径
    project_root = setup_python_path()
    print(f"项目根目录: {project_root}")
    
    # 创建缺失的__init__.py文件
    create_missing_init_files()
    
    # 检查依赖
    if not check_dependencies():
        print("❌ 依赖检查失败，无法运行测试")
        return 1
    
    # 运行测试
    tests_passed = 0
    total_tests = 2
    
    print("\n" + "=" * 60)
    print("开始运行测试...")
    print("=" * 60)
    
    # 1. 运行简化测试
    print("\n1. 运行简化测试...")
    if run_simple_test():
        print("✅ 简化测试通过")
        tests_passed += 1
    else:
        print("❌ 简化测试失败")
    
    # 2. 运行集成测试
    print("\n2. 运行集成测试...")
    if run_integration_test():
        print("✅ 集成测试通过")
        tests_passed += 1
    else:
        print("❌ 集成测试失败")
    
    # 总结
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    print(f"通过测试: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("🎉 所有测试都通过了！")
        print("\n实现的功能包括:")
        print("1. ✓ 多券商API统一接口封装")
        print("2. ✓ 订单路由和负载均衡机制") 
        print("3. ✓ 交易接口容错和重试逻辑")
        print("4. ✓ 交易状态同步和监控系统")
        return 0
    else:
        print("❌ 部分测试失败")
        return 1

if __name__ == "__main__":
    exit(main())