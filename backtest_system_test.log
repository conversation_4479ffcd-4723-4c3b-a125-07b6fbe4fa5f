2025-07-30 12:10:09,486 - __main__ - INFO - 开始回测系统集成测试
2025-07-30 12:10:09,486 - __main__ - INFO - ================================================================================
2025-07-30 12:10:09,487 - __main__ - INFO - ==================================================
2025-07-30 12:10:09,487 - __main__ - INFO - 开始测试历史数据回放功能
2025-07-30 12:10:09,488 - qlib_trading_system.backtest.data_replay - INFO - 历史数据回放系统初始化完成，时间范围：2024-01-01 00:00:00 - 2024-01-10 00:00:00
2025-07-30 12:10:09,488 - qlib_trading_system.backtest.data_replay - INFO - 开始加载历史数据...
2025-07-30 12:10:09,489 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000001.SZ_1d.csv
2025-07-30 12:10:09,489 - qlib_trading_system.backtest.data_replay - INFO - 生成 000001.SZ 的模拟历史数据
2025-07-30 12:10:09,499 - qlib_trading_system.backtest.data_replay - WARNING - 计算技术指标失败：'close'
2025-07-30 12:10:09,499 - qlib_trading_system.backtest.data_replay - WARNING - 未找到 000001.SZ 的历史数据
2025-07-30 12:10:09,499 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000002.SZ_1d.csv
2025-07-30 12:10:09,500 - qlib_trading_system.backtest.data_replay - INFO - 生成 000002.SZ 的模拟历史数据
2025-07-30 12:10:09,504 - qlib_trading_system.backtest.data_replay - WARNING - 计算技术指标失败：'close'
2025-07-30 12:10:09,504 - qlib_trading_system.backtest.data_replay - WARNING - 未找到 000002.SZ 的历史数据
2025-07-30 12:10:09,505 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000858.SZ_1d.csv
2025-07-30 12:10:09,505 - qlib_trading_system.backtest.data_replay - INFO - 生成 000858.SZ 的模拟历史数据
2025-07-30 12:10:09,507 - qlib_trading_system.backtest.data_replay - WARNING - 计算技术指标失败：'close'
2025-07-30 12:10:09,508 - qlib_trading_system.backtest.data_replay - WARNING - 未找到 000858.SZ 的历史数据
2025-07-30 12:10:09,508 - qlib_trading_system.backtest.data_replay - INFO - 历史数据加载完成，共加载 0 只股票
2025-07-30 12:10:09,508 - __main__ - INFO - 成功加载 0 只股票的数据
2025-07-30 12:10:09,509 - qlib_trading_system.backtest.data_replay - INFO - 开始加载历史数据...
2025-07-30 12:10:09,509 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000001.SZ_1d.csv
2025-07-30 12:10:09,509 - qlib_trading_system.backtest.data_replay - INFO - 生成 000001.SZ 的模拟历史数据
2025-07-30 12:10:09,513 - qlib_trading_system.backtest.data_replay - WARNING - 计算技术指标失败：'close'
2025-07-30 12:10:09,513 - qlib_trading_system.backtest.data_replay - WARNING - 未找到 000001.SZ 的历史数据
2025-07-30 12:10:09,514 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000002.SZ_1d.csv
2025-07-30 12:10:09,514 - qlib_trading_system.backtest.data_replay - INFO - 生成 000002.SZ 的模拟历史数据
2025-07-30 12:10:09,517 - qlib_trading_system.backtest.data_replay - WARNING - 计算技术指标失败：'close'
2025-07-30 12:10:09,517 - qlib_trading_system.backtest.data_replay - WARNING - 未找到 000002.SZ 的历史数据
2025-07-30 12:10:09,518 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000858.SZ_1d.csv
2025-07-30 12:10:09,518 - qlib_trading_system.backtest.data_replay - INFO - 生成 000858.SZ 的模拟历史数据
2025-07-30 12:10:09,521 - qlib_trading_system.backtest.data_replay - WARNING - 计算技术指标失败：'close'
2025-07-30 12:10:09,522 - qlib_trading_system.backtest.data_replay - WARNING - 未找到 000858.SZ 的历史数据
2025-07-30 12:10:09,522 - qlib_trading_system.backtest.data_replay - INFO - 历史数据加载完成，共加载 0 只股票
2025-07-30 12:10:09,522 - qlib_trading_system.backtest.data_replay - ERROR - 没有可用的历史数据进行回放
2025-07-30 12:10:09,522 - __main__ - INFO - 数据摘要：{}
2025-07-30 12:10:09,524 - __main__ - INFO - 历史数据回放功能测试通过 ✓
2025-07-30 12:10:09,524 - __main__ - INFO - ==================================================
2025-07-30 12:10:09,524 - __main__ - INFO - 开始测试回测引擎功能
2025-07-30 12:10:09,525 - qlib_trading_system.backtest.data_replay - INFO - 历史数据回放系统初始化完成，时间范围：2024-01-01 00:00:00 - 2024-01-31 00:00:00
2025-07-30 12:10:09,525 - qlib_trading_system.backtest.backtest_engine - INFO - 回测引擎初始化完成，初始资金：1,000,000.00
2025-07-30 12:10:09,526 - qlib_trading_system.backtest.backtest_engine - INFO - 数据回放器设置完成
2025-07-30 12:10:09,526 - __main__ - INFO - 初始化测试MA策略：test_ma_5_20, 短期=5, 长期=20
2025-07-30 12:10:09,526 - qlib_trading_system.backtest.backtest_engine - INFO - 添加策略：test_ma_5_20
2025-07-30 12:10:09,527 - __main__ - INFO - 开始运行回测...
2025-07-30 12:10:09,527 - qlib_trading_system.backtest.backtest_engine - INFO - 开始运行回测...
2025-07-30 12:10:09,528 - qlib_trading_system.backtest.data_replay - INFO - 开始加载历史数据...
2025-07-30 12:10:09,528 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000001.SZ_1d.csv
2025-07-30 12:10:09,529 - qlib_trading_system.backtest.data_replay - INFO - 生成 000001.SZ 的模拟历史数据
2025-07-30 12:10:09,538 - qlib_trading_system.backtest.data_replay - WARNING - 计算技术指标失败：'close'
2025-07-30 12:10:09,538 - qlib_trading_system.backtest.data_replay - WARNING - 未找到 000001.SZ 的历史数据
2025-07-30 12:10:09,539 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000002.SZ_1d.csv
2025-07-30 12:10:09,539 - qlib_trading_system.backtest.data_replay - INFO - 生成 000002.SZ 的模拟历史数据
2025-07-30 12:10:09,543 - qlib_trading_system.backtest.data_replay - WARNING - 计算技术指标失败：'close'
2025-07-30 12:10:09,544 - qlib_trading_system.backtest.data_replay - WARNING - 未找到 000002.SZ 的历史数据
2025-07-30 12:10:09,544 - qlib_trading_system.backtest.data_replay - INFO - 历史数据加载完成，共加载 0 只股票
2025-07-30 12:10:09,544 - qlib_trading_system.backtest.data_replay - ERROR - 没有可用的历史数据进行回放
2025-07-30 12:10:09,545 - qlib_trading_system.backtest.backtest_engine - INFO - 回测完成
2025-07-30 12:10:09,545 - __main__ - INFO - 回测结果:
2025-07-30 12:10:09,545 - __main__ - INFO -   初始资金: 0.00
2025-07-30 12:10:09,546 - __main__ - INFO -   最终价值: 0.00
2025-07-30 12:10:09,546 - __main__ - INFO -   总收益率: 0.00%
2025-07-30 12:10:09,546 - __main__ - INFO -   年化收益率: 0.00%
2025-07-30 12:10:09,546 - __main__ - INFO -   夏普比率: 0.000
2025-07-30 12:10:09,547 - __main__ - INFO -   最大回撤: 0.00%
2025-07-30 12:10:09,547 - __main__ - INFO -   总交易次数: 0
2025-07-30 12:10:09,548 - __main__ - INFO -   胜率: 0.00%
2025-07-30 12:10:09,548 - __main__ - INFO - 业绩摘要:
2025-07-30 12:10:09,549 - __main__ - INFO - 尚未运行回测
2025-07-30 12:10:09,551 - __main__ - INFO - 回测引擎功能测试通过 ✓
2025-07-30 12:10:09,552 - __main__ - INFO - ==================================================
2025-07-30 12:10:09,552 - __main__ - INFO - 开始测试性能分析功能
2025-07-30 12:10:09,552 - __main__ - WARNING - 没有回测结果，跳过性能分析测试
2025-07-30 12:10:09,554 - __main__ - INFO - ==================================================
2025-07-30 12:10:09,554 - __main__ - INFO - 开始测试并行回测功能
2025-07-30 12:10:09,555 - qlib_trading_system.backtest.parallel_backtest - INFO - 并行回测框架初始化完成，将使用 2 个工作进程
2025-07-30 12:10:09,555 - __main__ - INFO - 开始运行并行回测...
2025-07-30 12:10:09,556 - qlib_trading_system.backtest.parallel_backtest - INFO - 开始并行回测，共 3 个策略
2025-07-30 12:10:09,556 - qlib_trading_system.backtest.parallel_backtest - INFO - 使用多线程模式执行回测
2025-07-30 12:10:09,559 - qlib_trading_system.backtest.data_replay - INFO - 历史数据回放系统初始化完成，时间范围：2024-01-01 00:00:00 - 2024-01-31 00:00:00
2025-07-30 12:10:09,559 - qlib_trading_system.backtest.backtest_engine - INFO - 回测引擎初始化完成，初始资金：1,000,000.00
2025-07-30 12:10:09,559 - qlib_trading_system.backtest.data_replay - INFO - 历史数据回放系统初始化完成，时间范围：2024-01-01 00:00:00 - 2024-01-31 00:00:00
2025-07-30 12:10:09,560 - qlib_trading_system.backtest.backtest_engine - INFO - 数据回放器设置完成
2025-07-30 12:10:09,561 - qlib_trading_system.backtest.backtest_engine - INFO - 回测引擎初始化完成，初始资金：1,000,000.00
2025-07-30 12:10:09,561 - __main__ - INFO - 初始化测试买入持有策略：buy_hold_test, 目标股票=['000001.SZ', '000002.SZ']
2025-07-30 12:10:09,561 - qlib_trading_system.backtest.backtest_engine - INFO - 数据回放器设置完成
2025-07-30 12:10:09,562 - qlib_trading_system.backtest.backtest_engine - INFO - 添加策略：buy_hold_test
2025-07-30 12:10:09,562 - __main__ - INFO - 初始化测试MA策略：ma_5_10_test, 短期=5, 长期=10
2025-07-30 12:10:09,562 - qlib_trading_system.backtest.backtest_engine - INFO - 开始运行回测...
2025-07-30 12:10:09,563 - qlib_trading_system.backtest.backtest_engine - INFO - 添加策略：ma_5_10_test
2025-07-30 12:10:09,563 - qlib_trading_system.backtest.data_replay - INFO - 开始加载历史数据...
2025-07-30 12:10:09,564 - qlib_trading_system.backtest.backtest_engine - INFO - 开始运行回测...
2025-07-30 12:10:09,565 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000001.SZ_1d.csv
2025-07-30 12:10:09,565 - qlib_trading_system.backtest.data_replay - INFO - 开始加载历史数据...
2025-07-30 12:10:09,565 - qlib_trading_system.backtest.data_replay - INFO - 生成 000001.SZ 的模拟历史数据
2025-07-30 12:10:09,567 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000001.SZ_1d.csv
2025-07-30 12:10:09,572 - qlib_trading_system.backtest.data_replay - WARNING - 计算技术指标失败：'close'
2025-07-30 12:10:09,572 - qlib_trading_system.backtest.data_replay - INFO - 生成 000001.SZ 的模拟历史数据
2025-07-30 12:10:09,572 - qlib_trading_system.backtest.data_replay - WARNING - 未找到 000001.SZ 的历史数据
2025-07-30 12:10:09,575 - qlib_trading_system.backtest.data_replay - WARNING - 计算技术指标失败：'close'
2025-07-30 12:10:09,576 - qlib_trading_system.backtest.data_replay - WARNING - 未找到 000001.SZ 的历史数据
2025-07-30 12:10:09,576 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000002.SZ_1d.csv
2025-07-30 12:10:09,577 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000002.SZ_1d.csv
2025-07-30 12:10:09,577 - qlib_trading_system.backtest.data_replay - INFO - 生成 000002.SZ 的模拟历史数据
2025-07-30 12:10:09,577 - qlib_trading_system.backtest.data_replay - INFO - 生成 000002.SZ 的模拟历史数据
2025-07-30 12:10:09,583 - qlib_trading_system.backtest.data_replay - WARNING - 计算技术指标失败：'close'
2025-07-30 12:10:09,588 - qlib_trading_system.backtest.data_replay - WARNING - 计算技术指标失败：'close'
2025-07-30 12:10:09,588 - qlib_trading_system.backtest.data_replay - WARNING - 未找到 000002.SZ 的历史数据
2025-07-30 12:10:09,589 - qlib_trading_system.backtest.data_replay - WARNING - 未找到 000002.SZ 的历史数据
2025-07-30 12:10:09,589 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000858.SZ_1d.csv
2025-07-30 12:10:09,589 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000858.SZ_1d.csv
2025-07-30 12:10:09,589 - qlib_trading_system.backtest.data_replay - INFO - 生成 000858.SZ 的模拟历史数据
2025-07-30 12:10:09,591 - qlib_trading_system.backtest.data_replay - INFO - 生成 000858.SZ 的模拟历史数据
2025-07-30 12:10:09,595 - qlib_trading_system.backtest.data_replay - WARNING - 计算技术指标失败：'close'
2025-07-30 12:10:09,602 - qlib_trading_system.backtest.data_replay - WARNING - 计算技术指标失败：'close'
2025-07-30 12:10:09,602 - qlib_trading_system.backtest.data_replay - WARNING - 未找到 000858.SZ 的历史数据
2025-07-30 12:10:09,604 - qlib_trading_system.backtest.data_replay - WARNING - 未找到 000858.SZ 的历史数据
2025-07-30 12:10:09,604 - qlib_trading_system.backtest.data_replay - INFO - 历史数据加载完成，共加载 0 只股票
2025-07-30 12:10:09,604 - qlib_trading_system.backtest.data_replay - INFO - 历史数据加载完成，共加载 0 只股票
2025-07-30 12:10:09,605 - qlib_trading_system.backtest.data_replay - ERROR - 没有可用的历史数据进行回放
2025-07-30 12:10:09,605 - qlib_trading_system.backtest.data_replay - ERROR - 没有可用的历史数据进行回放
2025-07-30 12:10:09,605 - qlib_trading_system.backtest.backtest_engine - INFO - 回测完成
2025-07-30 12:10:09,606 - qlib_trading_system.backtest.backtest_engine - INFO - 回测完成
2025-07-30 12:10:09,606 - qlib_trading_system.backtest.parallel_backtest - INFO - 策略 buy_hold_test 回测完成
2025-07-30 12:10:09,606 - qlib_trading_system.backtest.data_replay - INFO - 历史数据回放系统初始化完成，时间范围：2024-01-01 00:00:00 - 2024-01-31 00:00:00
2025-07-30 12:10:09,607 - qlib_trading_system.backtest.parallel_backtest - INFO - 策略 ma_5_10_test 回测完成
2025-07-30 12:10:09,607 - qlib_trading_system.backtest.backtest_engine - INFO - 回测引擎初始化完成，初始资金：1,000,000.00
2025-07-30 12:10:09,608 - qlib_trading_system.backtest.backtest_engine - INFO - 数据回放器设置完成
2025-07-30 12:10:09,608 - __main__ - INFO - 初始化测试MA策略：ma_5_20_test, 短期=5, 长期=20
2025-07-30 12:10:09,608 - qlib_trading_system.backtest.backtest_engine - INFO - 添加策略：ma_5_20_test
2025-07-30 12:10:09,609 - qlib_trading_system.backtest.backtest_engine - INFO - 开始运行回测...
2025-07-30 12:10:09,609 - qlib_trading_system.backtest.data_replay - INFO - 开始加载历史数据...
2025-07-30 12:10:09,610 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000001.SZ_1d.csv
2025-07-30 12:10:09,610 - qlib_trading_system.backtest.data_replay - INFO - 生成 000001.SZ 的模拟历史数据
2025-07-30 12:10:09,617 - qlib_trading_system.backtest.data_replay - WARNING - 计算技术指标失败：'close'
2025-07-30 12:10:09,617 - qlib_trading_system.backtest.data_replay - WARNING - 未找到 000001.SZ 的历史数据
2025-07-30 12:10:09,618 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000002.SZ_1d.csv
2025-07-30 12:10:09,619 - qlib_trading_system.backtest.data_replay - INFO - 生成 000002.SZ 的模拟历史数据
2025-07-30 12:10:09,623 - qlib_trading_system.backtest.data_replay - WARNING - 计算技术指标失败：'close'
2025-07-30 12:10:09,624 - qlib_trading_system.backtest.data_replay - WARNING - 未找到 000002.SZ 的历史数据
2025-07-30 12:10:09,625 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000858.SZ_1d.csv
2025-07-30 12:10:09,625 - qlib_trading_system.backtest.data_replay - INFO - 生成 000858.SZ 的模拟历史数据
2025-07-30 12:10:09,630 - qlib_trading_system.backtest.data_replay - WARNING - 计算技术指标失败：'close'
2025-07-30 12:10:09,631 - qlib_trading_system.backtest.data_replay - WARNING - 未找到 000858.SZ 的历史数据
2025-07-30 12:10:09,631 - qlib_trading_system.backtest.data_replay - INFO - 历史数据加载完成，共加载 0 只股票
2025-07-30 12:10:09,633 - qlib_trading_system.backtest.data_replay - ERROR - 没有可用的历史数据进行回放
2025-07-30 12:10:09,633 - qlib_trading_system.backtest.backtest_engine - INFO - 回测完成
2025-07-30 12:10:09,634 - qlib_trading_system.backtest.parallel_backtest - INFO - 策略 ma_5_20_test 回测完成
2025-07-30 12:10:09,634 - qlib_trading_system.backtest.parallel_backtest - INFO - 计算所有策略的性能指标
2025-07-30 12:10:09,636 - qlib_trading_system.backtest.performance_analyzer - INFO - 回测结果分析器初始化完成
2025-07-30 12:10:09,636 - qlib_trading_system.backtest.performance_analyzer - WARNING - 权益曲线数据为空，无法计算指标
2025-07-30 12:10:09,637 - qlib_trading_system.backtest.performance_analyzer - INFO - 回测结果分析器初始化完成
2025-07-30 12:10:09,638 - qlib_trading_system.backtest.performance_analyzer - WARNING - 权益曲线数据为空，无法计算指标
2025-07-30 12:10:09,639 - qlib_trading_system.backtest.performance_analyzer - INFO - 回测结果分析器初始化完成
2025-07-30 12:10:09,639 - qlib_trading_system.backtest.performance_analyzer - WARNING - 权益曲线数据为空，无法计算指标
2025-07-30 12:10:09,641 - qlib_trading_system.backtest.parallel_backtest - INFO - 保存回测结果到文件
2025-07-30 12:10:09,643 - qlib_trading_system.backtest.performance_analyzer - INFO - 回测结果分析器初始化完成
2025-07-30 12:10:09,643 - qlib_trading_system.backtest.performance_analyzer - INFO - 导出回测结果到Excel：test_parallel_results\buy_hold_test_20250730_121009.xlsx
2025-07-30 12:10:10,384 - qlib_trading_system.backtest.performance_analyzer - WARNING - 权益曲线数据为空，无法计算指标
2025-07-30 12:10:10,423 - qlib_trading_system.backtest.performance_analyzer - INFO - Excel导出完成
2025-07-30 12:10:10,426 - qlib_trading_system.backtest.performance_analyzer - INFO - 回测结果分析器初始化完成
2025-07-30 12:10:10,427 - qlib_trading_system.backtest.performance_analyzer - INFO - 导出回测结果到Excel：test_parallel_results\ma_5_10_test_20250730_121009.xlsx
2025-07-30 12:10:10,431 - qlib_trading_system.backtest.performance_analyzer - WARNING - 权益曲线数据为空，无法计算指标
2025-07-30 12:10:10,529 - qlib_trading_system.backtest.performance_analyzer - INFO - Excel导出完成
2025-07-30 12:10:10,531 - qlib_trading_system.backtest.performance_analyzer - INFO - 回测结果分析器初始化完成
2025-07-30 12:10:10,532 - qlib_trading_system.backtest.performance_analyzer - INFO - 导出回测结果到Excel：test_parallel_results\ma_5_20_test_20250730_121009.xlsx
2025-07-30 12:10:10,539 - qlib_trading_system.backtest.performance_analyzer - WARNING - 权益曲线数据为空，无法计算指标
2025-07-30 12:10:10,561 - qlib_trading_system.backtest.performance_analyzer - INFO - Excel导出完成
2025-07-30 12:10:10,562 - qlib_trading_system.backtest.parallel_backtest - INFO - 生成策略对比分析
2025-07-30 12:10:10,649 - qlib_trading_system.backtest.parallel_backtest - INFO - 策略对比报告已保存到：test_parallel_results\comparison_report_20250730_121010.md
2025-07-30 12:10:10,650 - qlib_trading_system.backtest.parallel_backtest - INFO - 并行回测完成，耗时 1.09 秒
2025-07-30 12:10:10,651 - __main__ - INFO - 并行回测完成，共 3 个策略
2025-07-30 12:10:10,651 - __main__ - INFO - 策略 buy_hold_test:
2025-07-30 12:10:10,652 - __main__ - INFO -   总收益率: 0.00%
2025-07-30 12:10:10,653 - __main__ - INFO -   年化收益率: 0.00%
2025-07-30 12:10:10,653 - __main__ - INFO -   夏普比率: 0.000
2025-07-30 12:10:10,654 - __main__ - INFO - 策略 ma_5_10_test:
2025-07-30 12:10:10,654 - __main__ - INFO -   总收益率: 0.00%
2025-07-30 12:10:10,655 - __main__ - INFO -   年化收益率: 0.00%
2025-07-30 12:10:10,657 - __main__ - INFO -   夏普比率: 0.000
2025-07-30 12:10:10,659 - __main__ - INFO - 策略 ma_5_20_test:
2025-07-30 12:10:10,659 - __main__ - INFO -   总收益率: 0.00%
2025-07-30 12:10:10,661 - __main__ - INFO -   年化收益率: 0.00%
2025-07-30 12:10:10,661 - __main__ - INFO -   夏普比率: 0.000
2025-07-30 12:10:10,662 - __main__ - INFO - 
最佳策略排名:
2025-07-30 12:10:10,663 - __main__ - INFO - 1. buy_hold_test: 年化收益率=0.00%, 夏普比率=0.000
2025-07-30 12:10:10,663 - __main__ - INFO - 2. ma_5_10_test: 年化收益率=0.00%, 夏普比率=0.000
2025-07-30 12:10:10,664 - __main__ - INFO - 
策略对比矩阵:
2025-07-30 12:10:10,677 - __main__ - INFO -          策略ID  总收益率  年化收益率  年化波动率  夏普比率  索提诺比率  最大回撤  胜率  盈亏比  总交易次数  换手率
buy_hold_test   0.0    0.0    0.0   0.0    0.0   0.0 0.0  0.0      0  0.0
 ma_5_10_test   0.0    0.0    0.0   0.0    0.0   0.0 0.0  0.0      0  0.0
 ma_5_20_test   0.0    0.0    0.0   0.0    0.0   0.0 0.0  0.0      0  0.0
2025-07-30 12:10:10,678 - __main__ - INFO - 并行回测功能测试通过 ✓
2025-07-30 12:10:10,678 - __main__ - INFO - ================================================================================
2025-07-30 12:10:10,679 - __main__ - INFO - 回测系统集成测试结果汇总:
2025-07-30 12:10:10,679 - __main__ - INFO -   data_replay: 通过 ✓
2025-07-30 12:10:10,680 - __main__ - INFO -   backtest_engine: 通过 ✓
2025-07-30 12:10:10,680 - __main__ - INFO -   performance_analyzer: 失败 ✗
2025-07-30 12:10:10,680 - __main__ - INFO -   parallel_backtest: 通过 ✓
2025-07-30 12:10:10,681 - __main__ - ERROR - 
❌ 部分测试失败，请检查错误日志
2025-07-30 12:11:39,701 - __main__ - INFO - 开始回测系统集成测试
2025-07-30 12:11:39,701 - __main__ - INFO - ================================================================================
2025-07-30 12:11:39,702 - __main__ - INFO - ==================================================
2025-07-30 12:11:39,702 - __main__ - INFO - 开始测试历史数据回放功能
2025-07-30 12:11:39,702 - qlib_trading_system.backtest.data_replay - INFO - 历史数据回放系统初始化完成，时间范围：2024-01-01 00:00:00 - 2024-01-10 00:00:00
2025-07-30 12:11:39,703 - qlib_trading_system.backtest.data_replay - INFO - 开始加载历史数据...
2025-07-30 12:11:39,703 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000001.SZ_1d.csv
2025-07-30 12:11:39,703 - qlib_trading_system.backtest.data_replay - INFO - 生成 000001.SZ 的模拟历史数据
2025-07-30 12:11:39,707 - qlib_trading_system.backtest.data_replay - WARNING - 数据中没有close列，无法计算技术指标
2025-07-30 12:11:39,708 - qlib_trading_system.backtest.data_replay - INFO - 成功生成 000001.SZ 的模拟数据，共 0 条记录
2025-07-30 12:11:39,709 - qlib_trading_system.backtest.data_replay - WARNING - 未找到 000001.SZ 的历史数据
2025-07-30 12:11:39,710 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000002.SZ_1d.csv
2025-07-30 12:11:39,710 - qlib_trading_system.backtest.data_replay - INFO - 生成 000002.SZ 的模拟历史数据
2025-07-30 12:11:39,712 - qlib_trading_system.backtest.data_replay - WARNING - 数据中没有close列，无法计算技术指标
2025-07-30 12:11:39,713 - qlib_trading_system.backtest.data_replay - INFO - 成功生成 000002.SZ 的模拟数据，共 0 条记录
2025-07-30 12:11:39,713 - qlib_trading_system.backtest.data_replay - WARNING - 未找到 000002.SZ 的历史数据
2025-07-30 12:11:39,713 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000858.SZ_1d.csv
2025-07-30 12:11:39,714 - qlib_trading_system.backtest.data_replay - INFO - 生成 000858.SZ 的模拟历史数据
2025-07-30 12:11:39,717 - qlib_trading_system.backtest.data_replay - WARNING - 数据中没有close列，无法计算技术指标
2025-07-30 12:11:39,717 - qlib_trading_system.backtest.data_replay - INFO - 成功生成 000858.SZ 的模拟数据，共 0 条记录
2025-07-30 12:11:39,717 - qlib_trading_system.backtest.data_replay - WARNING - 未找到 000858.SZ 的历史数据
2025-07-30 12:11:39,718 - qlib_trading_system.backtest.data_replay - INFO - 历史数据加载完成，共加载 0 只股票
2025-07-30 12:11:39,718 - __main__ - INFO - 成功加载 0 只股票的数据
2025-07-30 12:11:39,718 - qlib_trading_system.backtest.data_replay - INFO - 开始加载历史数据...
2025-07-30 12:11:39,718 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000001.SZ_1d.csv
2025-07-30 12:11:39,719 - qlib_trading_system.backtest.data_replay - INFO - 生成 000001.SZ 的模拟历史数据
2025-07-30 12:11:39,721 - qlib_trading_system.backtest.data_replay - WARNING - 数据中没有close列，无法计算技术指标
2025-07-30 12:11:39,721 - qlib_trading_system.backtest.data_replay - INFO - 成功生成 000001.SZ 的模拟数据，共 0 条记录
2025-07-30 12:11:39,722 - qlib_trading_system.backtest.data_replay - WARNING - 未找到 000001.SZ 的历史数据
2025-07-30 12:11:39,722 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000002.SZ_1d.csv
2025-07-30 12:11:39,722 - qlib_trading_system.backtest.data_replay - INFO - 生成 000002.SZ 的模拟历史数据
2025-07-30 12:11:39,725 - qlib_trading_system.backtest.data_replay - WARNING - 数据中没有close列，无法计算技术指标
2025-07-30 12:11:39,725 - qlib_trading_system.backtest.data_replay - INFO - 成功生成 000002.SZ 的模拟数据，共 0 条记录
2025-07-30 12:11:39,726 - qlib_trading_system.backtest.data_replay - WARNING - 未找到 000002.SZ 的历史数据
2025-07-30 12:11:39,726 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000858.SZ_1d.csv
2025-07-30 12:11:39,726 - qlib_trading_system.backtest.data_replay - INFO - 生成 000858.SZ 的模拟历史数据
2025-07-30 12:11:39,729 - qlib_trading_system.backtest.data_replay - WARNING - 数据中没有close列，无法计算技术指标
2025-07-30 12:11:39,730 - qlib_trading_system.backtest.data_replay - INFO - 成功生成 000858.SZ 的模拟数据，共 0 条记录
2025-07-30 12:11:39,730 - qlib_trading_system.backtest.data_replay - WARNING - 未找到 000858.SZ 的历史数据
2025-07-30 12:11:39,730 - qlib_trading_system.backtest.data_replay - INFO - 历史数据加载完成，共加载 0 只股票
2025-07-30 12:11:39,732 - qlib_trading_system.backtest.data_replay - ERROR - 没有可用的历史数据进行回放
2025-07-30 12:11:39,732 - __main__ - INFO - 数据摘要：{}
2025-07-30 12:11:39,732 - __main__ - INFO - 历史数据回放功能测试通过 ✓
2025-07-30 12:11:39,732 - __main__ - INFO - ==================================================
2025-07-30 12:11:39,733 - __main__ - INFO - 开始测试回测引擎功能
2025-07-30 12:11:39,734 - qlib_trading_system.backtest.data_replay - INFO - 历史数据回放系统初始化完成，时间范围：2024-01-01 00:00:00 - 2024-01-31 00:00:00
2025-07-30 12:11:39,734 - qlib_trading_system.backtest.backtest_engine - INFO - 回测引擎初始化完成，初始资金：1,000,000.00
2025-07-30 12:11:39,735 - qlib_trading_system.backtest.backtest_engine - INFO - 数据回放器设置完成
2025-07-30 12:11:39,736 - __main__ - INFO - 初始化测试MA策略：test_ma_5_20, 短期=5, 长期=20
2025-07-30 12:11:39,736 - qlib_trading_system.backtest.backtest_engine - INFO - 添加策略：test_ma_5_20
2025-07-30 12:11:39,737 - __main__ - INFO - 开始运行回测...
2025-07-30 12:11:39,738 - qlib_trading_system.backtest.backtest_engine - INFO - 开始运行回测...
2025-07-30 12:11:39,738 - qlib_trading_system.backtest.data_replay - INFO - 开始加载历史数据...
2025-07-30 12:11:39,739 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000001.SZ_1d.csv
2025-07-30 12:11:39,739 - qlib_trading_system.backtest.data_replay - INFO - 生成 000001.SZ 的模拟历史数据
2025-07-30 12:11:39,745 - qlib_trading_system.backtest.data_replay - WARNING - 数据中没有close列，无法计算技术指标
2025-07-30 12:11:39,746 - qlib_trading_system.backtest.data_replay - INFO - 成功生成 000001.SZ 的模拟数据，共 0 条记录
2025-07-30 12:11:39,747 - qlib_trading_system.backtest.data_replay - WARNING - 未找到 000001.SZ 的历史数据
2025-07-30 12:11:39,748 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000002.SZ_1d.csv
2025-07-30 12:11:39,748 - qlib_trading_system.backtest.data_replay - INFO - 生成 000002.SZ 的模拟历史数据
2025-07-30 12:11:39,751 - qlib_trading_system.backtest.data_replay - WARNING - 数据中没有close列，无法计算技术指标
2025-07-30 12:11:39,752 - qlib_trading_system.backtest.data_replay - INFO - 成功生成 000002.SZ 的模拟数据，共 0 条记录
2025-07-30 12:11:39,752 - qlib_trading_system.backtest.data_replay - WARNING - 未找到 000002.SZ 的历史数据
2025-07-30 12:11:39,752 - qlib_trading_system.backtest.data_replay - INFO - 历史数据加载完成，共加载 0 只股票
2025-07-30 12:11:39,753 - qlib_trading_system.backtest.data_replay - ERROR - 没有可用的历史数据进行回放
2025-07-30 12:11:39,753 - qlib_trading_system.backtest.backtest_engine - INFO - 回测完成
2025-07-30 12:11:39,753 - __main__ - INFO - 回测结果:
2025-07-30 12:11:39,753 - __main__ - INFO -   初始资金: 0.00
2025-07-30 12:11:39,754 - __main__ - INFO -   最终价值: 0.00
2025-07-30 12:11:39,754 - __main__ - INFO -   总收益率: 0.00%
2025-07-30 12:11:39,754 - __main__ - INFO -   年化收益率: 0.00%
2025-07-30 12:11:39,754 - __main__ - INFO -   夏普比率: 0.000
2025-07-30 12:11:39,755 - __main__ - INFO -   最大回撤: 0.00%
2025-07-30 12:11:39,755 - __main__ - INFO -   总交易次数: 0
2025-07-30 12:11:39,755 - __main__ - INFO -   胜率: 0.00%
2025-07-30 12:11:39,755 - __main__ - INFO - 业绩摘要:
2025-07-30 12:11:39,756 - __main__ - INFO - 尚未运行回测
2025-07-30 12:11:39,758 - __main__ - INFO - 回测引擎功能测试通过 ✓
2025-07-30 12:11:39,758 - __main__ - INFO - ==================================================
2025-07-30 12:11:39,759 - __main__ - INFO - 开始测试性能分析功能
2025-07-30 12:11:39,759 - __main__ - WARNING - 没有回测结果，跳过性能分析测试
2025-07-30 12:11:39,759 - __main__ - INFO - ==================================================
2025-07-30 12:11:39,759 - __main__ - INFO - 开始测试并行回测功能
2025-07-30 12:11:39,760 - qlib_trading_system.backtest.parallel_backtest - INFO - 并行回测框架初始化完成，将使用 2 个工作进程
2025-07-30 12:11:39,761 - __main__ - INFO - 开始运行并行回测...
2025-07-30 12:11:39,761 - qlib_trading_system.backtest.parallel_backtest - INFO - 开始并行回测，共 3 个策略
2025-07-30 12:11:39,764 - qlib_trading_system.backtest.parallel_backtest - INFO - 使用多线程模式执行回测
2025-07-30 12:11:39,815 - qlib_trading_system.backtest.data_replay - INFO - 历史数据回放系统初始化完成，时间范围：2024-01-01 00:00:00 - 2024-01-31 00:00:00
2025-07-30 12:11:39,818 - qlib_trading_system.backtest.backtest_engine - INFO - 回测引擎初始化完成，初始资金：1,000,000.00
2025-07-30 12:11:39,821 - qlib_trading_system.backtest.backtest_engine - INFO - 数据回放器设置完成
2025-07-30 12:11:39,823 - __main__ - INFO - 初始化测试买入持有策略：buy_hold_test, 目标股票=['000001.SZ', '000002.SZ']
2025-07-30 12:11:39,827 - qlib_trading_system.backtest.backtest_engine - INFO - 添加策略：buy_hold_test
2025-07-30 12:11:39,829 - qlib_trading_system.backtest.backtest_engine - INFO - 开始运行回测...
2025-07-30 12:11:39,831 - qlib_trading_system.backtest.data_replay - INFO - 开始加载历史数据...
2025-07-30 12:11:39,835 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000001.SZ_1d.csv
2025-07-30 12:11:39,838 - qlib_trading_system.backtest.data_replay - INFO - 生成 000001.SZ 的模拟历史数据
2025-07-30 12:11:39,847 - qlib_trading_system.backtest.data_replay - WARNING - 数据中没有close列，无法计算技术指标
2025-07-30 12:11:39,847 - qlib_trading_system.backtest.data_replay - INFO - 历史数据回放系统初始化完成，时间范围：2024-01-01 00:00:00 - 2024-01-31 00:00:00
2025-07-30 12:11:39,847 - qlib_trading_system.backtest.data_replay - INFO - 成功生成 000001.SZ 的模拟数据，共 0 条记录
2025-07-30 12:11:39,848 - qlib_trading_system.backtest.backtest_engine - INFO - 回测引擎初始化完成，初始资金：1,000,000.00
2025-07-30 12:11:39,848 - qlib_trading_system.backtest.data_replay - WARNING - 未找到 000001.SZ 的历史数据
2025-07-30 12:11:39,849 - qlib_trading_system.backtest.backtest_engine - INFO - 数据回放器设置完成
2025-07-30 12:11:39,850 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000002.SZ_1d.csv
2025-07-30 12:11:39,850 - __main__ - INFO - 初始化测试MA策略：ma_5_10_test, 短期=5, 长期=10
2025-07-30 12:11:39,851 - qlib_trading_system.backtest.data_replay - INFO - 生成 000002.SZ 的模拟历史数据
2025-07-30 12:11:39,851 - qlib_trading_system.backtest.backtest_engine - INFO - 添加策略：ma_5_10_test
2025-07-30 12:11:39,856 - qlib_trading_system.backtest.data_replay - WARNING - 数据中没有close列，无法计算技术指标
2025-07-30 12:11:39,857 - qlib_trading_system.backtest.backtest_engine - INFO - 开始运行回测...
2025-07-30 12:11:39,858 - qlib_trading_system.backtest.data_replay - INFO - 成功生成 000002.SZ 的模拟数据，共 0 条记录
2025-07-30 12:11:39,860 - qlib_trading_system.backtest.data_replay - INFO - 开始加载历史数据...
2025-07-30 12:11:39,865 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000001.SZ_1d.csv
2025-07-30 12:11:39,866 - qlib_trading_system.backtest.data_replay - WARNING - 未找到 000002.SZ 的历史数据
2025-07-30 12:11:39,866 - qlib_trading_system.backtest.data_replay - INFO - 生成 000001.SZ 的模拟历史数据
2025-07-30 12:11:39,867 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000858.SZ_1d.csv
2025-07-30 12:11:39,872 - qlib_trading_system.backtest.data_replay - WARNING - 数据中没有close列，无法计算技术指标
2025-07-30 12:11:39,872 - qlib_trading_system.backtest.data_replay - INFO - 生成 000858.SZ 的模拟历史数据
2025-07-30 12:11:39,872 - qlib_trading_system.backtest.data_replay - INFO - 成功生成 000001.SZ 的模拟数据，共 0 条记录
2025-07-30 12:11:39,878 - qlib_trading_system.backtest.data_replay - WARNING - 数据中没有close列，无法计算技术指标
2025-07-30 12:11:39,879 - qlib_trading_system.backtest.data_replay - WARNING - 未找到 000001.SZ 的历史数据
2025-07-30 12:11:39,879 - qlib_trading_system.backtest.data_replay - INFO - 成功生成 000858.SZ 的模拟数据，共 0 条记录
2025-07-30 12:11:39,880 - qlib_trading_system.backtest.data_replay - WARNING - 未找到 000858.SZ 的历史数据
2025-07-30 12:11:39,881 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000002.SZ_1d.csv
2025-07-30 12:11:39,881 - qlib_trading_system.backtest.data_replay - INFO - 历史数据加载完成，共加载 0 只股票
2025-07-30 12:11:39,881 - qlib_trading_system.backtest.data_replay - INFO - 生成 000002.SZ 的模拟历史数据
2025-07-30 12:11:39,882 - qlib_trading_system.backtest.data_replay - ERROR - 没有可用的历史数据进行回放
2025-07-30 12:11:39,886 - qlib_trading_system.backtest.data_replay - WARNING - 数据中没有close列，无法计算技术指标
2025-07-30 12:11:39,886 - qlib_trading_system.backtest.backtest_engine - INFO - 回测完成
2025-07-30 12:11:39,886 - qlib_trading_system.backtest.data_replay - INFO - 成功生成 000002.SZ 的模拟数据，共 0 条记录
2025-07-30 12:11:39,887 - qlib_trading_system.backtest.parallel_backtest - INFO - 策略 buy_hold_test 回测完成
2025-07-30 12:11:39,887 - qlib_trading_system.backtest.data_replay - INFO - 历史数据回放系统初始化完成，时间范围：2024-01-01 00:00:00 - 2024-01-31 00:00:00
2025-07-30 12:11:39,887 - qlib_trading_system.backtest.data_replay - WARNING - 未找到 000002.SZ 的历史数据
2025-07-30 12:11:39,889 - qlib_trading_system.backtest.backtest_engine - INFO - 回测引擎初始化完成，初始资金：1,000,000.00
2025-07-30 12:11:39,889 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000858.SZ_1d.csv
2025-07-30 12:11:39,889 - qlib_trading_system.backtest.backtest_engine - INFO - 数据回放器设置完成
2025-07-30 12:11:39,889 - qlib_trading_system.backtest.data_replay - INFO - 生成 000858.SZ 的模拟历史数据
2025-07-30 12:11:39,890 - __main__ - INFO - 初始化测试MA策略：ma_5_20_test, 短期=5, 长期=20
2025-07-30 12:11:39,893 - qlib_trading_system.backtest.data_replay - WARNING - 数据中没有close列，无法计算技术指标
2025-07-30 12:11:39,894 - qlib_trading_system.backtest.backtest_engine - INFO - 添加策略：ma_5_20_test
2025-07-30 12:11:39,895 - qlib_trading_system.backtest.backtest_engine - INFO - 开始运行回测...
2025-07-30 12:11:39,896 - qlib_trading_system.backtest.data_replay - INFO - 成功生成 000858.SZ 的模拟数据，共 0 条记录
2025-07-30 12:11:39,896 - qlib_trading_system.backtest.data_replay - WARNING - 未找到 000858.SZ 的历史数据
2025-07-30 12:11:39,897 - qlib_trading_system.backtest.data_replay - INFO - 开始加载历史数据...
2025-07-30 12:11:39,897 - qlib_trading_system.backtest.data_replay - INFO - 历史数据加载完成，共加载 0 只股票
2025-07-30 12:11:39,897 - qlib_trading_system.backtest.data_replay - ERROR - 没有可用的历史数据进行回放
2025-07-30 12:11:39,897 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000001.SZ_1d.csv
2025-07-30 12:11:39,898 - qlib_trading_system.backtest.backtest_engine - INFO - 回测完成
2025-07-30 12:11:39,898 - qlib_trading_system.backtest.data_replay - INFO - 生成 000001.SZ 的模拟历史数据
2025-07-30 12:11:39,898 - qlib_trading_system.backtest.parallel_backtest - INFO - 策略 ma_5_10_test 回测完成
2025-07-30 12:11:39,901 - qlib_trading_system.backtest.data_replay - WARNING - 数据中没有close列，无法计算技术指标
2025-07-30 12:11:39,902 - qlib_trading_system.backtest.data_replay - INFO - 成功生成 000001.SZ 的模拟数据，共 0 条记录
2025-07-30 12:11:39,902 - qlib_trading_system.backtest.data_replay - WARNING - 未找到 000001.SZ 的历史数据
2025-07-30 12:11:39,903 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000002.SZ_1d.csv
2025-07-30 12:11:39,903 - qlib_trading_system.backtest.data_replay - INFO - 生成 000002.SZ 的模拟历史数据
2025-07-30 12:11:39,906 - qlib_trading_system.backtest.data_replay - WARNING - 数据中没有close列，无法计算技术指标
2025-07-30 12:11:39,906 - qlib_trading_system.backtest.data_replay - INFO - 成功生成 000002.SZ 的模拟数据，共 0 条记录
2025-07-30 12:11:39,907 - qlib_trading_system.backtest.data_replay - WARNING - 未找到 000002.SZ 的历史数据
2025-07-30 12:11:39,907 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000858.SZ_1d.csv
2025-07-30 12:11:39,908 - qlib_trading_system.backtest.data_replay - INFO - 生成 000858.SZ 的模拟历史数据
2025-07-30 12:11:39,912 - qlib_trading_system.backtest.data_replay - WARNING - 数据中没有close列，无法计算技术指标
2025-07-30 12:11:39,914 - qlib_trading_system.backtest.data_replay - INFO - 成功生成 000858.SZ 的模拟数据，共 0 条记录
2025-07-30 12:11:39,914 - qlib_trading_system.backtest.data_replay - WARNING - 未找到 000858.SZ 的历史数据
2025-07-30 12:11:39,914 - qlib_trading_system.backtest.data_replay - INFO - 历史数据加载完成，共加载 0 只股票
2025-07-30 12:11:39,914 - qlib_trading_system.backtest.data_replay - ERROR - 没有可用的历史数据进行回放
2025-07-30 12:11:39,915 - qlib_trading_system.backtest.backtest_engine - INFO - 回测完成
2025-07-30 12:11:39,915 - qlib_trading_system.backtest.parallel_backtest - INFO - 策略 ma_5_20_test 回测完成
2025-07-30 12:11:39,915 - qlib_trading_system.backtest.parallel_backtest - INFO - 计算所有策略的性能指标
2025-07-30 12:11:39,917 - qlib_trading_system.backtest.performance_analyzer - INFO - 回测结果分析器初始化完成
2025-07-30 12:11:39,918 - qlib_trading_system.backtest.performance_analyzer - WARNING - 权益曲线数据为空，无法计算指标
2025-07-30 12:11:39,919 - qlib_trading_system.backtest.performance_analyzer - INFO - 回测结果分析器初始化完成
2025-07-30 12:11:39,919 - qlib_trading_system.backtest.performance_analyzer - WARNING - 权益曲线数据为空，无法计算指标
2025-07-30 12:11:39,920 - qlib_trading_system.backtest.performance_analyzer - INFO - 回测结果分析器初始化完成
2025-07-30 12:11:39,921 - qlib_trading_system.backtest.performance_analyzer - WARNING - 权益曲线数据为空，无法计算指标
2025-07-30 12:11:39,921 - qlib_trading_system.backtest.parallel_backtest - INFO - 保存回测结果到文件
2025-07-30 12:11:39,925 - qlib_trading_system.backtest.performance_analyzer - INFO - 回测结果分析器初始化完成
2025-07-30 12:11:39,925 - qlib_trading_system.backtest.performance_analyzer - INFO - 导出回测结果到Excel：test_parallel_results\buy_hold_test_20250730_121139.xlsx
2025-07-30 12:11:40,309 - qlib_trading_system.backtest.performance_analyzer - WARNING - 权益曲线数据为空，无法计算指标
2025-07-30 12:11:40,331 - qlib_trading_system.backtest.performance_analyzer - INFO - Excel导出完成
2025-07-30 12:11:40,332 - qlib_trading_system.backtest.performance_analyzer - INFO - 回测结果分析器初始化完成
2025-07-30 12:11:40,332 - qlib_trading_system.backtest.performance_analyzer - INFO - 导出回测结果到Excel：test_parallel_results\ma_5_10_test_20250730_121139.xlsx
2025-07-30 12:11:40,335 - qlib_trading_system.backtest.performance_analyzer - WARNING - 权益曲线数据为空，无法计算指标
2025-07-30 12:11:40,395 - qlib_trading_system.backtest.performance_analyzer - INFO - Excel导出完成
2025-07-30 12:11:40,398 - qlib_trading_system.backtest.performance_analyzer - INFO - 回测结果分析器初始化完成
2025-07-30 12:11:40,398 - qlib_trading_system.backtest.performance_analyzer - INFO - 导出回测结果到Excel：test_parallel_results\ma_5_20_test_20250730_121139.xlsx
2025-07-30 12:11:40,401 - qlib_trading_system.backtest.performance_analyzer - WARNING - 权益曲线数据为空，无法计算指标
2025-07-30 12:11:40,414 - qlib_trading_system.backtest.performance_analyzer - INFO - Excel导出完成
2025-07-30 12:11:40,415 - qlib_trading_system.backtest.parallel_backtest - INFO - 生成策略对比分析
2025-07-30 12:11:40,458 - qlib_trading_system.backtest.parallel_backtest - INFO - 策略对比报告已保存到：test_parallel_results\comparison_report_20250730_121140.md
2025-07-30 12:11:40,458 - qlib_trading_system.backtest.parallel_backtest - INFO - 并行回测完成，耗时 0.69 秒
2025-07-30 12:11:40,459 - __main__ - INFO - 并行回测完成，共 3 个策略
2025-07-30 12:11:40,459 - __main__ - INFO - 策略 buy_hold_test:
2025-07-30 12:11:40,459 - __main__ - INFO -   总收益率: 0.00%
2025-07-30 12:11:40,459 - __main__ - INFO -   年化收益率: 0.00%
2025-07-30 12:11:40,461 - __main__ - INFO -   夏普比率: 0.000
2025-07-30 12:11:40,461 - __main__ - INFO - 策略 ma_5_10_test:
2025-07-30 12:11:40,461 - __main__ - INFO -   总收益率: 0.00%
2025-07-30 12:11:40,462 - __main__ - INFO -   年化收益率: 0.00%
2025-07-30 12:11:40,462 - __main__ - INFO -   夏普比率: 0.000
2025-07-30 12:11:40,462 - __main__ - INFO - 策略 ma_5_20_test:
2025-07-30 12:11:40,463 - __main__ - INFO -   总收益率: 0.00%
2025-07-30 12:11:40,463 - __main__ - INFO -   年化收益率: 0.00%
2025-07-30 12:11:40,463 - __main__ - INFO -   夏普比率: 0.000
2025-07-30 12:11:40,464 - __main__ - INFO - 
最佳策略排名:
2025-07-30 12:11:40,464 - __main__ - INFO - 1. buy_hold_test: 年化收益率=0.00%, 夏普比率=0.000
2025-07-30 12:11:40,464 - __main__ - INFO - 2. ma_5_10_test: 年化收益率=0.00%, 夏普比率=0.000
2025-07-30 12:11:40,466 - __main__ - INFO - 
策略对比矩阵:
2025-07-30 12:11:40,472 - __main__ - INFO -          策略ID  总收益率  年化收益率  年化波动率  夏普比率  索提诺比率  最大回撤  胜率  盈亏比  总交易次数  换手率
buy_hold_test   0.0    0.0    0.0   0.0    0.0   0.0 0.0  0.0      0  0.0
 ma_5_10_test   0.0    0.0    0.0   0.0    0.0   0.0 0.0  0.0      0  0.0
 ma_5_20_test   0.0    0.0    0.0   0.0    0.0   0.0 0.0  0.0      0  0.0
2025-07-30 12:11:40,472 - __main__ - INFO - 并行回测功能测试通过 ✓
2025-07-30 12:11:40,473 - __main__ - INFO - ================================================================================
2025-07-30 12:11:40,473 - __main__ - INFO - 回测系统集成测试结果汇总:
2025-07-30 12:11:40,473 - __main__ - INFO -   data_replay: 通过 ✓
2025-07-30 12:11:40,474 - __main__ - INFO -   backtest_engine: 通过 ✓
2025-07-30 12:11:40,474 - __main__ - INFO -   performance_analyzer: 失败 ✗
2025-07-30 12:11:40,474 - __main__ - INFO -   parallel_backtest: 通过 ✓
2025-07-30 12:11:40,476 - __main__ - ERROR - 
❌ 部分测试失败，请检查错误日志
2025-07-30 12:13:16,310 - __main__ - INFO - 开始回测系统集成测试
2025-07-30 12:13:16,310 - __main__ - INFO - ================================================================================
2025-07-30 12:13:16,311 - __main__ - INFO - ==================================================
2025-07-30 12:13:16,311 - __main__ - INFO - 开始测试历史数据回放功能
2025-07-30 12:13:16,312 - qlib_trading_system.backtest.data_replay - INFO - 历史数据回放系统初始化完成，时间范围：2024-01-01 00:00:00 - 2024-01-10 00:00:00
2025-07-30 12:13:16,312 - qlib_trading_system.backtest.data_replay - INFO - 开始加载历史数据...
2025-07-30 12:13:16,312 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000001.SZ_1d.csv
2025-07-30 12:13:16,313 - qlib_trading_system.backtest.data_replay - INFO - 生成 000001.SZ 的模拟历史数据
2025-07-30 12:13:16,328 - qlib_trading_system.backtest.data_replay - INFO - 成功生成 000001.SZ 的模拟数据，共 8 条记录
2025-07-30 12:13:16,329 - qlib_trading_system.backtest.data_replay - INFO - 成功加载 000001.SZ 数据，共 8 条记录
2025-07-30 12:13:16,329 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000002.SZ_1d.csv
2025-07-30 12:13:16,330 - qlib_trading_system.backtest.data_replay - INFO - 生成 000002.SZ 的模拟历史数据
2025-07-30 12:13:16,338 - qlib_trading_system.backtest.data_replay - INFO - 成功生成 000002.SZ 的模拟数据，共 8 条记录
2025-07-30 12:13:16,339 - qlib_trading_system.backtest.data_replay - INFO - 成功加载 000002.SZ 数据，共 8 条记录
2025-07-30 12:13:16,339 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000858.SZ_1d.csv
2025-07-30 12:13:16,339 - qlib_trading_system.backtest.data_replay - INFO - 生成 000858.SZ 的模拟历史数据
2025-07-30 12:13:16,351 - qlib_trading_system.backtest.data_replay - INFO - 成功生成 000858.SZ 的模拟数据，共 8 条记录
2025-07-30 12:13:16,351 - qlib_trading_system.backtest.data_replay - INFO - 成功加载 000858.SZ 数据，共 8 条记录
2025-07-30 12:13:16,352 - qlib_trading_system.backtest.data_replay - INFO - 历史数据加载完成，共加载 3 只股票
2025-07-30 12:13:16,352 - __main__ - INFO - 成功加载 3 只股票的数据
2025-07-30 12:13:16,352 - qlib_trading_system.backtest.data_replay - INFO - 开始历史数据回放...
2025-07-30 12:13:16,358 - __main__ - INFO - 时间：2024-01-01 15:00:00
2025-07-30 12:13:16,358 - __main__ - INFO -   000001.SZ: 开=19.89, 高=19.97, 低=19.81, 收=19.89, 量=5245198
2025-07-30 12:13:16,358 - __main__ - INFO -     技术指标: macd=0.00, macd_signal=0.00, macd_hist=0.00
2025-07-30 12:13:16,359 - __main__ - INFO -   000002.SZ: 开=10.78, 高=10.81, 低=10.74, 收=10.78, 量=7753031
2025-07-30 12:13:16,359 - __main__ - INFO -     技术指标: macd=0.00, macd_signal=0.00, macd_hist=0.00
2025-07-30 12:13:16,359 - __main__ - INFO -   000858.SZ: 开=10.55, 高=10.60, 低=10.50, 收=10.55, 量=2793235
2025-07-30 12:13:16,360 - __main__ - INFO -     技术指标: macd=0.00, macd_signal=0.00, macd_hist=0.00
2025-07-30 12:13:16,361 - __main__ - INFO - 时间：2024-01-02 15:00:00
2025-07-30 12:13:16,361 - __main__ - INFO -   000001.SZ: 开=19.89, 高=20.32, 低=19.89, 收=20.18, 量=5829562
2025-07-30 12:13:16,361 - __main__ - INFO -     技术指标: macd=0.01, macd_signal=0.00, macd_hist=0.00
2025-07-30 12:13:16,362 - __main__ - INFO -   000002.SZ: 开=10.78, 高=10.78, 低=10.57, 收=10.60, 量=1688151
2025-07-30 12:13:16,362 - __main__ - INFO -     技术指标: macd=-0.00, macd_signal=-0.00, macd_hist=-0.00
2025-07-30 12:13:16,362 - __main__ - INFO -   000858.SZ: 开=10.55, 高=10.69, 低=10.55, 收=10.69, 量=2841344
2025-07-30 12:13:16,362 - __main__ - INFO -     技术指标: macd=0.00, macd_signal=0.00, macd_hist=0.00
2025-07-30 12:13:16,364 - __main__ - INFO - 时间：2024-01-03 15:00:00
2025-07-30 12:13:16,364 - __main__ - INFO -   000001.SZ: 开=20.18, 高=20.66, 低=20.18, 收=20.59, 量=6190904
2025-07-30 12:13:16,365 - __main__ - INFO -     技术指标: macd=0.02, macd_signal=0.01, macd_hist=0.01
2025-07-30 12:13:16,365 - __main__ - INFO -   000002.SZ: 开=10.60, 高=10.83, 低=10.60, 收=10.81, 量=1858489
2025-07-30 12:13:16,365 - __main__ - INFO -     技术指标: macd=0.00, macd_signal=-0.00, macd_hist=0.00
2025-07-30 12:13:16,366 - __main__ - INFO -   000858.SZ: 开=10.69, 高=10.94, 低=10.69, 收=10.94, 量=1516162
2025-07-30 12:13:16,366 - __main__ - INFO -     技术指标: macd=0.01, macd_signal=0.01, macd_hist=0.01
2025-07-30 12:13:16,369 - __main__ - INFO - 时间：2024-01-04 15:00:00
2025-07-30 12:13:16,369 - __main__ - INFO -   000001.SZ: 开=20.59, 高=21.33, 低=20.59, 收=21.32, 量=5151307
2025-07-30 12:13:16,370 - __main__ - INFO -     技术指标: macd=0.05, macd_signal=0.03, macd_hist=0.03
2025-07-30 12:13:16,370 - __main__ - INFO -   000002.SZ: 开=10.81, 高=11.20, 低=10.81, 收=11.19, 量=5909724
2025-07-30 12:13:16,370 - __main__ - INFO -     技术指标: macd=0.02, macd_signal=0.01, macd_hist=0.01
2025-07-30 12:13:16,370 - __main__ - INFO -   000858.SZ: 开=10.94, 高=10.94, 低=10.36, 收=10.44, 量=8185226
2025-07-30 12:13:16,370 - __main__ - INFO -     技术指标: macd=-0.00, macd_signal=0.00, macd_hist=-0.01
2025-07-30 12:13:16,372 - __main__ - INFO - 时间：2024-01-05 15:00:00
2025-07-30 12:13:16,373 - __main__ - INFO -   000001.SZ: 开=21.32, 高=21.32, 低=20.66, 收=20.74, 量=6131492
2025-07-30 12:13:16,374 - __main__ - INFO -     技术指标: ma5=20.54, macd=0.05, macd_signal=0.03
2025-07-30 12:13:16,375 - __main__ - INFO -   000002.SZ: 开=11.19, 高=11.19, 低=11.01, 收=11.09, 量=3316967
2025-07-30 12:13:16,375 - __main__ - INFO -     技术指标: ma5=10.89, macd=0.02, macd_signal=0.01
2025-07-30 12:13:16,375 - __main__ - INFO -   000858.SZ: 开=10.44, 高=10.68, 低=10.44, 收=10.62, 量=6923389
2025-07-30 12:13:16,375 - __main__ - INFO -     技术指标: ma5=10.65, macd=-0.00, macd_signal=0.00
2025-07-30 12:13:16,376 - __main__ - INFO - 数据摘要：{
  "symbols": [
    "000001.SZ",
    "000002.SZ",
    "000858.SZ"
  ],
  "symbol_count": 3,
  "date_range": {
    "start": "2024-01-01 00:00:00",
    "end": "2024-01-10 00:00:00"
  },
  "frequency": "1d",
  "data_points": {
    "000001.SZ": 8,
    "000002.SZ": 8,
    "000858.SZ": 8
  }
}
2025-07-30 12:13:16,377 - __main__ - INFO - 历史数据回放功能测试通过 ✓
2025-07-30 12:13:16,377 - __main__ - INFO - ==================================================
2025-07-30 12:13:16,378 - __main__ - INFO - 开始测试回测引擎功能
2025-07-30 12:13:16,378 - qlib_trading_system.backtest.data_replay - INFO - 历史数据回放系统初始化完成，时间范围：2024-01-01 00:00:00 - 2024-01-31 00:00:00
2025-07-30 12:13:16,380 - qlib_trading_system.backtest.backtest_engine - INFO - 回测引擎初始化完成，初始资金：1,000,000.00
2025-07-30 12:13:16,380 - qlib_trading_system.backtest.backtest_engine - INFO - 数据回放器设置完成
2025-07-30 12:13:16,380 - __main__ - INFO - 初始化测试MA策略：test_ma_5_20, 短期=5, 长期=20
2025-07-30 12:13:16,381 - qlib_trading_system.backtest.backtest_engine - INFO - 添加策略：test_ma_5_20
2025-07-30 12:13:16,381 - __main__ - INFO - 开始运行回测...
2025-07-30 12:13:16,381 - qlib_trading_system.backtest.backtest_engine - INFO - 开始运行回测...
2025-07-30 12:13:16,381 - qlib_trading_system.backtest.data_replay - INFO - 开始加载历史数据...
2025-07-30 12:13:16,382 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000001.SZ_1d.csv
2025-07-30 12:13:16,382 - qlib_trading_system.backtest.data_replay - INFO - 生成 000001.SZ 的模拟历史数据
2025-07-30 12:13:16,395 - qlib_trading_system.backtest.data_replay - INFO - 成功生成 000001.SZ 的模拟数据，共 23 条记录
2025-07-30 12:13:16,395 - qlib_trading_system.backtest.data_replay - INFO - 成功加载 000001.SZ 数据，共 23 条记录
2025-07-30 12:13:16,396 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000002.SZ_1d.csv
2025-07-30 12:13:16,396 - qlib_trading_system.backtest.data_replay - INFO - 生成 000002.SZ 的模拟历史数据
2025-07-30 12:13:16,406 - qlib_trading_system.backtest.data_replay - INFO - 成功生成 000002.SZ 的模拟数据，共 23 条记录
2025-07-30 12:13:16,406 - qlib_trading_system.backtest.data_replay - INFO - 成功加载 000002.SZ 数据，共 23 条记录
2025-07-30 12:13:16,406 - qlib_trading_system.backtest.data_replay - INFO - 历史数据加载完成，共加载 2 只股票
2025-07-30 12:13:16,408 - qlib_trading_system.backtest.data_replay - INFO - 开始历史数据回放...
2025-07-30 12:13:16,426 - qlib_trading_system.backtest.backtest_engine - INFO - 回测完成
2025-07-30 12:13:16,426 - __main__ - INFO - 回测结果:
2025-07-30 12:13:16,426 - __main__ - INFO -   初始资金: 1,000,000.00
2025-07-30 12:13:16,427 - __main__ - INFO -   最终价值: 1,000,000.00
2025-07-30 12:13:16,427 - __main__ - INFO -   总收益率: 0.00%
2025-07-30 12:13:16,427 - __main__ - INFO -   年化收益率: 0.00%
2025-07-30 12:13:16,427 - __main__ - INFO -   夏普比率: 0.000
2025-07-30 12:13:16,428 - __main__ - INFO -   最大回撤: 0.00%
2025-07-30 12:13:16,428 - __main__ - INFO -   总交易次数: 0
2025-07-30 12:13:16,428 - __main__ - INFO -   胜率: 0.00%
2025-07-30 12:13:16,428 - __main__ - INFO - 业绩摘要:
2025-07-30 12:13:16,428 - __main__ - INFO - 回测业绩摘要
====================
初始资金: 1,000,000.00
最终价值: 1,000,000.00
总收益率: 0.00%
年化收益率: 0.00%
年化波动率: 0.00%
夏普比率: 0.00
最大回撤: 0.00%

交易统计
====================
总交易次数: 0
盈利交易: 0
亏损交易: 0
胜率: 0.00%
盈亏比: 0.00
总手续费: 0.00
2025-07-30 12:13:16,429 - __main__ - INFO - 回测引擎功能测试通过 ✓
2025-07-30 12:13:16,429 - __main__ - INFO - ==================================================
2025-07-30 12:13:16,429 - __main__ - INFO - 开始测试性能分析功能
2025-07-30 12:13:16,433 - qlib_trading_system.backtest.performance_analyzer - INFO - 回测结果分析器初始化完成
2025-07-30 12:13:16,434 - qlib_trading_system.backtest.performance_analyzer - INFO - 开始计算综合业绩指标...
2025-07-30 12:13:16,438 - qlib_trading_system.backtest.performance_analyzer - INFO - 综合业绩指标计算完成
2025-07-30 12:13:16,438 - __main__ - INFO - 综合性能指标:
2025-07-30 12:13:16,439 - __main__ - INFO -   总收益率: 0.00%
2025-07-30 12:13:16,440 - __main__ - INFO -   年化收益率: 0.00%
2025-07-30 12:13:16,440 - __main__ - INFO -   年化波动率: 0.00%
2025-07-30 12:13:16,441 - __main__ - INFO -   夏普比率: 0.000
2025-07-30 12:13:16,442 - __main__ - INFO -   索提诺比率: 0.000
2025-07-30 12:13:16,442 - __main__ - INFO -   最大回撤: 0.00%
2025-07-30 12:13:16,443 - __main__ - INFO -   95% VaR: 0.0000%
2025-07-30 12:13:16,443 - __main__ - INFO -   胜率: 0.00%
2025-07-30 12:13:16,443 - __main__ - INFO -   盈亏比: 0.00
2025-07-30 12:13:16,443 - qlib_trading_system.backtest.performance_analyzer - INFO - 生成详细业绩报告...
2025-07-30 12:13:16,444 - qlib_trading_system.backtest.performance_analyzer - INFO - 开始计算综合业绩指标...
2025-07-30 12:13:16,448 - qlib_trading_system.backtest.performance_analyzer - INFO - 综合业绩指标计算完成
2025-07-30 12:13:16,449 - __main__ - INFO - 生成详细报告成功
2025-07-30 12:13:16,450 - __main__ - INFO - 报告已保存到 test_performance_report.md
2025-07-30 12:13:16,450 - qlib_trading_system.backtest.performance_analyzer - INFO - 导出回测结果到Excel：test_backtest_results.xlsx
2025-07-30 12:13:16,820 - qlib_trading_system.backtest.performance_analyzer - INFO - 开始计算综合业绩指标...
2025-07-30 12:13:16,825 - qlib_trading_system.backtest.performance_analyzer - INFO - 综合业绩指标计算完成
2025-07-30 12:13:16,904 - qlib_trading_system.backtest.performance_analyzer - INFO - Excel导出完成
2025-07-30 12:13:16,905 - __main__ - INFO - 结果已导出到 test_backtest_results.xlsx
2025-07-30 12:13:16,905 - qlib_trading_system.backtest.performance_analyzer - INFO - 开始绘制业绩分析图表...
2025-07-30 12:13:53,186 - qlib_trading_system.backtest.performance_analyzer - INFO - 业绩分析图表绘制完成，共生成 2 个图表
2025-07-30 12:13:53,187 - __main__ - INFO - 生成图表: ['equity_curve', 'returns_distribution']
2025-07-30 12:13:53,187 - __main__ - INFO - 性能分析功能测试通过 ✓
2025-07-30 12:13:53,188 - __main__ - INFO - ==================================================
2025-07-30 12:13:53,188 - __main__ - INFO - 开始测试并行回测功能
2025-07-30 12:13:53,189 - qlib_trading_system.backtest.parallel_backtest - INFO - 并行回测框架初始化完成，将使用 2 个工作进程
2025-07-30 12:13:53,190 - __main__ - INFO - 开始运行并行回测...
2025-07-30 12:13:53,191 - qlib_trading_system.backtest.parallel_backtest - INFO - 开始并行回测，共 3 个策略
2025-07-30 12:13:53,191 - qlib_trading_system.backtest.parallel_backtest - INFO - 使用多线程模式执行回测
2025-07-30 12:13:53,194 - qlib_trading_system.backtest.data_replay - INFO - 历史数据回放系统初始化完成，时间范围：2024-01-01 00:00:00 - 2024-01-31 00:00:00
2025-07-30 12:13:53,194 - qlib_trading_system.backtest.backtest_engine - INFO - 回测引擎初始化完成，初始资金：1,000,000.00
2025-07-30 12:13:53,194 - qlib_trading_system.backtest.data_replay - INFO - 历史数据回放系统初始化完成，时间范围：2024-01-01 00:00:00 - 2024-01-31 00:00:00
2025-07-30 12:13:53,195 - qlib_trading_system.backtest.backtest_engine - INFO - 数据回放器设置完成
2025-07-30 12:13:53,195 - qlib_trading_system.backtest.backtest_engine - INFO - 回测引擎初始化完成，初始资金：1,000,000.00
2025-07-30 12:13:53,195 - __main__ - INFO - 初始化测试买入持有策略：buy_hold_test, 目标股票=['000001.SZ', '000002.SZ']
2025-07-30 12:13:53,195 - qlib_trading_system.backtest.backtest_engine - INFO - 数据回放器设置完成
2025-07-30 12:13:53,196 - qlib_trading_system.backtest.backtest_engine - INFO - 添加策略：buy_hold_test
2025-07-30 12:13:53,196 - __main__ - INFO - 初始化测试MA策略：ma_5_10_test, 短期=5, 长期=10
2025-07-30 12:13:53,196 - qlib_trading_system.backtest.backtest_engine - INFO - 开始运行回测...
2025-07-30 12:13:53,197 - qlib_trading_system.backtest.backtest_engine - INFO - 添加策略：ma_5_10_test
2025-07-30 12:13:53,197 - qlib_trading_system.backtest.data_replay - INFO - 开始加载历史数据...
2025-07-30 12:13:53,197 - qlib_trading_system.backtest.backtest_engine - INFO - 开始运行回测...
2025-07-30 12:13:53,198 - qlib_trading_system.backtest.data_replay - INFO - 开始加载历史数据...
2025-07-30 12:13:53,198 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000001.SZ_1d.csv
2025-07-30 12:13:53,198 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000001.SZ_1d.csv
2025-07-30 12:13:53,199 - qlib_trading_system.backtest.data_replay - INFO - 生成 000001.SZ 的模拟历史数据
2025-07-30 12:13:53,199 - qlib_trading_system.backtest.data_replay - INFO - 生成 000001.SZ 的模拟历史数据
2025-07-30 12:13:53,234 - qlib_trading_system.backtest.data_replay - INFO - 成功生成 000001.SZ 的模拟数据，共 23 条记录
2025-07-30 12:13:53,245 - qlib_trading_system.backtest.data_replay - INFO - 成功加载 000001.SZ 数据，共 23 条记录
2025-07-30 12:13:53,248 - qlib_trading_system.backtest.data_replay - INFO - 成功生成 000001.SZ 的模拟数据，共 23 条记录
2025-07-30 12:13:53,249 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000002.SZ_1d.csv
2025-07-30 12:13:53,249 - qlib_trading_system.backtest.data_replay - INFO - 成功加载 000001.SZ 数据，共 23 条记录
2025-07-30 12:13:53,250 - qlib_trading_system.backtest.data_replay - INFO - 生成 000002.SZ 的模拟历史数据
2025-07-30 12:13:53,250 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000002.SZ_1d.csv
2025-07-30 12:13:53,261 - qlib_trading_system.backtest.data_replay - INFO - 生成 000002.SZ 的模拟历史数据
2025-07-30 12:13:53,273 - qlib_trading_system.backtest.data_replay - INFO - 成功生成 000002.SZ 的模拟数据，共 23 条记录
2025-07-30 12:13:53,285 - qlib_trading_system.backtest.data_replay - INFO - 成功加载 000002.SZ 数据，共 23 条记录
2025-07-30 12:13:53,291 - qlib_trading_system.backtest.data_replay - INFO - 成功生成 000002.SZ 的模拟数据，共 23 条记录
2025-07-30 12:13:53,291 - qlib_trading_system.backtest.data_replay - INFO - 成功加载 000002.SZ 数据，共 23 条记录
2025-07-30 12:13:53,291 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000858.SZ_1d.csv
2025-07-30 12:13:53,292 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000858.SZ_1d.csv
2025-07-30 12:13:53,292 - qlib_trading_system.backtest.data_replay - INFO - 生成 000858.SZ 的模拟历史数据
2025-07-30 12:13:53,293 - qlib_trading_system.backtest.data_replay - INFO - 生成 000858.SZ 的模拟历史数据
2025-07-30 12:13:53,305 - qlib_trading_system.backtest.data_replay - INFO - 成功生成 000858.SZ 的模拟数据，共 23 条记录
2025-07-30 12:13:53,320 - qlib_trading_system.backtest.data_replay - INFO - 成功生成 000858.SZ 的模拟数据，共 23 条记录
2025-07-30 12:13:53,320 - qlib_trading_system.backtest.data_replay - INFO - 成功加载 000858.SZ 数据，共 23 条记录
2025-07-30 12:13:53,321 - qlib_trading_system.backtest.data_replay - INFO - 成功加载 000858.SZ 数据，共 23 条记录
2025-07-30 12:13:53,321 - qlib_trading_system.backtest.data_replay - INFO - 历史数据加载完成，共加载 3 只股票
2025-07-30 12:13:53,321 - qlib_trading_system.backtest.data_replay - INFO - 历史数据加载完成，共加载 3 只股票
2025-07-30 12:13:53,322 - qlib_trading_system.backtest.data_replay - INFO - 开始历史数据回放...
2025-07-30 12:13:53,322 - qlib_trading_system.backtest.data_replay - INFO - 开始历史数据回放...
2025-07-30 12:13:53,347 - __main__ - INFO - 买入持有策略买入：000001.SZ
2025-07-30 12:13:53,356 - qlib_trading_system.backtest.backtest_engine - INFO - 回测完成
2025-07-30 12:13:53,356 - __main__ - INFO - 买入持有策略买入：000002.SZ
2025-07-30 12:13:53,357 - qlib_trading_system.backtest.parallel_backtest - INFO - 策略 ma_5_10_test 回测完成
2025-07-30 12:13:53,357 - qlib_trading_system.backtest.data_replay - INFO - 历史数据回放系统初始化完成，时间范围：2024-01-01 00:00:00 - 2024-01-31 00:00:00
2025-07-30 12:13:53,390 - qlib_trading_system.backtest.backtest_engine - INFO - 回测完成
2025-07-30 12:13:53,390 - qlib_trading_system.backtest.backtest_engine - INFO - 回测引擎初始化完成，初始资金：1,000,000.00
2025-07-30 12:13:53,390 - qlib_trading_system.backtest.parallel_backtest - INFO - 策略 buy_hold_test 回测完成
2025-07-30 12:13:53,390 - qlib_trading_system.backtest.backtest_engine - INFO - 数据回放器设置完成
2025-07-30 12:13:53,391 - __main__ - INFO - 初始化测试MA策略：ma_5_20_test, 短期=5, 长期=20
2025-07-30 12:13:53,391 - qlib_trading_system.backtest.backtest_engine - INFO - 添加策略：ma_5_20_test
2025-07-30 12:13:53,391 - qlib_trading_system.backtest.backtest_engine - INFO - 开始运行回测...
2025-07-30 12:13:53,392 - qlib_trading_system.backtest.data_replay - INFO - 开始加载历史数据...
2025-07-30 12:13:53,392 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000001.SZ_1d.csv
2025-07-30 12:13:53,392 - qlib_trading_system.backtest.data_replay - INFO - 生成 000001.SZ 的模拟历史数据
2025-07-30 12:13:53,407 - qlib_trading_system.backtest.data_replay - INFO - 成功生成 000001.SZ 的模拟数据，共 23 条记录
2025-07-30 12:13:53,407 - qlib_trading_system.backtest.data_replay - INFO - 成功加载 000001.SZ 数据，共 23 条记录
2025-07-30 12:13:53,408 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000002.SZ_1d.csv
2025-07-30 12:13:53,408 - qlib_trading_system.backtest.data_replay - INFO - 生成 000002.SZ 的模拟历史数据
2025-07-30 12:13:53,423 - qlib_trading_system.backtest.data_replay - INFO - 成功生成 000002.SZ 的模拟数据，共 23 条记录
2025-07-30 12:13:53,424 - qlib_trading_system.backtest.data_replay - INFO - 成功加载 000002.SZ 数据，共 23 条记录
2025-07-30 12:13:53,424 - qlib_trading_system.backtest.data_replay - WARNING - 本地数据文件不存在：data/processed\000858.SZ_1d.csv
2025-07-30 12:13:53,424 - qlib_trading_system.backtest.data_replay - INFO - 生成 000858.SZ 的模拟历史数据
2025-07-30 12:13:53,446 - qlib_trading_system.backtest.data_replay - INFO - 成功生成 000858.SZ 的模拟数据，共 23 条记录
2025-07-30 12:13:53,447 - qlib_trading_system.backtest.data_replay - INFO - 成功加载 000858.SZ 数据，共 23 条记录
2025-07-30 12:13:53,447 - qlib_trading_system.backtest.data_replay - INFO - 历史数据加载完成，共加载 3 只股票
2025-07-30 12:13:53,447 - qlib_trading_system.backtest.data_replay - INFO - 开始历史数据回放...
2025-07-30 12:13:53,488 - qlib_trading_system.backtest.backtest_engine - INFO - 回测完成
2025-07-30 12:13:53,489 - qlib_trading_system.backtest.parallel_backtest - INFO - 策略 ma_5_20_test 回测完成
2025-07-30 12:13:53,489 - qlib_trading_system.backtest.parallel_backtest - INFO - 计算所有策略的性能指标
2025-07-30 12:13:53,495 - qlib_trading_system.backtest.performance_analyzer - INFO - 回测结果分析器初始化完成
2025-07-30 12:13:53,496 - qlib_trading_system.backtest.performance_analyzer - INFO - 开始计算综合业绩指标...
2025-07-30 12:13:53,501 - qlib_trading_system.backtest.performance_analyzer - INFO - 综合业绩指标计算完成
2025-07-30 12:13:53,508 - qlib_trading_system.backtest.performance_analyzer - INFO - 回测结果分析器初始化完成
2025-07-30 12:13:53,509 - qlib_trading_system.backtest.performance_analyzer - INFO - 开始计算综合业绩指标...
2025-07-30 12:13:53,533 - qlib_trading_system.backtest.performance_analyzer - INFO - 综合业绩指标计算完成
2025-07-30 12:13:53,542 - qlib_trading_system.backtest.performance_analyzer - INFO - 回测结果分析器初始化完成
2025-07-30 12:13:53,543 - qlib_trading_system.backtest.performance_analyzer - INFO - 开始计算综合业绩指标...
2025-07-30 12:13:53,554 - qlib_trading_system.backtest.performance_analyzer - INFO - 综合业绩指标计算完成
2025-07-30 12:13:53,555 - qlib_trading_system.backtest.parallel_backtest - INFO - 保存回测结果到文件
2025-07-30 12:13:53,567 - qlib_trading_system.backtest.performance_analyzer - INFO - 回测结果分析器初始化完成
2025-07-30 12:13:53,567 - qlib_trading_system.backtest.performance_analyzer - INFO - 导出回测结果到Excel：test_parallel_results\ma_5_10_test_20250730_121353.xlsx
2025-07-30 12:13:53,575 - qlib_trading_system.backtest.performance_analyzer - INFO - 开始计算综合业绩指标...
2025-07-30 12:13:53,585 - qlib_trading_system.backtest.performance_analyzer - INFO - 综合业绩指标计算完成
2025-07-30 12:13:53,639 - qlib_trading_system.backtest.performance_analyzer - INFO - Excel导出完成
2025-07-30 12:13:53,649 - qlib_trading_system.backtest.performance_analyzer - INFO - 回测结果分析器初始化完成
2025-07-30 12:13:53,650 - qlib_trading_system.backtest.performance_analyzer - INFO - 导出回测结果到Excel：test_parallel_results\buy_hold_test_20250730_121353.xlsx
2025-07-30 12:13:53,657 - qlib_trading_system.backtest.performance_analyzer - INFO - 开始计算综合业绩指标...
2025-07-30 12:13:53,675 - qlib_trading_system.backtest.performance_analyzer - INFO - 综合业绩指标计算完成
2025-07-30 12:13:53,740 - qlib_trading_system.backtest.performance_analyzer - INFO - Excel导出完成
2025-07-30 12:13:53,750 - qlib_trading_system.backtest.performance_analyzer - INFO - 回测结果分析器初始化完成
2025-07-30 12:13:53,753 - qlib_trading_system.backtest.performance_analyzer - INFO - 导出回测结果到Excel：test_parallel_results\ma_5_20_test_20250730_121353.xlsx
2025-07-30 12:13:53,758 - qlib_trading_system.backtest.performance_analyzer - INFO - 开始计算综合业绩指标...
2025-07-30 12:13:53,768 - qlib_trading_system.backtest.performance_analyzer - INFO - 综合业绩指标计算完成
2025-07-30 12:13:53,816 - qlib_trading_system.backtest.performance_analyzer - INFO - Excel导出完成
2025-07-30 12:13:53,816 - qlib_trading_system.backtest.parallel_backtest - INFO - 生成策略对比分析
2025-07-30 12:13:53,874 - qlib_trading_system.backtest.parallel_backtest - INFO - 策略对比报告已保存到：test_parallel_results\comparison_report_20250730_121353.md
2025-07-30 12:13:53,874 - qlib_trading_system.backtest.parallel_backtest - INFO - 并行回测完成，耗时 0.68 秒
2025-07-30 12:13:53,875 - __main__ - INFO - 并行回测完成，共 3 个策略
2025-07-30 12:13:53,875 - __main__ - INFO - 策略 ma_5_10_test:
2025-07-30 12:13:53,875 - __main__ - INFO -   总收益率: 0.00%
2025-07-30 12:13:53,876 - __main__ - INFO -   年化收益率: 0.00%
2025-07-30 12:13:53,876 - __main__ - INFO -   夏普比率: 0.000
2025-07-30 12:13:53,877 - __main__ - INFO - 策略 buy_hold_test:
2025-07-30 12:13:53,877 - __main__ - INFO -   总收益率: -0.14%
2025-07-30 12:13:53,879 - __main__ - INFO -   年化收益率: -1.54%
2025-07-30 12:13:53,881 - __main__ - INFO -   夏普比率: -3.041
2025-07-30 12:13:53,881 - __main__ - INFO - 策略 ma_5_20_test:
2025-07-30 12:13:53,881 - __main__ - INFO -   总收益率: 0.00%
2025-07-30 12:13:53,882 - __main__ - INFO -   年化收益率: 0.00%
2025-07-30 12:13:53,882 - __main__ - INFO -   夏普比率: 0.000
2025-07-30 12:13:53,882 - __main__ - INFO - 
最佳策略排名:
2025-07-30 12:13:53,883 - __main__ - INFO - 1. ma_5_10_test: 年化收益率=0.00%, 夏普比率=0.000
2025-07-30 12:13:53,883 - __main__ - INFO - 2. ma_5_20_test: 年化收益率=0.00%, 夏普比率=0.000
2025-07-30 12:13:53,885 - __main__ - INFO - 
策略对比矩阵:
2025-07-30 12:13:53,894 - __main__ - INFO -          策略ID     总收益率     年化收益率    年化波动率      夏普比率     索提诺比率      最大回撤  胜率  盈亏比  总交易次数      换手率
 ma_5_10_test  0.00000  0.000000 0.000000  0.000000  0.000000  0.000000 0.0  0.0      0 0.000000
buy_hold_test -0.00142 -0.015446 0.014947 -3.040524 -5.863869 -0.006704 0.0  0.0      2 0.030628
 ma_5_20_test  0.00000  0.000000 0.000000  0.000000  0.000000  0.000000 0.0  0.0      0 0.000000
2025-07-30 12:13:53,895 - __main__ - INFO - 并行回测功能测试通过 ✓
2025-07-30 12:13:53,895 - __main__ - INFO - ================================================================================
2025-07-30 12:13:53,896 - __main__ - INFO - 回测系统集成测试结果汇总:
2025-07-30 12:13:53,896 - __main__ - INFO -   data_replay: 通过 ✓
2025-07-30 12:13:53,897 - __main__ - INFO -   backtest_engine: 通过 ✓
2025-07-30 12:13:54,022 - __main__ - INFO -   performance_analyzer: 通过 ✓
2025-07-30 12:13:54,028 - __main__ - INFO -   parallel_backtest: 通过 ✓
2025-07-30 12:13:54,030 - __main__ - INFO - 
🎉 所有测试通过！回测系统功能正常
2025-07-30 12:13:54,030 - __main__ - INFO - 
系统功能说明:
2025-07-30 12:13:54,031 - __main__ - INFO - 1. ✓ 历史数据回放系统 - 支持多种频率的历史数据回放
2025-07-30 12:13:54,031 - __main__ - INFO - 2. ✓ 策略回测执行引擎 - 完整的订单执行和仓位管理
2025-07-30 12:13:54,032 - __main__ - INFO - 3. ✓ 回测结果分析算法 - 全面的业绩指标计算和报告生成
2025-07-30 12:13:54,041 - __main__ - INFO - 4. ✓ 多策略并行回测框架 - 支持多策略同时回测和对比分析
2025-07-30 12:13:54,045 - __main__ - INFO - 
生成的文件:
2025-07-30 12:13:54,046 - __main__ - INFO - - backtest_system_test.log: 测试日志
2025-07-30 12:13:54,046 - __main__ - INFO - - test_performance_report.md: 性能分析报告
2025-07-30 12:13:54,047 - __main__ - INFO - - test_backtest_results.xlsx: 回测结果Excel文件
2025-07-30 12:13:54,076 - __main__ - INFO - - test_parallel_results/: 并行回测结果目录
