"""
简化的FastAPI启动脚本
Simplified FastAPI Startup Script

启动最基本的FastAPI应用进行测试
"""

import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_simple_app():
    """创建简化的FastAPI应用"""
    from fastapi import FastAPI, WebSocket, WebSocketDisconnect
    from fastapi.middleware.cors import CORSMiddleware
    from fastapi.responses import HTMLResponse
    import json
    
    app = FastAPI(
        title="Qlib Trading System API",
        description="量化交易系统高性能异步API接口",
        version="2.0.0",
        docs_url=None,  # 禁用默认文档
        redoc_url=None,  # 禁用默认ReDoc
        openapi_url="/openapi.json"
    )
    
    # 添加CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 健康检查端点
    @app.get("/health")
    async def health_check():
        return {
            "status": "healthy",
            "timestamp": "2025-07-31T15:30:00.000000",
            "version": "2.0.0",
            "service": "qlib-trading-system-api"
        }
    
    @app.get("/docs", response_class=HTMLResponse)
    async def custom_swagger_ui_html():
        """自定义Swagger UI页面，不依赖外部CDN"""
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Qlib Trading System API - Documentation</title>
            <meta charset="utf-8"/>
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <style>
                body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
                .header { background: #1f2937; color: white; padding: 20px; margin: -20px -20px 20px -20px; }
                .api-info { background: #f3f4f6; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
                .endpoint { background: white; border: 1px solid #e5e7eb; padding: 15px; margin: 10px 0; border-radius: 5px; }
                .method { display: inline-block; padding: 4px 8px; border-radius: 3px; color: white; font-weight: bold; margin-right: 10px; }
                .get { background: #10b981; }
                .post { background: #3b82f6; }
                .put { background: #f59e0b; }
                .delete { background: #ef4444; }
                .ws { background: #8b5cf6; }
                code { background: #f3f4f6; padding: 2px 4px; border-radius: 3px; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🚀 Qlib Trading System API</h1>
                <p>量化交易系统高性能异步API接口 v2.0.0</p>
            </div>

            <div class="api-info">
                <h2>📋 API信息</h2>
                <p><strong>基础URL:</strong> <code>http://localhost:8000</code></p>
                <p><strong>OpenAPI规范:</strong> <a href="/openapi.json" target="_blank">/openapi.json</a></p>
                <p><strong>架构:</strong> FastAPI + Uvicorn + WebSocket + RabbitMQ</p>
            </div>

            <h2>🔗 API端点</h2>

            <div class="endpoint">
                <span class="method get">GET</span>
                <strong>/health</strong>
                <p>系统健康检查</p>
            </div>

            <div class="endpoint">
                <span class="method get">GET</span>
                <strong>/info</strong>
                <p>获取API基本信息</p>
            </div>

            <div class="endpoint">
                <span class="method post">POST</span>
                <strong>/api/v1/auth/login</strong>
                <p>用户登录认证</p>
            </div>

            <div class="endpoint">
                <span class="method get">GET</span>
                <strong>/api/v1/auth/profile</strong>
                <p>获取用户信息</p>
            </div>

            <div class="endpoint">
                <span class="method get">GET</span>
                <strong>/api/v1/auth/permissions</strong>
                <p>获取权限列表</p>
            </div>

            <div class="endpoint">
                <span class="method get">GET</span>
                <strong>/api/v1/config/health</strong>
                <p>配置系统健康检查</p>
            </div>

            <div class="endpoint">
                <span class="method get">GET</span>
                <strong>/api/v1/monitoring/health</strong>
                <p>监控系统健康检查</p>
            </div>

            <div class="endpoint">
                <span class="method get">GET</span>
                <strong>/api/v1/trading/health</strong>
                <p>交易系统健康检查</p>
            </div>

            <div class="endpoint">
                <span class="method ws">WS</span>
                <strong>/ws</strong>
                <p>WebSocket实时通信连接</p>
            </div>

            <h2>🧪 测试工具</h2>
            <p>您可以使用以下工具测试API：</p>
            <ul>
                <li><strong>curl:</strong> 命令行HTTP客户端</li>
                <li><strong>Postman:</strong> 图形化API测试工具</li>
                <li><strong>httpie:</strong> 现代化HTTP客户端</li>
                <li><strong>WebSocket测试:</strong> 使用浏览器开发者工具或专门的WebSocket客户端</li>
            </ul>

            <h2>📚 示例请求</h2>
            <div class="api-info">
                <h3>登录示例</h3>
                <pre><code>curl -X POST "http://localhost:8000/api/v1/auth/login" \\
     -H "Content-Type: application/json" \\
     -d '{"username": "admin", "password": "admin123"}'</code></pre>

                <h3>健康检查示例</h3>
                <pre><code>curl "http://localhost:8000/health"</code></pre>

                <h3>WebSocket连接示例 (JavaScript)</h3>
                <pre><code>const ws = new WebSocket('ws://localhost:8000/ws');
ws.onmessage = (event) => console.log(JSON.parse(event.data));
ws.send(JSON.stringify({type: 'heartbeat', data: {}}));</code></pre>
            </div>
        </body>
        </html>
        """

    @app.get("/info")
    async def api_info():
        return {
            "title": "Qlib Trading System API",
            "version": "2.0.0",
            "description": "量化交易系统高性能异步API接口",
            "documentation_url": "/docs",
            "openapi_spec_url": "/openapi.json",
            "endpoints": {
                "auth": "/api/v1/auth",
                "config": "/api/v1/config",
                "trading": "/api/v1/trading",
                "monitoring": "/api/v1/monitoring"
            }
        }
    
    # 简单的认证端点
    @app.post("/api/v1/auth/login")
    async def login():
        return {
            "token": "mock_jwt_token_for_testing",
            "user": {
                "user_id": "admin_001",
                "username": "admin",
                "role": "admin"
            },
            "expires_in": 86400
        }
    
    @app.get("/api/v1/auth/profile")
    async def get_profile():
        return {
            "user_id": "admin_001",
            "username": "admin",
            "email": "<EMAIL>",
            "role": "admin",
            "permissions": ["manage_config", "view_config", "execute_trade"],
            "is_active": True,
            "created_time": "2025-07-31T15:30:00.000000"
        }

    @app.get("/api/v1/auth/permissions")
    async def get_permissions():
        return {
            "permissions": [
                {"name": "MANAGE_CONFIG", "value": "manage_config"},
                {"name": "VIEW_CONFIG", "value": "view_config"},
                {"name": "EXECUTE_TRADE", "value": "execute_trade"}
            ]
        }
    
    # 各模块健康检查
    @app.get("/api/v1/config/health")
    async def config_health():
        return {"status": "healthy", "service": "config-management"}
    
    @app.get("/api/v1/monitoring/health")
    async def monitoring_health():
        return {"status": "healthy", "service": "monitoring"}
    
    @app.get("/api/v1/trading/health")
    async def trading_health():
        return {"status": "healthy", "service": "trading-system"}

    # WebSocket端点
    @app.websocket("/ws")
    async def websocket_endpoint(websocket: WebSocket):
        await websocket.accept()

        # 发送欢迎消息
        welcome_message = {
            "type": "system_status",
            "data": {
                "status": "connected",
                "server_time": "2025-07-31T15:30:00.000000"
            },
            "timestamp": "2025-07-31T15:30:00.000000"
        }
        await websocket.send_text(json.dumps(welcome_message))

        try:
            while True:
                # 接收消息
                data = await websocket.receive_text()
                message = json.loads(data)

                # 处理心跳消息
                if message.get("type") == "heartbeat":
                    response = {
                        "type": "heartbeat",
                        "data": {"status": "alive"},
                        "timestamp": "2025-07-31T15:30:00.000000"
                    }
                    await websocket.send_text(json.dumps(response))
                else:
                    # 回显其他消息
                    response = {
                        "type": "echo",
                        "data": message,
                        "timestamp": "2025-07-31T15:30:00.000000"
                    }
                    await websocket.send_text(json.dumps(response))

        except WebSocketDisconnect:
            logger.info("WebSocket连接断开")

    return app


if __name__ == "__main__":
    import uvicorn
    
    logger.info("启动简化的FastAPI应用...")
    
    app = create_simple_app()
    
    logger.info("FastAPI应用创建完成，开始启动服务器...")
    logger.info("访问地址:")
    logger.info("  - API文档: http://localhost:8000/docs")
    logger.info("  - 健康检查: http://localhost:8000/health")
    logger.info("  - API信息: http://localhost:8000/info")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        log_level="info"
    )
