#!/usr/bin/env python3
"""
系统设置验证脚本
"""
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from qlib_trading_system.utils.logging.logger import system_logger


def validate_directories():
    """验证目录结构"""
    print("🔍 验证目录结构...")
    
    required_dirs = [
        "logs",
        "data/raw",
        "data/processed",
        "models/stock_selection",
        "models/intraday_trading", 
        "configs/strategies",
        "configs/models",
        "backtest_results",
        "reports",
        "temp"
    ]
    
    missing_dirs = []
    for directory in required_dirs:
        if not Path(directory).exists():
            missing_dirs.append(directory)
    
    if missing_dirs:
        print(f"❌ 缺少目录: {missing_dirs}")
        return False
    else:
        print(f"✅ 所有必要目录已创建 ({len(required_dirs)}个)")
        return True


def validate_config_files():
    """验证配置文件"""
    print("🔍 验证配置文件...")
    
    required_files = [
        ".env",
        "configs/strategies/default.json",
        "configs/models/default.json", 
        "configs/logging.json",
        "data/raw/sample_stocks.json",
        "data/processed/feature_config.json"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ 缺少配置文件: {missing_files}")
        return False
    else:
        print(f"✅ 所有配置文件已创建 ({len(required_files)}个)")
        return True


def validate_python_modules():
    """验证Python模块"""
    print("🔍 验证Python模块...")
    
    core_modules = [
        "qlib_trading_system.utils.logging.logger",
        "qlib_trading_system.utils.config.manager",
        "qlib_trading_system.utils.basic_qlib_init",
        "qlib_trading_system.utils.simple_init"
    ]
    
    failed_imports = []
    for module in core_modules:
        try:
            __import__(module)
        except ImportError as e:
            failed_imports.append((module, str(e)))
    
    if failed_imports:
        print("❌ 模块导入失败:")
        for module, error in failed_imports:
            print(f"   {module}: {error}")
        return False
    else:
        print(f"✅ 所有核心模块可正常导入 ({len(core_modules)}个)")
        return True


def validate_dependencies():
    """验证依赖包"""
    print("🔍 验证依赖包...")
    
    basic_deps = [
        ("pandas", "pandas"),
        ("numpy", "numpy"), 
        ("loguru", "loguru"),
        ("pydantic_settings", "pydantic_settings"),
        ("python-dotenv", "dotenv")
    ]
    
    missing_deps = []
    for dep_name, import_name in basic_deps:
        try:
            __import__(import_name)
        except ImportError:
            missing_deps.append(dep_name)
    
    if missing_deps:
        print(f"❌ 缺少依赖包: {missing_deps}")
        print("请运行: pip install pandas numpy loguru pydantic-settings python-dotenv")
        return False
    else:
        print(f"✅ 基础依赖包已安装 ({len(basic_deps)}个)")
        return True


def validate_qlib_environment():
    """验证Qlib环境"""
    print("🔍 验证Qlib环境...")
    
    qlib_data_dir = Path.home() / ".qlib" / "qlib_data" / "cn_data"
    config_file = qlib_data_dir / "config.json"
    
    checks = {
        "数据目录": qlib_data_dir.exists(),
        "配置文件": config_file.exists(),
        "环境变量": "QLIB_DATA_PATH" in os.environ or qlib_data_dir.exists()  # 如果目录存在，环境变量问题不严重
    }
    
    failed_checks = [name for name, result in checks.items() if not result]
    
    if failed_checks:
        print(f"❌ Qlib环境检查失败: {failed_checks}")
        return False
    else:
        print("✅ Qlib基础环境已设置")
        return True


def validate_system_config():
    """验证系统配置"""
    print("🔍 验证系统配置...")
    
    try:
        from qlib_trading_system.utils.config.manager import config_manager
        
        # 检查关键配置
        checks = {
            "资金模式": hasattr(config_manager.trading, 'CAPITAL_MODE'),
            "总资金": hasattr(config_manager.trading, 'TOTAL_CAPITAL'),
            "数据源": hasattr(config_manager.data_source, 'PRIMARY_DATA_SOURCE'),
            "日志级别": hasattr(config_manager.system, 'LOG_LEVEL')
        }
        
        failed_checks = [name for name, result in checks.items() if not result]
        
        if failed_checks:
            print(f"❌ 系统配置检查失败: {failed_checks}")
            return False
        else:
            print("✅ 系统配置验证通过")
            print(f"   - 资金模式: {config_manager.trading.CAPITAL_MODE}")
            print(f"   - 总资金: {config_manager.trading.TOTAL_CAPITAL:,.0f}")
            print(f"   - 数据源: {config_manager.data_source.PRIMARY_DATA_SOURCE}")
            return True
            
    except Exception as e:
        print(f"❌ 系统配置验证异常: {str(e)}")
        return False


def main():
    """主验证函数"""
    print("=" * 60)
    print("🚀 Qlib双AI交易系统 - 设置验证")
    print("=" * 60)
    
    validation_steps = [
        ("目录结构", validate_directories),
        ("配置文件", validate_config_files),
        ("Python模块", validate_python_modules),
        ("依赖包", validate_dependencies),
        ("Qlib环境", validate_qlib_environment),
        ("系统配置", validate_system_config)
    ]
    
    passed_count = 0
    total_count = len(validation_steps)
    
    for step_name, step_func in validation_steps:
        print(f"\n📋 {step_name}")
        try:
            if step_func():
                passed_count += 1
            else:
                print(f"⚠️  {step_name} 验证未通过")
        except Exception as e:
            print(f"❌ {step_name} 验证异常: {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"📊 验证结果: {passed_count}/{total_count} 项通过")
    
    if passed_count == total_count:
        print("🎉 系统设置验证完全通过！")
        print("\n📋 后续步骤:")
        print("1. 安装完整依赖: pip install -r requirements.txt")
        print("2. 配置数据源API密钥")
        print("3. 下载Qlib数据")
        print("4. 启动系统: python main.py")
        return True
    else:
        print("⚠️  系统设置存在问题，请根据上述提示进行修复")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)