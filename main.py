"""
Qlib交易系统主入口
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from qlib_trading_system.utils.logging.logger import system_logger
from qlib_trading_system.utils.config.manager import config_manager
from qlib_trading_system.utils.qlib_init import qlib_initializer


async def main():
    """主函数"""
    system_logger.info("=" * 60)
    system_logger.info("启动 Qlib 双AI交易系统")
    system_logger.info("=" * 60)
    
    try:
        # 1. 验证配置
        system_logger.info("验证系统配置...")
        validation_results = config_manager.validate_config()
        
        for check, result in validation_results.items():
            if result:
                system_logger.info(f"✓ {check} 配置验证通过")
            else:
                system_logger.error(f"✗ {check} 配置验证失败")
                return False
        
        # 2. 初始化Qlib
        system_logger.info("初始化 Qlib 框架...")
        if not qlib_initializer.initialize():
            system_logger.error("Qlib初始化失败")
            return False
        
        # 3. 检查数据可用性
        system_logger.info("检查数据可用性...")
        if not qlib_initializer.check_data_availability():
            system_logger.warning("数据检查未通过，请确保已下载股票数据")
        
        # 4. 初始化数据库连接
        system_logger.info("测试数据库连接...")
        from qlib_trading_system.utils.database import db_manager
        db_results = db_manager.test_connections()
        
        for db_name, success in db_results.items():
            if success:
                system_logger.info(f"✓ {db_name} 数据库连接成功")
            else:
                system_logger.warning(f"✗ {db_name} 数据库连接失败")
        
        # 5. 显示配置信息
        system_logger.info("系统配置信息:")
        system_logger.info(f"  - 资金模式: {config_manager.trading.CAPITAL_MODE}")
        system_logger.info(f"  - 总资金: {config_manager.trading.TOTAL_CAPITAL:,.0f}")
        system_logger.info(f"  - 最大持股数: {config_manager.trading.MAX_STOCKS}")
        system_logger.info(f"  - 环境: {config_manager.system.ENVIRONMENT}")
        system_logger.info(f"  - 调试模式: {config_manager.system.DEBUG}")
        
        system_logger.info("=" * 60)
        system_logger.info("Qlib 双AI交易系统初始化完成")
        system_logger.info("=" * 60)
        
        return True
        
    except Exception as e:
        system_logger.error(f"系统启动失败: {str(e)}")
        return False


def install_dependencies():
    """安装依赖包"""
    from qlib_trading_system.utils.system_init import system_initializer
    return system_initializer.install_dependencies()


def full_system_init():
    """完整系统初始化"""
    from qlib_trading_system.utils.system_init import system_initializer
    return system_initializer.full_initialization()


if __name__ == "__main__":
    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] == "--install-deps":
            if install_dependencies():
                system_logger.info("依赖包安装完成，请重新运行程序: python main.py")
            sys.exit(0)
        elif sys.argv[1] == "--init":
            if full_system_init():
                system_logger.info("系统初始化完成，请运行: python main.py")
            sys.exit(0)
        elif sys.argv[1] == "--help":
            print("Qlib双AI交易系统")
            print("使用方法:")
            print("  python main.py              # 启动系统")
            print("  python main.py --init       # 完整系统初始化")
            print("  python main.py --install-deps  # 仅安装依赖包")
            print("  python main.py --help       # 显示帮助信息")
            sys.exit(0)
    
    # 运行主程序
    success = asyncio.run(main())
    
    if not success:
        system_logger.error("系统启动失败，请检查配置和日志")
        system_logger.info("提示: 首次运行请执行 python main.py --init 进行系统初始化")
        sys.exit(1)
    
    system_logger.info("系统运行中... 按 Ctrl+C 退出")