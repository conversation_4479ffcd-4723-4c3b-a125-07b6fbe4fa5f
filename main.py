#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Qlib交易系统主入口

支持单机模式和微服务模式启动
"""
import os
import sys
import argparse
import asyncio
import logging
from pathlib import Path

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

try:
    from qlib_trading_system.utils.logging.logger import system_logger
    from qlib_trading_system.utils.config.manager import config_manager
    from qlib_trading_system.utils.qlib_init import qlib_initializer
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保已安装所有依赖: pip install -r requirements.txt")
    sys.exit(1)


def setup_environment():
    """设置环境变量和配置"""
    # 从.env文件加载环境变量
    env_file = PROJECT_ROOT / ".env"
    if env_file.exists():
        try:
            from dotenv import load_dotenv
            load_dotenv(env_file)
            print(f"已加载环境配置: {env_file}")
        except ImportError:
            print("python-dotenv未安装，跳过.env文件加载")

    # 设置默认环境变量
    os.environ.setdefault("ENVIRONMENT", "development")
    os.environ.setdefault("LOG_LEVEL", "INFO")
    os.environ.setdefault("PORT", "8000")
    os.environ.setdefault("HOST", "0.0.0.0")

    # 设置Python路径
    os.environ["PYTHONPATH"] = str(PROJECT_ROOT)


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="qlib交易系统主程序",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python main.py                          # 启动完整系统 (默认)
  python main.py --service api            # 启动API服务
  python main.py --service data-processor # 启动数据处理服务
  python main.py --service model-server   # 启动模型服务
  python main.py --service risk-monitor   # 启动风险监控服务
  python main.py --port 8080              # 指定端口
  python main.py --host 127.0.0.1         # 指定主机
  python main.py --log-level DEBUG        # 设置日志级别
        """
    )

    parser.add_argument(
        "--service",
        choices=["all", "api", "data-processor", "model-server", "risk-monitor"],
        default="all",
        help="要启动的服务类型 (默认: all - 完整系统)"
    )

    parser.add_argument(
        "--host",
        default=os.getenv("HOST", "0.0.0.0"),
        help="服务主机地址 (默认: 0.0.0.0)"
    )

    parser.add_argument(
        "--port",
        type=int,
        default=int(os.getenv("PORT", "8000")),
        help="服务端口 (默认: 8000)"
    )

    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default=os.getenv("LOG_LEVEL", "INFO"),
        help="日志级别 (默认: INFO)"
    )

    parser.add_argument(
        "--config",
        help="配置文件路径"
    )

    parser.add_argument(
        "--reload",
        action="store_true",
        help="启用自动重载 (开发模式)"
    )

    parser.add_argument(
        "--version",
        action="version",
        version="qlib-trading-system 1.0.0"
    )

    return parser.parse_args()


def create_health_endpoint():
    """创建健康检查端点"""
    try:
        from fastapi import FastAPI
        from fastapi.responses import JSONResponse
        import time

        app = FastAPI(title="Health Check", version="1.0.0")

        @app.get("/health")
        async def health_check():
            """健康检查端点"""
            return JSONResponse({
                "status": "healthy",
                "service": "qlib-trading-system",
                "version": "1.0.0",
                "timestamp": time.time()
            })

        return app
    except ImportError:
        # 如果FastAPI不可用，返回None
        return None


async def run_full_system():
    """运行完整系统（原有逻辑）"""
    system_logger.info("=" * 60)
    system_logger.info("启动 Qlib 双AI交易系统")
    system_logger.info("=" * 60)
    
    try:
        # 1. 验证配置
        system_logger.info("验证系统配置...")
        validation_results = config_manager.validate_config()
        
        for check, result in validation_results.items():
            if result:
                system_logger.info(f"✓ {check} 配置验证通过")
            else:
                system_logger.error(f"✗ {check} 配置验证失败")
                return False
        
        # 2. 初始化Qlib
        system_logger.info("初始化 Qlib 框架...")
        if not qlib_initializer.initialize():
            system_logger.error("Qlib初始化失败")
            return False
        
        # 3. 检查数据可用性
        system_logger.info("检查数据可用性...")
        if not qlib_initializer.check_data_availability():
            system_logger.warning("数据检查未通过，请确保已下载股票数据")
        
        # 4. 初始化数据库连接
        system_logger.info("测试数据库连接...")
        from qlib_trading_system.utils.database import db_manager
        db_results = db_manager.test_connections()
        
        for db_name, success in db_results.items():
            if success:
                system_logger.info(f"✓ {db_name} 数据库连接成功")
            else:
                system_logger.warning(f"✗ {db_name} 数据库连接失败")
        
        # 5. 显示配置信息
        system_logger.info("系统配置信息:")
        system_logger.info(f"  - 资金模式: {config_manager.trading.CAPITAL_MODE}")
        system_logger.info(f"  - 总资金: {config_manager.trading.TOTAL_CAPITAL:,.0f}")
        system_logger.info(f"  - 最大持股数: {config_manager.trading.MAX_STOCKS}")
        system_logger.info(f"  - 环境: {config_manager.system.ENVIRONMENT}")
        system_logger.info(f"  - 调试模式: {config_manager.system.DEBUG}")
        
        system_logger.info("=" * 60)
        system_logger.info("Qlib 双AI交易系统初始化完成")
        system_logger.info("=" * 60)
        
        return True
        
    except Exception as e:
        system_logger.error(f"系统启动失败: {str(e)}")
        return False


def install_dependencies():
    """安装依赖包"""
    from qlib_trading_system.utils.system_init import system_initializer
    return system_initializer.install_dependencies()


def full_system_init():
    """完整系统初始化"""
    from qlib_trading_system.utils.system_init import system_initializer
    return system_initializer.full_initialization()


def start_single_service(service_type: str, host: str = "0.0.0.0", port: int = 8000):
    """启动单个服务（用于微服务模式）"""
    system_logger.info(f"启动单个服务: {service_type}")
    system_logger.info(f"主机: {host}, 端口: {port}")

    try:
        # 创建简单的HTTP服务器用于健康检查
        import http.server
        import socketserver
        import json
        import time

        class HealthHandler(http.server.BaseHTTPRequestHandler):
            def do_GET(self):
                if self.path == '/health':
                    self.send_response(200)
                    self.send_header('Content-type', 'application/json')
                    self.end_headers()
                    response = {
                        "status": "healthy",
                        "service": service_type,
                        "version": "1.0.0",
                        "timestamp": time.time()
                    }
                    self.wfile.write(json.dumps(response).encode())
                else:
                    self.send_response(404)
                    self.end_headers()

            def log_message(self, format, *args):
                system_logger.info(f"HTTP: {format % args}")

        with socketserver.TCPServer((host, port), HealthHandler) as httpd:
            system_logger.info(f"{service_type} 服务启动成功，监听 {host}:{port}")
            system_logger.info(f"健康检查: http://{host}:{port}/health")
            system_logger.info("按 Ctrl+C 停止服务")
            httpd.serve_forever()

    except Exception as e:
        system_logger.error(f"{service_type} 服务启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] == "--install-deps":
            if install_dependencies():
                system_logger.info("依赖包安装完成，请重新运行程序: python main.py")
            sys.exit(0)
        elif sys.argv[1] == "--init":
            if full_system_init():
                system_logger.info("系统初始化完成，请运行: python main.py")
            sys.exit(0)
        elif sys.argv[1] == "--service":
            # 微服务模式
            if len(sys.argv) < 3:
                print("错误: 请指定服务类型")
                print("支持的服务: api, data-processor, model-server, risk-monitor")
                sys.exit(1)

            service_type = sys.argv[2]
            host = sys.argv[3] if len(sys.argv) > 3 else "0.0.0.0"
            port = int(sys.argv[4]) if len(sys.argv) > 4 else 8000

            if service_type not in ["api", "data-processor", "model-server", "risk-monitor"]:
                print(f"错误: 不支持的服务类型: {service_type}")
                print("支持的服务: api, data-processor, model-server, risk-monitor")
                sys.exit(1)

            try:
                start_single_service(service_type, host, port)
            except KeyboardInterrupt:
                system_logger.info("收到中断信号，正在关闭服务...")
            sys.exit(0)
        elif sys.argv[1] == "--help":
            print("Qlib双AI交易系统")
            print("使用方法:")
            print("  python main.py                                    # 启动完整系统")
            print("  python main.py --init                             # 完整系统初始化")
            print("  python main.py --install-deps                     # 仅安装依赖包")
            print("  python main.py --service <type> [host] [port]     # 启动单个服务")
            print("  python main.py --help                             # 显示帮助信息")
            print("")
            print("支持的服务类型:")
            print("  api            - API网关服务")
            print("  data-processor - 数据处理服务")
            print("  model-server   - 模型推理服务")
            print("  risk-monitor   - 风险监控服务")
            print("")
            print("示例:")
            print("  python main.py --service api 0.0.0.0 8000")
            print("  python main.py --service data-processor localhost 8001")
            sys.exit(0)
    
    # 运行主程序
    success = asyncio.run(main())
    
    if not success:
        system_logger.error("系统启动失败，请检查配置和日志")
        system_logger.info("提示: 首次运行请执行 python main.py --init 进行系统初始化")
        sys.exit(1)
    
    system_logger.info("系统运行中... 按 Ctrl+C 退出")