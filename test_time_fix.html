<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时间显示测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        .time-display {
            font-size: 1.2rem;
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🕒 时间显示功能测试</h1>
        
        <div class="time-display">
            <strong>当前时间:</strong> <span id="currentTime">--:--:--</span>
        </div>
        
        <div class="time-display">
            <strong>最后更新:</strong> <span id="lastUpdateTime">--:--:--</span>
        </div>
        
        <div class="time-display">
            <strong>API状态:</strong> <span id="apiStatus">检查中...</span>
        </div>
        
        <div id="statusMessage" class="status"></div>
        
        <button onclick="testTimeUpdate()">测试时间更新</button>
        <button onclick="testAPICall()">测试API调用</button>
        <button onclick="startAutoUpdate()">开始自动更新</button>
        <button onclick="stopAutoUpdate()">停止自动更新</button>
    </div>

    <script>
        let updateInterval = null;
        
        // 更新当前时间
        function updateCurrentTime() {
            const element = document.getElementById('currentTime');
            if (element) {
                element.textContent = new Date().toLocaleTimeString('zh-CN', {
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });
            }
        }
        
        // 更新最后更新时间
        function updateLastUpdateTime() {
            const element = document.getElementById('lastUpdateTime');
            if (element) {
                element.textContent = new Date().toLocaleTimeString('zh-CN', {
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });
            }
        }
        
        // 显示状态消息
        function showStatus(message, isError = false) {
            const element = document.getElementById('statusMessage');
            element.textContent = message;
            element.className = 'status ' + (isError ? 'error' : 'success');
        }
        
        // 测试时间更新
        function testTimeUpdate() {
            updateCurrentTime();
            updateLastUpdateTime();
            showStatus('✅ 时间更新成功！');
        }
        
        // 测试API调用
        async function testAPICall() {
            try {
                const response = await fetch('http://127.0.0.1:8080/api/metrics/current');
                const statusElement = document.getElementById('apiStatus');
                
                if (response.ok) {
                    const data = await response.json();
                    statusElement.textContent = '✅ API正常';
                    updateLastUpdateTime();
                    showStatus(`✅ API调用成功！时间戳: ${data.timestamp}`);
                } else {
                    statusElement.textContent = '❌ API错误';
                    showStatus(`❌ API调用失败: ${response.status}`, true);
                }
            } catch (error) {
                document.getElementById('apiStatus').textContent = '❌ 连接失败';
                showStatus(`❌ API调用失败: ${error.message}`, true);
            }
        }
        
        // 开始自动更新
        function startAutoUpdate() {
            if (updateInterval) {
                clearInterval(updateInterval);
            }
            
            updateInterval = setInterval(() => {
                updateCurrentTime();
                testAPICall();
            }, 2000); // 每2秒更新一次
            
            showStatus('🔄 开始自动更新（每2秒）');
        }
        
        // 停止自动更新
        function stopAutoUpdate() {
            if (updateInterval) {
                clearInterval(updateInterval);
                updateInterval = null;
            }
            showStatus('⏹️ 停止自动更新');
        }
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateCurrentTime();
            testAPICall();
            
            // 每秒更新当前时间
            setInterval(updateCurrentTime, 1000);
        });
    </script>
</body>
</html>