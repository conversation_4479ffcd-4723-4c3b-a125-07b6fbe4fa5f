#!/usr/bin/env python3
"""
Qlib双AI交易系统安装脚本
"""
import os
import sys
import subprocess
from pathlib import Path


def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("Qlib双AI交易系统 - 安装脚本")
    print("=" * 60)


def check_python_version():
    """检查Python版本"""
    print("检查Python版本...")
    
    if sys.version_info < (3, 8):
        print(f"❌ Python版本过低: {sys.version}")
        print("需要Python 3.8或更高版本")
        return False
    
    print(f"✅ Python版本: {sys.version}")
    return True


def install_requirements():
    """安装依赖包"""
    print("安装依赖包...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ 依赖包安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖包安装失败: {e}")
        return False


def create_env_file():
    """创建环境配置文件"""
    print("创建环境配置文件...")
    
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if env_file.exists():
        print("✅ .env文件已存在")
        return True
    
    if not env_example.exists():
        print("❌ .env.example文件不存在")
        return False
    
    try:
        import shutil
        shutil.copy(env_example, env_file)
        print("✅ 已创建.env文件")
        print("⚠️  请根据实际情况修改.env文件中的配置")
        return True
    except Exception as e:
        print(f"❌ 创建.env文件失败: {e}")
        return False


def create_directories():
    """创建目录结构"""
    print("创建项目目录结构...")
    
    directories = [
        "logs",
        "data/raw",
        "data/processed", 
        "models/stock_selection",
        "models/intraday_trading",
        "backtest_results",
        "reports",
        "configs"
    ]
    
    try:
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
        
        print("✅ 目录结构创建完成")
        return True
    except Exception as e:
        print(f"❌ 目录创建失败: {e}")
        return False


def run_system_init():
    """运行系统初始化"""
    print("运行系统初始化...")
    
    try:
        result = subprocess.run([
            sys.executable, "main.py", "--init"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 系统初始化完成")
            return True
        else:
            print(f"❌ 系统初始化失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 系统初始化异常: {e}")
        return False


def print_next_steps():
    """打印后续步骤"""
    print("\n" + "=" * 60)
    print("安装完成！后续步骤:")
    print("=" * 60)
    print("1. 修改.env文件中的配置参数")
    print("2. 配置数据源API密钥")
    print("3. 启动数据库服务 (ClickHouse, Redis, MongoDB)")
    print("4. 下载Qlib数据:")
    print("   python -m qlib.run.get_data qlib_data --target_dir ~/.qlib/qlib_data/cn_data --region cn")
    print("5. 启动系统:")
    print("   python main.py")
    print("=" * 60)


def main():
    """主函数"""
    print_banner()
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 安装步骤
    steps = [
        ("创建目录结构", create_directories),
        ("创建环境文件", create_env_file),
        ("安装依赖包", install_requirements),
        ("系统初始化", run_system_init)
    ]
    
    for step_name, step_func in steps:
        print(f"\n执行: {step_name}")
        if not step_func():
            print(f"❌ 安装失败于步骤: {step_name}")
            sys.exit(1)
    
    print_next_steps()


if __name__ == "__main__":
    main()