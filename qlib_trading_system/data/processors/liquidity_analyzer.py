"""
流动性分析器
实现订单簿分析和流动性评估算法
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
from collections import deque
from enum import Enum

from ..collectors.base import TickData, Level2Data
from .level2_parser import OrderBookSnapshot, LargeOrderInfo


class LiquidityLevel(str, Enum):
    """流动性等级"""
    EXCELLENT = "excellent"  # 优秀
    GOOD = "good"           # 良好
    FAIR = "fair"           # 一般
    POOR = "poor"           # 较差
    VERY_POOR = "very_poor" # 极差


@dataclass
class LiquidityMetrics:
    """流动性指标"""
    symbol: str
    timestamp: datetime
    
    # 基础流动性指标
    bid_ask_spread: float = 0.0           # 买卖价差
    spread_percentage: float = 0.0        # 价差百分比
    market_depth: float = 0.0             # 市场深度
    
    # 成交量指标
    average_daily_volume: float = 0.0     # 日均成交量
    volume_weighted_spread: float = 0.0   # 成交量加权价差
    turnover_rate: float = 0.0            # 换手率
    
    # 冲击成本指标
    impact_cost_1pct: float = 0.0         # 1%交易量的冲击成本
    impact_cost_5pct: float = 0.0         # 5%交易量的冲击成本
    impact_cost_10pct: float = 0.0        # 10%交易量的冲击成本
    
    # 时间维度指标
    time_to_execute_1pct: float = 0.0     # 执行1%交易量所需时间(分钟)
    time_to_execute_5pct: float = 0.0     # 执行5%交易量所需时间(分钟)
    
    # 订单簿指标
    order_book_imbalance: float = 0.0     # 订单簿不平衡度
    effective_spread: float = 0.0         # 有效价差
    realized_spread: float = 0.0          # 实现价差
    
    # 综合评分
    liquidity_score: float = 0.0          # 流动性综合评分(0-100)
    liquidity_level: LiquidityLevel = LiquidityLevel.POOR


@dataclass
class MarketImpactModel:
    """市场冲击模型"""
    symbol: str
    
    # 线性冲击模型参数
    permanent_impact_coeff: float = 0.0   # 永久冲击系数
    temporary_impact_coeff: float = 0.0   # 临时冲击系数
    
    # 非线性冲击模型参数
    impact_exponent: float = 0.5          # 冲击指数
    volatility_factor: float = 1.0        # 波动率因子
    
    # 模型拟合度
    r_squared: float = 0.0                # R平方
    last_calibration: datetime = None     # 最后校准时间


class LiquidityAnalyzer:
    """流动性分析器"""
    
    def __init__(self, 
                 history_window: int = 1000,      # 历史数据窗口大小
                 impact_percentiles: List[float] = [0.01, 0.05, 0.1],  # 冲击成本计算百分位
                 min_data_points: int = 100):     # 最小数据点数
        
        self.history_window = history_window
        self.impact_percentiles = impact_percentiles
        self.min_data_points = min_data_points
        
        # 历史数据缓存
        self.tick_history: Dict[str, deque] = {}
        self.orderbook_history: Dict[str, deque] = {}
        self.trade_history: Dict[str, deque] = {}
        
        # 市场冲击模型
        self.impact_models: Dict[str, MarketImpactModel] = {}
        
        self.logger = logging.getLogger(__name__)
    
    def add_tick_data(self, tick: TickData):
        """添加tick数据"""
        symbol = tick.symbol
        
        if symbol not in self.tick_history:
            self.tick_history[symbol] = deque(maxlen=self.history_window)
        
        self.tick_history[symbol].append(tick)
    
    def add_orderbook_data(self, snapshot: OrderBookSnapshot):
        """添加订单簿数据"""
        symbol = snapshot.symbol
        
        if symbol not in self.orderbook_history:
            self.orderbook_history[symbol] = deque(maxlen=self.history_window)
        
        self.orderbook_history[symbol].append(snapshot)
    
    def calculate_liquidity_metrics(self, symbol: str) -> Optional[LiquidityMetrics]:
        """计算流动性指标"""
        try:
            if (symbol not in self.tick_history or 
                symbol not in self.orderbook_history or
                len(self.tick_history[symbol]) < self.min_data_points):
                return None
            
            metrics = LiquidityMetrics(
                symbol=symbol,
                timestamp=datetime.now()
            )
            
            # 获取历史数据
            ticks = list(self.tick_history[symbol])
            snapshots = list(self.orderbook_history[symbol])
            
            # 计算基础流动性指标
            self._calculate_basic_metrics(metrics, ticks, snapshots)
            
            # 计算成交量指标
            self._calculate_volume_metrics(metrics, ticks)
            
            # 计算冲击成本
            self._calculate_impact_costs(metrics, ticks, snapshots)
            
            # 计算时间维度指标
            self._calculate_time_metrics(metrics, ticks, snapshots)
            
            # 计算订单簿指标
            self._calculate_orderbook_metrics(metrics, snapshots)
            
            # 计算综合评分
            self._calculate_liquidity_score(metrics)
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"计算流动性指标失败 {symbol}: {e}")
            return None
    
    def _calculate_basic_metrics(self, metrics: LiquidityMetrics, 
                               ticks: List[TickData], 
                               snapshots: List[OrderBookSnapshot]):
        """计算基础流动性指标"""
        try:
            if not snapshots:
                return
            
            # 买卖价差
            spreads = [snapshot.spread for snapshot in snapshots if snapshot.spread > 0]
            if spreads:
                metrics.bid_ask_spread = np.mean(spreads)
                
                # 价差百分比
                mid_prices = [snapshot.mid_price for snapshot in snapshots if snapshot.mid_price > 0]
                if mid_prices:
                    avg_mid_price = np.mean(mid_prices)
                    metrics.spread_percentage = (metrics.bid_ask_spread / avg_mid_price) * 100
            
            # 市场深度（前5档总金额）
            depths = []
            for snapshot in snapshots:
                if (len(snapshot.bid_prices) >= 5 and len(snapshot.ask_prices) >= 5 and
                    len(snapshot.bid_volumes) >= 5 and len(snapshot.ask_volumes) >= 5):
                    
                    bid_depth = sum(p * v for p, v in zip(snapshot.bid_prices[:5], snapshot.bid_volumes[:5]))
                    ask_depth = sum(p * v for p, v in zip(snapshot.ask_prices[:5], snapshot.ask_volumes[:5]))
                    depths.append(bid_depth + ask_depth)
            
            if depths:
                metrics.market_depth = np.mean(depths)
                
        except Exception as e:
            self.logger.error(f"计算基础指标失败: {e}")
    
    def _calculate_volume_metrics(self, metrics: LiquidityMetrics, ticks: List[TickData]):
        """计算成交量指标"""
        try:
            if not ticks:
                return
            
            # 日均成交量（基于历史数据估算）
            total_volume = sum(tick.volume for tick in ticks)
            time_span_hours = (ticks[-1].timestamp - ticks[0].timestamp).total_seconds() / 3600
            
            if time_span_hours > 0:
                # 假设每天交易4小时
                metrics.average_daily_volume = total_volume * (4 / time_span_hours)
            
            # 成交量加权价差（基于tick数据估算）
            if len(ticks) >= 2:
                weighted_spreads = []
                for i in range(1, len(ticks)):
                    if (ticks[i].bid_price and ticks[i].ask_price and 
                        ticks[i].bid_price > 0 and ticks[i].ask_price > 0):
                        spread = ticks[i].ask_price - ticks[i].bid_price
                        weighted_spreads.append(spread * ticks[i].volume)
                
                total_volume = sum(tick.volume for tick in ticks[1:])
                if total_volume > 0 and weighted_spreads:
                    metrics.volume_weighted_spread = sum(weighted_spreads) / total_volume
            
            # 换手率（需要流通股本数据，这里简化处理）
            # 实际应用中需要从基础数据中获取流通股本
            # metrics.turnover_rate = total_volume / float_shares * 100
            
        except Exception as e:
            self.logger.error(f"计算成交量指标失败: {e}")
    
    def _calculate_impact_costs(self, metrics: LiquidityMetrics, 
                              ticks: List[TickData], 
                              snapshots: List[OrderBookSnapshot]):
        """计算冲击成本"""
        try:
            if not snapshots or not ticks:
                return
            
            # 估算不同交易量的冲击成本
            recent_snapshots = snapshots[-50:]  # 最近50个快照
            recent_volume = sum(tick.volume for tick in ticks[-100:]) if len(ticks) >= 100 else 0
            
            if recent_volume == 0:
                return
            
            for pct in self.impact_percentiles:
                target_volume = recent_volume * pct
                impact_cost = self._estimate_impact_cost(target_volume, recent_snapshots)
                
                if pct == 0.01:
                    metrics.impact_cost_1pct = impact_cost
                elif pct == 0.05:
                    metrics.impact_cost_5pct = impact_cost
                elif pct == 0.1:
                    metrics.impact_cost_10pct = impact_cost
                    
        except Exception as e:
            self.logger.error(f"计算冲击成本失败: {e}")
    
    def _estimate_impact_cost(self, target_volume: float, 
                            snapshots: List[OrderBookSnapshot]) -> float:
        """估算冲击成本"""
        try:
            if not snapshots:
                return 0.0
            
            # 使用最新快照估算冲击成本
            latest_snapshot = snapshots[-1]
            
            if (not latest_snapshot.ask_prices or not latest_snapshot.ask_volumes or
                not latest_snapshot.bid_prices or not latest_snapshot.bid_volumes):
                return 0.0
            
            # 计算买入冲击成本
            remaining_volume = target_volume
            total_cost = 0.0
            
            for i in range(min(5, len(latest_snapshot.ask_prices))):
                available_volume = latest_snapshot.ask_volumes[i]
                price = latest_snapshot.ask_prices[i]
                
                if remaining_volume <= 0:
                    break
                
                volume_to_buy = min(remaining_volume, available_volume)
                total_cost += volume_to_buy * price
                remaining_volume -= volume_to_buy
            
            if target_volume > 0:
                avg_price = total_cost / target_volume
                mid_price = latest_snapshot.mid_price
                
                if mid_price > 0:
                    impact_cost = (avg_price - mid_price) / mid_price * 100  # 百分比
                    return impact_cost
            
            return 0.0
            
        except Exception as e:
            self.logger.error(f"估算冲击成本失败: {e}")
            return 0.0
    
    def _calculate_time_metrics(self, metrics: LiquidityMetrics, 
                              ticks: List[TickData], 
                              snapshots: List[OrderBookSnapshot]):
        """计算时间维度指标"""
        try:
            if not ticks or len(ticks) < 10:
                return
            
            # 估算执行时间（基于历史成交速度）
            recent_ticks = ticks[-100:] if len(ticks) >= 100 else ticks
            
            if len(recent_ticks) >= 2:
                time_span = (recent_ticks[-1].timestamp - recent_ticks[0].timestamp).total_seconds() / 60  # 分钟
                total_volume = sum(tick.volume for tick in recent_ticks)
                
                if time_span > 0 and total_volume > 0:
                    volume_per_minute = total_volume / time_span
                    
                    # 估算执行不同比例交易量所需时间
                    avg_volume = total_volume / len(recent_ticks)
                    
                    if volume_per_minute > 0:
                        metrics.time_to_execute_1pct = (avg_volume * 0.01) / volume_per_minute
                        metrics.time_to_execute_5pct = (avg_volume * 0.05) / volume_per_minute
                        
        except Exception as e:
            self.logger.error(f"计算时间指标失败: {e}")
    
    def _calculate_orderbook_metrics(self, metrics: LiquidityMetrics, 
                                   snapshots: List[OrderBookSnapshot]):
        """计算订单簿指标"""
        try:
            if not snapshots:
                return
            
            # 订单簿不平衡度
            imbalances = [snapshot.order_imbalance for snapshot in snapshots]
            if imbalances:
                metrics.order_book_imbalance = np.mean(np.abs(imbalances))
            
            # 有效价差（考虑订单簿深度的价差）
            effective_spreads = []
            for snapshot in snapshots:
                if (snapshot.bid_prices and snapshot.ask_prices and 
                    snapshot.bid_volumes and snapshot.ask_volumes):
                    
                    # 加权有效价差
                    total_bid_vol = sum(snapshot.bid_volumes[:3])
                    total_ask_vol = sum(snapshot.ask_volumes[:3])
                    
                    if total_bid_vol > 0 and total_ask_vol > 0:
                        weighted_bid = sum(p * v for p, v in zip(snapshot.bid_prices[:3], snapshot.bid_volumes[:3])) / total_bid_vol
                        weighted_ask = sum(p * v for p, v in zip(snapshot.ask_prices[:3], snapshot.ask_volumes[:3])) / total_ask_vol
                        effective_spreads.append(weighted_ask - weighted_bid)
            
            if effective_spreads:
                metrics.effective_spread = np.mean(effective_spreads)
            
            # 实现价差（基于实际成交价格，这里简化处理）
            if len(snapshots) >= 2:
                mid_price_changes = []
                for i in range(1, len(snapshots)):
                    if snapshots[i].mid_price > 0 and snapshots[i-1].mid_price > 0:
                        change = abs(snapshots[i].mid_price - snapshots[i-1].mid_price)
                        mid_price_changes.append(change)
                
                if mid_price_changes:
                    metrics.realized_spread = np.mean(mid_price_changes)
                    
        except Exception as e:
            self.logger.error(f"计算订单簿指标失败: {e}")
    
    def _calculate_liquidity_score(self, metrics: LiquidityMetrics):
        """计算流动性综合评分"""
        try:
            score = 0.0
            
            # 价差评分 (权重: 25%)
            if metrics.spread_percentage > 0:
                spread_score = max(0, 100 - metrics.spread_percentage * 20)  # 价差越小评分越高
                score += spread_score * 0.25
            
            # 市场深度评分 (权重: 20%)
            if metrics.market_depth > 0:
                # 假设100万为满分深度
                depth_score = min(100, metrics.market_depth / 1000000 * 100)
                score += depth_score * 0.20
            
            # 成交量评分 (权重: 20%)
            if metrics.average_daily_volume > 0:
                # 假设100万股为满分成交量
                volume_score = min(100, metrics.average_daily_volume / 1000000 * 100)
                score += volume_score * 0.20
            
            # 冲击成本评分 (权重: 20%)
            if metrics.impact_cost_5pct >= 0:
                impact_score = max(0, 100 - metrics.impact_cost_5pct * 10)  # 冲击成本越小评分越高
                score += impact_score * 0.20
            
            # 订单簿平衡评分 (权重: 15%)
            if metrics.order_book_imbalance >= 0:
                balance_score = max(0, 100 - metrics.order_book_imbalance * 100)
                score += balance_score * 0.15
            
            metrics.liquidity_score = min(100, max(0, score))
            
            # 确定流动性等级
            if metrics.liquidity_score >= 80:
                metrics.liquidity_level = LiquidityLevel.EXCELLENT
            elif metrics.liquidity_score >= 65:
                metrics.liquidity_level = LiquidityLevel.GOOD
            elif metrics.liquidity_score >= 50:
                metrics.liquidity_level = LiquidityLevel.FAIR
            elif metrics.liquidity_score >= 30:
                metrics.liquidity_level = LiquidityLevel.POOR
            else:
                metrics.liquidity_level = LiquidityLevel.VERY_POOR
                
        except Exception as e:
            self.logger.error(f"计算流动性评分失败: {e}")
            metrics.liquidity_score = 0.0
            metrics.liquidity_level = LiquidityLevel.VERY_POOR
    
    def calibrate_impact_model(self, symbol: str) -> Optional[MarketImpactModel]:
        """校准市场冲击模型"""
        try:
            if (symbol not in self.tick_history or 
                len(self.tick_history[symbol]) < self.min_data_points):
                return None
            
            ticks = list(self.tick_history[symbol])
            
            # 提取价格和成交量数据
            prices = [tick.price for tick in ticks]
            volumes = [tick.volume for tick in ticks]
            
            if len(prices) < 50:
                return None
            
            # 计算价格变化和成交量的关系
            price_changes = []
            volume_impacts = []
            
            for i in range(1, len(prices)):
                price_change = abs(prices[i] - prices[i-1]) / prices[i-1]
                volume_impact = volumes[i] / np.mean(volumes[max(0, i-20):i]) if i >= 20 else 1.0
                
                price_changes.append(price_change)
                volume_impacts.append(volume_impact)
            
            # 简单线性回归拟合冲击模型
            if len(price_changes) >= 10:
                price_changes = np.array(price_changes)
                volume_impacts = np.array(volume_impacts)
                
                # 线性回归: price_change = a * volume_impact + b
                coeffs = np.polyfit(volume_impacts, price_changes, 1)
                
                model = MarketImpactModel(
                    symbol=symbol,
                    temporary_impact_coeff=coeffs[0],
                    permanent_impact_coeff=coeffs[0] * 0.3,  # 假设永久冲击是临时冲击的30%
                    last_calibration=datetime.now()
                )
                
                # 计算R平方
                predicted = np.polyval(coeffs, volume_impacts)
                ss_res = np.sum((price_changes - predicted) ** 2)
                ss_tot = np.sum((price_changes - np.mean(price_changes)) ** 2)
                model.r_squared = 1 - (ss_res / ss_tot) if ss_tot > 0 else 0
                
                self.impact_models[symbol] = model
                return model
            
            return None
            
        except Exception as e:
            self.logger.error(f"校准冲击模型失败 {symbol}: {e}")
            return None
    
    def predict_market_impact(self, symbol: str, trade_volume: float, 
                            trade_direction: str = 'BUY') -> Optional[float]:
        """预测市场冲击"""
        try:
            model = self.impact_models.get(symbol)
            if not model:
                model = self.calibrate_impact_model(symbol)
                if not model:
                    return None
            
            # 获取最近的平均成交量
            if symbol not in self.tick_history:
                return None
            
            recent_ticks = list(self.tick_history[symbol])[-100:]
            if not recent_ticks:
                return None
            
            avg_volume = np.mean([tick.volume for tick in recent_ticks])
            if avg_volume <= 0:
                return None
            
            # 计算相对交易量
            relative_volume = trade_volume / avg_volume
            
            # 预测冲击（使用线性模型）
            temporary_impact = model.temporary_impact_coeff * relative_volume
            permanent_impact = model.permanent_impact_coeff * relative_volume
            
            total_impact = temporary_impact + permanent_impact
            
            # 考虑交易方向（买入为正冲击，卖出为负冲击）
            if trade_direction.upper() == 'SELL':
                total_impact = -total_impact
            
            return total_impact
            
        except Exception as e:
            self.logger.error(f"预测市场冲击失败 {symbol}: {e}")
            return None
    
    def get_optimal_execution_strategy(self, symbol: str, target_volume: float, 
                                     max_impact: float = 0.01) -> Dict[str, Any]:
        """获取最优执行策略"""
        try:
            metrics = self.calculate_liquidity_metrics(symbol)
            if not metrics:
                return {}
            
            # 基于流动性指标制定执行策略
            strategy = {
                'symbol': symbol,
                'target_volume': target_volume,
                'max_impact': max_impact,
                'liquidity_level': metrics.liquidity_level.value,
                'recommended_strategy': 'TWAP',  # 默认时间加权平均价格策略
                'execution_time_minutes': 60,   # 默认执行时间
                'slice_size': target_volume / 10,  # 默认分10次执行
                'urgency_level': 'NORMAL'
            }
            
            # 根据流动性等级调整策略
            if metrics.liquidity_level == LiquidityLevel.EXCELLENT:
                strategy['recommended_strategy'] = 'MARKET'
                strategy['execution_time_minutes'] = 5
                strategy['slice_size'] = target_volume / 2
                strategy['urgency_level'] = 'HIGH'
                
            elif metrics.liquidity_level == LiquidityLevel.GOOD:
                strategy['recommended_strategy'] = 'VWAP'
                strategy['execution_time_minutes'] = 15
                strategy['slice_size'] = target_volume / 5
                strategy['urgency_level'] = 'MEDIUM'
                
            elif metrics.liquidity_level in [LiquidityLevel.POOR, LiquidityLevel.VERY_POOR]:
                strategy['recommended_strategy'] = 'ICEBERG'
                strategy['execution_time_minutes'] = 120
                strategy['slice_size'] = target_volume / 20
                strategy['urgency_level'] = 'LOW'
            
            # 根据冲击成本调整
            if metrics.impact_cost_5pct > max_impact:
                strategy['execution_time_minutes'] *= 2
                strategy['slice_size'] /= 2
                strategy['urgency_level'] = 'LOW'
            
            return strategy
            
        except Exception as e:
            self.logger.error(f"获取执行策略失败 {symbol}: {e}")
            return {}
    
    def get_liquidity_summary(self, symbol: str) -> Dict[str, Any]:
        """获取流动性摘要"""
        try:
            metrics = self.calculate_liquidity_metrics(symbol)
            if not metrics:
                return {}
            
            return {
                'symbol': symbol,
                'timestamp': metrics.timestamp,
                'liquidity_score': metrics.liquidity_score,
                'liquidity_level': metrics.liquidity_level.value,
                'bid_ask_spread': metrics.bid_ask_spread,
                'spread_percentage': metrics.spread_percentage,
                'market_depth': metrics.market_depth,
                'average_daily_volume': metrics.average_daily_volume,
                'impact_cost_5pct': metrics.impact_cost_5pct,
                'order_book_imbalance': metrics.order_book_imbalance,
                'data_points': len(self.tick_history.get(symbol, [])),
                'model_available': symbol in self.impact_models
            }
            
        except Exception as e:
            self.logger.error(f"获取流动性摘要失败 {symbol}: {e}")
            return {}
    
    def clear_history(self, symbol: str):
        """清空历史数据"""
        if symbol in self.tick_history:
            self.tick_history[symbol].clear()
        if symbol in self.orderbook_history:
            self.orderbook_history[symbol].clear()
        if symbol in self.trade_history:
            self.trade_history[symbol].clear()
        if symbol in self.impact_models:
            del self.impact_models[symbol]
        
        self.logger.info(f"已清空 {symbol} 的流动性分析历史数据")
    
    def clear_all_history(self):
        """清空所有历史数据"""
        self.tick_history.clear()
        self.orderbook_history.clear()
        self.trade_history.clear()
        self.impact_models.clear()
        
        self.logger.info("已清空所有流动性分析历史数据")