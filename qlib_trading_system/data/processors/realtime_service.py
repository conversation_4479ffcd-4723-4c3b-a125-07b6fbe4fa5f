"""
实时数据服务
整合实时数据处理器、连接管理器和Level-2解析器
"""
import asyncio
import logging
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime
import json

from .realtime_processor import RealtimeDataProcessor, QualityMetrics
from .connection_manager import ConnectionManager, ConnectionConfig, ConnectionStatus
from .level2_parser import Level2Parser, OrderBookSnapshot, LargeOrderInfo, MarketDepthAnalysis
from ..collectors.base import TickData, Level2Data, PriceData


class RealtimeDataService:
    """实时数据服务"""
    
    def __init__(self, 
                 websocket_configs: Dict[str, ConnectionConfig] = None,
                 large_order_threshold: float = 100000):
        
        # 初始化组件
        self.processor = RealtimeDataProcessor()
        self.connection_manager = ConnectionManager()
        self.level2_parser = Level2Parser(large_order_threshold=large_order_threshold)
        
        # 配置WebSocket连接
        if websocket_configs:
            for conn_id, config in websocket_configs.items():
                self.connection_manager.add_connection_config(conn_id, config)
        
        # 订阅的股票列表
        self.subscribed_symbols: set = set()
        
        # 回调函数
        self.tick_callbacks: List[Callable[[TickData], None]] = []
        self.level2_callbacks: List[Callable[[Level2Data], None]] = []
        self.price_callbacks: List[Callable[[PriceData], None]] = []
        self.order_book_callbacks: List[Callable[[OrderBookSnapshot], None]] = []
        self.large_order_callbacks: List[Callable[[List[LargeOrderInfo]], None]] = []
        self.quality_callbacks: List[Callable[[str, QualityMetrics], None]] = []
        
        self.logger = logging.getLogger(__name__)
        
        # 设置内部回调
        self._setup_internal_callbacks()
    
    def _setup_internal_callbacks(self):
        """设置内部回调"""
        # 设置数据处理器回调
        self.processor.add_tick_callback(self._on_tick_data)
        self.processor.add_level2_callback(self._on_level2_data)
        self.processor.add_price_callback(self._on_price_data)
        self.processor.add_quality_callback(self._on_quality_update)
        
        # 设置连接管理器回调
        self.connection_manager.add_status_callback(self._on_connection_status_change)
    
    async def _on_tick_data(self, tick: TickData):
        """处理tick数据回调"""
        # 触发外部回调
        for callback in self.tick_callbacks:
            try:
                await asyncio.create_task(callback(tick))
            except Exception as e:
                self.logger.error(f"Tick回调执行失败: {e}")
    
    async def _on_level2_data(self, level2: Level2Data):
        """处理Level-2数据回调"""
        try:
            # 解析为订单簿快照
            snapshot = self.level2_parser.parse_level2_data(level2)
            
            # 检测大单
            large_orders = self.level2_parser.detect_large_orders(snapshot)
            
            # 触发订单簿回调
            for callback in self.order_book_callbacks:
                try:
                    await asyncio.create_task(callback(snapshot))
                except Exception as e:
                    self.logger.error(f"订单簿回调执行失败: {e}")
            
            # 触发大单回调
            if large_orders:
                for callback in self.large_order_callbacks:
                    try:
                        await asyncio.create_task(callback(large_orders))
                    except Exception as e:
                        self.logger.error(f"大单回调执行失败: {e}")
            
            # 触发Level-2回调
            for callback in self.level2_callbacks:
                try:
                    await asyncio.create_task(callback(level2))
                except Exception as e:
                    self.logger.error(f"Level-2回调执行失败: {e}")
                    
        except Exception as e:
            self.logger.error(f"处理Level-2数据失败: {e}")
    
    async def _on_price_data(self, price: PriceData):
        """处理价格数据回调"""
        # 触发外部回调
        for callback in self.price_callbacks:
            try:
                await asyncio.create_task(callback(price))
            except Exception as e:
                self.logger.error(f"价格回调执行失败: {e}")
    
    async def _on_quality_update(self, symbol: str, metrics: QualityMetrics):
        """处理数据质量更新回调"""
        # 触发外部回调
        for callback in self.quality_callbacks:
            try:
                await asyncio.create_task(callback(symbol, metrics))
            except Exception as e:
                self.logger.error(f"质量回调执行失败: {e}")
    
    async def _on_connection_status_change(self, connection_id: str, status: ConnectionStatus):
        """处理连接状态变化"""
        self.logger.info(f"连接状态变化: {connection_id} -> {status.value}")
        
        # 如果连接恢复，重新订阅
        if status == ConnectionStatus.CONNECTED and self.subscribed_symbols:
            await self.connection_manager.subscribe_symbols(
                connection_id, list(self.subscribed_symbols)
            )
    
    def add_websocket_config(self, connection_id: str, config: ConnectionConfig):
        """添加WebSocket配置"""
        self.connection_manager.add_connection_config(connection_id, config)
        
        # 添加消息处理回调
        self.connection_manager.add_message_callback(
            connection_id, self._process_websocket_message
        )
    
    async def _process_websocket_message(self, message: Dict[str, Any]):
        """处理WebSocket消息"""
        try:
            msg_type = message.get('type', '')
            
            if msg_type == 'tick':
                # 处理tick数据
                tick_data = self._parse_tick_message(message)
                if tick_data:
                    await self.processor.process_tick_data(tick_data)
            
            elif msg_type == 'level2':
                # 处理Level-2数据
                level2_data = self._parse_level2_message(message)
                if level2_data:
                    await self.processor.process_level2_data(level2_data)
            
            elif msg_type == 'price':
                # 处理价格数据
                price_data = self._parse_price_message(message)
                if price_data:
                    await self.processor.process_price_data(price_data)
            
            elif msg_type == 'heartbeat':
                # 心跳消息，记录日志
                self.logger.debug("收到心跳消息")
            
            else:
                self.logger.warning(f"未知消息类型: {msg_type}")
                
        except Exception as e:
            self.logger.error(f"处理WebSocket消息失败: {e}")
    
    def _parse_tick_message(self, message: Dict[str, Any]) -> Optional[TickData]:
        """解析tick消息"""
        try:
            data = message.get('data', {})
            return TickData(
                symbol=data['symbol'],
                timestamp=datetime.fromisoformat(data['timestamp']),
                price=float(data['price']),
                volume=int(data['volume']),
                amount=float(data['amount']),
                direction=data.get('direction', 'N'),
                bid_price=float(data.get('bid_price', 0)),
                ask_price=float(data.get('ask_price', 0)),
                bid_volume=int(data.get('bid_volume', 0)),
                ask_volume=int(data.get('ask_volume', 0))
            )
        except Exception as e:
            self.logger.error(f"解析tick消息失败: {e}")
            return None
    
    def _parse_level2_message(self, message: Dict[str, Any]) -> Optional[Level2Data]:
        """解析Level-2消息"""
        try:
            data = message.get('data', {})
            return Level2Data(
                symbol=data['symbol'],
                timestamp=datetime.fromisoformat(data['timestamp']),
                bid_prices=[float(p) for p in data['bid_prices']],
                bid_volumes=[int(v) for v in data['bid_volumes']],
                ask_prices=[float(p) for p in data['ask_prices']],
                ask_volumes=[int(v) for v in data['ask_volumes']],
                total_bid_volume=int(data['total_bid_volume']),
                total_ask_volume=int(data['total_ask_volume'])
            )
        except Exception as e:
            self.logger.error(f"解析Level-2消息失败: {e}")
            return None
    
    def _parse_price_message(self, message: Dict[str, Any]) -> Optional[PriceData]:
        """解析价格消息"""
        try:
            data = message.get('data', {})
            return PriceData(
                symbol=data['symbol'],
                timestamp=datetime.fromisoformat(data['timestamp']),
                open=float(data['open']),
                high=float(data['high']),
                low=float(data['low']),
                close=float(data['close']),
                volume=int(data['volume']),
                amount=float(data['amount']),
                pre_close=float(data.get('pre_close', 0)),
                change=float(data.get('change', 0)),
                pct_change=float(data.get('pct_change', 0))
            )
        except Exception as e:
            self.logger.error(f"解析价格消息失败: {e}")
            return None
    
    # 回调注册方法
    def add_tick_callback(self, callback: Callable[[TickData], None]):
        """添加tick数据回调"""
        self.tick_callbacks.append(callback)
    
    def add_level2_callback(self, callback: Callable[[Level2Data], None]):
        """添加Level-2数据回调"""
        self.level2_callbacks.append(callback)
    
    def add_price_callback(self, callback: Callable[[PriceData], None]):
        """添加价格数据回调"""
        self.price_callbacks.append(callback)
    
    def add_order_book_callback(self, callback: Callable[[OrderBookSnapshot], None]):
        """添加订单簿回调"""
        self.order_book_callbacks.append(callback)
    
    def add_large_order_callback(self, callback: Callable[[List[LargeOrderInfo]], None]):
        """添加大单回调"""
        self.large_order_callbacks.append(callback)
    
    def add_quality_callback(self, callback: Callable[[str, QualityMetrics], None]):
        """添加数据质量回调"""
        self.quality_callbacks.append(callback)
    
    # 订阅管理
    async def subscribe_symbols(self, symbols: List[str]) -> bool:
        """订阅股票"""
        try:
            self.subscribed_symbols.update(symbols)
            
            # 向所有活跃连接发送订阅请求
            active_connections = self.connection_manager.get_active_connections()
            success_count = 0
            
            for conn_id in active_connections:
                if await self.connection_manager.subscribe_symbols(conn_id, symbols):
                    success_count += 1
            
            self.logger.info(f"订阅股票成功: {symbols}, 活跃连接: {success_count}/{len(active_connections)}")
            return success_count > 0
            
        except Exception as e:
            self.logger.error(f"订阅股票失败: {e}")
            return False
    
    async def unsubscribe_symbols(self, symbols: List[str]) -> bool:
        """取消订阅股票"""
        try:
            self.subscribed_symbols.difference_update(symbols)
            
            # 向所有活跃连接发送取消订阅请求
            active_connections = self.connection_manager.get_active_connections()
            success_count = 0
            
            for conn_id in active_connections:
                if await self.connection_manager.unsubscribe_symbols(conn_id, symbols):
                    success_count += 1
            
            self.logger.info(f"取消订阅股票成功: {symbols}, 活跃连接: {success_count}/{len(active_connections)}")
            return success_count > 0
            
        except Exception as e:
            self.logger.error(f"取消订阅股票失败: {e}")
            return False
    
    # 数据查询方法
    def get_latest_tick(self, symbol: str) -> Optional[TickData]:
        """获取最新tick数据"""
        return self.processor.get_latest_tick(symbol)
    
    def get_latest_level2(self, symbol: str) -> Optional[Level2Data]:
        """获取最新Level-2数据"""
        return self.processor.get_latest_level2(symbol)
    
    def get_latest_price(self, symbol: str) -> Optional[PriceData]:
        """获取最新价格数据"""
        return self.processor.get_latest_price(symbol)
    
    def get_order_book_snapshot(self, symbol: str) -> Optional[OrderBookSnapshot]:
        """获取最新订单簿快照"""
        level2 = self.get_latest_level2(symbol)
        if level2:
            return self.level2_parser.parse_level2_data(level2)
        return None
    
    def get_market_depth_analysis(self, symbol: str) -> Optional[MarketDepthAnalysis]:
        """获取市场深度分析"""
        snapshot = self.get_order_book_snapshot(symbol)
        if snapshot:
            return self.level2_parser.analyze_market_depth(snapshot)
        return None
    
    def get_tick_history(self, symbol: str, minutes: int = 60) -> List[TickData]:
        """获取历史tick数据"""
        return self.processor.get_tick_history(symbol, minutes)
    
    def get_order_flow_imbalance(self, symbol: str, window_minutes: int = 1) -> float:
        """获取订单流不平衡度"""
        return self.level2_parser.calculate_order_flow_imbalance(symbol, window_minutes)
    
    # 服务控制
    async def start(self):
        """启动实时数据服务"""
        try:
            # 启动数据处理器
            await self.processor.start()
            
            # 启动连接管理器
            await self.connection_manager.start()
            
            self.logger.info("实时数据服务已启动")
            
        except Exception as e:
            self.logger.error(f"启动实时数据服务失败: {e}")
            raise
    
    async def stop(self):
        """停止实时数据服务"""
        try:
            # 停止连接管理器
            await self.connection_manager.stop()
            
            # 停止数据处理器
            await self.processor.stop()
            
            self.logger.info("实时数据服务已停止")
            
        except Exception as e:
            self.logger.error(f"停止实时数据服务失败: {e}")
    
    def get_service_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        return {
            'processor_stats': self.processor.get_statistics(),
            'connection_summary': self.connection_manager.get_summary(),
            'subscribed_symbols': list(self.subscribed_symbols),
            'subscribed_count': len(self.subscribed_symbols)
        }
    
    def get_data_quality_report(self) -> Dict[str, Any]:
        """获取数据质量报告"""
        report = {}
        
        for symbol in self.subscribed_symbols:
            buffer = self.processor.data_buffers.get(symbol)
            if buffer:
                report[symbol] = {
                    'quality_metrics': buffer.quality_metrics,
                    'last_update_time': buffer.last_update_time,
                    'tick_count': len(buffer.tick_buffer),
                    'level2_count': len(buffer.level2_buffer),
                    'price_count': len(buffer.price_buffer)
                }
        
        return report