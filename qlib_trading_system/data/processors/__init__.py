"""
数据处理器模块
实现实时行情数据处理、连接管理和Level-2数据解析
"""

from .realtime_processor import (
    RealtimeDataProcessor, RealtimeDataBuffer, QualityMetrics, DataQuality
)
from .connection_manager import (
    ConnectionManager, ConnectionConfig, ConnectionStatus, ConnectionStats
)
from .level2_parser import (
    Level2Parser, OrderBookSnapshot, LargeOrderInfo, MarketDepthAnalysis
)

__all__ = [
    # 实时数据处理
    'RealtimeDataProcessor',
    'RealtimeDataBuffer', 
    'QualityMetrics',
    'DataQuality',
    
    # 连接管理
    'ConnectionManager',
    'ConnectionConfig',
    'ConnectionStatus', 
    'ConnectionStats',
    
    # Level-2数据解析
    'Level2Parser',
    'OrderBookSnapshot',
    'LargeOrderInfo',
    'MarketDepthAnalysis'
]

# 数据处理器接口将在后续任务中实现
# 这里先定义模块结构

__all__ = [
    'BaseDataProcessor',
    'FeatureProcessor',
    'TechnicalIndicatorProcessor',
    'SentimentProcessor',
    'RiskProcessor'
]