"""
实时行情数据处理模块
实现tick级数据接收、缓存、Level-2行情解析和数据质量检查
"""
import asyncio
import logging
from typing import Dict, List, Optional, Callable, Any, Deque
from datetime import datetime, timedelta
from collections import deque, defaultdict
from dataclasses import dataclass, field
import pandas as pd
import numpy as np
from enum import Enum
import json
import websockets
import aiohttp

from ..collectors.base import TickData, Level2Data, PriceData, DataSourceException


class DataQuality(str, Enum):
    """数据质量等级"""
    EXCELLENT = "excellent"  # 优秀
    GOOD = "good"           # 良好
    FAIR = "fair"           # 一般
    POOR = "poor"           # 较差
    INVALID = "invalid"     # 无效


@dataclass
class QualityMetrics:
    """数据质量指标"""
    completeness: float = 0.0      # 完整性 (0-1)
    timeliness: float = 0.0        # 及时性 (0-1)
    accuracy: float = 0.0          # 准确性 (0-1)
    consistency: float = 0.0       # 一致性 (0-1)
    overall_score: float = 0.0     # 综合评分 (0-1)
    quality_level: DataQuality = DataQuality.INVALID


@dataclass
class RealtimeDataBuffer:
    """实时数据缓冲区"""
    symbol: str
    tick_buffer: Deque[TickData] = field(default_factory=lambda: deque(maxlen=3600))  # 1小时tick数据
    level2_buffer: Deque[Level2Data] = field(default_factory=lambda: deque(maxlen=1800))  # 30分钟L2数据
    price_buffer: Deque[PriceData] = field(default_factory=lambda: deque(maxlen=300))  # 5分钟价格数据
    last_update_time: datetime = field(default_factory=datetime.now)
    quality_metrics: QualityMetrics = field(default_factory=QualityMetrics)


class RealtimeDataProcessor:
    """实时数据处理器"""
    
    def __init__(self, 
                 buffer_size: int = 10000,
                 quality_check_interval: int = 60,
                 reconnect_interval: int = 5):
        self.buffer_size = buffer_size
        self.quality_check_interval = quality_check_interval
        self.reconnect_interval = reconnect_interval
        
        # 数据缓冲区
        self.data_buffers: Dict[str, RealtimeDataBuffer] = {}
        
        # 连接管理
        self.connections: Dict[str, Any] = {}
        self.is_running = False
        
        # 回调函数
        self.tick_callbacks: List[Callable[[TickData], None]] = []
        self.level2_callbacks: List[Callable[[Level2Data], None]] = []
        self.price_callbacks: List[Callable[[PriceData], None]] = []
        self.quality_callbacks: List[Callable[[str, QualityMetrics], None]] = []
        
        # 统计信息
        self.stats = {
            'total_ticks': 0,
            'total_level2': 0,
            'total_prices': 0,
            'invalid_data_count': 0,
            'reconnect_count': 0,
            'last_data_time': None
        }
        
        self.logger = logging.getLogger(__name__)
    
    def add_tick_callback(self, callback: Callable[[TickData], None]):
        """添加tick数据回调"""
        self.tick_callbacks.append(callback)
    
    def add_level2_callback(self, callback: Callable[[Level2Data], None]):
        """添加Level-2数据回调"""
        self.level2_callbacks.append(callback)
    
    def add_price_callback(self, callback: Callable[[PriceData], None]):
        """添加价格数据回调"""
        self.price_callbacks.append(callback)
    
    def add_quality_callback(self, callback: Callable[[str, QualityMetrics], None]):
        """添加数据质量回调"""
        self.quality_callbacks.append(callback)
    
    def get_or_create_buffer(self, symbol: str) -> RealtimeDataBuffer:
        """获取或创建数据缓冲区"""
        if symbol not in self.data_buffers:
            self.data_buffers[symbol] = RealtimeDataBuffer(symbol=symbol)
        return self.data_buffers[symbol]
    
    async def process_tick_data(self, tick: TickData):
        """处理tick数据"""
        try:
            # 数据质量检查
            if not self._validate_tick_data(tick):
                self.stats['invalid_data_count'] += 1
                self.logger.warning(f"无效的tick数据: {tick.symbol}")
                return
            
            # 获取缓冲区
            buffer = self.get_or_create_buffer(tick.symbol)
            
            # 存储到缓冲区
            buffer.tick_buffer.append(tick)
            buffer.last_update_time = datetime.now()
            
            # 更新统计
            self.stats['total_ticks'] += 1
            self.stats['last_data_time'] = tick.timestamp
            
            # 触发回调
            for callback in self.tick_callbacks:
                try:
                    await asyncio.create_task(callback(tick))
                except Exception as e:
                    self.logger.error(f"Tick回调执行失败: {e}")
            
            self.logger.debug(f"处理tick数据: {tick.symbol} @ {tick.price}")
            
        except Exception as e:
            self.logger.error(f"处理tick数据失败: {e}")
    
    async def process_level2_data(self, level2: Level2Data):
        """处理Level-2数据"""
        try:
            # 数据质量检查
            if not self._validate_level2_data(level2):
                self.stats['invalid_data_count'] += 1
                self.logger.warning(f"无效的Level-2数据: {level2.symbol}")
                return
            
            # 获取缓冲区
            buffer = self.get_or_create_buffer(level2.symbol)
            
            # 存储到缓冲区
            buffer.level2_buffer.append(level2)
            buffer.last_update_time = datetime.now()
            
            # 更新统计
            self.stats['total_level2'] += 1
            self.stats['last_data_time'] = level2.timestamp
            
            # 触发回调
            for callback in self.level2_callbacks:
                try:
                    await asyncio.create_task(callback(level2))
                except Exception as e:
                    self.logger.error(f"Level-2回调执行失败: {e}")
            
            self.logger.debug(f"处理Level-2数据: {level2.symbol}")
            
        except Exception as e:
            self.logger.error(f"处理Level-2数据失败: {e}")
    
    async def process_price_data(self, price: PriceData):
        """处理价格数据"""
        try:
            # 数据质量检查
            if not self._validate_price_data(price):
                self.stats['invalid_data_count'] += 1
                self.logger.warning(f"无效的价格数据: {price.symbol}")
                return
            
            # 获取缓冲区
            buffer = self.get_or_create_buffer(price.symbol)
            
            # 存储到缓冲区
            buffer.price_buffer.append(price)
            buffer.last_update_time = datetime.now()
            
            # 更新统计
            self.stats['total_prices'] += 1
            self.stats['last_data_time'] = price.timestamp
            
            # 触发回调
            for callback in self.price_callbacks:
                try:
                    await asyncio.create_task(callback(price))
                except Exception as e:
                    self.logger.error(f"价格回调执行失败: {e}")
            
            self.logger.debug(f"处理价格数据: {price.symbol} @ {price.close}")
            
        except Exception as e:
            self.logger.error(f"处理价格数据失败: {e}")
    
    def _validate_tick_data(self, tick: TickData) -> bool:
        """验证tick数据"""
        try:
            # 基本字段检查
            if not tick.symbol or not tick.timestamp:
                return False
            
            # 价格合理性检查
            if tick.price <= 0 or tick.price > 10000:  # 假设股价不超过10000元
                return False
            
            # 成交量检查
            if tick.volume < 0:
                return False
            
            # 时间合理性检查
            now = datetime.now()
            if tick.timestamp > now + timedelta(minutes=5):  # 不能超过当前时间5分钟
                return False
            
            if tick.timestamp < now - timedelta(days=1):  # 不能早于1天前
                return False
            
            return True
            
        except Exception:
            return False
    
    def _validate_level2_data(self, level2: Level2Data) -> bool:
        """验证Level-2数据"""
        try:
            # 基本字段检查
            if not level2.symbol or not level2.timestamp:
                return False
            
            # 买卖盘数据检查
            if len(level2.bid_prices) != 5 or len(level2.ask_prices) != 5:
                return False
            
            if len(level2.bid_volumes) != 5 or len(level2.ask_volumes) != 5:
                return False
            
            # 价格合理性检查
            for price in level2.bid_prices + level2.ask_prices:
                if price <= 0 or price > 10000:
                    return False
            
            # 数量合理性检查
            for volume in level2.bid_volumes + level2.ask_volumes:
                if volume < 0:
                    return False
            
            # 买卖盘价格顺序检查
            if not all(level2.bid_prices[i] >= level2.bid_prices[i+1] for i in range(4)):
                return False
            
            if not all(level2.ask_prices[i] <= level2.ask_prices[i+1] for i in range(4)):
                return False
            
            return True
            
        except Exception:
            return False
    
    def _validate_price_data(self, price: PriceData) -> bool:
        """验证价格数据"""
        try:
            # 基本字段检查
            if not price.symbol or not price.timestamp:
                return False
            
            # OHLC价格检查
            prices = [price.open, price.high, price.low, price.close]
            if any(p <= 0 or p > 10000 for p in prices):
                return False
            
            # OHLC逻辑关系检查
            if not (price.low <= price.open <= price.high and 
                   price.low <= price.close <= price.high):
                return False
            
            # 成交量和成交额检查
            if price.volume < 0 or price.amount < 0:
                return False
            
            return True
            
        except Exception:
            return False
    
    async def calculate_quality_metrics(self, symbol: str) -> QualityMetrics:
        """计算数据质量指标"""
        try:
            buffer = self.data_buffers.get(symbol)
            if not buffer:
                return QualityMetrics()
            
            metrics = QualityMetrics()
            
            # 计算完整性（基于数据量）
            expected_ticks_per_minute = 20  # 假设每分钟20个tick
            minutes_elapsed = 60  # 检查最近1小时
            expected_total = expected_ticks_per_minute * minutes_elapsed
            actual_total = len(buffer.tick_buffer)
            metrics.completeness = min(actual_total / expected_total, 1.0) if expected_total > 0 else 0.0
            
            # 计算及时性（基于最新数据时间）
            if buffer.last_update_time:
                time_diff = (datetime.now() - buffer.last_update_time).total_seconds()
                metrics.timeliness = max(0.0, 1.0 - time_diff / 300)  # 5分钟内为满分
            
            # 计算准确性（基于数据验证通过率）
            total_data = len(buffer.tick_buffer) + len(buffer.level2_buffer) + len(buffer.price_buffer)
            if total_data > 0:
                # 简化计算，假设通过验证的数据都是准确的
                metrics.accuracy = 1.0 - (self.stats['invalid_data_count'] / max(total_data, 1))
                metrics.accuracy = max(0.0, metrics.accuracy)
            
            # 计算一致性（基于价格连续性）
            if len(buffer.tick_buffer) >= 2:
                price_changes = []
                for i in range(1, len(buffer.tick_buffer)):
                    prev_price = buffer.tick_buffer[i-1].price
                    curr_price = buffer.tick_buffer[i].price
                    change_pct = abs(curr_price - prev_price) / prev_price
                    price_changes.append(change_pct)
                
                # 如果价格变化过于剧烈，认为一致性较差
                avg_change = np.mean(price_changes) if price_changes else 0
                metrics.consistency = max(0.0, 1.0 - avg_change * 100)  # 1%变化扣1分
            
            # 计算综合评分
            weights = [0.3, 0.3, 0.2, 0.2]  # 完整性、及时性、准确性、一致性权重
            scores = [metrics.completeness, metrics.timeliness, metrics.accuracy, metrics.consistency]
            metrics.overall_score = sum(w * s for w, s in zip(weights, scores))
            
            # 确定质量等级
            if metrics.overall_score >= 0.9:
                metrics.quality_level = DataQuality.EXCELLENT
            elif metrics.overall_score >= 0.8:
                metrics.quality_level = DataQuality.GOOD
            elif metrics.overall_score >= 0.6:
                metrics.quality_level = DataQuality.FAIR
            elif metrics.overall_score >= 0.4:
                metrics.quality_level = DataQuality.POOR
            else:
                metrics.quality_level = DataQuality.INVALID
            
            # 更新缓冲区质量指标
            buffer.quality_metrics = metrics
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"计算质量指标失败: {e}")
            return QualityMetrics()
    
    async def start_quality_monitor(self):
        """启动数据质量监控"""
        while self.is_running:
            try:
                for symbol in self.data_buffers.keys():
                    metrics = await self.calculate_quality_metrics(symbol)
                    
                    # 触发质量回调
                    for callback in self.quality_callbacks:
                        try:
                            await asyncio.create_task(callback(symbol, metrics))
                        except Exception as e:
                            self.logger.error(f"质量回调执行失败: {e}")
                    
                    # 记录质量警告
                    if metrics.quality_level in [DataQuality.POOR, DataQuality.INVALID]:
                        self.logger.warning(f"数据质量警告 {symbol}: {metrics.quality_level.value}, 评分: {metrics.overall_score:.2f}")
                
                await asyncio.sleep(self.quality_check_interval)
                
            except Exception as e:
                self.logger.error(f"质量监控异常: {e}")
                await asyncio.sleep(self.quality_check_interval)
    
    def get_latest_tick(self, symbol: str) -> Optional[TickData]:
        """获取最新tick数据"""
        buffer = self.data_buffers.get(symbol)
        if buffer and buffer.tick_buffer:
            return buffer.tick_buffer[-1]
        return None
    
    def get_latest_level2(self, symbol: str) -> Optional[Level2Data]:
        """获取最新Level-2数据"""
        buffer = self.data_buffers.get(symbol)
        if buffer and buffer.level2_buffer:
            return buffer.level2_buffer[-1]
        return None
    
    def get_latest_price(self, symbol: str) -> Optional[PriceData]:
        """获取最新价格数据"""
        buffer = self.data_buffers.get(symbol)
        if buffer and buffer.price_buffer:
            return buffer.price_buffer[-1]
        return None
    
    def get_tick_history(self, symbol: str, minutes: int = 60) -> List[TickData]:
        """获取历史tick数据"""
        buffer = self.data_buffers.get(symbol)
        if not buffer:
            return []
        
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        return [tick for tick in buffer.tick_buffer if tick.timestamp >= cutoff_time]
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.stats.copy()
        stats['active_symbols'] = len(self.data_buffers)
        stats['total_connections'] = len(self.connections)
        stats['is_running'] = self.is_running
        
        # 计算平均质量评分
        if self.data_buffers:
            quality_scores = [buffer.quality_metrics.overall_score for buffer in self.data_buffers.values()]
            stats['avg_quality_score'] = np.mean(quality_scores) if quality_scores else 0.0
        else:
            stats['avg_quality_score'] = 0.0
        
        return stats
    
    async def start(self):
        """启动实时数据处理器"""
        self.is_running = True
        self.logger.info("实时数据处理器已启动")
        
        # 启动质量监控任务
        asyncio.create_task(self.start_quality_monitor())
    
    async def stop(self):
        """停止实时数据处理器"""
        self.is_running = False
        
        # 关闭所有连接
        for connection in self.connections.values():
            try:
                if hasattr(connection, 'close'):
                    await connection.close()
            except Exception as e:
                self.logger.error(f"关闭连接失败: {e}")
        
        self.connections.clear()
        self.logger.info("实时数据处理器已停止")
    
    def clear_buffer(self, symbol: str):
        """清空指定股票的缓冲区"""
        if symbol in self.data_buffers:
            buffer = self.data_buffers[symbol]
            buffer.tick_buffer.clear()
            buffer.level2_buffer.clear()
            buffer.price_buffer.clear()
            self.logger.info(f"已清空 {symbol} 的数据缓冲区")
    
    def clear_all_buffers(self):
        """清空所有缓冲区"""
        for symbol in list(self.data_buffers.keys()):
            self.clear_buffer(symbol)
        self.logger.info("已清空所有数据缓冲区")