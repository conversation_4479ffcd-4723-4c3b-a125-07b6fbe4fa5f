"""
实时数据处理引擎功能演示
展示秒级tick数据处理、技术指标计算、订单簿分析、流动性评估和异常检测的完整功能
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import random
from dataclasses import dataclass, field
from collections import deque
from enum import Enum


# ==================== 数据结构定义 ====================

@dataclass
class TickData:
    """Tick数据结构"""
    symbol: str
    timestamp: datetime
    price: float
    volume: int
    amount: float
    direction: str  # 'B'买入, 'S'卖出, 'N'中性
    bid_price: Optional[float] = None
    ask_price: Optional[float] = None
    bid_volume: Optional[int] = None
    ask_volume: Optional[int] = None


@dataclass
class Level2Data:
    """Level-2数据结构"""
    symbol: str
    timestamp: datetime
    bid_prices: List[float]  # 5档买价
    bid_volumes: List[int]   # 5档买量
    ask_prices: List[float]  # 5档卖价
    ask_volumes: List[int]   # 5档卖量
    total_bid_volume: int
    total_ask_volume: int


@dataclass
class TechnicalIndicator:
    """技术指标"""
    name: str
    value: float
    timestamp: datetime
    period: int = 0
    params: Dict[str, Any] = field(default_factory=dict)


@dataclass
class OrderBookSnapshot:
    """订单簿快照"""
    symbol: str
    timestamp: datetime
    bid_prices: List[float]
    bid_volumes: List[int]
    ask_prices: List[float]
    ask_volumes: List[int]
    spread: float = 0.0
    mid_price: float = 0.0
    order_imbalance: float = 0.0


@dataclass
class LiquidityMetrics:
    """流动性指标"""
    symbol: str
    timestamp: datetime
    bid_ask_spread: float = 0.0
    market_depth: float = 0.0
    liquidity_score: float = 0.0
    impact_cost: float = 0.0


@dataclass
class AnomalyEvent:
    """异常事件"""
    symbol: str
    timestamp: datetime
    anomaly_type: str
    severity: str
    description: str
    confidence: float = 0.0


# ==================== 核心处理引擎 ====================

class RealtimeDataProcessor:
    """实时数据处理器 - 演示版本"""
    
    def __init__(self):
        # 数据缓冲区
        self.tick_buffers: Dict[str, deque] = {}
        self.level2_buffers: Dict[str, deque] = {}
        self.indicator_buffers: Dict[str, Dict[str, deque]] = {}
        
        # 统计信息
        self.stats = {
            'total_ticks_processed': 0,
            'total_level2_processed': 0,
            'total_indicators_calculated': 0,
            'total_anomalies_detected': 0,
            'processing_latency_ms': 0.0,
            'last_update_time': None
        }
        
        self.logger = logging.getLogger(__name__)
        print("✅ 实时数据处理器初始化完成")
    
    async def process_tick_data(self, tick: TickData) -> Dict[str, Any]:
        """处理tick数据 - 子任务1: 实现秒级tick数据处理管道"""
        start_time = datetime.now()
        
        try:
            symbol = tick.symbol
            
            # 创建缓冲区
            if symbol not in self.tick_buffers:
                self.tick_buffers[symbol] = deque(maxlen=3600)  # 保存1小时数据
                self.indicator_buffers[symbol] = {}
            
            # 存储tick数据
            self.tick_buffers[symbol].append(tick)
            
            # 计算实时指标
            indicators = await self._calculate_realtime_indicators(symbol)
            
            # 检测异常
            anomalies = self._detect_tick_anomalies(symbol, tick)
            
            # 更新统计
            self.stats['total_ticks_processed'] += 1
            self.stats['processing_latency_ms'] = (datetime.now() - start_time).total_seconds() * 1000
            self.stats['last_update_time'] = datetime.now()
            
            return {
                'symbol': symbol,
                'processed': True,
                'indicators': indicators,
                'anomalies': anomalies,
                'buffer_size': len(self.tick_buffers[symbol])
            }
            
        except Exception as e:
            self.logger.error(f"处理tick数据失败: {e}")
            return {'symbol': tick.symbol, 'processed': False, 'error': str(e)}
    
    async def _calculate_realtime_indicators(self, symbol: str) -> Dict[str, TechnicalIndicator]:
        """子任务2: 构建实时技术指标计算引擎"""
        indicators = {}
        
        if symbol not in self.tick_buffers:
            return indicators
        
        ticks = list(self.tick_buffers[symbol])
        if len(ticks) < 10:
            return indicators
        
        prices = [tick.price for tick in ticks]
        volumes = [tick.volume for tick in ticks]
        current_time = datetime.now()
        
        # 移动平均线
        for period in [5, 10, 20]:
            if len(prices) >= period:
                sma = np.mean(prices[-period:])
                indicators[f'SMA_{period}'] = TechnicalIndicator(
                    name=f'SMA_{period}',
                    value=sma,
                    timestamp=current_time,
                    period=period
                )
        
        # RSI指标
        if len(prices) >= 14:
            rsi = self._calculate_rsi(prices, 14)
            indicators['RSI_14'] = TechnicalIndicator(
                name='RSI_14',
                value=rsi,
                timestamp=current_time,
                period=14
            )
        
        # 成交量指标
        if len(volumes) >= 10:
            volume_sma = np.mean(volumes[-10:])
            volume_ratio = volumes[-1] / volume_sma if volume_sma > 0 else 1.0
            indicators['VOLUME_RATIO'] = TechnicalIndicator(
                name='VOLUME_RATIO',
                value=volume_ratio,
                timestamp=current_time
            )
        
        # 价格动量
        if len(prices) >= 20:
            momentum = (prices[-1] - prices[-20]) / prices[-20] * 100
            indicators['MOMENTUM_20'] = TechnicalIndicator(
                name='MOMENTUM_20',
                value=momentum,
                timestamp=current_time,
                period=20
            )
        
        # 存储指标历史
        for name, indicator in indicators.items():
            if name not in self.indicator_buffers[symbol]:
                self.indicator_buffers[symbol][name] = deque(maxlen=100)
            self.indicator_buffers[symbol][name].append(indicator)
        
        self.stats['total_indicators_calculated'] += len(indicators)
        return indicators
    
    def _calculate_rsi(self, prices: List[float], period: int = 14) -> float:
        """计算RSI指标"""
        if len(prices) < period + 1:
            return 50.0
        
        deltas = [prices[i] - prices[i-1] for i in range(1, len(prices))]
        gains = [d if d > 0 else 0 for d in deltas]
        losses = [-d if d < 0 else 0 for d in deltas]
        
        if len(gains) < period:
            return 50.0
        
        avg_gain = np.mean(gains[-period:])
        avg_loss = np.mean(losses[-period:])
        
        if avg_loss == 0:
            return 100.0
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    async def process_level2_data(self, level2: Level2Data) -> Dict[str, Any]:
        """处理Level-2数据"""
        try:
            symbol = level2.symbol
            
            # 创建缓冲区
            if symbol not in self.level2_buffers:
                self.level2_buffers[symbol] = deque(maxlen=1800)  # 30分钟数据
            
            # 存储Level-2数据
            self.level2_buffers[symbol].append(level2)
            
            # 解析订单簿
            snapshot = self._parse_orderbook(level2)
            
            # 分析流动性
            liquidity = self._analyze_liquidity(symbol, snapshot)
            
            # 检测大单
            large_orders = self._detect_large_orders(symbol, level2)
            
            self.stats['total_level2_processed'] += 1
            
            return {
                'symbol': symbol,
                'processed': True,
                'orderbook': snapshot,
                'liquidity': liquidity,
                'large_orders': large_orders
            }
            
        except Exception as e:
            self.logger.error(f"处理Level-2数据失败: {e}")
            return {'symbol': level2.symbol, 'processed': False, 'error': str(e)}
    
    def _parse_orderbook(self, level2: Level2Data) -> OrderBookSnapshot:
        """子任务3: 编写订单簿分析算法"""
        snapshot = OrderBookSnapshot(
            symbol=level2.symbol,
            timestamp=level2.timestamp,
            bid_prices=level2.bid_prices.copy(),
            bid_volumes=level2.bid_volumes.copy(),
            ask_prices=level2.ask_prices.copy(),
            ask_volumes=level2.ask_volumes.copy()
        )
        
        # 计算买卖价差
        if level2.bid_prices and level2.ask_prices:
            snapshot.spread = level2.ask_prices[0] - level2.bid_prices[0]
            snapshot.mid_price = (level2.bid_prices[0] + level2.ask_prices[0]) / 2
        
        # 计算订单不平衡度
        total_volume = level2.total_bid_volume + level2.total_ask_volume
        if total_volume > 0:
            snapshot.order_imbalance = (level2.total_bid_volume - level2.total_ask_volume) / total_volume
        
        return snapshot
    
    def _analyze_liquidity(self, symbol: str, snapshot: OrderBookSnapshot) -> LiquidityMetrics:
        """子任务3: 流动性评估算法"""
        metrics = LiquidityMetrics(
            symbol=symbol,
            timestamp=snapshot.timestamp,
            bid_ask_spread=snapshot.spread
        )
        
        # 计算市场深度（前3档总金额）
        if len(snapshot.bid_prices) >= 3 and len(snapshot.ask_prices) >= 3:
            bid_depth = sum(p * v for p, v in zip(snapshot.bid_prices[:3], snapshot.bid_volumes[:3]))
            ask_depth = sum(p * v for p, v in zip(snapshot.ask_prices[:3], snapshot.ask_volumes[:3]))
            metrics.market_depth = bid_depth + ask_depth
        
        # 计算流动性评分
        if metrics.bid_ask_spread > 0 and metrics.market_depth > 0:
            # 简化的流动性评分：深度越大、价差越小，流动性越好
            spread_score = max(0, 100 - metrics.bid_ask_spread * 1000)  # 价差评分
            depth_score = min(100, metrics.market_depth / 100000)       # 深度评分
            metrics.liquidity_score = (spread_score + depth_score) / 2
        
        # 估算冲击成本
        if snapshot.ask_volumes and snapshot.ask_prices:
            # 假设买入1000股的冲击成本
            target_volume = 1000
            remaining = target_volume
            total_cost = 0
            
            for i, (price, volume) in enumerate(zip(snapshot.ask_prices, snapshot.ask_volumes)):
                if remaining <= 0:
                    break
                buy_volume = min(remaining, volume)
                total_cost += buy_volume * price
                remaining -= buy_volume
            
            if target_volume > remaining:
                avg_price = total_cost / (target_volume - remaining)
                metrics.impact_cost = (avg_price - snapshot.mid_price) / snapshot.mid_price * 100
        
        return metrics
    
    def _detect_large_orders(self, symbol: str, level2: Level2Data) -> List[Dict[str, Any]]:
        """检测大单"""
        large_orders = []
        threshold = 100000  # 10万元为大单阈值
        
        # 检查买盘大单
        for i, (price, volume) in enumerate(zip(level2.bid_prices, level2.bid_volumes)):
            amount = price * volume
            if amount >= threshold:
                large_orders.append({
                    'side': 'BUY',
                    'level': i + 1,
                    'price': price,
                    'volume': volume,
                    'amount': amount,
                    'timestamp': level2.timestamp
                })
        
        # 检查卖盘大单
        for i, (price, volume) in enumerate(zip(level2.ask_prices, level2.ask_volumes)):
            amount = price * volume
            if amount >= threshold:
                large_orders.append({
                    'side': 'SELL',
                    'level': i + 1,
                    'price': price,
                    'volume': volume,
                    'amount': amount,
                    'timestamp': level2.timestamp
                })
        
        return large_orders
    
    def _detect_tick_anomalies(self, symbol: str, tick: TickData) -> List[AnomalyEvent]:
        """子任务4: 实现实时异常检测和数据清洗"""
        anomalies = []
        
        if symbol not in self.tick_buffers:
            return anomalies
        
        ticks = list(self.tick_buffers[symbol])
        if len(ticks) < 20:
            return anomalies
        
        # 价格异常检测
        recent_prices = [t.price for t in ticks[-20:]]
        current_price = tick.price
        
        mean_price = np.mean(recent_prices[:-1])
        std_price = np.std(recent_prices[:-1])
        
        if std_price > 0:
            z_score = abs(current_price - mean_price) / std_price
            if z_score > 3.0:  # 3倍标准差
                anomalies.append(AnomalyEvent(
                    symbol=symbol,
                    timestamp=tick.timestamp,
                    anomaly_type='PRICE_SPIKE',
                    severity='HIGH' if z_score > 5 else 'MEDIUM',
                    description=f'价格异常波动，Z-score: {z_score:.2f}',
                    confidence=min(z_score / 10, 1.0)
                ))
        
        # 成交量异常检测
        recent_volumes = [t.volume for t in ticks[-20:]]
        current_volume = tick.volume
        
        mean_volume = np.mean(recent_volumes[:-1])
        std_volume = np.std(recent_volumes[:-1])
        
        if std_volume > 0:
            z_score = abs(current_volume - mean_volume) / std_volume
            if z_score > 3.0:
                anomaly_type = 'VOLUME_SURGE' if current_volume > mean_volume else 'VOLUME_DRY'
                anomalies.append(AnomalyEvent(
                    symbol=symbol,
                    timestamp=tick.timestamp,
                    anomaly_type=anomaly_type,
                    severity='HIGH' if z_score > 5 else 'MEDIUM',
                    description=f'成交量异常，Z-score: {z_score:.2f}',
                    confidence=min(z_score / 10, 1.0)
                ))
        
        # 数据质量检查
        if tick.price <= 0:
            anomalies.append(AnomalyEvent(
                symbol=symbol,
                timestamp=tick.timestamp,
                anomaly_type='DATA_ERROR',
                severity='CRITICAL',
                description='价格数据无效（小于等于0）',
                confidence=1.0
            ))
        
        if tick.volume < 0:
            anomalies.append(AnomalyEvent(
                symbol=symbol,
                timestamp=tick.timestamp,
                anomaly_type='DATA_ERROR',
                severity='HIGH',
                description='成交量数据无效（小于0）',
                confidence=1.0
            ))
        
        if anomalies:
            self.stats['total_anomalies_detected'] += len(anomalies)
        
        return anomalies
    
    def get_latest_indicators(self, symbol: str) -> Dict[str, TechnicalIndicator]:
        """获取最新技术指标"""
        if symbol not in self.indicator_buffers:
            return {}
        
        latest_indicators = {}
        for name, indicator_buffer in self.indicator_buffers[symbol].items():
            if indicator_buffer:
                latest_indicators[name] = indicator_buffer[-1]
        
        return latest_indicators
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.stats.copy()
        stats.update({
            'active_symbols': len(self.tick_buffers),
            'total_data_points': sum(len(buffer) for buffer in self.tick_buffers.values()),
            'indicator_types': sum(len(indicators) for indicators in self.indicator_buffers.values())
        })
        return stats


# ==================== 数据生成器 ====================

class AdvancedDataGenerator:
    """高级数据生成器"""
    
    def __init__(self, symbol: str = "000001.SZ"):
        self.symbol = symbol
        self.base_price = 10.0
        self.current_price = self.base_price
        self.current_time = datetime.now()
        
    def generate_realistic_ticks(self, count: int = 100) -> List[TickData]:
        """生成更真实的tick数据"""
        ticks = []
        
        for i in range(count):
            # 模拟盘中不同时段的特征
            hour = (self.current_time.hour + i // 60) % 24
            
            # 开盘和收盘时段波动更大
            if hour in [9, 10, 14, 15]:
                volatility = 0.002  # 0.2%
            else:
                volatility = 0.001  # 0.1%
            
            # 价格随机游走
            price_change = random.gauss(0, volatility)
            self.current_price = max(0.01, self.current_price * (1 + price_change))
            
            # 成交量模拟（开盘收盘时段更大）
            if hour in [9, 10, 14, 15]:
                base_volume = random.randint(1000, 20000)
            else:
                base_volume = random.randint(100, 5000)
            
            # 偶尔出现大单
            if random.random() < 0.05:  # 5%概率
                base_volume *= random.randint(5, 20)
            
            tick = TickData(
                symbol=self.symbol,
                timestamp=self.current_time + timedelta(seconds=i),
                price=round(self.current_price, 2),
                volume=base_volume,
                amount=round(self.current_price * base_volume, 2),
                direction=random.choice(['B', 'S', 'N']),
                bid_price=round(self.current_price - 0.01, 2),
                ask_price=round(self.current_price + 0.01, 2),
                bid_volume=random.randint(1000, 10000),
                ask_volume=random.randint(1000, 10000)
            )
            
            ticks.append(tick)
        
        return ticks
    
    def generate_level2_data(self, count: int = 50) -> List[Level2Data]:
        """生成Level-2数据"""
        level2_list = []
        
        for i in range(count):
            # 生成5档买卖盘
            bid_prices = []
            ask_prices = []
            bid_volumes = []
            ask_volumes = []
            
            base_bid = round(self.current_price - 0.01, 2)
            base_ask = round(self.current_price + 0.01, 2)
            
            for j in range(5):
                bid_price = round(base_bid - j * 0.01, 2)
                ask_price = round(base_ask + j * 0.01, 2)
                
                # 第一档量通常最大
                if j == 0:
                    bid_volume = random.randint(5000, 50000)
                    ask_volume = random.randint(5000, 50000)
                else:
                    bid_volume = random.randint(1000, 20000)
                    ask_volume = random.randint(1000, 20000)
                
                bid_prices.append(bid_price)
                ask_prices.append(ask_price)
                bid_volumes.append(bid_volume)
                ask_volumes.append(ask_volume)
            
            level2 = Level2Data(
                symbol=self.symbol,
                timestamp=self.current_time + timedelta(seconds=i * 2),
                bid_prices=bid_prices,
                bid_volumes=bid_volumes,
                ask_prices=ask_prices,
                ask_volumes=ask_volumes,
                total_bid_volume=sum(bid_volumes),
                total_ask_volume=sum(ask_volumes)
            )
            
            level2_list.append(level2)
            
            # 价格微调
            price_change = random.gauss(0, 0.0005)
            self.current_price = max(0.01, self.current_price * (1 + price_change))
        
        return level2_list


# ==================== 演示程序 ====================

async def demonstrate_realtime_processing():
    """演示实时数据处理功能"""
    print("🚀 实时数据处理引擎功能演示")
    print("="*60)
    
    # 初始化处理器
    processor = RealtimeDataProcessor()
    data_generator = AdvancedDataGenerator("000001.SZ")
    
    print("\n📊 子任务1: 秒级tick数据处理管道")
    print("-" * 40)
    
    # 生成并处理tick数据
    ticks = data_generator.generate_realistic_ticks(100)
    print(f"生成 {len(ticks)} 个tick数据")
    
    processed_results = []
    for i, tick in enumerate(ticks):
        result = await processor.process_tick_data(tick)
        processed_results.append(result)
        
        # 每20个tick显示一次进度
        if (i + 1) % 20 == 0:
            print(f"已处理: {i + 1}/{len(ticks)} ticks")
    
    print(f"✅ Tick数据处理完成，成功率: {sum(1 for r in processed_results if r['processed'])}/{len(processed_results)}")
    
    print("\n📈 子任务2: 实时技术指标计算引擎")
    print("-" * 40)
    
    # 显示最新技术指标
    latest_indicators = processor.get_latest_indicators("000001.SZ")
    print(f"计算的技术指标数量: {len(latest_indicators)}")
    
    for name, indicator in latest_indicators.items():
        print(f"  {name}: {indicator.value:.4f}")
    
    print("\n📋 子任务3: 订单簿分析和流动性评估")
    print("-" * 40)
    
    # 生成并处理Level-2数据
    level2_data = data_generator.generate_level2_data(30)
    print(f"生成 {len(level2_data)} 个Level-2数据")
    
    level2_results = []
    for level2 in level2_data:
        result = await processor.process_level2_data(level2)
        level2_results.append(result)
    
    # 显示最新的订单簿和流动性分析
    if level2_results and level2_results[-1]['processed']:
        latest_result = level2_results[-1]
        orderbook = latest_result['orderbook']
        liquidity = latest_result['liquidity']
        large_orders = latest_result['large_orders']
        
        print(f"✅ Level-2数据处理完成")
        print(f"  买卖价差: {orderbook.spread:.4f}")
        print(f"  中间价: {orderbook.mid_price:.4f}")
        print(f"  订单不平衡度: {orderbook.order_imbalance:.4f}")
        print(f"  流动性评分: {liquidity.liquidity_score:.2f}")
        print(f"  市场深度: {liquidity.market_depth:.2f}")
        print(f"  冲击成本: {liquidity.impact_cost:.4f}%")
        print(f"  检测到大单: {len(large_orders)} 个")
        
        if large_orders:
            print("  大单详情:")
            for order in large_orders[:3]:  # 显示前3个
                print(f"    {order['side']} {order['volume']} @ {order['price']:.2f} (金额: {order['amount']:.0f})")
    
    print("\n🔍 子任务4: 实时异常检测和数据清洗")
    print("-" * 40)
    
    # 统计异常检测结果
    total_anomalies = 0
    anomaly_types = {}
    
    for result in processed_results:
        if 'anomalies' in result:
            anomalies = result['anomalies']
            total_anomalies += len(anomalies)
            
            for anomaly in anomalies:
                anomaly_type = anomaly.anomaly_type
                if anomaly_type not in anomaly_types:
                    anomaly_types[anomaly_type] = 0
                anomaly_types[anomaly_type] += 1
    
    print(f"✅ 异常检测完成")
    print(f"  检测到异常: {total_anomalies} 个")
    print(f"  异常类型分布:")
    for anomaly_type, count in anomaly_types.items():
        print(f"    {anomaly_type}: {count} 个")
    
    # 生成一个明显的异常数据进行演示
    print("\n  演示异常检测:")
    anomaly_tick = TickData(
        symbol="000001.SZ",
        timestamp=datetime.now(),
        price=data_generator.current_price * 2.0,  # 价格翻倍
        volume=100000,  # 大成交量
        amount=data_generator.current_price * 2.0 * 100000,
        direction='B'
    )
    
    anomaly_result = await processor.process_tick_data(anomaly_tick)
    if anomaly_result['anomalies']:
        print(f"    检测到 {len(anomaly_result['anomalies'])} 个异常:")
        for anomaly in anomaly_result['anomalies']:
            print(f"      {anomaly.anomaly_type}: {anomaly.description} (置信度: {anomaly.confidence:.2f})")
    
    print("\n📊 性能统计")
    print("-" * 40)
    
    stats = processor.get_statistics()
    print(f"  处理的tick数据: {stats['total_ticks_processed']}")
    print(f"  处理的Level-2数据: {stats['total_level2_processed']}")
    print(f"  计算的技术指标: {stats['total_indicators_calculated']}")
    print(f"  检测的异常: {stats['total_anomalies_detected']}")
    print(f"  平均处理延迟: {stats['processing_latency_ms']:.3f} 毫秒")
    print(f"  活跃股票数: {stats['active_symbols']}")
    print(f"  总数据点: {stats['total_data_points']}")
    
    print("\n" + "="*60)
    print("🎉 实时数据处理引擎演示完成！")
    print("所有子任务功能验证成功：")
    print("  ✅ 秒级tick数据处理管道")
    print("  ✅ 实时技术指标计算引擎")
    print("  ✅ 订单簿分析和流动性评估算法")
    print("  ✅ 实时异常检测和数据清洗")
    print("="*60)


if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    # 运行演示
    asyncio.run(demonstrate_realtime_processing())