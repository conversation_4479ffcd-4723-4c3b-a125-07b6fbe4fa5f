"""
连接管理器
处理WebSocket连接、断线重连和数据流管理
"""
import asyncio
import logging
import json
from typing import Dict, List, Optional, Callable, Any, Set
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import websockets
import aiohttp
from websockets.exceptions import ConnectionClosed, WebSocketException

from ..collectors.base import TickData, Level2Data, PriceData


class ConnectionStatus(str, Enum):
    """连接状态"""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    RECONNECTING = "reconnecting"
    ERROR = "error"


@dataclass
class ConnectionConfig:
    """连接配置"""
    url: str
    headers: Dict[str, str] = None
    heartbeat_interval: int = 30  # 心跳间隔(秒)
    reconnect_interval: int = 5   # 重连间隔(秒)
    max_reconnect_attempts: int = 10  # 最大重连次数
    timeout: int = 30             # 连接超时(秒)
    ping_interval: int = 20       # ping间隔(秒)
    ping_timeout: int = 10        # ping超时(秒)


@dataclass
class ConnectionStats:
    """连接统计"""
    connection_id: str
    status: ConnectionStatus
    connect_time: Optional[datetime] = None
    last_message_time: Optional[datetime] = None
    total_messages: int = 0
    reconnect_count: int = 0
    error_count: int = 0
    last_error: Optional[str] = None


class ConnectionManager:
    """连接管理器"""
    
    def __init__(self):
        self.connections: Dict[str, websockets.WebSocketServerProtocol] = {}
        self.connection_configs: Dict[str, ConnectionConfig] = {}
        self.connection_stats: Dict[str, ConnectionStats] = {}
        self.subscriptions: Dict[str, Set[str]] = {}  # connection_id -> symbols
        
        # 回调函数
        self.message_callbacks: Dict[str, List[Callable]] = {}
        self.status_callbacks: List[Callable[[str, ConnectionStatus], None]] = []
        
        # 控制标志
        self.is_running = False
        self.reconnect_tasks: Dict[str, asyncio.Task] = {}
        
        self.logger = logging.getLogger(__name__)
    
    def add_connection_config(self, connection_id: str, config: ConnectionConfig):
        """添加连接配置"""
        self.connection_configs[connection_id] = config
        self.connection_stats[connection_id] = ConnectionStats(
            connection_id=connection_id,
            status=ConnectionStatus.DISCONNECTED
        )
        self.message_callbacks[connection_id] = []
        self.subscriptions[connection_id] = set()
    
    def add_message_callback(self, connection_id: str, callback: Callable):
        """添加消息回调"""
        if connection_id in self.message_callbacks:
            self.message_callbacks[connection_id].append(callback)
    
    def add_status_callback(self, callback: Callable[[str, ConnectionStatus], None]):
        """添加状态变化回调"""
        self.status_callbacks.append(callback)
    
    def _update_connection_status(self, connection_id: str, status: ConnectionStatus, error: str = None):
        """更新连接状态"""
        if connection_id in self.connection_stats:
            stats = self.connection_stats[connection_id]
            old_status = stats.status
            stats.status = status
            
            if error:
                stats.last_error = error
                stats.error_count += 1
            
            # 触发状态回调
            if old_status != status:
                for callback in self.status_callbacks:
                    try:
                        asyncio.create_task(callback(connection_id, status))
                    except Exception as e:
                        self.logger.error(f"状态回调执行失败: {e}")
                
                self.logger.info(f"连接 {connection_id} 状态变更: {old_status.value} -> {status.value}")
    
    async def connect(self, connection_id: str) -> bool:
        """建立连接"""
        if connection_id not in self.connection_configs:
            self.logger.error(f"连接配置不存在: {connection_id}")
            return False
        
        config = self.connection_configs[connection_id]
        self._update_connection_status(connection_id, ConnectionStatus.CONNECTING)
        
        try:
            # 建立WebSocket连接
            websocket = await websockets.connect(
                config.url,
                extra_headers=config.headers or {},
                ping_interval=config.ping_interval,
                ping_timeout=config.ping_timeout,
                timeout=config.timeout
            )
            
            self.connections[connection_id] = websocket
            
            # 更新统计信息
            stats = self.connection_stats[connection_id]
            stats.connect_time = datetime.now()
            
            self._update_connection_status(connection_id, ConnectionStatus.CONNECTED)
            
            # 启动消息监听任务
            asyncio.create_task(self._listen_messages(connection_id))
            
            # 启动心跳任务
            asyncio.create_task(self._heartbeat_task(connection_id))
            
            self.logger.info(f"连接 {connection_id} 建立成功")
            return True
            
        except Exception as e:
            self._update_connection_status(connection_id, ConnectionStatus.ERROR, str(e))
            self.logger.error(f"连接 {connection_id} 建立失败: {e}")
            return False
    
    async def disconnect(self, connection_id: str):
        """断开连接"""
        if connection_id in self.connections:
            try:
                websocket = self.connections[connection_id]
                await websocket.close()
                del self.connections[connection_id]
                
                # 取消重连任务
                if connection_id in self.reconnect_tasks:
                    self.reconnect_tasks[connection_id].cancel()
                    del self.reconnect_tasks[connection_id]
                
                self._update_connection_status(connection_id, ConnectionStatus.DISCONNECTED)
                self.logger.info(f"连接 {connection_id} 已断开")
                
            except Exception as e:
                self.logger.error(f"断开连接 {connection_id} 失败: {e}")
    
    async def _listen_messages(self, connection_id: str):
        """监听消息"""
        websocket = self.connections.get(connection_id)
        if not websocket:
            return
        
        try:
            async for message in websocket:
                try:
                    # 更新统计信息
                    stats = self.connection_stats[connection_id]
                    stats.last_message_time = datetime.now()
                    stats.total_messages += 1
                    
                    # 解析消息
                    if isinstance(message, str):
                        data = json.loads(message)
                    else:
                        data = message
                    
                    # 触发消息回调
                    for callback in self.message_callbacks.get(connection_id, []):
                        try:
                            await asyncio.create_task(callback(data))
                        except Exception as e:
                            self.logger.error(f"消息回调执行失败: {e}")
                    
                except json.JSONDecodeError as e:
                    self.logger.warning(f"消息解析失败 {connection_id}: {e}")
                except Exception as e:
                    self.logger.error(f"处理消息失败 {connection_id}: {e}")
        
        except ConnectionClosed:
            self.logger.warning(f"连接 {connection_id} 被关闭")
            self._update_connection_status(connection_id, ConnectionStatus.DISCONNECTED)
            
            # 启动自动重连
            if self.is_running:
                await self._start_reconnect(connection_id)
        
        except WebSocketException as e:
            self.logger.error(f"WebSocket异常 {connection_id}: {e}")
            self._update_connection_status(connection_id, ConnectionStatus.ERROR, str(e))
            
            # 启动自动重连
            if self.is_running:
                await self._start_reconnect(connection_id)
        
        except Exception as e:
            self.logger.error(f"监听消息异常 {connection_id}: {e}")
            self._update_connection_status(connection_id, ConnectionStatus.ERROR, str(e))
    
    async def _heartbeat_task(self, connection_id: str):
        """心跳任务"""
        config = self.connection_configs.get(connection_id)
        if not config:
            return
        
        while connection_id in self.connections and self.is_running:
            try:
                websocket = self.connections[connection_id]
                
                # 发送心跳消息
                heartbeat_msg = {
                    "type": "heartbeat",
                    "timestamp": datetime.now().isoformat()
                }
                
                await websocket.send(json.dumps(heartbeat_msg))
                self.logger.debug(f"发送心跳 {connection_id}")
                
                await asyncio.sleep(config.heartbeat_interval)
                
            except ConnectionClosed:
                self.logger.warning(f"心跳发送失败，连接 {connection_id} 已关闭")
                break
            except Exception as e:
                self.logger.error(f"心跳任务异常 {connection_id}: {e}")
                break
    
    async def _start_reconnect(self, connection_id: str):
        """启动重连任务"""
        if connection_id in self.reconnect_tasks:
            return  # 重连任务已存在
        
        config = self.connection_configs.get(connection_id)
        if not config:
            return
        
        self.reconnect_tasks[connection_id] = asyncio.create_task(
            self._reconnect_loop(connection_id)
        )
    
    async def _reconnect_loop(self, connection_id: str):
        """重连循环"""
        config = self.connection_configs[connection_id]
        stats = self.connection_stats[connection_id]
        
        for attempt in range(config.max_reconnect_attempts):
            if not self.is_running:
                break
            
            self.logger.info(f"尝试重连 {connection_id} (第{attempt + 1}次)")
            self._update_connection_status(connection_id, ConnectionStatus.RECONNECTING)
            
            try:
                # 清理旧连接
                if connection_id in self.connections:
                    try:
                        await self.connections[connection_id].close()
                    except:
                        pass
                    del self.connections[connection_id]
                
                # 尝试重连
                if await self.connect(connection_id):
                    stats.reconnect_count += 1
                    self.logger.info(f"重连 {connection_id} 成功")
                    
                    # 重新订阅
                    await self._resubscribe(connection_id)
                    break
                else:
                    self.logger.warning(f"重连 {connection_id} 失败，等待重试")
                    await asyncio.sleep(config.reconnect_interval)
            
            except Exception as e:
                self.logger.error(f"重连异常 {connection_id}: {e}")
                await asyncio.sleep(config.reconnect_interval)
        
        else:
            # 重连次数用尽
            self.logger.error(f"重连 {connection_id} 失败，已达到最大重试次数")
            self._update_connection_status(connection_id, ConnectionStatus.ERROR, "重连失败")
        
        # 清理重连任务
        if connection_id in self.reconnect_tasks:
            del self.reconnect_tasks[connection_id]
    
    async def _resubscribe(self, connection_id: str):
        """重新订阅"""
        symbols = self.subscriptions.get(connection_id, set())
        if symbols:
            await self.subscribe_symbols(connection_id, list(symbols))
            self.logger.info(f"重新订阅 {connection_id}: {len(symbols)} 个股票")
    
    async def send_message(self, connection_id: str, message: Dict[str, Any]) -> bool:
        """发送消息"""
        websocket = self.connections.get(connection_id)
        if not websocket:
            self.logger.error(f"连接 {connection_id} 不存在")
            return False
        
        try:
            await websocket.send(json.dumps(message))
            return True
        except Exception as e:
            self.logger.error(f"发送消息失败 {connection_id}: {e}")
            return False
    
    async def subscribe_symbols(self, connection_id: str, symbols: List[str]) -> bool:
        """订阅股票"""
        message = {
            "type": "subscribe",
            "symbols": symbols,
            "timestamp": datetime.now().isoformat()
        }
        
        if await self.send_message(connection_id, message):
            # 更新订阅列表
            self.subscriptions[connection_id].update(symbols)
            self.logger.info(f"订阅股票 {connection_id}: {symbols}")
            return True
        
        return False
    
    async def unsubscribe_symbols(self, connection_id: str, symbols: List[str]) -> bool:
        """取消订阅股票"""
        message = {
            "type": "unsubscribe",
            "symbols": symbols,
            "timestamp": datetime.now().isoformat()
        }
        
        if await self.send_message(connection_id, message):
            # 更新订阅列表
            self.subscriptions[connection_id].difference_update(symbols)
            self.logger.info(f"取消订阅股票 {connection_id}: {symbols}")
            return True
        
        return False
    
    def get_connection_status(self, connection_id: str) -> Optional[ConnectionStatus]:
        """获取连接状态"""
        stats = self.connection_stats.get(connection_id)
        return stats.status if stats else None
    
    def get_connection_stats(self, connection_id: str) -> Optional[ConnectionStats]:
        """获取连接统计"""
        return self.connection_stats.get(connection_id)
    
    def get_all_stats(self) -> Dict[str, ConnectionStats]:
        """获取所有连接统计"""
        return self.connection_stats.copy()
    
    def is_connected(self, connection_id: str) -> bool:
        """检查连接状态"""
        return (connection_id in self.connections and 
                self.get_connection_status(connection_id) == ConnectionStatus.CONNECTED)
    
    def get_active_connections(self) -> List[str]:
        """获取活跃连接列表"""
        return [conn_id for conn_id, status in self.connection_stats.items() 
                if status.status == ConnectionStatus.CONNECTED]
    
    async def start(self):
        """启动连接管理器"""
        self.is_running = True
        self.logger.info("连接管理器已启动")
        
        # 连接所有配置的连接
        for connection_id in self.connection_configs.keys():
            await self.connect(connection_id)
    
    async def stop(self):
        """停止连接管理器"""
        self.is_running = False
        
        # 取消所有重连任务
        for task in self.reconnect_tasks.values():
            task.cancel()
        self.reconnect_tasks.clear()
        
        # 断开所有连接
        for connection_id in list(self.connections.keys()):
            await self.disconnect(connection_id)
        
        self.logger.info("连接管理器已停止")
    
    def get_summary(self) -> Dict[str, Any]:
        """获取连接摘要"""
        total_connections = len(self.connection_configs)
        active_connections = len(self.get_active_connections())
        
        total_messages = sum(stats.total_messages for stats in self.connection_stats.values())
        total_errors = sum(stats.error_count for stats in self.connection_stats.values())
        total_reconnects = sum(stats.reconnect_count for stats in self.connection_stats.values())
        
        return {
            'total_connections': total_connections,
            'active_connections': active_connections,
            'connection_rate': active_connections / total_connections if total_connections > 0 else 0,
            'total_messages': total_messages,
            'total_errors': total_errors,
            'total_reconnects': total_reconnects,
            'is_running': self.is_running
        }