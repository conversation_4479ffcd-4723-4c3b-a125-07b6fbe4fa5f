"""
实时数据处理引擎集成测试
测试秒级tick数据处理、技术指标计算、异常检测等功能
"""

import asyncio
import logging
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Any
import random

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

from qlib_trading_system.data.collectors.base import TickData, Level2Data, PriceData
from qlib_trading_system.data.processors.realtime_engine import RealtimeDataEngine, MarketMicrostructure, TechnicalIndicator
from qlib_trading_system.data.processors.anomaly_detector import RealtimeAnomalyDetector, AnomalyEvent
from qlib_trading_system.data.processors.liquidity_analyzer import LiquidityAnalyzer


class MockDataGenerator:
    """模拟数据生成器"""
    
    def __init__(self, symbol: str = "000001.SZ"):
        self.symbol = symbol
        self.base_price = 10.0
        self.current_price = self.base_price
        self.current_time = datetime.now()
        
    def generate_tick_data(self, count: int = 100) -> List[TickData]:
        """生成模拟tick数据"""
        ticks = []
        
        for i in range(count):
            # 模拟价格随机游走
            price_change = random.gauss(0, 0.001)  # 0.1%的标准差
            self.current_price = max(0.01, self.current_price * (1 + price_change))
            
            # 模拟成交量
            volume = random.randint(100, 10000)
            amount = self.current_price * volume
            
            # 模拟买卖方向
            direction = random.choice(['B', 'S', 'N'])
            
            tick = TickData(
                symbol=self.symbol,
                timestamp=self.current_time + timedelta(seconds=i),
                price=round(self.current_price, 2),
                volume=volume,
                amount=round(amount, 2),
                direction=direction,
                bid_price=round(self.current_price - 0.01, 2),
                ask_price=round(self.current_price + 0.01, 2),
                bid_volume=random.randint(1000, 5000),
                ask_volume=random.randint(1000, 5000)
            )
            
            ticks.append(tick)
        
        return ticks
    
    def generate_level2_data(self, count: int = 50) -> List[Level2Data]:
        """生成模拟Level-2数据"""
        level2_list = []
        
        for i in range(count):
            # 生成5档买卖盘数据
            bid_prices = []
            ask_prices = []
            bid_volumes = []
            ask_volumes = []
            
            base_bid = round(self.current_price - 0.01, 2)
            base_ask = round(self.current_price + 0.01, 2)
            
            for j in range(5):
                bid_prices.append(round(base_bid - j * 0.01, 2))
                ask_prices.append(round(base_ask + j * 0.01, 2))
                bid_volumes.append(random.randint(1000, 10000))
                ask_volumes.append(random.randint(1000, 10000))
            
            level2 = Level2Data(
                symbol=self.symbol,
                timestamp=self.current_time + timedelta(seconds=i * 2),
                bid_prices=bid_prices,
                bid_volumes=bid_volumes,
                ask_prices=ask_prices,
                ask_volumes=ask_volumes,
                total_bid_volume=sum(bid_volumes),
                total_ask_volume=sum(ask_volumes)
            )
            
            level2_list.append(level2)
            
            # 更新价格
            price_change = random.gauss(0, 0.0005)
            self.current_price = max(0.01, self.current_price * (1 + price_change))
        
        return level2_list
    
    def generate_anomaly_tick(self) -> TickData:
        """生成异常tick数据"""
        # 生成价格异常的tick
        anomaly_price = self.current_price * random.choice([0.5, 2.0])  # 50%跌幅或100%涨幅
        
        return TickData(
            symbol=self.symbol,
            timestamp=self.current_time,
            price=round(anomaly_price, 2),
            volume=random.randint(100, 1000),
            amount=round(anomaly_price * random.randint(100, 1000), 2),
            direction='B',
            bid_price=round(anomaly_price - 0.01, 2),
            ask_price=round(anomaly_price + 0.01, 2),
            bid_volume=1000,
            ask_volume=1000
        )


class TestRealtimeDataEngine:
    """实时数据处理引擎测试类"""
    
    async def test_tick_data_processing(self, engine, mock_data):
        """测试tick数据处理"""
        print("\n=== 测试tick数据处理 ===")
        
        # 生成测试数据
        ticks = mock_data.generate_tick_data(50)
        
        # 处理tick数据
        processed_count = 0
        for tick in ticks:
            microstructure = await engine.process_tick_data(tick)
            if microstructure:
                processed_count += 1
        
        print(f"处理tick数据: {processed_count}/{len(ticks)}")
        
        # 验证数据缓冲区
        buffer = engine.data_buffers.get("000001.SZ")
        assert buffer is not None, "数据缓冲区应该被创建"
        assert len(buffer.tick_buffer) > 0, "tick缓冲区应该有数据"
        
        # 验证统计信息
        stats = engine.get_statistics()
        assert stats['total_ticks_processed'] > 0, "应该有处理的tick数据"
        assert stats['active_symbols'] == 1, "应该有1个活跃股票"
        
        print(f"统计信息: {stats}")
        print("✓ tick数据处理测试通过")
    
    async def test_level2_data_processing(self, engine, mock_data):
        """测试Level-2数据处理"""
        print("\n=== 测试Level-2数据处理 ===")
        
        # 生成测试数据
        level2_list = mock_data.generate_level2_data(20)
        
        # 处理Level-2数据
        processed_count = 0
        for level2 in level2_list:
            snapshot = await engine.process_level2_data(level2)
            if snapshot:
                processed_count += 1
        
        print(f"处理Level-2数据: {processed_count}/{len(level2_list)}")
        
        # 验证数据缓冲区
        buffer = engine.data_buffers.get("000001.SZ")
        assert buffer is not None, "数据缓冲区应该被创建"
        assert len(buffer.level2_buffer) > 0, "Level-2缓冲区应该有数据"
        assert len(buffer.orderbook_snapshots) > 0, "订单簿快照应该有数据"
        
        print("✓ Level-2数据处理测试通过")
    
    async def test_technical_indicators(self, engine, mock_data):
        """测试技术指标计算"""
        print("\n=== 测试技术指标计算 ===")
        
        # 生成足够的数据用于指标计算
        ticks = mock_data.generate_tick_data(100)
        
        # 处理数据
        for tick in ticks:
            await engine.process_tick_data(tick)
        
        # 等待指标计算
        await asyncio.sleep(0.5)
        
        # 获取最新指标
        indicators = engine.get_latest_indicators("000001.SZ")
        
        print(f"计算的指标数量: {len(indicators)}")
        for name, indicator in indicators.items():
            print(f"  {name}: {indicator.value:.4f}")
        
        # 验证常见指标
        expected_indicators = ['SMA_5', 'SMA_10', 'SMA_20', 'EMA_5', 'RSI_14']
        for indicator_name in expected_indicators:
            if indicator_name in indicators:
                assert indicators[indicator_name].value > 0, f"{indicator_name}应该有有效值"
        
        print("✓ 技术指标计算测试通过")
    
    async def test_microstructure_analysis(self, engine, mock_data):
        """测试市场微观结构分析"""
        print("\n=== 测试市场微观结构分析 ===")
        
        # 生成数据
        ticks = mock_data.generate_tick_data(60)
        level2_list = mock_data.generate_level2_data(30)
        
        # 处理数据
        for tick in ticks:
            await engine.process_tick_data(tick)
        
        for level2 in level2_list:
            await engine.process_level2_data(level2)
        
        # 获取最新微观结构数据
        microstructure = engine.get_latest_microstructure("000001.SZ")
        
        assert microstructure is not None, "应该有微观结构数据"
        
        print(f"价格动量(1分钟): {microstructure.price_momentum_1min:.4f}")
        print(f"成交量爆发比率: {microstructure.volume_surge_ratio:.4f}")
        print(f"买卖价差: {microstructure.bid_ask_spread:.4f}")
        print(f"订单不平衡度: {microstructure.order_imbalance:.4f}")
        print(f"异常评分: {microstructure.overall_anomaly_score:.4f}")
        
        print("✓ 市场微观结构分析测试通过")
    
    async def test_anomaly_detection(self, engine, mock_data):
        """测试异常检测"""
        print("\n=== 测试异常检测 ===")
        
        # 创建异常检测器
        anomaly_detector = RealtimeAnomalyDetector()
        
        # 生成正常数据
        normal_ticks = mock_data.generate_tick_data(50)
        
        # 处理正常数据
        normal_anomaly_count = 0
        for tick in normal_ticks:
            is_valid, anomalies = anomaly_detector.process_tick_data(tick)
            normal_anomaly_count += len(anomalies)
        
        print(f"正常数据异常数量: {normal_anomaly_count}")
        
        # 生成异常数据
        anomaly_tick = mock_data.generate_anomaly_tick()
        is_valid, anomalies = anomaly_detector.process_tick_data(anomaly_tick)
        
        print(f"异常数据检测结果: 有效={is_valid}, 异常数量={len(anomalies)}")
        
        for anomaly in anomalies:
            print(f"  异常类型: {anomaly.anomaly_type.value}, 严重程度: {anomaly.severity.value}")
            print(f"  描述: {anomaly.description}")
        
        # 验证异常检测
        assert len(anomalies) > 0, "应该检测到异常"
        
        # 生成质量报告
        quality_report = anomaly_detector.generate_quality_report("000001.SZ")
        print(f"数据质量评分: {quality_report.overall_quality_score:.2f}")
        
        print("✓ 异常检测测试通过")
    
    async def test_liquidity_analysis(self, engine, mock_data):
        """测试流动性分析"""
        print("\n=== 测试流动性分析 ===")
        
        # 创建流动性分析器
        liquidity_analyzer = LiquidityAnalyzer()
        
        # 生成数据
        ticks = mock_data.generate_tick_data(100)
        level2_list = mock_data.generate_level2_data(50)
        
        # 添加数据到分析器
        for tick in ticks:
            liquidity_analyzer.add_tick_data(tick)
        
        for level2 in level2_list:
            snapshot = engine.level2_parser.parse_level2_data(level2)
            liquidity_analyzer.add_orderbook_data(snapshot)
        
        # 计算流动性指标
        metrics = liquidity_analyzer.calculate_liquidity_metrics("000001.SZ")
        
        if metrics:
            print(f"流动性评分: {metrics.liquidity_score:.2f}")
            print(f"流动性等级: {metrics.liquidity_level.value}")
            print(f"买卖价差: {metrics.bid_ask_spread:.4f}")
            print(f"市场深度: {metrics.market_depth:.2f}")
            print(f"冲击成本(5%): {metrics.impact_cost_5pct:.4f}")
            
            # 获取执行策略建议
            strategy = liquidity_analyzer.get_optimal_execution_strategy("000001.SZ", 10000)
            if strategy:
                print(f"推荐执行策略: {strategy['recommended_strategy']}")
                print(f"建议执行时间: {strategy['execution_time_minutes']}分钟")
        
        print("✓ 流动性分析测试通过")
    
    async def test_performance_metrics(self, engine, mock_data):
        """测试性能指标"""
        print("\n=== 测试性能指标 ===")
        
        # 生成大量数据测试性能
        ticks = mock_data.generate_tick_data(1000)
        
        start_time = datetime.now()
        
        # 批量处理数据
        for tick in ticks:
            await engine.process_tick_data(tick)
        
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        
        # 计算性能指标
        throughput = len(ticks) / processing_time  # 每秒处理的tick数
        avg_latency = processing_time / len(ticks) * 1000  # 平均延迟(毫秒)
        
        print(f"处理数据量: {len(ticks)} ticks")
        print(f"总处理时间: {processing_time:.2f} 秒")
        print(f"吞吐量: {throughput:.2f} ticks/秒")
        print(f"平均延迟: {avg_latency:.2f} 毫秒")
        
        # 获取引擎统计信息
        stats = engine.get_statistics()
        print(f"引擎统计: {stats}")
        
        # 性能要求验证
        assert throughput > 100, "吞吐量应该大于100 ticks/秒"
        assert avg_latency < 10, "平均延迟应该小于10毫秒"
        
        print("✓ 性能指标测试通过")
    
    async def test_callback_system(self, engine, mock_data):
        """测试回调系统"""
        print("\n=== 测试回调系统 ===")
        
        # 回调计数器
        callback_counts = {
            'tick': 0,
            'level2': 0,
            'indicator': 0,
            'anomaly': 0
        }
        
        # 定义回调函数
        def tick_callback(tick: TickData, microstructure: MarketMicrostructure):
            callback_counts['tick'] += 1
        
        def level2_callback(level2: Level2Data, snapshot):
            callback_counts['level2'] += 1
        
        def indicator_callback(symbol: str, indicators: Dict[str, TechnicalIndicator]):
            callback_counts['indicator'] += 1
        
        def anomaly_callback(symbol: str, microstructure: MarketMicrostructure):
            callback_counts['anomaly'] += 1
        
        # 注册回调
        engine.add_tick_callback(tick_callback)
        engine.add_level2_callback(level2_callback)
        engine.add_indicator_callback(indicator_callback)
        engine.add_anomaly_callback(anomaly_callback)
        
        # 生成数据
        ticks = mock_data.generate_tick_data(20)
        level2_list = mock_data.generate_level2_data(10)
        
        # 处理数据
        for tick in ticks:
            await engine.process_tick_data(tick)
        
        for level2 in level2_list:
            await engine.process_level2_data(level2)
        
        # 等待指标计算
        await asyncio.sleep(0.5)
        
        print(f"回调统计: {callback_counts}")
        
        # 验证回调
        assert callback_counts['tick'] > 0, "tick回调应该被触发"
        assert callback_counts['level2'] > 0, "level2回调应该被触发"
        
        print("✓ 回调系统测试通过")


async def run_integration_tests():
    """运行集成测试"""
    print("开始实时数据处理引擎集成测试...")
    
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 创建测试实例
    test_instance = TestRealtimeDataEngine()
    
    # 创建引擎和模拟数据
    engine = RealtimeDataEngine(
        max_symbols=10,
        indicator_update_interval=0.1,
        anomaly_check_interval=0.1
    )
    
    mock_data = MockDataGenerator("000001.SZ")
    
    try:
        await engine.start()
        
        # 运行所有测试
        await test_instance.test_tick_data_processing(engine, mock_data)
        await test_instance.test_level2_data_processing(engine, mock_data)
        await test_instance.test_technical_indicators(engine, mock_data)
        await test_instance.test_microstructure_analysis(engine, mock_data)
        await test_instance.test_anomaly_detection(engine, mock_data)
        await test_instance.test_liquidity_analysis(engine, mock_data)
        await test_instance.test_performance_metrics(engine, mock_data)
        await test_instance.test_callback_system(engine, mock_data)
        
        print("\n" + "="*50)
        print("🎉 所有集成测试通过！")
        print("="*50)
        
        # 显示最终统计
        final_stats = engine.get_statistics()
        print(f"最终统计信息:")
        for key, value in final_stats.items():
            print(f"  {key}: {value}")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        raise
    finally:
        await engine.stop()


if __name__ == "__main__":
    # 运行集成测试
    asyncio.run(run_integration_tests())