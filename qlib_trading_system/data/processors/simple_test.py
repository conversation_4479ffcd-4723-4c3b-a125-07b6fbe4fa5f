"""
实时数据处理引擎简化测试
测试核心功能而不依赖外部模块
"""

import asyncio
import logging
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Any
import random
from dataclasses import dataclass


@dataclass
class TickData:
    """简化的Tick数据"""
    symbol: str
    timestamp: datetime
    price: float
    volume: int
    amount: float
    direction: str
    bid_price: float = None
    ask_price: float = None
    bid_volume: int = None
    ask_volume: int = None


@dataclass
class Level2Data:
    """简化的Level-2数据"""
    symbol: str
    timestamp: datetime
    bid_prices: List[float]
    bid_volumes: List[int]
    ask_prices: List[float]
    ask_volumes: List[int]
    total_bid_volume: int
    total_ask_volume: int


class MockDataGenerator:
    """模拟数据生成器"""
    
    def __init__(self, symbol: str = "000001.SZ"):
        self.symbol = symbol
        self.base_price = 10.0
        self.current_price = self.base_price
        self.current_time = datetime.now()
        
    def generate_tick_data(self, count: int = 100) -> List[TickData]:
        """生成模拟tick数据"""
        ticks = []
        
        for i in range(count):
            # 模拟价格随机游走
            price_change = random.gauss(0, 0.001)  # 0.1%的标准差
            self.current_price = max(0.01, self.current_price * (1 + price_change))
            
            # 模拟成交量
            volume = random.randint(100, 10000)
            amount = self.current_price * volume
            
            # 模拟买卖方向
            direction = random.choice(['B', 'S', 'N'])
            
            tick = TickData(
                symbol=self.symbol,
                timestamp=self.current_time + timedelta(seconds=i),
                price=round(self.current_price, 2),
                volume=volume,
                amount=round(amount, 2),
                direction=direction,
                bid_price=round(self.current_price - 0.01, 2),
                ask_price=round(self.current_price + 0.01, 2),
                bid_volume=random.randint(1000, 5000),
                ask_volume=random.randint(1000, 5000)
            )
            
            ticks.append(tick)
        
        return ticks


class SimpleRealtimeEngine:
    """简化的实时数据处理引擎"""
    
    def __init__(self):
        self.tick_buffers = {}
        self.stats = {
            'total_ticks_processed': 0,
            'processing_latency_ms': 0.0,
            'last_update_time': None
        }
        self.logger = logging.getLogger(__name__)
    
    async def process_tick_data(self, tick: TickData):
        """处理tick数据"""
        start_time = datetime.now()
        
        try:
            symbol = tick.symbol
            
            # 创建缓冲区
            if symbol not in self.tick_buffers:
                self.tick_buffers[symbol] = []
            
            # 存储数据
            self.tick_buffers[symbol].append(tick)
            
            # 保持缓冲区大小
            if len(self.tick_buffers[symbol]) > 1000:
                self.tick_buffers[symbol] = self.tick_buffers[symbol][-1000:]
            
            # 更新统计
            self.stats['total_ticks_processed'] += 1
            self.stats['processing_latency_ms'] = (datetime.now() - start_time).total_seconds() * 1000
            self.stats['last_update_time'] = datetime.now()
            
            return True
            
        except Exception as e:
            self.logger.error(f"处理tick数据失败: {e}")
            return False
    
    def calculate_sma(self, symbol: str, period: int = 20) -> float:
        """计算简单移动平均"""
        if symbol not in self.tick_buffers:
            return 0.0
        
        ticks = self.tick_buffers[symbol]
        if len(ticks) < period:
            return 0.0
        
        prices = [tick.price for tick in ticks[-period:]]
        return sum(prices) / len(prices)
    
    def detect_price_anomaly(self, symbol: str) -> bool:
        """检测价格异常"""
        if symbol not in self.tick_buffers:
            return False
        
        ticks = self.tick_buffers[symbol]
        if len(ticks) < 20:
            return False
        
        # 获取最近20个价格
        recent_prices = [tick.price for tick in ticks[-20:]]
        current_price = recent_prices[-1]
        historical_prices = recent_prices[:-1]
        
        # 计算Z-score
        mean_price = np.mean(historical_prices)
        std_price = np.std(historical_prices)
        
        if std_price > 0:
            z_score = abs(current_price - mean_price) / std_price
            return z_score > 3.0  # 3倍标准差为异常
        
        return False
    
    def get_statistics(self):
        """获取统计信息"""
        stats = self.stats.copy()
        stats['active_symbols'] = len(self.tick_buffers)
        stats['total_data_points'] = sum(len(buffer) for buffer in self.tick_buffers.values())
        return stats


async def test_basic_functionality():
    """测试基本功能"""
    print("=== 测试基本功能 ===")
    
    # 创建引擎和数据生成器
    engine = SimpleRealtimeEngine()
    data_generator = MockDataGenerator("000001.SZ")
    
    # 生成测试数据
    ticks = data_generator.generate_tick_data(100)
    print(f"生成测试数据: {len(ticks)} 个tick")
    
    # 处理数据
    processed_count = 0
    for tick in ticks:
        success = await engine.process_tick_data(tick)
        if success:
            processed_count += 1
    
    print(f"成功处理: {processed_count}/{len(ticks)} 个tick")
    
    # 测试技术指标计算
    sma_20 = engine.calculate_sma("000001.SZ", 20)
    print(f"SMA(20): {sma_20:.4f}")
    
    # 测试异常检测
    has_anomaly = engine.detect_price_anomaly("000001.SZ")
    print(f"检测到价格异常: {has_anomaly}")
    
    # 获取统计信息
    stats = engine.get_statistics()
    print(f"统计信息: {stats}")
    
    # 验证结果
    assert processed_count == len(ticks), "所有数据应该被成功处理"
    assert sma_20 > 0, "SMA应该有有效值"
    assert stats['total_ticks_processed'] > 0, "应该有处理的数据"
    
    print("✓ 基本功能测试通过")


async def test_performance():
    """测试性能"""
    print("\n=== 测试性能 ===")
    
    engine = SimpleRealtimeEngine()
    data_generator = MockDataGenerator("000001.SZ")
    
    # 生成大量数据
    ticks = data_generator.generate_tick_data(1000)
    
    start_time = datetime.now()
    
    # 批量处理
    for tick in ticks:
        await engine.process_tick_data(tick)
    
    end_time = datetime.now()
    processing_time = (end_time - start_time).total_seconds()
    
    # 计算性能指标
    throughput = len(ticks) / processing_time
    avg_latency = processing_time / len(ticks) * 1000
    
    print(f"处理数据量: {len(ticks)} ticks")
    print(f"总处理时间: {processing_time:.3f} 秒")
    print(f"吞吐量: {throughput:.2f} ticks/秒")
    print(f"平均延迟: {avg_latency:.3f} 毫秒")
    
    # 性能验证
    assert throughput > 1000, f"吞吐量应该大于1000 ticks/秒，实际: {throughput:.2f}"
    assert avg_latency < 1, f"平均延迟应该小于1毫秒，实际: {avg_latency:.3f}"
    
    print("✓ 性能测试通过")


async def test_anomaly_detection():
    """测试异常检测"""
    print("\n=== 测试异常检测 ===")
    
    engine = SimpleRealtimeEngine()
    data_generator = MockDataGenerator("000001.SZ")
    
    # 生成正常数据
    normal_ticks = data_generator.generate_tick_data(50)
    
    for tick in normal_ticks:
        await engine.process_tick_data(tick)
    
    # 检测正常数据
    normal_anomaly = engine.detect_price_anomaly("000001.SZ")
    print(f"正常数据异常检测: {normal_anomaly}")
    
    # 生成异常数据（价格突然翻倍）
    last_tick = normal_ticks[-1]
    anomaly_tick = TickData(
        symbol="000001.SZ",
        timestamp=last_tick.timestamp + timedelta(seconds=1),
        price=last_tick.price * 2.0,  # 价格翻倍
        volume=1000,
        amount=last_tick.price * 2.0 * 1000,
        direction='B'
    )
    
    await engine.process_tick_data(anomaly_tick)
    
    # 检测异常数据
    anomaly_detected = engine.detect_price_anomaly("000001.SZ")
    print(f"异常数据检测: {anomaly_detected}")
    
    # 验证异常检测
    assert not normal_anomaly, "正常数据不应该被检测为异常"
    assert anomaly_detected, "异常数据应该被检测出来"
    
    print("✓ 异常检测测试通过")


async def test_multiple_symbols():
    """测试多股票处理"""
    print("\n=== 测试多股票处理 ===")
    
    engine = SimpleRealtimeEngine()
    symbols = ["000001.SZ", "000002.SZ", "600000.SH"]
    
    # 为每个股票生成数据
    total_processed = 0
    for symbol in symbols:
        data_generator = MockDataGenerator(symbol)
        ticks = data_generator.generate_tick_data(50)
        
        for tick in ticks:
            success = await engine.process_tick_data(tick)
            if success:
                total_processed += 1
    
    print(f"处理多股票数据: {total_processed} 个tick")
    
    # 验证每个股票的数据
    for symbol in symbols:
        sma = engine.calculate_sma(symbol, 10)
        print(f"{symbol} SMA(10): {sma:.4f}")
        assert sma > 0, f"{symbol}应该有有效的SMA值"
    
    # 验证统计信息
    stats = engine.get_statistics()
    print(f"多股票统计: {stats}")
    
    assert stats['active_symbols'] == len(symbols), f"应该有{len(symbols)}个活跃股票"
    assert stats['total_data_points'] == total_processed, "数据点数量应该匹配"
    
    print("✓ 多股票处理测试通过")


async def run_all_tests():
    """运行所有测试"""
    print("开始实时数据处理引擎简化测试...")
    print("="*50)
    
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    try:
        # 运行所有测试
        await test_basic_functionality()
        await test_performance()
        await test_anomaly_detection()
        await test_multiple_symbols()
        
        print("\n" + "="*50)
        print("🎉 所有测试通过！")
        print("实时数据处理引擎核心功能验证成功")
        print("="*50)
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        raise


if __name__ == "__main__":
    # 运行所有测试
    asyncio.run(run_all_tests())