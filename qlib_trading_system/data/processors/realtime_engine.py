"""
实时数据处理引擎
实现秒级tick数据处理管道，支持高频数据流处理和实时技术指标计算
"""

import asyncio
import time
import logging
import numpy as np
import pandas as pd
from collections import deque, defaultdict
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Any, Deque, Tuple
from enum import Enum
import threading
from concurrent.futures import ThreadPoolExecutor
import talib

from ..collectors.base import TickData, Level2Data, PriceData, DataSourceException
from .level2_parser import Level2Parser, OrderBookSnapshot, LargeOrderInfo, MarketDepthAnalysis


class IndicatorType(str, Enum):
    """技术指标类型"""
    PRICE = "price"           # 价格类指标
    VOLUME = "volume"         # 成交量类指标
    MOMENTUM = "momentum"     # 动量类指标
    VOLATILITY = "volatility" # 波动率类指标
    TREND = "trend"          # 趋势类指标


@dataclass
class TechnicalIndicator:
    """技术指标数据"""
    name: str
    type: IndicatorType
    value: float
    timestamp: datetime
    period: int = 0
    params: Dict[str, Any] = field(default_factory=dict)


@dataclass
class MarketMicrostructure:
    """市场微观结构数据"""
    symbol: str
    timestamp: datetime
    
    # 价格动量指标
    price_momentum_1min: float = 0.0      # 1分钟价格动量
    price_momentum_5min: float = 0.0      # 5分钟价格动量
    price_acceleration: float = 0.0       # 价格加速度
    
    # 成交量指标
    volume_surge_ratio: float = 0.0       # 成交量爆发比率
    volume_weighted_price: float = 0.0    # 成交量加权价格
    volume_profile_imbalance: float = 0.0 # 成交量分布不平衡
    
    # 订单簿指标
    bid_ask_spread: float = 0.0           # 买卖价差
    order_imbalance: float = 0.0          # 订单不平衡度
    liquidity_depth: float = 0.0          # 流动性深度
    
    # 大单监控
    large_order_flow: float = 0.0         # 大单净流入
    institutional_activity: float = 0.0   # 机构活跃度
    
    # 异常检测
    price_anomaly_score: float = 0.0      # 价格异常评分
    volume_anomaly_score: float = 0.0     # 成交量异常评分
    overall_anomaly_score: float = 0.0    # 综合异常评分


@dataclass
class RealtimeDataBuffer:
    """实时数据缓冲区"""
    symbol: str
    
    # 原始数据缓冲区
    tick_buffer: Deque[TickData] = field(default_factory=lambda: deque(maxlen=3600))  # 1小时tick
    level2_buffer: Deque[Level2Data] = field(default_factory=lambda: deque(maxlen=1800))  # 30分钟L2
    price_buffer: Deque[PriceData] = field(default_factory=lambda: deque(maxlen=300))  # 5分钟价格
    
    # 技术指标缓冲区
    indicators: Dict[str, Deque[TechnicalIndicator]] = field(default_factory=lambda: defaultdict(lambda: deque(maxlen=100)))
    
    # 市场微观结构数据
    microstructure_buffer: Deque[MarketMicrostructure] = field(default_factory=lambda: deque(maxlen=300))
    
    # 订单簿快照
    orderbook_snapshots: Deque[OrderBookSnapshot] = field(default_factory=lambda: deque(maxlen=1000))
    
    # 大单记录
    large_orders: Deque[LargeOrderInfo] = field(default_factory=lambda: deque(maxlen=500))
    
    # 统计信息
    last_update_time: datetime = field(default_factory=datetime.now)
    total_ticks: int = 0
    total_volume: int = 0
    total_amount: float = 0.0


class AnomalyDetector:
    """异常检测器"""
    
    def __init__(self, 
                 price_threshold: float = 3.0,    # 价格异常阈值(标准差倍数)
                 volume_threshold: float = 5.0):  # 成交量异常阈值(标准差倍数)
        self.price_threshold = price_threshold
        self.volume_threshold = volume_threshold
        self.logger = logging.getLogger(__name__)
    
    def detect_price_anomaly(self, prices: List[float], current_price: float) -> float:
        """检测价格异常"""
        try:
            if len(prices) < 10:
                return 0.0
            
            # 计算价格变化率
            price_changes = [prices[i] / prices[i-1] - 1 for i in range(1, len(prices))]
            current_change = current_price / prices[-1] - 1 if prices else 0
            
            # 计算Z-score
            if len(price_changes) > 0:
                mean_change = np.mean(price_changes)
                std_change = np.std(price_changes)
                
                if std_change > 0:
                    z_score = abs(current_change - mean_change) / std_change
                    return min(z_score / self.price_threshold, 1.0)
            
            return 0.0
            
        except Exception as e:
            self.logger.error(f"价格异常检测失败: {e}")
            return 0.0
    
    def detect_volume_anomaly(self, volumes: List[int], current_volume: int) -> float:
        """检测成交量异常"""
        try:
            if len(volumes) < 10:
                return 0.0
            
            # 计算成交量Z-score
            mean_volume = np.mean(volumes)
            std_volume = np.std(volumes)
            
            if std_volume > 0:
                z_score = abs(current_volume - mean_volume) / std_volume
                return min(z_score / self.volume_threshold, 1.0)
            
            return 0.0
            
        except Exception as e:
            self.logger.error(f"成交量异常检测失败: {e}")
            return 0.0


class TechnicalIndicatorCalculator:
    """技术指标计算器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def calculate_sma(self, prices: List[float], period: int) -> Optional[float]:
        """计算简单移动平均"""
        try:
            if len(prices) < period:
                return None
            return np.mean(prices[-period:])
        except Exception as e:
            self.logger.error(f"计算SMA失败: {e}")
            return None
    
    def calculate_ema(self, prices: List[float], period: int) -> Optional[float]:
        """计算指数移动平均"""
        try:
            if len(prices) < period:
                return None
            prices_array = np.array(prices, dtype=float)
            ema = talib.EMA(prices_array, timeperiod=period)
            return float(ema[-1]) if not np.isnan(ema[-1]) else None
        except Exception as e:
            self.logger.error(f"计算EMA失败: {e}")
            return None
    
    def calculate_rsi(self, prices: List[float], period: int = 14) -> Optional[float]:
        """计算相对强弱指标"""
        try:
            if len(prices) < period + 1:
                return None
            prices_array = np.array(prices, dtype=float)
            rsi = talib.RSI(prices_array, timeperiod=period)
            return float(rsi[-1]) if not np.isnan(rsi[-1]) else None
        except Exception as e:
            self.logger.error(f"计算RSI失败: {e}")
            return None
    
    def calculate_macd(self, prices: List[float], 
                      fast_period: int = 12, 
                      slow_period: int = 26, 
                      signal_period: int = 9) -> Optional[Tuple[float, float, float]]:
        """计算MACD指标"""
        try:
            if len(prices) < slow_period + signal_period:
                return None
            prices_array = np.array(prices, dtype=float)
            macd, signal, hist = talib.MACD(prices_array, 
                                          fastperiod=fast_period,
                                          slowperiod=slow_period, 
                                          signalperiod=signal_period)
            
            if not (np.isnan(macd[-1]) or np.isnan(signal[-1]) or np.isnan(hist[-1])):
                return float(macd[-1]), float(signal[-1]), float(hist[-1])
            return None
        except Exception as e:
            self.logger.error(f"计算MACD失败: {e}")
            return None
    
    def calculate_bollinger_bands(self, prices: List[float], 
                                period: int = 20, 
                                std_dev: float = 2.0) -> Optional[Tuple[float, float, float]]:
        """计算布林带"""
        try:
            if len(prices) < period:
                return None
            prices_array = np.array(prices, dtype=float)
            upper, middle, lower = talib.BBANDS(prices_array, 
                                              timeperiod=period,
                                              nbdevup=std_dev, 
                                              nbdevdn=std_dev)
            
            if not (np.isnan(upper[-1]) or np.isnan(middle[-1]) or np.isnan(lower[-1])):
                return float(upper[-1]), float(middle[-1]), float(lower[-1])
            return None
        except Exception as e:
            self.logger.error(f"计算布林带失败: {e}")
            return None
    
    def calculate_volume_indicators(self, prices: List[float], volumes: List[int]) -> Dict[str, float]:
        """计算成交量相关指标"""
        try:
            if len(prices) != len(volumes) or len(prices) < 10:
                return {}
            
            indicators = {}
            
            # 成交量加权平均价格 (VWAP)
            total_volume = sum(volumes)
            if total_volume > 0:
                vwap = sum(p * v for p, v in zip(prices, volumes)) / total_volume
                indicators['vwap'] = vwap
            
            # 成交量比率 (Volume Ratio)
            if len(volumes) >= 20:
                recent_avg_volume = np.mean(volumes[-5:])
                historical_avg_volume = np.mean(volumes[-20:-5])
                if historical_avg_volume > 0:
                    indicators['volume_ratio'] = recent_avg_volume / historical_avg_volume
            
            # 价量背离检测
            if len(prices) >= 10 and len(volumes) >= 10:
                price_trend = (prices[-1] - prices[-10]) / prices[-10]
                volume_trend = (np.mean(volumes[-5:]) - np.mean(volumes[-10:-5])) / np.mean(volumes[-10:-5])
                
                # 背离度：价格上涨但成交量下降，或价格下跌但成交量上升
                if price_trend * volume_trend < 0:
                    indicators['price_volume_divergence'] = abs(price_trend - volume_trend)
                else:
                    indicators['price_volume_divergence'] = 0.0
            
            return indicators
            
        except Exception as e:
            self.logger.error(f"计算成交量指标失败: {e}")
            return {}


class RealtimeDataEngine:
    """实时数据处理引擎"""
    
    def __init__(self, 
                 max_symbols: int = 100,
                 indicator_update_interval: float = 1.0,  # 指标更新间隔(秒)
                 anomaly_check_interval: float = 5.0,    # 异常检测间隔(秒)
                 cleanup_interval: int = 3600):          # 数据清理间隔(秒)
        
        self.max_symbols = max_symbols
        self.indicator_update_interval = indicator_update_interval
        self.anomaly_check_interval = anomaly_check_interval
        self.cleanup_interval = cleanup_interval
        
        # 数据缓冲区
        self.data_buffers: Dict[str, RealtimeDataBuffer] = {}
        
        # 组件初始化
        self.level2_parser = Level2Parser()
        self.anomaly_detector = AnomalyDetector()
        self.indicator_calculator = TechnicalIndicatorCalculator()
        
        # 线程池用于并行计算
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        # 运行状态
        self.is_running = False
        self._tasks: List[asyncio.Task] = []
        
        # 回调函数
        self.tick_callbacks: List[Callable[[TickData, MarketMicrostructure], None]] = []
        self.level2_callbacks: List[Callable[[Level2Data, OrderBookSnapshot], None]] = []
        self.indicator_callbacks: List[Callable[[str, Dict[str, TechnicalIndicator]], None]] = []
        self.anomaly_callbacks: List[Callable[[str, MarketMicrostructure], None]] = []
        
        # 统计信息
        self.stats = {
            'total_ticks_processed': 0,
            'total_level2_processed': 0,
            'total_indicators_calculated': 0,
            'total_anomalies_detected': 0,
            'processing_latency_ms': 0.0,
            'last_update_time': None
        }
        
        self.logger = logging.getLogger(__name__)
    
    def add_tick_callback(self, callback: Callable[[TickData, MarketMicrostructure], None]):
        """添加tick数据回调"""
        self.tick_callbacks.append(callback)
    
    def add_level2_callback(self, callback: Callable[[Level2Data, OrderBookSnapshot], None]):
        """添加Level-2数据回调"""
        self.level2_callbacks.append(callback)
    
    def add_indicator_callback(self, callback: Callable[[str, Dict[str, TechnicalIndicator]], None]):
        """添加技术指标回调"""
        self.indicator_callbacks.append(callback)
    
    def add_anomaly_callback(self, callback: Callable[[str, MarketMicrostructure], None]):
        """添加异常检测回调"""
        self.anomaly_callbacks.append(callback)
    
    def get_or_create_buffer(self, symbol: str) -> RealtimeDataBuffer:
        """获取或创建数据缓冲区"""
        if symbol not in self.data_buffers:
            if len(self.data_buffers) >= self.max_symbols:
                # 移除最久未更新的缓冲区
                oldest_symbol = min(self.data_buffers.keys(), 
                                  key=lambda s: self.data_buffers[s].last_update_time)
                del self.data_buffers[oldest_symbol]
                self.logger.info(f"移除最久未更新的缓冲区: {oldest_symbol}")
            
            self.data_buffers[symbol] = RealtimeDataBuffer(symbol=symbol)
            self.logger.info(f"创建新的数据缓冲区: {symbol}")
        
        return self.data_buffers[symbol]    
async def process_tick_data(self, tick: TickData) -> Optional[MarketMicrostructure]:
        """处理tick数据"""
        start_time = time.time()
        
        try:
            # 获取缓冲区
            buffer = self.get_or_create_buffer(tick.symbol)
            
            # 存储tick数据
            buffer.tick_buffer.append(tick)
            buffer.last_update_time = datetime.now()
            buffer.total_ticks += 1
            buffer.total_volume += tick.volume
            buffer.total_amount += tick.amount
            
            # 计算市场微观结构指标
            microstructure = await self._calculate_microstructure(tick.symbol, buffer)
            
            if microstructure:
                buffer.microstructure_buffer.append(microstructure)
                
                # 触发回调
                for callback in self.tick_callbacks:
                    try:
                        callback(tick, microstructure)
                    except Exception as e:
                        self.logger.error(f"Tick回调执行失败: {e}")
            
            # 更新统计
            self.stats['total_ticks_processed'] += 1
            self.stats['processing_latency_ms'] = (time.time() - start_time) * 1000
            self.stats['last_update_time'] = datetime.now()
            
            self.logger.debug(f"处理tick数据: {tick.symbol} @ {tick.price}, 延迟: {self.stats['processing_latency_ms']:.2f}ms")
            
            return microstructure
            
        except Exception as e:
            self.logger.error(f"处理tick数据失败: {e}")
            return None
    
    async def process_level2_data(self, level2: Level2Data) -> Optional[OrderBookSnapshot]:
        """处理Level-2数据"""
        try:
            # 获取缓冲区
            buffer = self.get_or_create_buffer(level2.symbol)
            
            # 存储Level-2数据
            buffer.level2_buffer.append(level2)
            buffer.last_update_time = datetime.now()
            
            # 解析订单簿快照
            snapshot = self.level2_parser.parse_level2_data(level2)
            buffer.orderbook_snapshots.append(snapshot)
            
            # 检测大单
            large_orders = self.level2_parser.detect_large_orders(snapshot)
            if large_orders:
                buffer.large_orders.extend(large_orders)
                self.logger.info(f"检测到 {len(large_orders)} 个大单: {level2.symbol}")
            
            # 触发回调
            for callback in self.level2_callbacks:
                try:
                    callback(level2, snapshot)
                except Exception as e:
                    self.logger.error(f"Level-2回调执行失败: {e}")
            
            # 更新统计
            self.stats['total_level2_processed'] += 1
            
            return snapshot
            
        except Exception as e:
            self.logger.error(f"处理Level-2数据失败: {e}")
            return None
    
    async def _calculate_microstructure(self, symbol: str, buffer: RealtimeDataBuffer) -> Optional[MarketMicrostructure]:
        """计算市场微观结构指标"""
        try:
            if len(buffer.tick_buffer) < 10:
                return None
            
            microstructure = MarketMicrostructure(
                symbol=symbol,
                timestamp=datetime.now()
            )
            
            # 获取最近的价格和成交量数据
            recent_ticks = list(buffer.tick_buffer)[-60:]  # 最近60个tick
            prices = [tick.price for tick in recent_ticks]
            volumes = [tick.volume for tick in recent_ticks]
            amounts = [tick.amount for tick in recent_ticks]
            
            # 计算价格动量指标
            if len(prices) >= 20:
                # 1分钟价格动量 (假设每分钟20个tick)
                if len(prices) >= 20:
                    microstructure.price_momentum_1min = (prices[-1] - prices[-20]) / prices[-20] if prices[-20] > 0 else 0
                
                # 5分钟价格动量
                if len(prices) >= 60:
                    microstructure.price_momentum_5min = (prices[-1] - prices[-60]) / prices[-60] if prices[-60] > 0 else 0
                
                # 价格加速度
                if len(prices) >= 40:
                    momentum_1 = (prices[-1] - prices[-20]) / prices[-20] if prices[-20] > 0 else 0
                    momentum_2 = (prices[-20] - prices[-40]) / prices[-40] if prices[-40] > 0 else 0
                    microstructure.price_acceleration = momentum_1 - momentum_2
            
            # 计算成交量指标
            if len(volumes) >= 20:
                recent_avg_volume = np.mean(volumes[-10:])
                historical_avg_volume = np.mean(volumes[-20:-10])
                
                if historical_avg_volume > 0:
                    microstructure.volume_surge_ratio = recent_avg_volume / historical_avg_volume
                
                # 成交量加权价格
                total_volume = sum(volumes)
                if total_volume > 0:
                    microstructure.volume_weighted_price = sum(p * v for p, v in zip(prices, volumes)) / total_volume
            
            # 从订单簿获取指标
            if buffer.orderbook_snapshots:
                latest_snapshot = buffer.orderbook_snapshots[-1]
                microstructure.bid_ask_spread = latest_snapshot.spread
                microstructure.order_imbalance = latest_snapshot.order_imbalance
                microstructure.liquidity_depth = latest_snapshot.liquidity_depth
            
            # 计算大单流向
            if buffer.large_orders:
                recent_large_orders = [order for order in buffer.large_orders 
                                     if (datetime.now() - order.timestamp).seconds <= 300]  # 5分钟内
                
                buy_amount = sum(order.amount for order in recent_large_orders if order.side == 'BUY')
                sell_amount = sum(order.amount for order in recent_large_orders if order.side == 'SELL')
                
                microstructure.large_order_flow = buy_amount - sell_amount
                microstructure.institutional_activity = len(recent_large_orders) / 300 * 60  # 每分钟大单数
            
            # 异常检测
            microstructure.price_anomaly_score = self.anomaly_detector.detect_price_anomaly(prices[:-1], prices[-1])
            microstructure.volume_anomaly_score = self.anomaly_detector.detect_volume_anomaly(volumes[:-1], volumes[-1])
            microstructure.overall_anomaly_score = (microstructure.price_anomaly_score + microstructure.volume_anomaly_score) / 2
            
            # 如果检测到异常，触发异常回调
            if microstructure.overall_anomaly_score > 0.7:
                self.stats['total_anomalies_detected'] += 1
                for callback in self.anomaly_callbacks:
                    try:
                        callback(symbol, microstructure)
                    except Exception as e:
                        self.logger.error(f"异常回调执行失败: {e}")
            
            return microstructure
            
        except Exception as e:
            self.logger.error(f"计算市场微观结构失败: {e}")
            return None
    
    async def _calculate_technical_indicators(self, symbol: str) -> Dict[str, TechnicalIndicator]:
        """计算技术指标"""
        try:
            buffer = self.data_buffers.get(symbol)
            if not buffer or len(buffer.tick_buffer) < 50:
                return {}
            
            indicators = {}
            
            # 获取价格数据
            prices = [tick.price for tick in buffer.tick_buffer]
            volumes = [tick.volume for tick in buffer.tick_buffer]
            
            current_time = datetime.now()
            
            # 移动平均线
            for period in [5, 10, 20, 60]:
                sma = self.indicator_calculator.calculate_sma(prices, period)
                if sma is not None:
                    indicators[f'SMA_{period}'] = TechnicalIndicator(
                        name=f'SMA_{period}',
                        type=IndicatorType.TREND,
                        value=sma,
                        timestamp=current_time,
                        period=period
                    )
                
                ema = self.indicator_calculator.calculate_ema(prices, period)
                if ema is not None:
                    indicators[f'EMA_{period}'] = TechnicalIndicator(
                        name=f'EMA_{period}',
                        type=IndicatorType.TREND,
                        value=ema,
                        timestamp=current_time,
                        period=period
                    )
            
            # RSI
            rsi = self.indicator_calculator.calculate_rsi(prices, 14)
            if rsi is not None:
                indicators['RSI_14'] = TechnicalIndicator(
                    name='RSI_14',
                    type=IndicatorType.MOMENTUM,
                    value=rsi,
                    timestamp=current_time,
                    period=14
                )
            
            # MACD
            macd_result = self.indicator_calculator.calculate_macd(prices)
            if macd_result:
                macd, signal, hist = macd_result
                indicators['MACD'] = TechnicalIndicator(
                    name='MACD',
                    type=IndicatorType.MOMENTUM,
                    value=macd,
                    timestamp=current_time,
                    params={'signal': signal, 'histogram': hist}
                )
            
            # 布林带
            bb_result = self.indicator_calculator.calculate_bollinger_bands(prices)
            if bb_result:
                upper, middle, lower = bb_result
                indicators['BB_UPPER'] = TechnicalIndicator(
                    name='BB_UPPER',
                    type=IndicatorType.VOLATILITY,
                    value=upper,
                    timestamp=current_time,
                    params={'middle': middle, 'lower': lower}
                )
            
            # 成交量指标
            volume_indicators = self.indicator_calculator.calculate_volume_indicators(prices, volumes)
            for name, value in volume_indicators.items():
                indicators[name.upper()] = TechnicalIndicator(
                    name=name.upper(),
                    type=IndicatorType.VOLUME,
                    value=value,
                    timestamp=current_time
                )
            
            # 存储指标到缓冲区
            for name, indicator in indicators.items():
                buffer.indicators[name].append(indicator)
            
            # 更新统计
            self.stats['total_indicators_calculated'] += len(indicators)
            
            return indicators
            
        except Exception as e:
            self.logger.error(f"计算技术指标失败: {e}")
            return {}
    
    async def _indicator_update_loop(self):
        """技术指标更新循环"""
        while self.is_running:
            try:
                for symbol in list(self.data_buffers.keys()):
                    indicators = await self._calculate_technical_indicators(symbol)
                    
                    if indicators:
                        # 触发指标回调
                        for callback in self.indicator_callbacks:
                            try:
                                callback(symbol, indicators)
                            except Exception as e:
                                self.logger.error(f"指标回调执行失败: {e}")
                
                await asyncio.sleep(self.indicator_update_interval)
                
            except Exception as e:
                self.logger.error(f"指标更新循环异常: {e}")
                await asyncio.sleep(self.indicator_update_interval)
    
    async def _cleanup_loop(self):
        """数据清理循环"""
        while self.is_running:
            try:
                current_time = datetime.now()
                
                for symbol, buffer in list(self.data_buffers.items()):
                    # 清理过期数据
                    cutoff_time = current_time - timedelta(hours=2)
                    
                    # 清理过期的tick数据
                    while buffer.tick_buffer and buffer.tick_buffer[0].timestamp < cutoff_time:
                        buffer.tick_buffer.popleft()
                    
                    # 清理过期的Level-2数据
                    while buffer.level2_buffer and buffer.level2_buffer[0].timestamp < cutoff_time:
                        buffer.level2_buffer.popleft()
                    
                    # 清理过期的大单记录
                    while buffer.large_orders and buffer.large_orders[0].timestamp < cutoff_time:
                        buffer.large_orders.popleft()
                    
                    # 如果缓冲区长时间未更新，则移除
                    if (current_time - buffer.last_update_time).seconds > 3600:  # 1小时未更新
                        del self.data_buffers[symbol]
                        self.logger.info(f"移除长时间未更新的缓冲区: {symbol}")
                
                await asyncio.sleep(self.cleanup_interval)
                
            except Exception as e:
                self.logger.error(f"数据清理循环异常: {e}")
                await asyncio.sleep(self.cleanup_interval)
    
    def get_latest_microstructure(self, symbol: str) -> Optional[MarketMicrostructure]:
        """获取最新的市场微观结构数据"""
        buffer = self.data_buffers.get(symbol)
        if buffer and buffer.microstructure_buffer:
            return buffer.microstructure_buffer[-1]
        return None
    
    def get_latest_indicators(self, symbol: str) -> Dict[str, TechnicalIndicator]:
        """获取最新的技术指标"""
        buffer = self.data_buffers.get(symbol)
        if not buffer:
            return {}
        
        latest_indicators = {}
        for name, indicator_buffer in buffer.indicators.items():
            if indicator_buffer:
                latest_indicators[name] = indicator_buffer[-1]
        
        return latest_indicators
    
    def get_indicator_history(self, symbol: str, indicator_name: str, count: int = 100) -> List[TechnicalIndicator]:
        """获取指标历史数据"""
        buffer = self.data_buffers.get(symbol)
        if not buffer or indicator_name not in buffer.indicators:
            return []
        
        indicator_buffer = buffer.indicators[indicator_name]
        return list(indicator_buffer)[-count:]
    
    def get_large_orders(self, symbol: str, minutes: int = 60) -> List[LargeOrderInfo]:
        """获取大单记录"""
        buffer = self.data_buffers.get(symbol)
        if not buffer:
            return []
        
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        return [order for order in buffer.large_orders if order.timestamp >= cutoff_time]
    
    def get_market_depth_analysis(self, symbol: str) -> Optional[MarketDepthAnalysis]:
        """获取市场深度分析"""
        buffer = self.data_buffers.get(symbol)
        if not buffer or not buffer.orderbook_snapshots:
            return None
        
        latest_snapshot = buffer.orderbook_snapshots[-1]
        return self.level2_parser.analyze_market_depth(latest_snapshot)
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.stats.copy()
        stats.update({
            'active_symbols': len(self.data_buffers),
            'total_buffers': len(self.data_buffers),
            'is_running': self.is_running,
            'memory_usage_mb': sum(
                len(buffer.tick_buffer) + len(buffer.level2_buffer) + 
                len(buffer.microstructure_buffer) + len(buffer.orderbook_snapshots) +
                sum(len(ind_buffer) for ind_buffer in buffer.indicators.values())
                for buffer in self.data_buffers.values()
            ) * 0.001  # 粗略估算
        })
        
        return stats
    
    def get_symbol_statistics(self, symbol: str) -> Dict[str, Any]:
        """获取单个股票的统计信息"""
        buffer = self.data_buffers.get(symbol)
        if not buffer:
            return {}
        
        return {
            'symbol': symbol,
            'total_ticks': buffer.total_ticks,
            'total_volume': buffer.total_volume,
            'total_amount': buffer.total_amount,
            'tick_buffer_size': len(buffer.tick_buffer),
            'level2_buffer_size': len(buffer.level2_buffer),
            'indicators_count': len(buffer.indicators),
            'large_orders_count': len(buffer.large_orders),
            'last_update_time': buffer.last_update_time,
            'latest_price': buffer.tick_buffer[-1].price if buffer.tick_buffer else None,
            'latest_volume': buffer.tick_buffer[-1].volume if buffer.tick_buffer else None
        }
    
    async def start(self):
        """启动实时数据处理引擎"""
        if self.is_running:
            self.logger.warning("实时数据处理引擎已在运行")
            return
        
        self.is_running = True
        
        # 启动后台任务
        self._tasks = [
            asyncio.create_task(self._indicator_update_loop()),
            asyncio.create_task(self._cleanup_loop())
        ]
        
        self.logger.info("实时数据处理引擎已启动")
    
    async def stop(self):
        """停止实时数据处理引擎"""
        if not self.is_running:
            return
        
        self.is_running = False
        
        # 取消所有任务
        for task in self._tasks:
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass
        
        self._tasks.clear()
        
        # 关闭线程池
        self.executor.shutdown(wait=True)
        
        self.logger.info("实时数据处理引擎已停止")
    
    def clear_buffer(self, symbol: str):
        """清空指定股票的缓冲区"""
        if symbol in self.data_buffers:
            del self.data_buffers[symbol]
            self.logger.info(f"已清空 {symbol} 的数据缓冲区")
    
    def clear_all_buffers(self):
        """清空所有缓冲区"""
        self.data_buffers.clear()
        self.logger.info("已清空所有数据缓冲区")
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.stop()