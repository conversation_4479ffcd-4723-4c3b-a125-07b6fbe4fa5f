"""
Level-2行情数据解析器
解析和处理Level-2行情数据，包括买卖盘、大单监控等
"""
import logging
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
import pandas as pd
import numpy as np
from collections import deque

from ..collectors.base import Level2Data, TickData


@dataclass
class OrderBookSnapshot:
    """订单簿快照"""
    symbol: str
    timestamp: datetime
    bid_prices: List[float]
    bid_volumes: List[int]
    ask_prices: List[float]
    ask_volumes: List[int]
    total_bid_volume: int
    total_ask_volume: int
    
    # 计算字段
    spread: float = 0.0              # 买卖价差
    spread_pct: float = 0.0          # 价差百分比
    mid_price: float = 0.0           # 中间价
    weighted_mid_price: float = 0.0  # 加权中间价
    order_imbalance: float = 0.0     # 订单不平衡度
    liquidity_depth: float = 0.0     # 流动性深度


@dataclass
class LargeOrderInfo:
    """大单信息"""
    symbol: str
    timestamp: datetime
    side: str  # 'BUY' or 'SELL'
    price: float
    volume: int
    amount: float
    order_type: str  # 'NEW', 'CANCEL', 'MODIFY'
    level: int  # 档位 (1-5)


@dataclass
class MarketDepthAnalysis:
    """市场深度分析"""
    symbol: str
    timestamp: datetime
    
    # 流动性指标
    bid_liquidity_1: float = 0.0     # 买1流动性
    ask_liquidity_1: float = 0.0     # 卖1流动性
    total_bid_liquidity: float = 0.0 # 总买盘流动性
    total_ask_liquidity: float = 0.0 # 总卖盘流动性
    
    # 价格压力指标
    bid_pressure: float = 0.0        # 买盘压力
    ask_pressure: float = 0.0        # 卖盘压力
    net_pressure: float = 0.0        # 净压力
    
    # 大单监控
    large_bid_orders: int = 0        # 大买单数量
    large_ask_orders: int = 0        # 大卖单数量
    large_order_ratio: float = 0.0   # 大单占比


class Level2Parser:
    """Level-2数据解析器"""
    
    def __init__(self, 
                 large_order_threshold: float = 100000,  # 大单阈值(金额)
                 depth_window_size: int = 100):          # 深度分析窗口大小
        self.large_order_threshold = large_order_threshold
        self.depth_window_size = depth_window_size
        
        # 历史数据缓存
        self.order_book_history: Dict[str, deque] = {}
        self.large_orders_history: Dict[str, deque] = {}
        
        self.logger = logging.getLogger(__name__)
    
    def parse_level2_data(self, level2: Level2Data) -> OrderBookSnapshot:
        """解析Level-2数据为订单簿快照"""
        try:
            snapshot = OrderBookSnapshot(
                symbol=level2.symbol,
                timestamp=level2.timestamp,
                bid_prices=level2.bid_prices.copy(),
                bid_volumes=level2.bid_volumes.copy(),
                ask_prices=level2.ask_prices.copy(),
                ask_volumes=level2.ask_volumes.copy(),
                total_bid_volume=level2.total_bid_volume,
                total_ask_volume=level2.total_ask_volume
            )
            
            # 计算衍生指标
            self._calculate_derived_metrics(snapshot)
            
            # 缓存历史数据
            self._cache_order_book(snapshot)
            
            return snapshot
            
        except Exception as e:
            self.logger.error(f"解析Level-2数据失败: {e}")
            raise
    
    def _calculate_derived_metrics(self, snapshot: OrderBookSnapshot):
        """计算衍生指标"""
        try:
            # 买卖价差
            if snapshot.bid_prices and snapshot.ask_prices:
                best_bid = snapshot.bid_prices[0]
                best_ask = snapshot.ask_prices[0]
                
                snapshot.spread = best_ask - best_bid
                snapshot.spread_pct = snapshot.spread / best_bid * 100 if best_bid > 0 else 0
                
                # 中间价
                snapshot.mid_price = (best_bid + best_ask) / 2
                
                # 加权中间价
                bid_vol = snapshot.bid_volumes[0] if snapshot.bid_volumes else 0
                ask_vol = snapshot.ask_volumes[0] if snapshot.ask_volumes else 0
                total_vol = bid_vol + ask_vol
                
                if total_vol > 0:
                    snapshot.weighted_mid_price = (best_bid * ask_vol + best_ask * bid_vol) / total_vol
                else:
                    snapshot.weighted_mid_price = snapshot.mid_price
            
            # 订单不平衡度
            if snapshot.total_bid_volume + snapshot.total_ask_volume > 0:
                snapshot.order_imbalance = (snapshot.total_bid_volume - snapshot.total_ask_volume) / (snapshot.total_bid_volume + snapshot.total_ask_volume)
            
            # 流动性深度（前3档总金额）
            bid_depth = sum(p * v for p, v in zip(snapshot.bid_prices[:3], snapshot.bid_volumes[:3]))
            ask_depth = sum(p * v for p, v in zip(snapshot.ask_prices[:3], snapshot.ask_volumes[:3]))
            snapshot.liquidity_depth = bid_depth + ask_depth
            
        except Exception as e:
            self.logger.error(f"计算衍生指标失败: {e}")
    
    def _cache_order_book(self, snapshot: OrderBookSnapshot):
        """缓存订单簿数据"""
        symbol = snapshot.symbol
        
        if symbol not in self.order_book_history:
            self.order_book_history[symbol] = deque(maxlen=self.depth_window_size)
        
        self.order_book_history[symbol].append(snapshot)
    
    def detect_large_orders(self, current_snapshot: OrderBookSnapshot, 
                          previous_snapshot: Optional[OrderBookSnapshot] = None) -> List[LargeOrderInfo]:
        """检测大单"""
        large_orders = []
        
        if not previous_snapshot:
            # 如果没有历史数据，从缓存中获取
            history = self.order_book_history.get(current_snapshot.symbol)
            if history and len(history) >= 2:
                previous_snapshot = history[-2]
            else:
                return large_orders
        
        try:
            # 检测买盘大单
            for i in range(5):
                if i < len(current_snapshot.bid_prices) and i < len(previous_snapshot.bid_prices):
                    curr_price = current_snapshot.bid_prices[i]
                    curr_vol = current_snapshot.bid_volumes[i]
                    prev_price = previous_snapshot.bid_prices[i]
                    prev_vol = previous_snapshot.bid_volumes[i]
                    
                    # 检测新增大单
                    if curr_price == prev_price and curr_vol > prev_vol:
                        vol_diff = curr_vol - prev_vol
                        amount = curr_price * vol_diff
                        
                        if amount >= self.large_order_threshold:
                            large_order = LargeOrderInfo(
                                symbol=current_snapshot.symbol,
                                timestamp=current_snapshot.timestamp,
                                side='BUY',
                                price=curr_price,
                                volume=vol_diff,
                                amount=amount,
                                order_type='NEW',
                                level=i + 1
                            )
                            large_orders.append(large_order)
                    
                    # 检测撤单
                    elif curr_price == prev_price and curr_vol < prev_vol:
                        vol_diff = prev_vol - curr_vol
                        amount = curr_price * vol_diff
                        
                        if amount >= self.large_order_threshold:
                            large_order = LargeOrderInfo(
                                symbol=current_snapshot.symbol,
                                timestamp=current_snapshot.timestamp,
                                side='BUY',
                                price=curr_price,
                                volume=vol_diff,
                                amount=amount,
                                order_type='CANCEL',
                                level=i + 1
                            )
                            large_orders.append(large_order)
            
            # 检测卖盘大单
            for i in range(5):
                if i < len(current_snapshot.ask_prices) and i < len(previous_snapshot.ask_prices):
                    curr_price = current_snapshot.ask_prices[i]
                    curr_vol = current_snapshot.ask_volumes[i]
                    prev_price = previous_snapshot.ask_prices[i]
                    prev_vol = previous_snapshot.ask_volumes[i]
                    
                    # 检测新增大单
                    if curr_price == prev_price and curr_vol > prev_vol:
                        vol_diff = curr_vol - prev_vol
                        amount = curr_price * vol_diff
                        
                        if amount >= self.large_order_threshold:
                            large_order = LargeOrderInfo(
                                symbol=current_snapshot.symbol,
                                timestamp=current_snapshot.timestamp,
                                side='SELL',
                                price=curr_price,
                                volume=vol_diff,
                                amount=amount,
                                order_type='NEW',
                                level=i + 1
                            )
                            large_orders.append(large_order)
                    
                    # 检测撤单
                    elif curr_price == prev_price and curr_vol < prev_vol:
                        vol_diff = prev_vol - curr_vol
                        amount = curr_price * vol_diff
                        
                        if amount >= self.large_order_threshold:
                            large_order = LargeOrderInfo(
                                symbol=current_snapshot.symbol,
                                timestamp=current_snapshot.timestamp,
                                side='SELL',
                                price=curr_price,
                                volume=vol_diff,
                                amount=amount,
                                order_type='CANCEL',
                                level=i + 1
                            )
                            large_orders.append(large_order)
            
            # 缓存大单信息
            if large_orders:
                self._cache_large_orders(current_snapshot.symbol, large_orders)
            
            return large_orders
            
        except Exception as e:
            self.logger.error(f"检测大单失败: {e}")
            return []
    
    def _cache_large_orders(self, symbol: str, large_orders: List[LargeOrderInfo]):
        """缓存大单信息"""
        if symbol not in self.large_orders_history:
            self.large_orders_history[symbol] = deque(maxlen=1000)  # 保存最近1000个大单
        
        self.large_orders_history[symbol].extend(large_orders)
    
    def analyze_market_depth(self, snapshot: OrderBookSnapshot) -> MarketDepthAnalysis:
        """分析市场深度"""
        try:
            analysis = MarketDepthAnalysis(
                symbol=snapshot.symbol,
                timestamp=snapshot.timestamp
            )
            
            # 计算流动性指标
            if snapshot.bid_prices and snapshot.bid_volumes:
                analysis.bid_liquidity_1 = snapshot.bid_prices[0] * snapshot.bid_volumes[0]
                analysis.total_bid_liquidity = sum(p * v for p, v in zip(snapshot.bid_prices, snapshot.bid_volumes))
            
            if snapshot.ask_prices and snapshot.ask_volumes:
                analysis.ask_liquidity_1 = snapshot.ask_prices[0] * snapshot.ask_volumes[0]
                analysis.total_ask_liquidity = sum(p * v for p, v in zip(snapshot.ask_prices, snapshot.ask_volumes))
            
            # 计算价格压力
            total_liquidity = analysis.total_bid_liquidity + analysis.total_ask_liquidity
            if total_liquidity > 0:
                analysis.bid_pressure = analysis.total_bid_liquidity / total_liquidity
                analysis.ask_pressure = analysis.total_ask_liquidity / total_liquidity
                analysis.net_pressure = analysis.bid_pressure - analysis.ask_pressure
            
            # 统计大单
            recent_large_orders = self._get_recent_large_orders(snapshot.symbol, minutes=5)
            analysis.large_bid_orders = sum(1 for order in recent_large_orders if order.side == 'BUY' and order.order_type == 'NEW')
            analysis.large_ask_orders = sum(1 for order in recent_large_orders if order.side == 'SELL' and order.order_type == 'NEW')
            
            total_orders = len(recent_large_orders)
            if total_orders > 0:
                analysis.large_order_ratio = (analysis.large_bid_orders + analysis.large_ask_orders) / total_orders
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"分析市场深度失败: {e}")
            return MarketDepthAnalysis(symbol=snapshot.symbol, timestamp=snapshot.timestamp)
    
    def _get_recent_large_orders(self, symbol: str, minutes: int = 5) -> List[LargeOrderInfo]:
        """获取最近的大单"""
        if symbol not in self.large_orders_history:
            return []
        
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        return [order for order in self.large_orders_history[symbol] 
                if order.timestamp >= cutoff_time]
    
    def calculate_order_flow_imbalance(self, symbol: str, window_minutes: int = 1) -> float:
        """计算订单流不平衡度"""
        try:
            history = self.order_book_history.get(symbol, [])
            if len(history) < 2:
                return 0.0
            
            cutoff_time = datetime.now() - timedelta(minutes=window_minutes)
            recent_snapshots = [s for s in history if s.timestamp >= cutoff_time]
            
            if len(recent_snapshots) < 2:
                return 0.0
            
            # 计算买卖盘变化
            bid_changes = []
            ask_changes = []
            
            for i in range(1, len(recent_snapshots)):
                prev = recent_snapshots[i-1]
                curr = recent_snapshots[i]
                
                # 计算买盘变化
                bid_change = curr.total_bid_volume - prev.total_bid_volume
                bid_changes.append(bid_change)
                
                # 计算卖盘变化
                ask_change = curr.total_ask_volume - prev.total_ask_volume
                ask_changes.append(ask_change)
            
            # 计算不平衡度
            total_bid_change = sum(bid_changes)
            total_ask_change = sum(ask_changes)
            total_change = abs(total_bid_change) + abs(total_ask_change)
            
            if total_change > 0:
                return (total_bid_change - total_ask_change) / total_change
            else:
                return 0.0
                
        except Exception as e:
            self.logger.error(f"计算订单流不平衡度失败: {e}")
            return 0.0
    
    def get_price_level_changes(self, symbol: str, minutes: int = 1) -> Dict[str, List[Tuple[float, int]]]:
        """获取价格档位变化"""
        try:
            history = self.order_book_history.get(symbol, [])
            if len(history) < 2:
                return {'bid_changes': [], 'ask_changes': []}
            
            cutoff_time = datetime.now() - timedelta(minutes=minutes)
            recent_snapshots = [s for s in history if s.timestamp >= cutoff_time]
            
            if len(recent_snapshots) < 2:
                return {'bid_changes': [], 'ask_changes': []}
            
            first_snapshot = recent_snapshots[0]
            last_snapshot = recent_snapshots[-1]
            
            # 计算买盘变化
            bid_changes = []
            for i in range(min(5, len(first_snapshot.bid_prices), len(last_snapshot.bid_prices))):
                if first_snapshot.bid_prices[i] == last_snapshot.bid_prices[i]:
                    vol_change = last_snapshot.bid_volumes[i] - first_snapshot.bid_volumes[i]
                    if vol_change != 0:
                        bid_changes.append((first_snapshot.bid_prices[i], vol_change))
            
            # 计算卖盘变化
            ask_changes = []
            for i in range(min(5, len(first_snapshot.ask_prices), len(last_snapshot.ask_prices))):
                if first_snapshot.ask_prices[i] == last_snapshot.ask_prices[i]:
                    vol_change = last_snapshot.ask_volumes[i] - first_snapshot.ask_volumes[i]
                    if vol_change != 0:
                        ask_changes.append((first_snapshot.ask_prices[i], vol_change))
            
            return {
                'bid_changes': bid_changes,
                'ask_changes': ask_changes
            }
            
        except Exception as e:
            self.logger.error(f"获取价格档位变化失败: {e}")
            return {'bid_changes': [], 'ask_changes': []}
    
    def get_order_book_statistics(self, symbol: str) -> Dict[str, Any]:
        """获取订单簿统计信息"""
        try:
            history = self.order_book_history.get(symbol, [])
            large_orders = self.large_orders_history.get(symbol, [])
            
            if not history:
                return {}
            
            latest = history[-1]
            
            # 基本统计
            stats = {
                'symbol': symbol,
                'latest_timestamp': latest.timestamp,
                'snapshots_count': len(history),
                'large_orders_count': len(large_orders),
                
                # 最新快照信息
                'latest_spread': latest.spread,
                'latest_spread_pct': latest.spread_pct,
                'latest_mid_price': latest.mid_price,
                'latest_order_imbalance': latest.order_imbalance,
                'latest_liquidity_depth': latest.liquidity_depth,
            }
            
            # 历史统计
            if len(history) > 1:
                spreads = [s.spread for s in history]
                imbalances = [s.order_imbalance for s in history]
                
                stats.update({
                    'avg_spread': np.mean(spreads),
                    'min_spread': np.min(spreads),
                    'max_spread': np.max(spreads),
                    'avg_imbalance': np.mean(imbalances),
                    'imbalance_volatility': np.std(imbalances)
                })
            
            # 大单统计
            if large_orders:
                recent_large_orders = self._get_recent_large_orders(symbol, minutes=60)
                buy_orders = [o for o in recent_large_orders if o.side == 'BUY']
                sell_orders = [o for o in recent_large_orders if o.side == 'SELL']
                
                stats.update({
                    'recent_large_orders': len(recent_large_orders),
                    'recent_large_buy_orders': len(buy_orders),
                    'recent_large_sell_orders': len(sell_orders),
                    'large_order_net_flow': len(buy_orders) - len(sell_orders)
                })
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取订单簿统计失败: {e}")
            return {}
    
    def clear_history(self, symbol: str):
        """清空历史数据"""
        if symbol in self.order_book_history:
            self.order_book_history[symbol].clear()
        if symbol in self.large_orders_history:
            self.large_orders_history[symbol].clear()
        self.logger.info(f"已清空 {symbol} 的Level-2历史数据")
    
    def clear_all_history(self):
        """清空所有历史数据"""
        self.order_book_history.clear()
        self.large_orders_history.clear()
        self.logger.info("已清空所有Level-2历史数据")