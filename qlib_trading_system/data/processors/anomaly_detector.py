"""
实时异常检测和数据清洗模块
实现多维度异常检测算法和数据质量控制
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any, Set
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from collections import deque, defaultdict
from enum import Enum
import statistics
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

from ..collectors.base import TickData, Level2Data, PriceData


class AnomalyType(str, Enum):
    """异常类型"""
    PRICE_SPIKE = "price_spike"           # 价格异常波动
    VOLUME_SURGE = "volume_surge"         # 成交量异常
    SPREAD_ANOMALY = "spread_anomaly"     # 价差异常
    DATA_MISSING = "data_missing"         # 数据缺失
    DATA_DUPLICATE = "data_duplicate"     # 数据重复
    TIME_DISORDER = "time_disorder"       # 时间乱序
    PRICE_INVERSION = "price_inversion"   # 价格倒挂
    LIQUIDITY_DRY = "liquidity_dry"       # 流动性枯竭
    SYSTEM_ERROR = "system_error"         # 系统错误


class AnomalySeverity(str, Enum):
    """异常严重程度"""
    LOW = "low"           # 轻微异常
    MEDIUM = "medium"     # 中等异常
    HIGH = "high"         # 严重异常
    CRITICAL = "critical" # 极严重异常


@dataclass
class AnomalyEvent:
    """异常事件"""
    symbol: str
    timestamp: datetime
    anomaly_type: AnomalyType
    severity: AnomalySeverity
    description: str
    confidence: float = 0.0      # 置信度 (0-1)
    affected_data: Any = None    # 受影响的数据
    suggested_action: str = ""   # 建议处理方式
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class DataQualityReport:
    """数据质量报告"""
    symbol: str
    timestamp: datetime
    
    # 完整性指标
    total_records: int = 0
    missing_records: int = 0
    completeness_rate: float = 0.0
    
    # 准确性指标
    invalid_records: int = 0
    accuracy_rate: float = 0.0
    
    # 一致性指标
    inconsistent_records: int = 0
    consistency_rate: float = 0.0
    
    # 及时性指标
    delayed_records: int = 0
    timeliness_rate: float = 0.0
    
    # 异常统计
    total_anomalies: int = 0
    anomaly_breakdown: Dict[AnomalyType, int] = field(default_factory=dict)
    
    # 综合评分
    overall_quality_score: float = 0.0


class StatisticalAnomalyDetector:
    """统计异常检测器"""
    
    def __init__(self, 
                 z_threshold: float = 3.0,      # Z-score阈值
                 iqr_multiplier: float = 1.5,   # IQR倍数
                 window_size: int = 100):       # 滑动窗口大小
        
        self.z_threshold = z_threshold
        self.iqr_multiplier = iqr_multiplier
        self.window_size = window_size
        
        self.logger = logging.getLogger(__name__)
    
    def detect_price_anomalies(self, prices: List[float], 
                             current_price: float) -> List[AnomalyEvent]:
        """检测价格异常"""
        anomalies = []
        
        try:
            if len(prices) < 10:
                return anomalies
            
            # Z-score检测
            mean_price = np.mean(prices)
            std_price = np.std(prices)
            
            if std_price > 0:
                z_score = abs(current_price - mean_price) / std_price
                
                if z_score > self.z_threshold:
                    severity = AnomalySeverity.HIGH if z_score > 5 else AnomalySeverity.MEDIUM
                    
                    anomalies.append(AnomalyEvent(
                        symbol="",  # 将在调用处设置
                        timestamp=datetime.now(),
                        anomaly_type=AnomalyType.PRICE_SPIKE,
                        severity=severity,
                        description=f"价格异常波动，Z-score: {z_score:.2f}",
                        confidence=min(z_score / 10, 1.0),
                        metadata={'z_score': z_score, 'mean_price': mean_price, 'std_price': std_price}
                    ))
            
            # IQR检测
            q1 = np.percentile(prices, 25)
            q3 = np.percentile(prices, 75)
            iqr = q3 - q1
            
            lower_bound = q1 - self.iqr_multiplier * iqr
            upper_bound = q3 + self.iqr_multiplier * iqr
            
            if current_price < lower_bound or current_price > upper_bound:
                anomalies.append(AnomalyEvent(
                    symbol="",
                    timestamp=datetime.now(),
                    anomaly_type=AnomalyType.PRICE_SPIKE,
                    severity=AnomalySeverity.MEDIUM,
                    description=f"价格超出IQR范围 [{lower_bound:.2f}, {upper_bound:.2f}]",
                    confidence=0.8,
                    metadata={'iqr': iqr, 'lower_bound': lower_bound, 'upper_bound': upper_bound}
                ))
            
            return anomalies
            
        except Exception as e:
            self.logger.error(f"价格异常检测失败: {e}")
            return []
    
    def detect_volume_anomalies(self, volumes: List[int], 
                              current_volume: int) -> List[AnomalyEvent]:
        """检测成交量异常"""
        anomalies = []
        
        try:
            if len(volumes) < 10:
                return anomalies
            
            # 对数变换处理成交量的偏态分布
            log_volumes = [np.log(max(v, 1)) for v in volumes]
            log_current = np.log(max(current_volume, 1))
            
            mean_log_vol = np.mean(log_volumes)
            std_log_vol = np.std(log_volumes)
            
            if std_log_vol > 0:
                z_score = abs(log_current - mean_log_vol) / std_log_vol
                
                if z_score > self.z_threshold:
                    # 判断是成交量激增还是枯竭
                    if current_volume > np.mean(volumes):
                        anomaly_type = AnomalyType.VOLUME_SURGE
                        description = f"成交量异常激增，Z-score: {z_score:.2f}"
                    else:
                        anomaly_type = AnomalyType.LIQUIDITY_DRY
                        description = f"成交量异常萎缩，Z-score: {z_score:.2f}"
                    
                    severity = AnomalySeverity.HIGH if z_score > 5 else AnomalySeverity.MEDIUM
                    
                    anomalies.append(AnomalyEvent(
                        symbol="",
                        timestamp=datetime.now(),
                        anomaly_type=anomaly_type,
                        severity=severity,
                        description=description,
                        confidence=min(z_score / 10, 1.0),
                        metadata={'z_score': z_score, 'volume_ratio': current_volume / np.mean(volumes)}
                    ))
            
            return anomalies
            
        except Exception as e:
            self.logger.error(f"成交量异常检测失败: {e}")
            return []
    
    def detect_spread_anomalies(self, spreads: List[float], 
                              current_spread: float) -> List[AnomalyEvent]:
        """检测价差异常"""
        anomalies = []
        
        try:
            if len(spreads) < 10 or current_spread <= 0:
                return anomalies
            
            # 过滤无效价差
            valid_spreads = [s for s in spreads if s > 0]
            if len(valid_spreads) < 5:
                return anomalies
            
            mean_spread = np.mean(valid_spreads)
            std_spread = np.std(valid_spreads)
            
            if std_spread > 0:
                z_score = abs(current_spread - mean_spread) / std_spread
                
                if z_score > self.z_threshold:
                    severity = AnomalySeverity.HIGH if z_score > 5 else AnomalySeverity.MEDIUM
                    
                    anomalies.append(AnomalyEvent(
                        symbol="",
                        timestamp=datetime.now(),
                        anomaly_type=AnomalyType.SPREAD_ANOMALY,
                        severity=severity,
                        description=f"买卖价差异常，Z-score: {z_score:.2f}",
                        confidence=min(z_score / 10, 1.0),
                        metadata={'z_score': z_score, 'spread_ratio': current_spread / mean_spread}
                    ))
            
            return anomalies
            
        except Exception as e:
            self.logger.error(f"价差异常检测失败: {e}")
            return []


class DataQualityChecker:
    """数据质量检查器"""
    
    def __init__(self, 
                 max_price_change: float = 0.2,    # 最大价格变化比例
                 max_time_gap: int = 300,          # 最大时间间隔(秒)
                 min_price: float = 0.01,          # 最小有效价格
                 max_price: float = 10000):        # 最大有效价格
        
        self.max_price_change = max_price_change
        self.max_time_gap = max_time_gap
        self.min_price = min_price
        self.max_price = max_price
        
        self.logger = logging.getLogger(__name__)
    
    def validate_tick_data(self, tick: TickData, 
                          previous_tick: Optional[TickData] = None) -> List[AnomalyEvent]:
        """验证tick数据质量"""
        anomalies = []
        
        try:
            # 基本字段检查
            if not tick.symbol or not tick.timestamp:
                anomalies.append(AnomalyEvent(
                    symbol=tick.symbol or "UNKNOWN",
                    timestamp=datetime.now(),
                    anomaly_type=AnomalyType.DATA_MISSING,
                    severity=AnomalySeverity.CRITICAL,
                    description="缺少必要字段（symbol或timestamp）",
                    confidence=1.0,
                    suggested_action="丢弃数据"
                ))
            
            # 价格合理性检查
            if tick.price < self.min_price or tick.price > self.max_price:
                anomalies.append(AnomalyEvent(
                    symbol=tick.symbol,
                    timestamp=tick.timestamp,
                    anomaly_type=AnomalyType.PRICE_SPIKE,
                    severity=AnomalySeverity.HIGH,
                    description=f"价格超出合理范围: {tick.price}",
                    confidence=1.0,
                    suggested_action="标记为异常数据"
                ))
            
            # 成交量检查
            if tick.volume < 0:
                anomalies.append(AnomalyEvent(
                    symbol=tick.symbol,
                    timestamp=tick.timestamp,
                    anomaly_type=AnomalyType.SYSTEM_ERROR,
                    severity=AnomalySeverity.HIGH,
                    description=f"成交量为负数: {tick.volume}",
                    confidence=1.0,
                    suggested_action="修正数据"
                ))
            
            # 时间合理性检查
            now = datetime.now()
            if tick.timestamp > now + timedelta(minutes=5):
                anomalies.append(AnomalyEvent(
                    symbol=tick.symbol,
                    timestamp=tick.timestamp,
                    anomaly_type=AnomalyType.TIME_DISORDER,
                    severity=AnomalySeverity.MEDIUM,
                    description="时间戳超前当前时间",
                    confidence=0.9,
                    suggested_action="调整时间戳"
                ))
            
            # 与前一个tick的关系检查
            if previous_tick:
                # 时间顺序检查
                if tick.timestamp < previous_tick.timestamp:
                    anomalies.append(AnomalyEvent(
                        symbol=tick.symbol,
                        timestamp=tick.timestamp,
                        anomaly_type=AnomalyType.TIME_DISORDER,
                        severity=AnomalySeverity.HIGH,
                        description="时间戳乱序",
                        confidence=1.0,
                        suggested_action="重新排序"
                    ))
                
                # 价格跳跃检查
                if previous_tick.price > 0:
                    price_change = abs(tick.price - previous_tick.price) / previous_tick.price
                    if price_change > self.max_price_change:
                        anomalies.append(AnomalyEvent(
                            symbol=tick.symbol,
                            timestamp=tick.timestamp,
                            anomaly_type=AnomalyType.PRICE_SPIKE,
                            severity=AnomalySeverity.HIGH,
                            description=f"价格跳跃过大: {price_change:.2%}",
                            confidence=0.8,
                            metadata={'price_change': price_change}
                        ))
                
                # 时间间隔检查
                time_gap = (tick.timestamp - previous_tick.timestamp).total_seconds()
                if time_gap > self.max_time_gap:
                    anomalies.append(AnomalyEvent(
                        symbol=tick.symbol,
                        timestamp=tick.timestamp,
                        anomaly_type=AnomalyType.DATA_MISSING,
                        severity=AnomalySeverity.MEDIUM,
                        description=f"数据时间间隔过大: {time_gap}秒",
                        confidence=0.7,
                        metadata={'time_gap': time_gap}
                    ))
            
            return anomalies
            
        except Exception as e:
            self.logger.error(f"验证tick数据失败: {e}")
            return [AnomalyEvent(
                symbol=tick.symbol if tick else "UNKNOWN",
                timestamp=datetime.now(),
                anomaly_type=AnomalyType.SYSTEM_ERROR,
                severity=AnomalySeverity.CRITICAL,
                description=f"数据验证异常: {str(e)}",
                confidence=1.0
            )]
    
    def validate_level2_data(self, level2: Level2Data) -> List[AnomalyEvent]:
        """验证Level-2数据质量"""
        anomalies = []
        
        try:
            # 基本字段检查
            if not level2.symbol or not level2.timestamp:
                anomalies.append(AnomalyEvent(
                    symbol=level2.symbol or "UNKNOWN",
                    timestamp=datetime.now(),
                    anomaly_type=AnomalyType.DATA_MISSING,
                    severity=AnomalySeverity.CRITICAL,
                    description="Level-2数据缺少必要字段",
                    confidence=1.0
                ))
                return anomalies
            
            # 档位数量检查
            if (len(level2.bid_prices) != 5 or len(level2.ask_prices) != 5 or
                len(level2.bid_volumes) != 5 or len(level2.ask_volumes) != 5):
                anomalies.append(AnomalyEvent(
                    symbol=level2.symbol,
                    timestamp=level2.timestamp,
                    anomaly_type=AnomalyType.DATA_MISSING,
                    severity=AnomalySeverity.HIGH,
                    description="Level-2数据档位不完整",
                    confidence=1.0
                ))
            
            # 价格合理性检查
            all_prices = level2.bid_prices + level2.ask_prices
            for i, price in enumerate(all_prices):
                if price < self.min_price or price > self.max_price:
                    anomalies.append(AnomalyEvent(
                        symbol=level2.symbol,
                        timestamp=level2.timestamp,
                        anomaly_type=AnomalyType.PRICE_SPIKE,
                        severity=AnomalySeverity.HIGH,
                        description=f"Level-2价格异常: {price}",
                        confidence=0.9,
                        metadata={'price_index': i}
                    ))
            
            # 买卖盘价格顺序检查
            if len(level2.bid_prices) >= 2:
                for i in range(len(level2.bid_prices) - 1):
                    if level2.bid_prices[i] < level2.bid_prices[i + 1]:
                        anomalies.append(AnomalyEvent(
                            symbol=level2.symbol,
                            timestamp=level2.timestamp,
                            anomaly_type=AnomalyType.PRICE_INVERSION,
                            severity=AnomalySeverity.HIGH,
                            description="买盘价格顺序错误",
                            confidence=1.0,
                            metadata={'bid_index': i}
                        ))
            
            if len(level2.ask_prices) >= 2:
                for i in range(len(level2.ask_prices) - 1):
                    if level2.ask_prices[i] > level2.ask_prices[i + 1]:
                        anomalies.append(AnomalyEvent(
                            symbol=level2.symbol,
                            timestamp=level2.timestamp,
                            anomaly_type=AnomalyType.PRICE_INVERSION,
                            severity=AnomalySeverity.HIGH,
                            description="卖盘价格顺序错误",
                            confidence=1.0,
                            metadata={'ask_index': i}
                        ))
            
            # 买卖价差检查
            if (level2.bid_prices and level2.ask_prices and 
                level2.bid_prices[0] > 0 and level2.ask_prices[0] > 0):
                
                if level2.bid_prices[0] >= level2.ask_prices[0]:
                    anomalies.append(AnomalyEvent(
                        symbol=level2.symbol,
                        timestamp=level2.timestamp,
                        anomaly_type=AnomalyType.PRICE_INVERSION,
                        severity=AnomalySeverity.CRITICAL,
                        description="买一价格高于卖一价格",
                        confidence=1.0,
                        metadata={
                            'bid_price': level2.bid_prices[0],
                            'ask_price': level2.ask_prices[0]
                        }
                    ))
            
            # 成交量检查
            all_volumes = level2.bid_volumes + level2.ask_volumes
            for i, volume in enumerate(all_volumes):
                if volume < 0:
                    anomalies.append(AnomalyEvent(
                        symbol=level2.symbol,
                        timestamp=level2.timestamp,
                        anomaly_type=AnomalyType.SYSTEM_ERROR,
                        severity=AnomalySeverity.HIGH,
                        description=f"Level-2成交量为负: {volume}",
                        confidence=1.0,
                        metadata={'volume_index': i}
                    ))
            
            return anomalies
            
        except Exception as e:
            self.logger.error(f"验证Level-2数据失败: {e}")
            return [AnomalyEvent(
                symbol=level2.symbol if level2 else "UNKNOWN",
                timestamp=datetime.now(),
                anomaly_type=AnomalyType.SYSTEM_ERROR,
                severity=AnomalySeverity.CRITICAL,
                description=f"Level-2数据验证异常: {str(e)}",
                confidence=1.0
            )]


class RealtimeAnomalyDetector:
    """实时异常检测器"""
    
    def __init__(self, 
                 history_window: int = 1000,
                 detection_interval: float = 1.0,
                 alert_threshold: int = 5):
        
        self.history_window = history_window
        self.detection_interval = detection_interval
        self.alert_threshold = alert_threshold
        
        # 历史数据缓存
        self.tick_history: Dict[str, deque] = {}
        self.level2_history: Dict[str, deque] = {}
        self.anomaly_history: Dict[str, deque] = {}
        
        # 检测器组件
        self.statistical_detector = StatisticalAnomalyDetector()
        self.quality_checker = DataQualityChecker()
        
        # 异常统计
        self.anomaly_stats: Dict[str, Dict[AnomalyType, int]] = defaultdict(lambda: defaultdict(int))
        
        self.logger = logging.getLogger(__name__)
    
    def process_tick_data(self, tick: TickData) -> Tuple[bool, List[AnomalyEvent]]:
        """处理tick数据并检测异常"""
        try:
            symbol = tick.symbol
            anomalies = []
            
            # 获取历史数据
            if symbol not in self.tick_history:
                self.tick_history[symbol] = deque(maxlen=self.history_window)
                self.anomaly_history[symbol] = deque(maxlen=self.history_window)
            
            tick_history = self.tick_history[symbol]
            previous_tick = tick_history[-1] if tick_history else None
            
            # 数据质量检查
            quality_anomalies = self.quality_checker.validate_tick_data(tick, previous_tick)
            anomalies.extend(quality_anomalies)
            
            # 统计异常检测
            if len(tick_history) >= 10:
                prices = [t.price for t in tick_history]
                volumes = [t.volume for t in tick_history]
                
                price_anomalies = self.statistical_detector.detect_price_anomalies(prices, tick.price)
                volume_anomalies = self.statistical_detector.detect_volume_anomalies(volumes, tick.volume)
                
                # 设置symbol
                for anomaly in price_anomalies + volume_anomalies:
                    anomaly.symbol = symbol
                
                anomalies.extend(price_anomalies)
                anomalies.extend(volume_anomalies)
            
            # 存储数据
            tick_history.append(tick)
            
            # 记录异常
            if anomalies:
                self.anomaly_history[symbol].extend(anomalies)
                for anomaly in anomalies:
                    self.anomaly_stats[symbol][anomaly.anomaly_type] += 1
            
            # 判断数据是否可用
            is_valid = not any(anomaly.severity in [AnomalySeverity.HIGH, AnomalySeverity.CRITICAL] 
                             for anomaly in anomalies)
            
            return is_valid, anomalies
            
        except Exception as e:
            self.logger.error(f"处理tick数据异常检测失败: {e}")
            return False, [AnomalyEvent(
                symbol=tick.symbol if tick else "UNKNOWN",
                timestamp=datetime.now(),
                anomaly_type=AnomalyType.SYSTEM_ERROR,
                severity=AnomalySeverity.CRITICAL,
                description=f"异常检测系统错误: {str(e)}",
                confidence=1.0
            )]
    
    def process_level2_data(self, level2: Level2Data) -> Tuple[bool, List[AnomalyEvent]]:
        """处理Level-2数据并检测异常"""
        try:
            symbol = level2.symbol
            anomalies = []
            
            # 获取历史数据
            if symbol not in self.level2_history:
                self.level2_history[symbol] = deque(maxlen=self.history_window)
            
            level2_history = self.level2_history[symbol]
            
            # 数据质量检查
            quality_anomalies = self.quality_checker.validate_level2_data(level2)
            anomalies.extend(quality_anomalies)
            
            # 价差异常检测
            if len(level2_history) >= 10:
                spreads = []
                for l2 in level2_history:
                    if (l2.bid_prices and l2.ask_prices and 
                        l2.bid_prices[0] > 0 and l2.ask_prices[0] > 0):
                        spread = l2.ask_prices[0] - l2.bid_prices[0]
                        if spread > 0:
                            spreads.append(spread)
                
                if spreads and level2.bid_prices and level2.ask_prices:
                    current_spread = level2.ask_prices[0] - level2.bid_prices[0]
                    if current_spread > 0:
                        spread_anomalies = self.statistical_detector.detect_spread_anomalies(spreads, current_spread)
                        for anomaly in spread_anomalies:
                            anomaly.symbol = symbol
                        anomalies.extend(spread_anomalies)
            
            # 存储数据
            level2_history.append(level2)
            
            # 记录异常
            if anomalies:
                if symbol not in self.anomaly_history:
                    self.anomaly_history[symbol] = deque(maxlen=self.history_window)
                
                self.anomaly_history[symbol].extend(anomalies)
                for anomaly in anomalies:
                    self.anomaly_stats[symbol][anomaly.anomaly_type] += 1
            
            # 判断数据是否可用
            is_valid = not any(anomaly.severity in [AnomalySeverity.HIGH, AnomalySeverity.CRITICAL] 
                             for anomaly in anomalies)
            
            return is_valid, anomalies
            
        except Exception as e:
            self.logger.error(f"处理Level-2数据异常检测失败: {e}")
            return False, [AnomalyEvent(
                symbol=level2.symbol if level2 else "UNKNOWN",
                timestamp=datetime.now(),
                anomaly_type=AnomalyType.SYSTEM_ERROR,
                severity=AnomalySeverity.CRITICAL,
                description=f"Level-2异常检测系统错误: {str(e)}",
                confidence=1.0
            )]
    
    def clean_tick_data(self, tick: TickData, anomalies: List[AnomalyEvent]) -> Optional[TickData]:
        """清洗tick数据"""
        try:
            # 如果有严重异常，直接丢弃
            critical_anomalies = [a for a in anomalies if a.severity == AnomalySeverity.CRITICAL]
            if critical_anomalies:
                self.logger.warning(f"丢弃严重异常的tick数据: {tick.symbol} @ {tick.timestamp}")
                return None
            
            cleaned_tick = TickData(
                symbol=tick.symbol,
                timestamp=tick.timestamp,
                price=tick.price,
                volume=tick.volume,
                amount=tick.amount,
                direction=tick.direction,
                bid_price=tick.bid_price,
                ask_price=tick.ask_price,
                bid_volume=tick.bid_volume,
                ask_volume=tick.ask_volume
            )
            
            # 修正异常数据
            for anomaly in anomalies:
                if anomaly.anomaly_type == AnomalyType.PRICE_SPIKE and anomaly.severity == AnomalySeverity.HIGH:
                    # 使用历史平均价格替代异常价格
                    if tick.symbol in self.tick_history and len(self.tick_history[tick.symbol]) >= 10:
                        recent_prices = [t.price for t in list(self.tick_history[tick.symbol])[-10:]]
                        cleaned_tick.price = np.median(recent_prices)
                        self.logger.info(f"修正异常价格: {tick.price} -> {cleaned_tick.price}")
                
                elif anomaly.anomaly_type == AnomalyType.SYSTEM_ERROR and "成交量为负数" in anomaly.description:
                    # 修正负成交量
                    cleaned_tick.volume = abs(tick.volume)
                    cleaned_tick.amount = cleaned_tick.price * cleaned_tick.volume
                    self.logger.info(f"修正负成交量: {tick.volume} -> {cleaned_tick.volume}")
            
            return cleaned_tick
            
        except Exception as e:
            self.logger.error(f"清洗tick数据失败: {e}")
            return None
    
    def generate_quality_report(self, symbol: str) -> DataQualityReport:
        """生成数据质量报告"""
        try:
            report = DataQualityReport(
                symbol=symbol,
                timestamp=datetime.now()
            )
            
            # 获取历史数据
            tick_history = self.tick_history.get(symbol, deque())
            anomaly_history = self.anomaly_history.get(symbol, deque())
            
            if not tick_history:
                return report
            
            # 计算完整性指标
            report.total_records = len(tick_history)
            
            # 计算异常统计
            recent_anomalies = [a for a in anomaly_history 
                             if (datetime.now() - a.timestamp).seconds <= 3600]  # 最近1小时
            
            report.total_anomalies = len(recent_anomalies)
            
            # 按类型统计异常
            for anomaly in recent_anomalies:
                if anomaly.anomaly_type not in report.anomaly_breakdown:
                    report.anomaly_breakdown[anomaly.anomaly_type] = 0
                report.anomaly_breakdown[anomaly.anomaly_type] += 1
            
            # 计算质量指标
            if report.total_records > 0:
                invalid_count = sum(1 for a in recent_anomalies 
                                  if a.severity in [AnomalySeverity.HIGH, AnomalySeverity.CRITICAL])
                
                report.invalid_records = invalid_count
                report.accuracy_rate = (report.total_records - invalid_count) / report.total_records * 100
                report.completeness_rate = 100.0  # 简化处理
                report.consistency_rate = 100.0 - (report.total_anomalies / report.total_records * 100)
                report.timeliness_rate = 100.0  # 简化处理
                
                # 综合评分
                weights = [0.3, 0.3, 0.2, 0.2]  # 准确性、完整性、一致性、及时性权重
                scores = [report.accuracy_rate, report.completeness_rate, 
                         report.consistency_rate, report.timeliness_rate]
                report.overall_quality_score = sum(w * s for w, s in zip(weights, scores))
            
            return report
            
        except Exception as e:
            self.logger.error(f"生成质量报告失败: {e}")
            return DataQualityReport(symbol=symbol, timestamp=datetime.now())
    
    def get_anomaly_statistics(self, symbol: str = None) -> Dict[str, Any]:
        """获取异常统计信息"""
        try:
            if symbol:
                # 单个股票的异常统计
                stats = dict(self.anomaly_stats.get(symbol, {}))
                recent_anomalies = []
                
                if symbol in self.anomaly_history:
                    cutoff_time = datetime.now() - timedelta(hours=1)
                    recent_anomalies = [a for a in self.anomaly_history[symbol] 
                                      if a.timestamp >= cutoff_time]
                
                return {
                    'symbol': symbol,
                    'total_anomalies': sum(stats.values()),
                    'anomaly_breakdown': {k.value: v for k, v in stats.items()},
                    'recent_anomalies_count': len(recent_anomalies),
                    'data_points': len(self.tick_history.get(symbol, [])),
                    'quality_score': self.generate_quality_report(symbol).overall_quality_score
                }
            else:
                # 全局异常统计
                total_stats = defaultdict(int)
                for symbol_stats in self.anomaly_stats.values():
                    for anomaly_type, count in symbol_stats.items():
                        total_stats[anomaly_type] += count
                
                return {
                    'total_symbols': len(self.anomaly_stats),
                    'total_anomalies': sum(total_stats.values()),
                    'anomaly_breakdown': {k.value: v for k, v in total_stats.items()},
                    'active_symbols': len(self.tick_history),
                    'total_data_points': sum(len(history) for history in self.tick_history.values())
                }
                
        except Exception as e:
            self.logger.error(f"获取异常统计失败: {e}")
            return {}
    
    def clear_history(self, symbol: str):
        """清空历史数据"""
        if symbol in self.tick_history:
            self.tick_history[symbol].clear()
        if symbol in self.level2_history:
            self.level2_history[symbol].clear()
        if symbol in self.anomaly_history:
            self.anomaly_history[symbol].clear()
        if symbol in self.anomaly_stats:
            del self.anomaly_stats[symbol]
        
        self.logger.info(f"已清空 {symbol} 的异常检测历史数据")
    
    def clear_all_history(self):
        """清空所有历史数据"""
        self.tick_history.clear()
        self.level2_history.clear()
        self.anomaly_history.clear()
        self.anomaly_stats.clear()
        
        self.logger.info("已清空所有异常检测历史数据")