"""
数据采集器模块
实现多数据源接口适配器、数据源管理器和缓存管理器
"""

from .base import (
    BaseDataSource, DataSourceConfig, DataSourceType, DataType,
    StockBasicInfo, PriceData, TickData, Level2Data,
    DataSourceException, DataSourceConnectionError, DataSourceRateLimitError, DataSourceAuthError
)
from .itick_adapter import iTickAdapter
from .joinquant_adapter import JoinQuantAdapter
from .ricequant_adapter import RiceQuantAdapter
from .data_source_manager import DataSourceManager, FailoverStrategy
from .cache_manager import CacheManager, DataCacheDecorator
from .config_factory import DataSourceConfigFactory

__all__ = [
    # 基础类和数据结构
    'BaseDataSource',
    'DataSourceConfig', 
    'DataSourceType',
    'DataType',
    'StockBasicInfo',
    'PriceData',
    'TickData', 
    'Level2Data',
    
    # 异常类
    'DataSourceException',
    'DataSourceConnectionError',
    'DataSourceRateLimitError',
    'DataSourceAuthError',
    
    # 数据源适配器
    'iTickAdapter',
    'JoinQuantAdapter', 
    'RiceQuantAdapter',
    
    # 管理器
    'DataSourceManager',
    'FailoverStrategy',
    'CacheManager',
    'DataCacheDecorator',
    
    # 工厂类
    'DataSourceConfigFactory'
]

# 基础数据采集器接口将在后续任务中实现
# 这里先定义模块结构

__all__ = [
    'BaseDataCollector',
    'iTick Collector',
    'JoinQuantCollector', 
    'RiceQuantCollector',
    'FreeDataCollector'
]