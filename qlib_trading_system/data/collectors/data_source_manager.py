"""
数据源管理器
实现数据源自动切换、容错机制和缓存逻辑
"""
import asyncio
import logging
from typing import Dict, List, Optional, Union, Any, Type
from datetime import datetime, date, timedelta
from dataclasses import dataclass
import pandas as pd
from enum import Enum

from .base import (
    BaseDataSource, DataSourceConfig, DataSourceType, DataType,
    StockBasicInfo, PriceData, TickData, Level2Data,
    DataSourceException, DataSourceConnectionError, DataSourceRateLimitError
)
from .itick_adapter import iTickAdapter
from .joinquant_adapter import JoinQuantAdapter
from .ricequant_adapter import RiceQuantAdapter


class FailoverStrategy(str, Enum):
    """故障转移策略"""
    PRIORITY = "priority"      # 按优先级顺序
    ROUND_ROBIN = "round_robin"  # 轮询
    RANDOM = "random"          # 随机选择
    LOAD_BALANCE = "load_balance"  # 负载均衡


@dataclass
class DataSourceStatus:
    """数据源状态"""
    name: str
    is_available: bool
    last_check_time: datetime
    error_count: int
    success_count: int
    avg_response_time: float
    last_error: Optional[str] = None


class DataSourceManager:
    """数据源管理器"""
    
    def __init__(self, configs: List[DataSourceConfig], failover_strategy: FailoverStrategy = FailoverStrategy.PRIORITY):
        self.configs = configs
        self.failover_strategy = failover_strategy
        self.data_sources: Dict[str, BaseDataSource] = {}
        self.status_map: Dict[str, DataSourceStatus] = {}
        self.logger = logging.getLogger(__name__)
        
        # 数据源适配器映射
        self.adapter_map: Dict[DataSourceType, Type[BaseDataSource]] = {
            DataSourceType.ITICK: iTickAdapter,
            DataSourceType.JOINQUANT: JoinQuantAdapter,
            DataSourceType.RICEQUANT: RiceQuantAdapter
        }
        
        # 初始化数据源
        self._initialize_data_sources()
    
    def _initialize_data_sources(self):
        """初始化数据源"""
        for config in self.configs:
            if not config.enabled:
                continue
                
            adapter_class = self.adapter_map.get(config.type)
            if adapter_class:
                data_source = adapter_class(config)
                self.data_sources[config.name] = data_source
                
                # 初始化状态
                self.status_map[config.name] = DataSourceStatus(
                    name=config.name,
                    is_available=False,
                    last_check_time=datetime.now(),
                    error_count=0,
                    success_count=0,
                    avg_response_time=0.0
                )
            else:
                self.logger.warning(f"不支持的数据源类型: {config.type}")
    
    async def connect_all(self) -> Dict[str, bool]:
        """连接所有数据源"""
        results = {}
        tasks = []
        
        for name, data_source in self.data_sources.items():
            task = asyncio.create_task(self._connect_single(name, data_source))
            tasks.append(task)
        
        task_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        for i, (name, _) in enumerate(self.data_sources.items()):
            result = task_results[i]
            if isinstance(result, Exception):
                results[name] = False
                self.logger.error(f"连接数据源 {name} 失败: {result}")
            else:
                results[name] = result
        
        return results
    
    async def _connect_single(self, name: str, data_source: BaseDataSource) -> bool:
        """连接单个数据源"""
        try:
            start_time = datetime.now()
            success = await data_source.connect()
            response_time = (datetime.now() - start_time).total_seconds()
            
            status = self.status_map[name]
            status.is_available = success
            status.last_check_time = datetime.now()
            
            if success:
                status.success_count += 1
                status.avg_response_time = (status.avg_response_time + response_time) / 2
                self.logger.info(f"数据源 {name} 连接成功")
            else:
                status.error_count += 1
                self.logger.warning(f"数据源 {name} 连接失败")
            
            return success
            
        except Exception as e:
            status = self.status_map[name]
            status.is_available = False
            status.error_count += 1
            status.last_error = str(e)
            self.logger.error(f"连接数据源 {name} 异常: {e}")
            return False
    
    async def disconnect_all(self):
        """断开所有数据源连接"""
        tasks = []
        for data_source in self.data_sources.values():
            task = asyncio.create_task(data_source.disconnect())
            tasks.append(task)
        
        await asyncio.gather(*tasks, return_exceptions=True)
    
    def get_available_sources(self, feature: Optional[DataType] = None) -> List[BaseDataSource]:
        """获取可用的数据源"""
        available_sources = []
        
        for name, data_source in self.data_sources.items():
            status = self.status_map[name]
            if status.is_available and data_source.is_connected:
                if feature is None or data_source.supports_feature(feature):
                    available_sources.append(data_source)
        
        # 按优先级排序
        available_sources.sort(key=lambda x: x.priority)
        return available_sources
    
    def select_data_source(self, feature: DataType) -> Optional[BaseDataSource]:
        """选择数据源"""
        available_sources = self.get_available_sources(feature)
        
        if not available_sources:
            return None
        
        if self.failover_strategy == FailoverStrategy.PRIORITY:
            return available_sources[0]  # 返回优先级最高的
        elif self.failover_strategy == FailoverStrategy.ROUND_ROBIN:
            # 简单轮询实现
            import random
            return random.choice(available_sources)
        else:
            return available_sources[0]
    
    async def _execute_with_failover(self, feature: DataType, method_name: str, *args, **kwargs):
        """带故障转移的执行方法"""
        available_sources = self.get_available_sources(feature)
        
        if not available_sources:
            raise DataSourceException(f"没有可用的数据源支持功能: {feature}")
        
        last_exception = None
        
        for data_source in available_sources:
            try:
                method = getattr(data_source, method_name)
                start_time = datetime.now()
                result = await method(*args, **kwargs)
                
                # 更新成功统计
                status = self.status_map[data_source.name]
                status.success_count += 1
                response_time = (datetime.now() - start_time).total_seconds()
                status.avg_response_time = (status.avg_response_time + response_time) / 2
                
                self.logger.debug(f"使用数据源 {data_source.name} 成功获取数据")
                return result
                
            except DataSourceRateLimitError as e:
                self.logger.warning(f"数据源 {data_source.name} 请求限流: {e}")
                last_exception = e
                continue
                
            except Exception as e:
                self.logger.warning(f"数据源 {data_source.name} 请求失败: {e}")
                
                # 更新错误统计
                status = self.status_map[data_source.name]
                status.error_count += 1
                status.last_error = str(e)
                
                # 如果错误次数过多，标记为不可用
                if status.error_count > 5:
                    status.is_available = False
                    self.logger.error(f"数据源 {data_source.name} 错误次数过多，标记为不可用")
                
                last_exception = e
                continue
        
        # 所有数据源都失败
        raise DataSourceException(f"所有数据源都无法获取数据: {last_exception}")
    
    # 数据获取方法
    async def get_stock_list(self) -> List[StockBasicInfo]:
        """获取股票列表"""
        return await self._execute_with_failover(DataType.HISTORICAL, 'get_stock_list')
    
    async def get_stock_basic_info(self, symbols: List[str]) -> List[StockBasicInfo]:
        """获取股票基础信息"""
        return await self._execute_with_failover(DataType.HISTORICAL, 'get_stock_basic_info', symbols)
    
    async def get_daily_price(self, 
                            symbols: List[str], 
                            start_date: Union[str, date], 
                            end_date: Union[str, date]) -> pd.DataFrame:
        """获取日线数据"""
        return await self._execute_with_failover(DataType.HISTORICAL, 'get_daily_price', symbols, start_date, end_date)
    
    async def get_minute_price(self, 
                             symbol: str, 
                             date: Union[str, date],
                             frequency: str = "1min") -> pd.DataFrame:
        """获取分钟线数据"""
        return await self._execute_with_failover(DataType.HISTORICAL, 'get_minute_price', symbol, date, frequency)
    
    async def get_realtime_price(self, symbols: List[str]) -> List[PriceData]:
        """获取实时价格数据"""
        return await self._execute_with_failover(DataType.REALTIME, 'get_realtime_price', symbols)
    
    async def get_tick_data(self, 
                          symbol: str, 
                          date: Union[str, date]) -> List[TickData]:
        """获取Tick数据"""
        return await self._execute_with_failover(DataType.LEVEL2, 'get_tick_data', symbol, date)
    
    async def get_level2_data(self, 
                            symbol: str, 
                            date: Union[str, date]) -> List[Level2Data]:
        """获取Level-2数据"""
        return await self._execute_with_failover(DataType.LEVEL2, 'get_level2_data', symbol, date)
    
    async def get_financial_data(self, symbols: List[str]) -> pd.DataFrame:
        """获取财务数据"""
        return await self._execute_with_failover(DataType.FUNDAMENTAL, 'get_financial_data', symbols)
    
    async def get_news_data(self, 
                          symbols: Optional[List[str]] = None,
                          start_date: Optional[Union[str, date]] = None,
                          end_date: Optional[Union[str, date]] = None) -> pd.DataFrame:
        """获取新闻数据"""
        return await self._execute_with_failover(DataType.FUNDAMENTAL, 'get_news_data', symbols, start_date, end_date)
    
    # 健康检查和监控
    async def health_check(self) -> Dict[str, DataSourceStatus]:
        """健康检查"""
        tasks = []
        for name, data_source in self.data_sources.items():
            task = asyncio.create_task(self._check_single_source(name, data_source))
            tasks.append(task)
        
        await asyncio.gather(*tasks, return_exceptions=True)
        return self.status_map.copy()
    
    async def _check_single_source(self, name: str, data_source: BaseDataSource):
        """检查单个数据源"""
        try:
            start_time = datetime.now()
            is_available = await data_source.test_connection()
            response_time = (datetime.now() - start_time).total_seconds()
            
            status = self.status_map[name]
            status.is_available = is_available
            status.last_check_time = datetime.now()
            
            if is_available:
                status.success_count += 1
                status.avg_response_time = (status.avg_response_time + response_time) / 2
            else:
                status.error_count += 1
                
        except Exception as e:
            status = self.status_map[name]
            status.is_available = False
            status.error_count += 1
            status.last_error = str(e)
    
    def get_status_summary(self) -> Dict[str, Any]:
        """获取状态摘要"""
        total_sources = len(self.data_sources)
        available_sources = sum(1 for status in self.status_map.values() if status.is_available)
        
        return {
            'total_sources': total_sources,
            'available_sources': available_sources,
            'availability_rate': available_sources / total_sources if total_sources > 0 else 0,
            'sources': {name: {
                'is_available': status.is_available,
                'error_count': status.error_count,
                'success_count': status.success_count,
                'avg_response_time': status.avg_response_time,
                'last_error': status.last_error
            } for name, status in self.status_map.items()}
        }
    
    async def start_health_monitor(self, interval: int = 300):
        """启动健康监控（每5分钟检查一次）"""
        while True:
            try:
                await self.health_check()
                self.logger.info("数据源健康检查完成")
            except Exception as e:
                self.logger.error(f"健康检查异常: {e}")
            
            await asyncio.sleep(interval)