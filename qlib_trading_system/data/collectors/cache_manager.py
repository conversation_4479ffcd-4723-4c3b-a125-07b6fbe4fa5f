"""
数据缓存管理器
实现数据缓存和本地存储逻辑
"""
import asyncio
import json
import pickle
import hashlib
import logging
from typing import Any, Optional, Dict, List, Union
from datetime import datetime, timedelta
from pathlib import Path
import pandas as pd
import redis.asyncio as redis
from dataclasses import asdict

from .base import StockBasicInfo, PriceData, TickData, Level2Data


class CacheManager:
    """缓存管理器"""
    
    def __init__(self, 
                 redis_url: str = "redis://localhost:6379",
                 local_cache_dir: str = "data/cache",
                 default_ttl: int = 3600):  # 默认1小时过期
        self.redis_url = redis_url
        self.local_cache_dir = Path(local_cache_dir)
        self.default_ttl = default_ttl
        self.redis_client = None
        self.logger = logging.getLogger(__name__)
        
        # 创建本地缓存目录
        self.local_cache_dir.mkdir(parents=True, exist_ok=True)
        
        # 不同数据类型的TTL配置
        self.ttl_config = {
            'stock_list': 86400,        # 股票列表：24小时
            'stock_info': 86400,        # 股票信息：24小时
            'daily_price': 3600,        # 日线数据：1小时
            'minute_price': 300,        # 分钟数据：5分钟
            'realtime_price': 10,       # 实时价格：10秒
            'tick_data': 1800,          # Tick数据：30分钟
            'level2_data': 300,         # Level2数据：5分钟
            'financial_data': 86400,    # 财务数据：24小时
            'news_data': 3600           # 新闻数据：1小时
        }
    
    async def connect(self) -> bool:
        """连接Redis"""
        try:
            self.redis_client = redis.from_url(self.redis_url)
            await self.redis_client.ping()
            self.logger.info("Redis连接成功")
            return True
        except Exception as e:
            self.logger.warning(f"Redis连接失败，将使用本地缓存: {e}")
            self.redis_client = None
            return False
    
    async def disconnect(self):
        """断开Redis连接"""
        if self.redis_client:
            await self.redis_client.close()
            self.redis_client = None
    
    def _generate_cache_key(self, data_type: str, **kwargs) -> str:
        """生成缓存键"""
        # 将参数排序并序列化
        sorted_params = sorted(kwargs.items())
        param_str = json.dumps(sorted_params, sort_keys=True, default=str)
        
        # 生成哈希
        hash_obj = hashlib.md5(param_str.encode())
        hash_str = hash_obj.hexdigest()
        
        return f"qlib_trading:{data_type}:{hash_str}"
    
    def _get_local_cache_path(self, cache_key: str) -> Path:
        """获取本地缓存文件路径"""
        # 使用缓存键的哈希作为文件名
        filename = hashlib.md5(cache_key.encode()).hexdigest()
        return self.local_cache_dir / f"{filename}.pkl"
    
    async def get(self, data_type: str, **kwargs) -> Optional[Any]:
        """获取缓存数据"""
        cache_key = self._generate_cache_key(data_type, **kwargs)
        
        # 先尝试从Redis获取
        if self.redis_client:
            try:
                cached_data = await self.redis_client.get(cache_key)
                if cached_data:
                    data = pickle.loads(cached_data)
                    self.logger.debug(f"从Redis缓存获取数据: {cache_key}")
                    return data
            except Exception as e:
                self.logger.warning(f"Redis获取缓存失败: {e}")
        
        # 从本地缓存获取
        local_path = self._get_local_cache_path(cache_key)
        if local_path.exists():
            try:
                # 检查文件是否过期
                file_mtime = datetime.fromtimestamp(local_path.stat().st_mtime)
                ttl = self.ttl_config.get(data_type, self.default_ttl)
                
                if datetime.now() - file_mtime < timedelta(seconds=ttl):
                    with open(local_path, 'rb') as f:
                        data = pickle.load(f)
                    self.logger.debug(f"从本地缓存获取数据: {cache_key}")
                    return data
                else:
                    # 文件过期，删除
                    local_path.unlink()
                    self.logger.debug(f"本地缓存已过期: {cache_key}")
            except Exception as e:
                self.logger.warning(f"本地缓存读取失败: {e}")
        
        return None
    
    async def set(self, data_type: str, data: Any, **kwargs):
        """设置缓存数据"""
        cache_key = self._generate_cache_key(data_type, **kwargs)
        ttl = self.ttl_config.get(data_type, self.default_ttl)
        
        # 序列化数据
        try:
            serialized_data = pickle.dumps(data)
        except Exception as e:
            self.logger.error(f"数据序列化失败: {e}")
            return
        
        # 存储到Redis
        if self.redis_client:
            try:
                await self.redis_client.setex(cache_key, ttl, serialized_data)
                self.logger.debug(f"数据存储到Redis缓存: {cache_key}")
            except Exception as e:
                self.logger.warning(f"Redis存储缓存失败: {e}")
        
        # 存储到本地缓存
        local_path = self._get_local_cache_path(cache_key)
        try:
            with open(local_path, 'wb') as f:
                f.write(serialized_data)
            self.logger.debug(f"数据存储到本地缓存: {cache_key}")
        except Exception as e:
            self.logger.warning(f"本地缓存存储失败: {e}")
    
    async def delete(self, data_type: str, **kwargs):
        """删除缓存数据"""
        cache_key = self._generate_cache_key(data_type, **kwargs)
        
        # 从Redis删除
        if self.redis_client:
            try:
                await self.redis_client.delete(cache_key)
                self.logger.debug(f"从Redis删除缓存: {cache_key}")
            except Exception as e:
                self.logger.warning(f"Redis删除缓存失败: {e}")
        
        # 从本地删除
        local_path = self._get_local_cache_path(cache_key)
        if local_path.exists():
            try:
                local_path.unlink()
                self.logger.debug(f"从本地删除缓存: {cache_key}")
            except Exception as e:
                self.logger.warning(f"本地缓存删除失败: {e}")
    
    async def clear_expired(self):
        """清理过期的本地缓存"""
        try:
            current_time = datetime.now()
            cleared_count = 0
            
            for cache_file in self.local_cache_dir.glob("*.pkl"):
                try:
                    file_mtime = datetime.fromtimestamp(cache_file.stat().st_mtime)
                    # 使用最大TTL作为清理标准
                    max_ttl = max(self.ttl_config.values())
                    
                    if current_time - file_mtime > timedelta(seconds=max_ttl):
                        cache_file.unlink()
                        cleared_count += 1
                except Exception as e:
                    self.logger.warning(f"清理缓存文件失败 {cache_file}: {e}")
            
            if cleared_count > 0:
                self.logger.info(f"清理了 {cleared_count} 个过期缓存文件")
                
        except Exception as e:
            self.logger.error(f"清理过期缓存失败: {e}")
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        stats = {
            'redis_connected': self.redis_client is not None,
            'local_cache_dir': str(self.local_cache_dir),
            'local_cache_files': 0,
            'local_cache_size': 0
        }
        
        # 统计本地缓存
        try:
            cache_files = list(self.local_cache_dir.glob("*.pkl"))
            stats['local_cache_files'] = len(cache_files)
            stats['local_cache_size'] = sum(f.stat().st_size for f in cache_files)
        except Exception as e:
            self.logger.warning(f"统计本地缓存失败: {e}")
        
        # 统计Redis缓存
        if self.redis_client:
            try:
                info = await self.redis_client.info()
                stats['redis_memory_used'] = info.get('used_memory', 0)
                stats['redis_keys'] = info.get('db0', {}).get('keys', 0)
            except Exception as e:
                self.logger.warning(f"统计Redis缓存失败: {e}")
        
        return stats
    
    # 便捷方法
    async def get_stock_list_cached(self) -> Optional[List[StockBasicInfo]]:
        """获取缓存的股票列表"""
        return await self.get('stock_list')
    
    async def set_stock_list_cached(self, stock_list: List[StockBasicInfo]):
        """缓存股票列表"""
        await self.set('stock_list', stock_list)
    
    async def get_daily_price_cached(self, 
                                   symbols: List[str], 
                                   start_date: Union[str, datetime], 
                                   end_date: Union[str, datetime]) -> Optional[pd.DataFrame]:
        """获取缓存的日线数据"""
        return await self.get('daily_price', 
                            symbols=symbols, 
                            start_date=str(start_date), 
                            end_date=str(end_date))
    
    async def set_daily_price_cached(self, 
                                   data: pd.DataFrame,
                                   symbols: List[str], 
                                   start_date: Union[str, datetime], 
                                   end_date: Union[str, datetime]):
        """缓存日线数据"""
        await self.set('daily_price', data,
                      symbols=symbols, 
                      start_date=str(start_date), 
                      end_date=str(end_date))
    
    async def get_realtime_price_cached(self, symbols: List[str]) -> Optional[List[PriceData]]:
        """获取缓存的实时价格"""
        return await self.get('realtime_price', symbols=symbols)
    
    async def set_realtime_price_cached(self, data: List[PriceData], symbols: List[str]):
        """缓存实时价格"""
        await self.set('realtime_price', data, symbols=symbols)
    
    async def start_cleanup_task(self, interval: int = 3600):
        """启动定期清理任务（每小时清理一次）"""
        while True:
            try:
                await self.clear_expired()
            except Exception as e:
                self.logger.error(f"定期清理任务异常: {e}")
            
            await asyncio.sleep(interval)


class DataCacheDecorator:
    """数据缓存装饰器"""
    
    def __init__(self, cache_manager: CacheManager, data_type: str, ttl: Optional[int] = None):
        self.cache_manager = cache_manager
        self.data_type = data_type
        self.ttl = ttl
    
    def __call__(self, func):
        async def wrapper(*args, **kwargs):
            # 生成缓存键参数
            cache_kwargs = {}
            
            # 从函数参数中提取缓存键参数
            import inspect
            sig = inspect.signature(func)
            bound_args = sig.bind(*args, **kwargs)
            bound_args.apply_defaults()
            
            for param_name, param_value in bound_args.arguments.items():
                if param_name != 'self':  # 排除self参数
                    cache_kwargs[param_name] = param_value
            
            # 尝试从缓存获取
            cached_result = await self.cache_manager.get(self.data_type, **cache_kwargs)
            if cached_result is not None:
                return cached_result
            
            # 执行原函数
            result = await func(*args, **kwargs)
            
            # 存储到缓存
            if result is not None:
                await self.cache_manager.set(self.data_type, result, **cache_kwargs)
            
            return result
        
        return wrapper