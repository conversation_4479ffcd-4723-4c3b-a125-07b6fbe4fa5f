"""
iTick数据源适配器
主要数据源，提供实时行情、历史数据、Level-2数据
"""
import asyncio
import aiohttp
import pandas as pd
from typing import List, Union, Optional
from datetime import datetime, date
import json
import hashlib
import hmac
import time
from urllib.parse import urlencode

from .base import (
    BaseDataSource, DataSourceConfig, DataSourceType, DataType,
    StockBasicInfo, PriceData, TickData, Level2Data,
    DataSourceException, DataSourceConnectionError, DataSourceAuthError
)


class iTickAdapter(BaseDataSource):
    """iTick数据源适配器"""
    
    def __init__(self, config: DataSourceConfig):
        super().__init__(config)
        self.base_url = config.base_url or "https://api.itick.com"
        self.api_key = config.api_key
        self.secret_key = config.secret_key
        self.session = None
        
        # iTick支持的功能
        self.features = [
            DataType.REALTIME,
            DataType.HISTORICAL,
            DataType.LEVEL2
        ]
    
    async def connect(self) -> bool:
        """连接iTick API"""
        try:
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=self.config.timeout)
            )
            
            # 测试连接
            if await self.test_connection():
                self.is_connected = True
                return True
            else:
                await self.disconnect()
                return False
                
        except Exception as e:
            self.last_error = e
            raise DataSourceConnectionError(f"iTick连接失败: {str(e)}")
    
    async def disconnect(self) -> bool:
        """断开连接"""
        try:
            if self.session:
                await self.session.close()
                self.session = None
            self.is_connected = False
            return True
        except Exception as e:
            self.last_error = e
            return False
    
    async def test_connection(self) -> bool:
        """测试连接"""
        try:
            url = f"{self.base_url}/api/v1/ping"
            headers = self._get_auth_headers("GET", "/api/v1/ping")
            
            async with self.session.get(url, headers=headers) as response:
                if response.status == 200:
                    return True
                elif response.status == 401:
                    raise DataSourceAuthError("iTick认证失败，请检查API密钥")
                else:
                    return False
                    
        except Exception as e:
            self.last_error = e
            return False
    
    def _get_auth_headers(self, method: str, path: str, params: dict = None) -> dict:
        """生成认证头"""
        timestamp = str(int(time.time()))
        
        # 构建签名字符串
        sign_string = f"{method}\n{path}\n{timestamp}"
        if params:
            query_string = urlencode(sorted(params.items()))
            sign_string += f"\n{query_string}"
        
        # 生成签名
        signature = hmac.new(
            self.secret_key.encode('utf-8'),
            sign_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        return {
            'X-API-KEY': self.api_key,
            'X-TIMESTAMP': timestamp,
            'X-SIGNATURE': signature,
            'Content-Type': 'application/json'
        }
    
    async def get_stock_list(self) -> List[StockBasicInfo]:
        """获取股票列表"""
        try:
            url = f"{self.base_url}/api/v1/stocks/list"
            headers = self._get_auth_headers("GET", "/api/v1/stocks/list")
            
            async with self.session.get(url, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    stocks = []
                    
                    for item in data.get('data', []):
                        stock = StockBasicInfo(
                            symbol=item['symbol'],
                            name=item['name'],
                            industry=item.get('industry', ''),
                            market=item['market'],
                            market_cap=item.get('market_cap'),
                            listing_date=datetime.strptime(item['listing_date'], '%Y-%m-%d').date() if item.get('listing_date') else None,
                            is_st=item.get('is_st', False),
                            is_suspended=item.get('is_suspended', False)
                        )
                        stocks.append(stock)
                    
                    return stocks
                else:
                    raise DataSourceException(f"获取股票列表失败: {response.status}")
                    
        except Exception as e:
            raise DataSourceException(f"iTick获取股票列表失败: {str(e)}")
    
    async def get_stock_basic_info(self, symbols: List[str]) -> List[StockBasicInfo]:
        """获取股票基础信息"""
        try:
            params = {'symbols': ','.join(symbols)}
            url = f"{self.base_url}/api/v1/stocks/info"
            headers = self._get_auth_headers("GET", "/api/v1/stocks/info", params)
            
            async with self.session.get(url, headers=headers, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    stocks = []
                    
                    for item in data.get('data', []):
                        stock = StockBasicInfo(
                            symbol=item['symbol'],
                            name=item['name'],
                            industry=item.get('industry', ''),
                            market=item['market'],
                            market_cap=item.get('market_cap'),
                            listing_date=datetime.strptime(item['listing_date'], '%Y-%m-%d').date() if item.get('listing_date') else None,
                            is_st=item.get('is_st', False),
                            is_suspended=item.get('is_suspended', False)
                        )
                        stocks.append(stock)
                    
                    return stocks
                else:
                    raise DataSourceException(f"获取股票信息失败: {response.status}")
                    
        except Exception as e:
            raise DataSourceException(f"iTick获取股票信息失败: {str(e)}")
    
    async def get_daily_price(self, 
                            symbols: List[str], 
                            start_date: Union[str, date], 
                            end_date: Union[str, date]) -> pd.DataFrame:
        """获取日线数据"""
        try:
            # 转换日期格式
            if isinstance(start_date, date):
                start_date = start_date.strftime('%Y-%m-%d')
            if isinstance(end_date, date):
                end_date = end_date.strftime('%Y-%m-%d')
            
            params = {
                'symbols': ','.join(symbols),
                'start_date': start_date,
                'end_date': end_date,
                'frequency': 'daily'
            }
            
            url = f"{self.base_url}/api/v1/market/kline"
            headers = self._get_auth_headers("GET", "/api/v1/market/kline", params)
            
            async with self.session.get(url, headers=headers, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    # 转换为DataFrame
                    df_list = []
                    for symbol_data in data.get('data', []):
                        symbol = symbol_data['symbol']
                        for kline in symbol_data['klines']:
                            df_list.append({
                                'symbol': symbol,
                                'date': kline['date'],
                                'open': float(kline['open']),
                                'high': float(kline['high']),
                                'low': float(kline['low']),
                                'close': float(kline['close']),
                                'volume': int(kline['volume']),
                                'amount': float(kline['amount']),
                                'pre_close': float(kline.get('pre_close', 0)),
                                'change': float(kline.get('change', 0)),
                                'pct_change': float(kline.get('pct_change', 0))
                            })
                    
                    df = pd.DataFrame(df_list)
                    if not df.empty:
                        df['date'] = pd.to_datetime(df['date'])
                        df.set_index(['symbol', 'date'], inplace=True)
                    
                    return df
                else:
                    raise DataSourceException(f"获取日线数据失败: {response.status}")
                    
        except Exception as e:
            raise DataSourceException(f"iTick获取日线数据失败: {str(e)}")
    
    async def get_minute_price(self, 
                             symbol: str, 
                             date: Union[str, date],
                             frequency: str = "1min") -> pd.DataFrame:
        """获取分钟线数据"""
        try:
            if isinstance(date, date):
                date = date.strftime('%Y-%m-%d')
            
            params = {
                'symbol': symbol,
                'date': date,
                'frequency': frequency
            }
            
            url = f"{self.base_url}/api/v1/market/minute"
            headers = self._get_auth_headers("GET", "/api/v1/market/minute", params)
            
            async with self.session.get(url, headers=headers, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    # 转换为DataFrame
                    df_list = []
                    for minute_data in data.get('data', []):
                        df_list.append({
                            'symbol': symbol,
                            'datetime': minute_data['datetime'],
                            'open': float(minute_data['open']),
                            'high': float(minute_data['high']),
                            'low': float(minute_data['low']),
                            'close': float(minute_data['close']),
                            'volume': int(minute_data['volume']),
                            'amount': float(minute_data['amount'])
                        })
                    
                    df = pd.DataFrame(df_list)
                    if not df.empty:
                        df['datetime'] = pd.to_datetime(df['datetime'])
                        df.set_index('datetime', inplace=True)
                    
                    return df
                else:
                    raise DataSourceException(f"获取分钟线数据失败: {response.status}")
                    
        except Exception as e:
            raise DataSourceException(f"iTick获取分钟线数据失败: {str(e)}")
    
    async def get_realtime_price(self, symbols: List[str]) -> List[PriceData]:
        """获取实时价格数据"""
        try:
            params = {'symbols': ','.join(symbols)}
            url = f"{self.base_url}/api/v1/market/realtime"
            headers = self._get_auth_headers("GET", "/api/v1/market/realtime", params)
            
            async with self.session.get(url, headers=headers, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    prices = []
                    
                    for item in data.get('data', []):
                        price = PriceData(
                            symbol=item['symbol'],
                            timestamp=datetime.fromisoformat(item['timestamp']),
                            open=float(item['open']),
                            high=float(item['high']),
                            low=float(item['low']),
                            close=float(item['close']),
                            volume=int(item['volume']),
                            amount=float(item['amount']),
                            pre_close=float(item.get('pre_close', 0)),
                            change=float(item.get('change', 0)),
                            pct_change=float(item.get('pct_change', 0))
                        )
                        prices.append(price)
                    
                    return prices
                else:
                    raise DataSourceException(f"获取实时价格失败: {response.status}")
                    
        except Exception as e:
            raise DataSourceException(f"iTick获取实时价格失败: {str(e)}")
    
    async def get_tick_data(self, 
                          symbol: str, 
                          date: Union[str, date]) -> List[TickData]:
        """获取Tick数据"""
        try:
            if isinstance(date, date):
                date = date.strftime('%Y-%m-%d')
            
            params = {
                'symbol': symbol,
                'date': date
            }
            
            url = f"{self.base_url}/api/v1/market/tick"
            headers = self._get_auth_headers("GET", "/api/v1/market/tick", params)
            
            async with self.session.get(url, headers=headers, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    ticks = []
                    
                    for item in data.get('data', []):
                        tick = TickData(
                            symbol=symbol,
                            timestamp=datetime.fromisoformat(item['timestamp']),
                            price=float(item['price']),
                            volume=int(item['volume']),
                            amount=float(item['amount']),
                            direction=item['direction'],
                            bid_price=float(item.get('bid_price', 0)),
                            ask_price=float(item.get('ask_price', 0)),
                            bid_volume=int(item.get('bid_volume', 0)),
                            ask_volume=int(item.get('ask_volume', 0))
                        )
                        ticks.append(tick)
                    
                    return ticks
                else:
                    raise DataSourceException(f"获取Tick数据失败: {response.status}")
                    
        except Exception as e:
            raise DataSourceException(f"iTick获取Tick数据失败: {str(e)}")
    
    async def get_level2_data(self, 
                            symbol: str, 
                            date: Union[str, date]) -> List[Level2Data]:
        """获取Level-2数据"""
        try:
            if isinstance(date, date):
                date = date.strftime('%Y-%m-%d')
            
            params = {
                'symbol': symbol,
                'date': date
            }
            
            url = f"{self.base_url}/api/v1/market/level2"
            headers = self._get_auth_headers("GET", "/api/v1/market/level2", params)
            
            async with self.session.get(url, headers=headers, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    level2_data = []
                    
                    for item in data.get('data', []):
                        level2 = Level2Data(
                            symbol=symbol,
                            timestamp=datetime.fromisoformat(item['timestamp']),
                            bid_prices=[float(p) for p in item['bid_prices']],
                            bid_volumes=[int(v) for v in item['bid_volumes']],
                            ask_prices=[float(p) for p in item['ask_prices']],
                            ask_volumes=[int(v) for v in item['ask_volumes']],
                            total_bid_volume=int(item['total_bid_volume']),
                            total_ask_volume=int(item['total_ask_volume'])
                        )
                        level2_data.append(level2)
                    
                    return level2_data
                else:
                    raise DataSourceException(f"获取Level-2数据失败: {response.status}")
                    
        except Exception as e:
            raise DataSourceException(f"iTick获取Level-2数据失败: {str(e)}")