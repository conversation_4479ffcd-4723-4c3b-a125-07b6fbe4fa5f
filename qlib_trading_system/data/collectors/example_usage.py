"""
数据源适配器使用示例
演示如何使用多数据源接口适配器
"""
import asyncio
import logging
from datetime import datetime, date, timedelta
from typing import List

from .config_factory import DataSourceConfigFactory
from .data_source_manager import DataSourceManager, FailoverStrategy
from .cache_manager import CacheManager


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def example_basic_usage():
    """基础使用示例"""
    logger.info("=== 基础使用示例 ===")
    
    # 1. 创建数据源配置
    configs = DataSourceConfigFactory.create_default_config()
    logger.info(f"创建了 {len(configs)} 个数据源配置")
    
    # 2. 创建数据源管理器
    manager = DataSourceManager(configs, FailoverStrategy.PRIORITY)
    
    # 3. 连接所有数据源
    connection_results = await manager.connect_all()
    logger.info(f"数据源连接结果: {connection_results}")
    
    # 4. 获取可用数据源状态
    status_summary = manager.get_status_summary()
    logger.info(f"数据源状态: {status_summary}")
    
    # 5. 获取股票列表（演示故障转移）
    try:
        stock_list = await manager.get_stock_list()
        logger.info(f"获取到 {len(stock_list)} 只股票")
        
        # 显示前5只股票信息
        for i, stock in enumerate(stock_list[:5]):
            logger.info(f"股票 {i+1}: {stock.symbol} - {stock.name} ({stock.industry})")
            
    except Exception as e:
        logger.error(f"获取股票列表失败: {e}")
    
    # 6. 断开连接
    await manager.disconnect_all()
    logger.info("已断开所有数据源连接")


async def example_with_cache():
    """带缓存的使用示例"""
    logger.info("=== 带缓存的使用示例 ===")
    
    # 1. 创建缓存管理器
    cache_manager = CacheManager(
        redis_url="redis://localhost:6379",
        local_cache_dir="data/cache",
        default_ttl=3600
    )
    
    # 连接缓存
    cache_connected = await cache_manager.connect()
    logger.info(f"缓存连接状态: {cache_connected}")
    
    # 2. 创建数据源管理器
    configs = DataSourceConfigFactory.create_default_config()
    manager = DataSourceManager(configs)
    
    await manager.connect_all()
    
    # 3. 测试缓存功能
    symbols = ["000001.SZ", "000002.SZ", "600000.SH"]
    end_date = date.today()
    start_date = end_date - timedelta(days=30)
    
    try:
        # 第一次获取（从数据源）
        logger.info("第一次获取日线数据...")
        start_time = datetime.now()
        
        # 检查缓存
        cached_data = await cache_manager.get_daily_price_cached(
            symbols, start_date, end_date
        )
        
        if cached_data is not None:
            logger.info("从缓存获取数据")
            daily_data = cached_data
        else:
            logger.info("从数据源获取数据")
            daily_data = await manager.get_daily_price(symbols, start_date, end_date)
            
            # 存储到缓存
            await cache_manager.set_daily_price_cached(
                daily_data, symbols, start_date, end_date
            )
        
        first_time = (datetime.now() - start_time).total_seconds()
        logger.info(f"第一次获取耗时: {first_time:.2f}秒，数据量: {len(daily_data)}")
        
        # 第二次获取（从缓存）
        logger.info("第二次获取日线数据...")
        start_time = datetime.now()
        
        cached_data = await cache_manager.get_daily_price_cached(
            symbols, start_date, end_date
        )
        
        second_time = (datetime.now() - start_time).total_seconds()
        logger.info(f"第二次获取耗时: {second_time:.2f}秒")
        
        if cached_data is not None:
            logger.info("成功从缓存获取数据")
        
    except Exception as e:
        logger.error(f"获取数据失败: {e}")
    
    # 4. 获取缓存统计
    cache_stats = await cache_manager.get_cache_stats()
    logger.info(f"缓存统计: {cache_stats}")
    
    # 5. 清理
    await manager.disconnect_all()
    await cache_manager.disconnect()


async def example_health_monitoring():
    """健康监控示例"""
    logger.info("=== 健康监控示例 ===")
    
    # 创建数据源管理器
    configs = DataSourceConfigFactory.create_default_config()
    manager = DataSourceManager(configs)
    
    await manager.connect_all()
    
    # 执行健康检查
    health_status = await manager.health_check()
    
    logger.info("数据源健康状态:")
    for name, status in health_status.items():
        logger.info(f"  {name}: 可用={status.is_available}, "
                   f"成功={status.success_count}, "
                   f"错误={status.error_count}, "
                   f"响应时间={status.avg_response_time:.2f}s")
    
    await manager.disconnect_all()


async def example_custom_config():
    """自定义配置示例"""
    logger.info("=== 自定义配置示例 ===")
    
    # 创建自定义iTick配置
    itick_config = DataSourceConfigFactory.create_itick_config(
        api_key="your_api_key",
        secret_key="your_secret_key",
        priority=1,
        rate_limit=200,  # 自定义请求限制
        timeout=60       # 自定义超时时间
    )
    
    # 验证配置
    errors = DataSourceConfigFactory.validate_config(itick_config)
    if errors:
        logger.error(f"配置验证失败: {errors}")
    else:
        logger.info("配置验证通过")
    
    # 获取推荐配置方案
    recommended = DataSourceConfigFactory.get_recommended_config()
    logger.info("推荐的数据源配置方案:")
    logger.info(f"主要数据源: {recommended['primary']}")
    logger.info(f"备用数据源: {len(recommended['secondary'])} 个")
    logger.info(f"免费补充数据源: {len(recommended['free_supplements'])} 个")


async def main():
    """主函数"""
    try:
        # 运行所有示例
        await example_basic_usage()
        await asyncio.sleep(1)
        
        await example_with_cache()
        await asyncio.sleep(1)
        
        await example_health_monitoring()
        await asyncio.sleep(1)
        
        await example_custom_config()
        
    except Exception as e:
        logger.error(f"示例运行失败: {e}")


if __name__ == "__main__":
    # 运行示例
    asyncio.run(main())