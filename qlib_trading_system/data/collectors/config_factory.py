"""
数据源配置工厂
根据推荐的数据源方案创建配置
"""
import os
from typing import List, Dict, Any
from .base import DataSourceConfig, DataSourceType, DataType


class DataSourceConfigFactory:
    """数据源配置工厂"""
    
    @staticmethod
    def create_default_config() -> List[DataSourceConfig]:
        """创建默认的数据源配置（基于推荐方案）"""
        configs = []
        
        # 主要数据源：iTick
        itick_config = DataSourceConfig(
            name="iTick",
            type=DataSourceType.ITICK,
            priority=1,
            features=[DataType.REALTIME, DataType.HISTORICAL, DataType.LEVEL2],
            cost="low",
            api_key=os.getenv("ITICK_API_KEY", ""),
            secret_key=os.getenv("ITICK_SECRET_KEY", ""),
            base_url="https://api.itick.com",
            rate_limit=100,
            timeout=30,
            retry_times=3,
            enabled=bool(os.getenv("ITICK_API_KEY"))
        )
        configs.append(itick_config)
        
        # 备用数据源1：聚宽
        joinquant_config = DataSourceConfig(
            name="JoinQuant",
            type=DataSourceType.JOINQUANT,
            priority=2,
            features=[DataType.HISTORICAL, DataType.FUNDAMENTAL],
            cost="free",
            username=os.getenv("JOINQUANT_USERNAME", ""),
            password=os.getenv("JOINQUANT_PASSWORD", ""),
            base_url="https://dataapi.joinquant.com",
            rate_limit=50,
            timeout=30,
            retry_times=3,
            enabled=bool(os.getenv("JOINQUANT_USERNAME"))
        )
        configs.append(joinquant_config)
        
        # 备用数据源2：米筐
        ricequant_config = DataSourceConfig(
            name="RiceQuant",
            type=DataSourceType.RICEQUANT,
            priority=3,
            features=[DataType.REALTIME, DataType.HISTORICAL, DataType.FUNDAMENTAL],
            cost="medium",
            api_key=os.getenv("RICEQUANT_API_KEY", ""),
            secret_key=os.getenv("RICEQUANT_SECRET_KEY", ""),
            base_url="https://api.ricequant.com",
            rate_limit=80,
            timeout=30,
            retry_times=3,
            enabled=bool(os.getenv("RICEQUANT_API_KEY"))
        )
        configs.append(ricequant_config)
        
        return configs
    
    @staticmethod
    def create_itick_config(api_key: str, secret_key: str, **kwargs) -> DataSourceConfig:
        """创建iTick配置"""
        return DataSourceConfig(
            name="iTick",
            type=DataSourceType.ITICK,
            priority=kwargs.get('priority', 1),
            features=[DataType.REALTIME, DataType.HISTORICAL, DataType.LEVEL2],
            cost="low",
            api_key=api_key,
            secret_key=secret_key,
            base_url=kwargs.get('base_url', "https://api.itick.com"),
            rate_limit=kwargs.get('rate_limit', 100),
            timeout=kwargs.get('timeout', 30),
            retry_times=kwargs.get('retry_times', 3),
            enabled=kwargs.get('enabled', True)
        )
    
    @staticmethod
    def create_joinquant_config(username: str, password: str, **kwargs) -> DataSourceConfig:
        """创建聚宽配置"""
        return DataSourceConfig(
            name="JoinQuant",
            type=DataSourceType.JOINQUANT,
            priority=kwargs.get('priority', 2),
            features=[DataType.HISTORICAL, DataType.FUNDAMENTAL],
            cost="free",
            username=username,
            password=password,
            base_url=kwargs.get('base_url', "https://dataapi.joinquant.com"),
            rate_limit=kwargs.get('rate_limit', 50),
            timeout=kwargs.get('timeout', 30),
            retry_times=kwargs.get('retry_times', 3),
            enabled=kwargs.get('enabled', True)
        )
    
    @staticmethod
    def create_ricequant_config(api_key: str, secret_key: str, **kwargs) -> DataSourceConfig:
        """创建米筐配置"""
        return DataSourceConfig(
            name="RiceQuant",
            type=DataSourceType.RICEQUANT,
            priority=kwargs.get('priority', 3),
            features=[DataType.REALTIME, DataType.HISTORICAL, DataType.FUNDAMENTAL],
            cost="medium",
            api_key=api_key,
            secret_key=secret_key,
            base_url=kwargs.get('base_url', "https://api.ricequant.com"),
            rate_limit=kwargs.get('rate_limit', 80),
            timeout=kwargs.get('timeout', 30),
            retry_times=kwargs.get('retry_times', 3),
            enabled=kwargs.get('enabled', True)
        )
    
    @staticmethod
    def create_custom_config(name: str, 
                           data_source_type: DataSourceType,
                           features: List[DataType],
                           **kwargs) -> DataSourceConfig:
        """创建自定义配置"""
        return DataSourceConfig(
            name=name,
            type=data_source_type,
            priority=kwargs.get('priority', 10),
            features=features,
            cost=kwargs.get('cost', 'unknown'),
            api_key=kwargs.get('api_key'),
            secret_key=kwargs.get('secret_key'),
            username=kwargs.get('username'),
            password=kwargs.get('password'),
            base_url=kwargs.get('base_url'),
            rate_limit=kwargs.get('rate_limit', 60),
            timeout=kwargs.get('timeout', 30),
            retry_times=kwargs.get('retry_times', 3),
            enabled=kwargs.get('enabled', True)
        )
    
    @staticmethod
    def get_recommended_config() -> Dict[str, Any]:
        """获取推荐的数据源配置方案"""
        return {
            'primary': {
                'name': 'iTick',
                'type': 'paid',
                'priority': 1,
                'features': ['realtime', 'historical', 'level2'],
                'cost': 'low',
                'description': '价格便宜（月费几百元），数据质量好，支持实时行情'
            },
            'secondary': [
                {
                    'name': 'JoinQuant', 
                    'type': 'freemium',
                    'priority': 2,
                    'features': ['fundamental', 'historical'],
                    'cost': 'free',
                    'description': '免费额度较高，数据全面，社区活跃'
                },
                {
                    'name': 'RiceQuant',
                    'type': 'paid', 
                    'priority': 3,
                    'features': ['realtime', 'historical'],
                    'cost': 'medium',
                    'description': '数据质量高，支持期货和股票'
                }
            ],
            'free_supplements': [
                {'name': 'Sina', 'features': ['realtime_basic']},
                {'name': 'Tencent', 'features': ['basic_info']},
                {'name': 'EastMoney', 'features': ['news', 'announcements']},
                {'name': 'Xueqiu', 'features': ['sentiment']}
            ]
        }
    
    @staticmethod
    def validate_config(config: DataSourceConfig) -> List[str]:
        """验证配置"""
        errors = []
        
        if not config.name:
            errors.append("数据源名称不能为空")
        
        if not config.features:
            errors.append("必须指定至少一个支持的功能")
        
        # 验证认证信息
        if config.type == DataSourceType.ITICK:
            if not config.api_key or not config.secret_key:
                errors.append("iTick需要API密钥和密钥")
        elif config.type == DataSourceType.JOINQUANT:
            if not config.username or not config.password:
                errors.append("聚宽需要用户名和密码")
        elif config.type == DataSourceType.RICEQUANT:
            if not config.api_key or not config.secret_key:
                errors.append("米筐需要API密钥和密钥")
        
        if config.rate_limit <= 0:
            errors.append("请求频率限制必须大于0")
        
        if config.timeout <= 0:
            errors.append("超时时间必须大于0")
        
        if config.retry_times < 0:
            errors.append("重试次数不能小于0")
        
        return errors