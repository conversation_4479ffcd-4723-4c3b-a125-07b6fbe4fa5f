"""
米筐(RiceQuant)数据源适配器
备用数据源，提供实时行情和历史数据
"""
import asyncio
import aiohttp
import pandas as pd
from typing import List, Union, Optional
from datetime import datetime, date
import json
import hashlib
import hmac
import time
from urllib.parse import urlencode

from .base import (
    BaseDataSource, DataSourceConfig, DataSourceType, DataType,
    StockBasicInfo, PriceData,
    DataSourceException, DataSourceConnectionError, DataSourceAuthError
)


class RiceQuantAdapter(BaseDataSource):
    """米筐数据源适配器"""
    
    def __init__(self, config: DataSourceConfig):
        super().__init__(config)
        self.base_url = config.base_url or "https://api.ricequant.com"
        self.api_key = config.api_key
        self.secret_key = config.secret_key
        self.session = None
        
        # 米筐支持的功能
        self.features = [
            DataType.REALTIME,
            DataType.HISTORICAL,
            DataType.FUNDAMENTAL
        ]
    
    async def connect(self) -> bool:
        """连接米筐API"""
        try:
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=self.config.timeout)
            )
            
            # 测试连接
            if await self.test_connection():
                self.is_connected = True
                return True
            else:
                await self.disconnect()
                return False
                
        except Exception as e:
            self.last_error = e
            raise DataSourceConnectionError(f"米筐连接失败: {str(e)}")
    
    async def disconnect(self) -> bool:
        """断开连接"""
        try:
            if self.session:
                await self.session.close()
                self.session = None
            self.is_connected = False
            return True
        except Exception as e:
            self.last_error = e
            return False
    
    async def test_connection(self) -> bool:
        """测试连接"""
        try:
            url = f"{self.base_url}/v1/ping"
            headers = self._get_auth_headers("GET", "/v1/ping")
            
            async with self.session.get(url, headers=headers) as response:
                if response.status == 200:
                    return True
                elif response.status == 401:
                    raise DataSourceAuthError("米筐认证失败，请检查API密钥")
                else:
                    return False
                    
        except Exception as e:
            self.last_error = e
            return False
    
    def _get_auth_headers(self, method: str, path: str, params: dict = None) -> dict:
        """生成认证头"""
        timestamp = str(int(time.time() * 1000))
        
        # 构建签名字符串
        sign_string = f"{method}\n{path}\n{timestamp}"
        if params:
            query_string = urlencode(sorted(params.items()))
            sign_string += f"\n{query_string}"
        
        # 生成签名
        signature = hmac.new(
            self.secret_key.encode('utf-8'),
            sign_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        return {
            'RQ-ACCESS-KEY': self.api_key,
            'RQ-TIMESTAMP': timestamp,
            'RQ-SIGNATURE': signature,
            'Content-Type': 'application/json'
        }
    
    async def get_stock_list(self) -> List[StockBasicInfo]:
        """获取股票列表"""
        try:
            url = f"{self.base_url}/v1/instruments"
            params = {'type': 'CS'}  # CS表示股票
            headers = self._get_auth_headers("GET", "/v1/instruments", params)
            
            async with self.session.get(url, headers=headers, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    stocks = []
                    
                    for item in data.get('results', []):
                        stock = StockBasicInfo(
                            symbol=item['order_book_id'],
                            name=item['symbol'],
                            industry=item.get('industry_name', ''),
                            market=item['exchange'],
                            market_cap=item.get('market_cap'),
                            listing_date=datetime.strptime(item['listed_date'], '%Y-%m-%d').date() if item.get('listed_date') else None,
                            is_st=item.get('special_type') == 'ST',
                            is_suspended=item.get('status') == 'Suspended'
                        )
                        stocks.append(stock)
                    
                    return stocks
                else:
                    raise DataSourceException(f"获取股票列表失败: {response.status}")
                    
        except Exception as e:
            raise DataSourceException(f"米筐获取股票列表失败: {str(e)}")
    
    async def get_stock_basic_info(self, symbols: List[str]) -> List[StockBasicInfo]:
        """获取股票基础信息"""
        try:
            params = {'order_book_ids': ','.join(symbols)}
            url = f"{self.base_url}/v1/instruments"
            headers = self._get_auth_headers("GET", "/v1/instruments", params)
            
            async with self.session.get(url, headers=headers, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    stocks = []
                    
                    for item in data.get('results', []):
                        stock = StockBasicInfo(
                            symbol=item['order_book_id'],
                            name=item['symbol'],
                            industry=item.get('industry_name', ''),
                            market=item['exchange'],
                            market_cap=item.get('market_cap'),
                            listing_date=datetime.strptime(item['listed_date'], '%Y-%m-%d').date() if item.get('listed_date') else None,
                            is_st=item.get('special_type') == 'ST',
                            is_suspended=item.get('status') == 'Suspended'
                        )
                        stocks.append(stock)
                    
                    return stocks
                else:
                    raise DataSourceException(f"获取股票信息失败: {response.status}")
                    
        except Exception as e:
            raise DataSourceException(f"米筐获取股票信息失败: {str(e)}")
    
    async def get_daily_price(self, 
                            symbols: List[str], 
                            start_date: Union[str, date], 
                            end_date: Union[str, date]) -> pd.DataFrame:
        """获取日线数据"""
        try:
            # 转换日期格式
            if isinstance(start_date, date):
                start_date = start_date.strftime('%Y-%m-%d')
            if isinstance(end_date, date):
                end_date = end_date.strftime('%Y-%m-%d')
            
            params = {
                'order_book_ids': ','.join(symbols),
                'start_date': start_date,
                'end_date': end_date,
                'frequency': '1d',
                'fields': 'open,high,low,close,volume,total_turnover'
            }
            
            url = f"{self.base_url}/v1/price"
            headers = self._get_auth_headers("GET", "/v1/price", params)
            
            async with self.session.get(url, headers=headers, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    # 转换为DataFrame
                    df_list = []
                    for symbol_data in data.get('results', []):
                        symbol = symbol_data['order_book_id']
                        for price_data in symbol_data['data']:
                            df_list.append({
                                'symbol': symbol,
                                'date': price_data['datetime'],
                                'open': float(price_data['open']),
                                'high': float(price_data['high']),
                                'low': float(price_data['low']),
                                'close': float(price_data['close']),
                                'volume': int(price_data['volume']),
                                'amount': float(price_data['total_turnover']),
                                'pre_close': float(price_data.get('prev_close', 0)),
                                'change': float(price_data['close']) - float(price_data.get('prev_close', 0)),
                                'pct_change': (float(price_data['close']) - float(price_data.get('prev_close', 0))) / float(price_data.get('prev_close', 1)) * 100
                            })
                    
                    df = pd.DataFrame(df_list)
                    if not df.empty:
                        df['date'] = pd.to_datetime(df['date'])
                        df.set_index(['symbol', 'date'], inplace=True)
                    
                    return df
                else:
                    raise DataSourceException(f"获取日线数据失败: {response.status}")
                    
        except Exception as e:
            raise DataSourceException(f"米筐获取日线数据失败: {str(e)}")
    
    async def get_minute_price(self, 
                             symbol: str, 
                             date: Union[str, date],
                             frequency: str = "1m") -> pd.DataFrame:
        """获取分钟线数据"""
        try:
            if isinstance(date, date):
                date_str = date.strftime('%Y-%m-%d')
            else:
                date_str = date
            
            params = {
                'order_book_id': symbol,
                'start_date': date_str,
                'end_date': date_str,
                'frequency': frequency,
                'fields': 'open,high,low,close,volume,total_turnover'
            }
            
            url = f"{self.base_url}/v1/price"
            headers = self._get_auth_headers("GET", "/v1/price", params)
            
            async with self.session.get(url, headers=headers, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    # 转换为DataFrame
                    df_list = []
                    for item in data.get('results', []):
                        for price_data in item.get('data', []):
                            df_list.append({
                                'symbol': symbol,
                                'datetime': price_data['datetime'],
                                'open': float(price_data['open']),
                                'high': float(price_data['high']),
                                'low': float(price_data['low']),
                                'close': float(price_data['close']),
                                'volume': int(price_data['volume']),
                                'amount': float(price_data['total_turnover'])
                            })
                    
                    df = pd.DataFrame(df_list)
                    if not df.empty:
                        df['datetime'] = pd.to_datetime(df['datetime'])
                        df.set_index('datetime', inplace=True)
                    
                    return df
                else:
                    raise DataSourceException(f"获取分钟线数据失败: {response.status}")
                    
        except Exception as e:
            raise DataSourceException(f"米筐获取分钟线数据失败: {str(e)}")
    
    async def get_realtime_price(self, symbols: List[str]) -> List[PriceData]:
        """获取实时价格数据"""
        try:
            params = {'order_book_ids': ','.join(symbols)}
            url = f"{self.base_url}/v1/snapshot"
            headers = self._get_auth_headers("GET", "/v1/snapshot", params)
            
            async with self.session.get(url, headers=headers, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    prices = []
                    
                    for item in data.get('results', []):
                        price = PriceData(
                            symbol=item['order_book_id'],
                            timestamp=datetime.fromisoformat(item['datetime']),
                            open=float(item['open']),
                            high=float(item['high']),
                            low=float(item['low']),
                            close=float(item['last']),
                            volume=int(item['volume']),
                            amount=float(item['total_turnover']),
                            pre_close=float(item.get('prev_close', 0)),
                            change=float(item['last']) - float(item.get('prev_close', 0)),
                            pct_change=(float(item['last']) - float(item.get('prev_close', 0))) / float(item.get('prev_close', 1)) * 100
                        )
                        prices.append(price)
                    
                    return prices
                else:
                    raise DataSourceException(f"获取实时价格失败: {response.status}")
                    
        except Exception as e:
            raise DataSourceException(f"米筐获取实时价格失败: {str(e)}")
    
    async def get_financial_data(self, symbols: List[str]) -> pd.DataFrame:
        """获取财务数据"""
        try:
            params = {
                'order_book_ids': ','.join(symbols),
                'fields': 'total_revenue,operating_revenue,total_expense,operating_expense,operating_profit,net_profit,eps,roe,roa,debt_to_equity_ratio,current_ratio'
            }
            
            url = f"{self.base_url}/v1/fundamentals"
            headers = self._get_auth_headers("GET", "/v1/fundamentals", params)
            
            async with self.session.get(url, headers=headers, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    # 转换为DataFrame
                    df_list = []
                    for item in data.get('results', []):
                        df_list.append({
                            'symbol': item['order_book_id'],
                            'total_revenue': float(item.get('total_revenue', 0)),
                            'operating_revenue': float(item.get('operating_revenue', 0)),
                            'total_expense': float(item.get('total_expense', 0)),
                            'operating_expense': float(item.get('operating_expense', 0)),
                            'operating_profit': float(item.get('operating_profit', 0)),
                            'net_profit': float(item.get('net_profit', 0)),
                            'eps': float(item.get('eps', 0)),
                            'roe': float(item.get('roe', 0)),
                            'roa': float(item.get('roa', 0)),
                            'debt_to_equity_ratio': float(item.get('debt_to_equity_ratio', 0)),
                            'current_ratio': float(item.get('current_ratio', 0))
                        })
                    
                    df = pd.DataFrame(df_list)
                    if not df.empty:
                        df.set_index('symbol', inplace=True)
                    
                    return df
                else:
                    raise DataSourceException(f"获取财务数据失败: {response.status}")
                    
        except Exception as e:
            raise DataSourceException(f"米筐获取财务数据失败: {str(e)}")