"""
数据源基础接口和抽象类
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Union, Any
from datetime import datetime, date
import pandas as pd
from enum import Enum
import asyncio
from dataclasses import dataclass


class DataSourceType(str, Enum):
    """数据源类型"""
    ITICK = "iTick"
    JOINQUANT = "JoinQuant"
    RICEQUANT = "RiceQuant"
    SINA = "Sina"
    TENCENT = "Tencent"
    EASTMONEY = "EastMoney"
    XUEQIU = "Xueqiu"


class DataType(str, Enum):
    """数据类型"""
    REALTIME = "realtime"           # 实时行情
    HISTORICAL = "historical"       # 历史数据
    LEVEL2 = "level2"              # Level-2数据
    FUNDAMENTAL = "fundamental"     # 基本面数据
    NEWS = "news"                  # 新闻数据
    SENTIMENT = "sentiment"         # 情绪数据


@dataclass
class DataSourceConfig:
    """数据源配置"""
    name: str
    type: DataSourceType
    priority: int
    features: List[DataType]
    cost: str  # 'free', 'low', 'medium', 'high'
    api_key: Optional[str] = None
    secret_key: Optional[str] = None
    username: Optional[str] = None
    password: Optional[str] = None
    base_url: Optional[str] = None
    rate_limit: int = 100  # 每分钟请求限制
    timeout: int = 30      # 超时时间(秒)
    retry_times: int = 3   # 重试次数
    enabled: bool = True


@dataclass
class StockBasicInfo:
    """股票基础信息"""
    symbol: str
    name: str
    industry: str
    market: str  # 'SH', 'SZ', 'BJ'
    market_cap: Optional[float] = None
    listing_date: Optional[date] = None
    is_st: bool = False
    is_suspended: bool = False


@dataclass
class PriceData:
    """价格数据"""
    symbol: str
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: int
    amount: float
    pre_close: Optional[float] = None
    change: Optional[float] = None
    pct_change: Optional[float] = None


@dataclass
class TickData:
    """Tick数据"""
    symbol: str
    timestamp: datetime
    price: float
    volume: int
    amount: float
    direction: str  # 'B'买入, 'S'卖出, 'N'中性
    bid_price: Optional[float] = None
    ask_price: Optional[float] = None
    bid_volume: Optional[int] = None
    ask_volume: Optional[int] = None


@dataclass
class Level2Data:
    """Level-2数据"""
    symbol: str
    timestamp: datetime
    bid_prices: List[float]  # 买盘价格(5档)
    bid_volumes: List[int]   # 买盘数量(5档)
    ask_prices: List[float]  # 卖盘价格(5档)
    ask_volumes: List[int]   # 卖盘数量(5档)
    total_bid_volume: int    # 总买盘量
    total_ask_volume: int    # 总卖盘量


class DataSourceException(Exception):
    """数据源异常"""
    pass


class DataSourceConnectionError(DataSourceException):
    """数据源连接异常"""
    pass


class DataSourceRateLimitError(DataSourceException):
    """数据源限流异常"""
    pass


class DataSourceAuthError(DataSourceException):
    """数据源认证异常"""
    pass


class BaseDataSource(ABC):
    """数据源基础抽象类"""
    
    def __init__(self, config: DataSourceConfig):
        self.config = config
        self.name = config.name
        self.type = config.type
        self.priority = config.priority
        self.features = config.features
        self.is_connected = False
        self.last_error = None
        self.request_count = 0
        self.last_request_time = datetime.now()
    
    @abstractmethod
    async def connect(self) -> bool:
        """连接数据源"""
        pass
    
    @abstractmethod
    async def disconnect(self) -> bool:
        """断开连接"""
        pass
    
    @abstractmethod
    async def test_connection(self) -> bool:
        """测试连接"""
        pass
    
    @abstractmethod
    async def get_stock_list(self) -> List[StockBasicInfo]:
        """获取股票列表"""
        pass
    
    @abstractmethod
    async def get_stock_basic_info(self, symbols: List[str]) -> List[StockBasicInfo]:
        """获取股票基础信息"""
        pass
    
    @abstractmethod
    async def get_daily_price(self, 
                            symbols: List[str], 
                            start_date: Union[str, date], 
                            end_date: Union[str, date]) -> pd.DataFrame:
        """获取日线数据"""
        pass
    
    @abstractmethod
    async def get_minute_price(self, 
                             symbol: str, 
                             date: Union[str, date],
                             frequency: str = "1min") -> pd.DataFrame:
        """获取分钟线数据"""
        pass
    
    async def get_realtime_price(self, symbols: List[str]) -> List[PriceData]:
        """获取实时价格数据（可选实现）"""
        raise NotImplementedError(f"{self.name} 不支持实时价格数据")
    
    async def get_tick_data(self, 
                          symbol: str, 
                          date: Union[str, date]) -> List[TickData]:
        """获取Tick数据（可选实现）"""
        raise NotImplementedError(f"{self.name} 不支持Tick数据")
    
    async def get_level2_data(self, 
                            symbol: str, 
                            date: Union[str, date]) -> List[Level2Data]:
        """获取Level-2数据（可选实现）"""
        raise NotImplementedError(f"{self.name} 不支持Level-2数据")
    
    async def get_financial_data(self, symbols: List[str]) -> pd.DataFrame:
        """获取财务数据（可选实现）"""
        raise NotImplementedError(f"{self.name} 不支持财务数据")
    
    async def get_news_data(self, 
                          symbols: Optional[List[str]] = None,
                          start_date: Optional[Union[str, date]] = None,
                          end_date: Optional[Union[str, date]] = None) -> pd.DataFrame:
        """获取新闻数据（可选实现）"""
        raise NotImplementedError(f"{self.name} 不支持新闻数据")
    
    def supports_feature(self, feature: DataType) -> bool:
        """检查是否支持某个功能"""
        return feature in self.features
    
    def check_rate_limit(self) -> bool:
        """检查请求频率限制"""
        now = datetime.now()
        if (now - self.last_request_time).seconds >= 60:
            self.request_count = 0
            self.last_request_time = now
        
        return self.request_count < self.config.rate_limit
    
    def increment_request_count(self):
        """增加请求计数"""
        self.request_count += 1
    
    async def _handle_request(self, request_func, *args, **kwargs):
        """处理请求的通用方法，包含重试和错误处理"""
        if not self.check_rate_limit():
            raise DataSourceRateLimitError(f"{self.name} 请求频率超限")
        
        for attempt in range(self.config.retry_times):
            try:
                self.increment_request_count()
                result = await request_func(*args, **kwargs)
                self.last_error = None
                return result
            except Exception as e:
                self.last_error = e
                if attempt == self.config.retry_times - 1:
                    raise DataSourceException(f"{self.name} 请求失败: {str(e)}")
                await asyncio.sleep(2 ** attempt)  # 指数退避
    
    def get_status(self) -> Dict[str, Any]:
        """获取数据源状态"""
        return {
            'name': self.name,
            'type': self.type.value,
            'priority': self.priority,
            'is_connected': self.is_connected,
            'features': [f.value for f in self.features],
            'request_count': self.request_count,
            'last_error': str(self.last_error) if self.last_error else None,
            'enabled': self.config.enabled
        }