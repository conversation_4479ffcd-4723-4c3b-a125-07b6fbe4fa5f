"""
聚宽(JoinQuant)数据源适配器
备用数据源，主要提供基本面数据和历史数据
"""
import asyncio
import aiohttp
import pandas as pd
from typing import List, Union, Optional
from datetime import datetime, date
import json

from .base import (
    BaseDataSource, DataSourceConfig, DataSourceType, DataType,
    StockBasicInfo, PriceData,
    DataSourceException, DataSourceConnectionError, DataSourceAuthError
)


class JoinQuantAdapter(BaseDataSource):
    """聚宽数据源适配器"""
    
    def __init__(self, config: DataSourceConfig):
        super().__init__(config)
        self.base_url = config.base_url or "https://dataapi.joinquant.com"
        self.username = config.username
        self.password = config.password
        self.session = None
        self.token = None
        
        # 聚宽支持的功能
        self.features = [
            DataType.HISTORICAL,
            DataType.FUNDAMENTAL
        ]
    
    async def connect(self) -> bool:
        """连接聚宽API"""
        try:
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=self.config.timeout)
            )
            
            # 获取访问令牌
            if await self._authenticate():
                self.is_connected = True
                return True
            else:
                await self.disconnect()
                return False
                
        except Exception as e:
            self.last_error = e
            raise DataSourceConnectionError(f"聚宽连接失败: {str(e)}")
    
    async def disconnect(self) -> bool:
        """断开连接"""
        try:
            if self.session:
                await self.session.close()
                self.session = None
            self.is_connected = False
            self.token = None
            return True
        except Exception as e:
            self.last_error = e
            return False
    
    async def _authenticate(self) -> bool:
        """认证获取token"""
        try:
            url = f"{self.base_url}/apis/oauth2/token"
            data = {
                'grant_type': 'password',
                'username': self.username,
                'password': self.password
            }
            
            async with self.session.post(url, data=data) as response:
                if response.status == 200:
                    result = await response.json()
                    self.token = result.get('access_token')
                    return self.token is not None
                elif response.status == 401:
                    raise DataSourceAuthError("聚宽认证失败，请检查用户名密码")
                else:
                    return False
                    
        except Exception as e:
            self.last_error = e
            return False
    
    async def test_connection(self) -> bool:
        """测试连接"""
        try:
            if not self.token:
                return await self._authenticate()
            
            url = f"{self.base_url}/apis/v1/user/info"
            headers = {'Authorization': f'Bearer {self.token}'}
            
            async with self.session.get(url, headers=headers) as response:
                return response.status == 200
                
        except Exception as e:
            self.last_error = e
            return False
    
    def _get_headers(self) -> dict:
        """获取请求头"""
        return {
            'Authorization': f'Bearer {self.token}',
            'Content-Type': 'application/json'
        }
    
    async def get_stock_list(self) -> List[StockBasicInfo]:
        """获取股票列表"""
        try:
            url = f"{self.base_url}/apis/v1/stocks/all"
            headers = self._get_headers()
            
            async with self.session.get(url, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    stocks = []
                    
                    for item in data.get('data', []):
                        stock = StockBasicInfo(
                            symbol=item['code'],
                            name=item['display_name'],
                            industry=item.get('industry', ''),
                            market=item['code'][:2].upper(),  # 从代码推断市场
                            listing_date=datetime.strptime(item['start_date'], '%Y-%m-%d').date() if item.get('start_date') else None,
                            is_st='ST' in item['display_name'],
                            is_suspended=item.get('paused', False)
                        )
                        stocks.append(stock)
                    
                    return stocks
                else:
                    raise DataSourceException(f"获取股票列表失败: {response.status}")
                    
        except Exception as e:
            raise DataSourceException(f"聚宽获取股票列表失败: {str(e)}")
    
    async def get_stock_basic_info(self, symbols: List[str]) -> List[StockBasicInfo]:
        """获取股票基础信息"""
        try:
            params = {'codes': ','.join(symbols)}
            url = f"{self.base_url}/apis/v1/stocks/info"
            headers = self._get_headers()
            
            async with self.session.get(url, headers=headers, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    stocks = []
                    
                    for item in data.get('data', []):
                        stock = StockBasicInfo(
                            symbol=item['code'],
                            name=item['display_name'],
                            industry=item.get('industry', ''),
                            market=item['code'][:2].upper(),
                            listing_date=datetime.strptime(item['start_date'], '%Y-%m-%d').date() if item.get('start_date') else None,
                            is_st='ST' in item['display_name'],
                            is_suspended=item.get('paused', False)
                        )
                        stocks.append(stock)
                    
                    return stocks
                else:
                    raise DataSourceException(f"获取股票信息失败: {response.status}")
                    
        except Exception as e:
            raise DataSourceException(f"聚宽获取股票信息失败: {str(e)}")
    
    async def get_daily_price(self, 
                            symbols: List[str], 
                            start_date: Union[str, date], 
                            end_date: Union[str, date]) -> pd.DataFrame:
        """获取日线数据"""
        try:
            # 转换日期格式
            if isinstance(start_date, date):
                start_date = start_date.strftime('%Y-%m-%d')
            if isinstance(end_date, date):
                end_date = end_date.strftime('%Y-%m-%d')
            
            params = {
                'codes': ','.join(symbols),
                'start_date': start_date,
                'end_date': end_date,
                'fields': 'open,high,low,close,volume,money,pre_close,paused'
            }
            
            url = f"{self.base_url}/apis/v1/get_price"
            headers = self._get_headers()
            
            async with self.session.get(url, headers=headers, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    # 转换为DataFrame
                    df_list = []
                    for symbol in symbols:
                        symbol_data = data.get('data', {}).get(symbol, [])
                        for item in symbol_data:
                            df_list.append({
                                'symbol': symbol,
                                'date': item['time'],
                                'open': float(item['open']),
                                'high': float(item['high']),
                                'low': float(item['low']),
                                'close': float(item['close']),
                                'volume': int(item['volume']),
                                'amount': float(item['money']),
                                'pre_close': float(item.get('pre_close', 0)),
                                'change': float(item['close']) - float(item.get('pre_close', 0)),
                                'pct_change': (float(item['close']) - float(item.get('pre_close', 0))) / float(item.get('pre_close', 1)) * 100
                            })
                    
                    df = pd.DataFrame(df_list)
                    if not df.empty:
                        df['date'] = pd.to_datetime(df['date'])
                        df.set_index(['symbol', 'date'], inplace=True)
                    
                    return df
                else:
                    raise DataSourceException(f"获取日线数据失败: {response.status}")
                    
        except Exception as e:
            raise DataSourceException(f"聚宽获取日线数据失败: {str(e)}")
    
    async def get_minute_price(self, 
                             symbol: str, 
                             date: Union[str, date],
                             frequency: str = "1m") -> pd.DataFrame:
        """获取分钟线数据"""
        try:
            if isinstance(date, date):
                date_str = date.strftime('%Y-%m-%d')
            else:
                date_str = date
            
            params = {
                'code': symbol,
                'date': date_str,
                'frequency': frequency,
                'fields': 'open,high,low,close,volume,money'
            }
            
            url = f"{self.base_url}/apis/v1/get_bars"
            headers = self._get_headers()
            
            async with self.session.get(url, headers=headers, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    # 转换为DataFrame
                    df_list = []
                    for item in data.get('data', []):
                        df_list.append({
                            'symbol': symbol,
                            'datetime': item['datetime'],
                            'open': float(item['open']),
                            'high': float(item['high']),
                            'low': float(item['low']),
                            'close': float(item['close']),
                            'volume': int(item['volume']),
                            'amount': float(item['money'])
                        })
                    
                    df = pd.DataFrame(df_list)
                    if not df.empty:
                        df['datetime'] = pd.to_datetime(df['datetime'])
                        df.set_index('datetime', inplace=True)
                    
                    return df
                else:
                    raise DataSourceException(f"获取分钟线数据失败: {response.status}")
                    
        except Exception as e:
            raise DataSourceException(f"聚宽获取分钟线数据失败: {str(e)}")
    
    async def get_financial_data(self, symbols: List[str]) -> pd.DataFrame:
        """获取财务数据"""
        try:
            params = {
                'codes': ','.join(symbols),
                'fields': 'total_operating_revenue,operating_revenue,total_operating_cost,operating_cost,operating_profit,total_profit,net_profit,eps,roe,roa,debt_to_assets,current_ratio'
            }
            
            url = f"{self.base_url}/apis/v1/get_fundamentals"
            headers = self._get_headers()
            
            async with self.session.get(url, headers=headers, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    # 转换为DataFrame
                    df_list = []
                    for symbol in symbols:
                        symbol_data = data.get('data', {}).get(symbol, {})
                        if symbol_data:
                            df_list.append({
                                'symbol': symbol,
                                'total_operating_revenue': float(symbol_data.get('total_operating_revenue', 0)),
                                'operating_revenue': float(symbol_data.get('operating_revenue', 0)),
                                'total_operating_cost': float(symbol_data.get('total_operating_cost', 0)),
                                'operating_cost': float(symbol_data.get('operating_cost', 0)),
                                'operating_profit': float(symbol_data.get('operating_profit', 0)),
                                'total_profit': float(symbol_data.get('total_profit', 0)),
                                'net_profit': float(symbol_data.get('net_profit', 0)),
                                'eps': float(symbol_data.get('eps', 0)),
                                'roe': float(symbol_data.get('roe', 0)),
                                'roa': float(symbol_data.get('roa', 0)),
                                'debt_to_assets': float(symbol_data.get('debt_to_assets', 0)),
                                'current_ratio': float(symbol_data.get('current_ratio', 0))
                            })
                    
                    df = pd.DataFrame(df_list)
                    if not df.empty:
                        df.set_index('symbol', inplace=True)
                    
                    return df
                else:
                    raise DataSourceException(f"获取财务数据失败: {response.status}")
                    
        except Exception as e:
            raise DataSourceException(f"聚宽获取财务数据失败: {str(e)}")
    
    async def get_news_data(self, 
                          symbols: Optional[List[str]] = None,
                          start_date: Optional[Union[str, date]] = None,
                          end_date: Optional[Union[str, date]] = None) -> pd.DataFrame:
        """获取新闻数据"""
        try:
            params = {}
            if symbols:
                params['codes'] = ','.join(symbols)
            if start_date:
                if isinstance(start_date, date):
                    params['start_date'] = start_date.strftime('%Y-%m-%d')
                else:
                    params['start_date'] = start_date
            if end_date:
                if isinstance(end_date, date):
                    params['end_date'] = end_date.strftime('%Y-%m-%d')
                else:
                    params['end_date'] = end_date
            
            url = f"{self.base_url}/apis/v1/get_news"
            headers = self._get_headers()
            
            async with self.session.get(url, headers=headers, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    # 转换为DataFrame
                    df_list = []
                    for item in data.get('data', []):
                        df_list.append({
                            'symbol': item.get('code', ''),
                            'title': item['title'],
                            'content': item.get('content', ''),
                            'publish_time': item['pub_date'],
                            'source': item.get('source', ''),
                            'url': item.get('url', '')
                        })
                    
                    df = pd.DataFrame(df_list)
                    if not df.empty:
                        df['publish_time'] = pd.to_datetime(df['publish_time'])
                    
                    return df
                else:
                    raise DataSourceException(f"获取新闻数据失败: {response.status}")
                    
        except Exception as e:
            raise DataSourceException(f"聚宽获取新闻数据失败: {str(e)}")