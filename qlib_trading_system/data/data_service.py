"""
数据服务
整合数据采集、处理、存储和备份的统一服务接口
"""
import asyncio
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, date, timedelta
import pandas as pd

from .collectors import (
    DataSourceManager, DataSourceConfigFactory, CacheManager, FailoverStrategy
)
from .processors import RealtimeDataService, ConnectionConfig
from .storage import (
    ClickHouseStorage, HistoricalDataManager, BackupManager, BackupConfig
)
from .collectors.base import StockBasicInfo, PriceData, TickData, Level2Data
from ..utils.config.integration_adapter import get_module_config


class DataService:
    """数据服务统一接口"""
    
    def __init__(self,
                 clickhouse_config: Dict[str, Any] = None,
                 redis_config: Dict[str, Any] = None,
                 backup_config: Dict[str, Any] = None,
                 websocket_configs: Dict[str, ConnectionConfig] = None):

        # 从新配置系统加载数据源配置
        self._load_data_source_config()

        # 合并配置参数
        clickhouse_config = clickhouse_config or self._get_clickhouse_config()
        redis_config = redis_config or self._get_redis_config()
        backup_config = backup_config or self._get_backup_config()

        # 初始化存储
        ch_config = clickhouse_config or {}
        self.storage = ClickHouseStorage(
            host=ch_config.get('host', 'localhost'),
            port=ch_config.get('port', 9000),
            database=ch_config.get('database', 'qlib_trading'),
            username=ch_config.get('username', 'default'),
            password=ch_config.get('password', '')
        )
        
        # 初始化缓存
        redis_config = redis_config or {}
        self.cache_manager = CacheManager(
            redis_url=redis_config.get('url', 'redis://localhost:6379'),
            local_cache_dir=redis_config.get('local_dir', 'data/cache'),
            default_ttl=redis_config.get('ttl', 3600)
        )
        
        # 初始化数据源管理器
        data_source_configs = DataSourceConfigFactory.create_default_config()
        self.data_source_manager = DataSourceManager(
            data_source_configs, FailoverStrategy.PRIORITY
        )
        
        # 初始化历史数据管理器
        self.historical_manager = HistoricalDataManager(
            self.storage, self.data_source_manager
        )
        
        # 初始化备份管理器
        backup_config = backup_config or {}
        backup_cfg = BackupConfig(
            backup_dir=backup_config.get('backup_dir', 'data/backups'),
            retention_days=backup_config.get('retention_days', 30),
            compression=backup_config.get('compression', True),
            incremental=backup_config.get('incremental', True)
        )
        self.backup_manager = BackupManager(self.storage, backup_cfg)
        
        # 初始化实时数据服务
        self.realtime_service = RealtimeDataService(
            websocket_configs=websocket_configs or {}
        )
        
        # 服务状态
        self.is_initialized = False
        self.is_running = False
        
        self.logger = logging.getLogger(__name__)

    def _load_data_source_config(self):
        """从新配置系统加载数据源配置"""
        try:
            # 获取数据源配置
            self.data_source_config = get_module_config('data_source') or {}
            self.logger.info("已从新配置系统加载数据源配置")
        except Exception as e:
            self.logger.error(f"加载数据源配置失败: {e}")
            self.data_source_config = {}

    def _get_clickhouse_config(self) -> Dict[str, Any]:
        """获取ClickHouse配置"""
        return self.data_source_config.get('clickhouse', {
            'host': 'localhost',
            'port': 9000,
            'database': 'trading_data',
            'username': 'default',
            'password': ''
        })

    def _get_redis_config(self) -> Dict[str, Any]:
        """获取Redis配置"""
        return self.data_source_config.get('redis', {
            'host': 'localhost',
            'port': 6379,
            'db': 0,
            'password': None
        })

    def _get_backup_config(self) -> Dict[str, Any]:
        """获取备份配置"""
        return self.data_source_config.get('backup', {
            'backup_dir': 'data/backups',
            'retention_days': 30,
            'compression': True,
            'incremental': True
        })

    async def initialize(self) -> bool:
        """初始化数据服务"""
        try:
            self.logger.info("开始初始化数据服务...")
            
            # 1. 连接ClickHouse
            if not await self.storage.connect():
                self.logger.error("ClickHouse连接失败")
                return False
            
            # 2. 创建数据库和表
            await self.storage.create_database()
            await self.storage.create_tables()
            
            # 3. 连接缓存
            await self.cache_manager.connect()
            
            # 4. 连接数据源
            await self.data_source_manager.connect_all()
            
            # 5. 启动历史数据管理器
            await self.historical_manager.start()
            
            # 6. 启动实时数据服务
            await self.realtime_service.start()
            
            self.is_initialized = True
            self.logger.info("数据服务初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"数据服务初始化失败: {e}")
            return False
    
    async def start(self) -> bool:
        """启动数据服务"""
        if not self.is_initialized:
            if not await self.initialize():
                return False
        
        try:
            # 启动定时备份
            await self.backup_manager.start_scheduled_backup()
            
            # 启动缓存清理任务
            asyncio.create_task(self.cache_manager.start_cleanup_task())
            
            self.is_running = True
            self.logger.info("数据服务已启动")
            return True
            
        except Exception as e:
            self.logger.error(f"启动数据服务失败: {e}")
            return False
    
    async def stop(self):
        """停止数据服务"""
        try:
            self.is_running = False
            
            # 停止实时数据服务
            await self.realtime_service.stop()
            
            # 停止历史数据管理器
            await self.historical_manager.stop()
            
            # 停止定时备份
            await self.backup_manager.stop_scheduled_backup()
            
            # 断开数据源连接
            await self.data_source_manager.disconnect_all()
            
            # 断开缓存连接
            await self.cache_manager.disconnect()
            
            # 断开存储连接
            await self.storage.disconnect()
            
            self.logger.info("数据服务已停止")
            
        except Exception as e:
            self.logger.error(f"停止数据服务失败: {e}")
    
    # 股票基础信息接口
    async def get_stock_list(self, use_cache: bool = True) -> List[StockBasicInfo]:
        """获取股票列表"""
        try:
            # 先尝试从缓存获取
            if use_cache:
                cached_data = await self.cache_manager.get_stock_list_cached()
                if cached_data:
                    return cached_data
            
            # 从数据源获取
            stock_list = await self.data_source_manager.get_stock_list()
            
            # 存储到数据库
            if stock_list:
                await self.storage.insert_stock_basic_info(stock_list)
                
                # 缓存数据
                if use_cache:
                    await self.cache_manager.set_stock_list_cached(stock_list)
            
            return stock_list
            
        except Exception as e:
            self.logger.error(f"获取股票列表失败: {e}")
            return []
    
    async def get_stock_basic_info(self, 
                                 symbols: List[str],
                                 from_db: bool = False) -> List[StockBasicInfo]:
        """获取股票基础信息"""
        try:
            if from_db:
                # 从数据库获取
                return await self.storage.query_stock_basic_info(symbols)
            else:
                # 从数据源获取
                return await self.data_source_manager.get_stock_basic_info(symbols)
                
        except Exception as e:
            self.logger.error(f"获取股票基础信息失败: {e}")
            return []
    
    # 历史数据接口
    async def get_daily_price(self,
                            symbols: List[str],
                            start_date: Union[str, date],
                            end_date: Union[str, date],
                            from_db: bool = False,
                            use_cache: bool = True) -> pd.DataFrame:
        """获取日线数据"""
        try:
            # 先尝试从缓存获取
            if use_cache:
                cached_data = await self.cache_manager.get_daily_price_cached(
                    symbols, start_date, end_date
                )
                if cached_data is not None and not cached_data.empty:
                    return cached_data
            
            if from_db:
                # 从数据库获取
                df = await self.storage.query_daily_price(symbols, start_date, end_date)
            else:
                # 从数据源获取
                df = await self.data_source_manager.get_daily_price(symbols, start_date, end_date)
                
                # 存储到数据库
                if not df.empty:
                    await self.storage.insert_daily_price(df)
            
            # 缓存数据
            if use_cache and not df.empty:
                await self.cache_manager.set_daily_price_cached(
                    df, symbols, start_date, end_date
                )
            
            return df
            
        except Exception as e:
            self.logger.error(f"获取日线数据失败: {e}")
            return pd.DataFrame()
    
    async def get_minute_price(self,
                             symbol: str,
                             date: Union[str, date],
                             frequency: str = "1min",
                             from_db: bool = False) -> pd.DataFrame:
        """获取分钟线数据"""
        try:
            if from_db:
                # 从数据库获取
                start_time = datetime.combine(date, datetime.min.time()) if isinstance(date, date) else datetime.strptime(date, '%Y-%m-%d')
                end_time = start_time + timedelta(days=1)
                return await self.storage.query_minute_price(symbol, start_time, end_time)
            else:
                # 从数据源获取
                df = await self.data_source_manager.get_minute_price(symbol, date, frequency)
                
                # 存储到数据库
                if not df.empty:
                    await self.storage.insert_minute_price(df)
                
                return df
                
        except Exception as e:
            self.logger.error(f"获取分钟线数据失败: {e}")
            return pd.DataFrame()
    
    # 实时数据接口
    async def subscribe_realtime_data(self, symbols: List[str]) -> bool:
        """订阅实时数据"""
        return await self.realtime_service.subscribe_symbols(symbols)
    
    async def unsubscribe_realtime_data(self, symbols: List[str]) -> bool:
        """取消订阅实时数据"""
        return await self.realtime_service.unsubscribe_symbols(symbols)
    
    def get_latest_tick(self, symbol: str) -> Optional[TickData]:
        """获取最新tick数据"""
        return self.realtime_service.get_latest_tick(symbol)
    
    def get_latest_level2(self, symbol: str) -> Optional[Level2Data]:
        """获取最新Level-2数据"""
        return self.realtime_service.get_latest_level2(symbol)
    
    def get_latest_price(self, symbol: str) -> Optional[PriceData]:
        """获取最新价格数据"""
        return self.realtime_service.get_latest_price(symbol)
    
    # 历史数据管理接口
    async def import_historical_data(self,
                                   symbol: str,
                                   data_type: str,
                                   start_date: Union[str, date],
                                   end_date: Union[str, date]) -> str:
        """导入历史数据"""
        return await self.historical_manager.import_historical_data(
            symbol, data_type, start_date, end_date
        )
    
    async def batch_import_historical_data(self,
                                         symbols: List[str],
                                         data_type: str,
                                         start_date: Union[str, date],
                                         end_date: Union[str, date]) -> List[str]:
        """批量导入历史数据"""
        return await self.historical_manager.batch_import_historical_data(
            symbols, data_type, start_date, end_date
        )
    
    def get_import_task_status(self, task_id: str):
        """获取导入任务状态"""
        return self.historical_manager.get_task_status(task_id)
    
    async def check_data_integrity(self,
                                 symbol: str,
                                 data_type: str,
                                 start_date: Union[str, date],
                                 end_date: Union[str, date]):
        """检查数据完整性"""
        return await self.historical_manager.check_data_integrity(
            symbol, data_type, start_date, end_date
        )
    
    # 备份管理接口
    async def backup_table(self, table_name: str, backup_type: str = 'full') -> Optional[str]:
        """备份表"""
        return await self.backup_manager.backup_table(table_name, backup_type)
    
    async def backup_all_tables(self) -> List[str]:
        """备份所有表"""
        return await self.backup_manager.backup_all_tables()
    
    async def restore_table(self, backup_id: str) -> bool:
        """恢复表"""
        return await self.backup_manager.restore_table(backup_id)
    
    def get_backup_records(self, table_name: Optional[str] = None, limit: int = 100):
        """获取备份记录"""
        return self.backup_manager.get_backup_records(table_name, limit=limit)
    
    # 回调注册接口
    def add_tick_callback(self, callback):
        """添加tick数据回调"""
        self.realtime_service.add_tick_callback(callback)
    
    def add_level2_callback(self, callback):
        """添加Level-2数据回调"""
        self.realtime_service.add_level2_callback(callback)
    
    def add_price_callback(self, callback):
        """添加价格数据回调"""
        self.realtime_service.add_price_callback(callback)
    
    def add_order_book_callback(self, callback):
        """添加订单簿回调"""
        self.realtime_service.add_order_book_callback(callback)
    
    def add_large_order_callback(self, callback):
        """添加大单回调"""
        self.realtime_service.add_large_order_callback(callback)
    
    # 状态和统计接口
    def get_service_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        return {
            'is_initialized': self.is_initialized,
            'is_running': self.is_running,
            'storage_connected': self.storage.is_connected,
            'data_sources': self.data_source_manager.get_status_summary(),
            'realtime_service': self.realtime_service.get_service_status(),
            'import_statistics': asyncio.create_task(self.historical_manager.get_import_statistics()),
            'backup_statistics': self.backup_manager.get_backup_statistics(),
            'cache_statistics': asyncio.create_task(self.cache_manager.get_cache_stats())
        }
    
    def get_data_quality_report(self) -> Dict[str, Any]:
        """获取数据质量报告"""
        return self.realtime_service.get_data_quality_report()
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        health_status = {
            'overall_status': 'healthy',
            'components': {}
        }
        
        try:
            # 检查存储连接
            health_status['components']['storage'] = {
                'status': 'healthy' if self.storage.is_connected else 'unhealthy',
                'connected': self.storage.is_connected
            }
            
            # 检查数据源
            ds_health = await self.data_source_manager.health_check()
            available_sources = sum(1 for status in ds_health.values() if status.is_available)
            total_sources = len(ds_health)
            
            health_status['components']['data_sources'] = {
                'status': 'healthy' if available_sources > 0 else 'unhealthy',
                'available_sources': available_sources,
                'total_sources': total_sources,
                'availability_rate': available_sources / total_sources if total_sources > 0 else 0
            }
            
            # 检查实时服务
            realtime_status = self.realtime_service.get_service_status()
            active_connections = realtime_status['connection_summary']['active_connections']
            
            health_status['components']['realtime_service'] = {
                'status': 'healthy' if active_connections > 0 else 'degraded',
                'active_connections': active_connections
            }
            
            # 检查缓存
            cache_stats = await self.cache_manager.get_cache_stats()
            health_status['components']['cache'] = {
                'status': 'healthy' if cache_stats['redis_connected'] else 'degraded',
                'redis_connected': cache_stats['redis_connected']
            }
            
            # 确定整体状态
            component_statuses = [comp['status'] for comp in health_status['components'].values()]
            if all(status == 'healthy' for status in component_statuses):
                health_status['overall_status'] = 'healthy'
            elif any(status == 'unhealthy' for status in component_statuses):
                health_status['overall_status'] = 'unhealthy'
            else:
                health_status['overall_status'] = 'degraded'
            
            return health_status
            
        except Exception as e:
            self.logger.error(f"健康检查失败: {e}")
            return {
                'overall_status': 'unhealthy',
                'error': str(e)
            }