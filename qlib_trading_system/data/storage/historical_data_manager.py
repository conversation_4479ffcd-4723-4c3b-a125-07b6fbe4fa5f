"""
历史数据管理器
处理历史数据批量导入、完整性检查和修复
"""
import asyncio
import logging
from typing import Dict, List, Optional, Any, Union, Tuple
from datetime import datetime, date, timedelta
import pandas as pd
import numpy as np
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor
import os
import json

from .clickhouse_storage import ClickHouseStorage
from ..collectors.data_source_manager import DataSourceManager
from ..collectors.base import StockBasicInfo, PriceData, TickData, Level2Data


@dataclass
class ImportTask:
    """导入任务"""
    task_id: str
    symbol: str
    data_type: str  # 'daily', 'minute', 'tick', 'level2'
    start_date: date
    end_date: date
    status: str = 'pending'  # 'pending', 'running', 'completed', 'failed'
    progress: float = 0.0
    error_message: Optional[str] = None
    created_time: datetime = None
    started_time: Optional[datetime] = None
    completed_time: Optional[datetime] = None


@dataclass
class DataIntegrityReport:
    """数据完整性报告"""
    symbol: str
    data_type: str
    total_expected_records: int
    actual_records: int
    missing_records: int
    completeness_rate: float
    missing_dates: List[str]
    data_quality_issues: List[str]
    last_check_time: datetime


class HistoricalDataManager:
    """历史数据管理器"""
    
    def __init__(self, 
                 storage: ClickHouseStorage,
                 data_source_manager: DataSourceManager,
                 max_concurrent_tasks: int = 5,
                 batch_size: int = 1000):
        
        self.storage = storage
        self.data_source_manager = data_source_manager
        self.max_concurrent_tasks = max_concurrent_tasks
        self.batch_size = batch_size
        
        # 任务管理
        self.import_tasks: Dict[str, ImportTask] = {}
        self.task_queue = asyncio.Queue()
        self.running_tasks: Dict[str, asyncio.Task] = {}
        
        # 线程池用于CPU密集型操作
        self.thread_pool = ThreadPoolExecutor(max_workers=4)
        
        # 控制标志
        self.is_running = False
        
        self.logger = logging.getLogger(__name__)
    
    async def start(self):
        """启动历史数据管理器"""
        self.is_running = True
        
        # 启动任务处理器
        for i in range(self.max_concurrent_tasks):
            asyncio.create_task(self._task_processor(f"worker-{i}"))
        
        self.logger.info("历史数据管理器已启动")
    
    async def stop(self):
        """停止历史数据管理器"""
        self.is_running = False
        
        # 等待所有运行中的任务完成
        if self.running_tasks:
            await asyncio.gather(*self.running_tasks.values(), return_exceptions=True)
        
        # 关闭线程池
        self.thread_pool.shutdown(wait=True)
        
        self.logger.info("历史数据管理器已停止")
    
    async def _task_processor(self, worker_name: str):
        """任务处理器"""
        while self.is_running:
            try:
                # 从队列获取任务
                task = await asyncio.wait_for(self.task_queue.get(), timeout=1.0)
                
                self.logger.info(f"{worker_name} 开始处理任务: {task.task_id}")
                
                # 更新任务状态
                task.status = 'running'
                task.started_time = datetime.now()
                
                # 执行任务
                try:
                    await self._execute_import_task(task)
                    task.status = 'completed'
                    task.progress = 100.0
                    task.completed_time = datetime.now()
                    
                    self.logger.info(f"任务 {task.task_id} 完成")
                    
                except Exception as e:
                    task.status = 'failed'
                    task.error_message = str(e)
                    task.completed_time = datetime.now()
                    
                    self.logger.error(f"任务 {task.task_id} 失败: {e}")
                
                # 从运行任务中移除
                if task.task_id in self.running_tasks:
                    del self.running_tasks[task.task_id]
                
                # 标记任务完成
                self.task_queue.task_done()
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self.logger.error(f"{worker_name} 处理任务异常: {e}")
    
    async def _execute_import_task(self, task: ImportTask):
        """执行导入任务"""
        if task.data_type == 'daily':
            await self._import_daily_data(task)
        elif task.data_type == 'minute':
            await self._import_minute_data(task)
        elif task.data_type == 'tick':
            await self._import_tick_data(task)
        elif task.data_type == 'level2':
            await self._import_level2_data(task)
        else:
            raise ValueError(f"不支持的数据类型: {task.data_type}")
    
    async def _import_daily_data(self, task: ImportTask):
        """导入日线数据"""
        try:
            # 按月分批导入
            current_date = task.start_date
            total_days = (task.end_date - task.start_date).days + 1
            processed_days = 0
            
            while current_date <= task.end_date:
                # 计算月末日期
                if current_date.month == 12:
                    month_end = date(current_date.year + 1, 1, 1) - timedelta(days=1)
                else:
                    month_end = date(current_date.year, current_date.month + 1, 1) - timedelta(days=1)
                
                batch_end = min(month_end, task.end_date)
                
                # 获取数据
                df = await self.data_source_manager.get_daily_price(
                    [task.symbol], current_date, batch_end
                )
                
                if not df.empty:
                    # 插入数据库
                    await self.storage.insert_daily_price(df)
                
                # 更新进度
                batch_days = (batch_end - current_date).days + 1
                processed_days += batch_days
                task.progress = (processed_days / total_days) * 100
                
                self.logger.info(f"任务 {task.task_id} 进度: {task.progress:.1f}%")
                
                # 移动到下个月
                current_date = batch_end + timedelta(days=1)
                
                # 短暂休息避免过载
                await asyncio.sleep(0.1)
                
        except Exception as e:
            self.logger.error(f"导入日线数据失败: {e}")
            raise
    
    async def _import_minute_data(self, task: ImportTask):
        """导入分钟数据"""
        try:
            # 按天分批导入
            current_date = task.start_date
            total_days = (task.end_date - task.start_date).days + 1
            processed_days = 0
            
            while current_date <= task.end_date:
                # 获取单日分钟数据
                df = await self.data_source_manager.get_minute_price(
                    task.symbol, current_date
                )
                
                if not df.empty:
                    # 插入数据库
                    await self.storage.insert_minute_price(df)
                
                # 更新进度
                processed_days += 1
                task.progress = (processed_days / total_days) * 100
                
                if processed_days % 10 == 0:  # 每10天记录一次进度
                    self.logger.info(f"任务 {task.task_id} 进度: {task.progress:.1f}%")
                
                # 移动到下一天
                current_date += timedelta(days=1)
                
                # 短暂休息
                await asyncio.sleep(0.1)
                
        except Exception as e:
            self.logger.error(f"导入分钟数据失败: {e}")
            raise
    
    async def _import_tick_data(self, task: ImportTask):
        """导入Tick数据"""
        try:
            # 按天分批导入
            current_date = task.start_date
            total_days = (task.end_date - task.start_date).days + 1
            processed_days = 0
            
            while current_date <= task.end_date:
                # 获取单日Tick数据
                ticks = await self.data_source_manager.get_tick_data(
                    task.symbol, current_date
                )
                
                if ticks:
                    # 分批插入避免内存过载
                    for i in range(0, len(ticks), self.batch_size):
                        batch = ticks[i:i + self.batch_size]
                        await self.storage.insert_tick_data(batch)
                
                # 更新进度
                processed_days += 1
                task.progress = (processed_days / total_days) * 100
                
                if processed_days % 5 == 0:  # 每5天记录一次进度
                    self.logger.info(f"任务 {task.task_id} 进度: {task.progress:.1f}%")
                
                # 移动到下一天
                current_date += timedelta(days=1)
                
                # 短暂休息
                await asyncio.sleep(0.2)
                
        except Exception as e:
            self.logger.error(f"导入Tick数据失败: {e}")
            raise
    
    async def _import_level2_data(self, task: ImportTask):
        """导入Level-2数据"""
        try:
            # 按天分批导入
            current_date = task.start_date
            total_days = (task.end_date - task.start_date).days + 1
            processed_days = 0
            
            while current_date <= task.end_date:
                # 获取单日Level-2数据
                level2_data = await self.data_source_manager.get_level2_data(
                    task.symbol, current_date
                )
                
                if level2_data:
                    # 分批插入
                    for i in range(0, len(level2_data), self.batch_size):
                        batch = level2_data[i:i + self.batch_size]
                        await self.storage.insert_level2_data(batch)
                
                # 更新进度
                processed_days += 1
                task.progress = (processed_days / total_days) * 100
                
                if processed_days % 5 == 0:
                    self.logger.info(f"任务 {task.task_id} 进度: {task.progress:.1f}%")
                
                # 移动到下一天
                current_date += timedelta(days=1)
                
                # 短暂休息
                await asyncio.sleep(0.2)
                
        except Exception as e:
            self.logger.error(f"导入Level-2数据失败: {e}")
            raise
    
    # 公共接口方法
    async def import_historical_data(self,
                                   symbol: str,
                                   data_type: str,
                                   start_date: Union[str, date],
                                   end_date: Union[str, date]) -> str:
        """导入历史数据"""
        # 转换日期
        if isinstance(start_date, str):
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        if isinstance(end_date, str):
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
        
        # 创建任务
        task_id = f"{symbol}_{data_type}_{start_date}_{end_date}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        task = ImportTask(
            task_id=task_id,
            symbol=symbol,
            data_type=data_type,
            start_date=start_date,
            end_date=end_date,
            created_time=datetime.now()
        )
        
        # 保存任务
        self.import_tasks[task_id] = task
        
        # 添加到队列
        await self.task_queue.put(task)
        
        self.logger.info(f"创建导入任务: {task_id}")
        return task_id
    
    async def batch_import_historical_data(self,
                                         symbols: List[str],
                                         data_type: str,
                                         start_date: Union[str, date],
                                         end_date: Union[str, date]) -> List[str]:
        """批量导入历史数据"""
        task_ids = []
        
        for symbol in symbols:
            task_id = await self.import_historical_data(
                symbol, data_type, start_date, end_date
            )
            task_ids.append(task_id)
        
        self.logger.info(f"创建批量导入任务: {len(task_ids)} 个")
        return task_ids
    
    def get_task_status(self, task_id: str) -> Optional[ImportTask]:
        """获取任务状态"""
        return self.import_tasks.get(task_id)
    
    def get_all_tasks(self) -> List[ImportTask]:
        """获取所有任务"""
        return list(self.import_tasks.values())
    
    def get_running_tasks(self) -> List[ImportTask]:
        """获取运行中的任务"""
        return [task for task in self.import_tasks.values() if task.status == 'running']
    
    def get_pending_tasks(self) -> List[ImportTask]:
        """获取待处理的任务"""
        return [task for task in self.import_tasks.values() if task.status == 'pending']
    
    # 数据完整性检查
    async def check_data_integrity(self,
                                 symbol: str,
                                 data_type: str,
                                 start_date: Union[str, date],
                                 end_date: Union[str, date]) -> DataIntegrityReport:
        """检查数据完整性"""
        try:
            # 转换日期
            if isinstance(start_date, str):
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            if isinstance(end_date, str):
                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
            
            # 确定表名
            table_name = self._get_table_name(data_type)
            
            # 获取数据完整性统计
            completeness_stats = await self.storage.check_data_completeness(
                table_name, symbol, start_date, end_date
            )
            
            # 获取缺失日期
            missing_dates = await self.storage.get_missing_dates(
                table_name, symbol, start_date, end_date
            )
            
            # 计算预期记录数
            total_days = (end_date - start_date).days + 1
            if data_type == 'daily':
                # 假设交易日约为总天数的70%
                expected_records = int(total_days * 0.7)
            elif data_type == 'minute':
                # 假设每个交易日240分钟
                expected_records = int(total_days * 0.7 * 240)
            else:
                # Tick和Level-2数据难以准确估算
                expected_records = completeness_stats.get('total_records', 0)
            
            actual_records = completeness_stats.get('total_records', 0)
            missing_records = max(0, expected_records - actual_records)
            completeness_rate = actual_records / expected_records if expected_records > 0 else 0
            
            # 检查数据质量问题
            quality_issues = await self._check_data_quality_issues(
                table_name, symbol, start_date, end_date
            )
            
            report = DataIntegrityReport(
                symbol=symbol,
                data_type=data_type,
                total_expected_records=expected_records,
                actual_records=actual_records,
                missing_records=missing_records,
                completeness_rate=completeness_rate,
                missing_dates=missing_dates,
                data_quality_issues=quality_issues,
                last_check_time=datetime.now()
            )
            
            return report
            
        except Exception as e:
            self.logger.error(f"检查数据完整性失败: {e}")
            raise
    
    async def _check_data_quality_issues(self,
                                       table_name: str,
                                       symbol: str,
                                       start_date: date,
                                       end_date: date) -> List[str]:
        """检查数据质量问题"""
        issues = []
        
        try:
            # 根据表类型检查不同的质量问题
            if table_name == 'daily_price':
                # 检查价格异常
                sql = f"""
                SELECT COUNT(*) as count
                FROM daily_price
                WHERE symbol = '{symbol}'
                  AND date >= '{start_date}'
                  AND date <= '{end_date}'
                  AND (high < low OR open <= 0 OR close <= 0 OR volume < 0)
                """
                
                result = await self.storage.client.fetch(sql)
                if result and result[0]['count'] > 0:
                    issues.append(f"发现 {result[0]['count']} 条价格异常记录")
                
                # 检查涨跌幅异常
                sql = f"""
                SELECT COUNT(*) as count
                FROM daily_price
                WHERE symbol = '{symbol}'
                  AND date >= '{start_date}'
                  AND date <= '{end_date}'
                  AND abs(pct_change) > 20
                """
                
                result = await self.storage.client.fetch(sql)
                if result and result[0]['count'] > 0:
                    issues.append(f"发现 {result[0]['count']} 条涨跌幅异常记录(>20%)")
            
            elif table_name == 'tick_data':
                # 检查Tick数据异常
                sql = f"""
                SELECT COUNT(*) as count
                FROM tick_data
                WHERE symbol = '{symbol}'
                  AND toDate(timestamp) >= '{start_date}'
                  AND toDate(timestamp) <= '{end_date}'
                  AND (price <= 0 OR volume < 0)
                """
                
                result = await self.storage.client.fetch(sql)
                if result and result[0]['count'] > 0:
                    issues.append(f"发现 {result[0]['count']} 条Tick数据异常记录")
            
        except Exception as e:
            self.logger.error(f"检查数据质量问题失败: {e}")
            issues.append(f"质量检查异常: {str(e)}")
        
        return issues
    
    def _get_table_name(self, data_type: str) -> str:
        """获取表名"""
        table_mapping = {
            'daily': 'daily_price',
            'minute': 'minute_price',
            'tick': 'tick_data',
            'level2': 'level2_data'
        }
        return table_mapping.get(data_type, data_type)
    
    # 数据修复
    async def repair_missing_data(self,
                                symbol: str,
                                data_type: str,
                                missing_dates: List[str]) -> bool:
        """修复缺失数据"""
        try:
            if not missing_dates:
                return True
            
            # 将缺失日期转换为日期范围
            date_ranges = self._group_consecutive_dates(missing_dates)
            
            # 为每个日期范围创建修复任务
            for start_date, end_date in date_ranges:
                task_id = await self.import_historical_data(
                    symbol, data_type, start_date, end_date
                )
                self.logger.info(f"创建数据修复任务: {task_id}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"修复缺失数据失败: {e}")
            return False
    
    def _group_consecutive_dates(self, date_strings: List[str]) -> List[Tuple[date, date]]:
        """将连续日期分组"""
        if not date_strings:
            return []
        
        # 转换为日期对象并排序
        dates = [datetime.strptime(d, '%Y-%m-%d').date() for d in date_strings]
        dates.sort()
        
        ranges = []
        start_date = dates[0]
        end_date = dates[0]
        
        for i in range(1, len(dates)):
            if dates[i] == end_date + timedelta(days=1):
                # 连续日期
                end_date = dates[i]
            else:
                # 不连续，保存当前范围并开始新范围
                ranges.append((start_date, end_date))
                start_date = dates[i]
                end_date = dates[i]
        
        # 添加最后一个范围
        ranges.append((start_date, end_date))
        
        return ranges
    
    # 备份和恢复
    async def backup_historical_data(self,
                                   data_type: str,
                                   backup_path: str) -> bool:
        """备份历史数据"""
        try:
            table_name = self._get_table_name(data_type)
            return await self.storage.backup_table(table_name, backup_path)
        except Exception as e:
            self.logger.error(f"备份历史数据失败: {e}")
            return False
    
    async def restore_historical_data(self,
                                    data_type: str,
                                    backup_path: str) -> bool:
        """恢复历史数据"""
        try:
            table_name = self._get_table_name(data_type)
            return await self.storage.restore_table(table_name, backup_path)
        except Exception as e:
            self.logger.error(f"恢复历史数据失败: {e}")
            return False
    
    # 统计和监控
    async def get_import_statistics(self) -> Dict[str, Any]:
        """获取导入统计信息"""
        total_tasks = len(self.import_tasks)
        completed_tasks = len([t for t in self.import_tasks.values() if t.status == 'completed'])
        failed_tasks = len([t for t in self.import_tasks.values() if t.status == 'failed'])
        running_tasks = len([t for t in self.import_tasks.values() if t.status == 'running'])
        pending_tasks = len([t for t in self.import_tasks.values() if t.status == 'pending'])
        
        return {
            'total_tasks': total_tasks,
            'completed_tasks': completed_tasks,
            'failed_tasks': failed_tasks,
            'running_tasks': running_tasks,
            'pending_tasks': pending_tasks,
            'success_rate': completed_tasks / total_tasks if total_tasks > 0 else 0,
            'queue_size': self.task_queue.qsize()
        }
    
    async def cleanup_old_tasks(self, days: int = 30):
        """清理旧任务记录"""
        cutoff_date = datetime.now() - timedelta(days=days)
        
        old_task_ids = [
            task_id for task_id, task in self.import_tasks.items()
            if task.created_time and task.created_time < cutoff_date
            and task.status in ['completed', 'failed']
        ]
        
        for task_id in old_task_ids:
            del self.import_tasks[task_id]
        
        self.logger.info(f"清理了 {len(old_task_ids)} 个旧任务记录")