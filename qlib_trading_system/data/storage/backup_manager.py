"""
数据备份和恢复管理器
实现自动备份、增量备份和灾难恢复
"""
import asyncio
import logging
import os
import shutil
import gzip
import json
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from pathlib import Path
import tarfile
import hashlib

from .clickhouse_storage import ClickHouseStorage


@dataclass
class BackupConfig:
    """备份配置"""
    backup_dir: str
    retention_days: int = 30
    compression: bool = True
    incremental: bool = True
    schedule_hour: int = 2  # 每日备份时间(小时)
    max_backup_size_gb: float = 10.0  # 最大备份大小(GB)


@dataclass
class BackupRecord:
    """备份记录"""
    backup_id: str
    backup_type: str  # 'full', 'incremental'
    table_name: str
    backup_path: str
    file_size: int
    record_count: int
    checksum: str
    created_time: datetime
    status: str = 'completed'  # 'running', 'completed', 'failed'
    error_message: Optional[str] = None


class BackupManager:
    """备份管理器"""
    
    def __init__(self, 
                 storage: ClickHouseStorage,
                 config: BackupConfig):
        
        self.storage = storage
        self.config = config
        
        # 创建备份目录
        self.backup_dir = Path(config.backup_dir)
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        # 备份记录
        self.backup_records: Dict[str, BackupRecord] = {}
        self.backup_metadata_file = self.backup_dir / "backup_metadata.json"
        
        # 控制标志
        self.is_running = False
        self.backup_task = None
        
        self.logger = logging.getLogger(__name__)
        
        # 加载备份记录
        self._load_backup_metadata()
    
    def _load_backup_metadata(self):
        """加载备份元数据"""
        try:
            if self.backup_metadata_file.exists():
                with open(self.backup_metadata_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                for record_data in data.get('records', []):
                    # 转换时间字段
                    record_data['created_time'] = datetime.fromisoformat(record_data['created_time'])
                    
                    record = BackupRecord(**record_data)
                    self.backup_records[record.backup_id] = record
                
                self.logger.info(f"加载了 {len(self.backup_records)} 条备份记录")
            
        except Exception as e:
            self.logger.error(f"加载备份元数据失败: {e}")
    
    def _save_backup_metadata(self):
        """保存备份元数据"""
        try:
            data = {
                'records': []
            }
            
            for record in self.backup_records.values():
                record_dict = asdict(record)
                # 转换时间字段为字符串
                record_dict['created_time'] = record.created_time.isoformat()
                data['records'].append(record_dict)
            
            with open(self.backup_metadata_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
        except Exception as e:
            self.logger.error(f"保存备份元数据失败: {e}")
    
    async def start_scheduled_backup(self):
        """启动定时备份"""
        self.is_running = True
        self.backup_task = asyncio.create_task(self._backup_scheduler())
        self.logger.info("定时备份已启动")
    
    async def stop_scheduled_backup(self):
        """停止定时备份"""
        self.is_running = False
        if self.backup_task:
            self.backup_task.cancel()
            try:
                await self.backup_task
            except asyncio.CancelledError:
                pass
        self.logger.info("定时备份已停止")
    
    async def _backup_scheduler(self):
        """备份调度器"""
        while self.is_running:
            try:
                now = datetime.now()
                
                # 计算下次备份时间
                next_backup = now.replace(
                    hour=self.config.schedule_hour,
                    minute=0,
                    second=0,
                    microsecond=0
                )
                
                if next_backup <= now:
                    next_backup += timedelta(days=1)
                
                # 等待到备份时间
                wait_seconds = (next_backup - now).total_seconds()
                await asyncio.sleep(wait_seconds)
                
                # 执行备份
                if self.is_running:
                    await self.backup_all_tables()
                    
                    # 清理过期备份
                    await self.cleanup_old_backups()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"备份调度异常: {e}")
                await asyncio.sleep(3600)  # 出错后等待1小时再试
    
    async def backup_table(self, 
                         table_name: str, 
                         backup_type: str = 'full',
                         since_time: Optional[datetime] = None) -> Optional[str]:
        """备份单个表"""
        try:
            # 生成备份ID
            backup_id = f"{table_name}_{backup_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # 创建备份记录
            record = BackupRecord(
                backup_id=backup_id,
                backup_type=backup_type,
                table_name=table_name,
                backup_path="",
                file_size=0,
                record_count=0,
                checksum="",
                created_time=datetime.now(),
                status='running'
            )
            
            self.backup_records[backup_id] = record
            
            self.logger.info(f"开始备份表 {table_name}, 类型: {backup_type}")
            
            # 构建查询SQL
            if backup_type == 'incremental' and since_time:
                # 增量备份
                sql = f"""
                SELECT * FROM {table_name}
                WHERE insert_time >= '{since_time.strftime('%Y-%m-%d %H:%M:%S')}'
                ORDER BY insert_time
                """
            else:
                # 全量备份
                sql = f"SELECT * FROM {table_name} ORDER BY insert_time"
            
            # 执行查询并导出
            backup_file = self.backup_dir / f"{backup_id}.csv"
            
            # 使用ClickHouse的INTO OUTFILE功能
            export_sql = f"""
            {sql}
            INTO OUTFILE '{backup_file}'
            FORMAT CSV
            """
            
            await self.storage.client.execute(export_sql)
            
            # 获取记录数
            count_sql = f"SELECT COUNT(*) as count FROM ({sql})"
            result = await self.storage.client.fetch(count_sql)
            record_count = result[0]['count'] if result else 0
            
            # 压缩文件
            if self.config.compression:
                compressed_file = self.backup_dir / f"{backup_id}.csv.gz"
                with open(backup_file, 'rb') as f_in:
                    with gzip.open(compressed_file, 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)
                
                # 删除原文件
                backup_file.unlink()
                backup_file = compressed_file
            
            # 计算文件大小和校验和
            file_size = backup_file.stat().st_size
            checksum = self._calculate_file_checksum(backup_file)
            
            # 更新备份记录
            record.backup_path = str(backup_file)
            record.file_size = file_size
            record.record_count = record_count
            record.checksum = checksum
            record.status = 'completed'
            
            # 保存元数据
            self._save_backup_metadata()
            
            self.logger.info(f"表 {table_name} 备份完成: {backup_id}, "
                           f"记录数: {record_count}, 文件大小: {file_size / 1024 / 1024:.2f}MB")
            
            return backup_id
            
        except Exception as e:
            # 更新备份记录状态
            if backup_id in self.backup_records:
                self.backup_records[backup_id].status = 'failed'
                self.backup_records[backup_id].error_message = str(e)
                self._save_backup_metadata()
            
            self.logger.error(f"备份表 {table_name} 失败: {e}")
            return None
    
    async def backup_all_tables(self) -> List[str]:
        """备份所有表"""
        backup_ids = []
        
        # 获取所有表名
        table_names = ['stock_basic_info', 'daily_price', 'minute_price', 'tick_data', 'level2_data']
        
        for table_name in table_names:
            try:
                # 检查是否需要增量备份
                backup_type = 'full'
                since_time = None
                
                if self.config.incremental:
                    # 查找最近的备份
                    recent_backup = self._get_most_recent_backup(table_name)
                    if recent_backup and recent_backup.status == 'completed':
                        backup_type = 'incremental'
                        since_time = recent_backup.created_time
                
                # 执行备份
                backup_id = await self.backup_table(table_name, backup_type, since_time)
                if backup_id:
                    backup_ids.append(backup_id)
                
            except Exception as e:
                self.logger.error(f"备份表 {table_name} 异常: {e}")
        
        self.logger.info(f"批量备份完成，成功备份 {len(backup_ids)} 个表")
        return backup_ids
    
    def _get_most_recent_backup(self, table_name: str) -> Optional[BackupRecord]:
        """获取最近的备份记录"""
        table_backups = [
            record for record in self.backup_records.values()
            if record.table_name == table_name and record.status == 'completed'
        ]
        
        if table_backups:
            return max(table_backups, key=lambda x: x.created_time)
        
        return None
    
    def _calculate_file_checksum(self, file_path: Path) -> str:
        """计算文件校验和"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    async def restore_table(self, backup_id: str) -> bool:
        """恢复表数据"""
        try:
            record = self.backup_records.get(backup_id)
            if not record:
                self.logger.error(f"备份记录不存在: {backup_id}")
                return False
            
            if record.status != 'completed':
                self.logger.error(f"备份状态异常: {backup_id}, 状态: {record.status}")
                return False
            
            backup_file = Path(record.backup_path)
            if not backup_file.exists():
                self.logger.error(f"备份文件不存在: {backup_file}")
                return False
            
            # 验证文件完整性
            current_checksum = self._calculate_file_checksum(backup_file)
            if current_checksum != record.checksum:
                self.logger.error(f"备份文件校验和不匹配: {backup_id}")
                return False
            
            self.logger.info(f"开始恢复表 {record.table_name} 从备份 {backup_id}")
            
            # 解压文件（如果需要）
            restore_file = backup_file
            if backup_file.suffix == '.gz':
                restore_file = backup_file.with_suffix('')
                with gzip.open(backup_file, 'rb') as f_in:
                    with open(restore_file, 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)
            
            # 使用ClickHouse的FROM INFILE功能恢复数据
            restore_sql = f"""
            INSERT INTO {record.table_name}
            FROM INFILE '{restore_file}'
            FORMAT CSV
            """
            
            await self.storage.client.execute(restore_sql)
            
            # 清理临时文件
            if restore_file != backup_file:
                restore_file.unlink()
            
            self.logger.info(f"表 {record.table_name} 恢复完成: {backup_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"恢复表失败: {e}")
            return False
    
    async def restore_to_point_in_time(self, 
                                     table_name: str, 
                                     target_time: datetime) -> bool:
        """恢复到指定时间点"""
        try:
            # 找到目标时间之前的最近全量备份
            full_backups = [
                record for record in self.backup_records.values()
                if (record.table_name == table_name and 
                    record.backup_type == 'full' and
                    record.created_time <= target_time and
                    record.status == 'completed')
            ]
            
            if not full_backups:
                self.logger.error(f"未找到表 {table_name} 在 {target_time} 之前的全量备份")
                return False
            
            # 选择最近的全量备份
            base_backup = max(full_backups, key=lambda x: x.created_time)
            
            # 找到该全量备份之后到目标时间的所有增量备份
            incremental_backups = [
                record for record in self.backup_records.values()
                if (record.table_name == table_name and
                    record.backup_type == 'incremental' and
                    record.created_time > base_backup.created_time and
                    record.created_time <= target_time and
                    record.status == 'completed')
            ]
            
            # 按时间排序增量备份
            incremental_backups.sort(key=lambda x: x.created_time)
            
            self.logger.info(f"开始时间点恢复: 基础备份 {base_backup.backup_id}, "
                           f"增量备份 {len(incremental_backups)} 个")
            
            # 清空目标表
            await self.storage.client.execute(f"TRUNCATE TABLE {table_name}")
            
            # 恢复全量备份
            if not await self.restore_table(base_backup.backup_id):
                return False
            
            # 依次应用增量备份
            for inc_backup in incremental_backups:
                if not await self.restore_table(inc_backup.backup_id):
                    return False
            
            self.logger.info(f"时间点恢复完成: {table_name} -> {target_time}")
            return True
            
        except Exception as e:
            self.logger.error(f"时间点恢复失败: {e}")
            return False
    
    async def cleanup_old_backups(self):
        """清理过期备份"""
        try:
            cutoff_date = datetime.now() - timedelta(days=self.config.retention_days)
            
            old_backup_ids = [
                backup_id for backup_id, record in self.backup_records.items()
                if record.created_time < cutoff_date
            ]
            
            cleaned_count = 0
            for backup_id in old_backup_ids:
                record = self.backup_records[backup_id]
                
                # 删除备份文件
                backup_file = Path(record.backup_path)
                if backup_file.exists():
                    backup_file.unlink()
                
                # 删除记录
                del self.backup_records[backup_id]
                cleaned_count += 1
            
            if cleaned_count > 0:
                # 保存更新后的元数据
                self._save_backup_metadata()
                self.logger.info(f"清理了 {cleaned_count} 个过期备份")
            
        except Exception as e:
            self.logger.error(f"清理过期备份失败: {e}")
    
    def get_backup_statistics(self) -> Dict[str, Any]:
        """获取备份统计信息"""
        total_backups = len(self.backup_records)
        completed_backups = len([r for r in self.backup_records.values() if r.status == 'completed'])
        failed_backups = len([r for r in self.backup_records.values() if r.status == 'failed'])
        
        total_size = sum(r.file_size for r in self.backup_records.values() if r.status == 'completed')
        total_records = sum(r.record_count for r in self.backup_records.values() if r.status == 'completed')
        
        # 按表统计
        table_stats = {}
        for record in self.backup_records.values():
            if record.status == 'completed':
                if record.table_name not in table_stats:
                    table_stats[record.table_name] = {
                        'backup_count': 0,
                        'total_size': 0,
                        'total_records': 0,
                        'latest_backup': None
                    }
                
                stats = table_stats[record.table_name]
                stats['backup_count'] += 1
                stats['total_size'] += record.file_size
                stats['total_records'] += record.record_count
                
                if not stats['latest_backup'] or record.created_time > stats['latest_backup']:
                    stats['latest_backup'] = record.created_time
        
        return {
            'total_backups': total_backups,
            'completed_backups': completed_backups,
            'failed_backups': failed_backups,
            'success_rate': completed_backups / total_backups if total_backups > 0 else 0,
            'total_size_mb': total_size / 1024 / 1024,
            'total_records': total_records,
            'table_statistics': table_stats,
            'backup_directory': str(self.backup_dir),
            'retention_days': self.config.retention_days
        }
    
    def get_backup_records(self, 
                          table_name: Optional[str] = None,
                          backup_type: Optional[str] = None,
                          limit: int = 100) -> List[BackupRecord]:
        """获取备份记录"""
        records = list(self.backup_records.values())
        
        # 过滤条件
        if table_name:
            records = [r for r in records if r.table_name == table_name]
        
        if backup_type:
            records = [r for r in records if r.backup_type == backup_type]
        
        # 按创建时间倒序排列
        records.sort(key=lambda x: x.created_time, reverse=True)
        
        return records[:limit]
    
    async def verify_backup_integrity(self, backup_id: str) -> bool:
        """验证备份完整性"""
        try:
            record = self.backup_records.get(backup_id)
            if not record:
                return False
            
            backup_file = Path(record.backup_path)
            if not backup_file.exists():
                return False
            
            # 验证文件大小
            current_size = backup_file.stat().st_size
            if current_size != record.file_size:
                self.logger.error(f"备份文件大小不匹配: {backup_id}")
                return False
            
            # 验证校验和
            current_checksum = self._calculate_file_checksum(backup_file)
            if current_checksum != record.checksum:
                self.logger.error(f"备份文件校验和不匹配: {backup_id}")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"验证备份完整性失败: {e}")
            return False