"""
数据存储模块
实现ClickHouse时序数据库存储、历史数据管理和备份恢复
"""

from .clickhouse_storage import ClickHouseStorage, TableSchema
from .historical_data_manager import (
    HistoricalDataManager, ImportTask, DataIntegrityReport
)
from .backup_manager import BackupManager, BackupConfig, BackupRecord

__all__ = [
    # ClickHouse存储
    'ClickHouseStorage',
    'TableSchema',
    
    # 历史数据管理
    'HistoricalDataManager',
    'ImportTask',
    'DataIntegrityReport',
    
    # 备份管理
    'BackupManager',
    'BackupConfig',
    'BackupRecord'
]

# 数据存储接口将在后续任务中实现
# 这里先定义模块结构

__all__ = [
    'BaseDataStorage',
    'ClickHouseStorage',
    'RedisStorage', 
    'MongoDBStorage',
    'LocalFileStorage'
]