"""
ClickHouse时序数据库存储
用于存储历史行情数据、tick数据和Level-2数据
"""
import asyncio
import logging
from typing import Dict, List, Optional, Any, Union, Tuple
from datetime import datetime, date, timedelta
import pandas as pd
import numpy as np
from dataclasses import dataclass, asdict
import asyncio_clickhouse
from asyncio_clickhouse import ClickHouseError

from ..collectors.base import StockBasicInfo, PriceData, TickData, Level2Data


@dataclass
class TableSchema:
    """表结构定义"""
    table_name: str
    columns: Dict[str, str]  # 列名 -> 数据类型
    partition_by: Optional[str] = None
    order_by: Optional[List[str]] = None
    primary_key: Optional[List[str]] = None


class ClickHouseStorage:
    """ClickHouse存储管理器"""
    
    def __init__(self, 
                 host: str = "localhost",
                 port: int = 9000,
                 database: str = "qlib_trading",
                 username: str = "default",
                 password: str = ""):
        
        self.host = host
        self.port = port
        self.database = database
        self.username = username
        self.password = password
        
        self.client = None
        self.is_connected = False
        
        self.logger = logging.getLogger(__name__)
        
        # 定义表结构
        self.table_schemas = self._define_table_schemas()
    
    def _define_table_schemas(self) -> Dict[str, TableSchema]:
        """定义表结构"""
        schemas = {}
        
        # 股票基础信息表
        schemas['stock_basic_info'] = TableSchema(
            table_name='stock_basic_info',
            columns={
                'symbol': 'String',
                'name': 'String',
                'industry': 'String',
                'market': 'String',
                'market_cap': 'Nullable(Float64)',
                'listing_date': 'Nullable(Date)',
                'is_st': 'UInt8',
                'is_suspended': 'UInt8',
                'update_time': 'DateTime'
            },
            order_by=['symbol'],
            primary_key=['symbol']
        )
        
        # 日线数据表
        schemas['daily_price'] = TableSchema(
            table_name='daily_price',
            columns={
                'symbol': 'String',
                'date': 'Date',
                'open': 'Float64',
                'high': 'Float64',
                'low': 'Float64',
                'close': 'Float64',
                'volume': 'UInt64',
                'amount': 'Float64',
                'pre_close': 'Float64',
                'change': 'Float64',
                'pct_change': 'Float64',
                'insert_time': 'DateTime'
            },
            partition_by='toYYYYMM(date)',
            order_by=['symbol', 'date'],
            primary_key=['symbol', 'date']
        )
        
        # 分钟线数据表
        schemas['minute_price'] = TableSchema(
            table_name='minute_price',
            columns={
                'symbol': 'String',
                'datetime': 'DateTime',
                'open': 'Float64',
                'high': 'Float64',
                'low': 'Float64',
                'close': 'Float64',
                'volume': 'UInt64',
                'amount': 'Float64',
                'insert_time': 'DateTime'
            },
            partition_by='toYYYYMM(datetime)',
            order_by=['symbol', 'datetime'],
            primary_key=['symbol', 'datetime']
        )
        
        # Tick数据表
        schemas['tick_data'] = TableSchema(
            table_name='tick_data',
            columns={
                'symbol': 'String',
                'timestamp': 'DateTime64(3)',
                'price': 'Float64',
                'volume': 'UInt32',
                'amount': 'Float64',
                'direction': 'String',
                'bid_price': 'Float64',
                'ask_price': 'Float64',
                'bid_volume': 'UInt32',
                'ask_volume': 'UInt32',
                'insert_time': 'DateTime'
            },
            partition_by='toYYYYMMDD(timestamp)',
            order_by=['symbol', 'timestamp'],
            primary_key=['symbol', 'timestamp']
        )
        
        # Level-2数据表
        schemas['level2_data'] = TableSchema(
            table_name='level2_data',
            columns={
                'symbol': 'String',
                'timestamp': 'DateTime64(3)',
                'bid_prices': 'Array(Float64)',
                'bid_volumes': 'Array(UInt32)',
                'ask_prices': 'Array(Float64)',
                'ask_volumes': 'Array(UInt32)',
                'total_bid_volume': 'UInt64',
                'total_ask_volume': 'UInt64',
                'insert_time': 'DateTime'
            },
            partition_by='toYYYYMMDD(timestamp)',
            order_by=['symbol', 'timestamp'],
            primary_key=['symbol', 'timestamp']
        )
        
        return schemas
    
    async def connect(self) -> bool:
        """连接ClickHouse"""
        try:
            self.client = asyncio_clickhouse.Client(
                host=self.host,
                port=self.port,
                database=self.database,
                user=self.username,
                password=self.password
            )
            
            # 测试连接
            await self.client.execute("SELECT 1")
            self.is_connected = True
            
            self.logger.info(f"ClickHouse连接成功: {self.host}:{self.port}/{self.database}")
            return True
            
        except Exception as e:
            self.logger.error(f"ClickHouse连接失败: {e}")
            self.is_connected = False
            return False
    
    async def disconnect(self):
        """断开连接"""
        if self.client:
            await self.client.close()
            self.client = None
            self.is_connected = False
            self.logger.info("ClickHouse连接已断开")
    
    async def create_database(self) -> bool:
        """创建数据库"""
        try:
            # 连接到默认数据库
            temp_client = asyncio_clickhouse.Client(
                host=self.host,
                port=self.port,
                user=self.username,
                password=self.password
            )
            
            # 创建数据库
            await temp_client.execute(f"CREATE DATABASE IF NOT EXISTS {self.database}")
            await temp_client.close()
            
            self.logger.info(f"数据库 {self.database} 创建成功")
            return True
            
        except Exception as e:
            self.logger.error(f"创建数据库失败: {e}")
            return False
    
    async def create_tables(self) -> bool:
        """创建所有表"""
        if not self.is_connected:
            self.logger.error("未连接到ClickHouse")
            return False
        
        try:
            for schema in self.table_schemas.values():
                await self._create_table(schema)
            
            self.logger.info("所有表创建成功")
            return True
            
        except Exception as e:
            self.logger.error(f"创建表失败: {e}")
            return False
    
    async def _create_table(self, schema: TableSchema):
        """创建单个表"""
        # 构建列定义
        columns_def = []
        for col_name, col_type in schema.columns.items():
            columns_def.append(f"{col_name} {col_type}")
        
        # 构建CREATE TABLE语句
        sql = f"CREATE TABLE IF NOT EXISTS {schema.table_name} (\n"
        sql += ",\n".join(columns_def)
        sql += "\n) ENGINE = MergeTree()"
        
        # 添加分区
        if schema.partition_by:
            sql += f"\nPARTITION BY {schema.partition_by}"
        
        # 添加排序键
        if schema.order_by:
            sql += f"\nORDER BY ({', '.join(schema.order_by)})"
        
        # 添加主键
        if schema.primary_key:
            sql += f"\nPRIMARY KEY ({', '.join(schema.primary_key)})"
        
        await self.client.execute(sql)
        self.logger.info(f"表 {schema.table_name} 创建成功")
    
    # 数据插入方法
    async def insert_stock_basic_info(self, stocks: List[StockBasicInfo]) -> bool:
        """插入股票基础信息"""
        if not stocks:
            return True
        
        try:
            data = []
            for stock in stocks:
                data.append({
                    'symbol': stock.symbol,
                    'name': stock.name,
                    'industry': stock.industry,
                    'market': stock.market,
                    'market_cap': stock.market_cap,
                    'listing_date': stock.listing_date,
                    'is_st': 1 if stock.is_st else 0,
                    'is_suspended': 1 if stock.is_suspended else 0,
                    'update_time': datetime.now()
                })
            
            await self.client.insert('stock_basic_info', data)
            self.logger.info(f"插入股票基础信息: {len(data)} 条")
            return True
            
        except Exception as e:
            self.logger.error(f"插入股票基础信息失败: {e}")
            return False
    
    async def insert_daily_price(self, df: pd.DataFrame) -> bool:
        """插入日线数据"""
        if df.empty:
            return True
        
        try:
            # 重置索引，确保symbol和date是列
            df_reset = df.reset_index()
            
            # 添加插入时间
            df_reset['insert_time'] = datetime.now()
            
            # 转换为字典列表
            data = df_reset.to_dict('records')
            
            await self.client.insert('daily_price', data)
            self.logger.info(f"插入日线数据: {len(data)} 条")
            return True
            
        except Exception as e:
            self.logger.error(f"插入日线数据失败: {e}")
            return False
    
    async def insert_minute_price(self, df: pd.DataFrame) -> bool:
        """插入分钟线数据"""
        if df.empty:
            return True
        
        try:
            # 重置索引
            df_reset = df.reset_index()
            
            # 添加插入时间
            df_reset['insert_time'] = datetime.now()
            
            # 转换为字典列表
            data = df_reset.to_dict('records')
            
            await self.client.insert('minute_price', data)
            self.logger.info(f"插入分钟线数据: {len(data)} 条")
            return True
            
        except Exception as e:
            self.logger.error(f"插入分钟线数据失败: {e}")
            return False
    
    async def insert_tick_data(self, ticks: List[TickData]) -> bool:
        """插入Tick数据"""
        if not ticks:
            return True
        
        try:
            data = []
            for tick in ticks:
                data.append({
                    'symbol': tick.symbol,
                    'timestamp': tick.timestamp,
                    'price': tick.price,
                    'volume': tick.volume,
                    'amount': tick.amount,
                    'direction': tick.direction,
                    'bid_price': tick.bid_price or 0,
                    'ask_price': tick.ask_price or 0,
                    'bid_volume': tick.bid_volume or 0,
                    'ask_volume': tick.ask_volume or 0,
                    'insert_time': datetime.now()
                })
            
            await self.client.insert('tick_data', data)
            self.logger.info(f"插入Tick数据: {len(data)} 条")
            return True
            
        except Exception as e:
            self.logger.error(f"插入Tick数据失败: {e}")
            return False
    
    async def insert_level2_data(self, level2_list: List[Level2Data]) -> bool:
        """插入Level-2数据"""
        if not level2_list:
            return True
        
        try:
            data = []
            for level2 in level2_list:
                data.append({
                    'symbol': level2.symbol,
                    'timestamp': level2.timestamp,
                    'bid_prices': level2.bid_prices,
                    'bid_volumes': level2.bid_volumes,
                    'ask_prices': level2.ask_prices,
                    'ask_volumes': level2.ask_volumes,
                    'total_bid_volume': level2.total_bid_volume,
                    'total_ask_volume': level2.total_ask_volume,
                    'insert_time': datetime.now()
                })
            
            await self.client.insert('level2_data', data)
            self.logger.info(f"插入Level-2数据: {len(data)} 条")
            return True
            
        except Exception as e:
            self.logger.error(f"插入Level-2数据失败: {e}")
            return False
    
    # 数据查询方法
    async def query_stock_basic_info(self, symbols: Optional[List[str]] = None) -> List[StockBasicInfo]:
        """查询股票基础信息"""
        try:
            sql = "SELECT * FROM stock_basic_info"
            params = {}
            
            if symbols:
                placeholders = ','.join([f"'{symbol}'" for symbol in symbols])
                sql += f" WHERE symbol IN ({placeholders})"
            
            sql += " ORDER BY symbol"
            
            result = await self.client.fetch(sql, params)
            
            stocks = []
            for row in result:
                stock = StockBasicInfo(
                    symbol=row['symbol'],
                    name=row['name'],
                    industry=row['industry'],
                    market=row['market'],
                    market_cap=row['market_cap'],
                    listing_date=row['listing_date'],
                    is_st=bool(row['is_st']),
                    is_suspended=bool(row['is_suspended'])
                )
                stocks.append(stock)
            
            return stocks
            
        except Exception as e:
            self.logger.error(f"查询股票基础信息失败: {e}")
            return []
    
    async def query_daily_price(self, 
                              symbols: List[str],
                              start_date: Union[str, date],
                              end_date: Union[str, date]) -> pd.DataFrame:
        """查询日线数据"""
        try:
            # 转换日期格式
            if isinstance(start_date, date):
                start_date = start_date.strftime('%Y-%m-%d')
            if isinstance(end_date, date):
                end_date = end_date.strftime('%Y-%m-%d')
            
            symbols_str = ','.join([f"'{symbol}'" for symbol in symbols])
            
            sql = f"""
            SELECT symbol, date, open, high, low, close, volume, amount, 
                   pre_close, change, pct_change
            FROM daily_price
            WHERE symbol IN ({symbols_str})
              AND date >= '{start_date}'
              AND date <= '{end_date}'
            ORDER BY symbol, date
            """
            
            result = await self.client.fetch(sql)
            
            # 转换为DataFrame
            if result:
                df = pd.DataFrame(result)
                df['date'] = pd.to_datetime(df['date'])
                df.set_index(['symbol', 'date'], inplace=True)
                return df
            else:
                return pd.DataFrame()
                
        except Exception as e:
            self.logger.error(f"查询日线数据失败: {e}")
            return pd.DataFrame()
    
    async def query_minute_price(self,
                               symbol: str,
                               start_time: Union[str, datetime],
                               end_time: Union[str, datetime]) -> pd.DataFrame:
        """查询分钟线数据"""
        try:
            # 转换时间格式
            if isinstance(start_time, datetime):
                start_time = start_time.strftime('%Y-%m-%d %H:%M:%S')
            if isinstance(end_time, datetime):
                end_time = end_time.strftime('%Y-%m-%d %H:%M:%S')
            
            sql = f"""
            SELECT symbol, datetime, open, high, low, close, volume, amount
            FROM minute_price
            WHERE symbol = '{symbol}'
              AND datetime >= '{start_time}'
              AND datetime <= '{end_time}'
            ORDER BY datetime
            """
            
            result = await self.client.fetch(sql)
            
            # 转换为DataFrame
            if result:
                df = pd.DataFrame(result)
                df['datetime'] = pd.to_datetime(df['datetime'])
                df.set_index('datetime', inplace=True)
                return df
            else:
                return pd.DataFrame()
                
        except Exception as e:
            self.logger.error(f"查询分钟线数据失败: {e}")
            return pd.DataFrame()
    
    async def query_tick_data(self,
                            symbol: str,
                            start_time: Union[str, datetime],
                            end_time: Union[str, datetime]) -> List[TickData]:
        """查询Tick数据"""
        try:
            # 转换时间格式
            if isinstance(start_time, datetime):
                start_time = start_time.strftime('%Y-%m-%d %H:%M:%S')
            if isinstance(end_time, datetime):
                end_time = end_time.strftime('%Y-%m-%d %H:%M:%S')
            
            sql = f"""
            SELECT *
            FROM tick_data
            WHERE symbol = '{symbol}'
              AND timestamp >= '{start_time}'
              AND timestamp <= '{end_time}'
            ORDER BY timestamp
            """
            
            result = await self.client.fetch(sql)
            
            # 转换为TickData对象
            ticks = []
            for row in result:
                tick = TickData(
                    symbol=row['symbol'],
                    timestamp=row['timestamp'],
                    price=row['price'],
                    volume=row['volume'],
                    amount=row['amount'],
                    direction=row['direction'],
                    bid_price=row['bid_price'],
                    ask_price=row['ask_price'],
                    bid_volume=row['bid_volume'],
                    ask_volume=row['ask_volume']
                )
                ticks.append(tick)
            
            return ticks
            
        except Exception as e:
            self.logger.error(f"查询Tick数据失败: {e}")
            return []
    
    # 数据完整性检查
    async def check_data_completeness(self, 
                                    table_name: str,
                                    symbol: str,
                                    start_date: Union[str, date],
                                    end_date: Union[str, date]) -> Dict[str, Any]:
        """检查数据完整性"""
        try:
            # 转换日期格式
            if isinstance(start_date, date):
                start_date = start_date.strftime('%Y-%m-%d')
            if isinstance(end_date, date):
                end_date = end_date.strftime('%Y-%m-%d')
            
            # 根据表类型选择时间字段
            time_field = 'date' if table_name in ['daily_price'] else 'datetime' if table_name == 'minute_price' else 'timestamp'
            
            sql = f"""
            SELECT 
                COUNT(*) as total_records,
                MIN({time_field}) as min_time,
                MAX({time_field}) as max_time,
                COUNT(DISTINCT toDate({time_field})) as unique_days
            FROM {table_name}
            WHERE symbol = '{symbol}'
              AND {time_field} >= '{start_date}'
              AND {time_field} <= '{end_date}'
            """
            
            result = await self.client.fetch(sql)
            
            if result:
                row = result[0]
                return {
                    'symbol': symbol,
                    'table_name': table_name,
                    'total_records': row['total_records'],
                    'min_time': row['min_time'],
                    'max_time': row['max_time'],
                    'unique_days': row['unique_days'],
                    'start_date': start_date,
                    'end_date': end_date
                }
            else:
                return {}
                
        except Exception as e:
            self.logger.error(f"检查数据完整性失败: {e}")
            return {}
    
    async def get_missing_dates(self,
                              table_name: str,
                              symbol: str,
                              start_date: Union[str, date],
                              end_date: Union[str, date]) -> List[str]:
        """获取缺失的日期"""
        try:
            # 转换日期格式
            if isinstance(start_date, date):
                start_date = start_date.strftime('%Y-%m-%d')
            if isinstance(end_date, date):
                end_date = end_date.strftime('%Y-%m-%d')
            
            time_field = 'date' if table_name in ['daily_price'] else 'datetime' if table_name == 'minute_price' else 'timestamp'
            
            # 生成日期序列并找出缺失的日期
            sql = f"""
            WITH date_series AS (
                SELECT toDate('{start_date}') + number as expected_date
                FROM numbers(dateDiff('day', toDate('{start_date}'), toDate('{end_date}')) + 1)
            ),
            existing_dates AS (
                SELECT DISTINCT toDate({time_field}) as existing_date
                FROM {table_name}
                WHERE symbol = '{symbol}'
                  AND {time_field} >= '{start_date}'
                  AND {time_field} <= '{end_date}'
            )
            SELECT expected_date
            FROM date_series
            LEFT JOIN existing_dates ON expected_date = existing_date
            WHERE existing_date IS NULL
            ORDER BY expected_date
            """
            
            result = await self.client.fetch(sql)
            
            missing_dates = [row['expected_date'].strftime('%Y-%m-%d') for row in result]
            return missing_dates
            
        except Exception as e:
            self.logger.error(f"获取缺失日期失败: {e}")
            return []
    
    # 数据备份和恢复
    async def backup_table(self, table_name: str, backup_path: str) -> bool:
        """备份表数据"""
        try:
            sql = f"""
            INSERT INTO FUNCTION file('{backup_path}', 'Parquet')
            SELECT * FROM {table_name}
            """
            
            await self.client.execute(sql)
            self.logger.info(f"表 {table_name} 备份成功: {backup_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"备份表 {table_name} 失败: {e}")
            return False
    
    async def restore_table(self, table_name: str, backup_path: str) -> bool:
        """恢复表数据"""
        try:
            sql = f"""
            INSERT INTO {table_name}
            SELECT * FROM file('{backup_path}', 'Parquet')
            """
            
            await self.client.execute(sql)
            self.logger.info(f"表 {table_name} 恢复成功: {backup_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"恢复表 {table_name} 失败: {e}")
            return False
    
    # 统计信息
    async def get_table_stats(self, table_name: str) -> Dict[str, Any]:
        """获取表统计信息"""
        try:
            sql = f"""
            SELECT 
                COUNT(*) as total_rows,
                COUNT(DISTINCT symbol) as unique_symbols,
                MIN(insert_time) as earliest_insert,
                MAX(insert_time) as latest_insert
            FROM {table_name}
            """
            
            result = await self.client.fetch(sql)
            
            if result:
                return result[0]
            else:
                return {}
                
        except Exception as e:
            self.logger.error(f"获取表统计信息失败: {e}")
            return {}
    
    async def optimize_table(self, table_name: str) -> bool:
        """优化表"""
        try:
            sql = f"OPTIMIZE TABLE {table_name}"
            await self.client.execute(sql)
            self.logger.info(f"表 {table_name} 优化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"优化表 {table_name} 失败: {e}")
            return False