# -*- coding: utf-8 -*-
"""
历史数据回放系统

实现高保真的历史数据回放，支持tick级别和分钟级别数据回放
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Iterator, Tuple, Any
import logging
from dataclasses import dataclass
from enum import Enum
import sqlite3
import os

logger = logging.getLogger(__name__)


class DataFrequency(Enum):
    """数据频率枚举"""
    TICK = "tick"
    MINUTE_1 = "1min"
    MINUTE_5 = "5min"
    MINUTE_15 = "15min"
    MINUTE_30 = "30min"
    HOUR_1 = "1h"
    DAILY = "1d"


@dataclass
class MarketData:
    """市场数据结构"""
    symbol: str
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: int
    amount: float
    frequency: DataFrequency
    
    # Level-2数据（可选）
    bid_price: Optional[List[float]] = None
    bid_volume: Optional[List[int]] = None
    ask_price: Optional[List[float]] = None
    ask_volume: Optional[List[int]] = None
    
    # 技术指标（可选）
    indicators: Optional[Dict[str, float]] = None


@dataclass
class ReplayConfig:
    """回放配置"""
    start_date: datetime
    end_date: datetime
    symbols: List[str]
    frequency: DataFrequency
    include_level2: bool = False
    include_indicators: bool = True
    speed_multiplier: float = 1.0  # 回放速度倍数
    
    # 数据源配置
    data_source: str = "local"  # local, database, api
    data_path: Optional[str] = None
    
    # 过滤条件
    trading_hours_only: bool = True
    exclude_suspended: bool = True


class HistoricalDataReplay:
    """历史数据回放系统"""
    
    def __init__(self, config: ReplayConfig):
        """
        初始化历史数据回放系统
        
        Args:
            config: 回放配置
        """
        self.config = config
        self.data_cache: Dict[str, pd.DataFrame] = {}
        self.current_time: Optional[datetime] = None
        self.replay_iterator: Optional[Iterator] = None
        
        # 初始化数据源
        self._init_data_source()
        
        logger.info(f"历史数据回放系统初始化完成，时间范围：{config.start_date} - {config.end_date}")
    
    def _init_data_source(self):
        """初始化数据源"""
        if self.config.data_source == "local":
            self._init_local_data_source()
        elif self.config.data_source == "database":
            self._init_database_source()
        else:
            raise ValueError(f"不支持的数据源类型：{self.config.data_source}")
    
    def _init_local_data_source(self):
        """初始化本地数据源"""
        if not self.config.data_path:
            self.config.data_path = "data/processed"
        
        if not os.path.exists(self.config.data_path):
            os.makedirs(self.config.data_path)
            logger.warning(f"数据目录不存在，已创建：{self.config.data_path}")
    
    def _init_database_source(self):
        """初始化数据库数据源"""
        # 这里可以连接到ClickHouse或其他时序数据库
        pass
    
    def load_historical_data(self) -> Dict[str, pd.DataFrame]:
        """
        加载历史数据
        
        Returns:
            Dict[str, pd.DataFrame]: 股票代码到数据DataFrame的映射
        """
        logger.info("开始加载历史数据...")
        
        for symbol in self.config.symbols:
            try:
                data = self._load_symbol_data(symbol)
                if data is not None and not data.empty:
                    self.data_cache[symbol] = data
                    logger.info(f"成功加载 {symbol} 数据，共 {len(data)} 条记录")
                else:
                    logger.warning(f"未找到 {symbol} 的历史数据")
            except Exception as e:
                logger.error(f"加载 {symbol} 数据失败：{e}")
        
        logger.info(f"历史数据加载完成，共加载 {len(self.data_cache)} 只股票")
        return self.data_cache
    
    def _load_symbol_data(self, symbol: str) -> Optional[pd.DataFrame]:
        """
        加载单个股票的历史数据
        
        Args:
            symbol: 股票代码
            
        Returns:
            Optional[pd.DataFrame]: 历史数据
        """
        if self.config.data_source == "local":
            return self._load_local_symbol_data(symbol)
        elif self.config.data_source == "database":
            return self._load_database_symbol_data(symbol)
        else:
            return self._generate_mock_data(symbol)
    
    def _load_local_symbol_data(self, symbol: str) -> Optional[pd.DataFrame]:
        """从本地文件加载数据"""
        file_path = os.path.join(
            self.config.data_path, 
            f"{symbol}_{self.config.frequency.value}.csv"
        )
        
        if os.path.exists(file_path):
            df = pd.read_csv(file_path)
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df = df.set_index('timestamp')
            
            # 过滤时间范围
            df = df[
                (df.index >= self.config.start_date) & 
                (df.index <= self.config.end_date)
            ]
            
            return df
        else:
            logger.warning(f"本地数据文件不存在：{file_path}")
            # 直接生成模拟数据
            return self._generate_mock_data(symbol)
    
    def _load_database_symbol_data(self, symbol: str) -> Optional[pd.DataFrame]:
        """从数据库加载数据"""
        # 这里实现数据库查询逻辑
        # 暂时返回模拟数据
        return self._generate_mock_data(symbol)
    
    def _generate_mock_data(self, symbol: str) -> pd.DataFrame:
        """
        生成模拟历史数据用于测试
        
        Args:
            symbol: 股票代码
            
        Returns:
            pd.DataFrame: 模拟数据
        """
        logger.info(f"生成 {symbol} 的模拟历史数据")
        
        # 生成时间序列
        if self.config.frequency == DataFrequency.MINUTE_1:
            freq = '1min'
        elif self.config.frequency == DataFrequency.MINUTE_5:
            freq = '5min'
        elif self.config.frequency == DataFrequency.DAILY:
            freq = '1D'
        else:
            freq = '1min'
        
        # 只在交易时间生成数据
        if self.config.trading_hours_only:
            timestamps = self._generate_trading_timestamps(freq)
        else:
            timestamps = pd.date_range(
                start=self.config.start_date,
                end=self.config.end_date,
                freq=freq
            )
        
        n_points = len(timestamps)
        
        if n_points == 0:
            logger.warning(f"没有生成任何时间戳，请检查时间范围和频率设置")
            return pd.DataFrame()
        
        # 生成价格数据（随机游走）
        np.random.seed(hash(symbol) % 2**32)  # 确保每个股票的数据一致
        
        initial_price = 10.0 + np.random.random() * 20  # 10-30元初始价格
        returns = np.random.normal(0, 0.02, n_points)  # 2%日波动率
        
        prices = [initial_price]
        for ret in returns[1:]:
            new_price = prices[-1] * (1 + ret)
            prices.append(max(new_price, 0.1))  # 防止价格为负
        
        # 如果只有一个价格点，复制一个
        if len(prices) == 1:
            prices.append(prices[0])
        
        # 生成OHLCV数据
        data = []
        for i, timestamp in enumerate(timestamps):
            close = prices[i] if i < len(prices) else prices[-1]
            
            # 生成开高低价
            volatility = abs(np.random.normal(0, 0.01))
            high = close * (1 + volatility)
            low = close * (1 - volatility)
            
            if i == 0:
                open_price = close
            else:
                open_price = prices[i-1] if i-1 < len(prices) else close
            
            # 确保价格关系合理
            high = max(high, open_price, close)
            low = min(low, open_price, close)
            
            # 生成成交量
            base_volume = 1000000 + np.random.randint(0, 5000000)
            volume = int(base_volume * (1 + abs(returns[i] if i < len(returns) else 0) * 10))
            
            # 生成成交额
            amount = volume * close
            
            data.append({
                'symbol': symbol,
                'open': round(open_price, 2),
                'high': round(high, 2),
                'low': round(low, 2),
                'close': round(close, 2),
                'volume': volume,
                'amount': round(amount, 2)
            })
        
        df = pd.DataFrame(data)
        df.index = timestamps
        
        # 添加技术指标
        if self.config.include_indicators:
            df = self._add_technical_indicators(df)
        
        logger.info(f"成功生成 {symbol} 的模拟数据，共 {len(df)} 条记录")
        return df
    
    def _generate_trading_timestamps(self, freq: str) -> pd.DatetimeIndex:
        """生成交易时间戳（仅交易时间）"""
        # 对于日频数据，直接生成工作日
        if freq == '1D':
            all_timestamps = pd.date_range(
                start=self.config.start_date,
                end=self.config.end_date,
                freq='B'  # 工作日
            )
            # 设置为收盘时间
            trading_timestamps = [ts.replace(hour=15, minute=0, second=0) for ts in all_timestamps]
            return pd.DatetimeIndex(trading_timestamps)
        
        # 对于分钟级数据，生成交易时间
        all_timestamps = pd.date_range(
            start=self.config.start_date,
            end=self.config.end_date,
            freq=freq
        )
        
        # 过滤交易时间（9:30-11:30, 13:00-15:00）
        trading_timestamps = []
        for ts in all_timestamps:
            # 跳过周末
            if ts.weekday() >= 5:
                continue
            
            # 检查是否在交易时间内
            time = ts.time()
            morning_session = (time >= pd.Timestamp('09:30').time() and 
                             time <= pd.Timestamp('11:30').time())
            afternoon_session = (time >= pd.Timestamp('13:00').time() and 
                                time <= pd.Timestamp('15:00').time())
            
            if morning_session or afternoon_session:
                trading_timestamps.append(ts)
        
        return pd.DatetimeIndex(trading_timestamps)
    
    def _add_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加技术指标"""
        try:
            # 确保有close列
            if 'close' not in df.columns:
                logger.warning("数据中没有close列，无法计算技术指标")
                return df
            
            # 移动平均线
            df['ma5'] = df['close'].rolling(window=5).mean()
            df['ma10'] = df['close'].rolling(window=10).mean()
            df['ma20'] = df['close'].rolling(window=20).mean()
            
            # RSI
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            df['rsi'] = 100 - (100 / (1 + rs))
            
            # MACD
            exp1 = df['close'].ewm(span=12).mean()
            exp2 = df['close'].ewm(span=26).mean()
            df['macd'] = exp1 - exp2
            df['macd_signal'] = df['macd'].ewm(span=9).mean()
            df['macd_hist'] = df['macd'] - df['macd_signal']
            
            # 布林带
            df['bb_middle'] = df['close'].rolling(window=20).mean()
            bb_std = df['close'].rolling(window=20).std()
            df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
            df['bb_lower'] = df['bb_middle'] - (bb_std * 2)
            
        except Exception as e:
            logger.warning(f"计算技术指标失败：{e}")
        
        return df
    
    def start_replay(self) -> Iterator[Tuple[datetime, Dict[str, MarketData]]]:
        """
        开始数据回放
        
        Yields:
            Tuple[datetime, Dict[str, MarketData]]: 时间戳和市场数据
        """
        if not self.data_cache:
            self.load_historical_data()
        
        if not self.data_cache:
            logger.error("没有可用的历史数据进行回放")
            return
        
        logger.info("开始历史数据回放...")
        
        # 获取所有时间戳的并集
        all_timestamps = set()
        for df in self.data_cache.values():
            all_timestamps.update(df.index)
        
        # 按时间排序
        sorted_timestamps = sorted(all_timestamps)
        
        for timestamp in sorted_timestamps:
            self.current_time = timestamp
            
            # 获取当前时间点的所有股票数据
            current_data = {}
            for symbol, df in self.data_cache.items():
                if timestamp in df.index:
                    row = df.loc[timestamp]
                    
                    # 构建MarketData对象
                    market_data = MarketData(
                        symbol=symbol,
                        timestamp=timestamp,
                        open=row['open'],
                        high=row['high'],
                        low=row['low'],
                        close=row['close'],
                        volume=int(row['volume']),
                        amount=row['amount'],
                        frequency=self.config.frequency
                    )
                    
                    # 添加技术指标
                    if self.config.include_indicators:
                        indicators = {}
                        for col in df.columns:
                            if col not in ['open', 'high', 'low', 'close', 'volume', 'amount', 'symbol']:
                                if not pd.isna(row[col]):
                                    indicators[col] = row[col]
                        market_data.indicators = indicators
                    
                    current_data[symbol] = market_data
            
            if current_data:
                yield timestamp, current_data
    
    def get_data_at_time(self, timestamp: datetime, symbol: Optional[str] = None) -> Dict[str, MarketData]:
        """
        获取指定时间点的数据
        
        Args:
            timestamp: 时间戳
            symbol: 股票代码，如果为None则返回所有股票
            
        Returns:
            Dict[str, MarketData]: 市场数据
        """
        result = {}
        
        symbols = [symbol] if symbol else self.config.symbols
        
        for sym in symbols:
            if sym in self.data_cache:
                df = self.data_cache[sym]
                
                # 找到最接近的时间点
                if timestamp in df.index:
                    row = df.loc[timestamp]
                elif timestamp < df.index.min():
                    continue  # 时间太早，没有数据
                else:
                    # 找到最近的历史数据点
                    valid_timestamps = df.index[df.index <= timestamp]
                    if len(valid_timestamps) == 0:
                        continue
                    nearest_timestamp = valid_timestamps.max()
                    row = df.loc[nearest_timestamp]
                
                # 构建MarketData对象
                market_data = MarketData(
                    symbol=sym,
                    timestamp=timestamp,
                    open=row['open'],
                    high=row['high'],
                    low=row['low'],
                    close=row['close'],
                    volume=int(row['volume']),
                    amount=row['amount'],
                    frequency=self.config.frequency
                )
                
                # 添加技术指标
                if self.config.include_indicators:
                    indicators = {}
                    for col in df.columns:
                        if col not in ['open', 'high', 'low', 'close', 'volume', 'amount', 'symbol']:
                            if not pd.isna(row[col]):
                                indicators[col] = row[col]
                    market_data.indicators = indicators
                
                result[sym] = market_data
        
        return result
    
    def get_current_time(self) -> Optional[datetime]:
        """获取当前回放时间"""
        return self.current_time
    
    def reset(self):
        """重置回放状态"""
        self.current_time = None
        self.replay_iterator = None
        logger.info("历史数据回放状态已重置")
    
    def get_data_summary(self) -> Dict[str, Any]:
        """获取数据摘要信息"""
        if not self.data_cache:
            return {}
        
        summary = {
            'symbols': list(self.data_cache.keys()),
            'symbol_count': len(self.data_cache),
            'date_range': {
                'start': self.config.start_date,
                'end': self.config.end_date
            },
            'frequency': self.config.frequency.value,
            'data_points': {}
        }
        
        for symbol, df in self.data_cache.items():
            summary['data_points'][symbol] = len(df)
        
        return summary


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    # 创建回放配置
    config = ReplayConfig(
        start_date=datetime(2024, 1, 1),
        end_date=datetime(2024, 1, 5),
        symbols=['000001.SZ', '000002.SZ'],
        frequency=DataFrequency.MINUTE_5,
        include_indicators=True,
        trading_hours_only=True
    )
    
    # 创建回放系统
    replay = HistoricalDataReplay(config)
    
    # 加载数据
    data = replay.load_historical_data()
    print(f"加载了 {len(data)} 只股票的数据")
    
    # 开始回放（只显示前10个数据点）
    count = 0
    for timestamp, market_data in replay.start_replay():
        print(f"时间：{timestamp}")
        for symbol, data in market_data.items():
            print(f"  {symbol}: 收盘价={data.close}, 成交量={data.volume}")
        
        count += 1
        if count >= 10:
            break
    
    # 获取数据摘要
    summary = replay.get_data_summary()
    print(f"数据摘要：{summary}")