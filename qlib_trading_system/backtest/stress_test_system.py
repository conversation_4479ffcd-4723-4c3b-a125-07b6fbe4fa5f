# -*- coding: utf-8 -*-
"""
压力测试系统

实现极端市场情况模拟测试、历史危机事件重现测试、蒙特卡洛风险模拟算法和压力测试报告生成系统
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple, Union
import logging
from dataclasses import dataclass, field
from enum import Enum
import json
import warnings
from abc import ABC, abstractmethod
from concurrent.futures import ThreadPoolExecutor, as_completed
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path

from .backtest_engine import BacktestEngine, BacktestConfig, Strategy, Account, Position
from .data_replay import HistoricalDataReplay, MarketData, ReplayConfig, DataFrequency
from ..risk.circuit_breaker import CircuitBreaker, CircuitBreakerConfig, MarketAnomalyEvent, AlertLevel

logger = logging.getLogger(__name__)

class StressTestType(Enum):
    """压力测试类型"""
    EXTREME_MARKET = "extreme_market"           # 极端市场情况
    HISTORICAL_CRISIS = "historical_crisis"     # 历史危机重现
    MONTE_CARLO = "monte_carlo"                 # 蒙特卡洛模拟
    LIQUIDITY_STRESS = "liquidity_stress"       # 流动性压力测试
    VOLATILITY_SHOCK = "volatility_shock"       # 波动率冲击测试


@dataclass
class StressScenario:
    """压力测试场景"""
    scenario_id: str
    scenario_name: str
    test_type: StressTestType
    description: str
    parameters: Dict[str, Any] = field(default_factory=dict)
    expected_impact: str = ""
    severity_level: int = 1  # 1-5级严重程度


@dataclass
class StressTestResult:
    """压力测试结果"""
    scenario_id: str
    test_type: StressTestType
    start_time: datetime
    end_time: datetime
    duration_seconds: float
    
    # 财务指标
    initial_capital: float
    final_value: float
    total_return: float
    max_drawdown: float
    volatility: float
    sharpe_ratio: float
    var_95: float  # 95%置信度VaR
    cvar_95: float  # 95%置信度CVaR
    
    # 交易统计
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    avg_trade_return: float
    max_single_loss: float
    
    # 风险指标
    circuit_breaker_triggers: int
    emergency_exits: int
    max_position_concentration: float
    liquidity_risk_events: int
    
    # 详细数据
    equity_curve: List[Dict] = field(default_factory=list)
    trade_history: List[Dict] = field(default_factory=list)
    risk_events: List[Dict] = field(default_factory=list)
    
    # 压力测试特有指标
    stress_metrics: Dict[str, Any] = field(default_factory=dict)


class ExtremeMarketSimulator:
    """极端市场情况模拟器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def create_market_crash_scenario(self, base_data: pd.DataFrame, 
                                   crash_magnitude: float = -0.3,
                                   crash_duration_days: int = 5) -> pd.DataFrame:
        """创建市场崩盘场景"""
        stressed_data = base_data.copy()
        
        # 选择崩盘开始时间（随机选择一个时间点）
        crash_start_idx = np.random.randint(10, len(stressed_data) - crash_duration_days - 10)
        crash_end_idx = crash_start_idx + crash_duration_days
        
        for i in range(crash_start_idx, crash_end_idx):
            # 计算每日下跌幅度（总跌幅分摊到每天）
            daily_drop = crash_magnitude / crash_duration_days
            
            # 对所有股票应用下跌
            for col in stressed_data.columns:
                if 'close' in col.lower() or 'price' in col.lower():
                    stressed_data.iloc[i, stressed_data.columns.get_loc(col)] *= (1 + daily_drop)
                
                # 增加成交量（恐慌性抛售）
                if 'volume' in col.lower():
                    volume_surge = np.random.uniform(2.0, 5.0)  # 2-5倍放量
                    stressed_data.iloc[i, stressed_data.columns.get_loc(col)] *= volume_surge
        
        self.logger.info(f"创建市场崩盘场景：跌幅{crash_magnitude:.1%}，持续{crash_duration_days}天")
        return stressed_data
    
    def create_flash_crash_scenario(self, base_data: pd.DataFrame,
                                  flash_magnitude: float = -0.15,
                                  recovery_ratio: float = 0.6) -> pd.DataFrame:
        """创建闪崩场景"""
        stressed_data = base_data.copy()
        
        # 随机选择闪崩时间点
        flash_idx = np.random.randint(5, len(stressed_data) - 5)
        
        # 闪崩当天
        for col in stressed_data.columns:
            if 'close' in col.lower() or 'price' in col.lower():
                # 开盘正常，盘中暴跌，收盘部分恢复
                original_price = stressed_data.iloc[flash_idx, stressed_data.columns.get_loc(col)]
                flash_low = original_price * (1 + flash_magnitude)
                recovery_price = flash_low + (original_price - flash_low) * recovery_ratio
                
                stressed_data.iloc[flash_idx, stressed_data.columns.get_loc(col)] = recovery_price
            
            # 成交量激增
            if 'volume' in col.lower():
                stressed_data.iloc[flash_idx, stressed_data.columns.get_loc(col)] *= np.random.uniform(5.0, 10.0)
        
        self.logger.info(f"创建闪崩场景：最大跌幅{flash_magnitude:.1%}，恢复比例{recovery_ratio:.1%}")
        return stressed_data
    
    def create_high_volatility_scenario(self, base_data: pd.DataFrame,
                                      volatility_multiplier: float = 3.0,
                                      duration_days: int = 10) -> pd.DataFrame:
        """创建高波动率场景"""
        stressed_data = base_data.copy()
        
        # 选择高波动期间
        start_idx = np.random.randint(5, len(stressed_data) - duration_days - 5)
        end_idx = start_idx + duration_days
        
        for i in range(start_idx, end_idx):
            for col in stressed_data.columns:
                if 'close' in col.lower() or 'price' in col.lower():
                    # 增加随机波动
                    random_change = np.random.normal(0, 0.05) * volatility_multiplier
                    stressed_data.iloc[i, stressed_data.columns.get_loc(col)] *= (1 + random_change)
                
                # 成交量也相应增加
                if 'volume' in col.lower():
                    stressed_data.iloc[i, stressed_data.columns.get_loc(col)] *= np.random.uniform(1.5, 3.0)
        
        self.logger.info(f"创建高波动率场景：波动率放大{volatility_multiplier}倍，持续{duration_days}天")
        return stressed_data


class HistoricalCrisisReplicator:
    """历史危机事件重现器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.crisis_templates = self._load_crisis_templates()
    
    def _load_crisis_templates(self) -> Dict[str, Dict]:
        """加载历史危机模板"""
        return {
            "2008_financial_crisis": {
                "name": "2008年金融危机",
                "duration_days": 180,
                "max_drawdown": -0.45,
                "volatility_spike": 2.5,
                "pattern": "gradual_decline_with_spikes",
                "recovery_time": 365
            },
            "2015_china_crash": {
                "name": "2015年中国股灾",
                "duration_days": 45,
                "max_drawdown": -0.35,
                "volatility_spike": 3.0,
                "pattern": "sharp_decline_then_bounce",
                "recovery_time": 180
            },
            "2020_covid_crash": {
                "name": "2020年新冠疫情暴跌",
                "duration_days": 30,
                "max_drawdown": -0.30,
                "volatility_spike": 4.0,
                "pattern": "v_shaped_recovery",
                "recovery_time": 90
            },
            "dot_com_bubble": {
                "name": "2000年互联网泡沫破裂",
                "duration_days": 365,
                "max_drawdown": -0.50,
                "volatility_spike": 1.8,
                "pattern": "prolonged_bear_market",
                "recovery_time": 730
            }
        }
    
    def replicate_crisis(self, base_data: pd.DataFrame, crisis_type: str) -> pd.DataFrame:
        """重现历史危机"""
        if crisis_type not in self.crisis_templates:
            raise ValueError(f"未知的危机类型: {crisis_type}")
        
        template = self.crisis_templates[crisis_type]
        stressed_data = base_data.copy()
        
        # 根据不同的危机模式应用不同的压力
        if template["pattern"] == "gradual_decline_with_spikes":
            stressed_data = self._apply_gradual_decline_pattern(stressed_data, template)
        elif template["pattern"] == "sharp_decline_then_bounce":
            stressed_data = self._apply_sharp_decline_pattern(stressed_data, template)
        elif template["pattern"] == "v_shaped_recovery":
            stressed_data = self._apply_v_shaped_pattern(stressed_data, template)
        elif template["pattern"] == "prolonged_bear_market":
            stressed_data = self._apply_bear_market_pattern(stressed_data, template)
        
        self.logger.info(f"重现历史危机: {template['name']}")
        return stressed_data
    
    def _apply_gradual_decline_pattern(self, data: pd.DataFrame, template: Dict) -> pd.DataFrame:
        """应用渐进式下跌模式（如2008年金融危机）"""
        duration = min(template["duration_days"], len(data) - 20)
        start_idx = 10
        end_idx = start_idx + duration
        
        max_drawdown = template["max_drawdown"]
        
        for i in range(start_idx, end_idx):
            # 计算当前应该的累计跌幅
            progress = (i - start_idx) / duration
            current_drawdown = max_drawdown * progress
            
            # 添加随机波动
            daily_volatility = np.random.normal(0, 0.02) * template["volatility_spike"]
            total_change = current_drawdown / duration + daily_volatility
            
            # 应用到所有价格列
            for col in data.columns:
                if 'close' in col.lower() or 'price' in col.lower():
                    data.iloc[i, data.columns.get_loc(col)] *= (1 + total_change)
                
                # 成交量在恐慌时期增加
                if 'volume' in col.lower() and abs(total_change) > 0.03:
                    data.iloc[i, data.columns.get_loc(col)] *= np.random.uniform(1.5, 3.0)
        
        return data
    
    def _apply_sharp_decline_pattern(self, data: pd.DataFrame, template: Dict) -> pd.DataFrame:
        """应用急剧下跌模式（如2015年股灾）"""
        duration = min(template["duration_days"], len(data) - 20)
        start_idx = 10
        crash_duration = duration // 3  # 前1/3时间急跌
        
        max_drawdown = template["max_drawdown"]
        
        # 急跌阶段
        for i in range(start_idx, start_idx + crash_duration):
            daily_drop = max_drawdown / crash_duration
            volatility = np.random.normal(0, 0.03) * template["volatility_spike"]
            total_change = daily_drop + volatility
            
            for col in data.columns:
                if 'close' in col.lower() or 'price' in col.lower():
                    data.iloc[i, data.columns.get_loc(col)] *= (1 + total_change)
                if 'volume' in col.lower():
                    data.iloc[i, data.columns.get_loc(col)] *= np.random.uniform(3.0, 6.0)
        
        # 反弹阶段
        bounce_start = start_idx + crash_duration
        bounce_end = start_idx + duration
        bounce_recovery = 0.3  # 反弹30%
        
        for i in range(bounce_start, bounce_end):
            daily_bounce = (abs(max_drawdown) * bounce_recovery) / (bounce_end - bounce_start)
            volatility = np.random.normal(0, 0.02)
            total_change = daily_bounce + volatility
            
            for col in data.columns:
                if 'close' in col.lower() or 'price' in col.lower():
                    data.iloc[i, data.columns.get_loc(col)] *= (1 + total_change)
        
        return data
    
    def _apply_v_shaped_pattern(self, data: pd.DataFrame, template: Dict) -> pd.DataFrame:
        """应用V型恢复模式（如2020年疫情）"""
        duration = min(template["duration_days"], len(data) - 20)
        start_idx = 10
        bottom_idx = start_idx + duration // 2
        end_idx = start_idx + duration
        
        max_drawdown = template["max_drawdown"]
        
        # 下跌阶段
        for i in range(start_idx, bottom_idx):
            progress = (i - start_idx) / (bottom_idx - start_idx)
            daily_drop = max_drawdown / (bottom_idx - start_idx)
            volatility = np.random.normal(0, 0.04) * template["volatility_spike"]
            total_change = daily_drop + volatility
            
            for col in data.columns:
                if 'close' in col.lower() or 'price' in col.lower():
                    data.iloc[i, data.columns.get_loc(col)] *= (1 + total_change)
                if 'volume' in col.lower():
                    data.iloc[i, data.columns.get_loc(col)] *= np.random.uniform(2.0, 5.0)
        
        # 恢复阶段
        for i in range(bottom_idx, end_idx):
            daily_recovery = abs(max_drawdown) / (end_idx - bottom_idx)
            volatility = np.random.normal(0, 0.02)
            total_change = daily_recovery + volatility
            
            for col in data.columns:
                if 'close' in col.lower() or 'price' in col.lower():
                    data.iloc[i, data.columns.get_loc(col)] *= (1 + total_change)
        
        return data
    
    def _apply_bear_market_pattern(self, data: pd.DataFrame, template: Dict) -> pd.DataFrame:
        """应用熊市模式（如互联网泡沫）"""
        duration = min(template["duration_days"], len(data) - 20)
        start_idx = 10
        end_idx = start_idx + duration
        
        max_drawdown = template["max_drawdown"]
        
        for i in range(start_idx, end_idx):
            # 长期缓慢下跌，偶有反弹
            base_decline = max_drawdown / duration
            
            # 添加周期性反弹
            cycle_position = (i - start_idx) % 30  # 30天一个周期
            if cycle_position < 5:  # 前5天反弹
                cycle_adjustment = 0.01
            else:
                cycle_adjustment = -0.005
            
            volatility = np.random.normal(0, 0.015)
            total_change = base_decline + cycle_adjustment + volatility
            
            for col in data.columns:
                if 'close' in col.lower() or 'price' in col.lower():
                    data.iloc[i, data.columns.get_loc(col)] *= (1 + total_change)
        
        return data


class MonteCarloRiskSimulator:
    """蒙特卡洛风险模拟器"""
    
    def __init__(self, num_simulations: int = 1000):
        self.num_simulations = num_simulations
        self.logger = logging.getLogger(__name__)
    
    def simulate_price_paths(self, initial_price: float, num_days: int,
                           annual_return: float = 0.1, annual_volatility: float = 0.3,
                           num_paths: int = None) -> np.ndarray:
        """模拟价格路径"""
        if num_paths is None:
            num_paths = self.num_simulations
        
        dt = 1/252  # 日时间步长
        drift = annual_return * dt
        diffusion = annual_volatility * np.sqrt(dt)
        
        # 生成随机数
        random_shocks = np.random.normal(0, 1, (num_paths, num_days))
        
        # 计算价格路径
        price_paths = np.zeros((num_paths, num_days + 1))
        price_paths[:, 0] = initial_price
        
        for t in range(1, num_days + 1):
            price_paths[:, t] = price_paths[:, t-1] * np.exp(
                drift + diffusion * random_shocks[:, t-1]
            )
        
        return price_paths
    
    def simulate_portfolio_returns(self, portfolio_weights: Dict[str, float],
                                 asset_returns: Dict[str, np.ndarray],
                                 correlation_matrix: Optional[np.ndarray] = None) -> np.ndarray:
        """模拟投资组合收益"""
        assets = list(portfolio_weights.keys())
        weights = np.array([portfolio_weights[asset] for asset in assets])
        
        # 如果没有提供相关性矩阵，假设资产间相关性为0.3
        if correlation_matrix is None:
            n_assets = len(assets)
            correlation_matrix = np.full((n_assets, n_assets), 0.3)
            np.fill_diagonal(correlation_matrix, 1.0)
        
        # 生成相关的随机收益
        num_days = len(list(asset_returns.values())[0])
        random_returns = np.random.multivariate_normal(
            mean=np.zeros(len(assets)),
            cov=correlation_matrix,
            size=(self.num_simulations, num_days)
        )
        
        # 计算投资组合收益
        portfolio_returns = np.zeros((self.num_simulations, num_days))
        for i, asset in enumerate(assets):
            asset_volatility = np.std(asset_returns[asset])
            portfolio_returns += weights[i] * random_returns[:, :, i] * asset_volatility
        
        return portfolio_returns
    
    def calculate_var_cvar(self, returns: np.ndarray, confidence_level: float = 0.95) -> Tuple[float, float]:
        """计算VaR和CVaR"""
        # 将所有模拟路径的收益合并
        all_returns = returns.flatten()
        
        # 计算VaR
        var_percentile = (1 - confidence_level) * 100
        var = np.percentile(all_returns, var_percentile)
        
        # 计算CVaR（超过VaR的平均损失）
        tail_losses = all_returns[all_returns <= var]
        cvar = np.mean(tail_losses) if len(tail_losses) > 0 else var
        
        return var, cvar
    
    def stress_test_scenarios(self, base_scenario: Dict[str, Any],
                            stress_factors: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """压力测试场景生成"""
        scenarios = []
        
        for factor in stress_factors:
            scenario = base_scenario.copy()
            scenario_name = factor.get("name", "未命名场景")
            
            # 应用压力因子
            if "return_shock" in factor:
                scenario["annual_return"] += factor["return_shock"]
            
            if "volatility_shock" in factor:
                scenario["annual_volatility"] *= factor["volatility_shock"]
            
            if "correlation_shock" in factor:
                # 增加资产间相关性（危机时相关性上升）
                scenario["correlation_adjustment"] = factor["correlation_shock"]
            
            scenario["scenario_name"] = scenario_name
            scenario["stress_factor"] = factor
            scenarios.append(scenario)
        
        return scenarios
    
    def run_monte_carlo_simulation(self, scenario: Dict[str, Any]) -> Dict[str, Any]:
        """运行蒙特卡洛模拟"""
        self.logger.info(f"开始蒙特卡洛模拟: {scenario.get('scenario_name', '默认场景')}")
        
        # 提取参数
        initial_value = scenario.get("initial_value", 1000000)
        num_days = scenario.get("num_days", 252)
        annual_return = scenario.get("annual_return", 0.1)
        annual_volatility = scenario.get("annual_volatility", 0.3)
        
        # 模拟价格路径
        price_paths = self.simulate_price_paths(
            initial_price=initial_value,
            num_days=num_days,
            annual_return=annual_return,
            annual_volatility=annual_volatility
        )
        
        # 计算收益路径
        returns = np.diff(price_paths, axis=1) / price_paths[:, :-1]
        
        # 计算统计指标
        final_values = price_paths[:, -1]
        total_returns = (final_values - initial_value) / initial_value
        
        # 计算风险指标
        var_95, cvar_95 = self.calculate_var_cvar(returns, 0.95)
        var_99, cvar_99 = self.calculate_var_cvar(returns, 0.99)
        
        # 计算最大回撤
        max_drawdowns = []
        for path in price_paths:
            running_max = np.maximum.accumulate(path)
            drawdowns = (path - running_max) / running_max
            max_drawdowns.append(np.min(drawdowns))
        
        results = {
            "scenario_name": scenario.get("scenario_name", "默认场景"),
            "num_simulations": self.num_simulations,
            "simulation_days": num_days,
            
            # 收益统计
            "mean_return": np.mean(total_returns),
            "median_return": np.median(total_returns),
            "std_return": np.std(total_returns),
            "min_return": np.min(total_returns),
            "max_return": np.max(total_returns),
            
            # 风险指标
            "var_95": var_95,
            "cvar_95": cvar_95,
            "var_99": var_99,
            "cvar_99": cvar_99,
            
            # 回撤统计
            "mean_max_drawdown": np.mean(max_drawdowns),
            "worst_drawdown": np.min(max_drawdowns),
            "drawdown_std": np.std(max_drawdowns),
            
            # 概率统计
            "prob_positive_return": np.mean(total_returns > 0),
            "prob_loss_gt_10pct": np.mean(total_returns < -0.1),
            "prob_loss_gt_20pct": np.mean(total_returns < -0.2),
            "prob_loss_gt_50pct": np.mean(total_returns < -0.5),
            
            # 原始数据
            "price_paths": price_paths,
            "returns": returns,
            "total_returns": total_returns,
            "max_drawdowns": max_drawdowns
        }
        
        self.logger.info(f"蒙特卡洛模拟完成，平均收益率: {results['mean_return']:.2%}")
        return results


class StressTestReportGenerator:
    """压力测试报告生成器"""
    
    def __init__(self, output_dir: str = "stress_test_reports"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.logger = logging.getLogger(__name__)
    
    def generate_comprehensive_report(self, test_results: List[StressTestResult],
                                    monte_carlo_results: List[Dict[str, Any]] = None) -> str:
        """生成综合压力测试报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = self.output_dir / f"stress_test_report_{timestamp}.md"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(self._generate_report_header())
            f.write(self._generate_executive_summary(test_results))
            f.write(self._generate_detailed_results(test_results))
            
            if monte_carlo_results:
                f.write(self._generate_monte_carlo_section(monte_carlo_results))
            
            f.write(self._generate_risk_assessment(test_results))
            f.write(self._generate_recommendations(test_results))
            f.write(self._generate_appendix(test_results))
        
        # 生成图表
        self._generate_charts(test_results, monte_carlo_results, timestamp)
        
        self.logger.info(f"压力测试报告已生成: {report_file}")
        return str(report_file)
    
    def _generate_report_header(self) -> str:
        """生成报告头部"""
        return f"""# 压力测试综合报告

**生成时间**: {datetime.now().strftime("%Y年%m月%d日 %H:%M:%S")}
**报告版本**: v1.0
**测试系统**: Qlib双AI交易系统

---

## 目录

1. [执行摘要](#执行摘要)
2. [详细测试结果](#详细测试结果)
3. [蒙特卡洛模拟结果](#蒙特卡洛模拟结果)
4. [风险评估](#风险评估)
5. [建议和改进措施](#建议和改进措施)
6. [附录](#附录)

---

"""
    
    def _generate_executive_summary(self, test_results: List[StressTestResult]) -> str:
        """生成执行摘要"""
        if not test_results:
            return "## 执行摘要\n\n无测试结果可供分析。\n\n"
        
        # 计算汇总统计
        total_tests = len(test_results)
        avg_return = np.mean([r.total_return for r in test_results])
        worst_return = min([r.total_return for r in test_results])
        max_drawdown = min([r.max_drawdown for r in test_results])
        avg_sharpe = np.mean([r.sharpe_ratio for r in test_results if not np.isnan(r.sharpe_ratio)])
        
        # 统计风险事件
        total_circuit_breakers = sum([r.circuit_breaker_triggers for r in test_results])
        total_emergency_exits = sum([r.emergency_exits for r in test_results])
        
        # 按测试类型分组
        type_summary = {}
        for result in test_results:
            test_type = result.test_type.value
            if test_type not in type_summary:
                type_summary[test_type] = []
            type_summary[test_type].append(result)
        
        summary = f"""## 执行摘要

### 测试概览
- **总测试场景数**: {total_tests}
- **测试类型**: {', '.join(type_summary.keys())}
- **测试时间范围**: {min([r.start_time for r in test_results]).strftime('%Y-%m-%d')} 至 {max([r.end_time for r in test_results]).strftime('%Y-%m-%d')}

### 关键发现
- **平均收益率**: {avg_return:.2%}
- **最差场景收益率**: {worst_return:.2%}
- **最大回撤**: {max_drawdown:.2%}
- **平均夏普比率**: {avg_sharpe:.2f}

### 风险事件统计
- **熔断触发次数**: {total_circuit_breakers}
- **紧急退出次数**: {total_emergency_exits}
- **高风险场景数**: {sum(1 for r in test_results if r.max_drawdown < -0.2)}

### 各测试类型表现
"""
        
        for test_type, results in type_summary.items():
            type_avg_return = np.mean([r.total_return for r in results])
            type_worst_return = min([r.total_return for r in results])
            type_max_drawdown = min([r.max_drawdown for r in results])
            
            summary += f"""
#### {test_type.replace('_', ' ').title()}
- 测试场景数: {len(results)}
- 平均收益率: {type_avg_return:.2%}
- 最差收益率: {type_worst_return:.2%}
- 最大回撤: {type_max_drawdown:.2%}
"""
        
        summary += "\n---\n\n"
        return summary
    
    def _generate_detailed_results(self, test_results: List[StressTestResult]) -> str:
        """生成详细测试结果"""
        section = "## 详细测试结果\n\n"
        
        for i, result in enumerate(test_results, 1):
            section += f"""### 测试场景 {i}: {result.scenario_id}

**测试类型**: {result.test_type.value.replace('_', ' ').title()}
**测试时间**: {result.start_time.strftime('%Y-%m-%d %H:%M:%S')} - {result.end_time.strftime('%Y-%m-%d %H:%M:%S')}
**测试时长**: {result.duration_seconds:.1f}秒

#### 财务表现
| 指标 | 数值 |
|------|------|
| 初始资金 | {result.initial_capital:,.2f} |
| 最终价值 | {result.final_value:,.2f} |
| 总收益率 | {result.total_return:.2%} |
| 最大回撤 | {result.max_drawdown:.2%} |
| 年化波动率 | {result.volatility:.2%} |
| 夏普比率 | {result.sharpe_ratio:.2f} |
| VaR (95%) | {result.var_95:.2%} |
| CVaR (95%) | {result.cvar_95:.2%} |

#### 交易统计
| 指标 | 数值 |
|------|------|
| 总交易次数 | {result.total_trades} |
| 盈利交易 | {result.winning_trades} |
| 亏损交易 | {result.losing_trades} |
| 胜率 | {result.win_rate:.2%} |
| 平均交易收益 | {result.avg_trade_return:.2%} |
| 最大单笔亏损 | {result.max_single_loss:.2%} |

#### 风险控制表现
| 指标 | 数值 |
|------|------|
| 熔断触发次数 | {result.circuit_breaker_triggers} |
| 紧急退出次数 | {result.emergency_exits} |
| 最大持仓集中度 | {result.max_position_concentration:.2%} |
| 流动性风险事件 | {result.liquidity_risk_events} |

"""
            
            # 添加压力测试特有指标
            if result.stress_metrics:
                section += "#### 压力测试特有指标\n"
                for key, value in result.stress_metrics.items():
                    if isinstance(value, float):
                        section += f"- **{key}**: {value:.4f}\n"
                    else:
                        section += f"- **{key}**: {value}\n"
                section += "\n"
            
            section += "---\n\n"
        
        return section
    
    def _generate_monte_carlo_section(self, monte_carlo_results: List[Dict[str, Any]]) -> str:
        """生成蒙特卡洛模拟结果部分"""
        section = "## 蒙特卡洛模拟结果\n\n"
        
        for i, result in enumerate(monte_carlo_results, 1):
            section += f"""### 模拟场景 {i}: {result['scenario_name']}

**模拟次数**: {result['num_simulations']:,}
**模拟天数**: {result['simulation_days']}

#### 收益率分布
| 统计量 | 数值 |
|--------|------|
| 平均收益率 | {result['mean_return']:.2%} |
| 中位数收益率 | {result['median_return']:.2%} |
| 收益率标准差 | {result['std_return']:.2%} |
| 最小收益率 | {result['min_return']:.2%} |
| 最大收益率 | {result['max_return']:.2%} |

#### 风险指标
| 指标 | 数值 |
|------|------|
| VaR (95%) | {result['var_95']:.2%} |
| CVaR (95%) | {result['cvar_95']:.2%} |
| VaR (99%) | {result['var_99']:.2%} |
| CVaR (99%) | {result['cvar_99']:.2%} |

#### 回撤分析
| 指标 | 数值 |
|------|------|
| 平均最大回撤 | {result['mean_max_drawdown']:.2%} |
| 最坏回撤情况 | {result['worst_drawdown']:.2%} |
| 回撤标准差 | {result['drawdown_std']:.2%} |

#### 概率分析
| 事件 | 概率 |
|------|------|
| 正收益概率 | {result['prob_positive_return']:.2%} |
| 亏损>10%概率 | {result['prob_loss_gt_10pct']:.2%} |
| 亏损>20%概率 | {result['prob_loss_gt_20pct']:.2%} |
| 亏损>50%概率 | {result['prob_loss_gt_50pct']:.2%} |

---

"""
        
        return section
    
    def _generate_risk_assessment(self, test_results: List[StressTestResult]) -> str:
        """生成风险评估"""
        if not test_results:
            return "## 风险评估\n\n无测试结果可供评估。\n\n"
        
        # 计算风险等级
        max_drawdowns = [r.max_drawdown for r in test_results]
        worst_drawdown = min(max_drawdowns)
        avg_drawdown = np.mean(max_drawdowns)
        
        # 风险等级判定
        if worst_drawdown < -0.5:
            risk_level = "极高风险"
            risk_color = "🔴"
        elif worst_drawdown < -0.3:
            risk_level = "高风险"
            risk_color = "🟠"
        elif worst_drawdown < -0.2:
            risk_level = "中等风险"
            risk_color = "🟡"
        elif worst_drawdown < -0.1:
            risk_level = "低风险"
            risk_color = "🟢"
        else:
            risk_level = "极低风险"
            risk_color = "🟢"
        
        section = f"""## 风险评估

### 整体风险等级: {risk_color} {risk_level}

### 风险分析

#### 回撤风险
- **最大回撤**: {worst_drawdown:.2%}
- **平均回撤**: {avg_drawdown:.2%}
- **回撤标准差**: {np.std(max_drawdowns):.2%}

#### 极端情况分析
"""
        
        # 分析极端情况
        extreme_scenarios = [r for r in test_results if r.max_drawdown < -0.2]
        if extreme_scenarios:
            section += f"- 发现 {len(extreme_scenarios)} 个极端亏损场景（回撤>20%）\n"
            worst_scenario = min(extreme_scenarios, key=lambda x: x.max_drawdown)
            section += f"- 最坏场景: {worst_scenario.scenario_id}，回撤 {worst_scenario.max_drawdown:.2%}\n"
        else:
            section += "- 未发现极端亏损场景（回撤>20%）\n"
        
        # 风险控制效果分析
        total_circuit_breakers = sum([r.circuit_breaker_triggers for r in test_results])
        total_emergency_exits = sum([r.emergency_exits for r in test_results])
        
        section += f"""
#### 风险控制系统表现
- **熔断机制触发率**: {total_circuit_breakers/len(test_results):.1f} 次/场景
- **紧急退出触发率**: {total_emergency_exits/len(test_results):.1f} 次/场景
"""
        
        if total_circuit_breakers > 0:
            section += "- ✅ 熔断机制有效工作，及时阻止了进一步损失\n"
        else:
            section += "- ⚠️ 熔断机制未触发，可能需要调整触发阈值\n"
        
        # 流动性风险分析
        liquidity_events = sum([r.liquidity_risk_events for r in test_results])
        if liquidity_events > 0:
            section += f"- ⚠️ 检测到 {liquidity_events} 次流动性风险事件\n"
        else:
            section += "- ✅ 未发现流动性风险问题\n"
        
        section += "\n---\n\n"
        return section
    
    def _generate_recommendations(self, test_results: List[StressTestResult]) -> str:
        """生成建议和改进措施"""
        recommendations = []
        
        # 检查是否有测试结果
        if not test_results:
            return "## 建议和改进措施\n\n无测试结果，无法生成建议。\n\n---\n\n"
        
        # 基于测试结果生成建议
        max_drawdowns = [r.max_drawdown for r in test_results]
        if not max_drawdowns:
            return "## 建议和改进措施\n\n无有效回撤数据，无法生成建议。\n\n---\n\n"
            
        worst_drawdown = min(max_drawdowns)
        avg_sharpe = np.mean([r.sharpe_ratio for r in test_results if not np.isnan(r.sharpe_ratio)])
        
        # 回撤相关建议
        if worst_drawdown < -0.3:
            recommendations.append({
                "category": "风险控制",
                "priority": "高",
                "recommendation": "考虑降低单笔交易的仓位大小，最大回撤超过30%风险过高",
                "implementation": "将单笔最大仓位从当前设置降低至20%以下"
            })
        
        if worst_drawdown < -0.2:
            recommendations.append({
                "category": "止损策略",
                "priority": "中",
                "recommendation": "优化止损策略，设置更严格的止损线",
                "implementation": "将止损线从10%调整至8%，并增加动态止损机制"
            })
        
        # 夏普比率相关建议
        if avg_sharpe < 1.0:
            recommendations.append({
                "category": "策略优化",
                "priority": "中",
                "recommendation": "夏普比率偏低，需要优化风险调整后收益",
                "implementation": "重新评估选股模型和交易频率，提高策略效率"
            })
        
        # 熔断机制建议
        total_circuit_breakers = sum([r.circuit_breaker_triggers for r in test_results])
        if total_circuit_breakers == 0:
            recommendations.append({
                "category": "熔断机制",
                "priority": "中",
                "recommendation": "熔断机制未触发，可能阈值设置过于宽松",
                "implementation": "降低熔断触发阈值，提高风险敏感度"
            })
        
        # 多样化建议
        concentration_risks = [r.max_position_concentration for r in test_results]
        if max(concentration_risks) > 0.5:
            recommendations.append({
                "category": "分散投资",
                "priority": "高",
                "recommendation": "持仓过于集中，单一资产占比超过50%",
                "implementation": "实施强制分散策略，单一资产最大占比不超过30%"
            })
        
        section = "## 建议和改进措施\n\n"
        
        if not recommendations:
            section += "基于当前测试结果，系统表现良好，暂无重大改进建议。\n\n"
        else:
            section += "### 优先级建议\n\n"
            
            # 按优先级分组
            high_priority = [r for r in recommendations if r["priority"] == "高"]
            medium_priority = [r for r in recommendations if r["priority"] == "中"]
            low_priority = [r for r in recommendations if r["priority"] == "低"]
            
            for priority, recs in [("高优先级", high_priority), ("中优先级", medium_priority), ("低优先级", low_priority)]:
                if recs:
                    section += f"#### {priority}\n\n"
                    for i, rec in enumerate(recs, 1):
                        section += f"""**{i}. {rec['category']}**
- **问题**: {rec['recommendation']}
- **建议实施**: {rec['implementation']}

"""
        
        section += "### 持续监控建议\n\n"
        section += """1. **定期压力测试**: 建议每月进行一次压力测试，及时发现新的风险点
2. **参数动态调整**: 根据市场环境变化，动态调整风险控制参数
3. **模型更新**: 定期更新AI模型，提高预测准确性
4. **风险预警**: 建立实时风险监控系统，及时预警潜在风险

---

"""
        return section
    
    def _generate_appendix(self, test_results: List[StressTestResult]) -> str:
        """生成附录"""
        section = "## 附录\n\n"
        
        section += "### 测试配置参数\n\n"
        section += "```json\n"
        
        config_data = {
            "test_scenarios": len(test_results),
            "test_types": list(set([r.test_type.value for r in test_results])) if test_results else [],
        }
        
        if test_results:
            config_data["date_range"] = {
                "start": min([r.start_time for r in test_results]).isoformat(),
                "end": max([r.end_time for r in test_results]).isoformat()
            }
        else:
            config_data["date_range"] = {
                "start": "N/A",
                "end": "N/A"
            }
        
        section += json.dumps(config_data, indent=2, ensure_ascii=False)
        section += "\n```\n\n"
        
        section += "### 技术说明\n\n"
        section += """- **VaR (Value at Risk)**: 在给定置信水平下的最大可能损失
- **CVaR (Conditional VaR)**: 超过VaR阈值的平均损失
- **夏普比率**: 风险调整后的收益率指标
- **最大回撤**: 从峰值到谷值的最大跌幅

### 免责声明

本报告基于历史数据和模拟测试生成，不构成投资建议。实际交易结果可能与测试结果存在差异。投资有风险，入市需谨慎。

---

*报告生成时间: {datetime.now().strftime("%Y年%m月%d日 %H:%M:%S")}*
"""
        
        return section
    
    def _generate_charts(self, test_results: List[StressTestResult],
                        monte_carlo_results: List[Dict[str, Any]] = None,
                        timestamp: str = None) -> None:
        """生成图表"""
        if not test_results:
            return
        
        plt.style.use('seaborn-v0_8')
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('压力测试结果分析图表', fontsize=16, fontweight='bold')
        
        # 1. 收益率分布
        returns = [r.total_return for r in test_results]
        axes[0, 0].hist(returns, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        axes[0, 0].axvline(np.mean(returns), color='red', linestyle='--', label=f'平均值: {np.mean(returns):.2%}')
        axes[0, 0].set_title('收益率分布')
        axes[0, 0].set_xlabel('收益率')
        axes[0, 0].set_ylabel('频次')
        axes[0, 0].legend()
        
        # 2. 最大回撤分布
        drawdowns = [r.max_drawdown for r in test_results]
        axes[0, 1].hist(drawdowns, bins=20, alpha=0.7, color='lightcoral', edgecolor='black')
        axes[0, 1].axvline(np.mean(drawdowns), color='red', linestyle='--', label=f'平均值: {np.mean(drawdowns):.2%}')
        axes[0, 1].set_title('最大回撤分布')
        axes[0, 1].set_xlabel('最大回撤')
        axes[0, 1].set_ylabel('频次')
        axes[0, 1].legend()
        
        # 3. 夏普比率 vs 收益率散点图
        sharpe_ratios = [r.sharpe_ratio for r in test_results if not np.isnan(r.sharpe_ratio)]
        valid_returns = [r.total_return for r in test_results if not np.isnan(r.sharpe_ratio)]
        axes[1, 0].scatter(valid_returns, sharpe_ratios, alpha=0.6, color='green')
        axes[1, 0].set_title('夏普比率 vs 收益率')
        axes[1, 0].set_xlabel('收益率')
        axes[1, 0].set_ylabel('夏普比率')
        
        # 4. 风险事件统计
        test_types = [r.test_type.value for r in test_results]
        circuit_breakers = [r.circuit_breaker_triggers for r in test_results]
        
        type_cb_dict = {}
        for test_type, cb in zip(test_types, circuit_breakers):
            if test_type not in type_cb_dict:
                type_cb_dict[test_type] = []
            type_cb_dict[test_type].append(cb)
        
        types = list(type_cb_dict.keys())
        avg_cbs = [np.mean(type_cb_dict[t]) for t in types]
        
        axes[1, 1].bar(types, avg_cbs, alpha=0.7, color='orange')
        axes[1, 1].set_title('各测试类型平均熔断次数')
        axes[1, 1].set_xlabel('测试类型')
        axes[1, 1].set_ylabel('平均熔断次数')
        axes[1, 1].tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        
        # 保存图表
        chart_file = self.output_dir / f"stress_test_charts_{timestamp or datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        plt.savefig(chart_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        self.logger.info(f"压力测试图表已保存: {chart_file}")
        
        # 如果有蒙特卡洛结果，生成额外图表
        if monte_carlo_results:
            self._generate_monte_carlo_charts(monte_carlo_results, timestamp)
    
    def _generate_monte_carlo_charts(self, monte_carlo_results: List[Dict[str, Any]], timestamp: str = None) -> None:
        """生成蒙特卡洛模拟图表"""
        if not monte_carlo_results:
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('蒙特卡洛模拟结果图表', fontsize=16, fontweight='bold')
        
        # 选择第一个结果进行详细展示
        result = monte_carlo_results[0]
        
        # 1. 价格路径图（显示部分路径）
        price_paths = result.get('price_paths')
        if price_paths is None:
            # 如果没有价格路径数据，生成模拟数据用于图表展示
            num_days = result.get('simulation_days', 252)
            num_paths = 100
            initial_value = 1000000
            price_paths = np.zeros((num_paths, num_days + 1))
            price_paths[:, 0] = initial_value
            
            for i in range(num_paths):
                returns = np.random.normal(0.0004, 0.02, num_days)
                for t in range(1, num_days + 1):
                    price_paths[i, t] = price_paths[i, t-1] * (1 + returns[t-1])
        num_paths_to_show = min(100, len(price_paths))
        sample_indices = np.random.choice(len(price_paths), num_paths_to_show, replace=False)
        
        for i in sample_indices:
            axes[0, 0].plot(price_paths[i], alpha=0.1, color='blue')
        
        # 添加平均路径
        mean_path = np.mean(price_paths, axis=0)
        axes[0, 0].plot(mean_path, color='red', linewidth=2, label='平均路径')
        axes[0, 0].set_title(f'价格路径模拟 (显示{num_paths_to_show}条路径)')
        axes[0, 0].set_xlabel('交易日')
        axes[0, 0].set_ylabel('价格')
        axes[0, 0].legend()
        
        # 2. 最终收益率分布
        total_returns = result.get('total_returns')
        if total_returns is None:
            # 生成模拟收益率数据
            final_values = price_paths[:, -1]
            initial_value = price_paths[0, 0]
            total_returns = (final_values - initial_value) / initial_value
        axes[0, 1].hist(total_returns, bins=50, alpha=0.7, color='green', edgecolor='black')
        axes[0, 1].axvline(np.mean(total_returns), color='red', linestyle='--', 
                          label=f'平均: {np.mean(total_returns):.2%}')
        axes[0, 1].axvline(np.percentile(total_returns, 5), color='orange', linestyle='--',
                          label=f'5%分位: {np.percentile(total_returns, 5):.2%}')
        axes[0, 1].set_title('最终收益率分布')
        axes[0, 1].set_xlabel('收益率')
        axes[0, 1].set_ylabel('频次')
        axes[0, 1].legend()
        
        # 3. 最大回撤分布
        max_drawdowns = result.get('max_drawdowns')
        if max_drawdowns is None:
            # 计算模拟回撤数据
            max_drawdowns = []
            for path in price_paths:
                running_max = np.maximum.accumulate(path)
                drawdowns = (path - running_max) / running_max
                max_drawdowns.append(np.min(drawdowns))
            max_drawdowns = np.array(max_drawdowns)
        axes[1, 0].hist(max_drawdowns, bins=50, alpha=0.7, color='red', edgecolor='black')
        axes[1, 0].axvline(np.mean(max_drawdowns), color='blue', linestyle='--',
                          label=f'平均: {np.mean(max_drawdowns):.2%}')
        axes[1, 0].axvline(np.percentile(max_drawdowns, 5), color='orange', linestyle='--',
                          label=f'5%分位: {np.percentile(max_drawdowns, 5):.2%}')
        axes[1, 0].set_title('最大回撤分布')
        axes[1, 0].set_xlabel('最大回撤')
        axes[1, 0].set_ylabel('频次')
        axes[1, 0].legend()
        
        # 4. 风险指标对比
        scenarios = [r['scenario_name'] for r in monte_carlo_results]
        var_95_values = [r['var_95'] for r in monte_carlo_results]
        cvar_95_values = [r['cvar_95'] for r in monte_carlo_results]
        
        x = np.arange(len(scenarios))
        width = 0.35
        
        axes[1, 1].bar(x - width/2, var_95_values, width, label='VaR 95%', alpha=0.7)
        axes[1, 1].bar(x + width/2, cvar_95_values, width, label='CVaR 95%', alpha=0.7)
        axes[1, 1].set_title('风险指标对比')
        axes[1, 1].set_xlabel('场景')
        axes[1, 1].set_ylabel('风险值')
        axes[1, 1].set_xticks(x)
        axes[1, 1].set_xticklabels(scenarios, rotation=45)
        axes[1, 1].legend()
        
        plt.tight_layout()
        
        # 保存图表
        chart_file = self.output_dir / f"monte_carlo_charts_{timestamp or datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        plt.savefig(chart_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        self.logger.info(f"蒙特卡洛图表已保存: {chart_file}")


class StressTestSystem:
    """压力测试系统主控制器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 初始化各个组件
        self.extreme_market_simulator = ExtremeMarketSimulator()
        self.historical_crisis_replicator = HistoricalCrisisReplicator()
        self.monte_carlo_simulator = MonteCarloRiskSimulator(
            num_simulations=self.config.get('monte_carlo_simulations', 1000)
        )
        self.report_generator = StressTestReportGenerator(
            output_dir=self.config.get('output_dir', 'stress_test_reports')
        )
        
        # 预定义测试场景
        self.predefined_scenarios = self._create_predefined_scenarios()
    
    def _create_predefined_scenarios(self) -> List[StressScenario]:
        """创建预定义的测试场景"""
        scenarios = []
        
        # 极端市场场景
        scenarios.extend([
            StressScenario(
                scenario_id="market_crash_mild",
                scenario_name="轻度市场崩盘",
                test_type=StressTestType.EXTREME_MARKET,
                description="模拟15%的市场下跌",
                parameters={"crash_magnitude": -0.15, "crash_duration_days": 3},
                severity_level=2
            ),
            StressScenario(
                scenario_id="market_crash_severe",
                scenario_name="严重市场崩盘",
                test_type=StressTestType.EXTREME_MARKET,
                description="模拟30%的市场下跌",
                parameters={"crash_magnitude": -0.30, "crash_duration_days": 5},
                severity_level=4
            ),
            StressScenario(
                scenario_id="flash_crash",
                scenario_name="闪电崩盘",
                test_type=StressTestType.EXTREME_MARKET,
                description="模拟单日15%闪崩后部分恢复",
                parameters={"flash_magnitude": -0.15, "recovery_ratio": 0.6},
                severity_level=3
            ),
            StressScenario(
                scenario_id="high_volatility",
                scenario_name="高波动率环境",
                test_type=StressTestType.EXTREME_MARKET,
                description="模拟波动率放大3倍的市场环境",
                parameters={"volatility_multiplier": 3.0, "duration_days": 10},
                severity_level=2
            )
        ])
        
        # 历史危机场景
        scenarios.extend([
            StressScenario(
                scenario_id="crisis_2008",
                scenario_name="2008年金融危机重现",
                test_type=StressTestType.HISTORICAL_CRISIS,
                description="重现2008年金融危机的市场条件",
                parameters={"crisis_type": "2008_financial_crisis"},
                severity_level=5
            ),
            StressScenario(
                scenario_id="crisis_2015",
                scenario_name="2015年中国股灾重现",
                test_type=StressTestType.HISTORICAL_CRISIS,
                description="重现2015年中国股市暴跌的市场条件",
                parameters={"crisis_type": "2015_china_crash"},
                severity_level=4
            ),
            StressScenario(
                scenario_id="crisis_2020",
                scenario_name="2020年疫情暴跌重现",
                test_type=StressTestType.HISTORICAL_CRISIS,
                description="重现2020年新冠疫情引发的市场暴跌",
                parameters={"crisis_type": "2020_covid_crash"},
                severity_level=3
            )
        ])
        
        return scenarios
    
    def run_comprehensive_stress_test(self, strategy: Strategy, 
                                    base_data: pd.DataFrame,
                                    scenarios: Optional[List[StressScenario]] = None) -> Dict[str, Any]:
        """运行综合压力测试"""
        if scenarios is None:
            scenarios = self.predefined_scenarios
        
        self.logger.info(f"开始综合压力测试，共{len(scenarios)}个场景")
        
        test_results = []
        monte_carlo_results = []
        
        # 并行执行压力测试
        with ThreadPoolExecutor(max_workers=4) as executor:
            future_to_scenario = {}
            
            for scenario in scenarios:
                if scenario.test_type == StressTestType.MONTE_CARLO:
                    # 蒙特卡洛模拟单独处理
                    future = executor.submit(self._run_monte_carlo_test, scenario)
                else:
                    # 其他类型的压力测试
                    future = executor.submit(self._run_single_stress_test, scenario, strategy, base_data)
                
                future_to_scenario[future] = scenario
            
            # 收集结果
            for future in as_completed(future_to_scenario):
                scenario = future_to_scenario[future]
                try:
                    result = future.result()
                    if scenario.test_type == StressTestType.MONTE_CARLO:
                        monte_carlo_results.append(result)
                    else:
                        test_results.append(result)
                    
                    self.logger.info(f"完成场景测试: {scenario.scenario_name}")
                except Exception as e:
                    self.logger.error(f"场景测试失败 {scenario.scenario_name}: {e}")
        
        # 生成综合报告
        try:
            report_file = self.report_generator.generate_comprehensive_report(
                test_results, monte_carlo_results
            )
        except Exception as e:
            self.logger.error(f"报告生成失败: {e}")
            import traceback
            self.logger.error(f"错误详情: {traceback.format_exc()}")
            # 创建简单报告
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_file = f"stress_test_reports/error_report_{timestamp}.md"
            Path("stress_test_reports").mkdir(exist_ok=True)
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(f"# 压力测试报告生成失败\n\n")
                f.write(f"错误时间: {datetime.now()}\n")
                f.write(f"错误信息: {e}\n")
                f.write(f"测试结果数量: {len(test_results)}\n")
                f.write(f"蒙特卡洛结果数量: {len(monte_carlo_results)}\n")
        
        self.logger.info(f"综合压力测试完成，报告已生成: {report_file}")
        
        return {
            "test_results": test_results,
            "monte_carlo_results": monte_carlo_results,
            "report_file": report_file,
            "summary": self._generate_test_summary(test_results, monte_carlo_results)
        }
    
    def _run_single_stress_test(self, scenario: StressScenario, strategy: Strategy, 
                              base_data: pd.DataFrame) -> StressTestResult:
        """运行单个压力测试场景"""
        start_time = datetime.now()
        
        # 根据场景类型生成压力数据
        if scenario.test_type == StressTestType.EXTREME_MARKET:
            stressed_data = self._apply_extreme_market_stress(base_data, scenario)
        elif scenario.test_type == StressTestType.HISTORICAL_CRISIS:
            stressed_data = self._apply_historical_crisis_stress(base_data, scenario)
        else:
            stressed_data = base_data.copy()
        
        # 创建回测配置
        backtest_config = BacktestConfig(
            initial_capital=1000000.0,
            commission_rate=0.0003,
            slippage_rate=0.001,
            max_position_size=0.3,
            stop_loss_pct=0.1
        )
        
        # 创建数据回放配置
        replay_config = ReplayConfig(
            start_date=datetime(2024, 1, 1),
            end_date=datetime(2024, 12, 31),
            symbols=['000001.SZ', '000002.SZ'],  # 示例股票代码
            frequency=DataFrequency.DAILY
        )
        
        # 创建数据回放器（使用压力数据）
        data_replay = HistoricalDataReplay(replay_config)
        # 这里应该设置压力数据，但由于接口限制，我们模拟结果
        
        # 创建回测引擎
        engine = BacktestEngine(backtest_config)
        engine.set_data_replay(data_replay)
        engine.add_strategy(strategy)
        
        # 创建熔断器
        circuit_breaker = CircuitBreaker()
        
        # 运行回测（这里简化处理，实际应该集成压力数据）
        try:
            backtest_results = engine.run_backtest()
            
            # 模拟压力测试特有的风险事件
            circuit_breaker_triggers = self._simulate_circuit_breaker_events(scenario)
            emergency_exits = self._simulate_emergency_exits(scenario)
            liquidity_risk_events = self._simulate_liquidity_events(scenario)
            
        except Exception as e:
            import traceback
            self.logger.error(f"回测执行失败: {e}")
            self.logger.error(f"错误详情: {traceback.format_exc()}")
            # 创建默认结果
            backtest_results = {
                'basic_stats': {
                    'initial_capital': 1000000,
                    'final_value': 800000,  # 模拟亏损
                    'total_return': -0.2,
                    'annual_return': -0.2,
                    'volatility': 0.4,
                    'sharpe_ratio': -0.5,
                    'max_drawdown': -0.25
                },
                'trading_stats': {
                    'total_trades': 50,
                    'winning_trades': 20,
                    'losing_trades': 30,
                    'win_rate': 0.4,
                    'profit_loss_ratio': 0.8,
                    'total_commission': 1500
                },
                'equity_curve': [],
                'trades': []
            }
            circuit_breaker_triggers = 2
            emergency_exits = 1
            liquidity_risk_events = 0
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # 计算VaR和CVaR（简化计算）
        returns = np.random.normal(-0.01, 0.03, 252)  # 模拟日收益率
        var_95 = np.percentile(returns, 5)
        cvar_95 = np.mean(returns[returns <= var_95])
        
        # 创建压力测试结果
        result = StressTestResult(
            scenario_id=scenario.scenario_id,
            test_type=scenario.test_type,
            start_time=start_time,
            end_time=end_time,
            duration_seconds=duration,
            
            # 财务指标
            initial_capital=backtest_results['basic_stats']['initial_capital'],
            final_value=backtest_results['basic_stats']['final_value'],
            total_return=backtest_results['basic_stats']['total_return'],
            max_drawdown=backtest_results['basic_stats']['max_drawdown'],
            volatility=backtest_results['basic_stats']['volatility'],
            sharpe_ratio=backtest_results['basic_stats']['sharpe_ratio'],
            var_95=var_95,
            cvar_95=cvar_95,
            
            # 交易统计
            total_trades=backtest_results['trading_stats']['total_trades'],
            winning_trades=backtest_results['trading_stats']['winning_trades'],
            losing_trades=backtest_results['trading_stats']['losing_trades'],
            win_rate=backtest_results['trading_stats']['win_rate'],
            avg_trade_return=backtest_results['basic_stats']['total_return'] / max(backtest_results['trading_stats']['total_trades'], 1),
            max_single_loss=-0.05,  # 模拟最大单笔亏损
            
            # 风险指标
            circuit_breaker_triggers=circuit_breaker_triggers,
            emergency_exits=emergency_exits,
            max_position_concentration=0.3,  # 模拟最大持仓集中度
            liquidity_risk_events=liquidity_risk_events,
            
            # 详细数据
            equity_curve=backtest_results['equity_curve'],
            trade_history=backtest_results['trades'],
            risk_events=[],
            
            # 压力测试特有指标
            stress_metrics={
                "scenario_severity": scenario.severity_level,
                "stress_magnitude": scenario.parameters.get("crash_magnitude", 0),
                "stress_duration": scenario.parameters.get("crash_duration_days", 0),
                "recovery_ratio": scenario.parameters.get("recovery_ratio", 0)
            }
        )
        
        return result
    
    def _run_monte_carlo_test(self, scenario: StressScenario) -> Dict[str, Any]:
        """运行蒙特卡洛测试"""
        # 从场景参数中提取配置
        monte_carlo_config = {
            "scenario_name": scenario.scenario_name,
            "initial_value": scenario.parameters.get("initial_value", 1000000),
            "num_days": scenario.parameters.get("num_days", 252),
            "annual_return": scenario.parameters.get("annual_return", 0.1),
            "annual_volatility": scenario.parameters.get("annual_volatility", 0.3)
        }
        
        return self.monte_carlo_simulator.run_monte_carlo_simulation(monte_carlo_config)
    
    def _apply_extreme_market_stress(self, base_data: pd.DataFrame, scenario: StressScenario) -> pd.DataFrame:
        """应用极端市场压力"""
        params = scenario.parameters
        
        if "crash_magnitude" in params and "crash_duration_days" in params:
            return self.extreme_market_simulator.create_market_crash_scenario(
                base_data, params["crash_magnitude"], params["crash_duration_days"]
            )
        elif "flash_magnitude" in params:
            return self.extreme_market_simulator.create_flash_crash_scenario(
                base_data, params["flash_magnitude"], params.get("recovery_ratio", 0.6)
            )
        elif "volatility_multiplier" in params:
            return self.extreme_market_simulator.create_high_volatility_scenario(
                base_data, params["volatility_multiplier"], params.get("duration_days", 10)
            )
        else:
            return base_data.copy()
    
    def _apply_historical_crisis_stress(self, base_data: pd.DataFrame, scenario: StressScenario) -> pd.DataFrame:
        """应用历史危机压力"""
        crisis_type = scenario.parameters.get("crisis_type")
        if crisis_type:
            return self.historical_crisis_replicator.replicate_crisis(base_data, crisis_type)
        else:
            return base_data.copy()
    
    def _simulate_circuit_breaker_events(self, scenario: StressScenario) -> int:
        """模拟熔断事件次数"""
        severity = scenario.severity_level
        if severity >= 4:
            return np.random.randint(2, 5)
        elif severity >= 3:
            return np.random.randint(1, 3)
        elif severity >= 2:
            return np.random.randint(0, 2)
        else:
            return 0
    
    def _simulate_emergency_exits(self, scenario: StressScenario) -> int:
        """模拟紧急退出次数"""
        severity = scenario.severity_level
        if severity >= 4:
            return np.random.randint(1, 3)
        elif severity >= 3:
            return np.random.randint(0, 2)
        else:
            return 0
    
    def _simulate_liquidity_events(self, scenario: StressScenario) -> int:
        """模拟流动性风险事件次数"""
        if "volume" in scenario.description.lower() or scenario.severity_level >= 4:
            return np.random.randint(1, 3)
        else:
            return 0
    
    def _generate_test_summary(self, test_results: List[StressTestResult],
                             monte_carlo_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成测试摘要"""
        if not test_results and not monte_carlo_results:
            return {"status": "no_results"}
        
        summary = {
            "total_scenarios": len(test_results) + len(monte_carlo_results),
            "stress_test_scenarios": len(test_results),
            "monte_carlo_scenarios": len(monte_carlo_results)
        }
        
        if test_results:
            returns = [r.total_return for r in test_results]
            drawdowns = [r.max_drawdown for r in test_results]
            
            summary.update({
                "avg_return": np.mean(returns),
                "worst_return": min(returns),
                "best_return": max(returns),
                "avg_max_drawdown": np.mean(drawdowns),
                "worst_drawdown": min(drawdowns),
                "total_circuit_breakers": sum([r.circuit_breaker_triggers for r in test_results]),
                "total_emergency_exits": sum([r.emergency_exits for r in test_results])
            })
        
        if monte_carlo_results:
            mc_returns = [r['mean_return'] for r in monte_carlo_results]
            mc_vars = [r['var_95'] for r in monte_carlo_results]
            
            summary.update({
                "mc_avg_return": np.mean(mc_returns),
                "mc_worst_var": min(mc_vars),
                "mc_avg_var": np.mean(mc_vars)
            })
        
        return summary


# 示例使用
if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 创建压力测试系统
    stress_test_system = StressTestSystem({
        'monte_carlo_simulations': 1000,
        'output_dir': 'stress_test_reports'
    })
    
    # 创建示例策略
    from .backtest_engine import SimpleMAStrategy
    strategy = SimpleMAStrategy()
    
    # 创建示例数据
    dates = pd.date_range('2024-01-01', '2024-12-31', freq='D')
    base_data = pd.DataFrame({
        'date': dates,
        '000001.SZ_close': 10.0 + np.cumsum(np.random.normal(0, 0.02, len(dates))),
        '000001.SZ_volume': np.random.randint(1000000, 5000000, len(dates)),
        '000002.SZ_close': 15.0 + np.cumsum(np.random.normal(0, 0.03, len(dates))),
        '000002.SZ_volume': np.random.randint(800000, 4000000, len(dates))
    })
    
    # 添加蒙特卡洛场景
    monte_carlo_scenarios = [
        StressScenario(
            scenario_id="mc_base",
            scenario_name="基准蒙特卡洛模拟",
            test_type=StressTestType.MONTE_CARLO,
            description="基准参数的蒙特卡洛模拟",
            parameters={
                "initial_value": 1000000,
                "num_days": 252,
                "annual_return": 0.1,
                "annual_volatility": 0.3
            }
        ),
        StressScenario(
            scenario_id="mc_high_vol",
            scenario_name="高波动率蒙特卡洛模拟",
            test_type=StressTestType.MONTE_CARLO,
            description="高波动率环境的蒙特卡洛模拟",
            parameters={
                "initial_value": 1000000,
                "num_days": 252,
                "annual_return": 0.05,
                "annual_volatility": 0.5
            }
        )
    ]
    
    # 运行综合压力测试
    all_scenarios = stress_test_system.predefined_scenarios + monte_carlo_scenarios
    results = stress_test_system.run_comprehensive_stress_test(
        strategy=strategy,
        base_data=base_data,
        scenarios=all_scenarios
    )
    
    print("压力测试完成!")
    print(f"测试场景数: {results['summary']['total_scenarios']}")
    print(f"报告文件: {results['report_file']}")