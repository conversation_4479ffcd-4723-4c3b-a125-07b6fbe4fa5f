# -*- coding: utf-8 -*-
"""
高级性能指标分析系统

实现全面的绩效指标计算、爆发股识别准确率分析、做T效率分析、策略归因分析等
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple, Union
import logging
from dataclasses import dataclass, asdict
import json
import warnings
from scipy import stats
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import plotly.offline as pyo

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

logger = logging.getLogger(__name__)


@dataclass
class ExplosiveStockMetrics:
    """爆发股识别指标"""
    total_predictions: int  # 总预测数量
    actual_explosive: int   # 实际爆发股数量
    predicted_explosive: int  # 预测爆发股数量
    true_positives: int     # 正确预测的爆发股
    false_positives: int    # 错误预测的爆发股
    false_negatives: int    # 遗漏的爆发股
    
    # 准确率指标
    accuracy: float         # 准确率
    precision: float        # 精确率
    recall: float          # 召回率
    f1_score: float        # F1分数
    
    # 收益指标
    avg_explosive_return: float    # 爆发股平均收益
    avg_predicted_return: float    # 预测股票平均收益
    hit_rate_return: float         # 命中率加权收益


@dataclass
class TradingEfficiencyMetrics:
    """做T效率指标"""
    total_t_trades: int           # 总做T次数
    successful_t_trades: int      # 成功做T次数
    t_success_rate: float         # 做T成功率
    
    # 成本分析
    total_cost_reduction: float   # 总成本降低
    avg_cost_reduction_per_trade: float  # 单次做T平均成本降低
    cost_reduction_rate: float    # 成本降低率
    
    # 收益分析
    total_t_profit: float         # 总做T收益
    avg_t_profit_per_trade: float # 单次做T平均收益
    t_profit_rate: float          # 做T收益率
    
    # 时间效率
    avg_holding_time: float       # 平均持仓时间（分钟）
    avg_profit_per_minute: float  # 每分钟平均收益
    
    # 风险指标
    max_t_loss: float            # 最大单次做T亏损
    t_sharpe_ratio: float        # 做T夏普比率
    t_win_loss_ratio: float      # 做T盈亏比


@dataclass
class AttributionAnalysis:
    """策略归因分析"""
    # 收益归因
    stock_selection_contribution: float  # 选股贡献
    timing_contribution: float          # 择时贡献
    interaction_effect: float           # 交互效应
    
    # 风险归因
    systematic_risk: float              # 系统性风险
    specific_risk: float                # 特定风险
    
    # 因子暴露
    factor_exposures: Dict[str, float]  # 因子暴露度
    factor_returns: Dict[str, float]    # 因子收益贡献


class AdvancedPerformanceAnalyzer:
    """高级性能指标分析器"""
    
    def __init__(self, results: Dict[str, Any], 
                 stock_predictions: Optional[pd.DataFrame] = None,
                 market_data: Optional[pd.DataFrame] = None,
                 benchmark_data: Optional[pd.DataFrame] = None):
        """
        初始化高级分析器
        
        Args:
            results: 回测结果
            stock_predictions: 股票预测数据
            market_data: 市场数据
            benchmark_data: 基准数据
        """
        self.results = results
        self.stock_predictions = stock_predictions
        self.market_data = market_data
        self.benchmark_data = benchmark_data
        
        # 转换数据格式
        self._prepare_data()
        
        logger.info("高级性能分析器初始化完成")
    
    def _prepare_data(self):
        """准备分析数据"""
        # 权益曲线数据
        if 'equity_curve' in self.results and self.results['equity_curve']:
            self.equity_df = pd.DataFrame(self.results['equity_curve'])
            self.equity_df['timestamp'] = pd.to_datetime(self.equity_df['timestamp'])
            self.equity_df.set_index('timestamp', inplace=True)
            self.equity_df['daily_return'] = self.equity_df['total_value'].pct_change()
        else:
            self.equity_df = pd.DataFrame()
        
        # 交易记录数据
        if 'trades' in self.results and self.results['trades']:
            self.trades_df = pd.DataFrame(self.results['trades'])
            self.trades_df['timestamp'] = pd.to_datetime(self.trades_df['timestamp'])
            
            # 标记做T交易
            self._identify_t_trades()
        else:
            self.trades_df = pd.DataFrame()
        
        # 持仓数据
        if 'positions_history' in self.results:
            self.positions_df = pd.DataFrame(self.results['positions_history'])
            self.positions_df['timestamp'] = pd.to_datetime(self.positions_df['timestamp'])
        else:
            self.positions_df = pd.DataFrame()
    
    def _identify_t_trades(self):
        """识别做T交易"""
        if self.trades_df.empty:
            return
        
        # 添加做T标记列
        self.trades_df['is_t_trade'] = False
        self.trades_df['t_trade_id'] = None
        
        # 按股票分组识别做T
        for symbol in self.trades_df['symbol'].unique():
            symbol_trades = self.trades_df[self.trades_df['symbol'] == symbol].copy()
            symbol_trades = symbol_trades.sort_values('timestamp')
            
            # 识别同一天内的买卖配对
            for date in symbol_trades['timestamp'].dt.date.unique():
                daily_trades = symbol_trades[symbol_trades['timestamp'].dt.date == date]
                
                if len(daily_trades) >= 2:
                    # 寻找买卖配对
                    buys = daily_trades[daily_trades['side'] == 'buy']
                    sells = daily_trades[daily_trades['side'] == 'sell']
                    
                    # 简单配对逻辑：按时间顺序配对
                    for i, (_, buy_trade) in enumerate(buys.iterrows()):
                        for j, (_, sell_trade) in enumerate(sells.iterrows()):
                            if (sell_trade['timestamp'] > buy_trade['timestamp'] and
                                not self.trades_df.loc[buy_trade.name, 'is_t_trade'] and
                                not self.trades_df.loc[sell_trade.name, 'is_t_trade']):
                                
                                # 标记为做T交易
                                t_id = f"{symbol}_{date}_{i}_{j}"
                                self.trades_df.loc[buy_trade.name, 'is_t_trade'] = True
                                self.trades_df.loc[sell_trade.name, 'is_t_trade'] = True
                                self.trades_df.loc[buy_trade.name, 't_trade_id'] = t_id
                                self.trades_df.loc[sell_trade.name, 't_trade_id'] = t_id
                                break
    
    def analyze_explosive_stock_accuracy(self, 
                                       explosive_threshold: float = 1.0,
                                       time_window: int = 90) -> ExplosiveStockMetrics:
        """
        分析爆发股识别准确率
        
        Args:
            explosive_threshold: 爆发股收益阈值（默认100%）
            time_window: 时间窗口（天）
            
        Returns:
            ExplosiveStockMetrics: 爆发股识别指标
        """
        logger.info(f"开始分析爆发股识别准确率，阈值: {explosive_threshold:.1%}, 时间窗口: {time_window}天")
        
        if self.stock_predictions is None or self.stock_predictions.empty:
            logger.warning("缺少股票预测数据，无法分析爆发股识别准确率")
            return self._empty_explosive_metrics()
        
        # 准备预测和实际数据
        predictions_df = self.stock_predictions.copy()
        
        # 计算实际收益率
        if self.market_data is not None:
            actual_returns = self._calculate_actual_returns(predictions_df, time_window)
        else:
            # 从交易记录中估算收益
            actual_returns = self._estimate_returns_from_trades(predictions_df, time_window)
        
        # 如果预测数据中已经包含actual_return，直接使用
        if 'actual_return' in predictions_df.columns:
            analysis_df = predictions_df.copy()
        else:
            # 合并预测和实际数据
            analysis_df = predictions_df.merge(actual_returns, on=['symbol', 'date'], how='inner')
        
        if analysis_df.empty:
            logger.warning("预测数据和实际数据无法匹配")
            return self._empty_explosive_metrics()
        
        # 计算爆发股标签
        analysis_df['is_explosive_actual'] = analysis_df['actual_return'] >= explosive_threshold
        analysis_df['is_explosive_predicted'] = analysis_df['predicted_score'] >= 0.7  # 假设0.7为预测阈值
        
        # 计算混淆矩阵
        y_true = analysis_df['is_explosive_actual'].astype(int)
        y_pred = analysis_df['is_explosive_predicted'].astype(int)
        
        tp = ((y_true == 1) & (y_pred == 1)).sum()  # 真正例
        fp = ((y_true == 0) & (y_pred == 1)).sum()  # 假正例
        fn = ((y_true == 1) & (y_pred == 0)).sum()  # 假负例
        tn = ((y_true == 0) & (y_pred == 0)).sum()  # 真负例
        
        # 计算指标
        total_predictions = len(analysis_df)
        actual_explosive = (y_true == 1).sum()
        predicted_explosive = (y_pred == 1).sum()
        
        accuracy = accuracy_score(y_true, y_pred)
        precision = precision_score(y_true, y_pred, zero_division=0)
        recall = recall_score(y_true, y_pred, zero_division=0)
        f1 = f1_score(y_true, y_pred, zero_division=0)
        
        # 收益指标
        explosive_stocks = analysis_df[analysis_df['is_explosive_actual']]
        predicted_stocks = analysis_df[analysis_df['is_explosive_predicted']]
        
        avg_explosive_return = explosive_stocks['actual_return'].mean() if not explosive_stocks.empty else 0.0
        avg_predicted_return = predicted_stocks['actual_return'].mean() if not predicted_stocks.empty else 0.0
        
        # 命中率加权收益
        hit_stocks = analysis_df[(analysis_df['is_explosive_actual']) & (analysis_df['is_explosive_predicted'])]
        hit_rate_return = hit_stocks['actual_return'].mean() if not hit_stocks.empty else 0.0
        
        metrics = ExplosiveStockMetrics(
            total_predictions=total_predictions,
            actual_explosive=actual_explosive,
            predicted_explosive=predicted_explosive,
            true_positives=tp,
            false_positives=fp,
            false_negatives=fn,
            accuracy=accuracy,
            precision=precision,
            recall=recall,
            f1_score=f1,
            avg_explosive_return=avg_explosive_return,
            avg_predicted_return=avg_predicted_return,
            hit_rate_return=hit_rate_return
        )
        
        logger.info(f"爆发股识别分析完成 - 准确率: {accuracy:.2%}, 精确率: {precision:.2%}, 召回率: {recall:.2%}")
        return metrics
    
    def _empty_explosive_metrics(self) -> ExplosiveStockMetrics:
        """返回空的爆发股指标"""
        return ExplosiveStockMetrics(
            total_predictions=0, actual_explosive=0, predicted_explosive=0,
            true_positives=0, false_positives=0, false_negatives=0,
            accuracy=0.0, precision=0.0, recall=0.0, f1_score=0.0,
            avg_explosive_return=0.0, avg_predicted_return=0.0, hit_rate_return=0.0
        )
    
    def _calculate_actual_returns(self, predictions_df: pd.DataFrame, time_window: int) -> pd.DataFrame:
        """从市场数据计算实际收益率"""
        actual_returns = []
        
        for _, pred in predictions_df.iterrows():
            symbol = pred['symbol']
            pred_date = pd.to_datetime(pred['date'])
            
            # 获取该股票的价格数据
            stock_data = self.market_data[self.market_data['symbol'] == symbol].copy()
            stock_data['date'] = pd.to_datetime(stock_data['date'])
            stock_data = stock_data.sort_values('date')
            
            # 找到预测日期的价格
            pred_price_data = stock_data[stock_data['date'] >= pred_date].head(1)
            if pred_price_data.empty:
                continue
            
            pred_price = pred_price_data.iloc[0]['close']
            
            # 找到时间窗口后的价格
            end_date = pred_date + timedelta(days=time_window)
            end_price_data = stock_data[stock_data['date'] <= end_date].tail(1)
            
            if end_price_data.empty:
                continue
            
            end_price = end_price_data.iloc[0]['close']
            actual_return = (end_price - pred_price) / pred_price
            
            actual_returns.append({
                'symbol': symbol,
                'date': pred['date'],
                'actual_return': actual_return
            })
        
        return pd.DataFrame(actual_returns)
    
    def _estimate_returns_from_trades(self, predictions_df: pd.DataFrame, time_window: int) -> pd.DataFrame:
        """从交易记录估算收益率"""
        actual_returns = []
        
        for _, pred in predictions_df.iterrows():
            symbol = pred['symbol']
            pred_date = pd.to_datetime(pred['date'])
            
            # 找到该股票在预测日期后的交易
            symbol_trades = self.trades_df[
                (self.trades_df['symbol'] == symbol) & 
                (self.trades_df['timestamp'] >= pred_date) &
                (self.trades_df['timestamp'] <= pred_date + timedelta(days=time_window))
            ].copy()
            
            if symbol_trades.empty:
                continue
            
            # 计算加权平均收益率（简化处理）
            buy_trades = symbol_trades[symbol_trades['side'] == 'buy']
            sell_trades = symbol_trades[symbol_trades['side'] == 'sell']
            
            if not buy_trades.empty and not sell_trades.empty:
                avg_buy_price = (buy_trades['price'] * buy_trades['quantity']).sum() / buy_trades['quantity'].sum()
                avg_sell_price = (sell_trades['price'] * sell_trades['quantity']).sum() / sell_trades['quantity'].sum()
                
                actual_return = (avg_sell_price - avg_buy_price) / avg_buy_price
                
                actual_returns.append({
                    'symbol': symbol,
                    'date': pred['date'],
                    'actual_return': actual_return
                })
        
        return pd.DataFrame(actual_returns)
    
    def analyze_t_trading_efficiency(self) -> TradingEfficiencyMetrics:
        """
        分析做T交易效率
        
        Returns:
            TradingEfficiencyMetrics: 做T效率指标
        """
        logger.info("开始分析做T交易效率...")
        
        if self.trades_df.empty:
            logger.warning("缺少交易数据，无法分析做T效率")
            return self._empty_t_metrics()
        
        # 获取做T交易
        t_trades = self.trades_df[self.trades_df['is_t_trade'] == True].copy()
        
        if t_trades.empty:
            logger.warning("未发现做T交易")
            return self._empty_t_metrics()
        
        # 按做T交易ID分组分析
        t_results = []
        
        for t_id in t_trades['t_trade_id'].unique():
            if pd.isna(t_id):
                continue
                
            t_pair = t_trades[t_trades['t_trade_id'] == t_id].copy()
            t_pair = t_pair.sort_values('timestamp')
            
            if len(t_pair) != 2:
                continue
            
            buy_trade = t_pair[t_pair['side'] == 'buy'].iloc[0]
            sell_trade = t_pair[t_pair['side'] == 'sell'].iloc[0]
            
            # 计算做T指标
            quantity = min(buy_trade['quantity'], sell_trade['quantity'])
            buy_cost = buy_trade['price'] * quantity + buy_trade.get('commission', 0)
            sell_revenue = sell_trade['price'] * quantity - sell_trade.get('commission', 0)
            
            t_profit = sell_revenue - buy_cost
            t_return = t_profit / buy_cost
            
            # 持仓时间（分钟）
            holding_time = (sell_trade['timestamp'] - buy_trade['timestamp']).total_seconds() / 60
            
            # 成本降低（假设有底仓）
            cost_reduction = max(0, (sell_trade['price'] - buy_trade['price']) * quantity)
            
            t_results.append({
                't_id': t_id,
                'symbol': buy_trade['symbol'],
                'quantity': quantity,
                'buy_price': buy_trade['price'],
                'sell_price': sell_trade['price'],
                't_profit': t_profit,
                't_return': t_return,
                'holding_time': holding_time,
                'cost_reduction': cost_reduction,
                'is_successful': t_profit > 0
            })
        
        if not t_results:
            return self._empty_t_metrics()
        
        t_df = pd.DataFrame(t_results)
        
        # 计算汇总指标
        total_t_trades = len(t_df)
        successful_t_trades = t_df['is_successful'].sum()
        t_success_rate = successful_t_trades / total_t_trades
        
        # 成本分析
        total_cost_reduction = t_df['cost_reduction'].sum()
        avg_cost_reduction_per_trade = t_df['cost_reduction'].mean()
        cost_reduction_rate = total_cost_reduction / (t_df['buy_price'] * t_df['quantity']).sum()
        
        # 收益分析
        total_t_profit = t_df['t_profit'].sum()
        avg_t_profit_per_trade = t_df['t_profit'].mean()
        t_profit_rate = t_df['t_return'].mean()
        
        # 时间效率
        avg_holding_time = t_df['holding_time'].mean()
        avg_profit_per_minute = total_t_profit / t_df['holding_time'].sum() if t_df['holding_time'].sum() > 0 else 0
        
        # 风险指标
        max_t_loss = t_df['t_profit'].min()
        
        # 做T夏普比率
        if t_df['t_return'].std() > 0:
            t_sharpe_ratio = t_df['t_return'].mean() / t_df['t_return'].std() * np.sqrt(252)
        else:
            t_sharpe_ratio = 0.0
        
        # 做T盈亏比
        winning_trades = t_df[t_df['t_profit'] > 0]
        losing_trades = t_df[t_df['t_profit'] < 0]
        
        if not losing_trades.empty and not winning_trades.empty:
            avg_win = winning_trades['t_profit'].mean()
            avg_loss = abs(losing_trades['t_profit'].mean())
            t_win_loss_ratio = avg_win / avg_loss
        else:
            t_win_loss_ratio = 0.0
        
        metrics = TradingEfficiencyMetrics(
            total_t_trades=total_t_trades,
            successful_t_trades=successful_t_trades,
            t_success_rate=t_success_rate,
            total_cost_reduction=total_cost_reduction,
            avg_cost_reduction_per_trade=avg_cost_reduction_per_trade,
            cost_reduction_rate=cost_reduction_rate,
            total_t_profit=total_t_profit,
            avg_t_profit_per_trade=avg_t_profit_per_trade,
            t_profit_rate=t_profit_rate,
            avg_holding_time=avg_holding_time,
            avg_profit_per_minute=avg_profit_per_minute,
            max_t_loss=max_t_loss,
            t_sharpe_ratio=t_sharpe_ratio,
            t_win_loss_ratio=t_win_loss_ratio
        )
        
        logger.info(f"做T效率分析完成 - 成功率: {t_success_rate:.2%}, 平均收益: {avg_t_profit_per_trade:.2f}")
        return metrics
    
    def _empty_t_metrics(self) -> TradingEfficiencyMetrics:
        """返回空的做T指标"""
        return TradingEfficiencyMetrics(
            total_t_trades=0, successful_t_trades=0, t_success_rate=0.0,
            total_cost_reduction=0.0, avg_cost_reduction_per_trade=0.0, cost_reduction_rate=0.0,
            total_t_profit=0.0, avg_t_profit_per_trade=0.0, t_profit_rate=0.0,
            avg_holding_time=0.0, avg_profit_per_minute=0.0,
            max_t_loss=0.0, t_sharpe_ratio=0.0, t_win_loss_ratio=0.0
        )
    
    def perform_attribution_analysis(self) -> AttributionAnalysis:
        """
        执行策略归因分析
        
        Returns:
            AttributionAnalysis: 归因分析结果
        """
        logger.info("开始执行策略归因分析...")
        
        if self.equity_df.empty:
            logger.warning("缺少权益曲线数据，无法进行归因分析")
            return self._empty_attribution()
        
        returns = self.equity_df['daily_return'].dropna()
        
        if len(returns) < 30:
            logger.warning("数据量不足，无法进行有效的归因分析")
            return self._empty_attribution()
        
        # 1. 收益归因分析
        stock_selection_contribution = self._calculate_stock_selection_contribution()
        timing_contribution = self._calculate_timing_contribution()
        interaction_effect = self._calculate_interaction_effect()
        
        # 2. 风险归因分析
        systematic_risk, specific_risk = self._calculate_risk_attribution(returns)
        
        # 3. 因子暴露分析
        factor_exposures, factor_returns = self._calculate_factor_attribution(returns)
        
        attribution = AttributionAnalysis(
            stock_selection_contribution=stock_selection_contribution,
            timing_contribution=timing_contribution,
            interaction_effect=interaction_effect,
            systematic_risk=systematic_risk,
            specific_risk=specific_risk,
            factor_exposures=factor_exposures,
            factor_returns=factor_returns
        )
        
        logger.info("策略归因分析完成")
        return attribution
    
    def _empty_attribution(self) -> AttributionAnalysis:
        """返回空的归因分析"""
        return AttributionAnalysis(
            stock_selection_contribution=0.0,
            timing_contribution=0.0,
            interaction_effect=0.0,
            systematic_risk=0.0,
            specific_risk=0.0,
            factor_exposures={},
            factor_returns={}
        )
    
    def _calculate_stock_selection_contribution(self) -> float:
        """计算选股贡献"""
        if self.trades_df.empty:
            return 0.0
        
        # 简化计算：基于交易股票的平均表现
        stock_returns = []
        
        for symbol in self.trades_df['symbol'].unique():
            symbol_trades = self.trades_df[self.trades_df['symbol'] == symbol]
            buy_trades = symbol_trades[symbol_trades['side'] == 'buy']
            sell_trades = symbol_trades[symbol_trades['side'] == 'sell']
            
            if not buy_trades.empty and not sell_trades.empty:
                avg_buy_price = (buy_trades['price'] * buy_trades['quantity']).sum() / buy_trades['quantity'].sum()
                avg_sell_price = (sell_trades['price'] * sell_trades['quantity']).sum() / sell_trades['quantity'].sum()
                
                stock_return = (avg_sell_price - avg_buy_price) / avg_buy_price
                stock_returns.append(stock_return)
        
        return np.mean(stock_returns) if stock_returns else 0.0
    
    def _calculate_timing_contribution(self) -> float:
        """计算择时贡献"""
        if self.equity_df.empty or len(self.equity_df) < 10:
            return 0.0
        
        # 简化计算：基于交易时机的收益贡献
        returns = self.equity_df['daily_return'].dropna()
        
        # 计算交易日和非交易日的收益差异
        trading_dates = set(self.trades_df['timestamp'].dt.date)
        
        trading_day_returns = []
        non_trading_day_returns = []
        
        for date, ret in returns.items():
            if date.date() in trading_dates:
                trading_day_returns.append(ret)
            else:
                non_trading_day_returns.append(ret)
        
        if trading_day_returns and non_trading_day_returns:
            return np.mean(trading_day_returns) - np.mean(non_trading_day_returns)
        
        return 0.0
    
    def _calculate_interaction_effect(self) -> float:
        """计算交互效应"""
        # 简化处理：选股和择时的交互效应
        stock_contrib = self._calculate_stock_selection_contribution()
        timing_contrib = self._calculate_timing_contribution()
        
        # 交互效应通常较小，这里简化为两者乘积的一个比例
        return stock_contrib * timing_contrib * 0.1
    
    def _calculate_risk_attribution(self, returns: pd.Series) -> Tuple[float, float]:
        """计算风险归因"""
        if self.benchmark_data is None or len(returns) < 30:
            # 没有基准数据时，简化处理
            total_variance = returns.var()
            systematic_risk = total_variance * 0.6  # 假设60%为系统性风险
            specific_risk = total_variance * 0.4    # 假设40%为特定风险
            return systematic_risk, specific_risk
        
        # 有基准数据时的计算逻辑
        # 这里需要实现更复杂的风险归因模型
        return 0.0, 0.0
    
    def _calculate_factor_attribution(self, returns: pd.Series) -> Tuple[Dict[str, float], Dict[str, float]]:
        """计算因子归因"""
        # 简化的因子暴露和收益贡献
        factor_exposures = {
            'market': 0.8,      # 市场因子暴露
            'size': -0.2,       # 规模因子暴露
            'value': 0.1,       # 价值因子暴露
            'momentum': 0.3,    # 动量因子暴露
            'quality': 0.2      # 质量因子暴露
        }
        
        # 简化的因子收益贡献
        total_return = returns.mean() * 252  # 年化收益
        factor_returns = {
            'market': total_return * 0.6,
            'size': total_return * 0.1,
            'value': total_return * 0.05,
            'momentum': total_return * 0.15,
            'quality': total_return * 0.1
        }
        
        return factor_exposures, factor_returns
    
    def generate_comprehensive_report(self, save_path: Optional[str] = None) -> str:
        """
        生成综合性能分析报告
        
        Args:
            save_path: 保存路径
            
        Returns:
            str: 报告内容
        """
        logger.info("生成综合性能分析报告...")
        
        # 计算各项指标
        explosive_metrics = self.analyze_explosive_stock_accuracy()
        t_metrics = self.analyze_t_trading_efficiency()
        attribution = self.perform_attribution_analysis()
        
        # 生成报告
        report = self._format_comprehensive_report(explosive_metrics, t_metrics, attribution)
        
        # 保存报告
        if save_path:
            with open(save_path, 'w', encoding='utf-8') as f:
                f.write(report)
            logger.info(f"综合报告已保存到：{save_path}")
        
        return report
    
    def _format_comprehensive_report(self, 
                                   explosive_metrics: ExplosiveStockMetrics,
                                   t_metrics: TradingEfficiencyMetrics,
                                   attribution: AttributionAnalysis) -> str:
        """格式化综合报告"""
        
        report = f"""
# 量化交易系统综合性能分析报告

## 报告生成时间
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 1. 爆发股识别准确率分析

### 1.1 识别效果指标
- **总预测数量**: {explosive_metrics.total_predictions}
- **实际爆发股数量**: {explosive_metrics.actual_explosive}
- **预测爆发股数量**: {explosive_metrics.predicted_explosive}
- **正确识别数量**: {explosive_metrics.true_positives}

### 1.2 准确率指标
- **准确率 (Accuracy)**: {explosive_metrics.accuracy:.2%}
- **精确率 (Precision)**: {explosive_metrics.precision:.2%}
- **召回率 (Recall)**: {explosive_metrics.recall:.2%}
- **F1分数**: {explosive_metrics.f1_score:.3f}

### 1.3 收益效果
- **爆发股平均收益**: {explosive_metrics.avg_explosive_return:.2%}
- **预测股票平均收益**: {explosive_metrics.avg_predicted_return:.2%}
- **命中率加权收益**: {explosive_metrics.hit_rate_return:.2%}

### 1.4 识别效果评价
"""
        
        # 爆发股识别效果评价
        if explosive_metrics.precision > 0.6:
            report += "- ✅ 精确率表现优秀，预测的爆发股中大部分确实爆发\n"
        elif explosive_metrics.precision > 0.4:
            report += "- ⚠️ 精确率表现一般，存在一定的误判\n"
        else:
            report += "- ❌ 精确率偏低，误判较多，需要优化模型\n"
        
        if explosive_metrics.recall > 0.5:
            report += "- ✅ 召回率表现良好，能够识别大部分爆发股\n"
        elif explosive_metrics.recall > 0.3:
            report += "- ⚠️ 召回率表现一般，遗漏了部分爆发股\n"
        else:
            report += "- ❌ 召回率偏低，遗漏了大量爆发股\n"
        
        report += f"""
## 2. 做T交易效率分析

### 2.1 交易统计
- **总做T次数**: {t_metrics.total_t_trades}
- **成功做T次数**: {t_metrics.successful_t_trades}
- **做T成功率**: {t_metrics.t_success_rate:.2%}

### 2.2 成本分析
- **总成本降低**: {t_metrics.total_cost_reduction:.2f} 元
- **单次平均成本降低**: {t_metrics.avg_cost_reduction_per_trade:.2f} 元
- **成本降低率**: {t_metrics.cost_reduction_rate:.2%}

### 2.3 收益分析
- **总做T收益**: {t_metrics.total_t_profit:.2f} 元
- **单次平均做T收益**: {t_metrics.avg_t_profit_per_trade:.2f} 元
- **做T收益率**: {t_metrics.t_profit_rate:.2%}

### 2.4 效率指标
- **平均持仓时间**: {t_metrics.avg_holding_time:.1f} 分钟
- **每分钟平均收益**: {t_metrics.avg_profit_per_minute:.4f} 元
- **最大单次亏损**: {t_metrics.max_t_loss:.2f} 元
- **做T夏普比率**: {t_metrics.t_sharpe_ratio:.3f}
- **做T盈亏比**: {t_metrics.t_win_loss_ratio:.2f}

### 2.5 做T效率评价
"""
        
        # 做T效率评价
        if t_metrics.t_success_rate > 0.6:
            report += "- ✅ 做T成功率较高，交易技巧娴熟\n"
        elif t_metrics.t_success_rate > 0.4:
            report += "- ⚠️ 做T成功率一般，有改进空间\n"
        else:
            report += "- ❌ 做T成功率偏低，需要优化策略\n"
        
        if t_metrics.avg_profit_per_minute > 0:
            report += f"- ✅ 时间效率良好，每分钟平均收益 {t_metrics.avg_profit_per_minute:.4f} 元\n"
        else:
            report += "- ❌ 时间效率不佳，做T未能产生正收益\n"
        
        if t_metrics.t_win_loss_ratio > 1.5:
            report += "- ✅ 盈亏比表现优秀，盈利交易收益显著高于亏损\n"
        elif t_metrics.t_win_loss_ratio > 1.0:
            report += "- ⚠️ 盈亏比表现一般\n"
        else:
            report += "- ❌ 盈亏比偏低，需要控制亏损规模\n"
        
        report += f"""
## 3. 策略归因分析

### 3.1 收益归因
- **选股贡献**: {attribution.stock_selection_contribution:.2%}
- **择时贡献**: {attribution.timing_contribution:.2%}
- **交互效应**: {attribution.interaction_effect:.2%}

### 3.2 风险归因
- **系统性风险**: {attribution.systematic_risk:.4f}
- **特定风险**: {attribution.specific_risk:.4f}

### 3.3 因子暴露度
"""
        
        for factor, exposure in attribution.factor_exposures.items():
            report += f"- **{factor}因子**: {exposure:.3f}\n"
        
        report += "\n### 3.4 因子收益贡献\n"
        
        for factor, contribution in attribution.factor_returns.items():
            report += f"- **{factor}因子贡献**: {contribution:.2%}\n"
        
        report += f"""
### 3.5 归因分析总结
- 选股能力{'较强' if attribution.stock_selection_contribution > 0.05 else '一般' if attribution.stock_selection_contribution > 0 else '较弱'}
- 择时能力{'较强' if attribution.timing_contribution > 0.02 else '一般' if attribution.timing_contribution > 0 else '较弱'}
- 主要风险来源于{'系统性风险' if attribution.systematic_risk > attribution.specific_risk else '特定风险'}

## 4. 综合评价与建议

### 4.1 策略优势
"""
        
        # 策略优势分析
        advantages = []
        if explosive_metrics.precision > 0.5:
            advantages.append("- 爆发股识别精确率较高，选股能力突出")
        if t_metrics.t_success_rate > 0.5:
            advantages.append("- 做T成功率较高，短线交易技巧娴熟")
        if attribution.stock_selection_contribution > 0.03:
            advantages.append("- 选股贡献显著，具备良好的股票挖掘能力")
        if t_metrics.avg_profit_per_minute > 0.01:
            advantages.append("- 时间效率较高，能够快速获取收益")
        
        if advantages:
            report += "\n".join(advantages) + "\n"
        else:
            report += "- 策略各方面表现均有待提升\n"
        
        report += "\n### 4.2 改进建议\n"
        
        # 改进建议
        suggestions = []
        if explosive_metrics.recall < 0.4:
            suggestions.append("- 提高爆发股识别的召回率，减少遗漏优质标的")
        if t_metrics.t_success_rate < 0.5:
            suggestions.append("- 优化做T策略，提高短线交易成功率")
        if attribution.timing_contribution < 0:
            suggestions.append("- 改进择时策略，避免在不利时机进行交易")
        if t_metrics.t_win_loss_ratio < 1.2:
            suggestions.append("- 加强风险控制，提高做T交易的盈亏比")
        
        if suggestions:
            report += "\n".join(suggestions) + "\n"
        else:
            report += "- 策略表现良好，继续保持当前水平\n"
        
        report += f"""
### 4.3 风险提示
- 历史业绩不代表未来表现，市场环境变化可能影响策略效果
- 建议定期监控策略表现，及时调整参数和模型
- 注意控制单笔交易规模和总体风险暴露
- 保持策略的适应性，应对市场风格变化

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
        
        return report
    
    def create_interactive_dashboard(self, save_path: Optional[str] = None) -> str:
        """
        创建交互式性能分析仪表板
        
        Args:
            save_path: 保存路径
            
        Returns:
            str: 仪表板HTML文件路径
        """
        logger.info("创建交互式性能分析仪表板...")
        
        # 计算指标
        explosive_metrics = self.analyze_explosive_stock_accuracy()
        t_metrics = self.analyze_t_trading_efficiency()
        
        # 创建子图
        fig = make_subplots(
            rows=3, cols=2,
            subplot_titles=[
                '权益曲线', '爆发股识别准确率',
                '做T收益分布', '月度收益热力图',
                '交易频次统计', '风险指标雷达图'
            ],
            specs=[
                [{"secondary_y": False}, {"type": "bar"}],
                [{"type": "histogram"}, {"type": "heatmap"}],
                [{"type": "bar"}, {"type": "scatterpolar"}]
            ]
        )
        
        # 1. 权益曲线
        if not self.equity_df.empty:
            fig.add_trace(
                go.Scatter(
                    x=self.equity_df.index,
                    y=self.equity_df['total_value'],
                    mode='lines',
                    name='账户总价值',
                    line=dict(color='blue', width=2)
                ),
                row=1, col=1
            )
        
        # 2. 爆发股识别准确率
        accuracy_data = [
            explosive_metrics.accuracy,
            explosive_metrics.precision,
            explosive_metrics.recall,
            explosive_metrics.f1_score
        ]
        accuracy_labels = ['准确率', '精确率', '召回率', 'F1分数']
        
        fig.add_trace(
            go.Bar(
                x=accuracy_labels,
                y=accuracy_data,
                name='识别准确率',
                marker_color=['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728']
            ),
            row=1, col=2
        )
        
        # 3. 做T收益分布
        if not self.trades_df.empty:
            t_trades = self.trades_df[self.trades_df['is_t_trade'] == True]
            if not t_trades.empty:
                # 简化的做T收益计算
                t_profits = []
                for t_id in t_trades['t_trade_id'].unique():
                    if pd.isna(t_id):
                        continue
                    t_pair = t_trades[t_trades['t_trade_id'] == t_id]
                    if len(t_pair) == 2:
                        buy_trade = t_pair[t_pair['side'] == 'buy'].iloc[0]
                        sell_trade = t_pair[t_pair['side'] == 'sell'].iloc[0]
                        profit = (sell_trade['price'] - buy_trade['price']) * min(buy_trade['quantity'], sell_trade['quantity'])
                        t_profits.append(profit)
                
                if t_profits:
                    fig.add_trace(
                        go.Histogram(
                            x=t_profits,
                            name='做T收益分布',
                            nbinsx=20,
                            marker_color='lightblue'
                        ),
                        row=2, col=1
                    )
        
        # 4. 月度收益热力图（简化版）
        if not self.equity_df.empty and len(self.equity_df) > 30:
            monthly_returns = self.equity_df['daily_return'].resample('M').apply(lambda x: (1 + x).prod() - 1)
            
            if len(monthly_returns) > 1:
                # 创建简化的热力图数据
                months = [date.strftime('%Y-%m') for date in monthly_returns.index]
                returns_values = monthly_returns.values
                
                fig.add_trace(
                    go.Heatmap(
                        z=[returns_values],
                        x=months,
                        y=['月度收益'],
                        colorscale='RdYlGn',
                        name='月度收益'
                    ),
                    row=2, col=2
                )
        
        # 5. 交易频次统计
        if not self.trades_df.empty:
            symbol_counts = self.trades_df['symbol'].value_counts().head(10)
            
            fig.add_trace(
                go.Bar(
                    x=symbol_counts.index,
                    y=symbol_counts.values,
                    name='交易频次',
                    marker_color='lightgreen'
                ),
                row=3, col=1
            )
        
        # 6. 风险指标雷达图
        risk_metrics = [
            explosive_metrics.precision,
            explosive_metrics.recall,
            t_metrics.t_success_rate,
            min(1.0, abs(t_metrics.t_sharpe_ratio) / 2),  # 标准化夏普比率
            min(1.0, t_metrics.t_win_loss_ratio / 3)      # 标准化盈亏比
        ]
        risk_labels = ['精确率', '召回率', '做T成功率', '夏普比率', '盈亏比']
        
        fig.add_trace(
            go.Scatterpolar(
                r=risk_metrics,
                theta=risk_labels,
                fill='toself',
                name='风险指标',
                line_color='red'
            ),
            row=3, col=2
        )
        
        # 更新布局
        fig.update_layout(
            title_text="量化交易系统性能分析仪表板",
            title_x=0.5,
            height=1200,
            showlegend=True
        )
        
        # 保存仪表板
        if save_path is None:
            save_path = f"performance_dashboard_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
        
        pyo.plot(fig, filename=save_path, auto_open=False)
        
        logger.info(f"交互式仪表板已保存到：{save_path}")
        return save_path
    
    def export_metrics_to_json(self, file_path: str):
        """
        导出所有指标到JSON文件
        
        Args:
            file_path: JSON文件路径
        """
        logger.info(f"导出性能指标到JSON：{file_path}")
        
        # 计算所有指标
        explosive_metrics = self.analyze_explosive_stock_accuracy()
        t_metrics = self.analyze_t_trading_efficiency()
        attribution = self.perform_attribution_analysis()
        
        # 转换为字典
        metrics_dict = {
            'explosive_stock_metrics': asdict(explosive_metrics),
            't_trading_metrics': asdict(t_metrics),
            'attribution_analysis': asdict(attribution),
            'export_time': datetime.now().isoformat()
        }
        
        # 处理numpy数据类型
        def convert_numpy_types(obj):
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, dict):
                return {key: convert_numpy_types(value) for key, value in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy_types(item) for item in obj]
            return obj
        
        metrics_dict = convert_numpy_types(metrics_dict)
        
        # 保存到JSON
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(metrics_dict, f, ensure_ascii=False, indent=2)
        
        logger.info("JSON导出完成")


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    # 创建模拟数据
    dates = pd.date_range('2024-01-01', '2024-03-31', freq='D')
    np.random.seed(42)
    
    # 模拟权益曲线
    returns = np.random.normal(0.001, 0.02, len(dates))
    values = [1000000]
    for ret in returns[1:]:
        values.append(values[-1] * (1 + ret))
    
    equity_curve = []
    for i, (date, value) in enumerate(zip(dates, values)):
        equity_curve.append({
            'timestamp': date,
            'total_value': value,
            'cash': value * 0.1,
            'positions_value': value * 0.9
        })
    
    # 模拟交易记录（包含做T交易）
    trades = []
    for i in range(100):
        symbol = f'00000{i%5+1}.SZ'
        timestamp = dates[i % len(dates)] + timedelta(hours=np.random.randint(9, 15))
        
        trades.append({
            'timestamp': timestamp,
            'symbol': symbol,
            'side': 'buy' if i % 2 == 0 else 'sell',
            'quantity': 1000 + i * 100,
            'price': 10 + np.random.random() * 5,
            'commission': 15 + np.random.random() * 10,
            'strategy_id': 'test_strategy'
        })
    
    # 模拟股票预测数据
    stock_predictions = []
    for i in range(50):
        stock_predictions.append({
            'date': dates[i],
            'symbol': f'00000{i%10+1}.SZ',
            'predicted_score': np.random.random(),
            'predicted_return': np.random.normal(0.05, 0.3)
        })
    
    # 创建回测结果
    results = {
        'equity_curve': equity_curve,
        'trades': trades
    }
    
    # 创建高级分析器
    analyzer = AdvancedPerformanceAnalyzer(
        results=results,
        stock_predictions=pd.DataFrame(stock_predictions)
    )
    
    # 测试各项分析功能
    print("=== 爆发股识别准确率分析 ===")
    explosive_metrics = analyzer.analyze_explosive_stock_accuracy()
    print(f"准确率: {explosive_metrics.accuracy:.2%}")
    print(f"精确率: {explosive_metrics.precision:.2%}")
    print(f"召回率: {explosive_metrics.recall:.2%}")
    
    print("\n=== 做T效率分析 ===")
    t_metrics = analyzer.analyze_t_trading_efficiency()
    print(f"做T成功率: {t_metrics.t_success_rate:.2%}")
    print(f"平均做T收益: {t_metrics.avg_t_profit_per_trade:.2f}")
    
    print("\n=== 策略归因分析 ===")
    attribution = analyzer.perform_attribution_analysis()
    print(f"选股贡献: {attribution.stock_selection_contribution:.2%}")
    print(f"择时贡献: {attribution.timing_contribution:.2%}")
    
    # 生成综合报告
    print("\n=== 生成综合报告 ===")
    report = analyzer.generate_comprehensive_report("comprehensive_performance_report.md")
    print("综合报告已生成")
    
    # 创建交互式仪表板
    print("\n=== 创建交互式仪表板 ===")
    dashboard_path = analyzer.create_interactive_dashboard()
    print(f"仪表板已保存到: {dashboard_path}")
    
    # 导出指标到JSON
    print("\n=== 导出指标到JSON ===")
    analyzer.export_metrics_to_json("performance_metrics.json")
    print("指标已导出到JSON文件")