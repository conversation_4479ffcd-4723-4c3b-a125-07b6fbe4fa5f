# -*- coding: utf-8 -*-
"""
策略回测执行引擎

提供完整的策略回测功能，包括订单执行、仓位管理、风险控制等
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable, Tuple
import logging
from dataclasses import dataclass, field
from enum import Enum
import json
from abc import ABC, abstractmethod

from .data_replay import HistoricalDataReplay, MarketData, ReplayConfig, DataFrequency

logger = logging.getLogger(__name__)


class OrderType(Enum):
    """订单类型"""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"


class OrderSide(Enum):
    """订单方向"""
    BUY = "buy"
    SELL = "sell"


class OrderStatus(Enum):
    """订单状态"""
    PENDING = "pending"
    FILLED = "filled"
    PARTIALLY_FILLED = "partially_filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"


@dataclass
class Order:
    """订单对象"""
    order_id: str
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: int
    price: Optional[float] = None
    stop_price: Optional[float] = None
    timestamp: Optional[datetime] = None
    status: OrderStatus = OrderStatus.PENDING
    filled_quantity: int = 0
    filled_price: Optional[float] = None
    commission: float = 0.0
    
    # 策略相关信息
    strategy_id: Optional[str] = None
    signal_info: Optional[Dict] = None


@dataclass
class Position:
    """持仓对象"""
    symbol: str
    quantity: int
    avg_cost: float
    market_value: float = 0.0
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0
    
    def update_market_value(self, current_price: float):
        """更新市值和未实现盈亏"""
        self.market_value = self.quantity * current_price
        self.unrealized_pnl = (current_price - self.avg_cost) * self.quantity


@dataclass
class Account:
    """账户对象"""
    initial_capital: float
    cash: float
    total_value: float = 0.0
    positions: Dict[str, Position] = field(default_factory=dict)
    orders: List[Order] = field(default_factory=list)
    trades: List[Dict] = field(default_factory=list)
    
    # 统计信息
    total_commission: float = 0.0
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0


@dataclass
class BacktestConfig:
    """回测配置"""
    initial_capital: float = 1000000.0  # 初始资金
    commission_rate: float = 0.0003  # 手续费率
    slippage_rate: float = 0.001  # 滑点率
    min_commission: float = 5.0  # 最小手续费
    
    # 风险控制参数
    max_position_size: float = 0.3  # 单只股票最大仓位
    max_total_position: float = 0.95  # 最大总仓位
    stop_loss_pct: float = 0.1  # 止损比例
    
    # 交易限制
    allow_short_selling: bool = False  # 是否允许做空
    margin_ratio: float = 1.0  # 保证金比例
    
    # 其他设置
    benchmark: Optional[str] = None  # 基准指数
    save_trades: bool = True  # 是否保存交易记录


class Strategy(ABC):
    """策略基类"""
    
    def __init__(self, strategy_id: str):
        self.strategy_id = strategy_id
        self.positions: Dict[str, Position] = {}
        self.orders: List[Order] = []
        
    @abstractmethod
    def on_data(self, timestamp: datetime, data: Dict[str, MarketData]) -> List[Order]:
        """
        数据回调函数，策略在此处生成交易信号
        
        Args:
            timestamp: 当前时间
            data: 市场数据
            
        Returns:
            List[Order]: 订单列表
        """
        pass
    
    @abstractmethod
    def on_order_filled(self, order: Order):
        """订单成交回调"""
        pass
    
    def on_position_update(self, positions: Dict[str, Position]):
        """持仓更新回调"""
        self.positions = positions


class BacktestEngine:
    """回测执行引擎"""
    
    def __init__(self, config: BacktestConfig):
        """
        初始化回测引擎
        
        Args:
            config: 回测配置
        """
        self.config = config
        self.account = Account(
            initial_capital=config.initial_capital,
            cash=config.initial_capital,
            total_value=config.initial_capital
        )
        
        self.data_replay: Optional[HistoricalDataReplay] = None
        self.strategies: List[Strategy] = []
        self.current_time: Optional[datetime] = None
        
        # 回测结果
        self.equity_curve: List[Dict] = []
        self.performance_metrics: Dict = {}
        
        logger.info(f"回测引擎初始化完成，初始资金：{config.initial_capital:,.2f}")
    
    def add_strategy(self, strategy: Strategy):
        """添加策略"""
        self.strategies.append(strategy)
        logger.info(f"添加策略：{strategy.strategy_id}")
    
    def set_data_replay(self, data_replay: HistoricalDataReplay):
        """设置数据回放器"""
        self.data_replay = data_replay
        logger.info("数据回放器设置完成")
    
    def run_backtest(self) -> Dict[str, Any]:
        """
        运行回测
        
        Returns:
            Dict[str, Any]: 回测结果
        """
        if not self.data_replay:
            raise ValueError("未设置数据回放器")
        
        if not self.strategies:
            raise ValueError("未添加策略")
        
        logger.info("开始运行回测...")
        
        # 重置状态
        self._reset_backtest()
        
        # 开始数据回放
        for timestamp, market_data in self.data_replay.start_replay():
            self.current_time = timestamp
            
            # 更新持仓市值
            self._update_positions_value(market_data)
            
            # 处理挂单
            self._process_pending_orders(market_data)
            
            # 策略信号生成
            new_orders = []
            for strategy in self.strategies:
                try:
                    strategy_orders = strategy.on_data(timestamp, market_data)
                    if strategy_orders:
                        new_orders.extend(strategy_orders)
                except Exception as e:
                    logger.error(f"策略 {strategy.strategy_id} 执行失败：{e}")
            
            # 处理新订单
            for order in new_orders:
                self._process_order(order, market_data)
            
            # 风险控制检查
            self._risk_control_check(market_data)
            
            # 记录权益曲线
            self._record_equity_curve(timestamp)
        
        # 计算回测结果
        results = self._calculate_results()
        
        logger.info("回测完成")
        return results
    
    def _reset_backtest(self):
        """重置回测状态"""
        self.account = Account(
            initial_capital=self.config.initial_capital,
            cash=self.config.initial_capital,
            total_value=self.config.initial_capital
        )
        self.equity_curve = []
        self.performance_metrics = {}
        self.current_time = None
        
        # 重置策略状态
        for strategy in self.strategies:
            strategy.positions = {}
            strategy.orders = []
    
    def _update_positions_value(self, market_data: Dict[str, MarketData]):
        """更新持仓市值"""
        total_market_value = 0.0
        
        for symbol, position in self.account.positions.items():
            if symbol in market_data:
                current_price = market_data[symbol].close
                position.update_market_value(current_price)
                total_market_value += position.market_value
        
        self.account.total_value = self.account.cash + total_market_value
    
    def _process_pending_orders(self, market_data: Dict[str, MarketData]):
        """处理挂单"""
        filled_orders = []
        
        for order in self.account.orders:
            if order.status == OrderStatus.PENDING:
                if order.symbol in market_data:
                    market_info = market_data[order.symbol]
                    
                    # 检查订单是否可以成交
                    if self._can_fill_order(order, market_info):
                        fill_price = self._calculate_fill_price(order, market_info)
                        self._fill_order(order, fill_price, order.quantity)
                        filled_orders.append(order)
        
        # 通知策略订单成交
        for order in filled_orders:
            for strategy in self.strategies:
                if strategy.strategy_id == order.strategy_id:
                    strategy.on_order_filled(order)
    
    def _can_fill_order(self, order: Order, market_data: MarketData) -> bool:
        """检查订单是否可以成交"""
        if order.order_type == OrderType.MARKET:
            return True
        elif order.order_type == OrderType.LIMIT:
            if order.side == OrderSide.BUY:
                return market_data.low <= order.price
            else:
                return market_data.high >= order.price
        elif order.order_type == OrderType.STOP:
            if order.side == OrderSide.BUY:
                return market_data.high >= order.stop_price
            else:
                return market_data.low <= order.stop_price
        
        return False
    
    def _calculate_fill_price(self, order: Order, market_data: MarketData) -> float:
        """计算成交价格"""
        if order.order_type == OrderType.MARKET:
            # 市价单使用开盘价，考虑滑点
            base_price = market_data.open
            if order.side == OrderSide.BUY:
                return base_price * (1 + self.config.slippage_rate)
            else:
                return base_price * (1 - self.config.slippage_rate)
        elif order.order_type == OrderType.LIMIT:
            return order.price
        elif order.order_type == OrderType.STOP:
            return order.stop_price
        
        return market_data.close
    
    def _fill_order(self, order: Order, fill_price: float, fill_quantity: int):
        """执行订单成交"""
        # 计算手续费
        commission = max(
            fill_price * fill_quantity * self.config.commission_rate,
            self.config.min_commission
        )
        
        order.status = OrderStatus.FILLED
        order.filled_quantity = fill_quantity
        order.filled_price = fill_price
        order.commission = commission
        
        # 更新账户
        if order.side == OrderSide.BUY:
            # 买入
            total_cost = fill_price * fill_quantity + commission
            
            if self.account.cash >= total_cost:
                self.account.cash -= total_cost
                
                # 更新持仓
                if order.symbol in self.account.positions:
                    position = self.account.positions[order.symbol]
                    total_quantity = position.quantity + fill_quantity
                    total_cost_basis = position.avg_cost * position.quantity + fill_price * fill_quantity
                    position.quantity = total_quantity
                    position.avg_cost = total_cost_basis / total_quantity
                else:
                    self.account.positions[order.symbol] = Position(
                        symbol=order.symbol,
                        quantity=fill_quantity,
                        avg_cost=fill_price
                    )
                
                # 记录交易
                self._record_trade(order, fill_price, fill_quantity, commission)
                
                logger.debug(f"买入成交：{order.symbol} {fill_quantity}股 @{fill_price:.2f}")
            else:
                order.status = OrderStatus.REJECTED
                logger.warning(f"资金不足，订单被拒绝：{order.symbol}")
        
        else:
            # 卖出
            if order.symbol in self.account.positions:
                position = self.account.positions[order.symbol]
                
                if position.quantity >= fill_quantity:
                    # 计算已实现盈亏
                    realized_pnl = (fill_price - position.avg_cost) * fill_quantity
                    
                    # 更新现金
                    proceeds = fill_price * fill_quantity - commission
                    self.account.cash += proceeds
                    
                    # 更新持仓
                    position.quantity -= fill_quantity
                    position.realized_pnl += realized_pnl
                    
                    if position.quantity == 0:
                        del self.account.positions[order.symbol]
                    
                    # 记录交易
                    self._record_trade(order, fill_price, fill_quantity, commission)
                    
                    logger.debug(f"卖出成交：{order.symbol} {fill_quantity}股 @{fill_price:.2f}")
                else:
                    order.status = OrderStatus.REJECTED
                    logger.warning(f"持仓不足，订单被拒绝：{order.symbol}")
            else:
                order.status = OrderStatus.REJECTED
                logger.warning(f"无持仓，订单被拒绝：{order.symbol}")
        
        self.account.total_commission += commission
    
    def _record_trade(self, order: Order, price: float, quantity: int, commission: float):
        """记录交易"""
        if self.config.save_trades:
            trade = {
                'timestamp': self.current_time,
                'symbol': order.symbol,
                'side': order.side.value,
                'quantity': quantity,
                'price': price,
                'commission': commission,
                'strategy_id': order.strategy_id,
                'signal_info': order.signal_info
            }
            self.account.trades.append(trade)
        
        # 更新统计
        self.account.total_trades += 1
        if order.side == OrderSide.SELL and order.symbol in self.account.positions:
            position = self.account.positions[order.symbol]
            if position.realized_pnl > 0:
                self.account.winning_trades += 1
            else:
                self.account.losing_trades += 1
    
    def _process_order(self, order: Order, market_data: Dict[str, MarketData]):
        """处理新订单"""
        # 设置订单时间戳
        order.timestamp = self.current_time
        
        # 风险检查
        if not self._risk_check_order(order):
            order.status = OrderStatus.REJECTED
            logger.warning(f"订单风险检查失败：{order.symbol}")
            return
        
        # 添加到订单列表
        self.account.orders.append(order)
        
        # 如果是市价单，立即尝试成交
        if order.order_type == OrderType.MARKET:
            if order.symbol in market_data:
                market_info = market_data[order.symbol]
                fill_price = self._calculate_fill_price(order, market_info)
                self._fill_order(order, fill_price, order.quantity)
    
    def _risk_check_order(self, order: Order) -> bool:
        """订单风险检查"""
        # 检查单只股票仓位限制
        if order.side == OrderSide.BUY:
            current_position = 0
            if order.symbol in self.account.positions:
                current_position = self.account.positions[order.symbol].quantity
            
            # 估算订单价值
            estimated_price = order.price if order.price else 10.0  # 默认价格
            order_value = estimated_price * order.quantity
            position_ratio = order_value / self.account.total_value
            
            if position_ratio > self.config.max_position_size:
                logger.warning(f"单只股票仓位超限：{order.symbol} {position_ratio:.2%}")
                return False
        
        # 检查总仓位限制
        total_position_value = sum(pos.market_value for pos in self.account.positions.values())
        if order.side == OrderSide.BUY:
            estimated_price = order.price if order.price else 10.0
            order_value = estimated_price * order.quantity
            total_ratio = (total_position_value + order_value) / self.account.total_value
            
            if total_ratio > self.config.max_total_position:
                logger.warning(f"总仓位超限：{total_ratio:.2%}")
                return False
        
        return True
    
    def _risk_control_check(self, market_data: Dict[str, MarketData]):
        """风险控制检查"""
        # 止损检查
        stop_orders = []
        
        for symbol, position in self.account.positions.items():
            if symbol in market_data:
                current_price = market_data[symbol].close
                loss_pct = (position.avg_cost - current_price) / position.avg_cost
                
                if loss_pct > self.config.stop_loss_pct:
                    # 生成止损订单
                    stop_order = Order(
                        order_id=f"stop_{symbol}_{self.current_time.strftime('%Y%m%d_%H%M%S')}",
                        symbol=symbol,
                        side=OrderSide.SELL,
                        order_type=OrderType.MARKET,
                        quantity=position.quantity,
                        strategy_id="risk_control",
                        signal_info={'reason': 'stop_loss', 'loss_pct': loss_pct}
                    )
                    stop_orders.append(stop_order)
                    
                    logger.warning(f"触发止损：{symbol} 亏损{loss_pct:.2%}")
        
        # 执行止损订单
        for order in stop_orders:
            self._process_order(order, market_data)
    
    def _record_equity_curve(self, timestamp: datetime):
        """记录权益曲线"""
        equity_point = {
            'timestamp': timestamp,
            'total_value': self.account.total_value,
            'cash': self.account.cash,
            'positions_value': sum(pos.market_value for pos in self.account.positions.values()),
            'unrealized_pnl': sum(pos.unrealized_pnl for pos in self.account.positions.values()),
            'realized_pnl': sum(pos.realized_pnl for pos in self.account.positions.values())
        }
        self.equity_curve.append(equity_point)
    
    def _calculate_results(self) -> Dict[str, Any]:
        """计算回测结果"""
        if not self.equity_curve:
            return {}
        
        # 转换为DataFrame便于计算
        df = pd.DataFrame(self.equity_curve)
        df.set_index('timestamp', inplace=True)
        
        # 基本统计
        initial_value = self.config.initial_capital
        final_value = df['total_value'].iloc[-1]
        total_return = (final_value - initial_value) / initial_value
        
        # 计算日收益率
        df['daily_return'] = df['total_value'].pct_change()
        
        # 年化收益率
        trading_days = len(df)
        annual_return = (1 + total_return) ** (252 / trading_days) - 1
        
        # 波动率
        volatility = df['daily_return'].std() * np.sqrt(252)
        
        # 夏普比率（假设无风险利率为3%）
        risk_free_rate = 0.03
        sharpe_ratio = (annual_return - risk_free_rate) / volatility if volatility > 0 else 0
        
        # 最大回撤
        df['cumulative'] = (1 + df['daily_return']).cumprod()
        df['running_max'] = df['cumulative'].expanding().max()
        df['drawdown'] = (df['cumulative'] - df['running_max']) / df['running_max']
        max_drawdown = df['drawdown'].min()
        
        # 胜率
        win_rate = self.account.winning_trades / max(self.account.total_trades, 1)
        
        # 盈亏比
        winning_trades_pnl = sum(trade.get('pnl', 0) for trade in self.account.trades 
                               if trade.get('pnl', 0) > 0)
        losing_trades_pnl = abs(sum(trade.get('pnl', 0) for trade in self.account.trades 
                                  if trade.get('pnl', 0) < 0))
        profit_loss_ratio = winning_trades_pnl / max(losing_trades_pnl, 1)
        
        results = {
            'basic_stats': {
                'initial_capital': initial_value,
                'final_value': final_value,
                'total_return': total_return,
                'annual_return': annual_return,
                'volatility': volatility,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'trading_days': trading_days
            },
            'trading_stats': {
                'total_trades': self.account.total_trades,
                'winning_trades': self.account.winning_trades,
                'losing_trades': self.account.losing_trades,
                'win_rate': win_rate,
                'profit_loss_ratio': profit_loss_ratio,
                'total_commission': self.account.total_commission
            },
            'equity_curve': self.equity_curve,
            'trades': self.account.trades if self.config.save_trades else [],
            'final_positions': {
                symbol: {
                    'quantity': pos.quantity,
                    'avg_cost': pos.avg_cost,
                    'market_value': pos.market_value,
                    'unrealized_pnl': pos.unrealized_pnl
                }
                for symbol, pos in self.account.positions.items()
            }
        }
        
        self.performance_metrics = results
        return results
    
    def get_performance_summary(self) -> str:
        """获取业绩摘要"""
        if not self.performance_metrics:
            return "尚未运行回测"
        
        stats = self.performance_metrics['basic_stats']
        trading_stats = self.performance_metrics['trading_stats']
        
        summary = f"""
回测业绩摘要
====================
初始资金: {stats['initial_capital']:,.2f}
最终价值: {stats['final_value']:,.2f}
总收益率: {stats['total_return']:.2%}
年化收益率: {stats['annual_return']:.2%}
年化波动率: {stats['volatility']:.2%}
夏普比率: {stats['sharpe_ratio']:.2f}
最大回撤: {stats['max_drawdown']:.2%}

交易统计
====================
总交易次数: {trading_stats['total_trades']}
盈利交易: {trading_stats['winning_trades']}
亏损交易: {trading_stats['losing_trades']}
胜率: {trading_stats['win_rate']:.2%}
盈亏比: {trading_stats['profit_loss_ratio']:.2f}
总手续费: {trading_stats['total_commission']:,.2f}
        """
        
        return summary.strip()


# 示例策略：简单移动平均策略
class SimpleMAStrategy(Strategy):
    """简单移动平均策略"""
    
    def __init__(self, strategy_id: str = "simple_ma", short_window: int = 5, long_window: int = 20):
        super().__init__(strategy_id)
        self.short_window = short_window
        self.long_window = long_window
        self.price_history: Dict[str, List[float]] = {}
        self.order_counter = 0
    
    def on_data(self, timestamp: datetime, data: Dict[str, MarketData]) -> List[Order]:
        orders = []
        
        for symbol, market_data in data.items():
            # 更新价格历史
            if symbol not in self.price_history:
                self.price_history[symbol] = []
            
            self.price_history[symbol].append(market_data.close)
            
            # 保持历史数据长度
            if len(self.price_history[symbol]) > self.long_window:
                self.price_history[symbol] = self.price_history[symbol][-self.long_window:]
            
            # 计算移动平均
            if len(self.price_history[symbol]) >= self.long_window:
                short_ma = np.mean(self.price_history[symbol][-self.short_window:])
                long_ma = np.mean(self.price_history[symbol][-self.long_window:])
                prev_short_ma = np.mean(self.price_history[symbol][-self.short_window-1:-1])
                prev_long_ma = np.mean(self.price_history[symbol][-self.long_window-1:-1])
                
                # 金叉买入信号
                if short_ma > long_ma and prev_short_ma <= prev_long_ma:
                    if symbol not in self.positions or self.positions[symbol].quantity == 0:
                        self.order_counter += 1
                        order = Order(
                            order_id=f"{self.strategy_id}_{self.order_counter}",
                            symbol=symbol,
                            side=OrderSide.BUY,
                            order_type=OrderType.MARKET,
                            quantity=1000,  # 固定数量
                            strategy_id=self.strategy_id,
                            signal_info={
                                'signal': 'golden_cross',
                                'short_ma': short_ma,
                                'long_ma': long_ma
                            }
                        )
                        orders.append(order)
                
                # 死叉卖出信号
                elif short_ma < long_ma and prev_short_ma >= prev_long_ma:
                    if symbol in self.positions and self.positions[symbol].quantity > 0:
                        self.order_counter += 1
                        order = Order(
                            order_id=f"{self.strategy_id}_{self.order_counter}",
                            symbol=symbol,
                            side=OrderSide.SELL,
                            order_type=OrderType.MARKET,
                            quantity=self.positions[symbol].quantity,
                            strategy_id=self.strategy_id,
                            signal_info={
                                'signal': 'death_cross',
                                'short_ma': short_ma,
                                'long_ma': long_ma
                            }
                        )
                        orders.append(order)
        
        return orders
    
    def on_order_filled(self, order: Order):
        logger.info(f"策略 {self.strategy_id} 订单成交：{order.symbol} {order.side.value} {order.filled_quantity}股")


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    # 创建回测配置
    backtest_config = BacktestConfig(
        initial_capital=1000000,
        commission_rate=0.0003,
        slippage_rate=0.001
    )
    
    # 创建数据回放配置
    replay_config = ReplayConfig(
        start_date=datetime(2024, 1, 1),
        end_date=datetime(2024, 1, 10),
        symbols=['000001.SZ', '000002.SZ'],
        frequency=DataFrequency.DAILY,
        include_indicators=True
    )
    
    # 创建数据回放器
    data_replay = HistoricalDataReplay(replay_config)
    
    # 创建回测引擎
    engine = BacktestEngine(backtest_config)
    engine.set_data_replay(data_replay)
    
    # 添加策略
    strategy = SimpleMAStrategy()
    engine.add_strategy(strategy)
    
    # 运行回测
    results = engine.run_backtest()
    
    # 显示结果
    print(engine.get_performance_summary())