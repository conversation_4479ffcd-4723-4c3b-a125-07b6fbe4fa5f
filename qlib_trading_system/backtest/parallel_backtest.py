# -*- coding: utf-8 -*-
"""
多策略并行回测框架

支持多个策略同时回测，提供策略对比分析功能
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable, Tuple
import logging
from dataclasses import dataclass, field
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor, as_completed
import multiprocessing as mp
import pickle
import os
import json
from abc import ABC, abstractmethod
import time

from .data_replay import HistoricalDataReplay, ReplayConfig, DataFrequency
from .backtest_engine import BacktestEngine, BacktestConfig, Strategy
from .performance_analyzer import PerformanceAnalyzer, PerformanceMetrics

logger = logging.getLogger(__name__)


@dataclass
class StrategyConfig:
    """策略配置"""
    strategy_class: type
    strategy_params: Dict[str, Any]
    strategy_id: str
    description: str = ""
    enabled: bool = True


@dataclass
class ParallelBacktestConfig:
    """并行回测配置"""
    # 数据配置
    data_config: ReplayConfig
    
    # 回测配置
    backtest_config: BacktestConfig
    
    # 策略配置列表
    strategies: List[StrategyConfig]
    
    # 并行配置
    max_workers: Optional[int] = None  # 最大工作进程数，None表示使用CPU核心数
    use_multiprocessing: bool = True  # 是否使用多进程
    
    # 结果保存配置
    save_results: bool = True
    results_dir: str = "backtest_results"
    
    # 对比分析配置
    benchmark_strategy: Optional[str] = None  # 基准策略ID
    generate_comparison: bool = True


class ParallelBacktestFramework:
    """多策略并行回测框架"""
    
    def __init__(self, config: ParallelBacktestConfig):
        """
        初始化并行回测框架
        
        Args:
            config: 并行回测配置
        """
        self.config = config
        self.results: Dict[str, Dict[str, Any]] = {}
        self.performance_metrics: Dict[str, PerformanceMetrics] = {}
        
        # 创建结果目录
        if self.config.save_results:
            os.makedirs(self.config.results_dir, exist_ok=True)
        
        # 设置工作进程数
        if self.config.max_workers is None:
            self.config.max_workers = min(mp.cpu_count(), len(self.config.strategies))
        
        logger.info(f"并行回测框架初始化完成，将使用 {self.config.max_workers} 个工作进程")
    
    def run_parallel_backtest(self) -> Dict[str, Dict[str, Any]]:
        """
        运行并行回测
        
        Returns:
            Dict[str, Dict[str, Any]]: 所有策略的回测结果
        """
        logger.info(f"开始并行回测，共 {len(self.config.strategies)} 个策略")
        
        start_time = time.time()
        
        # 过滤启用的策略
        enabled_strategies = [s for s in self.config.strategies if s.enabled]
        
        if not enabled_strategies:
            logger.warning("没有启用的策略")
            return {}
        
        # 准备回测任务
        tasks = []
        for strategy_config in enabled_strategies:
            task = {
                'strategy_config': strategy_config,
                'data_config': self.config.data_config,
                'backtest_config': self.config.backtest_config
            }
            tasks.append(task)
        
        # 执行并行回测
        if self.config.use_multiprocessing:
            results = self._run_multiprocessing_backtest(tasks)
        else:
            results = self._run_threading_backtest(tasks)
        
        # 保存结果
        self.results = results
        
        # 计算性能指标
        self._calculate_all_performance_metrics()
        
        # 保存结果到文件
        if self.config.save_results:
            self._save_results_to_files()
        
        # 生成对比分析
        if self.config.generate_comparison:
            self._generate_strategy_comparison()
        
        elapsed_time = time.time() - start_time
        logger.info(f"并行回测完成，耗时 {elapsed_time:.2f} 秒")
        
        return self.results
    
    def _run_multiprocessing_backtest(self, tasks: List[Dict]) -> Dict[str, Dict[str, Any]]:
        """使用多进程执行回测"""
        logger.info("使用多进程模式执行回测")
        
        results = {}
        
        with ProcessPoolExecutor(max_workers=self.config.max_workers) as executor:
            # 提交任务
            future_to_strategy = {}
            for task in tasks:
                future = executor.submit(_run_single_backtest, task)
                future_to_strategy[future] = task['strategy_config'].strategy_id
            
            # 收集结果
            for future in as_completed(future_to_strategy):
                strategy_id = future_to_strategy[future]
                try:
                    result = future.result()
                    results[strategy_id] = result
                    logger.info(f"策略 {strategy_id} 回测完成")
                except Exception as e:
                    logger.error(f"策略 {strategy_id} 回测失败：{e}")
                    results[strategy_id] = {'error': str(e)}
        
        return results
    
    def _run_threading_backtest(self, tasks: List[Dict]) -> Dict[str, Dict[str, Any]]:
        """使用多线程执行回测"""
        logger.info("使用多线程模式执行回测")
        
        results = {}
        
        with ThreadPoolExecutor(max_workers=self.config.max_workers) as executor:
            # 提交任务
            future_to_strategy = {}
            for task in tasks:
                future = executor.submit(_run_single_backtest, task)
                future_to_strategy[future] = task['strategy_config'].strategy_id
            
            # 收集结果
            for future in as_completed(future_to_strategy):
                strategy_id = future_to_strategy[future]
                try:
                    result = future.result()
                    results[strategy_id] = result
                    logger.info(f"策略 {strategy_id} 回测完成")
                except Exception as e:
                    logger.error(f"策略 {strategy_id} 回测失败：{e}")
                    results[strategy_id] = {'error': str(e)}
        
        return results
    
    def _calculate_all_performance_metrics(self):
        """计算所有策略的性能指标"""
        logger.info("计算所有策略的性能指标")
        
        for strategy_id, result in self.results.items():
            if 'error' not in result:
                try:
                    analyzer = PerformanceAnalyzer(result)
                    metrics = analyzer.calculate_comprehensive_metrics()
                    self.performance_metrics[strategy_id] = metrics
                except Exception as e:
                    logger.error(f"计算策略 {strategy_id} 性能指标失败：{e}")
    
    def _save_results_to_files(self):
        """保存结果到文件"""
        logger.info("保存回测结果到文件")
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        for strategy_id, result in self.results.items():
            if 'error' not in result:
                # 保存详细结果
                result_file = os.path.join(
                    self.config.results_dir, 
                    f"{strategy_id}_{timestamp}.json"
                )
                
                # 转换datetime对象为字符串以便JSON序列化
                serializable_result = self._make_json_serializable(result)
                
                with open(result_file, 'w', encoding='utf-8') as f:
                    json.dump(serializable_result, f, ensure_ascii=False, indent=2)
                
                # 生成Excel报告
                if strategy_id in self.performance_metrics:
                    excel_file = os.path.join(
                        self.config.results_dir,
                        f"{strategy_id}_{timestamp}.xlsx"
                    )
                    
                    analyzer = PerformanceAnalyzer(result)
                    analyzer.export_results_to_excel(excel_file)
    
    def _make_json_serializable(self, obj):
        """将对象转换为JSON可序列化格式"""
        if isinstance(obj, dict):
            return {k: self._make_json_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_json_serializable(item) for item in obj]
        elif isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        else:
            return obj
    
    def _generate_strategy_comparison(self):
        """生成策略对比分析"""
        logger.info("生成策略对比分析")
        
        if len(self.performance_metrics) < 2:
            logger.warning("策略数量不足，无法生成对比分析")
            return
        
        # 创建对比表格
        comparison_data = []
        
        for strategy_id, metrics in self.performance_metrics.items():
            comparison_data.append({
                '策略ID': strategy_id,
                '总收益率': f"{metrics.total_return:.2%}",
                '年化收益率': f"{metrics.annual_return:.2%}",
                '年化波动率': f"{metrics.volatility:.2%}",
                '夏普比率': f"{metrics.sharpe_ratio:.3f}",
                '最大回撤': f"{metrics.max_drawdown:.2%}",
                '胜率': f"{metrics.win_rate:.2%}",
                '盈亏比': f"{metrics.profit_loss_ratio:.2f}",
                '总交易次数': metrics.total_trades,
                '换手率': f"{metrics.turnover_rate:.2f}"
            })
        
        comparison_df = pd.DataFrame(comparison_data)
        
        # 保存对比表格
        if self.config.save_results:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            comparison_file = os.path.join(
                self.config.results_dir,
                f"strategy_comparison_{timestamp}.xlsx"
            )
            
            with pd.ExcelWriter(comparison_file, engine='openpyxl') as writer:
                comparison_df.to_excel(writer, sheet_name='策略对比', index=False)
                
                # 添加排名表
                ranking_data = []
                
                # 按不同指标排名
                metrics_list = list(self.performance_metrics.items())
                
                # 按年化收益率排名
                sorted_by_return = sorted(metrics_list, 
                                        key=lambda x: x[1].annual_return, reverse=True)
                for i, (strategy_id, _) in enumerate(sorted_by_return):
                    ranking_data.append({
                        '策略ID': strategy_id,
                        '年化收益率排名': i + 1,
                        '夏普比率排名': 0,
                        '最大回撤排名': 0,
                        '综合排名': 0
                    })
                
                # 按夏普比率排名
                sorted_by_sharpe = sorted(metrics_list,
                                        key=lambda x: x[1].sharpe_ratio, reverse=True)
                for i, (strategy_id, _) in enumerate(sorted_by_sharpe):
                    for item in ranking_data:
                        if item['策略ID'] == strategy_id:
                            item['夏普比率排名'] = i + 1
                            break
                
                # 按最大回撤排名（回撤越小排名越高）
                sorted_by_drawdown = sorted(metrics_list,
                                          key=lambda x: abs(x[1].max_drawdown))
                for i, (strategy_id, _) in enumerate(sorted_by_drawdown):
                    for item in ranking_data:
                        if item['策略ID'] == strategy_id:
                            item['最大回撤排名'] = i + 1
                            break
                
                # 计算综合排名（简单平均）
                for item in ranking_data:
                    item['综合排名'] = (
                        item['年化收益率排名'] + 
                        item['夏普比率排名'] + 
                        item['最大回撤排名']
                    ) / 3
                
                # 按综合排名排序
                ranking_data.sort(key=lambda x: x['综合排名'])
                for i, item in enumerate(ranking_data):
                    item['综合排名'] = i + 1
                
                ranking_df = pd.DataFrame(ranking_data)
                ranking_df.to_excel(writer, sheet_name='策略排名', index=False)
        
        # 生成对比报告
        self._generate_comparison_report(comparison_df, ranking_df)
    
    def _generate_comparison_report(self, comparison_df: pd.DataFrame, ranking_df: pd.DataFrame):
        """生成策略对比报告"""
        if not self.config.save_results:
            return
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = os.path.join(
            self.config.results_dir,
            f"comparison_report_{timestamp}.md"
        )
        
        # 找出最佳策略
        best_return_strategy = ranking_df[ranking_df['年化收益率排名'] == 1]['策略ID'].iloc[0]
        best_sharpe_strategy = ranking_df[ranking_df['夏普比率排名'] == 1]['策略ID'].iloc[0]
        best_drawdown_strategy = ranking_df[ranking_df['最大回撤排名'] == 1]['策略ID'].iloc[0]
        best_overall_strategy = ranking_df[ranking_df['综合排名'] == 1]['策略ID'].iloc[0]
        
        report_content = f"""# 多策略回测对比报告

## 报告生成时间
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 回测概况
- **回测期间**: {self.config.data_config.start_date.strftime('%Y-%m-%d')} 至 {self.config.data_config.end_date.strftime('%Y-%m-%d')}
- **参与策略数量**: {len(self.performance_metrics)}
- **初始资金**: {self.config.backtest_config.initial_capital:,.2f} 元

## 最佳策略表现

### 各项指标最佳策略
- **最高年化收益率**: {best_return_strategy}
- **最高夏普比率**: {best_sharpe_strategy}  
- **最小最大回撤**: {best_drawdown_strategy}
- **综合排名第一**: {best_overall_strategy}

## 策略对比表格

"""
        
        # 添加对比表格（转换为Markdown格式）
        report_content += comparison_df.to_markdown(index=False)
        
        report_content += f"""

## 策略排名

"""
        
        # 添加排名表格
        report_content += ranking_df.to_markdown(index=False)
        
        # 添加详细分析
        report_content += f"""

## 详细分析

### 收益表现分析
"""
        
        # 分析收益表现
        returns = [metrics.annual_return for metrics in self.performance_metrics.values()]
        avg_return = np.mean(returns)
        max_return = max(returns)
        min_return = min(returns)
        
        report_content += f"""
- 平均年化收益率: {avg_return:.2%}
- 最高年化收益率: {max_return:.2%} ({best_return_strategy})
- 最低年化收益率: {min_return:.2%}
- 收益率标准差: {np.std(returns):.2%}

"""
        
        # 分析风险表现
        report_content += f"""### 风险表现分析
"""
        
        sharpe_ratios = [metrics.sharpe_ratio for metrics in self.performance_metrics.values()]
        drawdowns = [abs(metrics.max_drawdown) for metrics in self.performance_metrics.values()]
        
        avg_sharpe = np.mean(sharpe_ratios)
        avg_drawdown = np.mean(drawdowns)
        
        report_content += f"""
- 平均夏普比率: {avg_sharpe:.3f}
- 平均最大回撤: {avg_drawdown:.2%}
- 风险调整后收益最佳: {best_sharpe_strategy}
- 回撤控制最佳: {best_drawdown_strategy}

"""
        
        # 添加建议
        report_content += f"""### 投资建议

基于回测结果分析：

1. **综合表现最佳策略**: {best_overall_strategy}
   - 建议作为主要投资策略

2. **高收益策略**: {best_return_strategy}
   - 适合风险承受能力较强的投资者

3. **稳健策略**: {best_drawdown_strategy}
   - 适合风险厌恶型投资者

4. **风险调整后最优**: {best_sharpe_strategy}
   - 在风险和收益之间取得最佳平衡

### 注意事项

- 回测结果基于历史数据，不代表未来表现
- 实际交易中需要考虑滑点、冲击成本等因素
- 建议结合实盘验证和风险管理
- 定期重新评估策略表现并调整参数

"""
        
        # 保存报告
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        logger.info(f"策略对比报告已保存到：{report_file}")
    
    def get_best_strategies(self, top_n: int = 3) -> List[Tuple[str, PerformanceMetrics]]:
        """
        获取表现最佳的策略
        
        Args:
            top_n: 返回前N个策略
            
        Returns:
            List[Tuple[str, PerformanceMetrics]]: 策略ID和性能指标的列表
        """
        if not self.performance_metrics:
            return []
        
        # 按综合评分排序（这里使用夏普比率作为主要指标）
        sorted_strategies = sorted(
            self.performance_metrics.items(),
            key=lambda x: x[1].sharpe_ratio,
            reverse=True
        )
        
        return sorted_strategies[:top_n]
    
    def get_strategy_comparison_matrix(self) -> pd.DataFrame:
        """
        获取策略对比矩阵
        
        Returns:
            pd.DataFrame: 策略对比矩阵
        """
        if not self.performance_metrics:
            return pd.DataFrame()
        
        data = []
        for strategy_id, metrics in self.performance_metrics.items():
            data.append({
                '策略ID': strategy_id,
                '总收益率': metrics.total_return,
                '年化收益率': metrics.annual_return,
                '年化波动率': metrics.volatility,
                '夏普比率': metrics.sharpe_ratio,
                '索提诺比率': metrics.sortino_ratio,
                '最大回撤': metrics.max_drawdown,
                '胜率': metrics.win_rate,
                '盈亏比': metrics.profit_loss_ratio,
                '总交易次数': metrics.total_trades,
                '换手率': metrics.turnover_rate
            })
        
        return pd.DataFrame(data)


def _run_single_backtest(task: Dict) -> Dict[str, Any]:
    """
    运行单个策略的回测（用于多进程）
    
    Args:
        task: 回测任务配置
        
    Returns:
        Dict[str, Any]: 回测结果
    """
    try:
        strategy_config = task['strategy_config']
        data_config = task['data_config']
        backtest_config = task['backtest_config']
        
        # 创建数据回放器
        data_replay = HistoricalDataReplay(data_config)
        
        # 创建回测引擎
        engine = BacktestEngine(backtest_config)
        engine.set_data_replay(data_replay)
        
        # 创建策略实例
        strategy = strategy_config.strategy_class(
            strategy_id=strategy_config.strategy_id,
            **strategy_config.strategy_params
        )
        
        # 添加策略到引擎
        engine.add_strategy(strategy)
        
        # 运行回测
        results = engine.run_backtest()
        
        return results
        
    except Exception as e:
        logger.error(f"单个回测执行失败：{e}")
        return {'error': str(e)}


# 示例策略类
class BuyAndHoldStrategy(Strategy):
    """买入持有策略"""
    
    def __init__(self, strategy_id: str = "buy_and_hold", target_symbols: List[str] = None):
        super().__init__(strategy_id)
        self.target_symbols = target_symbols or []
        self.has_bought = set()
        self.order_counter = 0
    
    def on_data(self, timestamp: datetime, data: Dict[str, Any]) -> List:
        from .backtest_engine import Order, OrderSide, OrderType
        
        orders = []
        
        # 只在第一次看到数据时买入
        for symbol in self.target_symbols:
            if symbol in data and symbol not in self.has_bought:
                self.order_counter += 1
                order = Order(
                    order_id=f"{self.strategy_id}_{self.order_counter}",
                    symbol=symbol,
                    side=OrderSide.BUY,
                    order_type=OrderType.MARKET,
                    quantity=5000,  # 固定数量
                    strategy_id=self.strategy_id,
                    signal_info={'signal': 'buy_and_hold'}
                )
                orders.append(order)
                self.has_bought.add(symbol)
        
        return orders
    
    def on_order_filled(self, order):
        logger.info(f"买入持有策略订单成交：{order.symbol}")


class MomentumStrategy(Strategy):
    """动量策略"""
    
    def __init__(self, strategy_id: str = "momentum", lookback_days: int = 20, top_n: int = 3):
        super().__init__(strategy_id)
        self.lookback_days = lookback_days
        self.top_n = top_n
        self.price_history = {}
        self.order_counter = 0
        self.rebalance_frequency = 5  # 每5天重新平衡
        self.last_rebalance = None
    
    def on_data(self, timestamp: datetime, data: Dict[str, Any]) -> List:
        from .backtest_engine import Order, OrderSide, OrderType
        
        orders = []
        
        # 更新价格历史
        for symbol, market_data in data.items():
            if symbol not in self.price_history:
                self.price_history[symbol] = []
            self.price_history[symbol].append({
                'timestamp': timestamp,
                'price': market_data.close
            })
            
            # 保持历史数据长度
            if len(self.price_history[symbol]) > self.lookback_days + 5:
                self.price_history[symbol] = self.price_history[symbol][-self.lookback_days-5:]
        
        # 检查是否需要重新平衡
        should_rebalance = (
            self.last_rebalance is None or
            (timestamp - self.last_rebalance).days >= self.rebalance_frequency
        )
        
        if should_rebalance and len(self.price_history) > 0:
            # 计算动量
            momentum_scores = {}
            for symbol, history in self.price_history.items():
                if len(history) >= self.lookback_days:
                    recent_prices = [h['price'] for h in history[-self.lookback_days:]]
                    momentum = (recent_prices[-1] - recent_prices[0]) / recent_prices[0]
                    momentum_scores[symbol] = momentum
            
            if momentum_scores:
                # 选择动量最强的股票
                sorted_symbols = sorted(momentum_scores.items(), 
                                      key=lambda x: x[1], reverse=True)
                top_symbols = [symbol for symbol, _ in sorted_symbols[:self.top_n]]
                
                # 卖出不在top_n中的持仓
                for symbol, position in self.positions.items():
                    if symbol not in top_symbols and position.quantity > 0:
                        self.order_counter += 1
                        order = Order(
                            order_id=f"{self.strategy_id}_{self.order_counter}",
                            symbol=symbol,
                            side=OrderSide.SELL,
                            order_type=OrderType.MARKET,
                            quantity=position.quantity,
                            strategy_id=self.strategy_id,
                            signal_info={'signal': 'momentum_sell', 'momentum': momentum_scores.get(symbol, 0)}
                        )
                        orders.append(order)
                
                # 买入top_n股票
                for symbol in top_symbols:
                    if symbol not in self.positions or self.positions[symbol].quantity == 0:
                        self.order_counter += 1
                        order = Order(
                            order_id=f"{self.strategy_id}_{self.order_counter}",
                            symbol=symbol,
                            side=OrderSide.BUY,
                            order_type=OrderType.MARKET,
                            quantity=3000,  # 固定数量
                            strategy_id=self.strategy_id,
                            signal_info={'signal': 'momentum_buy', 'momentum': momentum_scores[symbol]}
                        )
                        orders.append(order)
                
                self.last_rebalance = timestamp
        
        return orders
    
    def on_order_filled(self, order):
        logger.info(f"动量策略订单成交：{order.symbol} {order.side.value}")


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    # 创建数据配置
    data_config = ReplayConfig(
        start_date=datetime(2024, 1, 1),
        end_date=datetime(2024, 2, 29),
        symbols=['000001.SZ', '000002.SZ', '000858.SZ'],
        frequency=DataFrequency.DAILY,
        include_indicators=True
    )
    
    # 创建回测配置
    backtest_config = BacktestConfig(
        initial_capital=1000000,
        commission_rate=0.0003,
        slippage_rate=0.001
    )
    
    # 创建策略配置
    strategies = [
        StrategyConfig(
            strategy_class=BuyAndHoldStrategy,
            strategy_params={'target_symbols': ['000001.SZ', '000002.SZ']},
            strategy_id='buy_and_hold',
            description='买入持有策略'
        ),
        StrategyConfig(
            strategy_class=MomentumStrategy,
            strategy_params={'lookback_days': 10, 'top_n': 2},
            strategy_id='momentum_10d',
            description='10日动量策略'
        ),
        StrategyConfig(
            strategy_class=MomentumStrategy,
            strategy_params={'lookback_days': 20, 'top_n': 2},
            strategy_id='momentum_20d',
            description='20日动量策略'
        )
    ]
    
    # 创建并行回测配置
    parallel_config = ParallelBacktestConfig(
        data_config=data_config,
        backtest_config=backtest_config,
        strategies=strategies,
        max_workers=2,
        use_multiprocessing=False,  # 测试时使用线程模式
        save_results=True,
        results_dir='test_backtest_results'
    )
    
    # 创建并行回测框架
    framework = ParallelBacktestFramework(parallel_config)
    
    # 运行并行回测
    results = framework.run_parallel_backtest()
    
    # 显示结果
    print(f"回测完成，共 {len(results)} 个策略")
    
    # 获取最佳策略
    best_strategies = framework.get_best_strategies(top_n=2)
    print("\n最佳策略:")
    for i, (strategy_id, metrics) in enumerate(best_strategies):
        print(f"{i+1}. {strategy_id}: 年化收益率={metrics.annual_return:.2%}, 夏普比率={metrics.sharpe_ratio:.3f}")
    
    # 获取对比矩阵
    comparison_matrix = framework.get_strategy_comparison_matrix()
    print("\n策略对比矩阵:")
    print(comparison_matrix.to_string(index=False))