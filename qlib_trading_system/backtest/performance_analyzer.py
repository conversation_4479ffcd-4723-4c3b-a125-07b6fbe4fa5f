# -*- coding: utf-8 -*-
"""
回测结果计算和分析算法

提供全面的回测结果分析，包括收益指标、风险指标、交易分析等
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import logging
from dataclasses import dataclass
import json
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetrics:
    """业绩指标数据类"""
    # 收益指标
    total_return: float
    annual_return: float
    monthly_return: float
    daily_return_mean: float
    
    # 风险指标
    volatility: float
    max_drawdown: float
    var_95: float  # 95%置信度VaR
    cvar_95: float  # 95%置信度CVaR
    
    # 风险调整收益指标
    sharpe_ratio: float
    sortino_ratio: float
    calmar_ratio: float
    information_ratio: float
    
    # 交易指标
    total_trades: int
    win_rate: float
    profit_loss_ratio: float
    avg_holding_period: float
    turnover_rate: float
    
    # 其他指标
    beta: Optional[float] = None
    alpha: Optional[float] = None
    tracking_error: Optional[float] = None


class PerformanceAnalyzer:
    """回测结果分析器"""
    
    def __init__(self, results: Dict[str, Any], benchmark_data: Optional[pd.DataFrame] = None):
        """
        初始化分析器
        
        Args:
            results: 回测结果
            benchmark_data: 基准数据（可选）
        """
        self.results = results
        self.benchmark_data = benchmark_data
        
        # 转换权益曲线为DataFrame
        if 'equity_curve' in results and results['equity_curve']:
            self.equity_df = pd.DataFrame(results['equity_curve'])
            self.equity_df.set_index('timestamp', inplace=True)
            self.equity_df['daily_return'] = self.equity_df['total_value'].pct_change()
        else:
            self.equity_df = pd.DataFrame()
        
        # 转换交易记录为DataFrame
        if 'trades' in results and results['trades']:
            self.trades_df = pd.DataFrame(results['trades'])
            self.trades_df['timestamp'] = pd.to_datetime(self.trades_df['timestamp'])
        else:
            self.trades_df = pd.DataFrame()
        
        logger.info("回测结果分析器初始化完成")
    
    def calculate_comprehensive_metrics(self) -> PerformanceMetrics:
        """
        计算全面的业绩指标
        
        Returns:
            PerformanceMetrics: 业绩指标对象
        """
        if self.equity_df.empty:
            logger.warning("权益曲线数据为空，无法计算指标")
            return self._empty_metrics()
        
        logger.info("开始计算综合业绩指标...")
        
        # 基本数据
        returns = self.equity_df['daily_return'].dropna()
        total_value = self.equity_df['total_value']
        
        # 收益指标
        total_return = self._calculate_total_return()
        annual_return = self._calculate_annual_return(total_return)
        monthly_return = self._calculate_monthly_return(annual_return)
        daily_return_mean = returns.mean()
        
        # 风险指标
        volatility = self._calculate_volatility(returns)
        max_drawdown = self._calculate_max_drawdown()
        var_95 = self._calculate_var(returns, confidence=0.95)
        cvar_95 = self._calculate_cvar(returns, confidence=0.95)
        
        # 风险调整收益指标
        sharpe_ratio = self._calculate_sharpe_ratio(returns, annual_return, volatility)
        sortino_ratio = self._calculate_sortino_ratio(returns, annual_return)
        calmar_ratio = self._calculate_calmar_ratio(annual_return, max_drawdown)
        information_ratio = self._calculate_information_ratio(returns)
        
        # 交易指标
        trading_metrics = self._calculate_trading_metrics()
        
        # 基准相关指标
        beta, alpha, tracking_error = self._calculate_benchmark_metrics(returns)
        
        metrics = PerformanceMetrics(
            total_return=total_return,
            annual_return=annual_return,
            monthly_return=monthly_return,
            daily_return_mean=daily_return_mean,
            volatility=volatility,
            max_drawdown=max_drawdown,
            var_95=var_95,
            cvar_95=cvar_95,
            sharpe_ratio=sharpe_ratio,
            sortino_ratio=sortino_ratio,
            calmar_ratio=calmar_ratio,
            information_ratio=information_ratio,
            beta=beta,
            alpha=alpha,
            tracking_error=tracking_error,
            **trading_metrics
        )
        
        logger.info("综合业绩指标计算完成")
        return metrics
    
    def _empty_metrics(self) -> PerformanceMetrics:
        """返回空的指标对象"""
        return PerformanceMetrics(
            total_return=0.0, annual_return=0.0, monthly_return=0.0, daily_return_mean=0.0,
            volatility=0.0, max_drawdown=0.0, var_95=0.0, cvar_95=0.0,
            sharpe_ratio=0.0, sortino_ratio=0.0, calmar_ratio=0.0, information_ratio=0.0,
            total_trades=0, win_rate=0.0, profit_loss_ratio=0.0, 
            avg_holding_period=0.0, turnover_rate=0.0
        )
    
    def _calculate_total_return(self) -> float:
        """计算总收益率"""
        if len(self.equity_df) < 2:
            return 0.0
        
        initial_value = self.equity_df['total_value'].iloc[0]
        final_value = self.equity_df['total_value'].iloc[-1]
        
        return (final_value - initial_value) / initial_value
    
    def _calculate_annual_return(self, total_return: float) -> float:
        """计算年化收益率"""
        if len(self.equity_df) < 2:
            return 0.0
        
        trading_days = len(self.equity_df)
        years = trading_days / 252  # 假设一年252个交易日
        
        if years <= 0:
            return 0.0
        
        return (1 + total_return) ** (1 / years) - 1
    
    def _calculate_monthly_return(self, annual_return: float) -> float:
        """计算月化收益率"""
        return (1 + annual_return) ** (1/12) - 1
    
    def _calculate_volatility(self, returns: pd.Series) -> float:
        """计算年化波动率"""
        if len(returns) < 2:
            return 0.0
        
        return returns.std() * np.sqrt(252)
    
    def _calculate_max_drawdown(self) -> float:
        """计算最大回撤"""
        if len(self.equity_df) < 2:
            return 0.0
        
        cumulative = (1 + self.equity_df['daily_return'].fillna(0)).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        
        return drawdown.min()
    
    def _calculate_var(self, returns: pd.Series, confidence: float = 0.95) -> float:
        """计算VaR（风险价值）"""
        if len(returns) < 10:
            return 0.0
        
        return returns.quantile(1 - confidence)
    
    def _calculate_cvar(self, returns: pd.Series, confidence: float = 0.95) -> float:
        """计算CVaR（条件风险价值）"""
        if len(returns) < 10:
            return 0.0
        
        var = self._calculate_var(returns, confidence)
        return returns[returns <= var].mean()
    
    def _calculate_sharpe_ratio(self, returns: pd.Series, annual_return: float, volatility: float, risk_free_rate: float = 0.03) -> float:
        """计算夏普比率"""
        if volatility == 0:
            return 0.0
        
        return (annual_return - risk_free_rate) / volatility
    
    def _calculate_sortino_ratio(self, returns: pd.Series, annual_return: float, risk_free_rate: float = 0.03) -> float:
        """计算索提诺比率"""
        if len(returns) < 2:
            return 0.0
        
        downside_returns = returns[returns < 0]
        if len(downside_returns) == 0:
            return float('inf') if annual_return > risk_free_rate else 0.0
        
        downside_volatility = downside_returns.std() * np.sqrt(252)
        if downside_volatility == 0:
            return 0.0
        
        return (annual_return - risk_free_rate) / downside_volatility
    
    def _calculate_calmar_ratio(self, annual_return: float, max_drawdown: float) -> float:
        """计算卡玛比率"""
        if max_drawdown == 0:
            return float('inf') if annual_return > 0 else 0.0
        
        return annual_return / abs(max_drawdown)
    
    def _calculate_information_ratio(self, returns: pd.Series) -> float:
        """计算信息比率"""
        if self.benchmark_data is None or len(returns) < 2:
            return 0.0
        
        # 这里需要基准数据来计算，暂时返回0
        return 0.0
    
    def _calculate_trading_metrics(self) -> Dict[str, Any]:
        """计算交易相关指标"""
        if self.trades_df.empty:
            return {
                'total_trades': 0,
                'win_rate': 0.0,
                'profit_loss_ratio': 0.0,
                'avg_holding_period': 0.0,
                'turnover_rate': 0.0
            }
        
        # 总交易次数
        total_trades = len(self.trades_df)
        
        # 计算每笔交易的盈亏
        buy_trades = self.trades_df[self.trades_df['side'] == 'buy'].copy()
        sell_trades = self.trades_df[self.trades_df['side'] == 'sell'].copy()
        
        # 匹配买卖交易计算盈亏
        trade_pnl = []
        holding_periods = []
        
        for symbol in self.trades_df['symbol'].unique():
            symbol_buys = buy_trades[buy_trades['symbol'] == symbol].sort_values('timestamp')
            symbol_sells = sell_trades[sell_trades['symbol'] == symbol].sort_values('timestamp')
            
            position = 0
            cost_basis = 0
            
            for _, trade in pd.concat([symbol_buys, symbol_sells]).sort_values('timestamp').iterrows():
                if trade['side'] == 'buy':
                    if position == 0:
                        cost_basis = trade['price']
                    else:
                        # 加权平均成本
                        total_cost = cost_basis * position + trade['price'] * trade['quantity']
                        position += trade['quantity']
                        cost_basis = total_cost / position
                    position += trade['quantity']
                else:  # sell
                    if position > 0:
                        pnl = (trade['price'] - cost_basis) * trade['quantity']
                        trade_pnl.append(pnl)
                        position -= trade['quantity']
                        
                        # 计算持仓时间（简化处理）
                        if not symbol_buys.empty:
                            last_buy = symbol_buys[symbol_buys['timestamp'] <= trade['timestamp']].iloc[-1]
                            holding_period = (trade['timestamp'] - last_buy['timestamp']).days
                            holding_periods.append(holding_period)
        
        # 胜率
        winning_trades = len([pnl for pnl in trade_pnl if pnl > 0])
        win_rate = winning_trades / max(len(trade_pnl), 1)
        
        # 盈亏比
        winning_pnl = sum([pnl for pnl in trade_pnl if pnl > 0])
        losing_pnl = abs(sum([pnl for pnl in trade_pnl if pnl < 0]))
        profit_loss_ratio = winning_pnl / max(losing_pnl, 1)
        
        # 平均持仓期
        avg_holding_period = np.mean(holding_periods) if holding_periods else 0.0
        
        # 换手率（简化计算）
        if not self.equity_df.empty:
            total_trade_value = self.trades_df['price'] * self.trades_df['quantity']
            avg_portfolio_value = self.equity_df['total_value'].mean()
            turnover_rate = total_trade_value.sum() / (avg_portfolio_value * 2)  # 除以2因为买卖都算
        else:
            turnover_rate = 0.0
        
        return {
            'total_trades': total_trades,
            'win_rate': win_rate,
            'profit_loss_ratio': profit_loss_ratio,
            'avg_holding_period': avg_holding_period,
            'turnover_rate': turnover_rate
        }
    
    def _calculate_benchmark_metrics(self, returns: pd.Series) -> Tuple[Optional[float], Optional[float], Optional[float]]:
        """计算相对基准的指标"""
        if self.benchmark_data is None or len(returns) < 10:
            return None, None, None
        
        # 这里需要基准数据来计算beta、alpha、跟踪误差
        # 暂时返回None
        return None, None, None
    
    def generate_performance_report(self, save_path: Optional[str] = None) -> str:
        """
        生成详细的业绩报告
        
        Args:
            save_path: 保存路径（可选）
            
        Returns:
            str: 报告内容
        """
        logger.info("生成详细业绩报告...")
        
        metrics = self.calculate_comprehensive_metrics()
        
        # 生成报告内容
        report = self._format_performance_report(metrics)
        
        # 保存报告
        if save_path:
            with open(save_path, 'w', encoding='utf-8') as f:
                f.write(report)
            logger.info(f"业绩报告已保存到：{save_path}")
        
        return report
    
    def _format_performance_report(self, metrics: PerformanceMetrics) -> str:
        """格式化业绩报告"""
        report = f"""
# 量化交易策略回测报告

## 报告生成时间
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 1. 收益指标
- **总收益率**: {metrics.total_return:.2%}
- **年化收益率**: {metrics.annual_return:.2%}
- **月化收益率**: {metrics.monthly_return:.2%}
- **日均收益率**: {metrics.daily_return_mean:.4%}

## 2. 风险指标
- **年化波动率**: {metrics.volatility:.2%}
- **最大回撤**: {metrics.max_drawdown:.2%}
- **95% VaR**: {metrics.var_95:.4%}
- **95% CVaR**: {metrics.cvar_95:.4%}

## 3. 风险调整收益指标
- **夏普比率**: {metrics.sharpe_ratio:.3f}
- **索提诺比率**: {metrics.sortino_ratio:.3f}
- **卡玛比率**: {metrics.calmar_ratio:.3f}
- **信息比率**: {metrics.information_ratio:.3f}

## 4. 交易统计
- **总交易次数**: {metrics.total_trades}
- **胜率**: {metrics.win_rate:.2%}
- **盈亏比**: {metrics.profit_loss_ratio:.2f}
- **平均持仓期**: {metrics.avg_holding_period:.1f} 天
- **换手率**: {metrics.turnover_rate:.2f}

## 5. 基准比较指标
"""
        
        if metrics.beta is not None:
            report += f"- **Beta系数**: {metrics.beta:.3f}\n"
        if metrics.alpha is not None:
            report += f"- **Alpha**: {metrics.alpha:.4%}\n"
        if metrics.tracking_error is not None:
            report += f"- **跟踪误差**: {metrics.tracking_error:.4%}\n"
        
        if all(x is None for x in [metrics.beta, metrics.alpha, metrics.tracking_error]):
            report += "- 未提供基准数据，无法计算基准比较指标\n"
        
        # 添加风险评估
        report += f"""
## 6. 风险评估

### 收益风险特征
- 策略年化收益率为 {metrics.annual_return:.2%}，年化波动率为 {metrics.volatility:.2%}
- 夏普比率为 {metrics.sharpe_ratio:.3f}，{'表现优秀' if metrics.sharpe_ratio > 1.5 else '表现良好' if metrics.sharpe_ratio > 1.0 else '表现一般' if metrics.sharpe_ratio > 0.5 else '表现较差'}
- 最大回撤为 {metrics.max_drawdown:.2%}，{'风险较低' if abs(metrics.max_drawdown) < 0.1 else '风险适中' if abs(metrics.max_drawdown) < 0.2 else '风险较高'}

### 交易特征分析
- 胜率为 {metrics.win_rate:.2%}，盈亏比为 {metrics.profit_loss_ratio:.2f}
- {'交易频率较高' if metrics.turnover_rate > 5 else '交易频率适中' if metrics.turnover_rate > 2 else '交易频率较低'}，换手率为 {metrics.turnover_rate:.2f}
- 平均持仓期为 {metrics.avg_holding_period:.1f} 天

## 7. 策略评价

### 优势
"""
        
        # 添加策略优势分析
        advantages = []
        if metrics.sharpe_ratio > 1.0:
            advantages.append("- 风险调整后收益表现良好")
        if metrics.win_rate > 0.5:
            advantages.append("- 胜率超过50%，交易成功率较高")
        if metrics.profit_loss_ratio > 1.5:
            advantages.append("- 盈亏比较好，盈利交易平均收益高于亏损交易")
        if abs(metrics.max_drawdown) < 0.15:
            advantages.append("- 最大回撤控制较好，风险管理有效")
        
        if advantages:
            report += "\n".join(advantages) + "\n"
        else:
            report += "- 策略表现有待改进\n"
        
        # 添加策略劣势分析
        report += "\n### 需要改进的方面\n"
        
        weaknesses = []
        if metrics.sharpe_ratio < 0.5:
            weaknesses.append("- 夏普比率偏低，需要提高风险调整后收益")
        if metrics.win_rate < 0.4:
            weaknesses.append("- 胜率偏低，需要优化交易信号质量")
        if abs(metrics.max_drawdown) > 0.25:
            weaknesses.append("- 最大回撤过大，需要加强风险控制")
        if metrics.volatility > 0.3:
            weaknesses.append("- 波动率较高，需要考虑降低仓位或优化策略")
        
        if weaknesses:
            report += "\n".join(weaknesses) + "\n"
        else:
            report += "- 策略表现良好，继续保持\n"
        
        return report
    
    def plot_performance_charts(self, save_dir: Optional[str] = None) -> Dict[str, str]:
        """
        绘制业绩分析图表
        
        Args:
            save_dir: 图表保存目录
            
        Returns:
            Dict[str, str]: 图表文件路径
        """
        if self.equity_df.empty:
            logger.warning("权益曲线数据为空，无法绘制图表")
            return {}
        
        logger.info("开始绘制业绩分析图表...")
        
        chart_paths = {}
        
        # 设置图表样式
        plt.style.use('seaborn-v0_8')
        
        # 1. 权益曲线图
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
        
        # 权益曲线
        ax1.plot(self.equity_df.index, self.equity_df['total_value'], 
                label='策略净值', linewidth=2, color='blue')
        ax1.set_title('策略权益曲线', fontsize=14, fontweight='bold')
        ax1.set_ylabel('账户总价值', fontsize=12)
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 回撤图
        cumulative = (1 + self.equity_df['daily_return'].fillna(0)).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        
        ax2.fill_between(self.equity_df.index, drawdown, 0, 
                        alpha=0.3, color='red', label='回撤')
        ax2.plot(self.equity_df.index, drawdown, color='red', linewidth=1)
        ax2.set_title('策略回撤曲线', fontsize=14, fontweight='bold')
        ax2.set_ylabel('回撤比例', fontsize=12)
        ax2.set_xlabel('时间', fontsize=12)
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_dir:
            equity_path = f"{save_dir}/equity_curve.png"
            plt.savefig(equity_path, dpi=300, bbox_inches='tight')
            chart_paths['equity_curve'] = equity_path
        
        plt.show()
        
        # 2. 收益分布图
        if len(self.equity_df['daily_return'].dropna()) > 10:
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
            
            returns = self.equity_df['daily_return'].dropna()
            
            # 收益率直方图
            ax1.hist(returns, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
            ax1.axvline(returns.mean(), color='red', linestyle='--', 
                       label=f'均值: {returns.mean():.4f}')
            ax1.set_title('日收益率分布', fontsize=14, fontweight='bold')
            ax1.set_xlabel('日收益率', fontsize=12)
            ax1.set_ylabel('频次', fontsize=12)
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            
            # Q-Q图
            from scipy import stats
            stats.probplot(returns, dist="norm", plot=ax2)
            ax2.set_title('收益率正态性检验 (Q-Q图)', fontsize=14, fontweight='bold')
            ax2.grid(True, alpha=0.3)
            
            plt.tight_layout()
            
            if save_dir:
                returns_path = f"{save_dir}/returns_distribution.png"
                plt.savefig(returns_path, dpi=300, bbox_inches='tight')
                chart_paths['returns_distribution'] = returns_path
            
            plt.show()
        
        # 3. 月度收益热力图
        if len(self.equity_df) > 30:
            monthly_returns = self.equity_df['daily_return'].resample('M').apply(
                lambda x: (1 + x).prod() - 1
            )
            
            if len(monthly_returns) > 1:
                # 创建月度收益矩阵
                monthly_data = []
                for date, ret in monthly_returns.items():
                    monthly_data.append({
                        'year': date.year,
                        'month': date.month,
                        'return': ret
                    })
                
                monthly_df = pd.DataFrame(monthly_data)
                pivot_table = monthly_df.pivot(index='year', columns='month', values='return')
                
                plt.figure(figsize=(12, 8))
                sns.heatmap(pivot_table, annot=True, fmt='.2%', cmap='RdYlGn', 
                           center=0, cbar_kws={'label': '月度收益率'})
                plt.title('月度收益率热力图', fontsize=14, fontweight='bold')
                plt.xlabel('月份', fontsize=12)
                plt.ylabel('年份', fontsize=12)
                
                if save_dir:
                    heatmap_path = f"{save_dir}/monthly_returns_heatmap.png"
                    plt.savefig(heatmap_path, dpi=300, bbox_inches='tight')
                    chart_paths['monthly_heatmap'] = heatmap_path
                
                plt.show()
        
        # 4. 交易分析图
        if not self.trades_df.empty:
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
            
            # 交易次数按月统计
            monthly_trades = self.trades_df.set_index('timestamp').resample('M').size()
            ax1.bar(monthly_trades.index, monthly_trades.values, alpha=0.7, color='lightblue')
            ax1.set_title('月度交易次数', fontsize=12, fontweight='bold')
            ax1.set_ylabel('交易次数')
            ax1.tick_params(axis='x', rotation=45)
            
            # 买卖比例
            side_counts = self.trades_df['side'].value_counts()
            ax2.pie(side_counts.values, labels=side_counts.index, autopct='%1.1f%%', 
                   colors=['lightgreen', 'lightcoral'])
            ax2.set_title('买卖交易比例', fontsize=12, fontweight='bold')
            
            # 交易金额分布
            self.trades_df['trade_value'] = self.trades_df['price'] * self.trades_df['quantity']
            ax3.hist(self.trades_df['trade_value'], bins=30, alpha=0.7, color='gold')
            ax3.set_title('单笔交易金额分布', fontsize=12, fontweight='bold')
            ax3.set_xlabel('交易金额')
            ax3.set_ylabel('频次')
            
            # 股票交易频次
            symbol_counts = self.trades_df['symbol'].value_counts().head(10)
            ax4.barh(range(len(symbol_counts)), symbol_counts.values, color='lightsteelblue')
            ax4.set_yticks(range(len(symbol_counts)))
            ax4.set_yticklabels(symbol_counts.index)
            ax4.set_title('股票交易频次 (Top 10)', fontsize=12, fontweight='bold')
            ax4.set_xlabel('交易次数')
            
            plt.tight_layout()
            
            if save_dir:
                trades_path = f"{save_dir}/trading_analysis.png"
                plt.savefig(trades_path, dpi=300, bbox_inches='tight')
                chart_paths['trading_analysis'] = trades_path
            
            plt.show()
        
        logger.info(f"业绩分析图表绘制完成，共生成 {len(chart_paths)} 个图表")
        return chart_paths
    
    def export_results_to_excel(self, file_path: str):
        """
        导出回测结果到Excel文件
        
        Args:
            file_path: Excel文件路径
        """
        logger.info(f"导出回测结果到Excel：{file_path}")
        
        with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
            # 业绩指标
            metrics = self.calculate_comprehensive_metrics()
            metrics_dict = {
                '指标': [
                    '总收益率', '年化收益率', '月化收益率', '日均收益率',
                    '年化波动率', '最大回撤', '95% VaR', '95% CVaR',
                    '夏普比率', '索提诺比率', '卡玛比率', '信息比率',
                    '总交易次数', '胜率', '盈亏比', '平均持仓期', '换手率'
                ],
                '数值': [
                    f"{metrics.total_return:.2%}",
                    f"{metrics.annual_return:.2%}",
                    f"{metrics.monthly_return:.2%}",
                    f"{metrics.daily_return_mean:.4%}",
                    f"{metrics.volatility:.2%}",
                    f"{metrics.max_drawdown:.2%}",
                    f"{metrics.var_95:.4%}",
                    f"{metrics.cvar_95:.4%}",
                    f"{metrics.sharpe_ratio:.3f}",
                    f"{metrics.sortino_ratio:.3f}",
                    f"{metrics.calmar_ratio:.3f}",
                    f"{metrics.information_ratio:.3f}",
                    metrics.total_trades,
                    f"{metrics.win_rate:.2%}",
                    f"{metrics.profit_loss_ratio:.2f}",
                    f"{metrics.avg_holding_period:.1f}天",
                    f"{metrics.turnover_rate:.2f}"
                ]
            }
            
            pd.DataFrame(metrics_dict).to_excel(writer, sheet_name='业绩指标', index=False)
            
            # 权益曲线
            if not self.equity_df.empty:
                self.equity_df.to_excel(writer, sheet_name='权益曲线')
            
            # 交易记录
            if not self.trades_df.empty:
                self.trades_df.to_excel(writer, sheet_name='交易记录', index=False)
            
            # 最终持仓
            if 'final_positions' in self.results:
                positions_data = []
                for symbol, pos_info in self.results['final_positions'].items():
                    positions_data.append({
                        '股票代码': symbol,
                        '持仓数量': pos_info['quantity'],
                        '平均成本': pos_info['avg_cost'],
                        '市值': pos_info['market_value'],
                        '未实现盈亏': pos_info['unrealized_pnl']
                    })
                
                if positions_data:
                    pd.DataFrame(positions_data).to_excel(writer, sheet_name='最终持仓', index=False)
        
        logger.info("Excel导出完成")


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    # 创建模拟回测结果
    dates = pd.date_range('2024-01-01', '2024-03-31', freq='D')
    np.random.seed(42)
    
    # 模拟权益曲线
    returns = np.random.normal(0.001, 0.02, len(dates))
    values = [1000000]
    for ret in returns[1:]:
        values.append(values[-1] * (1 + ret))
    
    equity_curve = []
    for i, (date, value) in enumerate(zip(dates, values)):
        equity_curve.append({
            'timestamp': date,
            'total_value': value,
            'cash': value * 0.1,
            'positions_value': value * 0.9,
            'unrealized_pnl': (value - 1000000) * 0.8,
            'realized_pnl': (value - 1000000) * 0.2
        })
    
    # 模拟交易记录
    trades = []
    for i in range(50):
        trades.append({
            'timestamp': dates[i % len(dates)],
            'symbol': f'00000{i%5+1}.SZ',
            'side': 'buy' if i % 2 == 0 else 'sell',
            'quantity': 1000 + i * 100,
            'price': 10 + np.random.random() * 5,
            'commission': 15 + np.random.random() * 10,
            'strategy_id': 'test_strategy'
        })
    
    # 创建回测结果
    results = {
        'equity_curve': equity_curve,
        'trades': trades,
        'final_positions': {
            '000001.SZ': {
                'quantity': 2000,
                'avg_cost': 12.5,
                'market_value': 25000,
                'unrealized_pnl': 1000
            }
        }
    }
    
    # 创建分析器
    analyzer = PerformanceAnalyzer(results)
    
    # 计算指标
    metrics = analyzer.calculate_comprehensive_metrics()
    print(f"年化收益率: {metrics.annual_return:.2%}")
    print(f"夏普比率: {metrics.sharpe_ratio:.3f}")
    print(f"最大回撤: {metrics.max_drawdown:.2%}")
    
    # 生成报告
    report = analyzer.generate_performance_report()
    print("\n" + "="*50)
    print(report)
    
    # 绘制图表
    charts = analyzer.plot_performance_charts()
    print(f"生成图表: {list(charts.keys())}")
    
    # 导出Excel
    analyzer.export_results_to_excel("test_backtest_results.xlsx")
    print("结果已导出到Excel文件")