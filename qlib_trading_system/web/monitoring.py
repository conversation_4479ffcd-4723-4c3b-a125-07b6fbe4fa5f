# -*- coding: utf-8 -*-
"""
系统监控模块

提供系统状态监控、性能指标、日志查看等功能
"""

import logging
import psutil
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from fastapi import APIRouter, HTTPException, Depends, Query
from pydantic import BaseModel
import asyncio

from qlib_trading_system.web.auth import get_current_user, require_permission

logger = logging.getLogger(__name__)

# 创建监控路由
monitoring_router = APIRouter()

class SystemMetrics(BaseModel):
    """系统指标模型"""
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    network_io: Dict[str, int]
    process_count: int
    uptime: str
    timestamp: datetime

class TradingMetrics(BaseModel):
    """交易指标模型"""
    total_trades: int
    successful_trades: int
    failed_trades: int
    success_rate: float
    total_pnl: float
    daily_pnl: float
    max_drawdown: float
    sharpe_ratio: float
    win_rate: float
    avg_holding_time: float  # 分钟

class AlertLevel(BaseModel):
    """告警级别模型"""
    level: str  # INFO, WARNING, ERROR, CRITICAL
    message: str
    timestamp: datetime
    source: str
    resolved: bool = False

class LogEntry(BaseModel):
    """日志条目模型"""
    timestamp: datetime
    level: str
    logger_name: str
    message: str
    module: str

class MonitoringManager:
    """监控管理器"""
    
    def __init__(self):
        self.alerts = []
        self.metrics_history = []
        self.start_time = datetime.now()
        
        # 初始化一些模拟数据
        self._init_mock_data()
    
    def _init_mock_data(self):
        """初始化模拟数据"""
        # 添加一些模拟告警
        self.alerts = [
            AlertLevel(
                level="WARNING",
                message="内存使用率超过80%",
                timestamp=datetime.now() - timedelta(minutes=30),
                source="system_monitor"
            ),
            AlertLevel(
                level="INFO",
                message="交易系统启动成功",
                timestamp=datetime.now() - timedelta(hours=2),
                source="trading_system",
                resolved=True
            ),
            AlertLevel(
                level="ERROR",
                message="数据源连接失败，已切换到备用数据源",
                timestamp=datetime.now() - timedelta(minutes=15),
                source="data_collector"
            )
        ]
    
    def get_system_metrics(self) -> SystemMetrics:
        """获取系统指标"""
        try:
            # 获取CPU使用率
            cpu_usage = psutil.cpu_percent(interval=1)
            
            # 获取内存使用率
            memory = psutil.virtual_memory()
            memory_usage = memory.percent
            
            # 获取磁盘使用率
            disk = psutil.disk_usage('/')
            disk_usage = (disk.used / disk.total) * 100
            
            # 获取网络IO
            network = psutil.net_io_counters()
            network_io = {
                "bytes_sent": network.bytes_sent,
                "bytes_recv": network.bytes_recv
            }
            
            # 获取进程数量
            process_count = len(psutil.pids())
            
            # 计算运行时间
            uptime_seconds = (datetime.now() - self.start_time).total_seconds()
            uptime = str(timedelta(seconds=int(uptime_seconds)))
            
            return SystemMetrics(
                cpu_usage=cpu_usage,
                memory_usage=memory_usage,
                disk_usage=disk_usage,
                network_io=network_io,
                process_count=process_count,
                uptime=uptime,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"获取系统指标错误: {e}")
            # 返回默认值
            return SystemMetrics(
                cpu_usage=0.0,
                memory_usage=0.0,
                disk_usage=0.0,
                network_io={"bytes_sent": 0, "bytes_recv": 0},
                process_count=0,
                uptime="0:00:00",
                timestamp=datetime.now()
            )
    
    def get_trading_metrics(self) -> TradingMetrics:
        """获取交易指标"""
        try:
            # 模拟交易指标数据
            return TradingMetrics(
                total_trades=156,
                successful_trades=140,
                failed_trades=16,
                success_rate=89.7,
                total_pnl=15800.0,
                daily_pnl=1250.0,
                max_drawdown=-8.5,
                sharpe_ratio=1.85,
                win_rate=75.6,
                avg_holding_time=25.6
            )
        except Exception as e:
            logger.error(f"获取交易指标错误: {e}")
            return TradingMetrics(
                total_trades=0,
                successful_trades=0,
                failed_trades=0,
                success_rate=0.0,
                total_pnl=0.0,
                daily_pnl=0.0,
                max_drawdown=0.0,
                sharpe_ratio=0.0,
                win_rate=0.0,
                avg_holding_time=0.0
            )
    
    def get_alerts(self, level: Optional[str] = None, limit: int = 50) -> List[AlertLevel]:
        """获取告警信息"""
        try:
            alerts = self.alerts.copy()
            
            # 按级别过滤
            if level:
                alerts = [alert for alert in alerts if alert.level == level]
            
            # 按时间排序，最新的在前
            alerts.sort(key=lambda x: x.timestamp, reverse=True)
            
            return alerts[:limit]
        except Exception as e:
            logger.error(f"获取告警信息错误: {e}")
            return []
    
    def add_alert(self, level: str, message: str, source: str):
        """添加告警"""
        try:
            alert = AlertLevel(
                level=level,
                message=message,
                timestamp=datetime.now(),
                source=source
            )
            self.alerts.append(alert)
            
            # 保持告警数量在合理范围内
            if len(self.alerts) > 1000:
                self.alerts = self.alerts[-500:]
                
            logger.info(f"添加告警: {level} - {message}")
        except Exception as e:
            logger.error(f"添加告警错误: {e}")
    
    def resolve_alert(self, alert_index: int) -> bool:
        """解决告警"""
        try:
            if 0 <= alert_index < len(self.alerts):
                self.alerts[alert_index].resolved = True
                return True
            return False
        except Exception as e:
            logger.error(f"解决告警错误: {e}")
            return False
    
    def get_logs(self, level: Optional[str] = None, limit: int = 100) -> List[LogEntry]:
        """获取日志条目"""
        try:
            # 模拟日志数据
            mock_logs = [
                LogEntry(
                    timestamp=datetime.now() - timedelta(seconds=5),
                    level="INFO",
                    logger_name="trading_system",
                    message="交易信号生成成功: 000001.SZ BUY 置信度0.85",
                    module="signal_generator"
                ),
                LogEntry(
                    timestamp=datetime.now() - timedelta(seconds=10),
                    level="DEBUG",
                    logger_name="data_collector",
                    message="接收到实时行情数据: 000001.SZ 价格13.20",
                    module="data_collector"
                ),
                LogEntry(
                    timestamp=datetime.now() - timedelta(seconds=15),
                    level="WARNING",
                    logger_name="risk_manager",
                    message="持仓集中度较高，当前单股占比85%",
                    module="risk_manager"
                ),
                LogEntry(
                    timestamp=datetime.now() - timedelta(seconds=20),
                    level="ERROR",
                    logger_name="data_collector",
                    message="米筐数据源连接失败，错误代码: CONNECTION_TIMEOUT",
                    module="data_collector"
                ),
                LogEntry(
                    timestamp=datetime.now() - timedelta(seconds=25),
                    level="INFO",
                    logger_name="order_manager",
                    message="订单执行成功: ORD001 买入 000001.SZ 100股",
                    module="order_manager"
                )
            ]
            
            # 按级别过滤
            if level:
                mock_logs = [log for log in mock_logs if log.level == level]
            
            return mock_logs[:limit]
            
        except Exception as e:
            logger.error(f"获取日志错误: {e}")
            return []

# 全局监控管理器实例
monitoring_manager = MonitoringManager()

@monitoring_router.get("/system")
async def get_system_metrics(current_user: Dict = Depends(require_permission("view_monitoring"))):
    """获取系统指标"""
    try:
        metrics = monitoring_manager.get_system_metrics()
        return {
            "metrics": metrics.dict(),
            "message": "系统指标获取成功"
        }
    except Exception as e:
        logger.error(f"获取系统指标错误: {e}")
        raise HTTPException(status_code=500, detail="获取系统指标失败")

@monitoring_router.get("/trading")
async def get_trading_metrics(current_user: Dict = Depends(require_permission("view_monitoring"))):
    """获取交易指标"""
    try:
        metrics = monitoring_manager.get_trading_metrics()
        return {
            "metrics": metrics.dict(),
            "message": "交易指标获取成功"
        }
    except Exception as e:
        logger.error(f"获取交易指标错误: {e}")
        raise HTTPException(status_code=500, detail="获取交易指标失败")

@monitoring_router.get("/alerts")
async def get_alerts(
    level: Optional[str] = Query(None, description="告警级别过滤"),
    limit: int = Query(50, description="返回数量限制"),
    current_user: Dict = Depends(require_permission("view_monitoring"))
):
    """获取告警信息"""
    try:
        alerts = monitoring_manager.get_alerts(level, limit)
        return {
            "alerts": [alert.dict() for alert in alerts],
            "total": len(alerts),
            "message": "告警信息获取成功"
        }
    except Exception as e:
        logger.error(f"获取告警信息错误: {e}")
        raise HTTPException(status_code=500, detail="获取告警信息失败")

from pydantic import BaseModel

class AlertRequest(BaseModel):
    """告警请求模型"""
    level: str
    message: str
    source: str

@monitoring_router.post("/alerts")
async def add_alert(
    alert_request: AlertRequest,
    current_user: Dict = Depends(require_permission("manage_monitoring"))
):
    """添加告警"""
    try:
        monitoring_manager.add_alert(alert_request.level, alert_request.message, alert_request.source)
        return {"message": "告警添加成功"}
    except Exception as e:
        logger.error(f"添加告警错误: {e}")
        raise HTTPException(status_code=500, detail="添加告警失败")

@monitoring_router.put("/alerts/{alert_index}/resolve")
async def resolve_alert(
    alert_index: int,
    current_user: Dict = Depends(require_permission("manage_monitoring"))
):
    """解决告警"""
    try:
        success = monitoring_manager.resolve_alert(alert_index)
        if success:
            return {"message": "告警已解决"}
        else:
            raise HTTPException(status_code=404, detail="告警不存在")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"解决告警错误: {e}")
        raise HTTPException(status_code=500, detail="解决告警失败")

@monitoring_router.get("/logs")
async def get_logs(
    level: Optional[str] = Query(None, description="日志级别过滤"),
    limit: int = Query(100, description="返回数量限制"),
    current_user: Dict = Depends(require_permission("view_monitoring"))
):
    """获取系统日志"""
    try:
        logs = monitoring_manager.get_logs(level, limit)
        return {
            "logs": [log.dict() for log in logs],
            "total": len(logs),
            "message": "日志获取成功"
        }
    except Exception as e:
        logger.error(f"获取日志错误: {e}")
        raise HTTPException(status_code=500, detail="获取日志失败")

@monitoring_router.get("/health")
async def get_system_health(current_user: Dict = Depends(require_permission("view_monitoring"))):
    """获取系统健康状态"""
    try:
        system_metrics = monitoring_manager.get_system_metrics()
        trading_metrics = monitoring_manager.get_trading_metrics()
        alerts = monitoring_manager.get_alerts(limit=10)
        
        # 计算健康评分
        health_score = 100
        
        # 系统资源检查
        if system_metrics.cpu_usage > 80:
            health_score -= 20
        elif system_metrics.cpu_usage > 60:
            health_score -= 10
            
        if system_metrics.memory_usage > 80:
            health_score -= 20
        elif system_metrics.memory_usage > 60:
            health_score -= 10
            
        if system_metrics.disk_usage > 90:
            health_score -= 15
        elif system_metrics.disk_usage > 80:
            health_score -= 5
        
        # 告警检查
        error_alerts = [alert for alert in alerts if alert.level == "ERROR" and not alert.resolved]
        critical_alerts = [alert for alert in alerts if alert.level == "CRITICAL" and not alert.resolved]
        
        health_score -= len(error_alerts) * 5
        health_score -= len(critical_alerts) * 10
        
        # 交易性能检查
        if trading_metrics.success_rate < 70:
            health_score -= 15
        elif trading_metrics.success_rate < 80:
            health_score -= 5
        
        health_score = max(0, min(100, health_score))
        
        # 确定健康状态
        if health_score >= 90:
            health_status = "优秀"
            status_color = "success"
        elif health_score >= 70:
            health_status = "良好"
            status_color = "info"
        elif health_score >= 50:
            health_status = "一般"
            status_color = "warning"
        else:
            health_status = "较差"
            status_color = "danger"
        
        return {
            "health": {
                "score": health_score,
                "status": health_status,
                "status_color": status_color,
                "system_metrics": system_metrics.dict(),
                "trading_metrics": trading_metrics.dict(),
                "active_alerts": len([alert for alert in alerts if not alert.resolved]),
                "error_alerts": len(error_alerts),
                "critical_alerts": len(critical_alerts)
            },
            "message": "系统健康状态获取成功"
        }
        
    except Exception as e:
        logger.error(f"获取系统健康状态错误: {e}")
        raise HTTPException(status_code=500, detail="获取系统健康状态失败")
