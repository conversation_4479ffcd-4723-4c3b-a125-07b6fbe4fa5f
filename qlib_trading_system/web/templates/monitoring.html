{% extends "base.html" %}

{% block title %}系统监控 - Qlib交易系统{% endblock %}

{% block content %}
<div class="fade-in">
    <h1 style="margin-bottom: 2rem;">系统监控与运维</h1>
    
    <!-- 系统状态概览 -->
    <div class="card">
        <div class="card-header">
            <span>系统状态概览</span>
            <div style="float: right;">
                <button class="btn btn-info btn-sm" onclick="loadSystemMetrics()">刷新数据</button>
            </div>
        </div>
        <div class="card-body">
            <div id="system-metrics">
                <div class="row">
                    <div class="col-3">
                        <div class="card">
                            <div class="card-body">
                                <div class="metric-value">
                                    <div class="loading"></div>
                                </div>
                                <div class="metric-label">加载中...</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="card">
                            <div class="card-body">
                                <div class="metric-value">
                                    <div class="loading"></div>
                                </div>
                                <div class="metric-label">加载中...</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="card">
                            <div class="card-body">
                                <div class="metric-value">
                                    <div class="loading"></div>
                                </div>
                                <div class="metric-label">加载中...</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="card">
                            <div class="card-body">
                                <div class="metric-value">
                                    <div class="loading"></div>
                                </div>
                                <div class="metric-label">加载中...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- 交易指标 -->
        <div class="col-6">
            <div class="card">
                <div class="card-header">交易性能指标</div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="metric-value positive">156</div>
                            <div class="metric-label">总交易次数</div>
                        </div>
                        <div class="col-6">
                            <div class="metric-value positive">89.7%</div>
                            <div class="metric-label">成功率</div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="row">
                        <div class="col-6">
                            <div class="metric-value positive">+15.8%</div>
                            <div class="metric-label">总收益率</div>
                        </div>
                        <div class="col-6">
                            <div class="metric-value">1.85</div>
                            <div class="metric-label">夏普比率</div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="row">
                        <div class="col-6">
                            <div class="metric-value negative">-8.5%</div>
                            <div class="metric-label">最大回撤</div>
                        </div>
                        <div class="col-6">
                            <div class="metric-value">25.6分钟</div>
                            <div class="metric-label">平均持仓时间</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 系统告警 -->
        <div class="col-6">
            <div class="card">
                <div class="card-header">系统告警</div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <strong>WARNING</strong> - 内存使用率超过80%
                        <div style="font-size: 0.8rem; color: #666;">30分钟前 | system_monitor</div>
                    </div>
                    
                    <div class="alert alert-danger">
                        <strong>ERROR</strong> - 数据源连接失败，已切换到备用数据源
                        <div style="font-size: 0.8rem; color: #666;">15分钟前 | data_collector</div>
                    </div>
                    
                    <div class="alert alert-info">
                        <strong>INFO</strong> - 交易系统启动成功
                        <div style="font-size: 0.8rem; color: #666;">2小时前 | trading_system</div>
                    </div>
                    
                    <div style="text-align: center; margin-top: 1rem;">
                        <button class="btn btn-primary btn-sm" onclick="loadAlerts()">查看更多告警</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 模型状态 -->
    <div class="card">
        <div class="card-header">AI模型状态</div>
        <div class="card-body">
            <div class="row">
                <div class="col-6">
                    <div class="card">
                        <div class="card-header">股票筛选模型</div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-4">
                                    <div class="metric-value">
                                        <span class="status-indicator status-online"></span>
                                        运行中
                                    </div>
                                    <div class="metric-label">模型状态</div>
                                </div>
                                <div class="col-4">
                                    <div class="metric-value">92.5%</div>
                                    <div class="metric-label">预测准确率</div>
                                </div>
                                <div class="col-4">
                                    <div class="metric-value">2024-01-15</div>
                                    <div class="metric-label">最后训练</div>
                                </div>
                            </div>
                            
                            <div style="margin-top: 1rem;">
                                <button class="btn btn-success btn-sm">重新训练</button>
                                <button class="btn btn-info btn-sm">查看详情</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-6">
                    <div class="card">
                        <div class="card-header">日内交易模型</div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-4">
                                    <div class="metric-value">
                                        <span class="status-indicator status-online"></span>
                                        运行中
                                    </div>
                                    <div class="metric-label">模型状态</div>
                                </div>
                                <div class="col-4">
                                    <div class="metric-value">87.3%</div>
                                    <div class="metric-label">信号准确率</div>
                                </div>
                                <div class="col-4">
                                    <div class="metric-value">15:30:25</div>
                                    <div class="metric-label">最后更新</div>
                                </div>
                            </div>
                            
                            <div style="margin-top: 1rem;">
                                <button class="btn btn-success btn-sm">重新训练</button>
                                <button class="btn btn-info btn-sm">查看详情</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 数据源状态 -->
    <div class="card">
        <div class="card-header">数据源状态</div>
        <div class="card-body">
            <div class="row">
                <div class="col-4">
                    <div class="card">
                        <div class="card-body">
                            <div class="metric-value">
                                <span class="status-indicator status-online"></span>
                                iTick
                            </div>
                            <div class="metric-label">主数据源</div>
                            <div style="margin-top: 0.5rem;">
                                <small>延迟: 50ms | 连接正常</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-4">
                    <div class="card">
                        <div class="card-body">
                            <div class="metric-value">
                                <span class="status-indicator status-warning"></span>
                                聚宽
                            </div>
                            <div class="metric-label">备用数据源</div>
                            <div style="margin-top: 0.5rem;">
                                <small>延迟: 200ms | 限流中</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-4">
                    <div class="card">
                        <div class="card-body">
                            <div class="metric-value">
                                <span class="status-indicator status-offline"></span>
                                米筐
                            </div>
                            <div class="metric-label">备用数据源</div>
                            <div style="margin-top: 0.5rem;">
                                <small>连接失败 | 正在重试</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 系统日志 -->
    <div class="card">
        <div class="card-header">
            <span>系统日志</span>
            <div style="float: right;">
                <select id="log-level-filter" class="form-control" style="width: auto; display: inline-block;">
                    <option value="">全部级别</option>
                    <option value="DEBUG">DEBUG</option>
                    <option value="INFO">INFO</option>
                    <option value="WARNING">WARNING</option>
                    <option value="ERROR">ERROR</option>
                </select>
                <button class="btn btn-info btn-sm" onclick="loadLogs()">刷新日志</button>
            </div>
        </div>
        <div class="card-body">
            <div style="max-height: 400px; overflow-y: auto; background: #f8f9fa; padding: 1rem; border-radius: 5px; font-family: monospace; font-size: 0.9rem;">
                <div class="log-entry">
                    <span class="log-time">2024-01-15 15:30:25</span>
                    <span class="badge badge-info">INFO</span>
                    <span class="log-module">trading_system</span>
                    <span class="log-message">交易信号生成成功: 000001.SZ BUY 置信度0.85</span>
                </div>
                
                <div class="log-entry">
                    <span class="log-time">2024-01-15 15:30:20</span>
                    <span class="badge badge-success">DEBUG</span>
                    <span class="log-module">data_collector</span>
                    <span class="log-message">接收到实时行情数据: 000001.SZ 价格13.20</span>
                </div>
                
                <div class="log-entry">
                    <span class="log-time">2024-01-15 15:30:15</span>
                    <span class="badge badge-warning">WARNING</span>
                    <span class="log-module">risk_manager</span>
                    <span class="log-message">持仓集中度较高，当前单股占比85%</span>
                </div>
                
                <div class="log-entry">
                    <span class="log-time">2024-01-15 15:30:10</span>
                    <span class="badge badge-danger">ERROR</span>
                    <span class="log-module">data_collector</span>
                    <span class="log-message">米筐数据源连接失败，错误代码: CONNECTION_TIMEOUT</span>
                </div>
                
                <div class="log-entry">
                    <span class="log-time">2024-01-15 15:30:05</span>
                    <span class="badge badge-info">INFO</span>
                    <span class="log-module">order_manager</span>
                    <span class="log-message">订单执行成功: ORD001 买入 000001.SZ 100股</span>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.log-entry {
    margin-bottom: 0.5rem;
    padding: 0.25rem 0;
    border-bottom: 1px solid #eee;
}

.log-time {
    color: #666;
    margin-right: 1rem;
}

.log-module {
    color: #007bff;
    margin: 0 1rem;
    font-weight: bold;
}

.log-message {
    color: #333;
}

.badge {
    font-size: 0.7rem;
    padding: 0.2rem 0.5rem;
    margin-right: 0.5rem;
}

.badge-success {
    background-color: #28a745;
}

.badge-info {
    background-color: #17a2b8;
}

.badge-warning {
    background-color: #ffc107;
    color: #212529;
}

.badge-danger {
    background-color: #dc3545;
}
</style>

<script>
async function loadAlerts() {
    try {
        // 这里应该调用实际的API获取告警信息
        showAlert('告警信息已刷新', 'success');
    } catch (error) {
        showAlert('加载告警信息失败', 'danger');
    }
}

async function loadLogs() {
    try {
        const level = document.getElementById('log-level-filter').value;
        // 这里应该调用实际的API获取日志信息
        showAlert('日志信息已刷新', 'success');
    } catch (error) {
        showAlert('加载日志信息失败', 'danger');
    }
}

// 日志级别过滤
document.getElementById('log-level-filter').addEventListener('change', function() {
    loadLogs();
});
</script>
{% endblock %}