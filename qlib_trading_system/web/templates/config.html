{% extends "base.html" %}

{% block title %}配置管理 - Qlib交易系统{% endblock %}

{% block content %}
<div class="fade-in">
    <h1 style="margin-bottom: 2rem;">策略配置与参数管理</h1>
    
    <!-- 配置导航 -->
    <div class="card">
        <div class="card-body">
            <div class="row">
                <div class="col-3">
                    <button class="btn btn-primary" style="width: 100%;" onclick="showConfigTab('trading')">交易配置</button>
                </div>
                <div class="col-3">
                    <button class="btn btn-info" style="width: 100%;" onclick="showConfigTab('data-source')">数据源配置</button>
                </div>
                <div class="col-3">
                    <button class="btn btn-warning" style="width: 100%;" onclick="showConfigTab('model')">模型配置</button>
                </div>
                <div class="col-3">
                    <button class="btn btn-success" style="width: 100%;" onclick="showConfigTab('system')">系统配置</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 交易配置 -->
    <div id="trading-config" class="config-tab">
        <div class="card">
            <div class="card-header">
                <span>交易配置</span>
                <div style="float: right;">
                    <button class="btn btn-success btn-sm" onclick="saveConfig('trading', 'trading-config-form')">保存配置</button>
                    <button class="btn btn-warning btn-sm" onclick="resetConfig('trading')">重置默认</button>
                </div>
            </div>
            <div class="card-body">
                <form id="trading-config-form">
                    <div class="row">
                        <div class="col-6">
                            <h4>基础配置</h4>
                            
                            <div class="form-group">
                                <label class="form-label">资金模式</label>
                                <select name="capital_mode" class="form-control">
                                    <option value="small">小资金模式 (&lt;50万)</option>
                                    <option value="medium">中等资金模式 (50-500万)</option>
                                    <option value="large">大资金模式 (&gt;500万)</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">总资金 (元)</label>
                                <input type="number" name="total_capital" class="form-control" min="10000" step="1000">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">最大仓位比例</label>
                                <input type="number" name="max_position_size" class="form-control" min="0.1" max="1.0" step="0.1">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">单股最大比例</label>
                                <input type="number" name="max_single_stock_ratio" class="form-control" min="0.1" max="1.0" step="0.1">
                            </div>
                        </div>
                        
                        <div class="col-6">
                            <h4>风险控制</h4>
                            
                            <div class="form-group">
                                <label class="form-label">日最大亏损比例</label>
                                <input type="number" name="max_daily_loss_pct" class="form-control" min="0.001" max="0.1" step="0.001">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">最大回撤比例</label>
                                <input type="number" name="max_drawdown_pct" class="form-control" min="0.1" max="0.5" step="0.01">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">止损比例</label>
                                <input type="number" name="stop_loss_pct" class="form-control" min="0.01" max="0.1" step="0.01">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">持仓数量限制</label>
                                <input type="number" name="position_size_limit" class="form-control" min="1" max="10" step="1">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-6">
                            <h4>T+0交易参数</h4>
                            
                            <div class="form-group">
                                <label class="form-label">
                                    <input type="checkbox" name="enable_t_plus_zero"> 启用T+0交易
                                </label>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">底仓比例</label>
                                <input type="number" name="base_position_ratio" class="form-control" min="0.5" max="0.9" step="0.05">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">T仓比例</label>
                                <input type="number" name="t_position_ratio" class="form-control" min="0.1" max="0.4" step="0.05">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">现金储备比例</label>
                                <input type="number" name="cash_reserve_ratio" class="form-control" min="0.01" max="0.2" step="0.01">
                            </div>
                        </div>
                        
                        <div class="col-6">
                            <h4>模型参数</h4>
                            
                            <div class="form-group">
                                <label class="form-label">股票筛选阈值</label>
                                <input type="number" name="stock_selection_threshold" class="form-control" min="0.1" max="1.0" step="0.1">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">日内交易信心阈值</label>
                                <input type="number" name="intraday_confidence_threshold" class="form-control" min="0.1" max="1.0" step="0.1">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">最小盈利预期</label>
                                <input type="number" name="min_profit_expectation" class="form-control" min="0.001" max="0.1" step="0.001">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- 数据源配置 -->
    <div id="data-source-config" class="config-tab" style="display: none;">
        <div class="card">
            <div class="card-header">
                <span>数据源配置</span>
                <div style="float: right;">
                    <button class="btn btn-success btn-sm" onclick="saveConfig('data-source', 'data-source-config-form')">保存配置</button>
                    <button class="btn btn-warning btn-sm" onclick="resetConfig('data_source')">重置默认</button>
                </div>
            </div>
            <div class="card-body">
                <form id="data-source-config-form">
                    <div class="row">
                        <div class="col-6">
                            <h4>主要数据源</h4>
                            
                            <div class="form-group">
                                <label class="form-label">主数据源</label>
                                <select name="primary_source" class="form-control">
                                    <option value="iTick">iTick (推荐)</option>
                                    <option value="JoinQuant">聚宽</option>
                                    <option value="RiceQuant">米筐</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">iTick API Key</label>
                                <input type="text" name="itick_api_key" class="form-control" placeholder="请输入iTick API Key">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">iTick Secret Key</label>
                                <input type="password" name="itick_secret_key" class="form-control" placeholder="请输入iTick Secret Key">
                            </div>
                        </div>
                        
                        <div class="col-6">
                            <h4>备用数据源</h4>
                            
                            <div class="form-group">
                                <label class="form-label">聚宽用户名</label>
                                <input type="text" name="jq_username" class="form-control" placeholder="聚宽用户名">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">聚宽密码</label>
                                <input type="password" name="jq_password" class="form-control" placeholder="聚宽密码">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">米筐 API Key</label>
                                <input type="text" name="rq_api_key" class="form-control" placeholder="米筐 API Key">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-6">
                            <h4>更新频率</h4>
                            
                            <div class="form-group">
                                <label class="form-label">实时更新间隔 (秒)</label>
                                <input type="number" name="realtime_update_interval" class="form-control" min="1" max="60" step="1">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">日度更新时间</label>
                                <input type="time" name="daily_update_time" class="form-control">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- 模型配置 -->
    <div id="model-config" class="config-tab" style="display: none;">
        <div class="card">
            <div class="card-header">
                <span>模型配置</span>
                <div style="float: right;">
                    <button class="btn btn-success btn-sm" onclick="saveConfig('model', 'model-config-form')">保存配置</button>
                    <button class="btn btn-warning btn-sm" onclick="resetConfig('model')">重置默认</button>
                </div>
            </div>
            <div class="card-body">
                <form id="model-config-form">
                    <div class="row">
                        <div class="col-6">
                            <h4>股票筛选模型</h4>
                            
                            <div class="form-group">
                                <label class="form-label">模型类型</label>
                                <select name="stock_selection_model_type" class="form-control">
                                    <option value="LightGBM">LightGBM (推荐)</option>
                                    <option value="XGBoost">XGBoost</option>
                                    <option value="CatBoost">CatBoost</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">特征重要性阈值</label>
                                <input type="number" name="feature_importance_threshold" class="form-control" min="0.001" max="0.1" step="0.001">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">模型重训练频率</label>
                                <select name="model_retrain_frequency" class="form-control">
                                    <option value="daily">每日</option>
                                    <option value="weekly">每周</option>
                                    <option value="monthly">每月</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="col-6">
                            <h4>日内交易模型</h4>
                            
                            <div class="form-group">
                                <label class="form-label">模型类型</label>
                                <select name="intraday_model_type" class="form-control">
                                    <option value="Transformer">Transformer (推荐)</option>
                                    <option value="LSTM">LSTM</option>
                                    <option value="CNN">CNN</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">预测时间窗口 (分钟)</label>
                                <input type="number" name="prediction_horizon" class="form-control" min="5" max="120" step="5">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-6">
                            <h4>特征工程</h4>
                            
                            <div class="form-group">
                                <label class="form-label">技术指标数量</label>
                                <input type="number" name="technical_indicators_count" class="form-control" min="50" max="300" step="10">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">历史回看天数</label>
                                <input type="number" name="lookback_days" class="form-control" min="30" max="120" step="10">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">特征选择方法</label>
                                <select name="feature_selection_method" class="form-control">
                                    <option value="importance">重要性排序</option>
                                    <option value="correlation">相关性分析</option>
                                    <option value="mutual_info">互信息</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- 系统配置 -->
    <div id="system-config" class="config-tab" style="display: none;">
        <div class="card">
            <div class="card-header">
                <span>系统配置</span>
                <div style="float: right;">
                    <button class="btn btn-success btn-sm" onclick="saveConfig('system', 'system-config-form')">保存配置</button>
                    <button class="btn btn-warning btn-sm" onclick="resetConfig('system')">重置默认</button>
                </div>
            </div>
            <div class="card-body">
                <form id="system-config-form">
                    <div class="row">
                        <div class="col-6">
                            <h4>日志配置</h4>
                            
                            <div class="form-group">
                                <label class="form-label">日志级别</label>
                                <select name="log_level" class="form-control">
                                    <option value="DEBUG">DEBUG</option>
                                    <option value="INFO">INFO</option>
                                    <option value="WARNING">WARNING</option>
                                    <option value="ERROR">ERROR</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">日志保留天数</label>
                                <input type="number" name="log_retention_days" class="form-control" min="7" max="365" step="1">
                            </div>
                        </div>
                        
                        <div class="col-6">
                            <h4>数据库配置</h4>
                            
                            <div class="form-group">
                                <label class="form-label">ClickHouse主机</label>
                                <input type="text" name="clickhouse_host" class="form-control">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">ClickHouse端口</label>
                                <input type="number" name="clickhouse_port" class="form-control" min="1" max="65535">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Redis主机</label>
                                <input type="text" name="redis_host" class="form-control">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Redis端口</label>
                                <input type="number" name="redis_port" class="form-control" min="1" max="65535">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-6">
                            <h4>Web服务配置</h4>
                            
                            <div class="form-group">
                                <label class="form-label">Web服务主机</label>
                                <input type="text" name="web_host" class="form-control">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Web服务端口</label>
                                <input type="number" name="web_port" class="form-control" min="1000" max="65535">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">会话超时 (小时)</label>
                                <input type="number" name="session_timeout" class="form-control" min="1" max="168">
                            </div>
                        </div>
                        
                        <div class="col-6">
                            <h4>监控配置</h4>
                            
                            <div class="form-group">
                                <label class="form-label">
                                    <input type="checkbox" name="enable_email_alerts"> 启用邮件告警
                                </label>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">告警邮箱</label>
                                <input type="email" name="alert_email" class="form-control">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">
                                    <input type="checkbox" name="enable_sms_alerts"> 启用短信告警
                                </label>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">告警手机</label>
                                <input type="tel" name="alert_phone" class="form-control">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function showConfigTab(tabName) {
    // 隐藏所有配置标签
    const tabs = document.querySelectorAll('.config-tab');
    tabs.forEach(tab => tab.style.display = 'none');
    
    // 显示选中的标签
    const selectedTab = document.getElementById(tabName + '-config');
    if (selectedTab) {
        selectedTab.style.display = 'block';
    }
    
    // 更新按钮状态
    const buttons = document.querySelectorAll('.card-body .btn');
    buttons.forEach(btn => btn.classList.remove('btn-primary'));
    buttons.forEach(btn => btn.classList.add('btn-info'));
    
    event.target.classList.remove('btn-info');
    event.target.classList.add('btn-primary');
}

async function resetConfig(configType) {
    if (!confirm('确定要重置配置为默认值吗？')) {
        return;
    }
    
    try {
        await apiRequest(`/api/config/reset/${configType}`, { method: 'POST' });
        showAlert('配置重置成功', 'success');
        loadConfigs(); // 重新加载配置
    } catch (error) {
        showAlert('配置重置失败', 'danger');
    }
}

// 页面加载时显示第一个标签
document.addEventListener('DOMContentLoaded', function() {
    showConfigTab('trading');
});
</script>
{% endblock %}