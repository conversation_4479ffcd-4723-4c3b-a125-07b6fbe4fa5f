{% extends "base.html" %}

{% block title %}交易监控 - Qlib交易系统{% endblock %}

{% block content %}
<div class="fade-in">
    <h1 style="margin-bottom: 2rem;">交易监控与控制</h1>
    
    <!-- 交易控制面板 -->
    <div class="card">
        <div class="card-header">
            <span>交易控制面板</span>
            <div style="float: right;">
                <span id="trading-status-indicator" class="status-indicator status-offline"></span>
                <span id="trading-status-text">加载中...</span>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-3">
                    <button id="start-trading-btn" class="btn btn-success" style="width: 100%;" onclick="startTrading()">
                        启动交易
                    </button>
                </div>
                <div class="col-3">
                    <button id="stop-trading-btn" class="btn btn-danger" style="width: 100%;" onclick="stopTrading()">
                        停止交易
                    </button>
                </div>
                <div class="col-3">
                    <button class="btn btn-warning" style="width: 100%;" onclick="refreshTradingData()">
                        刷新数据
                    </button>
                </div>
                <div class="col-3">
                    <button class="btn btn-info" style="width: 100%;" onclick="showOrderModal()">
                        手动下单
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- 实时持仓 -->
        <div class="col-8">
            <div class="card">
                <div class="card-header">
                    实时持仓
                    <span id="positions-update-time" style="float: right; font-size: 0.9rem; opacity: 0.8;"></span>
                </div>
                <div class="card-body">
                    <table id="positions-table" class="table">
                        <thead>
                            <tr>
                                <th>股票代码</th>
                                <th>股票名称</th>
                                <th>持仓数量</th>
                                <th>成本价</th>
                                <th>现价</th>
                                <th>市值</th>
                                <th>浮动盈亏</th>
                                <th>盈亏比例</th>
                                <th>仓位类型</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="10" style="text-align: center;">
                                    <div class="loading"></div> 加载中...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <!-- 交易统计 -->
        <div class="col-4">
            <div class="card">
                <div class="card-header">今日交易统计</div>
                <div class="card-body">
                    <div id="trading-stats">
                        <div class="row">
                            <div class="col-6">
                                <div class="metric-value" id="total-trades">--</div>
                                <div class="metric-label">总交易次数</div>
                            </div>
                            <div class="col-6">
                                <div class="metric-value" id="success-rate">--</div>
                                <div class="metric-label">成功率</div>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-6">
                                <div class="metric-value" id="daily-pnl">--</div>
                                <div class="metric-label">今日盈亏</div>
                            </div>
                            <div class="col-6">
                                <div class="metric-value" id="total-volume">--</div>
                                <div class="metric-label">成交金额</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- AI模型状态 -->
            <div class="card">
                <div class="card-header">AI模型状态</div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12">
                            <div style="margin-bottom: 1rem;">
                                <strong>股票筛选AI</strong>
                                <span class="badge badge-success" style="float: right;">运行中</span>
                            </div>
                            <div style="margin-bottom: 1rem;">
                                <strong>日内交易AI</strong>
                                <span class="badge badge-success" style="float: right;">运行中</span>
                            </div>
                            <div style="margin-bottom: 1rem;">
                                <strong>风险控制</strong>
                                <span class="badge badge-success" style="float: right;">正常</span>
                            </div>
                            <div>
                                <strong>数据连接</strong>
                                <span class="badge badge-success" style="float: right;">正常</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 订单管理 -->
    <div class="card">
        <div class="card-header">
            订单管理
            <div style="float: right;">
                <select id="order-status-filter" class="form-control" style="display: inline-block; width: auto;" onchange="filterOrders()">
                    <option value="">全部订单</option>
                    <option value="pending">待成交</option>
                    <option value="filled">已成交</option>
                    <option value="cancelled">已撤销</option>
                </select>
            </div>
        </div>
        <div class="card-body">
            <table id="orders-table" class="table">
                <thead>
                    <tr>
                        <th>订单号</th>
                        <th>股票代码</th>
                        <th>股票名称</th>
                        <th>方向</th>
                        <th>类型</th>
                        <th>数量</th>
                        <th>价格</th>
                        <th>已成交</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="11" style="text-align: center;">
                            <div class="loading"></div> 加载中...
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 手动下单模态框 -->
<div id="order-modal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="closeOrderModal()">&times;</span>
        <h2>手动下单</h2>
        <form id="manual-order-form">
            <div class="form-row">
                <div class="form-group">
                    <label class="form-label">股票代码</label>
                    <input type="text" id="order-symbol" class="form-control" placeholder="例如: 000001.SZ" required>
                </div>
                <div class="form-group">
                    <label class="form-label">交易方向</label>
                    <select id="order-side" class="form-control" required>
                        <option value="buy">买入</option>
                        <option value="sell">卖出</option>
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label class="form-label">订单类型</label>
                    <select id="order-type" class="form-control" onchange="togglePriceInput()" required>
                        <option value="market">市价单</option>
                        <option value="limit">限价单</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">数量</label>
                    <input type="number" id="order-quantity" class="form-control" min="1" required>
                </div>
            </div>
            <div class="form-group" id="price-group" style="display: none;">
                <label class="form-label">价格</label>
                <input type="number" id="order-price" class="form-control" step="0.01" min="0">
            </div>
            <div style="text-align: right; margin-top: 2rem;">
                <button type="button" class="btn btn-secondary" onclick="closeOrderModal()">取消</button>
                <button type="submit" class="btn btn-primary">提交订单</button>
            </div>
        </form>
    </div>
</div>

<script>
// 交易页面特定JavaScript
let tradingPageInterval;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    if (window.location.pathname === '/trading') {
        initTradingPageSpecific();
    }
});

function initTradingPageSpecific() {
    loadTradingData();
    
    // 设置定时刷新
    tradingPageInterval = setInterval(loadTradingData, CONFIG.REFRESH_INTERVAL);
    
    // 手动下单表单处理
    const orderForm = document.getElementById('manual-order-form');
    if (orderForm) {
        orderForm.addEventListener('submit', function(e) {
            e.preventDefault();
            submitManualOrder();
        });
    }
}

// 加载交易数据
async function loadTradingData() {
    try {
        // 加载交易状态
        const status = await Trading.getStatus();
        if (status) {
            updateTradingStatusDisplay(status);
        }

        // 加载持仓信息
        const positions = await Trading.getPositions();
        if (positions) {
            updateTradingPositionsTable(positions);
        }

        // 加载订单信息
        const orders = await Trading.getOrders();
        if (orders) {
            updateOrdersTable(orders);
        }

        // 更新时间戳
        const updateTimeElement = document.getElementById('positions-update-time');
        if (updateTimeElement) {
            updateTimeElement.textContent = '更新时间: ' + new Date().toLocaleTimeString();
        }
            
    } catch (error) {
        console.error('Load trading data error:', error);
    }
}

// 更新交易状态显示
function updateTradingStatusDisplay(status) {
    const indicator = document.getElementById('trading-status-indicator');
    const text = document.getElementById('trading-status-text');
    
    if (indicator && text) {
        if (status.is_trading) {
            indicator.className = 'status-indicator status-online';
            text.textContent = '交易中';
        } else {
            indicator.className = 'status-indicator status-offline';
            text.textContent = '已停止';
        }
    }
    
    // 更新统计数据
    const dailyPnlElement = document.getElementById('daily-pnl');
    if (dailyPnlElement) {
        dailyPnlElement.textContent = Utils.formatCurrency(status.daily_pnl);
        dailyPnlElement.className = 'metric-value ' + Utils.getChangeColorClass(status.daily_pnl);
    }
    
    // 更新其他统计数据
    const totalTradesElement = document.getElementById('total-trades');
    if (totalTradesElement) {
        totalTradesElement.textContent = '12'; // 模拟数据
    }
    
    const successRateElement = document.getElementById('success-rate');
    if (successRateElement) {
        successRateElement.textContent = '85%'; // 模拟数据
    }
    
    const totalVolumeElement = document.getElementById('total-volume');
    if (totalVolumeElement) {
        totalVolumeElement.textContent = Utils.formatCurrency(status.total_market_value);
    }
}

// 更新持仓表格
function updateTradingPositionsTable(positions) {
    const tbody = document.querySelector('#positions-table tbody');
    if (!tbody) return;

    if (positions.length === 0) {
        tbody.innerHTML = '<tr><td colspan="10" style="text-align: center;">暂无持仓</td></tr>';
        return;
    }

    tbody.innerHTML = positions.map(position => `
        <tr>
            <td>${position.symbol}</td>
            <td>${position.name}</td>
            <td>${position.quantity}</td>
            <td>${Utils.formatCurrency(position.avg_cost)}</td>
            <td>${Utils.formatCurrency(position.current_price)}</td>
            <td>${Utils.formatCurrency(position.market_value)}</td>
            <td class="${Utils.getChangeColorClass(position.unrealized_pnl)}">
                ${Utils.formatCurrency(position.unrealized_pnl)}
            </td>
            <td class="${Utils.getChangeColorClass(position.unrealized_pnl_pct)}">
                ${Utils.formatPercent(position.unrealized_pnl_pct)}
            </td>
            <td>
                <span class="badge ${position.position_type === 'base' ? 'badge-primary' : 'badge-info'}">
                    ${position.position_type === 'base' ? '底仓' : 'T仓'}
                </span>
            </td>
            <td>
                <button class="btn btn-danger btn-sm" onclick="sellPosition('${position.symbol}', ${position.quantity})">
                    卖出
                </button>
            </td>
        </tr>
    `).join('');
}

// 更新订单表格
function updateOrdersTable(orders) {
    const tbody = document.querySelector('#orders-table tbody');
    if (!tbody) return;

    if (orders.length === 0) {
        tbody.innerHTML = '<tr><td colspan="11" style="text-align: center;">暂无订单</td></tr>';
        return;
    }

    tbody.innerHTML = orders.map(order => `
        <tr>
            <td>${order.order_id}</td>
            <td>${order.symbol}</td>
            <td>${order.name}</td>
            <td>
                <span class="badge ${order.side === 'buy' ? 'badge-success' : 'badge-danger'}">
                    ${order.side === 'buy' ? '买入' : '卖出'}
                </span>
            </td>
            <td>${order.order_type === 'market' ? '市价' : '限价'}</td>
            <td>${order.quantity}</td>
            <td>${order.price ? Utils.formatCurrency(order.price) : '市价'}</td>
            <td>${order.filled_quantity}</td>
            <td>
                <span class="badge ${getOrderStatusBadgeClass(order.status)}">
                    ${getOrderStatusText(order.status)}
                </span>
            </td>
            <td>${new Date(order.create_time).toLocaleString()}</td>
            <td>
                ${order.status === 'pending' ? 
                    `<button class="btn btn-warning btn-sm" onclick="cancelOrder('${order.order_id}')">撤单</button>` : 
                    '--'
                }
            </td>
        </tr>
    `).join('');
}

// 获取订单状态徽章样式
function getOrderStatusBadgeClass(status) {
    switch (status) {
        case 'pending': return 'badge-warning';
        case 'filled': return 'badge-success';
        case 'cancelled': return 'badge-secondary';
        default: return 'badge-info';
    }
}

// 获取订单状态文本
function getOrderStatusText(status) {
    switch (status) {
        case 'pending': return '待成交';
        case 'filled': return '已成交';
        case 'cancelled': return '已撤销';
        default: return status;
    }
}

// 显示下单模态框
function showOrderModal() {
    document.getElementById('order-modal').style.display = 'block';
}

// 关闭下单模态框
function closeOrderModal() {
    document.getElementById('order-modal').style.display = 'none';
    document.getElementById('manual-order-form').reset();
    document.getElementById('price-group').style.display = 'none';
}

// 切换价格输入框显示
function togglePriceInput() {
    const orderType = document.getElementById('order-type').value;
    const priceGroup = document.getElementById('price-group');
    
    if (orderType === 'limit') {
        priceGroup.style.display = 'block';
        document.getElementById('order-price').required = true;
    } else {
        priceGroup.style.display = 'none';
        document.getElementById('order-price').required = false;
    }
}

// 提交手动订单
async function submitManualOrder() {
    const symbol = document.getElementById('order-symbol').value;
    const side = document.getElementById('order-side').value;
    const orderType = document.getElementById('order-type').value;
    const quantity = parseInt(document.getElementById('order-quantity').value);
    const price = orderType === 'limit' ? parseFloat(document.getElementById('order-price').value) : null;
    
    const result = await Trading.placeOrder(symbol, side, quantity, orderType, price);
    if (result) {
        closeOrderModal();
        loadTradingData(); // 刷新数据
    }
}

// 卖出持仓
async function sellPosition(symbol, quantity) {
    Utils.confirm(`确定要卖出 ${symbol} 全部持仓(${quantity}股)吗？`, async function() {
        const result = await Trading.placeOrder(symbol, 'sell', quantity, 'market');
        if (result) {
            loadTradingData(); // 刷新数据
        }
    });
}

// 撤销订单
async function cancelOrder(orderId) {
    Utils.confirm('确定要撤销这个订单吗？', async function() {
        const result = await Trading.cancelOrder(orderId);
        if (result) {
            loadTradingData(); // 刷新数据
        }
    });
}

// 过滤订单
async function filterOrders() {
    const status = document.getElementById('order-status-filter').value;
    const orders = await Trading.getOrders(status);
    if (orders) {
        updateOrdersTable(orders);
    }
}

// 刷新数据
function refreshTradingData() {
    loadTradingData();
    Utils.showMessage('数据已刷新', 'success', 1000);
}

// 页面卸载时清理定时器
window.addEventListener('beforeunload', function() {
    if (tradingPageInterval) {
        clearInterval(tradingPageInterval);
    }
});
</script>
{% endblock %}