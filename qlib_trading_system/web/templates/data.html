{% extends "base.html" %}

{% block title %}数据查询 - Qlib交易系统{% endblock %}

{% block content %}
<div class="fade-in">
    <h1 style="margin-bottom: 2rem;">数据查询与分析工具</h1>
    
    <!-- 股票搜索 -->
    <div class="card">
        <div class="card-header">股票信息查询</div>
        <div class="card-body">
            <div class="row">
                <div class="col-4">
                    <div class="form-group">
                        <label class="form-label">股票代码</label>
                        <input type="text" id="stock-search" class="form-control" placeholder="如: 000001.SZ">
                    </div>
                </div>
                <div class="col-2">
                    <div class="form-group">
                        <label class="form-label">&nbsp;</label>
                        <button class="btn btn-primary" style="width: 100%;" onclick="searchStockInfo()">查询</button>
                    </div>
                </div>
            </div>
            
            <div id="stock-info-result" style="display: none;">
                <hr>
                <div class="row">
                    <div class="col-8">
                        <table class="table">
                            <tr>
                                <th>股票代码</th>
                                <td id="stock-symbol">--</td>
                                <th>股票名称</th>
                                <td id="stock-name">--</td>
                            </tr>
                            <tr>
                                <th>所属行业</th>
                                <td id="stock-industry">--</td>
                                <th>市值 (亿)</th>
                                <td id="stock-market-cap">--</td>
                            </tr>
                            <tr>
                                <th>当前价格</th>
                                <td id="stock-price">--</td>
                                <th>涨跌幅</th>
                                <td id="stock-change">--</td>
                            </tr>
                            <tr>
                                <th>市盈率</th>
                                <td id="stock-pe">--</td>
                                <th>市净率</th>
                                <td id="stock-pb">--</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-4">
                        <div class="card">
                            <div class="card-header">AI评分</div>
                            <div class="card-body">
                                <div class="metric-value" id="ai-score">--</div>
                                <div class="metric-label">爆发潜力评分</div>
                                <div style="margin-top: 1rem;">
                                    <div class="progress" style="height: 20px;">
                                        <div id="score-bar" class="progress-bar" style="width: 0%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 历史数据查询 -->
    <div class="row">
        <div class="col-6">
            <div class="card">
                <div class="card-header">历史价格数据</div>
                <div class="card-body">
                    <div class="form-group">
                        <label class="form-label">股票代码</label>
                        <input type="text" id="price-symbol" class="form-control" placeholder="如: 000001.SZ">
                    </div>
                    
                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <label class="form-label">开始日期</label>
                                <input type="date" id="start-date" class="form-control">
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <label class="form-label">结束日期</label>
                                <input type="date" id="end-date" class="form-control">
                            </div>
                        </div>
                    </div>
                    
                    <button class="btn btn-primary" onclick="queryPriceData()">查询价格数据</button>
                    
                    <div id="price-data-table" style="margin-top: 1rem; display: none;">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>日期</th>
                                    <th>开盘</th>
                                    <th>最高</th>
                                    <th>最低</th>
                                    <th>收盘</th>
                                    <th>成交量</th>
                                </tr>
                            </thead>
                            <tbody id="price-data-tbody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-6">
            <div class="card">
                <div class="card-header">技术指标计算</div>
                <div class="card-body">
                    <div class="form-group">
                        <label class="form-label">股票代码</label>
                        <input type="text" id="indicator-symbol" class="form-control" placeholder="如: 000001.SZ">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">技术指标</label>
                        <select id="indicator-type" class="form-control">
                            <option value="ma">移动平均线 (MA)</option>
                            <option value="macd">MACD</option>
                            <option value="rsi">RSI</option>
                            <option value="kdj">KDJ</option>
                            <option value="boll">布林带</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">计算周期</label>
                        <input type="number" id="indicator-period" class="form-control" value="20" min="5" max="250">
                    </div>
                    
                    <button class="btn btn-primary" onclick="calculateIndicator()">计算指标</button>
                    
                    <div id="indicator-result" style="margin-top: 1rem; display: none;">
                        <canvas id="indicator-chart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 市场概览 -->
    <div class="card">
        <div class="card-header">
            <span>市场概览</span>
            <div style="float: right;">
                <button class="btn btn-info btn-sm" onclick="loadMarketOverview()">刷新数据</button>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-3">
                    <div class="card">
                        <div class="card-body">
                            <div class="metric-value positive">3245.67</div>
                            <div class="metric-label">上证指数</div>
                            <div class="positive">+1.25%</div>
                        </div>
                    </div>
                </div>
                <div class="col-3">
                    <div class="card">
                        <div class="card-body">
                            <div class="metric-value positive">2156.89</div>
                            <div class="metric-label">深证成指</div>
                            <div class="positive">+0.89%</div>
                        </div>
                    </div>
                </div>
                <div class="col-3">
                    <div class="card">
                        <div class="card-body">
                            <div class="metric-value negative">1234.56</div>
                            <div class="metric-label">创业板指</div>
                            <div class="negative">-0.45%</div>
                        </div>
                    </div>
                </div>
                <div class="col-3">
                    <div class="card">
                        <div class="card-body">
                            <div class="metric-value positive">4567.89</div>
                            <div class="metric-label">科创50</div>
                            <div class="positive">+2.15%</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 热门股票 -->
    <div class="row">
        <div class="col-6">
            <div class="card">
                <div class="card-header">涨幅榜</div>
                <div class="card-body">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>股票</th>
                                <th>现价</th>
                                <th>涨幅</th>
                                <th>成交额</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>000001.SZ 平安银行</td>
                                <td>¥13.20</td>
                                <td class="positive">+9.98%</td>
                                <td>15.6亿</td>
                            </tr>
                            <tr>
                                <td>000002.SZ 万科A</td>
                                <td>¥19.50</td>
                                <td class="positive">+8.75%</td>
                                <td>12.3亿</td>
                            </tr>
                            <tr>
                                <td>000858.SZ 五粮液</td>
                                <td>¥168.50</td>
                                <td class="positive">+7.25%</td>
                                <td>28.9亿</td>
                            </tr>
                            <tr>
                                <td>600519.SH 贵州茅台</td>
                                <td>¥1680.00</td>
                                <td class="positive">+6.89%</td>
                                <td>45.2亿</td>
                            </tr>
                            <tr>
                                <td>002415.SZ 海康威视</td>
                                <td>¥32.15</td>
                                <td class="positive">+5.67%</td>
                                <td>18.7亿</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="col-6">
            <div class="card">
                <div class="card-header">跌幅榜</div>
                <div class="card-body">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>股票</th>
                                <th>现价</th>
                                <th>跌幅</th>
                                <th>成交额</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>300059.SZ 东方财富</td>
                                <td>¥15.20</td>
                                <td class="negative">-8.95%</td>
                                <td>22.1亿</td>
                            </tr>
                            <tr>
                                <td>000876.SZ 新希望</td>
                                <td>¥12.80</td>
                                <td class="negative">-7.25%</td>
                                <td>8.9亿</td>
                            </tr>
                            <tr>
                                <td>600036.SH 招商银行</td>
                                <td>¥35.60</td>
                                <td class="negative">-6.78%</td>
                                <td>19.5亿</td>
                            </tr>
                            <tr>
                                <td>600887.SH 伊利股份</td>
                                <td>¥28.90</td>
                                <td class="negative">-5.45%</td>
                                <td>11.2亿</td>
                            </tr>
                            <tr>
                                <td>000725.SZ 京东方A</td>
                                <td>¥3.45</td>
                                <td class="negative">-4.89%</td>
                                <td>25.8亿</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 设置默认日期
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date();
    const oneMonthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
    
    document.getElementById('start-date').value = oneMonthAgo.toISOString().split('T')[0];
    document.getElementById('end-date').value = today.toISOString().split('T')[0];
});

async function searchStockInfo() {
    const symbol = document.getElementById('stock-search').value.trim();
    if (!symbol) {
        showAlert('请输入股票代码', 'warning');
        return;
    }
    
    try {
        // 模拟股票信息查询
        const mockData = {
            symbol: symbol,
            name: '平安银行',
            industry: '银行',
            market_cap: 2580.5,
            current_price: 13.20,
            change_pct: 2.35,
            pe_ratio: 5.8,
            pb_ratio: 0.65,
            ai_score: 0.78
        };
        
        // 更新显示
        document.getElementById('stock-symbol').textContent = mockData.symbol;
        document.getElementById('stock-name').textContent = mockData.name;
        document.getElementById('stock-industry').textContent = mockData.industry;
        document.getElementById('stock-market-cap').textContent = formatNumber(mockData.market_cap);
        document.getElementById('stock-price').textContent = formatCurrency(mockData.current_price);
        
        const changeElement = document.getElementById('stock-change');
        changeElement.textContent = formatPercent(mockData.change_pct);
        changeElement.className = mockData.change_pct >= 0 ? 'positive' : 'negative';
        
        document.getElementById('stock-pe').textContent = formatNumber(mockData.pe_ratio);
        document.getElementById('stock-pb').textContent = formatNumber(mockData.pb_ratio);
        
        // AI评分
        const score = Math.round(mockData.ai_score * 100);
        document.getElementById('ai-score').textContent = score + '分';
        document.getElementById('score-bar').style.width = score + '%';
        
        // 根据评分设置颜色
        const scoreBar = document.getElementById('score-bar');
        if (score >= 80) {
            scoreBar.className = 'progress-bar bg-success';
        } else if (score >= 60) {
            scoreBar.className = 'progress-bar bg-warning';
        } else {
            scoreBar.className = 'progress-bar bg-danger';
        }
        
        document.getElementById('stock-info-result').style.display = 'block';
        showAlert('股票信息查询成功', 'success');
        
    } catch (error) {
        showAlert('股票信息查询失败', 'danger');
    }
}

async function queryPriceData() {
    const symbol = document.getElementById('price-symbol').value.trim();
    const startDate = document.getElementById('start-date').value;
    const endDate = document.getElementById('end-date').value;
    
    if (!symbol || !startDate || !endDate) {
        showAlert('请填写完整的查询条件', 'warning');
        return;
    }
    
    try {
        // 模拟价格数据
        const mockPriceData = [
            { date: '2024-01-15', open: 13.00, high: 13.25, low: 12.95, close: 13.20, volume: 15600000 },
            { date: '2024-01-14', open: 12.80, high: 13.05, low: 12.75, close: 13.00, volume: 12300000 },
            { date: '2024-01-13', open: 12.90, high: 12.95, low: 12.70, close: 12.80, volume: 9800000 },
            { date: '2024-01-12', open: 13.10, high: 13.15, low: 12.85, close: 12.90, volume: 11200000 },
            { date: '2024-01-11', open: 13.20, high: 13.30, low: 13.05, close: 13.10, volume: 8900000 }
        ];
        
        const tbody = document.getElementById('price-data-tbody');
        tbody.innerHTML = mockPriceData.map(data => `
            <tr>
                <td>${data.date}</td>
                <td>${formatCurrency(data.open)}</td>
                <td>${formatCurrency(data.high)}</td>
                <td>${formatCurrency(data.low)}</td>
                <td>${formatCurrency(data.close)}</td>
                <td>${(data.volume / 10000).toFixed(0)}万</td>
            </tr>
        `).join('');
        
        document.getElementById('price-data-table').style.display = 'block';
        showAlert('价格数据查询成功', 'success');
        
    } catch (error) {
        showAlert('价格数据查询失败', 'danger');
    }
}

async function calculateIndicator() {
    const symbol = document.getElementById('indicator-symbol').value.trim();
    const indicatorType = document.getElementById('indicator-type').value;
    const period = document.getElementById('indicator-period').value;
    
    if (!symbol) {
        showAlert('请输入股票代码', 'warning');
        return;
    }
    
    try {
        // 模拟技术指标计算
        showAlert(`正在计算 ${symbol} 的 ${indicatorType.toUpperCase()} 指标...`, 'info');
        
        // 这里应该调用实际的API
        setTimeout(() => {
            document.getElementById('indicator-result').style.display = 'block';
            showAlert('技术指标计算完成', 'success');
        }, 1000);
        
    } catch (error) {
        showAlert('技术指标计算失败', 'danger');
    }
}

function loadMarketOverview() {
    showAlert('市场数据已刷新', 'success');
}
</script>
{% endblock %}