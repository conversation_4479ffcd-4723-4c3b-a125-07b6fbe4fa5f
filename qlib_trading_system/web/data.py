# -*- coding: utf-8 -*-
"""
数据查询和分析工具模块

提供股票数据查询、历史数据分析、技术指标计算等功能
"""

import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from fastapi import APIRouter, HTTPException, Depends, Query
from pydantic import BaseModel
import random

from qlib_trading_system.web.auth import get_current_user, require_permission

logger = logging.getLogger(__name__)

# 创建数据路由
data_router = APIRouter()

class StockInfo(BaseModel):
    """股票信息模型"""
    symbol: str
    name: str
    industry: str
    market_cap: float
    pe_ratio: float
    pb_ratio: float
    current_price: float
    change_pct: float
    volume: int
    turnover: float

class PriceData(BaseModel):
    """价格数据模型"""
    date: str
    open: float
    high: float
    low: float
    close: float
    volume: int
    amount: float

class TechnicalIndicator(BaseModel):
    """技术指标模型"""
    date: str
    ma5: Optional[float] = None
    ma10: Optional[float] = None
    ma20: Optional[float] = None
    ma60: Optional[float] = None
    macd: Optional[float] = None
    macd_signal: Optional[float] = None
    macd_hist: Optional[float] = None
    rsi: Optional[float] = None
    kdj_k: Optional[float] = None
    kdj_d: Optional[float] = None
    kdj_j: Optional[float] = None

class MarketOverview(BaseModel):
    """市场概览模型"""
    index_name: str
    current_value: float
    change: float
    change_pct: float
    volume: int
    amount: float
    pe_ratio: float
    pb_ratio: float

class DataManager:
    """数据管理器"""
    
    def __init__(self):
        # 模拟股票池
        self.stock_pool = [
            {"symbol": "000001.SZ", "name": "平安银行", "industry": "银行"},
            {"symbol": "000002.SZ", "name": "万科A", "industry": "房地产"},
            {"symbol": "000858.SZ", "name": "五粮液", "industry": "食品饮料"},
            {"symbol": "000876.SZ", "name": "新希望", "industry": "农林牧渔"},
            {"symbol": "002415.SZ", "name": "海康威视", "industry": "电子"},
            {"symbol": "300059.SZ", "name": "东方财富", "industry": "非银金融"},
            {"symbol": "600036.SH", "name": "招商银行", "industry": "银行"},
            {"symbol": "600519.SH", "name": "贵州茅台", "industry": "食品饮料"},
            {"symbol": "600887.SH", "name": "伊利股份", "industry": "食品饮料"},
            {"symbol": "000725.SZ", "name": "京东方A", "industry": "电子"}
        ]
        
        # 初始化模拟数据
        self._init_mock_data()
    
    def _init_mock_data(self):
        """初始化模拟数据"""
        # 生成模拟的历史价格数据
        self.price_data_cache = {}
        self.technical_data_cache = {}
        
        for stock in self.stock_pool:
            symbol = stock["symbol"]
            # 生成60天的历史数据
            dates = pd.date_range(end=datetime.now(), periods=60, freq='D')
            
            # 模拟价格数据
            base_price = random.uniform(10, 100)
            prices = []
            current_price = base_price
            
            for i, date in enumerate(dates):
                # 随机价格波动
                change = random.uniform(-0.05, 0.05)
                current_price = max(current_price * (1 + change), 1.0)
                
                # 生成OHLC数据
                high = current_price * random.uniform(1.0, 1.03)
                low = current_price * random.uniform(0.97, 1.0)
                open_price = random.uniform(low, high)
                close_price = current_price
                volume = random.randint(1000000, 50000000)
                amount = volume * close_price
                
                price_data = PriceData(
                    date=date.strftime('%Y-%m-%d'),
                    open=round(open_price, 2),
                    high=round(high, 2),
                    low=round(low, 2),
                    close=round(close_price, 2),
                    volume=volume,
                    amount=round(amount, 2)
                )
                
                prices.append(price_data)
                
                # 计算技术指标
                if len(prices) >= 5:
                    ma5 = sum([p.close for p in prices[-5:]]) / 5
                    ma10 = sum([p.close for p in prices[-10:]]) / 10 if len(prices) >= 10 else None
                    ma20 = sum([p.close for p in prices[-20:]]) / 20 if len(prices) >= 20 else None
                    
                    # 简单RSI计算
                    if len(prices) >= 14:
                        gains = []
                        losses = []
                        for j in range(1, min(15, len(prices))):
                            change = prices[-j].close - prices[-j-1].close
                            if change > 0:
                                gains.append(change)
                                losses.append(0)
                            else:
                                gains.append(0)
                                losses.append(abs(change))
                        
                        avg_gain = sum(gains) / len(gains) if gains else 0
                        avg_loss = sum(losses) / len(losses) if losses else 0
                        rs = avg_gain / avg_loss if avg_loss != 0 else 0
                        rsi = 100 - (100 / (1 + rs)) if rs != 0 else 50
                    else:
                        rsi = None
                    
                    technical_indicator = TechnicalIndicator(
                        date=date.strftime('%Y-%m-%d'),
                        ma5=round(ma5, 2),
                        ma10=round(ma10, 2) if ma10 else None,
                        ma20=round(ma20, 2) if ma20 else None,
                        rsi=round(rsi, 2) if rsi else None
                    )
                else:
                    technical_indicator = TechnicalIndicator(
                        date=date.strftime('%Y-%m-%d')
                    )
                
                if symbol not in self.technical_data_cache:
                    self.technical_data_cache[symbol] = []
                self.technical_data_cache[symbol].append(technical_indicator)
            
            self.price_data_cache[symbol] = prices
    
    def get_stock_info(self, symbol: str) -> Optional[StockInfo]:
        """获取股票基本信息"""
        try:
            # 查找股票信息
            stock_data = None
            for stock in self.stock_pool:
                if stock["symbol"] == symbol:
                    stock_data = stock
                    break
            
            if not stock_data:
                return None
            
            # 生成模拟数据
            current_price = random.uniform(10, 100)
            change_pct = random.uniform(-10, 10)
            
            return StockInfo(
                symbol=symbol,
                name=stock_data["name"],
                industry=stock_data["industry"],
                market_cap=random.uniform(100, 5000),
                pe_ratio=random.uniform(5, 50),
                pb_ratio=random.uniform(0.5, 5),
                current_price=round(current_price, 2),
                change_pct=round(change_pct, 2),
                volume=random.randint(1000000, 50000000),
                turnover=random.uniform(1, 15)
            )
            
        except Exception as e:
            logger.error(f"获取股票信息错误: {e}")
            return None
    
    def get_price_data(self, symbol: str, start_date: str, end_date: str) -> List[PriceData]:
        """获取历史价格数据"""
        try:
            if symbol in self.price_data_cache:
                return self.price_data_cache[symbol]
            else:
                # 如果缓存中没有，生成新数据
                self._init_mock_data()
                return self.price_data_cache.get(symbol, [])
        except Exception as e:
            logger.error(f"获取价格数据错误: {e}")
            return []
    
    def get_technical_indicators(self, symbol: str, indicator_type: str, period: int = 20) -> List[TechnicalIndicator]:
        """获取技术指标数据"""
        try:
            if symbol in self.technical_data_cache:
                return self.technical_data_cache[symbol]
            else:
                return []
        except Exception as e:
            logger.error(f"获取技术指标错误: {e}")
            return []
    
    def get_market_overview(self) -> List[MarketOverview]:
        """获取市场概览"""
        try:
            return [
                MarketOverview(
                    index_name="上证指数",
                    current_value=3245.67,
                    change=38.25,
                    change_pct=1.25,
                    volume=125000000,
                    amount=285600000000,
                    pe_ratio=13.5,
                    pb_ratio=1.2
                ),
                MarketOverview(
                    index_name="深证成指",
                    current_value=2156.89,
                    change=19.05,
                    change_pct=0.89,
                    volume=98000000,
                    amount=198500000000,
                    pe_ratio=18.2,
                    pb_ratio=1.8
                ),
                MarketOverview(
                    index_name="创业板指",
                    current_value=1234.56,
                    change=-5.67,
                    change_pct=-0.45,
                    volume=45000000,
                    amount=89200000000,
                    pe_ratio=25.8,
                    pb_ratio=2.5
                )
            ]
        except Exception as e:
            logger.error(f"获取市场概览错误: {e}")
            return []
    
    def search_stocks(self, keyword: str) -> List[StockInfo]:
        """搜索股票"""
        try:
            results = []
            for stock in self.stock_pool:
                if keyword.lower() in stock["symbol"].lower() or keyword in stock["name"]:
                    stock_info = self.get_stock_info(stock["symbol"])
                    if stock_info:
                        results.append(stock_info)
            return results
        except Exception as e:
            logger.error(f"搜索股票错误: {e}")
            return []

# 全局数据管理器实例
data_manager = DataManager()

@data_router.get("/stocks/search")
async def search_stocks(
    keyword: str = Query(..., description="搜索关键词"),
    current_user: Dict = Depends(require_permission("view_data"))
):
    """搜索股票"""
    try:
        results = data_manager.search_stocks(keyword)
        return {
            "stocks": [stock.dict() for stock in results],
            "total": len(results),
            "message": "股票搜索成功"
        }
    except Exception as e:
        logger.error(f"搜索股票错误: {e}")
        raise HTTPException(status_code=500, detail="搜索股票失败")

@data_router.get("/stock/{symbol}")
async def get_stock_info(
    symbol: str,
    current_user: Dict = Depends(require_permission("view_data"))
):
    """获取股票基本信息"""
    try:
        stock_info = data_manager.get_stock_info(symbol)
        if not stock_info:
            raise HTTPException(status_code=404, detail="股票不存在")
        
        return {
            "stock_info": stock_info.dict(),
            "message": "股票信息获取成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取股票信息错误: {e}")
        raise HTTPException(status_code=500, detail="获取股票信息失败")

@data_router.get("/price/{symbol}")
async def get_price_data(
    symbol: str,
    start_date: str = Query(..., description="开始日期 YYYY-MM-DD"),
    end_date: str = Query(..., description="结束日期 YYYY-MM-DD"),
    current_user: Dict = Depends(require_permission("view_data"))
):
    """获取历史价格数据"""
    try:
        price_data = data_manager.get_price_data(symbol, start_date, end_date)
        return {
            "price_data": [data.dict() for data in price_data],
            "total": len(price_data),
            "message": "价格数据获取成功"
        }
    except Exception as e:
        logger.error(f"获取价格数据错误: {e}")
        raise HTTPException(status_code=500, detail="获取价格数据失败")

@data_router.get("/indicators/{symbol}")
async def get_technical_indicators(
    symbol: str,
    indicator_type: str = Query("ma", description="指标类型"),
    period: int = Query(20, description="计算周期"),
    current_user: Dict = Depends(require_permission("view_data"))
):
    """获取技术指标数据"""
    try:
        indicators = data_manager.get_technical_indicators(symbol, indicator_type, period)
        return {
            "indicators": [indicator.dict() for indicator in indicators],
            "total": len(indicators),
            "message": "技术指标获取成功"
        }
    except Exception as e:
        logger.error(f"获取技术指标错误: {e}")
        raise HTTPException(status_code=500, detail="获取技术指标失败")

@data_router.get("/market/overview")
async def get_market_overview(current_user: Dict = Depends(require_permission("view_data"))):
    """获取市场概览"""
    try:
        overview = data_manager.get_market_overview()
        return {
            "market_overview": [item.dict() for item in overview],
            "message": "市场概览获取成功"
        }
    except Exception as e:
        logger.error(f"获取市场概览错误: {e}")
        raise HTTPException(status_code=500, detail="获取市场概览失败")

@data_router.get("/stocks/hot")
async def get_hot_stocks(
    list_type: str = Query("gainers", description="榜单类型: gainers, losers, active"),
    limit: int = Query(10, description="返回数量"),
    current_user: Dict = Depends(require_permission("view_data"))
):
    """获取热门股票榜单"""
    try:
        # 模拟热门股票数据
        hot_stocks = []
        for i, stock in enumerate(data_manager.stock_pool[:limit]):
            stock_info = data_manager.get_stock_info(stock["symbol"])
            if stock_info:
                # 根据榜单类型调整涨跌幅
                if list_type == "gainers":
                    stock_info.change_pct = abs(stock_info.change_pct)
                elif list_type == "losers":
                    stock_info.change_pct = -abs(stock_info.change_pct)
                
                hot_stocks.append(stock_info)
        
        # 按涨跌幅排序
        if list_type == "gainers":
            hot_stocks.sort(key=lambda x: x.change_pct, reverse=True)
        elif list_type == "losers":
            hot_stocks.sort(key=lambda x: x.change_pct)
        else:  # active
            hot_stocks.sort(key=lambda x: x.volume, reverse=True)
        
        return {
            "hot_stocks": [stock.dict() for stock in hot_stocks],
            "list_type": list_type,
            "message": "热门股票获取成功"
        }
    except Exception as e:
        logger.error(f"获取热门股票错误: {e}")
        raise HTTPException(status_code=500, detail="获取热门股票失败")