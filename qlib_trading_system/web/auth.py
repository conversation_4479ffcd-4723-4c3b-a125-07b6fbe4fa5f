# -*- coding: utf-8 -*-
"""
用户认证和权限管理模块

提供用户登录、权限验证、会话管理等功能
"""

import hashlib
import json
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, List
from fastapi import APIRouter, Request, HTTPException, status, Depends, Form
from fastapi.responses import JSONResponse, RedirectResponse
from pydantic import BaseModel
import jwt

logger = logging.getLogger(__name__)

# JWT配置
JWT_SECRET_KEY = "qlib-trading-system-jwt-secret-2024"
JWT_ALGORITHM = "HS256"
JWT_EXPIRATION_HOURS = 24

# 创建认证路由
auth_router = APIRouter()

class User(BaseModel):
    """用户模型"""
    username: str
    email: str
    role: str
    permissions: List[str]
    created_at: datetime
    last_login: Optional[datetime] = None

class LoginRequest(BaseModel):
    """登录请求模型"""
    username: str
    password: str

class UserManager:
    """用户管理器"""
    
    def __init__(self):
        # 默认用户数据（实际应用中应该使用数据库）
        self.users_db = {
            "admin": {
                "username": "admin",
                "password_hash": self._hash_password("admin123"),
                "email": "<EMAIL>",
                "role": "administrator",
                "permissions": [
                    "view_dashboard",
                    "manage_trading",
                    "manage_config",
                    "view_config",
                    "view_monitoring",
                    "manage_monitoring",
                    "manage_data",
                    "view_data",
                    "manage_users"
                ],
                "created_at": datetime.now(),
                "last_login": None
            },
            "trader": {
                "username": "trader",
                "password_hash": self._hash_password("trader123"),
                "email": "<EMAIL>",
                "role": "trader",
                "permissions": [
                    "view_dashboard",
                    "manage_trading",
                    "view_monitoring",
                    "view_data"
                ],
                "created_at": datetime.now(),
                "last_login": None
            },
            "analyst": {
                "username": "analyst",
                "password_hash": self._hash_password("analyst123"),
                "email": "<EMAIL>",
                "role": "analyst",
                "permissions": [
                    "view_dashboard",
                    "view_monitoring",
                    "manage_data",
                    "view_data",
                    "view_config"
                ],
                "created_at": datetime.now(),
                "last_login": None
            }
        }
    
    def _hash_password(self, password: str) -> str:
        """密码哈希"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def authenticate_user(self, username: str, password: str) -> Optional[Dict]:
        """用户认证"""
        try:
            user_data = self.users_db.get(username)
            if not user_data:
                return None
            
            password_hash = self._hash_password(password)
            if password_hash != user_data["password_hash"]:
                return None
            
            # 更新最后登录时间
            user_data["last_login"] = datetime.now()
            
            # 返回用户信息（不包含密码哈希）
            user_info = {
                "username": user_data["username"],
                "email": user_data["email"],
                "role": user_data["role"],
                "permissions": user_data["permissions"],
                "created_at": user_data["created_at"].isoformat() if user_data["created_at"] else None,
                "last_login": user_data["last_login"].isoformat() if user_data["last_login"] else None
            }
            
            logger.info(f"用户 {username} 认证成功")
            return user_info
            
        except Exception as e:
            logger.error(f"用户认证错误: {e}")
            return None
    
    def get_user(self, username: str) -> Optional[Dict]:
        """获取用户信息"""
        user_data = self.users_db.get(username)
        if not user_data:
            return None
        
        return {
            "username": user_data["username"],
            "email": user_data["email"],
            "role": user_data["role"],
            "permissions": user_data["permissions"],
            "created_at": user_data["created_at"].isoformat() if user_data["created_at"] else None,
            "last_login": user_data["last_login"].isoformat() if user_data["last_login"] else None
        }
    
    def check_permission(self, username: str, permission: str) -> bool:
        """检查用户权限"""
        user_data = self.users_db.get(username)
        if not user_data:
            return False
        
        return permission in user_data["permissions"]

# 全局用户管理器实例
user_manager = UserManager()

def create_access_token(data: dict) -> str:
    """创建访问令牌"""
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(hours=JWT_EXPIRATION_HOURS)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)
    return encoded_jwt

def verify_token(token: str) -> Optional[Dict]:
    """验证令牌"""
    try:
        payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            return None
        return {"username": username}
    except jwt.PyJWTError:
        return None

async def get_current_user(request: Request) -> Dict:
    """获取当前用户（依赖注入）"""
    # 首先检查会话
    user = request.session.get("user")
    if user:
        return user
    
    # 检查JWT令牌
    token = request.headers.get("Authorization")
    if token and token.startswith("Bearer "):
        token = token[7:]
        token_data = verify_token(token)
        if token_data:
            user_info = user_manager.get_user(token_data["username"])
            if user_info:
                return user_info
    
    raise HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="未授权访问，请先登录"
    )

def require_permission(permission: str):
    """权限检查装饰器"""
    def permission_checker(current_user: Dict = Depends(get_current_user)):
        if not user_manager.check_permission(current_user["username"], permission):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"权限不足，需要权限: {permission}"
            )
        return current_user
    return permission_checker

@auth_router.post("/login")
async def login(request: Request, username: str = Form(...), password: str = Form(...)):
    """用户登录"""
    try:
        # 验证用户凭据
        user_info = user_manager.authenticate_user(username, password)
        if not user_info:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误"
            )
        
        # 创建访问令牌
        access_token = create_access_token(data={"sub": username})
        
        # 保存用户信息到会话
        request.session["user"] = user_info
        request.session["access_token"] = access_token
        
        logger.info(f"用户 {username} 登录成功")
        
        return JSONResponse(
            status_code=200,
            content={
                "message": "登录成功",
                "user": user_info,
                "access_token": access_token,
                "token_type": "bearer"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"登录处理错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登录处理失败"
        )

@auth_router.post("/logout")
async def logout(request: Request):
    """用户登出"""
    try:
        username = request.session.get("user", {}).get("username", "未知用户")
        
        # 清除会话
        request.session.clear()
        
        logger.info(f"用户 {username} 登出成功")
        
        return JSONResponse(
            status_code=200,
            content={"message": "登出成功"}
        )
        
    except Exception as e:
        logger.error(f"登出处理错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登出处理失败"
        )

@auth_router.get("/me")
async def get_current_user_info(current_user: Dict = Depends(get_current_user)):
    """获取当前用户信息"""
    return {
        "user": current_user,
        "message": "用户信息获取成功"
    }

@auth_router.get("/users")
async def list_users(current_user: Dict = Depends(require_permission("manage_users"))):
    """获取用户列表（需要管理员权限）"""
    try:
        users = []
        for username, user_data in user_manager.users_db.items():
            users.append({
                "username": user_data["username"],
                "email": user_data["email"],
                "role": user_data["role"],
                "permissions": user_data["permissions"],
                "created_at": user_data["created_at"].isoformat(),
                "last_login": user_data["last_login"].isoformat() if user_data["last_login"] else None
            })
        
        return {
            "users": users,
            "total": len(users),
            "message": "用户列表获取成功"
        }
        
    except Exception as e:
        logger.error(f"获取用户列表错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户列表失败"
        )

@auth_router.get("/permissions")
async def get_permissions(current_user: Dict = Depends(get_current_user)):
    """获取当前用户权限"""
    return {
        "permissions": current_user["permissions"],
        "role": current_user["role"],
        "message": "权限信息获取成功"
    }