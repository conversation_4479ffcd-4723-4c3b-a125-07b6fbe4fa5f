# -*- coding: utf-8 -*-
"""
Web应用主入口

基于FastAPI的Web管理界面主应用
"""

import os
import sys
from pathlib import Path
from fastapi import FastAPI, Request, Depends, HTTPException, status
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
from starlette.middleware.sessions import SessionMiddleware
from fastapi.responses import HTMLResponse, RedirectResponse
import uvicorn
import logging
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from qlib_trading_system.web.auth import auth_router, get_current_user
from qlib_trading_system.web.trading import trading_router
from qlib_trading_system.web.config import config_router
from qlib_trading_system.web.monitoring import monitoring_router
from qlib_trading_system.web.data import data_router
from qlib_trading_system.utils.config.manager import ConfigManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/web_app.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="Qlib交易系统Web管理界面",
    description="基于qlib框架的双AI交易系统Web管理界面",
    version="1.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加Session中间件
app.add_middleware(
    SessionMiddleware,
    secret_key="qlib-trading-system-secret-key-2024"
)

# 设置静态文件和模板
static_dir = Path(__file__).parent / "static"
templates_dir = Path(__file__).parent / "templates"

# 创建目录如果不存在
static_dir.mkdir(exist_ok=True)
templates_dir.mkdir(exist_ok=True)

app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")
templates = Jinja2Templates(directory=str(templates_dir))

# 注册路由
app.include_router(auth_router, prefix="/auth", tags=["认证"])
app.include_router(trading_router, prefix="/api/trading", tags=["交易"])
app.include_router(config_router, prefix="/api/config", tags=["配置"])
app.include_router(monitoring_router, prefix="/api/monitoring", tags=["监控"])
app.include_router(data_router, prefix="/api/data", tags=["数据"])

@app.get("/", response_class=HTMLResponse)
async def root(request: Request):
    """首页 - 重定向到登录页面或仪表板"""
    try:
        # 检查用户是否已登录
        user = request.session.get("user")
        if user:
            return RedirectResponse(url="/dashboard", status_code=302)
        else:
            return RedirectResponse(url="/login", status_code=302)
    except Exception as e:
        logger.error(f"首页访问错误: {e}")
        return RedirectResponse(url="/login", status_code=302)

@app.get("/login", response_class=HTMLResponse)
async def login_page(request: Request):
    """登录页面"""
    return templates.TemplateResponse("login.html", {"request": request})

@app.get("/dashboard", response_class=HTMLResponse)
async def dashboard(request: Request, current_user: dict = Depends(get_current_user)):
    """仪表板页面"""
    try:
        # 获取系统状态信息
        system_status = {
            "trading_status": "运行中",
            "last_update": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "total_positions": 0,
            "daily_pnl": 0.0,
            "system_health": "正常"
        }
        
        return templates.TemplateResponse("dashboard.html", {
            "request": request,
            "user": current_user,
            "system_status": system_status
        })
    except Exception as e:
        logger.error(f"仪表板访问错误: {e}")
        raise HTTPException(status_code=500, detail="仪表板加载失败")

@app.get("/trading", response_class=HTMLResponse)
async def trading_page(request: Request, current_user: dict = Depends(get_current_user)):
    """交易监控页面"""
    return templates.TemplateResponse("trading.html", {
        "request": request,
        "user": current_user
    })

@app.get("/config", response_class=HTMLResponse)
async def config_page(request: Request, current_user: dict = Depends(get_current_user)):
    """配置管理页面"""
    return templates.TemplateResponse("config.html", {
        "request": request,
        "user": current_user
    })

@app.get("/data", response_class=HTMLResponse)
async def data_page(request: Request, current_user: dict = Depends(get_current_user)):
    """数据查询页面"""
    return templates.TemplateResponse("data.html", {
        "request": request,
        "user": current_user
    })

@app.get("/monitoring", response_class=HTMLResponse)
async def monitoring_page(request: Request, current_user: dict = Depends(get_current_user)):
    """系统监控页面"""
    return templates.TemplateResponse("monitoring.html", {
        "request": request,
        "user": current_user
    })

@app.exception_handler(404)
async def not_found_handler(request: Request, exc):
    """404错误处理"""
    return templates.TemplateResponse("404.html", {
        "request": request
    }, status_code=404)

@app.exception_handler(500)
async def internal_error_handler(request: Request, exc):
    """500错误处理"""
    logger.error(f"内部服务器错误: {exc}")
    return templates.TemplateResponse("500.html", {
        "request": request
    }, status_code=500)

@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    logger.info("Qlib交易系统Web管理界面启动")
    
    # 创建必要的目录
    os.makedirs("logs", exist_ok=True)
    
    # 初始化配置管理器
    try:
        config_manager = ConfigManager()
        logger.info("配置管理器初始化成功")
    except Exception as e:
        logger.error(f"配置管理器初始化失败: {e}")

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    logger.info("Qlib交易系统Web管理界面关闭")

def run_web_app(host: str = "127.0.0.1", port: int = 8000, debug: bool = False):
    """运行Web应用"""
    logger.info(f"启动Web服务器: http://{host}:{port}")
    uvicorn.run(
        "qlib_trading_system.web.app:app",
        host=host,
        port=port,
        reload=debug,
        log_level="info"
    )

if __name__ == "__main__":
    run_web_app(debug=True)