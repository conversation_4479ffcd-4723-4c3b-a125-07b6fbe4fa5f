# -*- coding: utf-8 -*-
"""
策略配置和参数管理模块

提供策略参数配置、系统设置管理等功能
"""

import logging
import json
import os
from datetime import datetime
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, validator
import yaml

from qlib_trading_system.web.auth import get_current_user, require_permission
from qlib_trading_system.utils.config.integration_adapter import get_config_adapter, get_module_config

logger = logging.getLogger(__name__)

# 创建配置路由
config_router = APIRouter()

class TradingConfig(BaseModel):
    """交易配置模型"""
    # 基础配置
    capital_mode: str = "small"  # small, medium, large
    total_capital: float = 100000.0
    max_position_size: float = 0.3
    max_single_stock_ratio: float = 1.0
    
    # 风险控制
    max_daily_loss_pct: float = 0.01
    max_drawdown_pct: float = 0.30
    stop_loss_pct: float = 0.02
    position_size_limit: int = 1
    
    # 交易参数
    enable_t_plus_zero: bool = True
    base_position_ratio: float = 0.75
    t_position_ratio: float = 0.20
    cash_reserve_ratio: float = 0.05
    
    # 模型参数
    stock_selection_threshold: float = 0.7
    intraday_confidence_threshold: float = 0.6
    min_profit_expectation: float = 0.01
    
    @validator('capital_mode')
    def validate_capital_mode(cls, v):
        if v not in ['small', 'medium', 'large']:
            raise ValueError('资金模式必须是 small, medium, large 之一')
        return v
    
    @validator('total_capital')
    def validate_total_capital(cls, v):
        if v <= 0:
            raise ValueError('总资金必须大于0')
        return v

class DataSourceConfig(BaseModel):
    """数据源配置模型"""
    primary_source: str = "iTick"
    backup_sources: List[str] = ["JoinQuant", "RiceQuant"]
    
    # iTick配置
    itick_api_key: str = ""
    itick_secret_key: str = ""
    
    # JoinQuant配置
    jq_username: str = ""
    jq_password: str = ""
    
    # RiceQuant配置
    rq_api_key: str = ""
    
    # 数据更新频率
    realtime_update_interval: int = 1  # 秒
    daily_update_time: str = "15:30"
    
    @validator('realtime_update_interval')
    def validate_update_interval(cls, v):
        if v < 1 or v > 60:
            raise ValueError('实时更新间隔必须在1-60秒之间')
        return v

class ModelConfig(BaseModel):
    """模型配置"""
    # 股票筛选模型
    stock_selection_model_type: str = "LightGBM"
    feature_importance_threshold: float = 0.01
    model_retrain_frequency: str = "daily"
    
    # 日内交易模型
    intraday_model_type: str = "Transformer"
    prediction_horizon: int = 30  # 分钟
    signal_fusion_weights: Dict[str, float] = {
        "transformer": 0.5,
        "cnn": 0.3,
        "ta": 0.2
    }
    
    # 特征工程
    technical_indicators_count: int = 200
    lookback_days: int = 60
    feature_selection_method: str = "importance"

class SystemConfig(BaseModel):
    """系统配置"""
    # 日志配置
    log_level: str = "INFO"
    log_retention_days: int = 30
    
    # 数据库配置
    clickhouse_host: str = "localhost"
    clickhouse_port: int = 9000
    redis_host: str = "localhost"
    redis_port: int = 6379
    
    # Web服务配置
    web_host: str = "127.0.0.1"
    web_port: int = 8000
    session_timeout: int = 24  # 小时
    
    # 监控配置
    enable_email_alerts: bool = True
    alert_email: str = ""
    enable_sms_alerts: bool = False
    alert_phone: str = ""

class ConfigManager:
    """配置管理器"""
    
    def __init__(self):
        self.config_dir = "config"
        self.ensure_config_dir()

        # 获取配置适配器
        self.config_adapter = get_config_adapter()

        # 配置文件路径（保持向后兼容）
        self.trading_config_path = os.path.join(self.config_dir, "trading_config.json")
        self.data_source_config_path = os.path.join(self.config_dir, "data_source_config.json")
        self.model_config_path = os.path.join(self.config_dir, "model_config.json")
        self.system_config_path = os.path.join(self.config_dir, "system_config.json")

        # 从新配置系统加载配置
        self.trading_config = self.load_trading_config()
        self.data_source_config = self.load_data_source_config()
        self.model_config = self.load_model_config()
        self.system_config = self.load_system_config()
    
    def ensure_config_dir(self):
        """确保配置目录存在"""
        os.makedirs(self.config_dir, exist_ok=True)
    
    def load_trading_config(self) -> TradingConfig:
        """加载交易配置"""
        try:
            # 优先从新配置系统加载
            config_data = get_module_config('trading')
            if config_data:
                return TradingConfig(**config_data)

            # 回退到旧的文件加载方式
            if os.path.exists(self.trading_config_path):
                with open(self.trading_config_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                return TradingConfig(**data)
            else:
                # 返回默认配置
                config = TradingConfig()
                self.save_trading_config(config)
                return config
        except Exception as e:
            logger.error(f"加载交易配置错误: {e}")
            return TradingConfig()
    
    def save_trading_config(self, config: TradingConfig):
        """保存交易配置"""
        try:
            with open(self.trading_config_path, 'w', encoding='utf-8') as f:
                json.dump(config.dict(), f, ensure_ascii=False, indent=2)
            logger.info("交易配置保存成功")
        except Exception as e:
            logger.error(f"保存交易配置错误: {e}")
            raise
    
    def load_data_source_config(self) -> DataSourceConfig:
        """加载数据源配置"""
        try:
            if os.path.exists(self.data_source_config_path):
                with open(self.data_source_config_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                return DataSourceConfig(**data)
            else:
                config = DataSourceConfig()
                self.save_data_source_config(config)
                return config
        except Exception as e:
            logger.error(f"加载数据源配置错误: {e}")
            return DataSourceConfig()
    
    def save_data_source_config(self, config: DataSourceConfig):
        """保存数据源配置"""
        try:
            with open(self.data_source_config_path, 'w', encoding='utf-8') as f:
                json.dump(config.dict(), f, ensure_ascii=False, indent=2)
            logger.info("数据源配置保存成功")
        except Exception as e:
            logger.error(f"保存数据源配置错误: {e}")
            raise
    
    def load_model_config(self) -> ModelConfig:
        """加载模型配置"""
        try:
            if os.path.exists(self.model_config_path):
                with open(self.model_config_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                return ModelConfig(**data)
            else:
                config = ModelConfig()
                self.save_model_config(config)
                return config
        except Exception as e:
            logger.error(f"加载模型配置错误: {e}")
            return ModelConfig()
    
    def save_model_config(self, config: ModelConfig):
        """保存模型配置"""
        try:
            with open(self.model_config_path, 'w', encoding='utf-8') as f:
                json.dump(config.dict(), f, ensure_ascii=False, indent=2)
            logger.info("模型配置保存成功")
        except Exception as e:
            logger.error(f"保存模型配置错误: {e}")
            raise
    
    def load_system_config(self) -> SystemConfig:
        """加载系统配置"""
        try:
            if os.path.exists(self.system_config_path):
                with open(self.system_config_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                return SystemConfig(**data)
            else:
                config = SystemConfig()
                self.save_system_config(config)
                return config
        except Exception as e:
            logger.error(f"加载系统配置错误: {e}")
            return SystemConfig()
    
    def save_system_config(self, config: SystemConfig):
        """保存系统配置"""
        try:
            with open(self.system_config_path, 'w', encoding='utf-8') as f:
                json.dump(config.dict(), f, ensure_ascii=False, indent=2)
            logger.info("系统配置保存成功")
        except Exception as e:
            logger.error(f"保存系统配置错误: {e}")
            raise
    
    def get_all_configs(self) -> Dict[str, Any]:
        """获取所有配置"""
        return {
            "trading": self.trading_config.dict(),
            "data_source": self.data_source_config.dict(),
            "model": self.model_config.dict(),
            "system": self.system_config.dict()
        }
    
    def validate_config(self, config_type: str, config_data: Dict) -> bool:
        """验证配置"""
        try:
            if config_type == "trading":
                TradingConfig(**config_data)
            elif config_type == "data_source":
                DataSourceConfig(**config_data)
            elif config_type == "model":
                ModelConfig(**config_data)
            elif config_type == "system":
                SystemConfig(**config_data)
            else:
                return False
            return True
        except Exception as e:
            logger.error(f"配置验证错误: {e}")
            return False

# 全局配置管理器实例
config_manager = ConfigManager()

@config_router.get("/all")
async def get_all_configs(current_user: Dict = Depends(require_permission("view_config"))):
    """获取所有配置"""
    try:
        configs = config_manager.get_all_configs()
        return {
            "configs": configs,
            "message": "配置获取成功"
        }
    except Exception as e:
        logger.error(f"获取配置错误: {e}")
        raise HTTPException(status_code=500, detail="获取配置失败")

@config_router.get("/trading")
async def get_trading_config(current_user: Dict = Depends(require_permission("view_config"))):
    """获取交易配置"""
    try:
        return {
            "config": config_manager.trading_config.dict(),
            "message": "交易配置获取成功"
        }
    except Exception as e:
        logger.error(f"获取交易配置错误: {e}")
        raise HTTPException(status_code=500, detail="获取交易配置失败")

@config_router.put("/trading")
async def update_trading_config(
    config_data: Dict[str, Any],
    current_user: Dict = Depends(require_permission("manage_config"))
):
    """更新交易配置"""
    try:
        # 验证配置
        if not config_manager.validate_config("trading", config_data):
            raise HTTPException(status_code=400, detail="配置验证失败")
        
        # 更新配置
        new_config = TradingConfig(**config_data)
        config_manager.trading_config = new_config
        config_manager.save_trading_config(new_config)
        
        return {
            "config": new_config.dict(),
            "message": "交易配置更新成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新交易配置错误: {e}")
        raise HTTPException(status_code=500, detail="更新交易配置失败")

@config_router.get("/data-source")
async def get_data_source_config(current_user: Dict = Depends(require_permission("view_config"))):
    """获取数据源配置"""
    try:
        return {
            "config": config_manager.data_source_config.dict(),
            "message": "数据源配置获取成功"
        }
    except Exception as e:
        logger.error(f"获取数据源配置错误: {e}")
        raise HTTPException(status_code=500, detail="获取数据源配置失败")

@config_router.put("/data-source")
async def update_data_source_config(
    config_data: Dict[str, Any],
    current_user: Dict = Depends(require_permission("manage_config"))
):
    """更新数据源配置"""
    try:
        if not config_manager.validate_config("data_source", config_data):
            raise HTTPException(status_code=400, detail="配置验证失败")
        
        new_config = DataSourceConfig(**config_data)
        config_manager.data_source_config = new_config
        config_manager.save_data_source_config(new_config)
        
        return {
            "config": new_config.dict(),
            "message": "数据源配置更新成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新数据源配置错误: {e}")
        raise HTTPException(status_code=500, detail="更新数据源配置失败")

@config_router.get("/model")
async def get_model_config(current_user: Dict = Depends(require_permission("view_config"))):
    """获取模型配置"""
    try:
        return {
            "config": config_manager.model_config.dict(),
            "message": "模型配置获取成功"
        }
    except Exception as e:
        logger.error(f"获取模型配置错误: {e}")
        raise HTTPException(status_code=500, detail="获取模型配置失败")

@config_router.put("/model")
async def update_model_config(
    config_data: Dict[str, Any],
    current_user: Dict = Depends(require_permission("manage_config"))
):
    """更新模型配置"""
    try:
        if not config_manager.validate_config("model", config_data):
            raise HTTPException(status_code=400, detail="配置验证失败")
        
        new_config = ModelConfig(**config_data)
        config_manager.model_config = new_config
        config_manager.save_model_config(new_config)
        
        return {
            "config": new_config.dict(),
            "message": "模型配置更新成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新模型配置错误: {e}")
        raise HTTPException(status_code=500, detail="更新模型配置失败")

@config_router.get("/system")
async def get_system_config(current_user: Dict = Depends(require_permission("view_config"))):
    """获取系统配置"""
    try:
        return {
            "config": config_manager.system_config.dict(),
            "message": "系统配置获取成功"
        }
    except Exception as e:
        logger.error(f"获取系统配置错误: {e}")
        raise HTTPException(status_code=500, detail="获取系统配置失败")

@config_router.put("/system")
async def update_system_config(
    config_data: Dict[str, Any],
    current_user: Dict = Depends(require_permission("manage_config"))
):
    """更新系统配置"""
    try:
        if not config_manager.validate_config("system", config_data):
            raise HTTPException(status_code=400, detail="配置验证失败")
        
        new_config = SystemConfig(**config_data)
        config_manager.system_config = new_config
        config_manager.save_system_config(new_config)
        
        return {
            "config": new_config.dict(),
            "message": "系统配置更新成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新系统配置错误: {e}")
        raise HTTPException(status_code=500, detail="更新系统配置失败")

@config_router.post("/validate")
async def validate_config(
    config_type: str,
    config_data: Dict[str, Any],
    current_user: Dict = Depends(require_permission("view_config"))
):
    """验证配置"""
    try:
        is_valid = config_manager.validate_config(config_type, config_data)
        return {
            "valid": is_valid,
            "message": "配置验证成功" if is_valid else "配置验证失败"
        }
    except Exception as e:
        logger.error(f"配置验证错误: {e}")
        raise HTTPException(status_code=500, detail="配置验证失败")

@config_router.post("/reset/{config_type}")
async def reset_config(
    config_type: str,
    current_user: Dict = Depends(require_permission("manage_config"))
):
    """重置配置为默认值"""
    try:
        if config_type == "trading":
            config_manager.trading_config = TradingConfig()
            config_manager.save_trading_config(config_manager.trading_config)
        elif config_type == "data_source":
            config_manager.data_source_config = DataSourceConfig()
            config_manager.save_data_source_config(config_manager.data_source_config)
        elif config_type == "model":
            config_manager.model_config = ModelConfig()
            config_manager.save_model_config(config_manager.model_config)
        elif config_type == "system":
            config_manager.system_config = SystemConfig()
            config_manager.save_system_config(config_manager.system_config)
        else:
            raise HTTPException(status_code=400, detail="无效的配置类型")
        
        return {"message": f"{config_type}配置重置成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重置配置错误: {e}")
        raise HTTPException(status_code=500, detail="重置配置失败")