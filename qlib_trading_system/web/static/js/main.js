/**
 * Qlib交易系统Web管理界面主JavaScript文件
 * 提供通用功能和页面交互逻辑
 */

// 全局配置
const CONFIG = {
    API_BASE_URL: '/api',
    REFRESH_INTERVAL: 5000, // 5秒刷新间隔
    CHART_COLORS: [
        '#667eea', '#764ba2', '#f093fb', '#f5576c',
        '#4facfe', '#00f2fe', '#43e97b', '#38f9d7'
    ]
};

// 工具函数
const Utils = {
    /**
     * 格式化数字
     */
    formatNumber: function(num, decimals = 2) {
        if (num === null || num === undefined) return '--';
        return parseFloat(num).toFixed(decimals);
    },

    /**
     * 格式化货币
     */
    formatCurrency: function(amount, decimals = 2) {
        if (amount === null || amount === undefined) return '--';
        return '¥' + parseFloat(amount).toLocaleString('zh-CN', {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        });
    },

    /**
     * 格式化百分比
     */
    formatPercent: function(value, decimals = 2) {
        if (value === null || value === undefined) return '--';
        const formatted = parseFloat(value).toFixed(decimals);
        return formatted + '%';
    },

    /**
     * 获取涨跌颜色类
     */
    getChangeColorClass: function(value) {
        if (value > 0) return 'text-success';
        if (value < 0) return 'text-danger';
        return 'text-muted';
    },

    /**
     * 显示加载状态
     */
    showLoading: function(element) {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }
        if (element) {
            element.innerHTML = '<div class="loading"></div> 加载中...';
        }
    },

    /**
     * 隐藏加载状态
     */
    hideLoading: function(element) {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }
        if (element) {
            const loading = element.querySelector('.loading');
            if (loading) {
                loading.remove();
            }
        }
    },

    /**
     * 显示消息提示
     */
    showMessage: function(message, type = 'info', duration = 3000) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `alert alert-${type} fade-in`;
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        `;
        messageDiv.textContent = message;
        
        document.body.appendChild(messageDiv);
        
        setTimeout(() => {
            messageDiv.style.opacity = '0';
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.parentNode.removeChild(messageDiv);
                }
            }, 300);
        }, duration);
    },

    /**
     * 确认对话框
     */
    confirm: function(message, callback) {
        if (window.confirm(message)) {
            callback();
        }
    }
};

// API请求封装
const API = {
    /**
     * 发送GET请求
     */
    get: async function(url) {
        try {
            const response = await fetch(CONFIG.API_BASE_URL + url, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'same-origin'
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error('API GET Error:', error);
            Utils.showMessage('请求失败: ' + error.message, 'danger');
            throw error;
        }
    },

    /**
     * 发送POST请求
     */
    post: async function(url, data) {
        try {
            const response = await fetch(CONFIG.API_BASE_URL + url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'same-origin',
                body: JSON.stringify(data)
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error('API POST Error:', error);
            Utils.showMessage('请求失败: ' + error.message, 'danger');
            throw error;
        }
    },

    /**
     * 发送PUT请求
     */
    put: async function(url, data) {
        try {
            const response = await fetch(CONFIG.API_BASE_URL + url, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'same-origin',
                body: JSON.stringify(data)
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error('API PUT Error:', error);
            Utils.showMessage('请求失败: ' + error.message, 'danger');
            throw error;
        }
    },

    /**
     * 发送DELETE请求
     */
    delete: async function(url) {
        try {
            const response = await fetch(CONFIG.API_BASE_URL + url, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'same-origin'
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error('API DELETE Error:', error);
            Utils.showMessage('请求失败: ' + error.message, 'danger');
            throw error;
        }
    }
};

// 认证相关功能
const Auth = {
    /**
     * 用户登录
     */
    login: async function(username, password) {
        try {
            const formData = new FormData();
            formData.append('username', username);
            formData.append('password', password);
            
            const response = await fetch('/auth/login', {
                method: 'POST',
                body: formData,
                credentials: 'same-origin'
            });
            
            const result = await response.json();
            
            if (response.ok) {
                Utils.showMessage('登录成功', 'success');
                setTimeout(() => {
                    window.location.href = '/dashboard';
                }, 1000);
            } else {
                Utils.showMessage(result.detail || '登录失败', 'danger');
            }
        } catch (error) {
            console.error('Login Error:', error);
            Utils.showMessage('登录失败: ' + error.message, 'danger');
        }
    },

    /**
     * 用户登出
     */
    logout: async function() {
        try {
            const response = await fetch('/auth/logout', {
                method: 'POST',
                credentials: 'same-origin'
            });
            
            if (response.ok) {
                Utils.showMessage('登出成功', 'success');
                setTimeout(() => {
                    window.location.href = '/login';
                }, 1000);
            }
        } catch (error) {
            console.error('Logout Error:', error);
            Utils.showMessage('登出失败: ' + error.message, 'danger');
        }
    }
};

// 交易相关功能
const Trading = {
    /**
     * 获取交易状态
     */
    getStatus: async function() {
        try {
            const result = await API.get('/trading/status');
            return result.status;
        } catch (error) {
            console.error('Get trading status error:', error);
            return null;
        }
    },

    /**
     * 获取持仓信息
     */
    getPositions: async function() {
        try {
            const result = await API.get('/trading/positions');
            return result.positions;
        } catch (error) {
            console.error('Get positions error:', error);
            return [];
        }
    },

    /**
     * 获取订单列表
     */
    getOrders: async function(status = null) {
        try {
            let url = '/trading/orders';
            if (status) {
                url += `?status=${status}`;
            }
            const result = await API.get(url);
            return result.orders;
        } catch (error) {
            console.error('Get orders error:', error);
            return [];
        }
    },

    /**
     * 下单
     */
    placeOrder: async function(symbol, side, quantity, orderType = 'market', price = null) {
        try {
            const data = {
                symbol: symbol,
                side: side,
                quantity: quantity,
                order_type: orderType
            };
            
            if (price) {
                data.price = price;
            }
            
            const result = await API.post('/trading/orders', data);
            Utils.showMessage('下单成功', 'success');
            return result;
        } catch (error) {
            console.error('Place order error:', error);
            return null;
        }
    },

    /**
     * 撤单
     */
    cancelOrder: async function(orderId) {
        try {
            const result = await API.delete(`/trading/orders/${orderId}`);
            Utils.showMessage('撤单成功', 'success');
            return result;
        } catch (error) {
            console.error('Cancel order error:', error);
            return null;
        }
    },

    /**
     * 启动交易
     */
    start: async function() {
        try {
            const result = await API.post('/trading/start', {});
            Utils.showMessage('交易启动成功', 'success');
            return result;
        } catch (error) {
            console.error('Start trading error:', error);
            return null;
        }
    },

    /**
     * 停止交易
     */
    stop: async function() {
        try {
            const result = await API.post('/trading/stop', {});
            Utils.showMessage('交易停止成功', 'warning');
            return result;
        } catch (error) {
            console.error('Stop trading error:', error);
            return null;
        }
    }
};

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    // 登录表单处理
    const loginForm = document.getElementById('login-form');
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            Auth.login(username, password);
        });
    }

    // 登出按钮处理
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function() {
            Utils.confirm('确定要登出吗？', function() {
                Auth.logout();
            });
        });
    }

    // 导航高亮
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.navbar-nav a');
    navLinks.forEach(link => {
        if (link.getAttribute('href') === currentPath) {
            link.style.backgroundColor = 'rgba(255,255,255,0.2)';
        }
    });

    // 初始化页面特定功能
    initPageSpecificFeatures();
});

// 初始化页面特定功能
function initPageSpecificFeatures() {
    const path = window.location.pathname;
    
    switch (path) {
        case '/dashboard':
            initDashboard();
            break;
        case '/trading':
            initTradingPage();
            break;
        case '/config':
            initConfigPage();
            break;
        case '/data':
            initDataPage();
            break;
        case '/monitoring':
            initMonitoringPage();
            break;
    }
}

// 仪表板初始化
function initDashboard() {
    loadDashboardData();
    
    // 设置定时刷新
    setInterval(loadDashboardData, CONFIG.REFRESH_INTERVAL);
}

// 加载仪表板数据
async function loadDashboardData() {
    try {
        // 加载交易状态
        const status = await Trading.getStatus();
        if (status) {
            updateTradingStatus(status);
        }

        // 加载持仓信息
        const positions = await Trading.getPositions();
        if (positions) {
            updatePositionsTable(positions);
        }
    } catch (error) {
        console.error('Load dashboard data error:', error);
    }
}

// 更新交易状态显示
function updateTradingStatus(status) {
    const statusContainer = document.getElementById('trading-status');
    if (!statusContainer) return;

    const cards = statusContainer.querySelectorAll('.card');
    
    // 更新各个指标卡片
    if (cards[0]) {
        const valueDiv = cards[0].querySelector('.metric-value');
        const labelDiv = cards[0].querySelector('.metric-label');
        valueDiv.innerHTML = Utils.formatCurrency(status.total_assets);
        labelDiv.textContent = '总资产';
    }
    
    if (cards[1]) {
        const valueDiv = cards[1].querySelector('.metric-value');
        const labelDiv = cards[1].querySelector('.metric-label');
        valueDiv.innerHTML = Utils.formatCurrency(status.daily_pnl);
        valueDiv.className = 'metric-value ' + Utils.getChangeColorClass(status.daily_pnl);
        labelDiv.textContent = '今日盈亏';
    }
    
    if (cards[2]) {
        const valueDiv = cards[2].querySelector('.metric-value');
        const labelDiv = cards[2].querySelector('.metric-label');
        valueDiv.innerHTML = status.total_positions;
        labelDiv.textContent = '持仓数量';
    }
    
    if (cards[3]) {
        const valueDiv = cards[3].querySelector('.metric-value');
        const labelDiv = cards[3].querySelector('.metric-label');
        valueDiv.innerHTML = status.is_trading ? 
            '<span class="status-indicator status-online"></span>运行中' : 
            '<span class="status-indicator status-offline"></span>已停止';
        labelDiv.textContent = '交易状态';
    }
}

// 更新持仓表格
function updatePositionsTable(positions) {
    const tbody = document.querySelector('#positions-table tbody');
    if (!tbody) return;

    if (positions.length === 0) {
        tbody.innerHTML = '<tr><td colspan="9" style="text-align: center;">暂无持仓</td></tr>';
        return;
    }

    tbody.innerHTML = positions.map(position => `
        <tr>
            <td>${position.symbol}</td>
            <td>${position.name}</td>
            <td>${position.quantity}</td>
            <td>${Utils.formatCurrency(position.avg_cost)}</td>
            <td>${Utils.formatCurrency(position.current_price)}</td>
            <td>${Utils.formatCurrency(position.market_value)}</td>
            <td class="${Utils.getChangeColorClass(position.unrealized_pnl)}">
                ${Utils.formatCurrency(position.unrealized_pnl)}
            </td>
            <td class="${Utils.getChangeColorClass(position.unrealized_pnl_pct)}">
                ${Utils.formatPercent(position.unrealized_pnl_pct)}
            </td>
            <td>
                <span class="badge ${position.position_type === 'base' ? 'badge-primary' : 'badge-info'}">
                    ${position.position_type === 'base' ? '底仓' : 'T仓'}
                </span>
            </td>
        </tr>
    `).join('');
}

// 交易控制函数
async function startTrading() {
    Utils.confirm('确定要启动交易吗？', async function() {
        const result = await Trading.start();
        if (result) {
            loadDashboardData(); // 刷新数据
        }
    });
}

async function stopTrading() {
    Utils.confirm('确定要停止交易吗？', async function() {
        const result = await Trading.stop();
        if (result) {
            loadDashboardData(); // 刷新数据
        }
    });
}

// 交易页面初始化
function initTradingPage() {
    // 交易页面特定初始化逻辑
    console.log('Trading page initialized');
}

// 配置页面初始化
function initConfigPage() {
    // 配置页面特定初始化逻辑
    console.log('Config page initialized');
}

// 数据页面初始化
function initDataPage() {
    // 数据页面特定初始化逻辑
    console.log('Data page initialized');
}

// 监控页面初始化
function initMonitoringPage() {
    // 监控页面特定初始化逻辑
    console.log('Monitoring page initialized');
}

// 导出全局对象
window.Utils = Utils;
window.API = API;
window.Auth = Auth;
window.Trading = Trading;