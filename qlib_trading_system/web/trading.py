# -*- coding: utf-8 -*-
"""
交易监控和控制面板模块

提供交易状态监控、订单管理、持仓查看等功能
"""

import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from fastapi import APIRouter, HTTPException, Depends, Query
from pydantic import BaseModel
import asyncio
import random

from qlib_trading_system.web.auth import get_current_user, require_permission

logger = logging.getLogger(__name__)

# 创建交易路由
trading_router = APIRouter()

class Position(BaseModel):
    """持仓模型"""
    symbol: str
    name: str
    quantity: int
    avg_cost: float
    current_price: float
    market_value: float
    unrealized_pnl: float
    unrealized_pnl_pct: float
    position_type: str  # 'base' or 't'

class Order(BaseModel):
    """订单模型"""
    order_id: str
    symbol: str
    name: str
    side: str  # 'buy' or 'sell'
    order_type: str  # 'market' or 'limit'
    quantity: int
    price: Optional[float]
    filled_quantity: int
    status: str  # 'pending', 'filled', 'cancelled'
    create_time: datetime
    update_time: datetime

class TradingStatus(BaseModel):
    """交易状态模型"""
    is_trading: bool
    market_status: str
    total_positions: int
    total_market_value: float
    available_cash: float
    total_assets: float
    daily_pnl: float
    daily_pnl_pct: float

class TradingManager:
    """交易管理器"""
    
    def __init__(self):
        self.is_trading = True
        self.positions = {}
        self.orders = {}
        self.trading_history = []
        
        # 模拟一些初始数据
        self._init_mock_data()
    
    def _init_mock_data(self):
        """初始化模拟数据"""
        # 模拟持仓数据
        mock_positions = [
            {
                "symbol": "000001.SZ",
                "name": "平安银行",
                "quantity": 1000,
                "avg_cost": 12.50,
                "current_price": 13.20,
                "position_type": "base"
            },
            {
                "symbol": "000002.SZ", 
                "name": "万科A",
                "quantity": 500,
                "avg_cost": 18.80,
                "current_price": 19.50,
                "position_type": "t"
            }
        ]
        
        for pos_data in mock_positions:
            market_value = pos_data["quantity"] * pos_data["current_price"]
            cost_value = pos_data["quantity"] * pos_data["avg_cost"]
            unrealized_pnl = market_value - cost_value
            unrealized_pnl_pct = (unrealized_pnl / cost_value) * 100
            
            position = Position(
                symbol=pos_data["symbol"],
                name=pos_data["name"],
                quantity=pos_data["quantity"],
                avg_cost=pos_data["avg_cost"],
                current_price=pos_data["current_price"],
                market_value=market_value,
                unrealized_pnl=unrealized_pnl,
                unrealized_pnl_pct=unrealized_pnl_pct,
                position_type=pos_data["position_type"]
            )
            
            self.positions[pos_data["symbol"]] = position
        
        # 模拟订单数据
        mock_orders = [
            {
                "order_id": "ORD001",
                "symbol": "000001.SZ",
                "name": "平安银行",
                "side": "buy",
                "order_type": "limit",
                "quantity": 100,
                "price": 13.00,
                "filled_quantity": 100,
                "status": "filled"
            },
            {
                "order_id": "ORD002",
                "symbol": "000002.SZ",
                "name": "万科A",
                "side": "sell",
                "order_type": "market",
                "quantity": 200,
                "price": None,
                "filled_quantity": 0,
                "status": "pending"
            }
        ]
        
        for order_data in mock_orders:
            order = Order(
                order_id=order_data["order_id"],
                symbol=order_data["symbol"],
                name=order_data["name"],
                side=order_data["side"],
                order_type=order_data["order_type"],
                quantity=order_data["quantity"],
                price=order_data["price"],
                filled_quantity=order_data["filled_quantity"],
                status=order_data["status"],
                create_time=datetime.now() - timedelta(minutes=random.randint(1, 60)),
                update_time=datetime.now()
            )
            
            self.orders[order_data["order_id"]] = order
    
    def get_trading_status(self) -> TradingStatus:
        """获取交易状态"""
        total_market_value = sum(pos.market_value for pos in self.positions.values())
        daily_pnl = sum(pos.unrealized_pnl for pos in self.positions.values())
        
        return TradingStatus(
            is_trading=self.is_trading,
            market_status="开盘" if self.is_trading else "休市",
            total_positions=len(self.positions),
            total_market_value=total_market_value,
            available_cash=50000.0,  # 模拟可用资金
            total_assets=total_market_value + 50000.0,
            daily_pnl=daily_pnl,
            daily_pnl_pct=(daily_pnl / (total_market_value - daily_pnl)) * 100 if total_market_value > daily_pnl else 0
        )
    
    def get_positions(self) -> List[Position]:
        """获取持仓列表"""
        return list(self.positions.values())
    
    def get_orders(self, status: Optional[str] = None) -> List[Order]:
        """获取订单列表"""
        orders = list(self.orders.values())
        if status:
            orders = [order for order in orders if order.status == status]
        return sorted(orders, key=lambda x: x.create_time, reverse=True)
    
    def place_order(self, symbol: str, side: str, quantity: int, order_type: str = "market", price: Optional[float] = None) -> str:
        """下单"""
        order_id = f"ORD{len(self.orders) + 1:03d}"
        
        order = Order(
            order_id=order_id,
            symbol=symbol,
            name=f"股票{symbol}",  # 实际应该从数据库获取
            side=side,
            order_type=order_type,
            quantity=quantity,
            price=price,
            filled_quantity=0,
            status="pending",
            create_time=datetime.now(),
            update_time=datetime.now()
        )
        
        self.orders[order_id] = order
        logger.info(f"下单成功: {order_id}")
        return order_id
    
    def cancel_order(self, order_id: str) -> bool:
        """撤单"""
        if order_id in self.orders:
            order = self.orders[order_id]
            if order.status == "pending":
                order.status = "cancelled"
                order.update_time = datetime.now()
                logger.info(f"撤单成功: {order_id}")
                return True
        return False
    
    def start_trading(self):
        """开始交易"""
        self.is_trading = True
        logger.info("交易已启动")
    
    def stop_trading(self):
        """停止交易"""
        self.is_trading = False
        logger.info("交易已停止")

# 全局交易管理器实例
trading_manager = TradingManager()

@trading_router.get("/status")
async def get_trading_status(current_user: Dict = Depends(require_permission("view_dashboard"))):
    """获取交易状态"""
    try:
        status = trading_manager.get_trading_status()
        return {
            "status": status.dict(),
            "message": "交易状态获取成功"
        }
    except Exception as e:
        logger.error(f"获取交易状态错误: {e}")
        raise HTTPException(status_code=500, detail="获取交易状态失败")

@trading_router.get("/positions")
async def get_positions(current_user: Dict = Depends(require_permission("view_dashboard"))):
    """获取持仓列表"""
    try:
        positions = trading_manager.get_positions()
        return {
            "positions": [pos.dict() for pos in positions],
            "total": len(positions),
            "message": "持仓列表获取成功"
        }
    except Exception as e:
        logger.error(f"获取持仓列表错误: {e}")
        raise HTTPException(status_code=500, detail="获取持仓列表失败")

@trading_router.get("/orders")
async def get_orders(
    status: Optional[str] = Query(None, description="订单状态过滤"),
    current_user: Dict = Depends(require_permission("view_dashboard"))
):
    """获取订单列表"""
    try:
        orders = trading_manager.get_orders(status)
        return {
            "orders": [order.dict() for order in orders],
            "total": len(orders),
            "message": "订单列表获取成功"
        }
    except Exception as e:
        logger.error(f"获取订单列表错误: {e}")
        raise HTTPException(status_code=500, detail="获取订单列表失败")

from pydantic import BaseModel

class OrderRequest(BaseModel):
    """下单请求模型"""
    symbol: str
    side: str
    quantity: int
    order_type: str = "market"
    price: Optional[float] = None

@trading_router.post("/orders")
async def place_order(
    order_request: OrderRequest,
    current_user: Dict = Depends(require_permission("manage_trading"))
):
    """下单"""
    try:
        # 验证参数
        if order_request.side not in ["buy", "sell"]:
            raise HTTPException(status_code=400, detail="无效的交易方向")
        
        if order_request.order_type not in ["market", "limit"]:
            raise HTTPException(status_code=400, detail="无效的订单类型")
        
        if order_request.order_type == "limit" and order_request.price is None:
            raise HTTPException(status_code=400, detail="限价单必须指定价格")
        
        if order_request.quantity <= 0:
            raise HTTPException(status_code=400, detail="数量必须大于0")
        
        # 检查交易状态
        if not trading_manager.is_trading:
            raise HTTPException(status_code=400, detail="交易已停止")
        
        order_id = trading_manager.place_order(
            order_request.symbol, 
            order_request.side, 
            order_request.quantity, 
            order_request.order_type, 
            order_request.price
        )
        
        return {
            "order_id": order_id,
            "message": "下单成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"下单错误: {e}")
        raise HTTPException(status_code=500, detail="下单失败")

@trading_router.delete("/orders/{order_id}")
async def cancel_order(
    order_id: str,
    current_user: Dict = Depends(require_permission("manage_trading"))
):
    """撤单"""
    try:
        success = trading_manager.cancel_order(order_id)
        if success:
            return {"message": "撤单成功"}
        else:
            raise HTTPException(status_code=404, detail="订单不存在或无法撤销")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"撤单错误: {e}")
        raise HTTPException(status_code=500, detail="撤单失败")

@trading_router.post("/start")
async def start_trading(current_user: Dict = Depends(require_permission("manage_trading"))):
    """启动交易"""
    try:
        trading_manager.start_trading()
        return {"message": "交易启动成功"}
    except Exception as e:
        logger.error(f"启动交易错误: {e}")
        raise HTTPException(status_code=500, detail="启动交易失败")

@trading_router.post("/stop")
async def stop_trading(current_user: Dict = Depends(require_permission("manage_trading"))):
    """停止交易"""
    try:
        trading_manager.stop_trading()
        return {"message": "交易停止成功"}
    except Exception as e:
        logger.error(f"停止交易错误: {e}")
        raise HTTPException(status_code=500, detail="停止交易失败")

@trading_router.get("/realtime")
async def get_realtime_data(current_user: Dict = Depends(require_permission("view_dashboard"))):
    """获取实时交易数据"""
    try:
        # 模拟实时数据更新
        status = trading_manager.get_trading_status()
        positions = trading_manager.get_positions()
        recent_orders = trading_manager.get_orders()[:5]  # 最近5个订单
        
        # 模拟价格波动
        for position in positions:
            # 随机价格波动 ±2%
            price_change = random.uniform(-0.02, 0.02)
            position.current_price = round(position.current_price * (1 + price_change), 2)
            position.market_value = position.quantity * position.current_price
            position.unrealized_pnl = position.market_value - (position.quantity * position.avg_cost)
            position.unrealized_pnl_pct = (position.unrealized_pnl / (position.quantity * position.avg_cost)) * 100
        
        return {
            "status": status.dict(),
            "positions": [pos.dict() for pos in positions],
            "recent_orders": [order.dict() for order in recent_orders],
            "timestamp": datetime.now().isoformat(),
            "message": "实时数据获取成功"
        }
        
    except Exception as e:
        logger.error(f"获取实时数据错误: {e}")
        raise HTTPException(status_code=500, detail="获取实时数据失败")