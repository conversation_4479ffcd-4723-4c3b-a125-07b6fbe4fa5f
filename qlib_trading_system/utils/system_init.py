"""
系统初始化工具
"""
import os
import sys
import subprocess
from pathlib import Path
from typing import Dict, List, Optional

from qlib_trading_system.utils.logging.logger import system_logger
from qlib_trading_system.utils.config.manager import config_manager
from qlib_trading_system.utils.validators import config_validator
from qlib_trading_system.utils.database import db_manager


class SystemInitializer:
    """系统初始化器"""
    
    def __init__(self):
        """初始化"""
        self.logger = system_logger
        self.config = config_manager
        self.validator = config_validator
        self.db_manager = db_manager
    
    def install_dependencies(self, force: bool = False) -> bool:
        """安装依赖包"""
        self.logger.info("开始安装依赖包...")
        
        try:
            # 检查requirements.txt是否存在
            requirements_file = Path("requirements.txt")
            if not requirements_file.exists():
                self.logger.error("requirements.txt文件不存在")
                return False
            
            # 安装依赖
            cmd = [sys.executable, "-m", "pip", "install", "-r", "requirements.txt"]
            if force:
                cmd.append("--force-reinstall")
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=600  # 10分钟超时
            )
            
            if result.returncode == 0:
                self.logger.info("依赖包安装成功")
                return True
            else:
                self.logger.error(f"依赖包安装失败: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            self.logger.error("依赖包安装超时")
            return False
        except Exception as e:
            self.logger.error(f"依赖包安装异常: {str(e)}")
            return False
    
    def setup_directories(self) -> bool:
        """设置目录结构"""
        self.logger.info("创建项目目录结构...")
        
        directories = [
            # 日志目录
            "logs",
            
            # 数据目录
            "data/raw/stock_prices",
            "data/raw/financial_data",
            "data/raw/news_data",
            "data/processed/features",
            "data/processed/indicators",
            
            # 模型目录
            "models/stock_selection/checkpoints",
            "models/stock_selection/configs",
            "models/intraday_trading/checkpoints", 
            "models/intraday_trading/configs",
            
            # 回测目录
            "backtest_results/stock_selection",
            "backtest_results/intraday_trading",
            
            # 报告目录
            "reports/daily",
            "reports/weekly",
            "reports/monthly",
            
            # 配置目录
            "configs/strategies",
            "configs/models",
            
            # 临时目录
            "temp/downloads",
            "temp/processing"
        ]
        
        try:
            for directory in directories:
                Path(directory).mkdir(parents=True, exist_ok=True)
                self.logger.debug(f"创建目录: {directory}")
            
            self.logger.info("目录结构创建完成")
            return True
            
        except Exception as e:
            self.logger.error(f"目录创建失败: {str(e)}")
            return False
    
    def setup_environment_file(self) -> bool:
        """设置环境配置文件"""
        env_file = Path(".env")
        env_example = Path(".env.example")
        
        if env_file.exists():
            self.logger.info(".env文件已存在，跳过创建")
            return True
        
        if not env_example.exists():
            self.logger.error(".env.example文件不存在")
            return False
        
        try:
            # 复制示例文件
            import shutil
            shutil.copy(env_example, env_file)
            
            self.logger.info("已创建.env文件，请根据实际情况修改配置")
            return True
            
        except Exception as e:
            self.logger.error(f"创建.env文件失败: {str(e)}")
            return False
    
    def initialize_qlib(self) -> bool:
        """初始化Qlib框架"""
        self.logger.info("初始化Qlib框架...")
        
        try:
            from qlib_trading_system.utils.qlib_init import qlib_initializer
            
            # 初始化qlib
            if not qlib_initializer.initialize():
                self.logger.error("Qlib初始化失败")
                return False
            
            # 检查数据可用性
            if not qlib_initializer.check_data_availability():
                self.logger.warning("Qlib数据检查未通过，可能需要下载数据")
                self._suggest_data_download()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Qlib初始化异常: {str(e)}")
            return False
    
    def initialize_databases(self) -> bool:
        """初始化数据库"""
        self.logger.info("初始化数据库连接和表结构...")
        
        try:
            # 测试数据库连接
            connection_results = self.db_manager.test_connections()
            
            failed_connections = [
                db for db, success in connection_results.items() 
                if not success
            ]
            
            if failed_connections:
                self.logger.warning(f"以下数据库连接失败: {failed_connections}")
                self.logger.warning("系统将在有限模式下运行")
            
            # 初始化表结构（仅对成功连接的数据库）
            if connection_results.get("clickhouse", False):
                self.db_manager.initialize_schemas()
            
            return True
            
        except Exception as e:
            self.logger.error(f"数据库初始化失败: {str(e)}")
            return False
    
    def run_system_validation(self) -> bool:
        """运行系统验证"""
        self.logger.info("运行系统配置验证...")
        
        validation_results = self.validator.run_full_validation()
        
        # 检查关键验证项
        critical_checks = [
            ("environment", "python_version"),
            ("environment", "directories"),
            ("trading", "position_ratios"),
            ("trading", "risk_parameters")
        ]
        
        failed_critical = []
        for category, check in critical_checks:
            if not validation_results.get(category, {}).get(check, False):
                failed_critical.append(f"{category}.{check}")
        
        if failed_critical:
            self.logger.error(f"关键验证项失败: {failed_critical}")
            return False
        
        self.logger.info("系统验证通过")
        return True
    
    def _suggest_data_download(self):
        """建议数据下载方法"""
        self.logger.info("=" * 50)
        self.logger.info("Qlib数据下载建议:")
        self.logger.info("1. 下载中国股市数据:")
        self.logger.info("   python -m qlib.run.get_data qlib_data --target_dir ~/.qlib/qlib_data/cn_data --region cn")
        self.logger.info("2. 或使用在线数据:")
        self.logger.info("   qlib.init(provider_uri='https://qlib.blob.core.windows.net/qlib_data/cn_data')")
        self.logger.info("=" * 50)
    
    def full_initialization(self) -> bool:
        """完整系统初始化"""
        self.logger.info("=" * 60)
        self.logger.info("开始Qlib双AI交易系统完整初始化")
        self.logger.info("=" * 60)
        
        initialization_steps = [
            ("设置目录结构", self.setup_directories),
            ("设置环境文件", self.setup_environment_file),
            ("安装依赖包", self.install_dependencies),
            ("初始化Qlib", self.initialize_qlib),
            ("初始化数据库", self.initialize_databases),
            ("系统验证", self.run_system_validation)
        ]
        
        for step_name, step_func in initialization_steps:
            self.logger.info(f"执行步骤: {step_name}")
            
            try:
                if not step_func():
                    self.logger.error(f"步骤失败: {step_name}")
                    return False
                
                self.logger.info(f"步骤完成: {step_name}")
                
            except Exception as e:
                self.logger.error(f"步骤异常: {step_name}, 错误: {str(e)}")
                return False
        
        self.logger.info("=" * 60)
        self.logger.info("Qlib双AI交易系统初始化完成")
        self.logger.info("=" * 60)
        
        return True


# 全局初始化器实例
system_initializer = SystemInitializer()