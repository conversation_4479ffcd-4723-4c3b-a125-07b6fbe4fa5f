"""
配置验证工具
"""
import os
import socket
from typing import Dict, List, Tuple, Any
from pathlib import Path

from qlib_trading_system.utils.logging.logger import system_logger


class ConfigValidator:
    """配置验证器"""
    
    def __init__(self):
        """初始化验证器"""
        self.logger = system_logger
    
    def validate_environment(self) -> Dict[str, bool]:
        """验证环境配置"""
        results = {}
        
        # 检查Python版本
        import sys
        python_version = sys.version_info
        results["python_version"] = python_version >= (3, 8)
        
        if not results["python_version"]:
            self.logger.error(f"Python版本过低: {python_version}, 需要3.8+")
        
        # 检查必要的目录
        required_dirs = [
            "logs",
            "data/raw", 
            "data/processed",
            "models/stock_selection",
            "models/intraday_trading"
        ]
        
        results["directories"] = True
        for directory in required_dirs:
            if not Path(directory).exists():
                results["directories"] = False
                self.logger.error(f"缺少必要目录: {directory}")
        
        # 检查环境变量
        required_env_vars = [
            "TOTAL_CAPITAL",
            "CAPITAL_MODE",
            "PRIMARY_DATA_SOURCE"
        ]
        
        results["environment_variables"] = True
        for env_var in required_env_vars:
            if not os.getenv(env_var):
                results["environment_variables"] = False
                self.logger.warning(f"缺少环境变量: {env_var}")
        
        return results
    
    def validate_dependencies(self) -> Dict[str, bool]:
        """验证依赖包"""
        results = {}
        
        # 核心依赖包
        core_packages = [
            ("qlib", "0.9.0"),
            ("pandas", "1.5.0"),
            ("numpy", "1.23.0"),
            ("lightgbm", "3.3.0"),
            ("torch", "1.13.0")
        ]
        
        for package_name, min_version in core_packages:
            try:
                __import__(package_name)
                results[package_name] = True
                self.logger.debug(f"依赖包检查通过: {package_name}")
            except ImportError:
                results[package_name] = False
                self.logger.error(f"缺少依赖包: {package_name}>={min_version}")
        
        return results
    
    def validate_database_config(self) -> Dict[str, bool]:
        """验证数据库配置"""
        results = {}
        
        from qlib_trading_system.utils.config.manager import config_manager
        db_config = config_manager.database
        
        # 检查ClickHouse连接
        results["clickhouse"] = self._test_connection(
            db_config.CLICKHOUSE_HOST,
            db_config.CLICKHOUSE_PORT,
            "ClickHouse"
        )
        
        # 检查Redis连接
        results["redis"] = self._test_connection(
            db_config.REDIS_HOST,
            db_config.REDIS_PORT,
            "Redis"
        )
        
        # 检查MongoDB连接
        results["mongodb"] = self._test_connection(
            db_config.MONGODB_HOST,
            db_config.MONGODB_PORT,
            "MongoDB"
        )
        
        return results
    
    def validate_trading_config(self) -> Dict[str, bool]:
        """验证交易配置"""
        results = {}
        
        from qlib_trading_system.utils.config.manager import config_manager
        trading_config = config_manager.trading
        
        # 验证资金配置
        total_ratio = (
            trading_config.BASE_POSITION_RATIO +
            trading_config.T_POSITION_RATIO +
            trading_config.CASH_RESERVE_RATIO
        )
        results["position_ratios"] = abs(total_ratio - 1.0) < 0.01
        
        if not results["position_ratios"]:
            self.logger.error(f"仓位比例配置错误，总和为: {total_ratio}")
        
        # 验证风险参数
        results["risk_parameters"] = (
            0 < trading_config.MAX_SINGLE_LOSS < 1 and
            0 < trading_config.MAX_DAILY_LOSS < 1 and
            0 < trading_config.MAX_DRAWDOWN < 1
        )
        
        if not results["risk_parameters"]:
            self.logger.error("风险参数配置错误")
        
        # 验证资金模式
        valid_modes = ["small", "medium", "large"]
        results["capital_mode"] = trading_config.CAPITAL_MODE in valid_modes
        
        if not results["capital_mode"]:
            self.logger.error(f"无效的资金模式: {trading_config.CAPITAL_MODE}")
        
        return results
    
    def validate_data_source_config(self) -> Dict[str, bool]:
        """验证数据源配置"""
        results = {}
        
        from qlib_trading_system.utils.config.manager import config_manager
        data_config = config_manager.data_source
        
        # 检查主要数据源配置
        valid_sources = ["iTick", "JoinQuant", "RiceQuant"]
        results["primary_source"] = data_config.PRIMARY_DATA_SOURCE in valid_sources
        
        if not results["primary_source"]:
            self.logger.error(f"无效的主要数据源: {data_config.PRIMARY_DATA_SOURCE}")
        
        # 检查API密钥配置（警告级别）
        if data_config.PRIMARY_DATA_SOURCE == "iTick":
            results["api_keys"] = bool(data_config.ITICK_API_KEY)
            if not results["api_keys"]:
                self.logger.warning("iTick API密钥未配置")
        
        return results
    
    def _test_connection(self, host: str, port: int, service_name: str) -> bool:
        """测试网络连接"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((host, port))
            sock.close()
            
            if result == 0:
                self.logger.debug(f"{service_name}连接测试通过: {host}:{port}")
                return True
            else:
                self.logger.warning(f"{service_name}连接测试失败: {host}:{port}")
                return False
                
        except Exception as e:
            self.logger.warning(f"{service_name}连接测试异常: {str(e)}")
            return False
    
    def run_full_validation(self) -> Dict[str, Dict[str, bool]]:
        """运行完整验证"""
        self.logger.info("开始系统配置验证...")
        
        validation_results = {
            "environment": self.validate_environment(),
            "dependencies": self.validate_dependencies(),
            "database": self.validate_database_config(),
            "trading": self.validate_trading_config(),
            "data_source": self.validate_data_source_config()
        }
        
        # 统计验证结果
        total_checks = 0
        passed_checks = 0
        
        for category, results in validation_results.items():
            for check, result in results.items():
                total_checks += 1
                if result:
                    passed_checks += 1
        
        success_rate = passed_checks / total_checks if total_checks > 0 else 0
        
        self.logger.info(f"配置验证完成: {passed_checks}/{total_checks} 项通过 ({success_rate:.1%})")
        
        return validation_results


# 全局验证器实例
config_validator = ConfigValidator()