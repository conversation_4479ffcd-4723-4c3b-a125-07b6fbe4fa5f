"""
日志备份和长期存储方案
实现日志的自动备份、压缩、归档和长期存储功能
"""
import os
import gzip
import shutil
import tarfile
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from pathlib import Path
from dataclasses import dataclass
from enum import Enum
import threading
import schedule
import time

from .logger import trading_logger


class BackupType(Enum):
    """备份类型"""
    DAILY = "daily"         # 日备份
    WEEKLY = "weekly"       # 周备份
    MONTHLY = "monthly"     # 月备份
    YEARLY = "yearly"       # 年备份


class CompressionType(Enum):
    """压缩类型"""
    GZIP = "gzip"           # gzip压缩
    TAR_GZ = "tar.gz"       # tar.gz压缩
    ZIP = "zip"             # zip压缩


class StorageType(Enum):
    """存储类型"""
    LOCAL = "local"         # 本地存储
    CLOUD = "cloud"         # 云存储
    NETWORK = "network"     # 网络存储


@dataclass
class BackupConfig:
    """备份配置"""
    backup_type: BackupType             # 备份类型
    source_path: str                    # 源路径
    backup_path: str                    # 备份路径
    compression: CompressionType        # 压缩类型
    storage_type: StorageType           # 存储类型
    retention_days: int                 # 保留天数
    enabled: bool = True                # 是否启用
    schedule_time: str = "02:00"        # 调度时间
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'backup_type': self.backup_type.value,
            'source_path': self.source_path,
            'backup_path': self.backup_path,
            'compression': self.compression.value,
            'storage_type': self.storage_type.value,
            'retention_days': self.retention_days,
            'enabled': self.enabled,
            'schedule_time': self.schedule_time
        }


@dataclass
class BackupRecord:
    """备份记录"""
    backup_id: str                      # 备份ID
    backup_type: BackupType             # 备份类型
    source_path: str                    # 源路径
    backup_path: str                    # 备份路径
    file_size: int                      # 文件大小
    compressed_size: int                # 压缩后大小
    compression_ratio: float            # 压缩比
    start_time: datetime                # 开始时间
    end_time: datetime                  # 结束时间
    duration: float                     # 持续时间（秒）
    status: str                         # 状态
    error_message: Optional[str] = None # 错误信息
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'backup_id': self.backup_id,
            'backup_type': self.backup_type.value,
            'source_path': self.source_path,
            'backup_path': self.backup_path,
            'file_size': self.file_size,
            'compressed_size': self.compressed_size,
            'compression_ratio': self.compression_ratio,
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat(),
            'duration': self.duration,
            'status': self.status,
            'error_message': self.error_message
        }


class LogBackupManager:
    """日志备份管理器"""
    
    def __init__(self, config_file: str = "config/log_backup_config.json"):
        """初始化日志备份管理器"""
        self.logger = trading_logger.get_logger("LOG_BACKUP")
        self.config_file = config_file
        self.configs: Dict[str, BackupConfig] = {}
        self.backup_records: List[BackupRecord] = []
        self._running = False
        self._scheduler_thread = None
        
        # 创建必要的目录
        self._create_directories()
        
        # 加载配置
        self._load_config()
        
        # 加载默认配置
        self._load_default_configs()
    
    def _create_directories(self):
        """创建必要的目录"""
        directories = [
            "logs/backup",
            "logs/archive",
            "config"
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    def _load_config(self):
        """加载配置文件"""
        try:
            if Path(self.config_file).exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                for config_id, config_dict in config_data.items():
                    self.configs[config_id] = BackupConfig(
                        backup_type=BackupType(config_dict['backup_type']),
                        source_path=config_dict['source_path'],
                        backup_path=config_dict['backup_path'],
                        compression=CompressionType(config_dict['compression']),
                        storage_type=StorageType(config_dict['storage_type']),
                        retention_days=config_dict['retention_days'],
                        enabled=config_dict.get('enabled', True),
                        schedule_time=config_dict.get('schedule_time', '02:00')
                    )
                
                self.logger.info(f"加载备份配置: {len(self.configs)}个配置")
        except Exception as e:
            self.logger.error(f"加载备份配置失败: {str(e)}")
    
    def _save_config(self):
        """保存配置文件"""
        try:
            config_data = {}
            for config_id, config in self.configs.items():
                config_data[config_id] = config.to_dict()
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
            
            self.logger.info("保存备份配置成功")
        except Exception as e:
            self.logger.error(f"保存备份配置失败: {str(e)}")
    
    def _load_default_configs(self):
        """加载默认备份配置"""
        # 交易日志日备份
        self.add_backup_config(
            config_id="trading_daily",
            config=BackupConfig(
                backup_type=BackupType.DAILY,
                source_path="logs/trading",
                backup_path="logs/backup/trading/daily",
                compression=CompressionType.GZIP,
                storage_type=StorageType.LOCAL,
                retention_days=30,
                schedule_time="02:00"
            )
        )
        
        # 审计日志日备份
        self.add_backup_config(
            config_id="audit_daily",
            config=BackupConfig(
                backup_type=BackupType.DAILY,
                source_path="logs/audit",
                backup_path="logs/backup/audit/daily",
                compression=CompressionType.TAR_GZ,
                storage_type=StorageType.LOCAL,
                retention_days=2555,  # 7年
                schedule_time="02:30"
            )
        )
        
        # 系统日志周备份
        self.add_backup_config(
            config_id="system_weekly",
            config=BackupConfig(
                backup_type=BackupType.WEEKLY,
                source_path="logs/system",
                backup_path="logs/backup/system/weekly",
                compression=CompressionType.TAR_GZ,
                storage_type=StorageType.LOCAL,
                retention_days=365,
                schedule_time="03:00"
            )
        )
        
        # 风险日志月备份
        self.add_backup_config(
            config_id="risk_monthly",
            config=BackupConfig(
                backup_type=BackupType.MONTHLY,
                source_path="logs/risk",
                backup_path="logs/backup/risk/monthly",
                compression=CompressionType.TAR_GZ,
                storage_type=StorageType.LOCAL,
                retention_days=1825,  # 5年
                schedule_time="04:00"
            )
        )
    
    def add_backup_config(self, config_id: str, config: BackupConfig):
        """添加备份配置"""
        self.configs[config_id] = config
        self._save_config()
        self.logger.info(f"添加备份配置: {config_id}")
    
    def remove_backup_config(self, config_id: str):
        """移除备份配置"""
        if config_id in self.configs:
            del self.configs[config_id]
            self._save_config()
            self.logger.info(f"移除备份配置: {config_id}")
    
    def enable_backup_config(self, config_id: str):
        """启用备份配置"""
        if config_id in self.configs:
            self.configs[config_id].enabled = True
            self._save_config()
            self.logger.info(f"启用备份配置: {config_id}")
    
    def disable_backup_config(self, config_id: str):
        """禁用备份配置"""
        if config_id in self.configs:
            self.configs[config_id].enabled = False
            self._save_config()
            self.logger.info(f"禁用备份配置: {config_id}")
    
    def backup_logs(self, config_id: str) -> BackupRecord:
        """执行日志备份"""
        if config_id not in self.configs:
            raise ValueError(f"备份配置不存在: {config_id}")
        
        config = self.configs[config_id]
        if not config.enabled:
            raise ValueError(f"备份配置已禁用: {config_id}")
        
        start_time = datetime.now()
        backup_id = f"{config_id}_{start_time.strftime('%Y%m%d_%H%M%S')}"
        
        try:
            self.logger.info(f"开始备份: {config_id}")
            
            # 创建备份目录
            backup_dir = Path(config.backup_path)
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            # 生成备份文件名
            backup_filename = self._generate_backup_filename(config, start_time)
            backup_file_path = backup_dir / backup_filename
            
            # 获取源文件大小
            source_size = self._get_directory_size(config.source_path)
            
            # 执行备份
            if config.compression == CompressionType.GZIP:
                compressed_size = self._backup_with_gzip(config.source_path, backup_file_path)
            elif config.compression == CompressionType.TAR_GZ:
                compressed_size = self._backup_with_tar_gz(config.source_path, backup_file_path)
            else:
                compressed_size = self._backup_without_compression(config.source_path, backup_file_path)
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            compression_ratio = compressed_size / source_size if source_size > 0 else 0
            
            # 创建备份记录
            backup_record = BackupRecord(
                backup_id=backup_id,
                backup_type=config.backup_type,
                source_path=config.source_path,
                backup_path=str(backup_file_path),
                file_size=source_size,
                compressed_size=compressed_size,
                compression_ratio=compression_ratio,
                start_time=start_time,
                end_time=end_time,
                duration=duration,
                status="SUCCESS"
            )
            
            self.backup_records.append(backup_record)
            self.logger.info(f"备份完成: {config_id}, 压缩比: {compression_ratio:.2%}, 耗时: {duration:.2f}秒")
            
            # 清理过期备份
            self._cleanup_old_backups(config)
            
            return backup_record
            
        except Exception as e:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            backup_record = BackupRecord(
                backup_id=backup_id,
                backup_type=config.backup_type,
                source_path=config.source_path,
                backup_path="",
                file_size=0,
                compressed_size=0,
                compression_ratio=0,
                start_time=start_time,
                end_time=end_time,
                duration=duration,
                status="FAILED",
                error_message=str(e)
            )
            
            self.backup_records.append(backup_record)
            self.logger.error(f"备份失败: {config_id}, 错误: {str(e)}")
            raise
    
    def _generate_backup_filename(self, config: BackupConfig, timestamp: datetime) -> str:
        """生成备份文件名"""
        if config.backup_type == BackupType.DAILY:
            date_str = timestamp.strftime('%Y%m%d')
        elif config.backup_type == BackupType.WEEKLY:
            # 获取周数
            year, week, _ = timestamp.isocalendar()
            date_str = f"{year}W{week:02d}"
        elif config.backup_type == BackupType.MONTHLY:
            date_str = timestamp.strftime('%Y%m')
        elif config.backup_type == BackupType.YEARLY:
            date_str = timestamp.strftime('%Y')
        else:
            date_str = timestamp.strftime('%Y%m%d_%H%M%S')
        
        source_name = Path(config.source_path).name
        
        if config.compression == CompressionType.GZIP:
            extension = ".gz"
        elif config.compression == CompressionType.TAR_GZ:
            extension = ".tar.gz"
        elif config.compression == CompressionType.ZIP:
            extension = ".zip"
        else:
            extension = ""
        
        return f"{source_name}_{date_str}{extension}"
    
    def _get_directory_size(self, directory: str) -> int:
        """获取目录大小"""
        total_size = 0
        try:
            for dirpath, dirnames, filenames in os.walk(directory):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    if os.path.exists(filepath):
                        total_size += os.path.getsize(filepath)
        except Exception as e:
            self.logger.warning(f"计算目录大小失败 {directory}: {str(e)}")
        return total_size
    
    def _backup_with_gzip(self, source_path: str, backup_path: Path) -> int:
        """使用gzip压缩备份"""
        total_size = 0
        
        # 如果是单个文件
        if os.path.isfile(source_path):
            with open(source_path, 'rb') as f_in:
                with gzip.open(backup_path, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            total_size = backup_path.stat().st_size
        else:
            # 如果是目录，先打包再压缩
            tar_path = backup_path.with_suffix('')
            with tarfile.open(tar_path, 'w') as tar:
                tar.add(source_path, arcname=Path(source_path).name)
            
            # 压缩tar文件
            with open(tar_path, 'rb') as f_in:
                with gzip.open(backup_path, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            
            total_size = backup_path.stat().st_size
            # 删除临时tar文件
            tar_path.unlink()
        
        return total_size
    
    def _backup_with_tar_gz(self, source_path: str, backup_path: Path) -> int:
        """使用tar.gz压缩备份"""
        with tarfile.open(backup_path, 'w:gz') as tar:
            tar.add(source_path, arcname=Path(source_path).name)
        
        return backup_path.stat().st_size
    
    def _backup_without_compression(self, source_path: str, backup_path: Path) -> int:
        """不压缩备份"""
        if os.path.isfile(source_path):
            shutil.copy2(source_path, backup_path)
        else:
            shutil.copytree(source_path, backup_path)
        
        return self._get_directory_size(str(backup_path))
    
    def _cleanup_old_backups(self, config: BackupConfig):
        """清理过期备份"""
        try:
            backup_dir = Path(config.backup_path)
            if not backup_dir.exists():
                return
            
            cutoff_date = datetime.now() - timedelta(days=config.retention_days)
            
            for backup_file in backup_dir.iterdir():
                if backup_file.is_file():
                    # 获取文件修改时间
                    file_mtime = datetime.fromtimestamp(backup_file.stat().st_mtime)
                    
                    if file_mtime < cutoff_date:
                        backup_file.unlink()
                        self.logger.info(f"删除过期备份: {backup_file}")
                        
        except Exception as e:
            self.logger.error(f"清理过期备份失败: {str(e)}")
    
    def start_scheduler(self):
        """启动调度器"""
        if self._running:
            return
        
        self._running = True
        
        # 设置调度任务
        for config_id, config in self.configs.items():
            if not config.enabled:
                continue
            
            if config.backup_type == BackupType.DAILY:
                schedule.every().day.at(config.schedule_time).do(
                    self._scheduled_backup, config_id
                )
            elif config.backup_type == BackupType.WEEKLY:
                schedule.every().monday.at(config.schedule_time).do(
                    self._scheduled_backup, config_id
                )
            elif config.backup_type == BackupType.MONTHLY:
                # 使用每月第一天执行
                schedule.every().day.at(config.schedule_time).do(
                    self._monthly_backup_check, config_id
                )
        
        # 启动调度线程
        self._scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self._scheduler_thread.start()
        
        self.logger.info("日志备份调度器已启动")
    
    def stop_scheduler(self):
        """停止调度器"""
        self._running = False
        schedule.clear()
        
        if self._scheduler_thread and self._scheduler_thread.is_alive():
            self._scheduler_thread.join(timeout=5)
        
        self.logger.info("日志备份调度器已停止")
    
    def _run_scheduler(self):
        """运行调度器"""
        while self._running:
            try:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
            except Exception as e:
                self.logger.error(f"调度器运行错误: {str(e)}")
    
    def _scheduled_backup(self, config_id: str):
        """调度备份任务"""
        try:
            self.backup_logs(config_id)
        except Exception as e:
            self.logger.error(f"调度备份失败 {config_id}: {str(e)}")
    
    def _monthly_backup_check(self, config_id: str):
        """月度备份检查"""
        try:
            # 只在每月第一天执行
            if datetime.now().day == 1:
                self.backup_logs(config_id)
        except Exception as e:
            self.logger.error(f"月度备份失败 {config_id}: {str(e)}")
    
    def restore_backup(self, backup_path: str, restore_path: str):
        """恢复备份"""
        try:
            backup_file = Path(backup_path)
            if not backup_file.exists():
                raise FileNotFoundError(f"备份文件不存在: {backup_path}")
            
            restore_dir = Path(restore_path)
            restore_dir.mkdir(parents=True, exist_ok=True)
            
            # 根据文件扩展名确定解压方式
            if backup_path.endswith('.tar.gz'):
                with tarfile.open(backup_path, 'r:gz') as tar:
                    tar.extractall(restore_dir)
            elif backup_path.endswith('.gz'):
                # 先解压gzip
                with gzip.open(backup_path, 'rb') as f_in:
                    with open(restore_dir / 'temp.tar', 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)
                
                # 再解压tar
                with tarfile.open(restore_dir / 'temp.tar', 'r') as tar:
                    tar.extractall(restore_dir)
                
                # 删除临时文件
                (restore_dir / 'temp.tar').unlink()
            else:
                # 直接复制
                if backup_file.is_file():
                    shutil.copy2(backup_path, restore_dir)
                else:
                    shutil.copytree(backup_path, restore_dir / backup_file.name)
            
            self.logger.info(f"备份恢复成功: {backup_path} -> {restore_path}")
            
        except Exception as e:
            self.logger.error(f"备份恢复失败: {str(e)}")
            raise
    
    def get_backup_records(
        self,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        status: Optional[str] = None
    ) -> List[BackupRecord]:
        """获取备份记录"""
        filtered_records = self.backup_records
        
        if start_time:
            filtered_records = [r for r in filtered_records if r.start_time >= start_time]
        
        if end_time:
            filtered_records = [r for r in filtered_records if r.start_time <= end_time]
        
        if status:
            filtered_records = [r for r in filtered_records if r.status == status]
        
        return filtered_records
    
    def generate_backup_report(self) -> Dict[str, Any]:
        """生成备份报告"""
        total_backups = len(self.backup_records)
        successful_backups = len([r for r in self.backup_records if r.status == "SUCCESS"])
        failed_backups = len([r for r in self.backup_records if r.status == "FAILED"])
        
        # 计算总的备份大小
        total_original_size = sum(r.file_size for r in self.backup_records if r.status == "SUCCESS")
        total_compressed_size = sum(r.compressed_size for r in self.backup_records if r.status == "SUCCESS")
        
        # 平均压缩比
        compression_ratios = [
            r.compression_ratio for r in self.backup_records 
            if r.status == "SUCCESS" and r.compression_ratio > 0
        ]
        avg_compression_ratio = sum(compression_ratios) / len(compression_ratios) if compression_ratios else 0
        
        # 最近7天的备份统计
        now = datetime.now()
        last_7_days = now - timedelta(days=7)
        recent_backups = [r for r in self.backup_records if r.start_time >= last_7_days]
        
        return {
            'report_time': datetime.now().isoformat(),
            'total_backups': total_backups,
            'successful_backups': successful_backups,
            'failed_backups': failed_backups,
            'success_rate': successful_backups / total_backups if total_backups > 0 else 0,
            'total_original_size': total_original_size,
            'total_compressed_size': total_compressed_size,
            'space_saved': total_original_size - total_compressed_size,
            'avg_compression_ratio': avg_compression_ratio,
            'recent_7_days_count': len(recent_backups),
            'active_configs': len([c for c in self.configs.values() if c.enabled]),
            'total_configs': len(self.configs)
        }


# 全局日志备份管理器实例
log_backup_manager = LogBackupManager()