"""
简化版日志分析器
用于测试和调试
"""
import json
import re
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum
from pathlib import Path
from collections import defaultdict

from .logger import trading_logger


class AnomalyType(Enum):
    """异常类型"""
    HIGH_ERROR_RATE = "high_error_rate"
    UNUSUAL_PATTERN = "unusual_pattern"
    PERFORMANCE_DEGRADATION = "performance_degradation"
    SECURITY_THREAT = "security_threat"
    DATA_QUALITY_ISSUE = "data_quality_issue"
    SYSTEM_INSTABILITY = "system_instability"
    BUSINESS_ANOMALY = "business_anomaly"


class AnomalyLevel(Enum):
    """异常级别"""
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    CRITICAL = "CRITICAL"


@dataclass
class LogAnomaly:
    """日志异常"""
    anomaly_id: str
    anomaly_type: AnomalyType
    level: AnomalyLevel
    detected_time: datetime
    description: str
    details: Dict[str, Any]
    affected_components: List[str]
    log_entries: List[Dict]
    confidence: float
    resolved: bool = False


class LogAnalyzer:
    """简化版日志分析器"""
    
    def __init__(self):
        """初始化日志分析器"""
        self.logger = trading_logger.get_logger("LOG_ANALYZER")
        self.anomalies: List[LogAnomaly] = []
    
    def analyze_directory(self, log_dir: str, file_pattern: str = "*.log") -> List[LogAnomaly]:
        """分析日志目录"""
        anomalies = []
        log_dir_path = Path(log_dir)
        
        if not log_dir_path.exists():
            self.logger.warning(f"日志目录不存在: {log_dir}")
            return anomalies
        
        # 查找匹配的日志文件
        log_files = list(log_dir_path.glob(file_pattern))
        
        for log_file in log_files:
            self.logger.info(f"分析日志文件: {log_file}")
            file_anomalies = self._analyze_file(str(log_file))
            anomalies.extend(file_anomalies)
        
        self.anomalies.extend(anomalies)
        return anomalies
    
    def _analyze_file(self, log_file_path: str) -> List[LogAnomaly]:
        """分析单个日志文件"""
        anomalies = []
        
        try:
            with open(log_file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            error_count = 0
            total_count = len(lines)
            
            for line in lines:
                if 'ERROR' in line or 'CRITICAL' in line:
                    error_count += 1
            
            # 简单的错误率检测
            if total_count > 0:
                error_rate = error_count / total_count
                if error_rate > 0.1:  # 10%错误率阈值
                    anomaly = LogAnomaly(
                        anomaly_id=f"ERROR_RATE_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                        anomaly_type=AnomalyType.HIGH_ERROR_RATE,
                        level=AnomalyLevel.HIGH if error_rate > 0.2 else AnomalyLevel.MEDIUM,
                        detected_time=datetime.now(),
                        description=f"高错误率: {error_rate:.2%}",
                        details={
                            'error_count': error_count,
                            'total_count': total_count,
                            'error_rate': error_rate,
                            'file_path': log_file_path
                        },
                        affected_components=[],
                        log_entries=[],
                        confidence=min(1.0, error_rate / 0.1)
                    )
                    anomalies.append(anomaly)
                    
        except Exception as e:
            self.logger.error(f"分析日志文件失败 {log_file_path}: {str(e)}")
        
        return anomalies
    
    def get_anomalies(
        self,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        anomaly_type: Optional[AnomalyType] = None,
        level: Optional[AnomalyLevel] = None
    ) -> List[LogAnomaly]:
        """获取异常记录"""
        filtered_anomalies = self.anomalies
        
        if start_time:
            filtered_anomalies = [a for a in filtered_anomalies if a.detected_time >= start_time]
        
        if end_time:
            filtered_anomalies = [a for a in filtered_anomalies if a.detected_time <= end_time]
        
        if anomaly_type:
            filtered_anomalies = [a for a in filtered_anomalies if a.anomaly_type == anomaly_type]
        
        if level:
            filtered_anomalies = [a for a in filtered_anomalies if a.level == level]
        
        return filtered_anomalies
    
    def generate_analysis_report(self) -> Dict[str, Any]:
        """生成分析报告"""
        total_anomalies = len(self.anomalies)
        unresolved_anomalies = len([a for a in self.anomalies if not a.resolved])
        
        anomaly_by_type = {}
        for anomaly_type in AnomalyType:
            anomaly_by_type[anomaly_type.value] = len([
                a for a in self.anomalies if a.anomaly_type == anomaly_type
            ])
        
        anomaly_by_level = {}
        for level in AnomalyLevel:
            anomaly_by_level[level.value] = len([
                a for a in self.anomalies if a.level == level
            ])
        
        return {
            'report_time': datetime.now().isoformat(),
            'total_anomalies': total_anomalies,
            'unresolved_anomalies': unresolved_anomalies,
            'anomaly_by_type': anomaly_by_type,
            'anomaly_by_level': anomaly_by_level,
            'active_patterns': 0
        }


# 全局日志分析器实例
log_analyzer = LogAnalyzer()