"""
日志系统
"""
import os
import sys
from pathlib import Path
from typing import Optional
from loguru import logger
from datetime import datetime

from config.settings import system_config


class TradingLogger:
    """交易系统日志器"""
    
    def __init__(self):
        """初始化日志器"""
        self._setup_logger()
    
    def _setup_logger(self):
        """设置日志器"""
        # 移除默认处理器
        logger.remove()
        
        # 创建日志目录
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # 控制台输出格式
        console_format = (
            "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
            "<level>{level: <8}</level> | "
            "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
            "<level>{message}</level>"
        )
        
        # 文件输出格式
        file_format = (
            "{time:YYYY-MM-DD HH:mm:ss} | "
            "{level: <8} | "
            "{name}:{function}:{line} | "
            "{message}"
        )
        
        # 添加控制台处理器
        logger.add(
            sys.stdout,
            format=console_format,
            level=system_config.LOG_LEVEL,
            colorize=True
        )
        
        # 添加主日志文件处理器
        logger.add(
            log_dir / "trading_system.log",
            format=file_format,
            level=system_config.LOG_LEVEL,
            rotation="100 MB",
            retention="30 days",
            compression="zip",
            encoding="utf-8"
        )
        
        # 添加错误日志文件处理器
        logger.add(
            log_dir / "error.log",
            format=file_format,
            level="ERROR",
            rotation="50 MB",
            retention="90 days",
            compression="zip",
            encoding="utf-8"
        )
        
        # 添加交易日志文件处理器
        logger.add(
            log_dir / "trading.log",
            format=file_format,
            level="INFO",
            rotation="daily",
            retention="365 days",
            compression="zip",
            encoding="utf-8",
            filter=lambda record: "TRADING" in record["extra"]
        )
        
        # 添加风险日志文件处理器
        logger.add(
            log_dir / "risk.log",
            format=file_format,
            level="WARNING",
            rotation="daily", 
            retention="365 days",
            compression="zip",
            encoding="utf-8",
            filter=lambda record: "RISK" in record["extra"]
        )
    
    def get_logger(self, name: str):
        """获取指定名称的日志器"""
        return logger.bind(name=name)
    
    def get_trading_logger(self):
        """获取交易日志器"""
        return logger.bind(TRADING=True)
    
    def get_risk_logger(self):
        """获取风险日志器"""
        return logger.bind(RISK=True)


class LoggerMixin:
    """日志器混入类"""
    
    @property
    def logger(self):
        """获取日志器"""
        if not hasattr(self, '_logger'):
            self._logger = trading_logger.get_logger(self.__class__.__name__)
        return self._logger
    
    @property
    def trading_logger(self):
        """获取交易日志器"""
        if not hasattr(self, '_trading_logger'):
            self._trading_logger = trading_logger.get_trading_logger()
        return self._trading_logger
    
    @property
    def risk_logger(self):
        """获取风险日志器"""
        if not hasattr(self, '_risk_logger'):
            self._risk_logger = trading_logger.get_risk_logger()
        return self._risk_logger


# 全局日志器实例
trading_logger = TradingLogger()

# 导出常用日志器
system_logger = trading_logger.get_logger("SYSTEM")
data_logger = trading_logger.get_logger("DATA")
model_logger = trading_logger.get_logger("MODEL")
trading_execution_logger = trading_logger.get_trading_logger()
risk_monitor_logger = trading_logger.get_risk_logger()


def get_logger(name: str):
    """获取指定名称的日志器（兼容函数）"""
    return trading_logger.get_logger(name)