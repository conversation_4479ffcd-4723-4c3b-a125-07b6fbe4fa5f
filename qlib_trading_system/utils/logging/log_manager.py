"""
综合日志管理系统
整合审计日志、合规检查、日志分析和备份功能
"""
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from pathlib import Path
import threading
import asyncio

from .audit_logger import audit_logger, AuditEvent, AuditEventType, AuditLevel
from .compliance_checker import compliance_checker, ComplianceViolation
from .log_analyzer_simple import log_analyzer, LogAnomaly, AnomalyType, AnomalyLevel
from .log_backup import log_backup_manager, BackupRecord
from .logger import trading_logger


class LogManager:
    """综合日志管理器"""
    
    def __init__(self):
        """初始化日志管理器"""
        self.logger = trading_logger.get_logger("LOG_MANAGER")
        self._monitoring_active = False
        self._monitoring_thread = None
        
        # 初始化各个组件
        self._initialize_components()
        
        # 启动监控
        self.start_monitoring()
    
    def _initialize_components(self):
        """初始化各个组件"""
        try:
            # 启动备份调度器
            log_backup_manager.start_scheduler()
            
            self.logger.info("日志管理系统初始化完成")
        except Exception as e:
            self.logger.error(f"日志管理系统初始化失败: {str(e)}")
    
    def log_trading_event(
        self,
        event_type: str,
        details: Dict[str, Any],
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        level: AuditLevel = AuditLevel.INFO
    ):
        """记录交易事件"""
        try:
            # 记录审计日志
            audit_logger.log_trading_action(
                action=event_type,
                details=details,
                user_id=user_id,
                session_id=session_id,
                result="SUCCESS"
            )
            
            # 检查合规性
            event = AuditEvent(
                event_id=f"TRADING_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                timestamp=datetime.now(),
                event_type=AuditEventType.ORDER_SUBMIT,  # 根据event_type动态确定
                level=level,
                user_id=user_id,
                session_id=session_id,
                component="TRADING",
                action=event_type,
                details=details,
                before_state=None,
                after_state=None,
                ip_address=None,
                result="SUCCESS",
                error_message=None
            )
            
            violations = compliance_checker.check_compliance(event)
            if violations:
                self.logger.warning(f"检测到合规违规: {len(violations)}个")
                
        except Exception as e:
            self.logger.error(f"记录交易事件失败: {str(e)}")
    
    def log_risk_event(
        self,
        risk_type: str,
        details: Dict[str, Any],
        level: AuditLevel = AuditLevel.WARNING
    ):
        """记录风险事件"""
        try:
            # 记录审计日志
            audit_logger.log_risk_event(
                risk_type=risk_type,
                details=details,
                level=level
            )
            
            # 如果是严重风险事件，立即分析
            if level in [AuditLevel.ERROR, AuditLevel.CRITICAL]:
                self._analyze_critical_event(risk_type, details)
                
        except Exception as e:
            self.logger.error(f"记录风险事件失败: {str(e)}")
    
    def log_system_event(
        self,
        action: str,
        details: Dict[str, Any],
        component: str = "SYSTEM",
        level: AuditLevel = AuditLevel.INFO
    ):
        """记录系统事件"""
        try:
            # 记录审计日志
            audit_logger.log_system_event(
                action=action,
                details=details,
                component=component,
                level=level
            )
            
        except Exception as e:
            self.logger.error(f"记录系统事件失败: {str(e)}")
    
    def log_user_action(
        self,
        action: str,
        user_id: str,
        session_id: str,
        details: Dict[str, Any],
        ip_address: Optional[str] = None
    ):
        """记录用户操作"""
        try:
            # 记录审计日志
            audit_logger.log_user_action(
                action=action,
                user_id=user_id,
                session_id=session_id,
                details=details,
                ip_address=ip_address
            )
            
            # 检查用户行为合规性
            event = AuditEvent(
                event_id=f"USER_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                timestamp=datetime.now(),
                event_type=AuditEventType.USER_LOGIN,  # 根据action动态确定
                level=AuditLevel.INFO,
                user_id=user_id,
                session_id=session_id,
                component="USER",
                action=action,
                details=details,
                before_state=None,
                after_state=None,
                ip_address=ip_address,
                result="SUCCESS",
                error_message=None
            )
            
            violations = compliance_checker.check_compliance(event)
            if violations:
                self.logger.warning(f"用户操作违规: {user_id}, 违规数: {len(violations)}")
                
        except Exception as e:
            self.logger.error(f"记录用户操作失败: {str(e)}")
    
    def analyze_logs(
        self,
        log_directory: str = "logs",
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """分析日志"""
        try:
            # 分析日志异常
            anomalies = log_analyzer.analyze_directory(log_directory)
            
            # 获取合规违规
            violations = compliance_checker.get_violations(start_time, end_time)
            
            # 生成分析报告
            analysis_report = {
                'analysis_time': datetime.now().isoformat(),
                'log_directory': log_directory,
                'time_range': {
                    'start_time': start_time.isoformat() if start_time else None,
                    'end_time': end_time.isoformat() if end_time else None
                },
                'anomalies': {
                    'total_count': len(anomalies),
                    'by_type': self._group_anomalies_by_type(anomalies),
                    'by_level': self._group_anomalies_by_level(anomalies),
                    'critical_anomalies': [
                        a.anomaly_id for a in anomalies 
                        if a.level == AnomalyLevel.CRITICAL
                    ]
                },
                'violations': {
                    'total_count': len(violations),
                    'by_level': self._group_violations_by_level(violations),
                    'unresolved_count': len([v for v in violations if not v.resolved])
                }
            }
            
            self.logger.info(f"日志分析完成: 异常{len(anomalies)}个, 违规{len(violations)}个")
            return analysis_report
            
        except Exception as e:
            self.logger.error(f"日志分析失败: {str(e)}")
            return {}
    
    def backup_logs(self, config_id: Optional[str] = None) -> List[BackupRecord]:
        """备份日志"""
        try:
            backup_records = []
            
            if config_id:
                # 备份指定配置
                record = log_backup_manager.backup_logs(config_id)
                backup_records.append(record)
            else:
                # 备份所有启用的配置
                for config_id in log_backup_manager.configs.keys():
                    config = log_backup_manager.configs[config_id]
                    if config.enabled:
                        try:
                            record = log_backup_manager.backup_logs(config_id)
                            backup_records.append(record)
                        except Exception as e:
                            self.logger.error(f"备份配置失败 {config_id}: {str(e)}")
            
            self.logger.info(f"日志备份完成: {len(backup_records)}个备份")
            return backup_records
            
        except Exception as e:
            self.logger.error(f"日志备份失败: {str(e)}")
            return []
    
    def get_system_health(self) -> Dict[str, Any]:
        """获取系统健康状态"""
        try:
            now = datetime.now()
            last_24h = now - timedelta(hours=24)
            
            # 获取最近24小时的异常
            recent_anomalies = log_analyzer.get_anomalies(start_time=last_24h)
            critical_anomalies = [a for a in recent_anomalies if a.level == AnomalyLevel.CRITICAL]
            
            # 获取最近24小时的违规
            recent_violations = compliance_checker.get_violations(start_time=last_24h)
            critical_violations = [v for v in recent_violations if v.violation_level.value == "CRITICAL"]
            
            # 获取备份状态
            recent_backups = log_backup_manager.get_backup_records(start_time=last_24h)
            failed_backups = [b for b in recent_backups if b.status == "FAILED"]
            
            # 计算健康分数
            health_score = self._calculate_health_score(
                critical_anomalies, critical_violations, failed_backups
            )
            
            return {
                'timestamp': now.isoformat(),
                'health_score': health_score,
                'status': self._get_health_status(health_score),
                'recent_24h': {
                    'anomalies': {
                        'total': len(recent_anomalies),
                        'critical': len(critical_anomalies)
                    },
                    'violations': {
                        'total': len(recent_violations),
                        'critical': len(critical_violations)
                    },
                    'backups': {
                        'total': len(recent_backups),
                        'failed': len(failed_backups)
                    }
                },
                'recommendations': self._generate_health_recommendations(
                    critical_anomalies, critical_violations, failed_backups
                )
            }
            
        except Exception as e:
            self.logger.error(f"获取系统健康状态失败: {str(e)}")
            return {
                'timestamp': datetime.now().isoformat(),
                'health_score': 0,
                'status': 'ERROR',
                'error': str(e)
            }
    
    def generate_comprehensive_report(
        self,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """生成综合报告"""
        try:
            if not start_time:
                start_time = datetime.now() - timedelta(days=7)
            if not end_time:
                end_time = datetime.now()
            
            # 获取各种报告
            analysis_report = log_analyzer.generate_analysis_report()
            compliance_report = compliance_checker.get_compliance_report()
            backup_report = log_backup_manager.generate_backup_report()
            
            # 获取统计数据
            anomalies = log_analyzer.get_anomalies(start_time, end_time)
            violations = compliance_checker.get_violations(start_time, end_time)
            backups = log_backup_manager.get_backup_records(start_time, end_time)
            
            return {
                'report_time': datetime.now().isoformat(),
                'time_range': {
                    'start_time': start_time.isoformat(),
                    'end_time': end_time.isoformat()
                },
                'summary': {
                    'total_anomalies': len(anomalies),
                    'total_violations': len(violations),
                    'total_backups': len(backups),
                    'health_score': self.get_system_health()['health_score']
                },
                'analysis_report': analysis_report,
                'compliance_report': compliance_report,
                'backup_report': backup_report,
                'detailed_stats': {
                    'anomalies_by_type': self._group_anomalies_by_type(anomalies),
                    'violations_by_level': self._group_violations_by_level(violations),
                    'backup_success_rate': len([b for b in backups if b.status == "SUCCESS"]) / len(backups) if backups else 0
                }
            }
            
        except Exception as e:
            self.logger.error(f"生成综合报告失败: {str(e)}")
            return {'error': str(e)}
    
    def start_monitoring(self):
        """启动监控"""
        if self._monitoring_active:
            return
        
        self._monitoring_active = True
        self._monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self._monitoring_thread.start()
        
        self.logger.info("日志监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self._monitoring_active = False
        
        if self._monitoring_thread and self._monitoring_thread.is_alive():
            self._monitoring_thread.join(timeout=5)
        
        # 停止备份调度器
        log_backup_manager.stop_scheduler()
        
        self.logger.info("日志监控已停止")
    
    def _monitoring_loop(self):
        """监控循环"""
        import time
        
        while self._monitoring_active:
            try:
                # 每5分钟执行一次监控检查
                time.sleep(300)
                
                if not self._monitoring_active:
                    break
                
                # 分析最近的日志
                self._periodic_analysis()
                
            except Exception as e:
                self.logger.error(f"监控循环错误: {str(e)}")
    
    def _periodic_analysis(self):
        """定期分析"""
        try:
            # 分析最近1小时的日志
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=1)
            
            # 分析系统日志
            anomalies = log_analyzer.analyze_directory("logs/system", start_time, end_time)
            
            # 检查是否有严重异常
            critical_anomalies = [a for a in anomalies if a.level == AnomalyLevel.CRITICAL]
            if critical_anomalies:
                self.logger.critical(f"检测到严重异常: {len(critical_anomalies)}个")
                
                # 可以在这里添加告警逻辑
                self._send_alert("CRITICAL_ANOMALY", {
                    'count': len(critical_anomalies),
                    'anomalies': [a.anomaly_id for a in critical_anomalies]
                })
            
        except Exception as e:
            self.logger.error(f"定期分析失败: {str(e)}")
    
    def _analyze_critical_event(self, event_type: str, details: Dict[str, Any]):
        """分析严重事件"""
        try:
            self.logger.critical(f"严重事件: {event_type}, 详情: {details}")
            
            # 可以在这里添加特殊处理逻辑
            # 例如：立即备份相关日志、发送告警等
            
        except Exception as e:
            self.logger.error(f"分析严重事件失败: {str(e)}")
    
    def _send_alert(self, alert_type: str, details: Dict[str, Any]):
        """发送告警"""
        try:
            # 这里可以实现具体的告警逻辑
            # 例如：发送邮件、短信、钉钉消息等
            self.logger.warning(f"告警: {alert_type}, 详情: {details}")
            
        except Exception as e:
            self.logger.error(f"发送告警失败: {str(e)}")
    
    def _group_anomalies_by_type(self, anomalies: List[LogAnomaly]) -> Dict[str, int]:
        """按类型分组异常"""
        groups = {}
        for anomaly in anomalies:
            anomaly_type = anomaly.anomaly_type.value
            groups[anomaly_type] = groups.get(anomaly_type, 0) + 1
        return groups
    
    def _group_anomalies_by_level(self, anomalies: List[LogAnomaly]) -> Dict[str, int]:
        """按级别分组异常"""
        groups = {}
        for anomaly in anomalies:
            level = anomaly.level.value
            groups[level] = groups.get(level, 0) + 1
        return groups
    
    def _group_violations_by_level(self, violations: List[ComplianceViolation]) -> Dict[str, int]:
        """按级别分组违规"""
        groups = {}
        for violation in violations:
            level = violation.violation_level.value
            groups[level] = groups.get(level, 0) + 1
        return groups
    
    def _calculate_health_score(
        self,
        critical_anomalies: List[LogAnomaly],
        critical_violations: List[ComplianceViolation],
        failed_backups: List[BackupRecord]
    ) -> float:
        """计算健康分数"""
        base_score = 100.0
        
        # 严重异常扣分
        base_score -= len(critical_anomalies) * 10
        
        # 严重违规扣分
        base_score -= len(critical_violations) * 15
        
        # 备份失败扣分
        base_score -= len(failed_backups) * 5
        
        return max(0.0, min(100.0, base_score))
    
    def _get_health_status(self, health_score: float) -> str:
        """获取健康状态"""
        if health_score >= 90:
            return "EXCELLENT"
        elif health_score >= 75:
            return "GOOD"
        elif health_score >= 60:
            return "FAIR"
        elif health_score >= 40:
            return "POOR"
        else:
            return "CRITICAL"
    
    def _generate_health_recommendations(
        self,
        critical_anomalies: List[LogAnomaly],
        critical_violations: List[ComplianceViolation],
        failed_backups: List[BackupRecord]
    ) -> List[str]:
        """生成健康建议"""
        recommendations = []
        
        if critical_anomalies:
            recommendations.append(f"立即处理{len(critical_anomalies)}个严重异常")
        
        if critical_violations:
            recommendations.append(f"立即处理{len(critical_violations)}个严重合规违规")
        
        if failed_backups:
            recommendations.append(f"检查并修复{len(failed_backups)}个失败的备份")
        
        if not recommendations:
            recommendations.append("系统运行正常，继续保持")
        
        return recommendations


# 全局日志管理器实例
log_manager = LogManager()