"""
日志分析和异常检测工具
实现日志分析、异常检测和模式识别功能
"""
import json
import re
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
from pathlib import Path
from collections import defaultdict, Counter

# 导入numpy用于统计计算
try:
    import numpy as np
except ImportError:
    # 如果没有numpy，使用简单的统计函数
    class SimpleStats:
        @staticmethod
        def mean(values):
            return sum(values) / len(values) if values else 0
        
        @staticmethod
        def std(values):
            if not values:
                return 0
            mean_val = sum(values) / len(values)
            variance = sum((x - mean_val) ** 2 for x in values) / len(values)
            return variance ** 0.5
    
    np = SimpleStats()

from .logger import trading_logger


class AnomalyType(Enum):
    """异常类型"""
    HIGH_ERROR_RATE = "high_error_rate"         # 高错误率
    UNUSUAL_PATTERN = "unusual_pattern"         # 异常模式
    PERFORMANCE_DEGRADATION = "performance_degradation"  # 性能下降
    SECURITY_THREAT = "security_threat"         # 安全威胁
    DATA_QUALITY_ISSUE = "data_quality_issue"   # 数据质量问题
    SYSTEM_INSTABILITY = "system_instability"  # 系统不稳定
    BUSINESS_ANOMALY = "business_anomaly"       # 业务异常


class AnomalyLevel(Enum):
    """异常级别"""
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    CRITICAL = "CRITICAL"


@dataclass
class LogAnomaly:
    """日志异常"""
    anomaly_id: str                 # 异常ID
    anomaly_type: AnomalyType       # 异常类型
    level: AnomalyLevel             # 异常级别
    detected_time: datetime         # 检测时间
    description: str                # 异常描述
    details: Dict[str, Any]         # 异常详情
    affected_components: List[str]  # 受影响的组件
    log_entries: List[Dict]         # 相关日志条目
    confidence: float               # 置信度
    resolved: bool = False          # 是否已解决


@dataclass
class LogPattern:
    """日志模式"""
    pattern_id: str                 # 模式ID
    pattern_name: str               # 模式名称
    regex_pattern: str              # 正则表达式模式
    description: str                # 模式描述
    severity: str                   # 严重程度
    frequency_threshold: int        # 频率阈值
    time_window: int                # 时间窗口（分钟）


class LogAnalyzer:
    """日志分析器"""
    
    def __init__(self):
        """初始化日志分析器"""
        self.logger = trading_logger.get_logger("LOG_ANALYZER")
        self.anomalies: List[LogAnomaly] = []
        self.patterns: Dict[str, LogPattern] = {}
        self._load_default_patterns()
        
    def _load_default_patterns(self):
        """加载默认日志模式"""
        # 错误模式
        self.add_pattern(LogPattern(
            pattern_id="ERROR_001",
            pattern_name="数据库连接错误",
            regex_pattern=r".*database.*connection.*error.*",
            description="数据库连接失败",
            severity="HIGH",
            frequency_threshold=5,
            time_window=10
        ))
        
        self.add_pattern(LogPattern(
            pattern_id="ERROR_002",
            pattern_name="API调用失败",
            regex_pattern=r".*API.*failed.*|.*request.*timeout.*",
            description="API调用失败或超时",
            severity="MEDIUM",
            frequency_threshold=10,
            time_window=15
        ))
        
        self.add_pattern(LogPattern(
            pattern_id="ERROR_003",
            pattern_name="内存不足",
            regex_pattern=r".*out of memory.*|.*memory.*exceeded.*",
            description="内存不足错误",
            severity="CRITICAL",
            frequency_threshold=1,
            time_window=5
        ))
        
        # 安全模式
        self.add_pattern(LogPattern(
            pattern_id="SECURITY_001",
            pattern_name="异常登录尝试",
            regex_pattern=r".*login.*failed.*|.*authentication.*failed.*",
            description="异常登录尝试",
            severity="HIGH",
            frequency_threshold=5,
            time_window=10
        ))
        
        self.add_pattern(LogPattern(
            pattern_id="SECURITY_002",
            pattern_name="权限违规",
            regex_pattern=r".*permission.*denied.*|.*access.*denied.*",
            description="权限违规访问",
            severity="MEDIUM",
            frequency_threshold=3,
            time_window=5
        ))
        
        # 性能模式
        self.add_pattern(LogPattern(
            pattern_id="PERFORMANCE_001",
            pattern_name="响应时间过长",
            regex_pattern=r".*response.*time.*exceeded.*|.*slow.*query.*",
            description="响应时间过长",
            severity="MEDIUM",
            frequency_threshold=20,
            time_window=30
        ))
        
        self.add_pattern(LogPattern(
            pattern_id="PERFORMANCE_002",
            pattern_name="CPU使用率过高",
            regex_pattern=r".*CPU.*usage.*high.*|.*processor.*overload.*",
            description="CPU使用率过高",
            severity="HIGH",
            frequency_threshold=10,
            time_window=15
        ))
        
        # 业务模式
        self.add_pattern(LogPattern(
            pattern_id="BUSINESS_001",
            pattern_name="交易异常",
            regex_pattern=r".*trade.*error.*|.*order.*failed.*",
            description="交易执行异常",
            severity="HIGH",
            frequency_threshold=3,
            time_window=5
        ))
        
        self.add_pattern(LogPattern(
            pattern_id="BUSINESS_002",
            pattern_name="数据质量问题",
            regex_pattern=r".*data.*quality.*|.*invalid.*data.*",
            description="数据质量问题",
            severity="MEDIUM",
            frequency_threshold=5,
            time_window=10
        ))
    
    def add_pattern(self, pattern: LogPattern):
        """添加日志模式"""
        self.patterns[pattern.pattern_id] = pattern
        self.logger.info(f"添加日志模式: {pattern.pattern_name} ({pattern.pattern_id})")
    
    def remove_pattern(self, pattern_id: str):
        """移除日志模式"""
        if pattern_id in self.patterns:
            pattern_name = self.patterns[pattern_id].pattern_name
            del self.patterns[pattern_id]
            self.logger.info(f"移除日志模式: {pattern_name} ({pattern_id})")
    
    def analyze_log_file(self, log_file_path: str, start_time: Optional[datetime] = None, 
                        end_time: Optional[datetime] = None) -> List[LogAnomaly]:
        """分析日志文件"""
        try:
            log_entries = self._read_log_file(log_file_path, start_time, end_time)
            anomalies = []
            
            # 模式匹配检测
            pattern_anomalies = self._detect_pattern_anomalies(log_entries)
            anomalies.extend(pattern_anomalies)
            
            # 统计异常检测
            statistical_anomalies = self._detect_statistical_anomalies(log_entries)
            anomalies.extend(statistical_anomalies)
            
            # 时序异常检测
            temporal_anomalies = self._detect_temporal_anomalies(log_entries)
            anomalies.extend(temporal_anomalies)
            
            # 保存检测到的异常
            self.anomalies.extend(anomalies)
            
            return anomalies
            
        except Exception as e:
            self.logger.error(f"分析日志文件失败 {log_file_path}: {str(e)}")
            return []
    
    def _read_log_file(self, log_file_path: str, start_time: Optional[datetime] = None,
                      end_time: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """读取日志文件"""
        log_entries = []
        
        try:
            with open(log_file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    try:
                        # 尝试解析JSON格式的日志
                        if line.strip().startswith('{'):
                            entry = json.loads(line.strip())
                        else:
                            # 解析普通格式的日志
                            entry = self._parse_log_line(line.strip(), line_num)
                        
                        # 时间过滤
                        if start_time or end_time:
                            entry_time = self._extract_timestamp(entry)
                            if entry_time:
                                if start_time and entry_time < start_time:
                                    continue
                                if end_time and entry_time > end_time:
                                    continue
                        
                        log_entries.append(entry)
                        
                    except json.JSONDecodeError:
                        # 处理非JSON格式的日志行
                        entry = self._parse_log_line(line.strip(), line_num)
                        log_entries.append(entry)
                        
        except Exception as e:
            self.logger.error(f"读取日志文件失败 {log_file_path}: {str(e)}")
        
        return log_entries
    
    def _parse_log_line(self, line: str, line_num: int) -> Dict[str, Any]:
        """解析日志行"""
        # 通用日志格式解析: TIMESTAMP | LEVEL | MESSAGE
        pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}[.\d]*)\s*\|\s*(\w+)\s*\|\s*(.*)'
        match = re.match(pattern, line)
        
        if match:
            timestamp_str, level, message = match.groups()
            try:
                timestamp = datetime.fromisoformat(timestamp_str.replace(' ', 'T'))
            except:
                timestamp = datetime.now()
            
            return {
                'timestamp': timestamp,
                'level': level,
                'message': message,
                'line_number': line_num,
                'raw_line': line
            }
        else:
            return {
                'timestamp': datetime.now(),
                'level': 'UNKNOWN',
                'message': line,
                'line_number': line_num,
                'raw_line': line
            }
    
    def _extract_timestamp(self, entry: Dict[str, Any]) -> Optional[datetime]:
        """提取时间戳"""
        if 'timestamp' in entry:
            if isinstance(entry['timestamp'], datetime):
                return entry['timestamp']
            elif isinstance(entry['timestamp'], str):
                try:
                    return datetime.fromisoformat(entry['timestamp'])
                except:
                    pass
        return None
    
    def _detect_pattern_anomalies(self, log_entries: List[Dict[str, Any]]) -> List[LogAnomaly]:
        """检测模式异常"""
        anomalies = []
        
        for pattern in self.patterns.values():
            matches = []
            
            # 查找匹配的日志条目
            for entry in log_entries:
                message = entry.get('message', '')
                if re.search(pattern.regex_pattern, message, re.IGNORECASE):
                    matches.append(entry)
            
            # 检查是否超过阈值
            if len(matches) >= pattern.frequency_threshold:
                # 检查时间窗口
                if self._check_time_window(matches, pattern.time_window):
                    anomaly = LogAnomaly(
                        anomaly_id=f"PATTERN_{pattern.pattern_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                        anomaly_type=AnomalyType.UNUSUAL_PATTERN,
                        level=self._severity_to_level(pattern.severity),
                        detected_time=datetime.now(),
                        description=f"检测到异常模式: {pattern.pattern_name}",
                        details={
                            'pattern_id': pattern.pattern_id,
                            'pattern_name': pattern.pattern_name,
                            'match_count': len(matches),
                            'threshold': pattern.frequency_threshold,
                            'time_window': pattern.time_window
                        },
                        affected_components=self._extract_components(matches),
                        log_entries=matches[:10],  # 只保留前10条
                        confidence=min(1.0, len(matches) / pattern.frequency_threshold)
                    )
                    anomalies.append(anomaly)
        
        return anomalies
    
    def _detect_statistical_anomalies(self, log_entries: List[Dict[str, Any]]) -> List[LogAnomaly]:
        """检测统计异常"""
        anomalies = []
        
        # 错误率异常检测
        error_rate_anomaly = self._detect_error_rate_anomaly(log_entries)
        if error_rate_anomaly:
            anomalies.append(error_rate_anomaly)
        
        # 日志量异常检测
        volume_anomaly = self._detect_volume_anomaly(log_entries)
        if volume_anomaly:
            anomalies.append(volume_anomaly)
        
        return anomalies
    
    def _detect_error_rate_anomaly(self, log_entries: List[Dict[str, Any]]) -> Optional[LogAnomaly]:
        """检测错误率异常"""
        if not log_entries:
            return None
        
        error_levels = ['ERROR', 'CRITICAL', 'FATAL']
        error_count = sum(1 for entry in log_entries if entry.get('level') in error_levels)
        total_count = len(log_entries)
        error_rate = error_count / total_count if total_count > 0 else 0
        
        # 错误率阈值
        normal_error_rate = 0.05  # 5%
        high_error_rate = 0.15    # 15%
        critical_error_rate = 0.30  # 30%
        
        if error_rate > critical_error_rate:
            level = AnomalyLevel.CRITICAL
        elif error_rate > high_error_rate:
            level = AnomalyLevel.HIGH
        elif error_rate > normal_error_rate:
            level = AnomalyLevel.MEDIUM
        else:
            return None
        
        return LogAnomaly(
            anomaly_id=f"ERROR_RATE_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            anomaly_type=AnomalyType.HIGH_ERROR_RATE,
            level=level,
            detected_time=datetime.now(),
            description=f"错误率异常: {error_rate:.2%}",
            details={
                'error_count': error_count,
                'total_count': total_count,
                'error_rate': error_rate,
                'threshold': normal_error_rate
            },
            affected_components=[],
            log_entries=[entry for entry in log_entries if entry.get('level') in error_levels][:10],
            confidence=min(1.0, error_rate / normal_error_rate)
        )
    
    def _detect_volume_anomaly(self, log_entries: List[Dict[str, Any]]) -> Optional[LogAnomaly]:
        """检测日志量异常"""
        if not log_entries:
            return None
        
        # 按小时统计日志量
        hourly_counts = defaultdict(int)
        for entry in log_entries:
            timestamp = self._extract_timestamp(entry)
            if timestamp:
                hour_key = timestamp.strftime('%Y-%m-%d %H')
                hourly_counts[hour_key] += 1
        
        if len(hourly_counts) < 2:
            return None
        
        counts = list(hourly_counts.values())
        mean_count = np.mean(counts)
        std_count = np.std(counts)
        
        # 检测异常高的日志量
        for hour, count in hourly_counts.items():
            if std_count > 0 and count > mean_count + 3 * std_count:
                return LogAnomaly(
                    anomaly_id=f"VOLUME_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    anomaly_type=AnomalyType.SYSTEM_INSTABILITY,
                    level=AnomalyLevel.MEDIUM,
                    detected_time=datetime.now(),
                    description=f"日志量异常: {hour}时段日志量{count}条，超过正常水平",
                    details={
                        'hour': hour,
                        'count': count,
                        'mean_count': mean_count,
                        'std_count': std_count,
                        'threshold': mean_count + 3 * std_count
                    },
                    affected_components=[],
                    log_entries=[],
                    confidence=min(1.0, (count - mean_count) / (3 * std_count) if std_count > 0 else 1.0)
                )
        
        return None
    
    def _detect_temporal_anomalies(self, log_entries: List[Dict[str, Any]]) -> List[LogAnomaly]:
        """检测时序异常"""
        anomalies = []
        
        # 检测日志时间间隔异常
        timestamps = []
        for entry in log_entries:
            timestamp = self._extract_timestamp(entry)
            if timestamp:
                timestamps.append(timestamp)
        
        if len(timestamps) < 10:
            return anomalies
        
        timestamps.sort()
        intervals = []
        for i in range(1, len(timestamps)):
            interval = (timestamps[i] - timestamps[i-1]).total_seconds()
            intervals.append(interval)
        
        if intervals:
            mean_interval = np.mean(intervals)
            std_interval = np.std(intervals)
            
            # 检测异常长的间隔
            for i, interval in enumerate(intervals):
                if std_interval > 0 and interval > mean_interval + 5 * std_interval:
                    anomaly = LogAnomaly(
                        anomaly_id=f"TEMPORAL_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{i}",
                        anomaly_type=AnomalyType.SYSTEM_INSTABILITY,
                        level=AnomalyLevel.MEDIUM,
                        detected_time=datetime.now(),
                        description=f"日志时间间隔异常: {interval:.2f}秒",
                        details={
                            'interval': interval,
                            'mean_interval': mean_interval,
                            'std_interval': std_interval,
                            'threshold': mean_interval + 5 * std_interval
                        },
                        affected_components=[],
                        log_entries=[],
                        confidence=min(1.0, (interval - mean_interval) / (5 * std_interval) if std_interval > 0 else 1.0)
                    )
                    anomalies.append(anomaly)
        
        return anomalies
    
    def _check_time_window(self, matches: List[Dict[str, Any]], window_minutes: int) -> bool:
        """检查时间窗口内的匹配"""
        if not matches:
            return False
        
        timestamps = []
        for match in matches:
            timestamp = self._extract_timestamp(match)
            if timestamp:
                timestamps.append(timestamp)
        
        if len(timestamps) < 2:
            return True
        
        timestamps.sort()
        time_span = (timestamps[-1] - timestamps[0]).total_seconds() / 60
        return time_span <= window_minutes
    
    def _severity_to_level(self, severity: str) -> AnomalyLevel:
        """严重程度转换为异常级别"""
        severity_map = {
            'LOW': AnomalyLevel.LOW,
            'MEDIUM': AnomalyLevel.MEDIUM,
            'HIGH': AnomalyLevel.HIGH,
            'CRITICAL': AnomalyLevel.CRITICAL
        }
        return severity_map.get(severity, AnomalyLevel.MEDIUM)
    
    def _extract_components(self, log_entries: List[Dict[str, Any]]) -> List[str]:
        """提取受影响的组件"""
        components = set()
        for entry in log_entries:
            message = entry.get('message', '')
            # 简单的组件提取逻辑
            if 'database' in message.lower():
                components.add('DATABASE')
            elif 'api' in message.lower():
                components.add('API')
            elif 'trading' in message.lower():
                components.add('TRADING')
            elif 'risk' in message.lower():
                components.add('RISK')
        return list(components)
    
    def analyze_directory(self, log_dir: str, file_pattern: str = "*.log") -> List[LogAnomaly]:
        """分析日志目录"""
        all_anomalies = []
        log_dir_path = Path(log_dir)
        
        if not log_dir_path.exists():
            self.logger.warning(f"日志目录不存在: {log_dir}")
            return all_anomalies
        
        # 查找匹配的日志文件
        log_files = list(log_dir_path.glob(file_pattern))
        
        for log_file in log_files:
            self.logger.info(f"分析日志文件: {log_file}")
            anomalies = self.analyze_log_file(str(log_file))
            all_anomalies.extend(anomalies)
        
        return all_anomalies
    
    def get_anomalies(
        self,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        anomaly_type: Optional[AnomalyType] = None,
        level: Optional[AnomalyLevel] = None
    ) -> List[LogAnomaly]:
        """获取异常记录"""
        filtered_anomalies = self.anomalies
        
        if start_time:
            filtered_anomalies = [a for a in filtered_anomalies if a.detected_time >= start_time]
        
        if end_time:
            filtered_anomalies = [a for a in filtered_anomalies if a.detected_time <= end_time]
        
        if anomaly_type:
            filtered_anomalies = [a for a in filtered_anomalies if a.anomaly_type == anomaly_type]
        
        if level:
            filtered_anomalies = [a for a in filtered_anomalies if a.level == level]
        
        return filtered_anomalies
    
    def resolve_anomaly(self, anomaly_id: str):
        """解决异常"""
        for anomaly in self.anomalies:
            if anomaly.anomaly_id == anomaly_id:
                anomaly.resolved = True
                self.logger.info(f"异常已解决: {anomaly_id}")
                break
    
    def generate_analysis_report(self) -> Dict[str, Any]:
        """生成分析报告"""
        total_anomalies = len(self.anomalies)
        unresolved_anomalies = len([a for a in self.anomalies if not a.resolved])
        
        anomaly_by_type = {}
        for anomaly_type in AnomalyType:
            anomaly_by_type[anomaly_type.value] = len([
                a for a in self.anomalies if a.anomaly_type == anomaly_type
            ])
        
        anomaly_by_level = {}
        for level in AnomalyLevel:
            anomaly_by_level[level.value] = len([
                a for a in self.anomalies if a.level == level
            ])
        
        # 最近24小时的异常趋势
        now = datetime.now()
        last_24h = now - timedelta(hours=24)
        recent_anomalies = [a for a in self.anomalies if a.detected_time >= last_24h]
        
        hourly_trend = defaultdict(int)
        for anomaly in recent_anomalies:
            hour_key = anomaly.detected_time.strftime('%Y-%m-%d %H')
            hourly_trend[hour_key] += 1
        
        return {
            'report_time': datetime.now().isoformat(),
            'total_anomalies': total_anomalies,
            'unresolved_anomalies': unresolved_anomalies,
            'anomaly_by_type': anomaly_by_type,
            'anomaly_by_level': anomaly_by_level,
            'recent_24h_count': len(recent_anomalies),
            'hourly_trend': dict(hourly_trend),
            'active_patterns': len(self.patterns)
        }


# 全局日志分析器实例
log_analyzer = LogAnalyzer()