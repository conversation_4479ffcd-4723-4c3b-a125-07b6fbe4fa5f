# 日志和审计系统

## 系统概述

本系统实现了完整的日志和审计功能，包括全链路交易日志记录、操作审计、合规检查、日志分析和备份功能。系统采用模块化设计，各组件独立运行，可以灵活配置和扩展。

## 核心组件

### 1. 审计日志系统 (audit_logger.py)

**功能特性：**
- 全链路交易日志记录
- 多种事件类型支持（交易、风险、系统、用户操作）
- 结构化日志格式（JSON）
- 自动日志轮转和压缩
- 长期保存（监管要求7年）

**主要类：**
- `AuditEvent`: 审计事件数据结构
- `AuditLogger`: 审计日志记录器
- `AuditEventType`: 事件类型枚举
- `AuditLevel`: 审计级别枚举

**使用示例：**
```python
from qlib_trading_system.utils.logging.audit_logger import audit_logger, AuditLevel

# 记录交易操作
audit_logger.log_trading_action(
    action="ORDER_SUBMIT",
    details={
        "symbol": "000001.SZ",
        "side": "BUY",
        "quantity": 1000,
        "price": 15.50
    },
    user_id="user_001"
)
```

### 2. 合规检查机制 (compliance_checker.py)

**功能特性：**
- 实时合规检查
- 多种合规规则支持
- 违规记录和追踪
- 自动告警和处理
- 规则动态配置

**内置规则：**
- 交易频率限制
- 单笔交易金额限制
- 交易时间限制
- 持仓集中度限制
- 日内最大亏损限制
- 异常交易模式检测

**主要类：**
- `ComplianceRule`: 合规规则定义
- `ComplianceViolation`: 违规记录
- `ComplianceChecker`: 合规检查器

**使用示例：**
```python
from qlib_trading_system.utils.logging.compliance_checker import compliance_checker

# 检查合规性
violations = compliance_checker.check_compliance(audit_event)
if violations:
    print(f"检测到违规: {len(violations)}个")
```

### 3. 日志分析工具 (log_analyzer_simple.py)

**功能特性：**
- 自动异常检测
- 模式识别
- 统计分析
- 时序分析
- 异常分类和级别评估

**检测类型：**
- 高错误率异常
- 异常模式识别
- 性能下降检测
- 安全威胁识别
- 数据质量问题
- 系统不稳定性

**主要类：**
- `LogAnomaly`: 日志异常记录
- `LogAnalyzer`: 日志分析器
- `AnomalyType`: 异常类型枚举
- `AnomalyLevel`: 异常级别枚举

**使用示例：**
```python
from qlib_trading_system.utils.logging.log_analyzer_simple import log_analyzer

# 分析日志目录
anomalies = log_analyzer.analyze_directory("logs/system")
print(f"检测到异常: {len(anomalies)}个")
```

### 4. 日志备份系统 (log_backup.py)

**功能特性：**
- 自动定时备份
- 多种压缩格式支持
- 灵活的保留策略
- 备份完整性验证
- 恢复功能

**备份类型：**
- 日备份
- 周备份
- 月备份
- 年备份

**压缩格式：**
- GZIP
- TAR.GZ
- ZIP

**主要类：**
- `BackupConfig`: 备份配置
- `BackupRecord`: 备份记录
- `LogBackupManager`: 备份管理器

**使用示例：**
```python
from qlib_trading_system.utils.logging.log_backup import log_backup_manager

# 执行备份
backup_record = log_backup_manager.backup_logs("trading_daily")
print(f"备份完成，压缩比: {backup_record.compression_ratio:.2%}")
```

### 5. 综合日志管理器 (log_manager.py)

**功能特性：**
- 统一的日志管理接口
- 实时监控
- 健康状态检查
- 综合报告生成
- 自动告警

**主要功能：**
- 事件记录统一入口
- 自动合规检查
- 定期日志分析
- 系统健康评估
- 综合报告生成

**使用示例：**
```python
from qlib_trading_system.utils.logging.log_manager import log_manager

# 记录交易事件
log_manager.log_trading_event(
    event_type="ORDER_SUBMIT",
    details={"symbol": "000001.SZ", "amount": 15500},
    user_id="user_001"
)

# 获取系统健康状态
health = log_manager.get_system_health()
print(f"健康分数: {health['health_score']}")
```

## 目录结构

```
qlib_trading_system/utils/logging/
├── __init__.py                 # 模块初始化
├── logger.py                   # 基础日志配置
├── audit_logger.py             # 审计日志系统
├── compliance_checker.py       # 合规检查机制
├── log_analyzer_simple.py      # 日志分析工具
├── log_backup.py              # 日志备份系统
├── log_manager.py             # 综合日志管理器
└── README.md                  # 系统文档
```

## 日志文件结构

```
logs/
├── audit/                     # 审计日志
│   ├── trading_audit.log      # 交易审计日志
│   ├── risk_audit.log         # 风险审计日志
│   ├── system_audit.log       # 系统审计日志
│   └── user_audit.log         # 用户审计日志
├── backup/                    # 备份目录
│   ├── trading/               # 交易日志备份
│   ├── audit/                 # 审计日志备份
│   ├── system/                # 系统日志备份
│   └── risk/                  # 风险日志备份
└── system/                    # 系统日志
    └── trading_system.log     # 主系统日志
```

## 配置文件

### 备份配置 (config/log_backup_config.json)

```json
{
  "trading_daily": {
    "backup_type": "daily",
    "source_path": "logs/trading",
    "backup_path": "logs/backup/trading/daily",
    "compression": "gzip",
    "storage_type": "local",
    "retention_days": 30,
    "enabled": true,
    "schedule_time": "02:00"
  }
}
```

## 监管合规

### 数据保留策略

- **交易审计日志**: 保留7年（监管要求）
- **风险审计日志**: 保留5年
- **系统日志**: 保留1年
- **用户操作日志**: 保留5年

### 合规检查规则

1. **交易频率限制**: 单用户单日不超过1000次交易
2. **交易金额限制**: 单笔交易不超过账户总资产50%
3. **交易时间限制**: 只能在交易时间内进行交易
4. **持仓集中度限制**: 单只股票持仓不超过总资产80%
5. **风险限制**: 日内亏损不超过账户总资产10%

## 性能指标

### 系统性能

- **日志写入延迟**: < 10ms
- **合规检查延迟**: < 50ms
- **异常检测延迟**: < 100ms
- **备份压缩比**: 平均70%

### 存储效率

- **日志压缩**: 使用GZIP/TAR.GZ压缩
- **自动清理**: 根据保留策略自动清理过期日志
- **增量备份**: 支持增量备份减少存储空间

## 安全特性

### 数据安全

- **日志完整性**: 使用校验和验证日志完整性
- **访问控制**: 严格的文件权限控制
- **加密存储**: 敏感信息加密存储

### 审计追踪

- **操作记录**: 所有操作都有完整的审计记录
- **用户追踪**: 记录用户ID、会话ID、IP地址
- **时间戳**: 精确到毫秒的时间戳记录

## 监控和告警

### 实时监控

- **异常检测**: 实时检测日志异常
- **合规监控**: 实时合规检查
- **性能监控**: 系统性能指标监控

### 告警机制

- **严重异常**: 立即告警
- **合规违规**: 根据级别告警
- **系统故障**: 自动告警和恢复

## 使用指南

### 快速开始

1. **导入日志管理器**:
```python
from qlib_trading_system.utils.logging.log_manager import log_manager
```

2. **记录交易事件**:
```python
log_manager.log_trading_event(
    event_type="ORDER_SUBMIT",
    details={"symbol": "000001.SZ", "amount": 15500},
    user_id="user_001"
)
```

3. **检查系统健康**:
```python
health = log_manager.get_system_health()
print(f"健康状态: {health['status']}")
```

### 高级功能

1. **自定义合规规则**:
```python
from qlib_trading_system.utils.logging.compliance_checker import compliance_checker, ComplianceRule

rule = ComplianceRule(
    rule_id="CUSTOM_001",
    rule_name="自定义规则",
    rule_type=ComplianceRuleType.TRADING_LIMIT,
    description="自定义交易限制",
    conditions={"max_amount": 100000},
    violation_level=ComplianceViolationLevel.HIGH
)
compliance_checker.add_rule(rule)
```

2. **自定义备份配置**:
```python
from qlib_trading_system.utils.logging.log_backup import log_backup_manager, BackupConfig

config = BackupConfig(
    backup_type=BackupType.DAILY,
    source_path="logs/custom",
    backup_path="logs/backup/custom",
    compression=CompressionType.TAR_GZ,
    storage_type=StorageType.LOCAL,
    retention_days=30
)
log_backup_manager.add_backup_config("custom_backup", config)
```

## 故障排除

### 常见问题

1. **日志文件过大**: 检查日志轮转配置
2. **备份失败**: 检查磁盘空间和权限
3. **合规检查误报**: 调整规则阈值
4. **性能问题**: 优化日志级别和频率

### 调试方法

1. **启用调试日志**: 设置日志级别为DEBUG
2. **检查配置文件**: 验证配置文件格式和内容
3. **查看系统日志**: 检查系统日志中的错误信息
4. **运行测试**: 使用测试脚本验证功能

## 扩展开发

### 添加新的事件类型

1. 在`AuditEventType`枚举中添加新类型
2. 在`audit_logger.py`中添加对应的记录方法
3. 在`compliance_checker.py`中添加相应的检查规则

### 添加新的异常检测

1. 在`AnomalyType`枚举中添加新类型
2. 在`log_analyzer.py`中实现检测逻辑
3. 添加相应的测试用例

### 集成外部系统

1. 实现相应的接口适配器
2. 添加配置选项
3. 更新文档和测试

## 版本历史

- **v1.0.0**: 初始版本，实现基础功能
- **v1.1.0**: 添加合规检查功能
- **v1.2.0**: 添加日志分析功能
- **v1.3.0**: 添加备份功能
- **v1.4.0**: 添加综合管理功能

## 许可证

本系统遵循项目整体许可证。

## 联系方式

如有问题或建议，请联系开发团队。