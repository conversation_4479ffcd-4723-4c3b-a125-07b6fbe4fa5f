"""
合规检查机制
实现操作审计和合规检查功能
"""
import json
import re
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
from pathlib import Path

from .audit_logger import AuditEvent, AuditEventType, AuditLevel, audit_logger
from .logger import trading_logger


class ComplianceRuleType(Enum):
    """合规规则类型"""
    TRADING_LIMIT = "trading_limit"         # 交易限制
    POSITION_LIMIT = "position_limit"       # 持仓限制
    RISK_LIMIT = "risk_limit"               # 风险限制
    TIME_RESTRICTION = "time_restriction"   # 时间限制
    FREQUENCY_LIMIT = "frequency_limit"     # 频率限制
    AMOUNT_LIMIT = "amount_limit"           # 金额限制
    BEHAVIOR_PATTERN = "behavior_pattern"   # 行为模式
    DATA_INTEGRITY = "data_integrity"       # 数据完整性


class ComplianceViolationLevel(Enum):
    """合规违规级别"""
    LOW = "LOW"                 # 低级别
    MEDIUM = "MEDIUM"           # 中级别
    HIGH = "HIGH"               # 高级别
    CRITICAL = "CRITICAL"       # 严重级别


@dataclass
class ComplianceRule:
    """合规规则"""
    rule_id: str                        # 规则ID
    rule_name: str                      # 规则名称
    rule_type: ComplianceRuleType       # 规则类型
    description: str                    # 规则描述
    conditions: Dict[str, Any]          # 规则条件
    violation_level: ComplianceViolationLevel  # 违规级别
    enabled: bool = True                # 是否启用
    created_time: datetime = None       # 创建时间
    updated_time: datetime = None       # 更新时间
    
    def __post_init__(self):
        if self.created_time is None:
            self.created_time = datetime.now()
        if self.updated_time is None:
            self.updated_time = datetime.now()


@dataclass
class ComplianceViolation:
    """合规违规记录"""
    violation_id: str                   # 违规ID
    rule_id: str                        # 规则ID
    rule_name: str                      # 规则名称
    violation_level: ComplianceViolationLevel  # 违规级别
    violation_time: datetime            # 违规时间
    event_id: str                       # 关联的审计事件ID
    description: str                    # 违规描述
    details: Dict[str, Any]             # 违规详情
    user_id: Optional[str] = None       # 用户ID
    component: Optional[str] = None     # 组件名称
    resolved: bool = False              # 是否已解决
    resolution_notes: Optional[str] = None  # 解决说明


class ComplianceChecker:
    """合规检查器"""
    
    def __init__(self):
        """初始化合规检查器"""
        self.logger = trading_logger.get_logger("COMPLIANCE")
        self.rules: Dict[str, ComplianceRule] = {}
        self.violations: List[ComplianceViolation] = []
        self._load_default_rules()
        
    def _load_default_rules(self):
        """加载默认合规规则"""
        # 交易频率限制规则
        self.add_rule(ComplianceRule(
            rule_id="FREQ_001",
            rule_name="单日交易频率限制",
            rule_type=ComplianceRuleType.FREQUENCY_LIMIT,
            description="单个用户单日交易次数不得超过1000次",
            conditions={
                "max_trades_per_day": 1000,
                "time_window": "1d"
            },
            violation_level=ComplianceViolationLevel.MEDIUM
        ))
        
        # 单笔交易金额限制
        self.add_rule(ComplianceRule(
            rule_id="AMOUNT_001",
            rule_name="单笔交易金额限制",
            rule_type=ComplianceRuleType.AMOUNT_LIMIT,
            description="单笔交易金额不得超过账户总资产的50%",
            conditions={
                "max_trade_ratio": 0.5
            },
            violation_level=ComplianceViolationLevel.HIGH
        ))
        
        # 交易时间限制
        self.add_rule(ComplianceRule(
            rule_id="TIME_001",
            rule_name="交易时间限制",
            rule_type=ComplianceRuleType.TIME_RESTRICTION,
            description="只能在交易时间内进行交易",
            conditions={
                "allowed_hours": [(9, 30), (11, 30), (13, 0), (15, 0)],
                "allowed_days": [0, 1, 2, 3, 4]  # 周一到周五
            },
            violation_level=ComplianceViolationLevel.HIGH
        ))
        
        # 持仓集中度限制
        self.add_rule(ComplianceRule(
            rule_id="POS_001",
            rule_name="持仓集中度限制",
            rule_type=ComplianceRuleType.POSITION_LIMIT,
            description="单只股票持仓不得超过总资产的80%",
            conditions={
                "max_single_position_ratio": 0.8
            },
            violation_level=ComplianceViolationLevel.CRITICAL
        ))
        
        # 风险限制规则
        self.add_rule(ComplianceRule(
            rule_id="RISK_001",
            rule_name="日内最大亏损限制",
            rule_type=ComplianceRuleType.RISK_LIMIT,
            description="日内亏损不得超过账户总资产的10%",
            conditions={
                "max_daily_loss_ratio": 0.1
            },
            violation_level=ComplianceViolationLevel.CRITICAL
        ))
        
        # 异常行为模式检测
        self.add_rule(ComplianceRule(
            rule_id="PATTERN_001",
            rule_name="异常交易模式检测",
            rule_type=ComplianceRuleType.BEHAVIOR_PATTERN,
            description="检测异常的交易行为模式",
            conditions={
                "min_interval_seconds": 1,  # 最小交易间隔
                "max_cancel_ratio": 0.8     # 最大撤单比例
            },
            violation_level=ComplianceViolationLevel.MEDIUM
        ))
    
    def add_rule(self, rule: ComplianceRule):
        """添加合规规则"""
        self.rules[rule.rule_id] = rule
        self.logger.info(f"添加合规规则: {rule.rule_name} ({rule.rule_id})")
    
    def remove_rule(self, rule_id: str):
        """移除合规规则"""
        if rule_id in self.rules:
            rule_name = self.rules[rule_id].rule_name
            del self.rules[rule_id]
            self.logger.info(f"移除合规规则: {rule_name} ({rule_id})")
    
    def enable_rule(self, rule_id: str):
        """启用合规规则"""
        if rule_id in self.rules:
            self.rules[rule_id].enabled = True
            self.rules[rule_id].updated_time = datetime.now()
            self.logger.info(f"启用合规规则: {rule_id}")
    
    def disable_rule(self, rule_id: str):
        """禁用合规规则"""
        if rule_id in self.rules:
            self.rules[rule_id].enabled = False
            self.rules[rule_id].updated_time = datetime.now()
            self.logger.info(f"禁用合规规则: {rule_id}")
    
    def check_compliance(self, event: AuditEvent) -> List[ComplianceViolation]:
        """检查合规性"""
        violations = []
        
        for rule in self.rules.values():
            if not rule.enabled:
                continue
                
            try:
                violation = self._check_rule(rule, event)
                if violation:
                    violations.append(violation)
                    self.violations.append(violation)
                    self._handle_violation(violation)
                    
            except Exception as e:
                self.logger.error(f"检查合规规则 {rule.rule_id} 时发生错误: {str(e)}")
        
        return violations
    
    def _check_rule(self, rule: ComplianceRule, event: AuditEvent) -> Optional[ComplianceViolation]:
        """检查单个规则"""
        if rule.rule_type == ComplianceRuleType.FREQUENCY_LIMIT:
            return self._check_frequency_limit(rule, event)
        elif rule.rule_type == ComplianceRuleType.AMOUNT_LIMIT:
            return self._check_amount_limit(rule, event)
        elif rule.rule_type == ComplianceRuleType.TIME_RESTRICTION:
            return self._check_time_restriction(rule, event)
        elif rule.rule_type == ComplianceRuleType.POSITION_LIMIT:
            return self._check_position_limit(rule, event)
        elif rule.rule_type == ComplianceRuleType.RISK_LIMIT:
            return self._check_risk_limit(rule, event)
        elif rule.rule_type == ComplianceRuleType.BEHAVIOR_PATTERN:
            return self._check_behavior_pattern(rule, event)
        
        return None
    
    def _check_frequency_limit(self, rule: ComplianceRule, event: AuditEvent) -> Optional[ComplianceViolation]:
        """检查交易频率限制"""
        if event.event_type not in [AuditEventType.ORDER_SUBMIT]:
            return None
            
        max_trades = rule.conditions.get("max_trades_per_day", 1000)
        
        # 统计今日交易次数（这里简化处理，实际应该查询数据库）
        today_trades = self._count_today_trades(event.user_id)
        
        if today_trades > max_trades:
            return ComplianceViolation(
                violation_id=f"FREQ_VIOLATION_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                rule_id=rule.rule_id,
                rule_name=rule.rule_name,
                violation_level=rule.violation_level,
                violation_time=datetime.now(),
                event_id=event.event_id,
                description=f"交易频率超限: 今日已交易{today_trades}次，超过限制{max_trades}次",
                details={
                    "today_trades": today_trades,
                    "max_allowed": max_trades,
                    "user_id": event.user_id
                },
                user_id=event.user_id,
                component=event.component
            )
        
        return None
    
    def _check_amount_limit(self, rule: ComplianceRule, event: AuditEvent) -> Optional[ComplianceViolation]:
        """检查交易金额限制"""
        if event.event_type not in [AuditEventType.ORDER_SUBMIT]:
            return None
            
        max_ratio = rule.conditions.get("max_trade_ratio", 0.5)
        trade_amount = event.details.get("amount", 0)
        total_assets = event.details.get("total_assets", 1)
        
        if total_assets > 0:
            trade_ratio = trade_amount / total_assets
            if trade_ratio > max_ratio:
                return ComplianceViolation(
                    violation_id=f"AMOUNT_VIOLATION_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    rule_id=rule.rule_id,
                    rule_name=rule.rule_name,
                    violation_level=rule.violation_level,
                    violation_time=datetime.now(),
                    event_id=event.event_id,
                    description=f"交易金额超限: 交易比例{trade_ratio:.2%}，超过限制{max_ratio:.2%}",
                    details={
                        "trade_amount": trade_amount,
                        "total_assets": total_assets,
                        "trade_ratio": trade_ratio,
                        "max_allowed_ratio": max_ratio
                    },
                    user_id=event.user_id,
                    component=event.component
                )
        
        return None
    
    def _check_time_restriction(self, rule: ComplianceRule, event: AuditEvent) -> Optional[ComplianceViolation]:
        """检查交易时间限制"""
        if event.event_type not in [AuditEventType.ORDER_SUBMIT]:
            return None
            
        now = datetime.now()
        allowed_hours = rule.conditions.get("allowed_hours", [])
        allowed_days = rule.conditions.get("allowed_days", [])
        
        # 检查是否在允许的交易日
        if now.weekday() not in allowed_days:
            return ComplianceViolation(
                violation_id=f"TIME_VIOLATION_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                rule_id=rule.rule_id,
                rule_name=rule.rule_name,
                violation_level=rule.violation_level,
                violation_time=datetime.now(),
                event_id=event.event_id,
                description=f"非交易日交易: 当前为{now.strftime('%A')}，不在允许的交易日内",
                details={
                    "current_day": now.weekday(),
                    "allowed_days": allowed_days
                },
                user_id=event.user_id,
                component=event.component
            )
        
        # 检查是否在允许的交易时间
        current_time = now.hour * 100 + now.minute
        in_trading_hours = False
        
        for start_hour, start_min in allowed_hours[::2]:
            end_hour, end_min = allowed_hours[allowed_hours.index((start_hour, start_min)) + 1]
            start_time = start_hour * 100 + start_min
            end_time = end_hour * 100 + end_min
            
            if start_time <= current_time <= end_time:
                in_trading_hours = True
                break
        
        if not in_trading_hours:
            return ComplianceViolation(
                violation_id=f"TIME_VIOLATION_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                rule_id=rule.rule_id,
                rule_name=rule.rule_name,
                violation_level=rule.violation_level,
                violation_time=datetime.now(),
                event_id=event.event_id,
                description=f"非交易时间交易: 当前时间{now.strftime('%H:%M')}不在允许的交易时间内",
                details={
                    "current_time": now.strftime('%H:%M'),
                    "allowed_hours": allowed_hours
                },
                user_id=event.user_id,
                component=event.component
            )
        
        return None
    
    def _check_position_limit(self, rule: ComplianceRule, event: AuditEvent) -> Optional[ComplianceViolation]:
        """检查持仓限制"""
        if event.event_type not in [AuditEventType.POSITION_CHANGE]:
            return None
            
        max_ratio = rule.conditions.get("max_single_position_ratio", 0.8)
        position_value = event.details.get("position_value", 0)
        total_assets = event.details.get("total_assets", 1)
        
        if total_assets > 0:
            position_ratio = position_value / total_assets
            if position_ratio > max_ratio:
                return ComplianceViolation(
                    violation_id=f"POS_VIOLATION_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    rule_id=rule.rule_id,
                    rule_name=rule.rule_name,
                    violation_level=rule.violation_level,
                    violation_time=datetime.now(),
                    event_id=event.event_id,
                    description=f"持仓集中度超限: 单只股票持仓比例{position_ratio:.2%}，超过限制{max_ratio:.2%}",
                    details={
                        "position_value": position_value,
                        "total_assets": total_assets,
                        "position_ratio": position_ratio,
                        "max_allowed_ratio": max_ratio,
                        "symbol": event.details.get("symbol")
                    },
                    user_id=event.user_id,
                    component=event.component
                )
        
        return None
    
    def _check_risk_limit(self, rule: ComplianceRule, event: AuditEvent) -> Optional[ComplianceViolation]:
        """检查风险限制"""
        if event.event_type not in [AuditEventType.RISK_ALERT, AuditEventType.RISK_LIMIT_HIT]:
            return None
            
        max_loss_ratio = rule.conditions.get("max_daily_loss_ratio", 0.1)
        daily_pnl = event.details.get("daily_pnl", 0)
        total_assets = event.details.get("total_assets", 1)
        
        if total_assets > 0 and daily_pnl < 0:
            loss_ratio = abs(daily_pnl) / total_assets
            if loss_ratio > max_loss_ratio:
                return ComplianceViolation(
                    violation_id=f"RISK_VIOLATION_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    rule_id=rule.rule_id,
                    rule_name=rule.rule_name,
                    violation_level=rule.violation_level,
                    violation_time=datetime.now(),
                    event_id=event.event_id,
                    description=f"日内亏损超限: 亏损比例{loss_ratio:.2%}，超过限制{max_loss_ratio:.2%}",
                    details={
                        "daily_pnl": daily_pnl,
                        "total_assets": total_assets,
                        "loss_ratio": loss_ratio,
                        "max_allowed_ratio": max_loss_ratio
                    },
                    user_id=event.user_id,
                    component=event.component
                )
        
        return None
    
    def _check_behavior_pattern(self, rule: ComplianceRule, event: AuditEvent) -> Optional[ComplianceViolation]:
        """检查行为模式"""
        # 这里可以实现更复杂的行为模式检测逻辑
        # 例如检测高频交易、异常撤单等行为
        return None
    
    def _count_today_trades(self, user_id: Optional[str]) -> int:
        """统计今日交易次数（简化实现）"""
        # 实际实现中应该查询数据库
        return 0
    
    def _handle_violation(self, violation: ComplianceViolation):
        """处理合规违规"""
        # 记录违规到审计日志
        audit_logger.log_system_event(
            action="COMPLIANCE_VIOLATION",
            details={
                "violation_id": violation.violation_id,
                "rule_id": violation.rule_id,
                "rule_name": violation.rule_name,
                "violation_level": violation.violation_level.value,
                "description": violation.description,
                "details": violation.details
            },
            component="COMPLIANCE",
            level=AuditLevel.WARNING if violation.violation_level in [
                ComplianceViolationLevel.LOW, ComplianceViolationLevel.MEDIUM
            ] else AuditLevel.ERROR
        )
        
        # 根据违规级别采取相应措施
        if violation.violation_level == ComplianceViolationLevel.CRITICAL:
            self.logger.critical(f"严重合规违规: {violation.description}")
            # 可以触发紧急停止交易等措施
        elif violation.violation_level == ComplianceViolationLevel.HIGH:
            self.logger.error(f"高级别合规违规: {violation.description}")
            # 可以触发风险控制措施
        else:
            self.logger.warning(f"合规违规: {violation.description}")
    
    def get_violations(
        self,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        violation_level: Optional[ComplianceViolationLevel] = None,
        user_id: Optional[str] = None
    ) -> List[ComplianceViolation]:
        """获取违规记录"""
        filtered_violations = self.violations
        
        if start_time:
            filtered_violations = [v for v in filtered_violations if v.violation_time >= start_time]
        
        if end_time:
            filtered_violations = [v for v in filtered_violations if v.violation_time <= end_time]
        
        if violation_level:
            filtered_violations = [v for v in filtered_violations if v.violation_level == violation_level]
        
        if user_id:
            filtered_violations = [v for v in filtered_violations if v.user_id == user_id]
        
        return filtered_violations
    
    def resolve_violation(self, violation_id: str, resolution_notes: str):
        """解决违规"""
        for violation in self.violations:
            if violation.violation_id == violation_id:
                violation.resolved = True
                violation.resolution_notes = resolution_notes
                self.logger.info(f"违规已解决: {violation_id}")
                break
    
    def get_compliance_report(self) -> Dict[str, Any]:
        """生成合规报告"""
        total_violations = len(self.violations)
        unresolved_violations = len([v for v in self.violations if not v.resolved])
        
        violation_by_level = {}
        for level in ComplianceViolationLevel:
            violation_by_level[level.value] = len([
                v for v in self.violations if v.violation_level == level
            ])
        
        violation_by_rule = {}
        for violation in self.violations:
            rule_id = violation.rule_id
            if rule_id not in violation_by_rule:
                violation_by_rule[rule_id] = 0
            violation_by_rule[rule_id] += 1
        
        return {
            "report_time": datetime.now().isoformat(),
            "total_violations": total_violations,
            "unresolved_violations": unresolved_violations,
            "violation_by_level": violation_by_level,
            "violation_by_rule": violation_by_rule,
            "active_rules": len([r for r in self.rules.values() if r.enabled]),
            "total_rules": len(self.rules)
        }


# 全局合规检查器实例
compliance_checker = ComplianceChecker()