"""
审计日志系统
实现全链路交易日志记录和操作审计
"""
import json
import uuid
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path

from .logger import trading_logger


class AuditEventType(Enum):
    """审计事件类型"""
    # 交易相关
    ORDER_SUBMIT = "order_submit"           # 订单提交
    ORDER_CANCEL = "order_cancel"           # 订单取消
    ORDER_MODIFY = "order_modify"           # 订单修改
    ORDER_FILL = "order_fill"               # 订单成交
    POSITION_CHANGE = "position_change"     # 持仓变化
    
    # 风险控制
    RISK_ALERT = "risk_alert"               # 风险警报
    RISK_LIMIT_HIT = "risk_limit_hit"       # 风险限制触发
    CIRCUIT_BREAKER = "circuit_breaker"     # 熔断触发
    STOP_LOSS = "stop_loss"                 # 止损触发
    
    # 系统操作
    SYSTEM_START = "system_start"           # 系统启动
    SYSTEM_STOP = "system_stop"             # 系统停止
    CONFIG_CHANGE = "config_change"         # 配置变更
    MODEL_UPDATE = "model_update"           # 模型更新
    
    # 用户操作
    USER_LOGIN = "user_login"               # 用户登录
    USER_LOGOUT = "user_logout"             # 用户登出
    MANUAL_OVERRIDE = "manual_override"     # 手动干预
    STRATEGY_ENABLE = "strategy_enable"     # 策略启用
    STRATEGY_DISABLE = "strategy_disable"   # 策略禁用
    
    # 数据操作
    DATA_FETCH = "data_fetch"               # 数据获取
    DATA_ERROR = "data_error"               # 数据错误
    DATA_QUALITY_ISSUE = "data_quality_issue"  # 数据质量问题


class AuditLevel(Enum):
    """审计级别"""
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


@dataclass
class AuditEvent:
    """审计事件"""
    event_id: str                           # 事件ID
    timestamp: datetime                     # 时间戳
    event_type: AuditEventType             # 事件类型
    level: AuditLevel                      # 审计级别
    user_id: Optional[str]                 # 用户ID
    session_id: Optional[str]              # 会话ID
    component: str                         # 组件名称
    action: str                            # 具体操作
    details: Dict[str, Any]                # 详细信息
    before_state: Optional[Dict[str, Any]] # 操作前状态
    after_state: Optional[Dict[str, Any]]  # 操作后状态
    ip_address: Optional[str]              # IP地址
    result: str                            # 操作结果
    error_message: Optional[str]           # 错误信息
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        data['event_type'] = self.event_type.value
        data['level'] = self.level.value
        return data
    
    def to_json(self) -> str:
        """转换为JSON字符串"""
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2)


class AuditLogger:
    """审计日志记录器"""
    
    def __init__(self):
        """初始化审计日志记录器"""
        self.logger = trading_logger.get_logger("AUDIT")
        self._setup_audit_files()
        
    def _setup_audit_files(self):
        """设置审计日志文件"""
        # 创建审计日志目录
        audit_dir = Path("logs/audit")
        audit_dir.mkdir(parents=True, exist_ok=True)
        
        # 设置不同类型的审计日志文件
        self._setup_trading_audit_log()
        self._setup_risk_audit_log()
        self._setup_system_audit_log()
        self._setup_user_audit_log()
        
    def _setup_trading_audit_log(self):
        """设置交易审计日志"""
        from loguru import logger
        
        logger.add(
            "logs/audit/trading_audit.log",
            format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level} | {message}",
            level="INFO",
            rotation="daily",
            retention="7 years",  # 监管要求保存7年
            compression="zip",
            encoding="utf-8",
            filter=lambda record: "TRADING_AUDIT" in record["extra"]
        )
        
    def _setup_risk_audit_log(self):
        """设置风险审计日志"""
        from loguru import logger
        
        logger.add(
            "logs/audit/risk_audit.log",
            format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level} | {message}",
            level="INFO",
            rotation="daily",
            retention="7 years",
            compression="zip",
            encoding="utf-8",
            filter=lambda record: "RISK_AUDIT" in record["extra"]
        )
        
    def _setup_system_audit_log(self):
        """设置系统审计日志"""
        from loguru import logger
        
        logger.add(
            "logs/audit/system_audit.log",
            format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level} | {message}",
            level="INFO",
            rotation="daily",
            retention="3 years",
            compression="zip",
            encoding="utf-8",
            filter=lambda record: "SYSTEM_AUDIT" in record["extra"]
        )
        
    def _setup_user_audit_log(self):
        """设置用户审计日志"""
        from loguru import logger
        
        logger.add(
            "logs/audit/user_audit.log",
            format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level} | {message}",
            level="INFO",
            rotation="daily",
            retention="5 years",
            compression="zip",
            encoding="utf-8",
            filter=lambda record: "USER_AUDIT" in record["extra"]
        )
    
    def log_event(self, event: AuditEvent):
        """记录审计事件"""
        try:
            # 根据事件类型选择合适的日志器
            if event.event_type in [
                AuditEventType.ORDER_SUBMIT, AuditEventType.ORDER_CANCEL,
                AuditEventType.ORDER_MODIFY, AuditEventType.ORDER_FILL,
                AuditEventType.POSITION_CHANGE
            ]:
                logger_extra = {"TRADING_AUDIT": True}
            elif event.event_type in [
                AuditEventType.RISK_ALERT, AuditEventType.RISK_LIMIT_HIT,
                AuditEventType.CIRCUIT_BREAKER, AuditEventType.STOP_LOSS
            ]:
                logger_extra = {"RISK_AUDIT": True}
            elif event.event_type in [
                AuditEventType.USER_LOGIN, AuditEventType.USER_LOGOUT,
                AuditEventType.MANUAL_OVERRIDE
            ]:
                logger_extra = {"USER_AUDIT": True}
            else:
                logger_extra = {"SYSTEM_AUDIT": True}
            
            # 记录到对应的审计日志
            from loguru import logger
            audit_logger = logger.bind(**logger_extra)
            
            # 根据级别记录日志
            log_message = event.to_json()
            if event.level == AuditLevel.INFO:
                audit_logger.info(log_message)
            elif event.level == AuditLevel.WARNING:
                audit_logger.warning(log_message)
            elif event.level == AuditLevel.ERROR:
                audit_logger.error(log_message)
            elif event.level == AuditLevel.CRITICAL:
                audit_logger.critical(log_message)
                
        except Exception as e:
            self.logger.error(f"记录审计事件失败: {str(e)}")
    
    def log_trading_action(
        self,
        action: str,
        details: Dict[str, Any],
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        before_state: Optional[Dict[str, Any]] = None,
        after_state: Optional[Dict[str, Any]] = None,
        result: str = "SUCCESS",
        error_message: Optional[str] = None
    ):
        """记录交易操作"""
        event = AuditEvent(
            event_id=str(uuid.uuid4()),
            timestamp=datetime.now(timezone.utc),
            event_type=AuditEventType.ORDER_SUBMIT,  # 根据action动态确定
            level=AuditLevel.ERROR if error_message else AuditLevel.INFO,
            user_id=user_id,
            session_id=session_id,
            component="TRADING",
            action=action,
            details=details,
            before_state=before_state,
            after_state=after_state,
            ip_address=None,
            result=result,
            error_message=error_message
        )
        self.log_event(event)
    
    def log_risk_event(
        self,
        risk_type: str,
        details: Dict[str, Any],
        level: AuditLevel = AuditLevel.WARNING,
        component: str = "RISK"
    ):
        """记录风险事件"""
        event = AuditEvent(
            event_id=str(uuid.uuid4()),
            timestamp=datetime.now(timezone.utc),
            event_type=AuditEventType.RISK_ALERT,
            level=level,
            user_id=None,
            session_id=None,
            component=component,
            action=risk_type,
            details=details,
            before_state=None,
            after_state=None,
            ip_address=None,
            result="DETECTED",
            error_message=None
        )
        self.log_event(event)
    
    def log_system_event(
        self,
        action: str,
        details: Dict[str, Any],
        component: str = "SYSTEM",
        level: AuditLevel = AuditLevel.INFO
    ):
        """记录系统事件"""
        event = AuditEvent(
            event_id=str(uuid.uuid4()),
            timestamp=datetime.now(timezone.utc),
            event_type=AuditEventType.SYSTEM_START,  # 根据action动态确定
            level=level,
            user_id=None,
            session_id=None,
            component=component,
            action=action,
            details=details,
            before_state=None,
            after_state=None,
            ip_address=None,
            result="SUCCESS",
            error_message=None
        )
        self.log_event(event)
    
    def log_user_action(
        self,
        action: str,
        user_id: str,
        session_id: str,
        details: Dict[str, Any],
        ip_address: Optional[str] = None,
        result: str = "SUCCESS",
        error_message: Optional[str] = None
    ):
        """记录用户操作"""
        event = AuditEvent(
            event_id=str(uuid.uuid4()),
            timestamp=datetime.now(timezone.utc),
            event_type=AuditEventType.USER_LOGIN,  # 根据action动态确定
            level=AuditLevel.ERROR if error_message else AuditLevel.INFO,
            user_id=user_id,
            session_id=session_id,
            component="USER",
            action=action,
            details=details,
            before_state=None,
            after_state=None,
            ip_address=ip_address,
            result=result,
            error_message=error_message
        )
        self.log_event(event)


# 全局审计日志记录器实例
audit_logger = AuditLogger()


def log_audit_event(event_type: AuditEventType, **kwargs):
    """审计事件装饰器"""
    def decorator(func):
        def wrapper(*args, **func_kwargs):
            start_time = datetime.now(timezone.utc)
            event_id = str(uuid.uuid4())
            
            try:
                result = func(*args, **func_kwargs)
                
                # 记录成功的审计事件
                event = AuditEvent(
                    event_id=event_id,
                    timestamp=start_time,
                    event_type=event_type,
                    level=AuditLevel.INFO,
                    user_id=kwargs.get('user_id'),
                    session_id=kwargs.get('session_id'),
                    component=kwargs.get('component', func.__module__),
                    action=func.__name__,
                    details=kwargs.get('details', {}),
                    before_state=kwargs.get('before_state'),
                    after_state=kwargs.get('after_state'),
                    ip_address=kwargs.get('ip_address'),
                    result="SUCCESS",
                    error_message=None
                )
                audit_logger.log_event(event)
                return result
                
            except Exception as e:
                # 记录失败的审计事件
                event = AuditEvent(
                    event_id=event_id,
                    timestamp=start_time,
                    event_type=event_type,
                    level=AuditLevel.ERROR,
                    user_id=kwargs.get('user_id'),
                    session_id=kwargs.get('session_id'),
                    component=kwargs.get('component', func.__module__),
                    action=func.__name__,
                    details=kwargs.get('details', {}),
                    before_state=kwargs.get('before_state'),
                    after_state=kwargs.get('after_state'),
                    ip_address=kwargs.get('ip_address'),
                    result="FAILED",
                    error_message=str(e)
                )
                audit_logger.log_event(event)
                raise
                
        return wrapper
    return decorator