"""
日志装饰器
"""
import functools
import time
from typing import Callable, Any
from .logger import trading_logger


def log_execution_time(logger_name: str = "PERFORMANCE"):
    """记录函数执行时间的装饰器"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            logger = trading_logger.get_logger(logger_name)
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                logger.info(
                    f"{func.__name__} 执行完成，耗时: {execution_time:.4f}秒"
                )
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(
                    f"{func.__name__} 执行失败，耗时: {execution_time:.4f}秒，错误: {str(e)}"
                )
                raise
        return wrapper
    return decorator


def log_trading_action(action_type: str):
    """记录交易行为的装饰器"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            logger = trading_logger.get_trading_logger()
            
            # 记录开始
            logger.info(f"开始执行交易行为: {action_type}")
            
            try:
                result = func(*args, **kwargs)
                logger.info(f"交易行为执行成功: {action_type}")
                return result
            except Exception as e:
                logger.error(f"交易行为执行失败: {action_type}, 错误: {str(e)}")
                raise
        return wrapper
    return decorator


def log_risk_event(risk_type: str):
    """记录风险事件的装饰器"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            logger = trading_logger.get_risk_logger()
            
            try:
                result = func(*args, **kwargs)
                if result:  # 如果检测到风险
                    logger.warning(f"检测到风险事件: {risk_type}")
                return result
            except Exception as e:
                logger.error(f"风险检测异常: {risk_type}, 错误: {str(e)}")
                raise
        return wrapper
    return decorator


def log_data_operation(operation_type: str):
    """记录数据操作的装饰器"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            logger = trading_logger.get_logger("DATA")
            
            logger.debug(f"开始数据操作: {operation_type}")
            
            try:
                result = func(*args, **kwargs)
                logger.debug(f"数据操作完成: {operation_type}")
                return result
            except Exception as e:
                logger.error(f"数据操作失败: {operation_type}, 错误: {str(e)}")
                raise
        return wrapper
    return decorator