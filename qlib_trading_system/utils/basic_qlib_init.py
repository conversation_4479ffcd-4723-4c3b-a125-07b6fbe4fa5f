"""
基础Qlib初始化（不依赖完整qlib包）
"""
import os
import sys
from pathlib import Path
from typing import Dict, Any

from qlib_trading_system.utils.logging.logger import system_logger


class BasicQlibInitializer:
    """基础Qlib初始化器"""
    
    def __init__(self):
        """初始化"""
        self.logger = system_logger
        self.is_initialized = False
    
    def setup_basic_environment(self) -> bool:
        """设置基础环境"""
        try:
            # 创建qlib数据目录
            qlib_data_dir = Path.home() / ".qlib" / "qlib_data" / "cn_data"
            qlib_data_dir.mkdir(parents=True, exist_ok=True)
            
            self.logger.info(f"Qlib数据目录已创建: {qlib_data_dir}")
            
            # 设置环境变量
            os.environ["QLIB_DATA_PATH"] = str(qlib_data_dir)
            
            # 确保环境变量持久化
            if os.name == 'nt':  # Windows
                import subprocess
                subprocess.run([
                    'setx', 'QLIB_DATA_PATH', str(qlib_data_dir)
                ], capture_output=True)
            
            self.is_initialized = True
            return True
            
        except Exception as e:
            self.logger.error(f"基础环境设置失败: {str(e)}")
            return False
    
    def create_mock_data_structure(self) -> bool:
        """创建模拟数据结构"""
        try:
            data_dir = Path.home() / ".qlib" / "qlib_data" / "cn_data"
            
            # 创建基础数据目录结构
            subdirs = [
                "instruments",
                "features", 
                "calendars",
                "price_data"
            ]
            
            for subdir in subdirs:
                (data_dir / subdir).mkdir(parents=True, exist_ok=True)
            
            # 创建基础配置文件
            config_content = {
                "provider_uri": str(data_dir),
                "region": "cn",
                "auto_mount": True,
                "created_by": "qlib_trading_system",
                "version": "1.0.0"
            }
            
            import json
            config_file = data_dir / "config.json"
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_content, f, indent=2, ensure_ascii=False)
            
            self.logger.info("模拟数据结构创建完成")
            return True
            
        except Exception as e:
            self.logger.error(f"模拟数据结构创建失败: {str(e)}")
            return False
    
    def validate_setup(self) -> Dict[str, bool]:
        """验证设置"""
        results = {}
        
        # 检查数据目录
        qlib_data_dir = Path.home() / ".qlib" / "qlib_data" / "cn_data"
        results["data_directory"] = qlib_data_dir.exists()
        
        # 检查配置文件
        config_file = qlib_data_dir / "config.json"
        results["config_file"] = config_file.exists()
        
        # 检查环境变量
        results["environment_variable"] = "QLIB_DATA_PATH" in os.environ
        
        return results
    
    def get_data_download_instructions(self) -> str:
        """获取数据下载说明"""
        instructions = """
Qlib数据下载说明:

1. 安装qlib包:
   pip install qlib

2. 下载中国股市数据:
   python -m qlib.run.get_data qlib_data --target_dir ~/.qlib/qlib_data/cn_data --region cn

3. 或使用在线数据源:
   在代码中使用: qlib.init(provider_uri='https://qlib.blob.core.windows.net/qlib_data/cn_data')

4. 验证数据:
   python -c "import qlib; qlib.init(); from qlib.data import D; print(len(D.instruments()))"
"""
        return instructions


# 全局基础初始化器实例
basic_qlib_initializer = BasicQlibInitializer()