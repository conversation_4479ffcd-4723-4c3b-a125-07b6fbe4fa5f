"""
Qlib框架初始化
"""
import qlib
from pathlib import Path
from typing import Optional

from qlib_trading_system.utils.logging.logger import system_logger
from qlib_trading_system.utils.config.manager import config_manager


class QlibInitializer:
    """Qlib初始化器"""
    
    def __init__(self):
        """初始化"""
        self.logger = system_logger
        self.config = config_manager
        self.is_initialized = False
    
    def initialize(self, 
                  provider_uri: str = "~/.qlib/qlib_data/cn_data",
                  region: str = "cn",
                  auto_mount: bool = True) -> bool:
        """
        初始化qlib
        
        Args:
            provider_uri: 数据提供者URI
            region: 区域设置
            auto_mount: 是否自动挂载数据
            
        Returns:
            bool: 初始化是否成功
        """
        try:
            # 确保数据目录存在
            data_path = Path(provider_uri).expanduser()
            data_path.mkdir(parents=True, exist_ok=True)
            
            # 初始化qlib
            qlib.init(
                provider_uri=provider_uri,
                region=region,
                auto_mount=auto_mount,
                flask_server=False,  # 不启动flask服务器
                logging_level="INFO"
            )
            
            self.is_initialized = True
            self.logger.info(f"Qlib初始化成功，数据路径: {provider_uri}")
            return True
            
        except Exception as e:
            self.logger.error(f"Qlib初始化失败: {str(e)}")
            return False
    
    def check_data_availability(self) -> bool:
        """检查数据可用性"""
        try:
            from qlib.data import D
            
            # 尝试获取一些基础数据来验证
            instruments = D.instruments(market="csi300")
            if len(instruments) > 0:
                self.logger.info(f"数据检查通过，发现 {len(instruments)} 只股票")
                return True
            else:
                self.logger.warning("未发现股票数据")
                return False
                
        except Exception as e:
            self.logger.error(f"数据检查失败: {str(e)}")
            return False
    
    def setup_data_handler(self):
        """设置数据处理器"""
        try:
            from qlib.data.dataset import DatasetH
            from qlib.data.dataset.handler import DataHandlerLP
            
            # 配置数据处理器
            data_handler_config = {
                "start_time": "2020-01-01",
                "end_time": "2024-12-31",
                "fit_start_time": "2020-01-01",
                "fit_end_time": "2023-12-31",
                "instruments": "csi300",
                "infer_processors": [
                    {
                        "class": "RobustZScoreNorm",
                        "kwargs": {"fields_group": "feature", "clip_outlier": True}
                    },
                    {
                        "class": "Fillna",
                        "kwargs": {"fields_group": "feature"}
                    }
                ],
                "learn_processors": [
                    {
                        "class": "DropnaLabel"
                    },
                    {
                        "class": "CSRankNorm",
                        "kwargs": {"fields_group": "label"}
                    }
                ],
                "label": ["Ref($close, -2) / Ref($close, -1) - 1"]
            }
            
            self.logger.info("数据处理器配置完成")
            return data_handler_config
            
        except Exception as e:
            self.logger.error(f"数据处理器设置失败: {str(e)}")
            return None
    
    def get_default_features(self) -> list:
        """获取默认特征配置"""
        features = [
            # 价格特征
            "($close - Ref($close, 1)) / Ref($close, 1)",  # 收益率
            "($high - $low) / $open",  # 振幅
            "$volume / Mean($volume, 30)",  # 成交量比率
            
            # 技术指标
            "RSI($close, 14)",  # RSI
            "MACD($close)",     # MACD
            "BOLL($close, 20)", # 布林带
            
            # 移动平均
            "$close / Mean($close, 5)",   # 5日均线比率
            "$close / Mean($close, 10)",  # 10日均线比率
            "$close / Mean($close, 20)",  # 20日均线比率
            "$close / Mean($close, 60)",  # 60日均线比率
            
            # 动量指标
            "Ref($close, 1) / Ref($close, 5) - 1",   # 5日动量
            "Ref($close, 1) / Ref($close, 10) - 1",  # 10日动量
            "Ref($close, 1) / Ref($close, 20) - 1",  # 20日动量
        ]
        
        return features


# 全局初始化器实例
qlib_initializer = QlibInitializer()