"""
数据库连接工具
"""
import asyncio
from typing import Optional, Dict, Any
from contextlib import asynccontextmanager

import redis
import pymongo
from clickhouse_driver import Client as ClickHouseClient

from qlib_trading_system.utils.logging.logger import system_logger
from qlib_trading_system.utils.config.manager import config_manager


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        """初始化数据库管理器"""
        self.logger = system_logger
        self.config = config_manager.database
        
        # 连接实例
        self._clickhouse_client: Optional[ClickHouseClient] = None
        self._redis_client: Optional[redis.Redis] = None
        self._mongodb_client: Optional[pymongo.MongoClient] = None
    
    @property
    def clickhouse(self) -> ClickHouseClient:
        """获取ClickHouse客户端"""
        if self._clickhouse_client is None:
            try:
                self._clickhouse_client = ClickHouseClient(
                    host=self.config.CLICKHOUSE_HOST,
                    port=self.config.CLICKHOUSE_PORT,
                    user=self.config.CLICKHOUSE_USER,
                    password=self.config.CLICKHOUSE_PASSWORD,
                    database=self.config.CLICKHOUSE_DATABASE
                )
                self.logger.info("ClickHouse连接建立成功")
            except Exception as e:
                self.logger.error(f"ClickHouse连接失败: {str(e)}")
                raise
        
        return self._clickhouse_client
    
    @property
    def redis(self) -> redis.Redis:
        """获取Redis客户端"""
        if self._redis_client is None:
            try:
                self._redis_client = redis.Redis(
                    host=self.config.REDIS_HOST,
                    port=self.config.REDIS_PORT,
                    password=self.config.REDIS_PASSWORD if self.config.REDIS_PASSWORD else None,
                    db=self.config.REDIS_DB,
                    decode_responses=True
                )
                # 测试连接
                self._redis_client.ping()
                self.logger.info("Redis连接建立成功")
            except Exception as e:
                self.logger.error(f"Redis连接失败: {str(e)}")
                raise
        
        return self._redis_client
    
    @property
    def mongodb(self) -> pymongo.MongoClient:
        """获取MongoDB客户端"""
        if self._mongodb_client is None:
            try:
                self._mongodb_client = pymongo.MongoClient(
                    host=self.config.MONGODB_HOST,
                    port=self.config.MONGODB_PORT,
                    username=self.config.MONGODB_USER if self.config.MONGODB_USER else None,
                    password=self.config.MONGODB_PASSWORD if self.config.MONGODB_PASSWORD else None
                )
                # 测试连接
                self._mongodb_client.admin.command('ping')
                self.logger.info("MongoDB连接建立成功")
            except Exception as e:
                self.logger.error(f"MongoDB连接失败: {str(e)}")
                raise
        
        return self._mongodb_client
    
    def test_connections(self) -> Dict[str, bool]:
        """测试所有数据库连接"""
        results = {}
        
        # 测试ClickHouse
        try:
            self.clickhouse.execute("SELECT 1")
            results["clickhouse"] = True
            self.logger.info("ClickHouse连接测试通过")
        except Exception as e:
            results["clickhouse"] = False
            self.logger.error(f"ClickHouse连接测试失败: {str(e)}")
        
        # 测试Redis
        try:
            self.redis.ping()
            results["redis"] = True
            self.logger.info("Redis连接测试通过")
        except Exception as e:
            results["redis"] = False
            self.logger.error(f"Redis连接测试失败: {str(e)}")
        
        # 测试MongoDB
        try:
            self.mongodb.admin.command('ping')
            results["mongodb"] = True
            self.logger.info("MongoDB连接测试通过")
        except Exception as e:
            results["mongodb"] = False
            self.logger.error(f"MongoDB连接测试失败: {str(e)}")
        
        return results
    
    def close_connections(self):
        """关闭所有数据库连接"""
        if self._clickhouse_client:
            self._clickhouse_client.disconnect()
            self.logger.info("ClickHouse连接已关闭")
        
        if self._redis_client:
            self._redis_client.close()
            self.logger.info("Redis连接已关闭")
        
        if self._mongodb_client:
            self._mongodb_client.close()
            self.logger.info("MongoDB连接已关闭")
    
    def initialize_schemas(self):
        """初始化数据库表结构"""
        try:
            # 创建ClickHouse表
            self._create_clickhouse_tables()
            
            # 创建MongoDB集合索引
            self._create_mongodb_indexes()
            
            self.logger.info("数据库表结构初始化完成")
            
        except Exception as e:
            self.logger.error(f"数据库表结构初始化失败: {str(e)}")
            raise
    
    def _create_clickhouse_tables(self):
        """创建ClickHouse表"""
        # 股票价格数据表
        price_table_sql = """
        CREATE TABLE IF NOT EXISTS stock_prices (
            symbol String,
            date Date,
            datetime DateTime,
            open Float64,
            high Float64,
            low Float64,
            close Float64,
            volume UInt64,
            amount Float64
        ) ENGINE = MergeTree()
        PARTITION BY toYYYYMM(date)
        ORDER BY (symbol, datetime)
        """
        
        # 技术指标数据表
        indicators_table_sql = """
        CREATE TABLE IF NOT EXISTS technical_indicators (
            symbol String,
            date Date,
            datetime DateTime,
            indicator_name String,
            indicator_value Float64
        ) ENGINE = MergeTree()
        PARTITION BY toYYYYMM(date)
        ORDER BY (symbol, datetime, indicator_name)
        """
        
        # 交易记录表
        trades_table_sql = """
        CREATE TABLE IF NOT EXISTS trades (
            trade_id String,
            symbol String,
            side String,
            quantity Float64,
            price Float64,
            amount Float64,
            commission Float64,
            datetime DateTime,
            strategy String
        ) ENGINE = MergeTree()
        PARTITION BY toYYYYMM(datetime)
        ORDER BY (datetime, trade_id)
        """
        
        tables = [price_table_sql, indicators_table_sql, trades_table_sql]
        
        for table_sql in tables:
            self.clickhouse.execute(table_sql)
            
        self.logger.info("ClickHouse表创建完成")
    
    def _create_mongodb_indexes(self):
        """创建MongoDB索引"""
        db = self.mongodb[self.config.MONGODB_DATABASE]
        
        # 股票基本信息集合索引
        db.stock_info.create_index("symbol", unique=True)
        db.stock_info.create_index("industry")
        
        # 新闻数据集合索引
        db.news.create_index([("symbol", 1), ("datetime", -1)])
        db.news.create_index("datetime")
        
        # 模型预测结果集合索引
        db.predictions.create_index([("symbol", 1), ("datetime", -1)])
        db.predictions.create_index("model_name")
        
        self.logger.info("MongoDB索引创建完成")


# 全局数据库管理器实例
db_manager = DatabaseManager()