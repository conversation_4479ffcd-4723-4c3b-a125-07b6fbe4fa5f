"""
简化系统初始化
"""
import os
import sys
from pathlib import Path
from typing import Dict, List

from qlib_trading_system.utils.logging.logger import system_logger
from qlib_trading_system.utils.basic_qlib_init import basic_qlib_initializer


class SimpleSystemInitializer:
    """简化系统初始化器"""
    
    def __init__(self):
        """初始化"""
        self.logger = system_logger
    
    def create_project_structure(self) -> bool:
        """创建项目目录结构"""
        self.logger.info("创建项目目录结构...")
        
        directories = [
            # 日志目录
            "logs",
            
            # 数据目录
            "data/raw/stock_prices",
            "data/raw/financial_data", 
            "data/raw/news_data",
            "data/processed/features",
            "data/processed/indicators",
            
            # 模型目录
            "models/stock_selection/checkpoints",
            "models/stock_selection/configs",
            "models/intraday_trading/checkpoints",
            "models/intraday_trading/configs",
            
            # 回测目录
            "backtest_results/stock_selection",
            "backtest_results/intraday_trading",
            
            # 报告目录
            "reports/daily",
            "reports/weekly", 
            "reports/monthly",
            
            # 配置目录
            "configs/strategies",
            "configs/models",
            
            # 临时目录
            "temp/downloads",
            "temp/processing"
        ]
        
        try:
            for directory in directories:
                Path(directory).mkdir(parents=True, exist_ok=True)
                self.logger.debug(f"创建目录: {directory}")
            
            self.logger.info("项目目录结构创建完成")
            return True
            
        except Exception as e:
            self.logger.error(f"目录创建失败: {str(e)}")
            return False
    
    def setup_basic_configs(self) -> bool:
        """设置基础配置文件"""
        self.logger.info("设置基础配置文件...")
        
        try:
            # 创建基础策略配置
            strategy_config = {
                "stock_selection": {
                    "model_type": "lightgbm",
                    "features": [
                        "price_momentum",
                        "volume_ratio", 
                        "rsi",
                        "macd"
                    ],
                    "prediction_horizon": 90,
                    "retrain_frequency": "daily"
                },
                "intraday_trading": {
                    "model_type": "transformer",
                    "timeframe": 30,
                    "signal_threshold": 0.6,
                    "max_positions": 1
                }
            }
            
            import json
            config_file = Path("configs/strategies/default.json")
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(strategy_config, f, indent=2, ensure_ascii=False)
            
            # 创建模型配置
            model_config = {
                "stock_selection_model": {
                    "lightgbm": {
                        "num_leaves": 31,
                        "learning_rate": 0.05,
                        "feature_fraction": 0.9,
                        "bagging_fraction": 0.8,
                        "bagging_freq": 5,
                        "verbose": 0
                    }
                },
                "intraday_model": {
                    "transformer": {
                        "d_model": 512,
                        "nhead": 8,
                        "num_layers": 6,
                        "dropout": 0.1
                    }
                }
            }
            
            model_config_file = Path("configs/models/default.json")
            with open(model_config_file, 'w', encoding='utf-8') as f:
                json.dump(model_config, f, indent=2, ensure_ascii=False)
            
            self.logger.info("基础配置文件创建完成")
            return True
            
        except Exception as e:
            self.logger.error(f"配置文件创建失败: {str(e)}")
            return False
    
    def create_sample_data_files(self) -> bool:
        """创建示例数据文件"""
        self.logger.info("创建示例数据文件...")
        
        try:
            # 创建示例股票列表
            sample_stocks = [
                {"symbol": "000001.SZ", "name": "平安银行", "industry": "银行"},
                {"symbol": "000002.SZ", "name": "万科A", "industry": "房地产"},
                {"symbol": "600000.SH", "name": "浦发银行", "industry": "银行"},
                {"symbol": "600036.SH", "name": "招商银行", "industry": "银行"},
                {"symbol": "000858.SZ", "name": "五粮液", "industry": "食品饮料"}
            ]
            
            import json
            stocks_file = Path("data/raw/sample_stocks.json")
            with open(stocks_file, 'w', encoding='utf-8') as f:
                json.dump(sample_stocks, f, indent=2, ensure_ascii=False)
            
            # 创建示例特征配置
            feature_config = {
                "basic_features": [
                    "close",
                    "volume", 
                    "high",
                    "low",
                    "open"
                ],
                "technical_indicators": [
                    "rsi_14",
                    "macd",
                    "bollinger_bands",
                    "moving_average_5",
                    "moving_average_20"
                ],
                "fundamental_features": [
                    "pe_ratio",
                    "pb_ratio",
                    "roe",
                    "debt_ratio"
                ]
            }
            
            feature_file = Path("data/processed/feature_config.json")
            with open(feature_file, 'w', encoding='utf-8') as f:
                json.dump(feature_config, f, indent=2, ensure_ascii=False)
            
            self.logger.info("示例数据文件创建完成")
            return True
            
        except Exception as e:
            self.logger.error(f"示例数据文件创建失败: {str(e)}")
            return False
    
    def setup_logging_config(self) -> bool:
        """设置日志配置"""
        self.logger.info("设置日志配置...")
        
        try:
            # 创建日志配置文件
            log_config = {
                "version": 1,
                "disable_existing_loggers": False,
                "formatters": {
                    "standard": {
                        "format": "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
                    },
                    "detailed": {
                        "format": "%(asctime)s [%(levelname)s] %(name)s:%(lineno)d: %(message)s"
                    }
                },
                "handlers": {
                    "console": {
                        "class": "logging.StreamHandler",
                        "level": "INFO",
                        "formatter": "standard",
                        "stream": "ext://sys.stdout"
                    },
                    "file": {
                        "class": "logging.handlers.RotatingFileHandler",
                        "level": "DEBUG",
                        "formatter": "detailed",
                        "filename": "logs/trading_system.log",
                        "maxBytes": 10485760,
                        "backupCount": 5
                    }
                },
                "loggers": {
                    "qlib_trading_system": {
                        "level": "DEBUG",
                        "handlers": ["console", "file"],
                        "propagate": False
                    }
                },
                "root": {
                    "level": "INFO",
                    "handlers": ["console"]
                }
            }
            
            import json
            log_config_file = Path("configs/logging.json")
            with open(log_config_file, 'w', encoding='utf-8') as f:
                json.dump(log_config, f, indent=2, ensure_ascii=False)
            
            self.logger.info("日志配置文件创建完成")
            return True
            
        except Exception as e:
            self.logger.error(f"日志配置创建失败: {str(e)}")
            return False
    
    def validate_environment(self) -> Dict[str, bool]:
        """验证环境"""
        results = {}
        
        # 检查Python版本
        import sys
        python_version = sys.version_info
        results["python_version"] = python_version >= (3, 8)
        
        # 检查必要目录
        required_dirs = [
            "logs",
            "data/raw",
            "data/processed", 
            "models/stock_selection",
            "models/intraday_trading",
            "configs"
        ]
        
        results["directories"] = all(Path(d).exists() for d in required_dirs)
        
        # 检查配置文件
        config_files = [
            "configs/strategies/default.json",
            "configs/models/default.json",
            "configs/logging.json"
        ]
        
        results["config_files"] = all(Path(f).exists() for f in config_files)
        
        # 检查环境变量
        results["env_file"] = Path(".env").exists()
        
        return results
    
    def run_simple_initialization(self) -> bool:
        """运行简化初始化"""
        self.logger.info("=" * 60)
        self.logger.info("开始Qlib双AI交易系统简化初始化")
        self.logger.info("=" * 60)
        
        steps = [
            ("创建项目目录结构", self.create_project_structure),
            ("设置基础配置文件", self.setup_basic_configs),
            ("创建示例数据文件", self.create_sample_data_files),
            ("设置日志配置", self.setup_logging_config),
            ("设置基础Qlib环境", basic_qlib_initializer.setup_basic_environment),
            ("创建模拟数据结构", basic_qlib_initializer.create_mock_data_structure)
        ]
        
        for step_name, step_func in steps:
            self.logger.info(f"执行步骤: {step_name}")
            
            try:
                if not step_func():
                    self.logger.error(f"步骤失败: {step_name}")
                    return False
                
                self.logger.info(f"步骤完成: {step_name}")
                
            except Exception as e:
                self.logger.error(f"步骤异常: {step_name}, 错误: {str(e)}")
                return False
        
        # 验证环境
        self.logger.info("验证环境配置...")
        validation_results = self.validate_environment()
        
        for check, result in validation_results.items():
            if result:
                self.logger.info(f"✓ {check} 验证通过")
            else:
                self.logger.warning(f"✗ {check} 验证失败")
        
        # 显示后续步骤
        self.logger.info("=" * 60)
        self.logger.info("初始化完成！后续步骤:")
        self.logger.info("1. 修改.env文件中的配置参数")
        self.logger.info("2. 配置数据源API密钥")
        self.logger.info("3. 安装完整依赖包: pip install -r requirements.txt")
        self.logger.info("4. 下载Qlib数据:")
        self.logger.info("   python -m qlib.run.get_data qlib_data --target_dir ~/.qlib/qlib_data/cn_data --region cn")
        self.logger.info("5. 启动系统: python main.py")
        self.logger.info("=" * 60)
        
        return True


# 全局简化初始化器实例
simple_initializer = SimpleSystemInitializer()