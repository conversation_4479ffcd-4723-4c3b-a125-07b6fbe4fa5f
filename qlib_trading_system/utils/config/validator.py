"""
配置验证和安全检查模块
Configuration Validation and Security Check Module

提供配置数据的验证、安全检查和完整性校验功能
"""

import json
import re
from typing import Dict, Any, List, Optional, Tuple, Union
from dataclasses import dataclass
from enum import Enum
import logging
from pathlib import Path

logger = logging.getLogger(__name__)


class ValidationLevel(Enum):
    """验证级别"""
    BASIC = "basic"        # 基础验证
    STANDARD = "standard"  # 标准验证
    STRICT = "strict"      # 严格验证


class SecurityLevel(Enum):
    """安全级别"""
    LOW = "low"           # 低安全级别
    MEDIUM = "medium"     # 中等安全级别
    HIGH = "high"         # 高安全级别


@dataclass
class ValidationResult:
    """验证结果"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    security_issues: List[str]
    
    def has_errors(self) -> bool:
        """是否有错误"""
        return len(self.errors) > 0
    
    def has_warnings(self) -> bool:
        """是否有警告"""
        return len(self.warnings) > 0
    
    def has_security_issues(self) -> bool:
        """是否有安全问题"""
        return len(self.security_issues) > 0


class ConfigValidator:
    """配置验证器"""
    
    def __init__(self, validation_level: ValidationLevel = ValidationLevel.STANDARD):
        """
        初始化配置验证器
        
        Args:
            validation_level: 验证级别
        """
        self.validation_level = validation_level
        
        # 加载验证规则
        self.validation_rules = self._load_validation_rules()
        self.security_rules = self._load_security_rules()
        
        logger.info(f"配置验证器初始化完成，验证级别: {validation_level.value}")
    
    def _load_validation_rules(self) -> Dict[str, Any]:
        """加载验证规则"""
        return {
            "global": {
                "required_fields": ["system", "logging", "database"],
                "field_types": {
                    "system.environment": str,
                    "system.debug": bool,
                    "logging.level": str,
                    "database.url": str
                },
                "value_ranges": {
                    "logging.level": ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
                }
            },
            "strategy": {
                "required_fields": ["name", "type", "parameters"],
                "field_types": {
                    "name": str,
                    "type": str,
                    "parameters.max_position": (int, float),
                    "parameters.stop_loss": (int, float),
                    "parameters.take_profit": (int, float)
                },
                "value_ranges": {
                    "type": ["stock_selection", "intraday_trading", "hybrid"],
                    "parameters.max_position": (0.0, 1.0),
                    "parameters.stop_loss": (0.0, 0.5),
                    "parameters.take_profit": (0.0, 2.0)
                }
            },
            "stock": {
                "required_fields": ["symbol", "parameters"],
                "field_types": {
                    "symbol": str,
                    "parameters.weight": (int, float),
                    "parameters.max_position": (int, float)
                },
                "value_ranges": {
                    "parameters.weight": (0.0, 1.0),
                    "parameters.max_position": (0.0, 1.0)
                }
            }
        }
    
    def _load_security_rules(self) -> Dict[str, Any]:
        """加载安全规则"""
        return {
            "sensitive_fields": [
                "password", "secret", "key", "token", "credential"
            ],
            "dangerous_values": [
                "eval", "exec", "import", "__", "system", "os."
            ],
            "max_string_length": 1000,
            "max_nested_depth": 10,
            "allowed_file_extensions": [".json", ".yaml", ".yml"],
            "forbidden_patterns": [
                r"<script.*?>.*?</script>",  # XSS防护
                r"javascript:",              # JavaScript协议
                r"data:.*base64",           # Base64数据URI
                r"file://",                 # 文件协议
                r"\$\{.*\}",               # 模板注入
            ]
        }
    
    def validate_config(
        self, 
        config_data: Dict[str, Any], 
        config_type: str,
        security_level: SecurityLevel = SecurityLevel.MEDIUM
    ) -> ValidationResult:
        """
        验证配置数据
        
        Args:
            config_data: 配置数据
            config_type: 配置类型 (global/strategy/stock)
            security_level: 安全级别
            
        Returns:
            验证结果
        """
        errors = []
        warnings = []
        security_issues = []
        
        try:
            # 1. 基础结构验证
            struct_errors = self._validate_structure(config_data, config_type)
            errors.extend(struct_errors)
            
            # 2. 数据类型验证
            type_errors = self._validate_types(config_data, config_type)
            errors.extend(type_errors)
            
            # 3. 值范围验证
            range_errors, range_warnings = self._validate_ranges(config_data, config_type)
            errors.extend(range_errors)
            warnings.extend(range_warnings)
            
            # 4. 安全检查
            if security_level != SecurityLevel.LOW:
                security_issues = self._perform_security_check(config_data, security_level)
            
            # 5. 业务逻辑验证
            if self.validation_level in [ValidationLevel.STANDARD, ValidationLevel.STRICT]:
                logic_errors = self._validate_business_logic(config_data, config_type)
                errors.extend(logic_errors)
            
            # 6. 严格模式额外检查
            if self.validation_level == ValidationLevel.STRICT:
                strict_warnings = self._perform_strict_validation(config_data, config_type)
                warnings.extend(strict_warnings)
            
        except Exception as e:
            errors.append(f"验证过程中发生异常: {str(e)}")
            logger.error(f"配置验证异常: {e}")
        
        # 创建验证结果
        result = ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            security_issues=security_issues
        )
        
        return result
    
    def _validate_structure(self, config_data: Dict[str, Any], config_type: str) -> List[str]:
        """验证配置结构"""
        errors = []
        
        if config_type not in self.validation_rules:
            errors.append(f"不支持的配置类型: {config_type}")
            return errors
        
        rules = self.validation_rules[config_type]
        required_fields = rules.get("required_fields", [])
        
        # 检查必需字段
        for field in required_fields:
            if not self._has_nested_field(config_data, field):
                errors.append(f"缺少必需字段: {field}")
        
        return errors
    
    def _validate_types(self, config_data: Dict[str, Any], config_type: str) -> List[str]:
        """验证数据类型"""
        errors = []
        
        rules = self.validation_rules.get(config_type, {})
        field_types = rules.get("field_types", {})
        
        for field_path, expected_type in field_types.items():
            value = self._get_nested_value(config_data, field_path)
            if value is not None:
                if isinstance(expected_type, tuple):
                    # 多种类型中的一种
                    if not isinstance(value, expected_type):
                        errors.append(f"字段 {field_path} 类型错误，期望: {expected_type}, 实际: {type(value)}")
                else:
                    # 单一类型
                    if not isinstance(value, expected_type):
                        errors.append(f"字段 {field_path} 类型错误，期望: {expected_type}, 实际: {type(value)}")
        
        return errors
    
    def _validate_ranges(self, config_data: Dict[str, Any], config_type: str) -> Tuple[List[str], List[str]]:
        """验证值范围"""
        errors = []
        warnings = []
        
        rules = self.validation_rules.get(config_type, {})
        value_ranges = rules.get("value_ranges", {})
        
        for field_path, valid_range in value_ranges.items():
            value = self._get_nested_value(config_data, field_path)
            if value is not None:
                if isinstance(valid_range, list):
                    # 枚举值
                    if value not in valid_range:
                        errors.append(f"字段 {field_path} 值无效，有效值: {valid_range}, 实际值: {value}")
                elif isinstance(valid_range, tuple) and len(valid_range) == 2:
                    # 数值范围
                    min_val, max_val = valid_range
                    if not (min_val <= value <= max_val):
                        errors.append(f"字段 {field_path} 值超出范围 [{min_val}, {max_val}]，实际值: {value}")
        
        return errors, warnings
    
    def _perform_security_check(self, config_data: Dict[str, Any], security_level: SecurityLevel) -> List[str]:
        """执行安全检查"""
        security_issues = []
        
        # 检查敏感字段
        sensitive_issues = self._check_sensitive_fields(config_data)
        security_issues.extend(sensitive_issues)
        
        # 检查危险值
        dangerous_issues = self._check_dangerous_values(config_data)
        security_issues.extend(dangerous_issues)
        
        # 检查字符串长度
        length_issues = self._check_string_lengths(config_data)
        security_issues.extend(length_issues)
        
        # 检查嵌套深度
        depth_issues = self._check_nested_depth(config_data)
        security_issues.extend(depth_issues)
        
        # 高安全级别额外检查
        if security_level == SecurityLevel.HIGH:
            pattern_issues = self._check_forbidden_patterns(config_data)
            security_issues.extend(pattern_issues)
        
        return security_issues
    
    def _check_sensitive_fields(self, config_data: Dict[str, Any]) -> List[str]:
        """检查敏感字段"""
        issues = []
        sensitive_fields = self.security_rules["sensitive_fields"]
        
        def check_dict(data: Dict[str, Any], path: str = ""):
            for key, value in data.items():
                current_path = f"{path}.{key}" if path else key
                
                # 检查字段名是否包含敏感词
                if any(sensitive in key.lower() for sensitive in sensitive_fields):
                    if isinstance(value, str) and value:
                        issues.append(f"检测到敏感字段 {current_path}，建议加密存储")
                
                # 递归检查嵌套字典
                if isinstance(value, dict):
                    check_dict(value, current_path)
        
        check_dict(config_data)
        return issues

    def _check_dangerous_values(self, config_data: Dict[str, Any]) -> List[str]:
        """检查危险值"""
        issues = []
        dangerous_values = self.security_rules["dangerous_values"]

        def check_value(value: Any, path: str = ""):
            if isinstance(value, str):
                for dangerous in dangerous_values:
                    if dangerous in value.lower():
                        issues.append(f"检测到危险值 '{dangerous}' 在字段 {path}")
            elif isinstance(value, dict):
                for key, val in value.items():
                    current_path = f"{path}.{key}" if path else key
                    check_value(val, current_path)
            elif isinstance(value, list):
                for i, val in enumerate(value):
                    current_path = f"{path}[{i}]" if path else f"[{i}]"
                    check_value(val, current_path)

        check_value(config_data)
        return issues

    def _check_string_lengths(self, config_data: Dict[str, Any]) -> List[str]:
        """检查字符串长度"""
        issues = []
        max_length = self.security_rules["max_string_length"]

        def check_length(value: Any, path: str = ""):
            if isinstance(value, str) and len(value) > max_length:
                issues.append(f"字段 {path} 字符串过长 ({len(value)} > {max_length})")
            elif isinstance(value, dict):
                for key, val in value.items():
                    current_path = f"{path}.{key}" if path else key
                    check_length(val, current_path)
            elif isinstance(value, list):
                for i, val in enumerate(value):
                    current_path = f"{path}[{i}]" if path else f"[{i}]"
                    check_length(val, current_path)

        check_length(config_data)
        return issues

    def _check_nested_depth(self, config_data: Dict[str, Any], max_depth: int = None) -> List[str]:
        """检查嵌套深度"""
        if max_depth is None:
            max_depth = self.security_rules["max_nested_depth"]

        def get_depth(obj: Any) -> int:
            if isinstance(obj, dict):
                return 1 + max((get_depth(v) for v in obj.values()), default=0)
            elif isinstance(obj, list):
                return 1 + max((get_depth(item) for item in obj), default=0)
            else:
                return 0

        depth = get_depth(config_data)
        if depth > max_depth:
            return [f"配置嵌套深度过深 ({depth} > {max_depth})"]

        return []

    def _check_forbidden_patterns(self, config_data: Dict[str, Any]) -> List[str]:
        """检查禁用模式"""
        issues = []
        forbidden_patterns = self.security_rules["forbidden_patterns"]

        def check_patterns(value: Any, path: str = ""):
            if isinstance(value, str):
                for pattern in forbidden_patterns:
                    if re.search(pattern, value, re.IGNORECASE):
                        issues.append(f"检测到禁用模式 '{pattern}' 在字段 {path}")
            elif isinstance(value, dict):
                for key, val in value.items():
                    current_path = f"{path}.{key}" if path else key
                    check_patterns(val, current_path)
            elif isinstance(value, list):
                for i, val in enumerate(value):
                    current_path = f"{path}[{i}]" if path else f"[{i}]"
                    check_patterns(val, current_path)

        check_patterns(config_data)
        return issues

    def _validate_business_logic(self, config_data: Dict[str, Any], config_type: str) -> List[str]:
        """验证业务逻辑"""
        errors = []

        if config_type == "strategy":
            # 策略特定的业务逻辑验证
            errors.extend(self._validate_strategy_logic(config_data))
        elif config_type == "stock":
            # 个股特定的业务逻辑验证
            errors.extend(self._validate_stock_logic(config_data))
        elif config_type == "global":
            # 全局配置的业务逻辑验证
            errors.extend(self._validate_global_logic(config_data))

        return errors

    def _validate_strategy_logic(self, config_data: Dict[str, Any]) -> List[str]:
        """验证策略业务逻辑"""
        errors = []

        # 检查止损和止盈设置
        params = config_data.get("parameters", {})
        stop_loss = params.get("stop_loss")
        take_profit = params.get("take_profit")

        if stop_loss and take_profit:
            if stop_loss >= take_profit:
                errors.append("止损值不应大于等于止盈值")

        # 检查仓位设置
        max_position = params.get("max_position")
        if max_position and max_position > 0.8:
            errors.append("单一策略最大仓位不建议超过80%")

        return errors

    def _validate_stock_logic(self, config_data: Dict[str, Any]) -> List[str]:
        """验证个股业务逻辑"""
        errors = []

        # 检查股票代码格式
        symbol = config_data.get("symbol")
        if symbol:
            if not re.match(r'^[0-9]{6}$', symbol):
                errors.append(f"股票代码格式错误: {symbol}")

        return errors

    def _validate_global_logic(self, config_data: Dict[str, Any]) -> List[str]:
        """验证全局配置业务逻辑"""
        errors = []

        # 检查系统配置一致性
        system = config_data.get("system", {})
        if system.get("debug") and system.get("environment") == "production":
            errors.append("生产环境不应启用调试模式")

        return errors

    def _perform_strict_validation(self, config_data: Dict[str, Any], config_type: str) -> List[str]:
        """执行严格验证"""
        warnings = []

        # 检查未使用的字段
        unused_warnings = self._check_unused_fields(config_data, config_type)
        warnings.extend(unused_warnings)

        # 检查命名规范
        naming_warnings = self._check_naming_conventions(config_data)
        warnings.extend(naming_warnings)

        return warnings

    def _check_unused_fields(self, config_data: Dict[str, Any], config_type: str) -> List[str]:
        """检查未使用的字段"""
        warnings = []

        # 这里可以根据实际使用情况定义已知字段
        known_fields = {
            "global": ["system", "logging", "database", "monitoring"],
            "strategy": ["name", "type", "parameters", "enabled"],
            "stock": ["symbol", "parameters", "enabled"]
        }

        if config_type in known_fields:
            for field in config_data.keys():
                if field not in known_fields[config_type]:
                    warnings.append(f"可能未使用的字段: {field}")

        return warnings

    def _check_naming_conventions(self, config_data: Dict[str, Any]) -> List[str]:
        """检查命名规范"""
        warnings = []

        def check_names(obj: Any, path: str = ""):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    current_path = f"{path}.{key}" if path else key

                    # 检查键名规范（snake_case）
                    if not re.match(r'^[a-z][a-z0-9_]*$', key):
                        warnings.append(f"字段名不符合snake_case规范: {current_path}")

                    check_names(value, current_path)

        check_names(config_data)
        return warnings

    def _has_nested_field(self, data: Dict[str, Any], field_path: str) -> bool:
        """检查是否存在嵌套字段"""
        keys = field_path.split('.')
        current = data

        for key in keys:
            if not isinstance(current, dict) or key not in current:
                return False
            current = current[key]

        return True

    def _get_nested_value(self, data: Dict[str, Any], field_path: str) -> Any:
        """获取嵌套字段值"""
        keys = field_path.split('.')
        current = data

        for key in keys:
            if not isinstance(current, dict) or key not in current:
                return None
            current = current[key]

        return current
