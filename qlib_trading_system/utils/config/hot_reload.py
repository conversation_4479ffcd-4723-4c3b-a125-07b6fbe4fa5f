"""
配置热更新机制
Configuration Hot Reload Mechanism

实现配置的热更新和实时生效功能
"""

import asyncio
import json
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional, Callable, Set
from dataclasses import dataclass
from enum import Enum
import logging
from threading import Thread, Lock
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import hashlib

logger = logging.getLogger(__name__)


class ReloadTrigger(Enum):
    """重载触发器类型"""
    FILE_CHANGE = "file_change"    # 文件变化
    API_REQUEST = "api_request"    # API请求
    SCHEDULED = "scheduled"        # 定时任务
    MANUAL = "manual"             # 手动触发


class ReloadStatus(Enum):
    """重载状态"""
    PENDING = "pending"      # 待处理
    PROCESSING = "processing"  # 处理中
    SUCCESS = "success"      # 成功
    FAILED = "failed"        # 失败


@dataclass
class ReloadEvent:
    """重载事件"""
    event_id: str
    trigger: ReloadTrigger
    config_path: str
    timestamp: datetime
    status: ReloadStatus
    error_message: Optional[str] = None
    duration: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "event_id": self.event_id,
            "trigger": self.trigger.value,
            "config_path": self.config_path,
            "timestamp": self.timestamp.isoformat(),
            "status": self.status.value,
            "error_message": self.error_message,
            "duration": self.duration
        }


class ConfigFileHandler(FileSystemEventHandler):
    """配置文件监控处理器"""
    
    def __init__(self, hot_reload_manager):
        """
        初始化文件处理器
        
        Args:
            hot_reload_manager: 热更新管理器实例
        """
        self.hot_reload_manager = hot_reload_manager
        self.last_modified = {}
        
    def on_modified(self, event):
        """文件修改事件处理"""
        if event.is_directory:
            return
        
        file_path = event.src_path
        
        # 只处理配置文件
        if not self._is_config_file(file_path):
            return
        
        # 防止重复触发
        current_time = time.time()
        if file_path in self.last_modified:
            if current_time - self.last_modified[file_path] < 1.0:  # 1秒内的重复事件忽略
                return
        
        self.last_modified[file_path] = current_time
        
        # 触发热更新
        self.hot_reload_manager.trigger_reload(
            config_path=file_path,
            trigger=ReloadTrigger.FILE_CHANGE
        )
    
    def _is_config_file(self, file_path: str) -> bool:
        """判断是否为配置文件"""
        config_extensions = ['.json', '.yaml', '.yml']
        return any(file_path.endswith(ext) for ext in config_extensions)


class HotReloadManager:
    """配置热更新管理器"""
    
    def __init__(self, config_dirs: List[str], hierarchical_manager=None):
        """
        初始化热更新管理器
        
        Args:
            config_dirs: 监控的配置目录列表
            hierarchical_manager: 分层配置管理器实例
        """
        self.config_dirs = [Path(d) for d in config_dirs]
        self.hierarchical_manager = hierarchical_manager
        
        # 事件存储
        self.reload_events: List[ReloadEvent] = []
        self.event_lock = Lock()
        
        # 回调函数注册
        self.reload_callbacks: Dict[str, List[Callable]] = {}
        
        # 文件监控
        self.observer = Observer()
        self.file_handler = ConfigFileHandler(self)
        
        # 配置缓存和校验和
        self.config_cache: Dict[str, Dict[str, Any]] = {}
        self.config_checksums: Dict[str, str] = {}
        
        # 启动监控
        self._start_monitoring()
        
        logger.info(f"配置热更新管理器初始化完成，监控目录: {config_dirs}")
    
    def _start_monitoring(self):
        """启动文件监控"""
        for config_dir in self.config_dirs:
            if config_dir.exists():
                self.observer.schedule(
                    self.file_handler,
                    str(config_dir),
                    recursive=True
                )
                logger.info(f"开始监控配置目录: {config_dir}")
        
        self.observer.start()
    
    def stop_monitoring(self):
        """停止文件监控"""
        self.observer.stop()
        self.observer.join()
        logger.info("配置文件监控已停止")
    
    def register_callback(self, config_path: str, callback: Callable):
        """
        注册配置更新回调函数
        
        Args:
            config_path: 配置路径
            callback: 回调函数
        """
        if config_path not in self.reload_callbacks:
            self.reload_callbacks[config_path] = []
        
        self.reload_callbacks[config_path].append(callback)
        logger.info(f"已注册配置更新回调: {config_path}")
    
    def unregister_callback(self, config_path: str, callback: Callable):
        """
        取消注册配置更新回调函数
        
        Args:
            config_path: 配置路径
            callback: 回调函数
        """
        if config_path in self.reload_callbacks:
            try:
                self.reload_callbacks[config_path].remove(callback)
                logger.info(f"已取消注册配置更新回调: {config_path}")
            except ValueError:
                logger.warning(f"回调函数未找到: {config_path}")
    
    def trigger_reload(
        self, 
        config_path: str, 
        trigger: ReloadTrigger = ReloadTrigger.MANUAL
    ) -> str:
        """
        触发配置重载
        
        Args:
            config_path: 配置文件路径
            trigger: 触发器类型
            
        Returns:
            事件ID
        """
        event_id = self._generate_event_id()
        
        # 创建重载事件
        reload_event = ReloadEvent(
            event_id=event_id,
            trigger=trigger,
            config_path=config_path,
            timestamp=datetime.now(),
            status=ReloadStatus.PENDING
        )
        
        # 记录事件
        with self.event_lock:
            self.reload_events.append(reload_event)
        
        # 异步处理重载
        Thread(target=self._process_reload, args=(reload_event,)).start()
        
        logger.info(f"触发配置重载: {config_path}, 事件ID: {event_id}")
        return event_id
    
    def _process_reload(self, reload_event: ReloadEvent):
        """处理配置重载"""
        start_time = time.time()
        
        try:
            # 更新状态为处理中
            reload_event.status = ReloadStatus.PROCESSING
            
            # 读取配置文件
            config_path = Path(reload_event.config_path)
            if not config_path.exists():
                raise FileNotFoundError(f"配置文件不存在: {config_path}")
            
            # 加载新配置
            with open(config_path, 'r', encoding='utf-8') as f:
                new_config = json.load(f)
            
            # 计算校验和
            new_checksum = self._calculate_checksum(new_config)
            old_checksum = self.config_checksums.get(str(config_path))
            
            # 检查是否真的有变化
            if old_checksum == new_checksum:
                logger.info(f"配置无变化，跳过重载: {config_path}")
                reload_event.status = ReloadStatus.SUCCESS
                return
            
            # 验证新配置
            if not self._validate_config(new_config, config_path):
                raise ValueError("配置验证失败")
            
            # 更新缓存
            self.config_cache[str(config_path)] = new_config
            self.config_checksums[str(config_path)] = new_checksum
            
            # 调用回调函数
            self._execute_callbacks(str(config_path), new_config)
            
            # 更新分层配置管理器（如果存在）
            if self.hierarchical_manager:
                self._update_hierarchical_config(config_path, new_config)
            
            # 更新状态为成功
            reload_event.status = ReloadStatus.SUCCESS
            
            logger.info(f"配置重载成功: {config_path}")
            
        except Exception as e:
            # 更新状态为失败
            reload_event.status = ReloadStatus.FAILED
            reload_event.error_message = str(e)
            
            logger.error(f"配置重载失败: {reload_event.config_path}, 错误: {e}")
        
        finally:
            # 记录处理时间
            reload_event.duration = time.time() - start_time
    
    def _calculate_checksum(self, config_data: Dict[str, Any]) -> str:
        """计算配置校验和"""
        config_str = json.dumps(config_data, sort_keys=True, ensure_ascii=False)
        return hashlib.md5(config_str.encode('utf-8')).hexdigest()
    
    def _validate_config(self, config_data: Dict[str, Any], config_path: Path) -> bool:
        """验证配置数据"""
        try:
            # 这里可以集成配置验证器
            # 简单验证：确保是有效的JSON结构
            if not isinstance(config_data, dict):
                return False
            
            # 可以添加更多验证逻辑
            return True
            
        except Exception as e:
            logger.error(f"配置验证异常: {e}")
            return False
    
    def _execute_callbacks(self, config_path: str, new_config: Dict[str, Any]):
        """执行回调函数"""
        callbacks = self.reload_callbacks.get(config_path, [])
        
        for callback in callbacks:
            try:
                callback(config_path, new_config)
            except Exception as e:
                logger.error(f"执行回调函数失败: {callback}, 错误: {e}")
    
    def _update_hierarchical_config(self, config_path: Path, new_config: Dict[str, Any]):
        """更新分层配置管理器"""
        try:
            # 根据文件路径确定配置层级和名称
            level, name = self._parse_config_path(config_path)
            
            if level and name:
                # 创建新版本
                version_id = self.hierarchical_manager.create_config_version(
                    level=level,
                    name=name,
                    config_data=new_config,
                    created_by="hot_reload",
                    description="热更新自动创建"
                )
                
                # 激活新版本
                self.hierarchical_manager.activate_version(version_id)
                
                logger.info(f"已更新分层配置: {level.value}/{name}, 版本: {version_id}")
                
        except Exception as e:
            logger.error(f"更新分层配置失败: {e}")
    
    def _parse_config_path(self, config_path: Path):
        """解析配置路径，确定层级和名称"""
        # 这里需要根据实际的目录结构来解析
        # 示例实现
        parts = config_path.parts
        
        if "global" in parts:
            from .hierarchical_manager import ConfigLevel
            return ConfigLevel.GLOBAL, config_path.stem
        elif "strategy" in parts:
            from .hierarchical_manager import ConfigLevel
            return ConfigLevel.STRATEGY, config_path.stem
        elif "stock" in parts:
            from .hierarchical_manager import ConfigLevel
            return ConfigLevel.STOCK, config_path.stem
        
        return None, None
    
    def _generate_event_id(self) -> str:
        """生成事件ID"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        return f"reload_{timestamp}"
    
    def get_reload_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取重载历史"""
        with self.event_lock:
            recent_events = self.reload_events[-limit:] if limit > 0 else self.reload_events
            return [event.to_dict() for event in reversed(recent_events)]
    
    def get_reload_statistics(self) -> Dict[str, Any]:
        """获取重载统计信息"""
        with self.event_lock:
            total_events = len(self.reload_events)
            success_events = sum(1 for e in self.reload_events if e.status == ReloadStatus.SUCCESS)
            failed_events = sum(1 for e in self.reload_events if e.status == ReloadStatus.FAILED)
            
            return {
                "total_events": total_events,
                "success_events": success_events,
                "failed_events": failed_events,
                "success_rate": success_events / total_events if total_events > 0 else 0,
                "monitored_directories": [str(d) for d in self.config_dirs],
                "registered_callbacks": len(self.reload_callbacks)
            }
