"""
分层配置管理系统
Hierarchical Configuration Management System

实现全局、策略、个股三层配置管理
"""

import json
import os
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass, asdict
from enum import Enum
import logging
from threading import Lock
import hashlib

logger = logging.getLogger(__name__)


class ConfigLevel(Enum):
    """配置层级枚举"""
    GLOBAL = "global"      # 全局配置
    STRATEGY = "strategy"  # 策略配置
    STOCK = "stock"       # 个股配置


class ConfigStatus(Enum):
    """配置状态枚举"""
    ACTIVE = "active"      # 活跃状态
    INACTIVE = "inactive"  # 非活跃状态
    PENDING = "pending"    # 待生效状态
    ARCHIVED = "archived"  # 已归档状态


@dataclass
class ConfigVersion:
    """配置版本信息"""
    version_id: str
    level: ConfigLevel
    name: str  # 配置名称（策略名或股票代码）
    config_data: Dict[str, Any]
    created_time: datetime
    created_by: str
    description: str
    status: ConfigStatus
    checksum: str
    parent_version: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['created_time'] = self.created_time.isoformat()
        data['level'] = self.level.value
        data['status'] = self.status.value
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ConfigVersion':
        """从字典创建实例"""
        data['created_time'] = datetime.fromisoformat(data['created_time'])
        data['level'] = ConfigLevel(data['level'])
        data['status'] = ConfigStatus(data['status'])
        return cls(**data)


class HierarchicalConfigManager:
    """分层配置管理器"""
    
    def __init__(self, config_root: str = "config/hierarchical"):
        """
        初始化分层配置管理器
        
        Args:
            config_root: 配置根目录
        """
        self.config_root = Path(config_root)
        self.config_root.mkdir(parents=True, exist_ok=True)
        
        # 各层级配置目录
        self.global_dir = self.config_root / "global"
        self.strategy_dir = self.config_root / "strategy"
        self.stock_dir = self.config_root / "stock"
        self.versions_dir = self.config_root / "versions"
        self.backup_dir = self.config_root / "backup"
        
        # 创建目录结构
        for directory in [self.global_dir, self.strategy_dir, 
                         self.stock_dir, self.versions_dir, self.backup_dir]:
            directory.mkdir(parents=True, exist_ok=True)
        
        # 版本注册表
        self.version_registry: Dict[str, ConfigVersion] = {}
        self.active_versions: Dict[str, str] = {}  # key: level_name, value: version_id
        
        # 线程锁
        self._lock = Lock()
        
        # 加载现有配置
        self._load_version_registry()
        self._load_active_versions()
        
        logger.info(f"分层配置管理器初始化完成，配置根目录: {self.config_root}")
    
    def _generate_version_id(self, level: ConfigLevel, name: str) -> str:
        """生成版本ID"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"{level.value}_{name}_{timestamp}"
    
    def _calculate_checksum(self, config_data: Dict[str, Any]) -> str:
        """计算配置数据校验和"""
        config_str = json.dumps(config_data, sort_keys=True, ensure_ascii=False)
        return hashlib.md5(config_str.encode('utf-8')).hexdigest()
    
    def _get_config_key(self, level: ConfigLevel, name: str) -> str:
        """获取配置键"""
        return f"{level.value}_{name}"
    
    def _save_version_registry(self):
        """保存版本注册表"""
        registry_file = self.config_root / "version_registry.json"
        registry_data = {
            version_id: version.to_dict() 
            for version_id, version in self.version_registry.items()
        }
        
        with open(registry_file, 'w', encoding='utf-8') as f:
            json.dump(registry_data, f, indent=2, ensure_ascii=False)
    
    def _load_version_registry(self):
        """加载版本注册表"""
        registry_file = self.config_root / "version_registry.json"
        if registry_file.exists():
            try:
                with open(registry_file, 'r', encoding='utf-8') as f:
                    registry_data = json.load(f)
                
                self.version_registry = {
                    version_id: ConfigVersion.from_dict(version_data)
                    for version_id, version_data in registry_data.items()
                }
                
                logger.info(f"已加载 {len(self.version_registry)} 个配置版本")
                
            except Exception as e:
                logger.error(f"加载版本注册表失败: {e}")
                self.version_registry = {}
    
    def _save_active_versions(self):
        """保存活跃版本信息"""
        active_file = self.config_root / "active_versions.json"
        with open(active_file, 'w', encoding='utf-8') as f:
            json.dump(self.active_versions, f, indent=2, ensure_ascii=False)
    
    def _load_active_versions(self):
        """加载活跃版本信息"""
        active_file = self.config_root / "active_versions.json"
        if active_file.exists():
            try:
                with open(active_file, 'r', encoding='utf-8') as f:
                    self.active_versions = json.load(f)
                
                logger.info(f"已加载 {len(self.active_versions)} 个活跃配置版本")

            except Exception as e:
                logger.error(f"加载活跃版本信息失败: {e}")
                self.active_versions = {}

    def create_config_version(
        self,
        level: ConfigLevel,
        name: str,
        config_data: Dict[str, Any],
        created_by: str,
        description: str = "",
        parent_version: Optional[str] = None
    ) -> str:
        """
        创建新的配置版本

        Args:
            level: 配置层级
            name: 配置名称
            config_data: 配置数据
            created_by: 创建者
            description: 描述信息
            parent_version: 父版本ID

        Returns:
            新版本ID
        """
        with self._lock:
            try:
                # 生成版本ID
                version_id = self._generate_version_id(level, name)

                # 计算校验和
                checksum = self._calculate_checksum(config_data)

                # 创建版本对象
                version = ConfigVersion(
                    version_id=version_id,
                    level=level,
                    name=name,
                    config_data=config_data.copy(),
                    created_time=datetime.now(),
                    created_by=created_by,
                    description=description,
                    status=ConfigStatus.PENDING,
                    checksum=checksum,
                    parent_version=parent_version
                )

                # 保存版本文件
                version_file = self.versions_dir / f"{version_id}.json"
                with open(version_file, 'w', encoding='utf-8') as f:
                    json.dump(version.to_dict(), f, indent=2, ensure_ascii=False)

                # 注册版本
                self.version_registry[version_id] = version
                self._save_version_registry()

                logger.info(f"创建配置版本成功: {version_id}")
                return version_id

            except Exception as e:
                logger.error(f"创建配置版本失败: {e}")
                raise

    def activate_version(self, version_id: str) -> bool:
        """
        激活指定版本

        Args:
            version_id: 版本ID

        Returns:
            是否成功激活
        """
        with self._lock:
            try:
                if version_id not in self.version_registry:
                    raise ValueError(f"版本不存在: {version_id}")

                version = self.version_registry[version_id]
                config_key = self._get_config_key(version.level, version.name)

                # 停用当前活跃版本
                current_version_id = self.active_versions.get(config_key)
                if current_version_id and current_version_id in self.version_registry:
                    self.version_registry[current_version_id].status = ConfigStatus.INACTIVE

                # 激活新版本
                version.status = ConfigStatus.ACTIVE
                self.active_versions[config_key] = version_id

                # 保存配置文件到对应目录
                self._save_config_to_level_dir(version)

                # 保存状态
                self._save_version_registry()
                self._save_active_versions()

                logger.info(f"版本已激活: {version_id}")
                return True

            except Exception as e:
                logger.error(f"激活版本失败: {e}")
                return False

    def _save_config_to_level_dir(self, version: ConfigVersion):
        """保存配置到对应层级目录"""
        if version.level == ConfigLevel.GLOBAL:
            config_file = self.global_dir / f"{version.name}.json"
        elif version.level == ConfigLevel.STRATEGY:
            config_file = self.strategy_dir / f"{version.name}.json"
        elif version.level == ConfigLevel.STOCK:
            config_file = self.stock_dir / f"{version.name}.json"
        else:
            raise ValueError(f"不支持的配置层级: {version.level}")

        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(version.config_data, f, indent=2, ensure_ascii=False)

    def get_active_config(self, level: ConfigLevel, name: str) -> Optional[Dict[str, Any]]:
        """
        获取活跃配置

        Args:
            level: 配置层级
            name: 配置名称

        Returns:
            配置数据，如果不存在返回None
        """
        config_key = self._get_config_key(level, name)
        version_id = self.active_versions.get(config_key)

        if version_id and version_id in self.version_registry:
            return self.version_registry[version_id].config_data.copy()

        return None

    def get_merged_config(self, strategy_name: str, stock_code: str = None) -> Dict[str, Any]:
        """
        获取合并后的配置（全局 -> 策略 -> 个股）

        Args:
            strategy_name: 策略名称
            stock_code: 股票代码（可选）

        Returns:
            合并后的配置
        """
        merged_config = {}

        # 1. 加载全局配置
        global_config = self.get_active_config(ConfigLevel.GLOBAL, "default")
        if global_config:
            merged_config.update(global_config)

        # 2. 加载策略配置
        strategy_config = self.get_active_config(ConfigLevel.STRATEGY, strategy_name)
        if strategy_config:
            merged_config.update(strategy_config)

        # 3. 加载个股配置（如果指定）
        if stock_code:
            stock_config = self.get_active_config(ConfigLevel.STOCK, stock_code)
            if stock_config:
                merged_config.update(stock_config)

        return merged_config

    def rollback_to_version(self, version_id: str) -> bool:
        """
        回滚到指定版本

        Args:
            version_id: 目标版本ID

        Returns:
            是否成功回滚
        """
        with self._lock:
            try:
                if version_id not in self.version_registry:
                    raise ValueError(f"版本不存在: {version_id}")

                # 创建备份
                self._create_backup()

                # 激活目标版本
                success = self.activate_version(version_id)

                if success:
                    logger.info(f"成功回滚到版本: {version_id}")
                else:
                    logger.error(f"回滚失败: {version_id}")

                return success

            except Exception as e:
                logger.error(f"回滚版本失败: {e}")
                return False

    def _create_backup(self):
        """创建当前配置的备份"""
        backup_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_subdir = self.backup_dir / backup_timestamp
        backup_subdir.mkdir(exist_ok=True)

        # 备份各层级配置
        for level_dir in [self.global_dir, self.strategy_dir, self.stock_dir]:
            if level_dir.exists():
                level_backup_dir = backup_subdir / level_dir.name
                shutil.copytree(level_dir, level_backup_dir, dirs_exist_ok=True)

        # 备份版本注册表和活跃版本信息
        shutil.copy2(self.config_root / "version_registry.json", backup_subdir)
        shutil.copy2(self.config_root / "active_versions.json", backup_subdir)

        logger.info(f"配置备份已创建: {backup_subdir}")

    def list_versions(
        self,
        level: Optional[ConfigLevel] = None,
        name: Optional[str] = None,
        status: Optional[ConfigStatus] = None
    ) -> List[ConfigVersion]:
        """
        列出配置版本

        Args:
            level: 过滤层级
            name: 过滤名称
            status: 过滤状态

        Returns:
            版本列表
        """
        versions = list(self.version_registry.values())

        # 应用过滤条件
        if level:
            versions = [v for v in versions if v.level == level]

        if name:
            versions = [v for v in versions if v.name == name]

        if status:
            versions = [v for v in versions if v.status == status]

        # 按创建时间倒序排列
        versions.sort(key=lambda v: v.created_time, reverse=True)

        return versions

    def delete_version(self, version_id: str) -> bool:
        """
        删除指定版本

        Args:
            version_id: 版本ID

        Returns:
            是否成功删除
        """
        with self._lock:
            try:
                if version_id not in self.version_registry:
                    raise ValueError(f"版本不存在: {version_id}")

                version = self.version_registry[version_id]

                # 检查是否为活跃版本
                config_key = self._get_config_key(version.level, version.name)
                if self.active_versions.get(config_key) == version_id:
                    raise ValueError("不能删除活跃版本")

                # 删除版本文件
                version_file = self.versions_dir / f"{version_id}.json"
                if version_file.exists():
                    version_file.unlink()

                # 从注册表中移除
                del self.version_registry[version_id]

                # 更新版本树
                if version.parent_version in self.version_tree:
                    if version_id in self.version_tree[version.parent_version]:
                        self.version_tree[version.parent_version].remove(version_id)

                # 保存注册表
                self._save_version_registry()

                logger.info(f"版本已删除: {version_id}")
                return True

            except Exception as e:
                logger.error(f"删除版本失败: {e}")
                return False

    def archive_version(self, version_id: str) -> bool:
        """
        归档指定版本

        Args:
            version_id: 版本ID

        Returns:
            是否成功归档
        """
        with self._lock:
            try:
                if version_id not in self.version_registry:
                    raise ValueError(f"版本不存在: {version_id}")

                version = self.version_registry[version_id]

                # 检查是否为活跃版本
                config_key = self._get_config_key(version.level, version.name)
                if self.active_versions.get(config_key) == version_id:
                    raise ValueError("不能归档活跃版本")

                # 更新状态为已归档
                version.status = ConfigStatus.ARCHIVED

                # 保存注册表
                self._save_version_registry()

                logger.info(f"版本已归档: {version_id}")
                return True

            except Exception as e:
                logger.error(f"归档版本失败: {e}")
                return False

    def get_version_info(self, version_id: str) -> Optional[ConfigVersion]:
        """
        获取版本信息

        Args:
            version_id: 版本ID

        Returns:
            版本信息，如果不存在返回None
        """
        return self.version_registry.get(version_id)

    def cleanup_old_versions(self, keep_count: int = 10) -> int:
        """
        清理旧版本，保留指定数量的最新版本

        Args:
            keep_count: 保留的版本数量

        Returns:
            清理的版本数量
        """
        cleaned_count = 0

        # 按配置分组
        config_groups = {}
        for version_id, version in self.version_registry.items():
            config_key = self._get_config_key(version.level, version.name)
            if config_key not in config_groups:
                config_groups[config_key] = []
            config_groups[config_key].append(version)

        # 对每个配置组进行清理
        for config_key, versions in config_groups.items():
            # 按创建时间排序
            versions.sort(key=lambda v: v.created_time, reverse=True)

            # 跳过活跃版本和需要保留的版本
            active_version_id = self.active_versions.get(config_key)
            versions_to_clean = []

            for i, version in enumerate(versions):
                if i >= keep_count and version.version_id != active_version_id:
                    if version.status != ConfigStatus.ARCHIVED:
                        versions_to_clean.append(version.version_id)

            # 删除旧版本
            for version_id in versions_to_clean:
                if self.delete_version(version_id):
                    cleaned_count += 1

        logger.info(f"清理了 {cleaned_count} 个旧版本")
        return cleaned_count
