"""
配置管理系统主入口
Configuration Management System Main Entry

整合分层配置管理、版本控制、验证和热更新功能
"""

import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass

from .hierarchical_manager import HierarchicalConfigManager, ConfigLevel, ConfigStatus
from .validator import ConfigValidator, ValidationLevel, SecurityLevel, ValidationResult
from .version_control import ConfigVersionControl, VersionOperation
from .hot_reload import HotReloadManager, ReloadTrigger

logger = logging.getLogger(__name__)


@dataclass
class ConfigSystemStats:
    """配置系统统计信息"""
    total_configs: int
    active_configs: int
    total_versions: int
    validation_errors: int
    reload_events: int
    last_update_time: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "total_configs": self.total_configs,
            "active_configs": self.active_configs,
            "total_versions": self.total_versions,
            "validation_errors": self.validation_errors,
            "reload_events": self.reload_events,
            "last_update_time": self.last_update_time.isoformat()
        }


class ConfigManagementSystem:
    """配置管理系统"""
    
    def __init__(
        self,
        config_root: str = "config",
        validation_level: ValidationLevel = ValidationLevel.STANDARD,
        security_level: SecurityLevel = SecurityLevel.MEDIUM,
        enable_hot_reload: bool = True
    ):
        """
        初始化配置管理系统
        
        Args:
            config_root: 配置根目录
            validation_level: 验证级别
            security_level: 安全级别
            enable_hot_reload: 是否启用热更新
        """
        self.config_root = Path(config_root)
        self.config_root.mkdir(parents=True, exist_ok=True)
        
        # 初始化各个组件
        self.hierarchical_manager = HierarchicalConfigManager(
            str(self.config_root / "hierarchical")
        )
        
        self.validator = ConfigValidator(validation_level)
        
        self.version_control = ConfigVersionControl(
            str(self.config_root / "repository")
        )
        
        # 热更新管理器（可选）
        self.hot_reload_manager = None
        if enable_hot_reload:
            monitor_dirs = [
                str(self.config_root / "hierarchical" / "global"),
                str(self.config_root / "hierarchical" / "strategy"),
                str(self.config_root / "hierarchical" / "stock")
            ]
            self.hot_reload_manager = HotReloadManager(
                monitor_dirs, self.hierarchical_manager
            )
        
        self.security_level = security_level
        self.validation_level = validation_level
        
        # 统计信息
        self.stats = ConfigSystemStats(
            total_configs=0,
            active_configs=0,
            total_versions=0,
            validation_errors=0,
            reload_events=0,
            last_update_time=datetime.now()
        )
        
        # 更新统计信息
        self._update_stats()
        
        logger.info("配置管理系统初始化完成")
    
    def create_config(
        self,
        level: ConfigLevel,
        name: str,
        config_data: Dict[str, Any],
        created_by: str,
        description: str = "",
        auto_activate: bool = True,
        validate: bool = True
    ) -> Optional[str]:
        """
        创建新配置
        
        Args:
            level: 配置层级
            name: 配置名称
            config_data: 配置数据
            created_by: 创建者
            description: 描述信息
            auto_activate: 是否自动激活
            validate: 是否验证配置
            
        Returns:
            版本ID，失败返回None
        """
        try:
            # 1. 验证配置（如果启用）
            if validate:
                validation_result = self.validate_config(config_data, level.value)
                if not validation_result.is_valid:
                    logger.error(f"配置验证失败: {validation_result.errors}")
                    self.stats.validation_errors += 1
                    return None
                
                # 记录警告
                if validation_result.has_warnings():
                    logger.warning(f"配置验证警告: {validation_result.warnings}")
                
                # 记录安全问题
                if validation_result.has_security_issues():
                    logger.warning(f"配置安全问题: {validation_result.security_issues}")
            
            # 2. 创建分层配置版本
            hierarchical_version_id = self.hierarchical_manager.create_config_version(
                level=level,
                name=name,
                config_data=config_data,
                created_by=created_by,
                description=description
            )
            
            # 3. 提交到版本控制
            version_control_id = self.version_control.commit_version(
                config_type=level.value,
                config_name=name,
                config_data=config_data,
                created_by=created_by,
                description=description,
                operation=VersionOperation.CREATE
            )
            
            # 4. 自动激活（如果启用）
            if auto_activate:
                success = self.hierarchical_manager.activate_version(hierarchical_version_id)
                if not success:
                    logger.error(f"激活配置失败: {hierarchical_version_id}")
                    return None
            
            # 5. 更新统计信息
            self._update_stats()
            
            logger.info(f"配置创建成功: {level.value}/{name}, 版本: {hierarchical_version_id}")
            return hierarchical_version_id
            
        except Exception as e:
            logger.error(f"创建配置失败: {e}")
            return None
    
    def update_config(
        self,
        level: ConfigLevel,
        name: str,
        config_data: Dict[str, Any],
        created_by: str,
        description: str = "",
        validate: bool = True
    ) -> Optional[str]:
        """
        更新配置
        
        Args:
            level: 配置层级
            name: 配置名称
            config_data: 新配置数据
            created_by: 更新者
            description: 描述信息
            validate: 是否验证配置
            
        Returns:
            新版本ID，失败返回None
        """
        try:
            # 获取当前版本作为父版本
            current_config = self.hierarchical_manager.get_active_config(level, name)
            if not current_config:
                logger.warning(f"配置不存在，将创建新配置: {level.value}/{name}")
                return self.create_config(level, name, config_data, created_by, description, validate=validate)
            
            # 验证配置（如果启用）
            if validate:
                validation_result = self.validate_config(config_data, level.value)
                if not validation_result.is_valid:
                    logger.error(f"配置验证失败: {validation_result.errors}")
                    self.stats.validation_errors += 1
                    return None
            
            # 创建新版本
            new_version_id = self.hierarchical_manager.create_config_version(
                level=level,
                name=name,
                config_data=config_data,
                created_by=created_by,
                description=description or "配置更新"
            )
            
            # 提交到版本控制
            self.version_control.commit_version(
                config_type=level.value,
                config_name=name,
                config_data=config_data,
                created_by=created_by,
                description=description or "配置更新",
                operation=VersionOperation.UPDATE
            )
            
            # 激活新版本
            success = self.hierarchical_manager.activate_version(new_version_id)
            if not success:
                logger.error(f"激活新版本失败: {new_version_id}")
                return None
            
            # 更新统计信息
            self._update_stats()
            
            logger.info(f"配置更新成功: {level.value}/{name}, 新版本: {new_version_id}")
            return new_version_id
            
        except Exception as e:
            logger.error(f"更新配置失败: {e}")
            return None
    
    def get_config(
        self,
        level: ConfigLevel,
        name: str,
        merged: bool = False,
        strategy_name: str = None,
        stock_code: str = None
    ) -> Optional[Dict[str, Any]]:
        """
        获取配置
        
        Args:
            level: 配置层级
            name: 配置名称
            merged: 是否获取合并后的配置
            strategy_name: 策略名称（合并模式需要）
            stock_code: 股票代码（合并模式需要）
            
        Returns:
            配置数据，不存在返回None
        """
        try:
            if merged:
                # 获取合并后的配置
                if not strategy_name:
                    raise ValueError("合并模式需要指定策略名称")
                
                return self.hierarchical_manager.get_merged_config(
                    strategy_name=strategy_name,
                    stock_code=stock_code
                )
            else:
                # 获取单层配置
                return self.hierarchical_manager.get_active_config(level, name)
                
        except Exception as e:
            logger.error(f"获取配置失败: {e}")
            return None
    
    def validate_config(
        self,
        config_data: Dict[str, Any],
        config_type: str
    ) -> ValidationResult:
        """
        验证配置
        
        Args:
            config_data: 配置数据
            config_type: 配置类型
            
        Returns:
            验证结果
        """
        return self.validator.validate_config(
            config_data=config_data,
            config_type=config_type,
            security_level=self.security_level
        )
    
    def rollback_config(
        self,
        level: ConfigLevel,
        name: str,
        target_version_id: str,
        created_by: str
    ) -> bool:
        """
        回滚配置到指定版本
        
        Args:
            level: 配置层级
            name: 配置名称
            target_version_id: 目标版本ID
            created_by: 操作者
            
        Returns:
            是否成功回滚
        """
        try:
            # 使用分层管理器回滚
            success = self.hierarchical_manager.rollback_to_version(target_version_id)
            
            if success:
                # 在版本控制中记录回滚操作
                current_config = self.hierarchical_manager.get_active_config(level, name)
                if current_config:
                    self.version_control.commit_version(
                        config_type=level.value,
                        config_name=name,
                        config_data=current_config,
                        created_by=created_by,
                        description=f"回滚到版本 {target_version_id}",
                        operation=VersionOperation.ROLLBACK,
                        parent_version=target_version_id
                    )
                
                # 更新统计信息
                self._update_stats()
                
                logger.info(f"配置回滚成功: {level.value}/{name} -> {target_version_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"配置回滚失败: {e}")
            return False
    
    def list_config_versions(
        self,
        level: Optional[ConfigLevel] = None,
        name: Optional[str] = None,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """
        列出配置版本
        
        Args:
            level: 过滤层级
            name: 过滤名称
            limit: 限制数量
            
        Returns:
            版本列表
        """
        try:
            # 从分层管理器获取版本
            versions = self.hierarchical_manager.list_versions(level, name)
            
            # 转换为字典格式
            result = []
            for version in versions[:limit]:
                result.append(version.to_dict())
            
            return result
            
        except Exception as e:
            logger.error(f"列出配置版本失败: {e}")
            return []
    
    def register_reload_callback(self, config_path: str, callback: Callable):
        """
        注册配置重载回调
        
        Args:
            config_path: 配置路径
            callback: 回调函数
        """
        if self.hot_reload_manager:
            self.hot_reload_manager.register_callback(config_path, callback)
        else:
            logger.warning("热更新管理器未启用")
    
    def trigger_manual_reload(self, config_path: str) -> Optional[str]:
        """
        手动触发配置重载
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            事件ID，失败返回None
        """
        if self.hot_reload_manager:
            return self.hot_reload_manager.trigger_reload(
                config_path=config_path,
                trigger=ReloadTrigger.MANUAL
            )
        else:
            logger.warning("热更新管理器未启用")
            return None
    
    def get_system_stats(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        self._update_stats()
        return self.stats.to_dict()
    
    def _update_stats(self):
        """更新统计信息"""
        try:
            # 统计分层配置
            total_configs = len(self.hierarchical_manager.version_registry)
            active_configs = len(self.hierarchical_manager.active_versions)
            
            # 统计版本控制
            total_versions = len(self.version_control.version_registry)
            
            # 统计热更新事件
            reload_events = 0
            if self.hot_reload_manager:
                reload_events = len(self.hot_reload_manager.reload_events)
            
            # 更新统计信息
            self.stats.total_configs = total_configs
            self.stats.active_configs = active_configs
            self.stats.total_versions = total_versions
            self.stats.reload_events = reload_events
            self.stats.last_update_time = datetime.now()
            
        except Exception as e:
            logger.error(f"更新统计信息失败: {e}")
    
    def export_config(
        self,
        output_path: str,
        level: Optional[ConfigLevel] = None,
        name: Optional[str] = None,
        include_history: bool = False
    ) -> bool:
        """
        导出配置
        
        Args:
            output_path: 输出路径
            level: 过滤层级
            name: 过滤名称
            include_history: 是否包含历史版本
            
        Returns:
            是否成功导出
        """
        try:
            export_data = {
                "export_time": datetime.now().isoformat(),
                "configs": {},
                "metadata": {
                    "validation_level": self.validation_level.value,
                    "security_level": self.security_level.value
                }
            }
            
            # 导出活跃配置
            for config_key, version_id in self.hierarchical_manager.active_versions.items():
                if version_id in self.hierarchical_manager.version_registry:
                    version = self.hierarchical_manager.version_registry[version_id]
                    
                    # 应用过滤条件
                    if level and version.level != level:
                        continue
                    if name and version.name != name:
                        continue
                    
                    export_data["configs"][config_key] = {
                        "version_info": version.to_dict(),
                        "config_data": version.config_data
                    }
                    
                    # 包含历史版本（如果启用）
                    if include_history:
                        history = self.hierarchical_manager.list_versions(
                            level=version.level,
                            name=version.name
                        )
                        export_data["configs"][config_key]["history"] = [
                            h.to_dict() for h in history
                        ]
            
            # 保存到文件
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"配置导出成功: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"配置导出失败: {e}")
            return False
    
    def shutdown(self):
        """关闭配置管理系统"""
        try:
            if self.hot_reload_manager:
                self.hot_reload_manager.stop_monitoring()
            
            logger.info("配置管理系统已关闭")
            
        except Exception as e:
            logger.error(f"关闭配置管理系统失败: {e}")


# 全局配置管理系统实例
config_management_system = None

def get_config_management_system(**kwargs) -> ConfigManagementSystem:
    """获取全局配置管理系统实例"""
    global config_management_system
    
    if config_management_system is None:
        config_management_system = ConfigManagementSystem(**kwargs)
    
    return config_management_system
