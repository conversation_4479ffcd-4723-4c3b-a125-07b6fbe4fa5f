"""
配置管理器
"""
import os
import json
from typing import Dict, Any, Optional
from pathlib import Path
from dotenv import load_dotenv

from config.settings import (
    trading_config,
    data_source_config, 
    model_config,
    system_config
)
from config.database import db_config


class ConfigManager:
    """配置管理器"""
    
    def __init__(self):
        """初始化配置管理器"""
        # 加载环境变量
        load_dotenv()
        
        # 配置实例
        self.trading = trading_config
        self.data_source = data_source_config
        self.model = model_config
        self.system = system_config
        self.database = db_config
        
        # 创建必要的目录
        self._create_directories()
    
    def _create_directories(self):
        """创建必要的目录"""
        directories = [
            "logs",
            "data/raw",
            "data/processed", 
            "models/stock_selection",
            "models/intraday_trading",
            "backtest_results",
            "reports"
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    def get_capital_config(self) -> Dict[str, Any]:
        """获取资金配置"""
        return {
            "mode": self.trading.CAPITAL_MODE,
            "total_capital": self.trading.TOTAL_CAPITAL,
            "max_stocks": self.trading.MAX_STOCKS,
            "base_position_ratio": self.trading.BASE_POSITION_RATIO,
            "t_position_ratio": self.trading.T_POSITION_RATIO,
            "cash_reserve_ratio": self.trading.CASH_RESERVE_RATIO
        }
    
    def get_risk_config(self) -> Dict[str, Any]:
        """获取风险配置"""
        return {
            "max_single_loss": self.trading.MAX_SINGLE_LOSS,
            "max_daily_loss": self.trading.MAX_DAILY_LOSS,
            "max_drawdown": self.trading.MAX_DRAWDOWN
        }
    
    def get_data_source_config(self) -> Dict[str, Any]:
        """获取数据源配置"""
        return {
            "primary": self.data_source.PRIMARY_DATA_SOURCE,
            "itick": {
                "api_key": self.data_source.ITICK_API_KEY,
                "secret_key": self.data_source.ITICK_SECRET_KEY
            },
            "joinquant": {
                "username": self.data_source.JOINQUANT_USERNAME,
                "password": self.data_source.JOINQUANT_PASSWORD
            },
            "ricequant": {
                "api_key": self.data_source.RICEQUANT_API_KEY,
                "secret_key": self.data_source.RICEQUANT_SECRET_KEY
            }
        }
    
    def get_database_config(self) -> Dict[str, str]:
        """获取数据库配置"""
        return {
            "clickhouse_url": self.database.clickhouse_url,
            "redis_url": self.database.redis_url,
            "mongodb_url": self.database.mongodb_url
        }
    
    def save_config_to_file(self, filepath: str):
        """保存配置到文件"""
        config_data = {
            "trading": self.get_capital_config(),
            "risk": self.get_risk_config(),
            "data_source": self.get_data_source_config(),
            "database": self.get_database_config(),
            "system": {
                "environment": self.system.ENVIRONMENT,
                "debug": self.system.DEBUG,
                "log_level": self.system.LOG_LEVEL
            }
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)
    
    def validate_config(self) -> Dict[str, bool]:
        """验证配置有效性"""
        validation_results = {}
        
        # 验证资金配置
        validation_results["capital_ratios"] = (
            self.trading.BASE_POSITION_RATIO + 
            self.trading.T_POSITION_RATIO + 
            self.trading.CASH_RESERVE_RATIO
        ) <= 1.0
        
        # 验证数据源配置
        validation_results["primary_data_source"] = bool(
            self.data_source.PRIMARY_DATA_SOURCE
        )
        
        # 验证风险配置
        validation_results["risk_limits"] = (
            0 < self.trading.MAX_SINGLE_LOSS < 1 and
            0 < self.trading.MAX_DAILY_LOSS < 1 and
            0 < self.trading.MAX_DRAWDOWN < 1
        )
        
        return validation_results


# 全局配置管理器实例
config_manager = ConfigManager()