"""
配置版本控制系统
Configuration Version Control System

提供配置的版本管理、回滚和历史追踪功能
"""

import json
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import logging
import hashlib
from threading import Lock

logger = logging.getLogger(__name__)


class VersionOperation(Enum):
    """版本操作类型"""
    CREATE = "create"      # 创建
    UPDATE = "update"      # 更新
    DELETE = "delete"      # 删除
    ROLLBACK = "rollback"  # 回滚
    MERGE = "merge"        # 合并


@dataclass
class VersionInfo:
    """版本信息"""
    version_id: str
    config_type: str  # global/strategy/stock
    config_name: str
    operation: VersionOperation
    created_time: datetime
    created_by: str
    description: str
    checksum: str
    file_size: int
    parent_version: Optional[str] = None
    tags: List[str] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['created_time'] = self.created_time.isoformat()
        data['operation'] = self.operation.value
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'VersionInfo':
        """从字典创建实例"""
        data['created_time'] = datetime.fromisoformat(data['created_time'])
        data['operation'] = VersionOperation(data['operation'])
        return cls(**data)


class ConfigVersionControl:
    """配置版本控制器"""
    
    def __init__(self, repository_root: str = "config/repository"):
        """
        初始化版本控制器
        
        Args:
            repository_root: 版本库根目录
        """
        self.repository_root = Path(repository_root)
        self.repository_root.mkdir(parents=True, exist_ok=True)
        
        # 版本存储目录
        self.versions_dir = self.repository_root / "versions"
        self.metadata_dir = self.repository_root / "metadata"
        self.branches_dir = self.repository_root / "branches"
        self.tags_dir = self.repository_root / "tags"
        
        # 创建目录结构
        for directory in [self.versions_dir, self.metadata_dir, 
                         self.branches_dir, self.tags_dir]:
            directory.mkdir(parents=True, exist_ok=True)
        
        # 版本注册表
        self.version_registry: Dict[str, VersionInfo] = {}
        self.version_tree: Dict[str, List[str]] = {}  # parent -> children
        
        # 当前分支
        self.current_branch = "main"
        
        # 线程锁
        self._lock = Lock()
        
        # 加载现有版本信息
        self._load_version_registry()
        self._build_version_tree()
        
        logger.info(f"配置版本控制器初始化完成，版本库: {self.repository_root}")
    
    def _generate_version_id(self) -> str:
        """生成版本ID"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        return f"v_{timestamp}"
    
    def _calculate_checksum(self, config_data: Dict[str, Any]) -> str:
        """计算配置校验和"""
        config_str = json.dumps(config_data, sort_keys=True, ensure_ascii=False)
        return hashlib.sha256(config_str.encode('utf-8')).hexdigest()
    
    def _save_version_registry(self):
        """保存版本注册表"""
        registry_file = self.metadata_dir / "version_registry.json"
        registry_data = {
            version_id: version_info.to_dict()
            for version_id, version_info in self.version_registry.items()
        }
        
        with open(registry_file, 'w', encoding='utf-8') as f:
            json.dump(registry_data, f, indent=2, ensure_ascii=False)
    
    def _load_version_registry(self):
        """加载版本注册表"""
        registry_file = self.metadata_dir / "version_registry.json"
        if registry_file.exists():
            try:
                with open(registry_file, 'r', encoding='utf-8') as f:
                    registry_data = json.load(f)
                
                self.version_registry = {
                    version_id: VersionInfo.from_dict(version_data)
                    for version_id, version_data in registry_data.items()
                }
                
                logger.info(f"已加载 {len(self.version_registry)} 个版本记录")
                
            except Exception as e:
                logger.error(f"加载版本注册表失败: {e}")
                self.version_registry = {}
    
    def _build_version_tree(self):
        """构建版本树"""
        self.version_tree = {}
        
        for version_id, version_info in self.version_registry.items():
            parent_id = version_info.parent_version
            if parent_id:
                if parent_id not in self.version_tree:
                    self.version_tree[parent_id] = []
                self.version_tree[parent_id].append(version_id)
    
    def commit_version(
        self,
        config_type: str,
        config_name: str,
        config_data: Dict[str, Any],
        created_by: str,
        description: str = "",
        operation: VersionOperation = VersionOperation.CREATE,
        parent_version: Optional[str] = None,
        tags: List[str] = None
    ) -> str:
        """
        提交新版本
        
        Args:
            config_type: 配置类型
            config_name: 配置名称
            config_data: 配置数据
            created_by: 创建者
            description: 描述信息
            operation: 操作类型
            parent_version: 父版本ID
            tags: 标签列表
            
        Returns:
            新版本ID
        """
        with self._lock:
            try:
                # 生成版本ID
                version_id = self._generate_version_id()
                
                # 计算校验和和文件大小
                checksum = self._calculate_checksum(config_data)
                config_str = json.dumps(config_data, indent=2, ensure_ascii=False)
                file_size = len(config_str.encode('utf-8'))
                
                # 创建版本信息
                version_info = VersionInfo(
                    version_id=version_id,
                    config_type=config_type,
                    config_name=config_name,
                    operation=operation,
                    created_time=datetime.now(),
                    created_by=created_by,
                    description=description,
                    checksum=checksum,
                    file_size=file_size,
                    parent_version=parent_version,
                    tags=tags or []
                )
                
                # 保存配置文件
                version_file = self.versions_dir / f"{version_id}.json"
                with open(version_file, 'w', encoding='utf-8') as f:
                    f.write(config_str)
                
                # 保存版本元数据
                metadata_file = self.metadata_dir / f"{version_id}_meta.json"
                with open(metadata_file, 'w', encoding='utf-8') as f:
                    json.dump(version_info.to_dict(), f, indent=2, ensure_ascii=False)
                
                # 注册版本
                self.version_registry[version_id] = version_info
                
                # 更新版本树
                if parent_version:
                    if parent_version not in self.version_tree:
                        self.version_tree[parent_version] = []
                    self.version_tree[parent_version].append(version_id)
                
                # 保存注册表
                self._save_version_registry()
                
                logger.info(f"版本提交成功: {version_id}")
                return version_id
                
            except Exception as e:
                logger.error(f"版本提交失败: {e}")
                raise
    
    def get_version(self, version_id: str) -> Optional[Tuple[VersionInfo, Dict[str, Any]]]:
        """
        获取指定版本
        
        Args:
            version_id: 版本ID
            
        Returns:
            版本信息和配置数据的元组，如果不存在返回None
        """
        if version_id not in self.version_registry:
            return None
        
        try:
            version_info = self.version_registry[version_id]
            version_file = self.versions_dir / f"{version_id}.json"
            
            if not version_file.exists():
                logger.error(f"版本文件不存在: {version_file}")
                return None
            
            with open(version_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            return version_info, config_data
            
        except Exception as e:
            logger.error(f"获取版本失败: {e}")
            return None
    
    def list_versions(
        self,
        config_type: Optional[str] = None,
        config_name: Optional[str] = None,
        created_by: Optional[str] = None,
        limit: int = 50
    ) -> List[VersionInfo]:
        """
        列出版本
        
        Args:
            config_type: 过滤配置类型
            config_name: 过滤配置名称
            created_by: 过滤创建者
            limit: 限制数量
            
        Returns:
            版本信息列表
        """
        versions = list(self.version_registry.values())
        
        # 应用过滤条件
        if config_type:
            versions = [v for v in versions if v.config_type == config_type]
        
        if config_name:
            versions = [v for v in versions if v.config_name == config_name]
        
        if created_by:
            versions = [v for v in versions if v.created_by == created_by]
        
        # 按创建时间倒序排列
        versions.sort(key=lambda v: v.created_time, reverse=True)
        
        return versions[:limit] if limit > 0 else versions
    
    def rollback_to_version(self, version_id: str, created_by: str) -> Optional[str]:
        """
        回滚到指定版本
        
        Args:
            version_id: 目标版本ID
            created_by: 操作者
            
        Returns:
            新创建的回滚版本ID，失败返回None
        """
        with self._lock:
            try:
                # 获取目标版本
                version_result = self.get_version(version_id)
                if not version_result:
                    raise ValueError(f"版本不存在: {version_id}")
                
                target_version_info, target_config_data = version_result
                
                # 创建回滚版本
                rollback_version_id = self.commit_version(
                    config_type=target_version_info.config_type,
                    config_name=target_version_info.config_name,
                    config_data=target_config_data,
                    created_by=created_by,
                    description=f"回滚到版本 {version_id}",
                    operation=VersionOperation.ROLLBACK,
                    parent_version=version_id,
                    tags=["rollback"]
                )
                
                logger.info(f"成功回滚到版本 {version_id}，新版本: {rollback_version_id}")
                return rollback_version_id
                
            except Exception as e:
                logger.error(f"回滚失败: {e}")
                return None
    
    def compare_versions(self, version_id1: str, version_id2: str) -> Optional[Dict[str, Any]]:
        """
        比较两个版本
        
        Args:
            version_id1: 版本1 ID
            version_id2: 版本2 ID
            
        Returns:
            比较结果，失败返回None
        """
        try:
            # 获取两个版本
            version1_result = self.get_version(version_id1)
            version2_result = self.get_version(version_id2)
            
            if not version1_result or not version2_result:
                raise ValueError("版本不存在")
            
            version1_info, config1_data = version1_result
            version2_info, config2_data = version2_result
            
            # 基本信息比较
            comparison = {
                "version1": {
                    "id": version_id1,
                    "created_time": version1_info.created_time.isoformat(),
                    "created_by": version1_info.created_by,
                    "checksum": version1_info.checksum,
                    "file_size": version1_info.file_size
                },
                "version2": {
                    "id": version_id2,
                    "created_time": version2_info.created_time.isoformat(),
                    "created_by": version2_info.created_by,
                    "checksum": version2_info.checksum,
                    "file_size": version2_info.file_size
                },
                "is_identical": version1_info.checksum == version2_info.checksum,
                "size_difference": version2_info.file_size - version1_info.file_size
            }
            
            # 配置差异分析
            if not comparison["is_identical"]:
                comparison["differences"] = self._analyze_config_differences(
                    config1_data, config2_data
                )
            
            return comparison
            
        except Exception as e:
            logger.error(f"版本比较失败: {e}")
            return None
    
    def _analyze_config_differences(
        self, 
        config1: Dict[str, Any], 
        config2: Dict[str, Any]
    ) -> Dict[str, Any]:
        """分析配置差异"""
        differences = {
            "added": [],      # 新增的键
            "removed": [],    # 删除的键
            "modified": [],   # 修改的键
            "unchanged": []   # 未变化的键
        }
        
        all_keys = set(config1.keys()) | set(config2.keys())
        
        for key in all_keys:
            if key not in config1:
                differences["added"].append(key)
            elif key not in config2:
                differences["removed"].append(key)
            elif config1[key] != config2[key]:
                differences["modified"].append({
                    "key": key,
                    "old_value": config1[key],
                    "new_value": config2[key]
                })
            else:
                differences["unchanged"].append(key)
        
        return differences
    
    def create_tag(self, version_id: str, tag_name: str, created_by: str) -> bool:
        """
        为版本创建标签
        
        Args:
            version_id: 版本ID
            tag_name: 标签名称
            created_by: 创建者
            
        Returns:
            是否成功创建
        """
        with self._lock:
            try:
                if version_id not in self.version_registry:
                    raise ValueError(f"版本不存在: {version_id}")
                
                # 更新版本信息
                version_info = self.version_registry[version_id]
                if tag_name not in version_info.tags:
                    version_info.tags.append(tag_name)
                
                # 创建标签文件
                tag_file = self.tags_dir / f"{tag_name}.json"
                tag_data = {
                    "tag_name": tag_name,
                    "version_id": version_id,
                    "created_by": created_by,
                    "created_time": datetime.now().isoformat()
                }
                
                with open(tag_file, 'w', encoding='utf-8') as f:
                    json.dump(tag_data, f, indent=2, ensure_ascii=False)
                
                # 保存注册表
                self._save_version_registry()
                
                logger.info(f"标签创建成功: {tag_name} -> {version_id}")
                return True
                
            except Exception as e:
                logger.error(f"创建标签失败: {e}")
                return False
    
    def get_version_by_tag(self, tag_name: str) -> Optional[str]:
        """
        根据标签获取版本ID
        
        Args:
            tag_name: 标签名称
            
        Returns:
            版本ID，如果不存在返回None
        """
        tag_file = self.tags_dir / f"{tag_name}.json"
        if tag_file.exists():
            try:
                with open(tag_file, 'r', encoding='utf-8') as f:
                    tag_data = json.load(f)
                return tag_data.get("version_id")
            except Exception as e:
                logger.error(f"读取标签失败: {e}")
        
        return None
    
    def get_version_history(self, config_type: str, config_name: str) -> List[VersionInfo]:
        """
        获取配置的版本历史
        
        Args:
            config_type: 配置类型
            config_name: 配置名称
            
        Returns:
            版本历史列表
        """
        versions = [
            v for v in self.version_registry.values()
            if v.config_type == config_type and v.config_name == config_name
        ]
        
        # 按创建时间排序
        versions.sort(key=lambda v: v.created_time)
        
        return versions
