"""
估值分析特征计算模块

实现多种估值模型（DCF、PEG、PB-ROE等）
构建估值修复机会识别系统和估值预警监控功能
专注于识别估值修复机会和相对估值优势
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union
from datetime import datetime, timedelta
import logging
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class ValuationMethod(Enum):
    """估值方法枚举"""
    DCF = "dcf"
    PEG = "peg"
    PB_ROE = "pb_roe"
    EV_EBITDA = "ev_ebitda"
    PS = "ps"
    PCF = "pcf"


@dataclass
class ValuationMetrics:
    """估值指标数据结构"""
    symbol: str
    valuation_date: datetime
    pe_ratio: float
    pb_ratio: float
    ps_ratio: float
    pcf_ratio: float
    ev_ebitda: float
    peg_ratio: float
    dcf_value: float
    pb_roe_value: float
    market_cap: float
    enterprise_value: float


@dataclass
class ValuationOpportunity:
    """估值修复机会数据结构"""
    symbol: str
    opportunity_type: str
    current_valuation: float
    fair_valuation: float
    upside_potential: float
    confidence_level: float
    catalyst: str
    risk_factors: List[str]


class DCFValuationModel:
    """DCF估值模型"""
    
    def __init__(self):
        self.default_discount_rate = 0.10  # 默认折现率10%
        self.default_terminal_growth = 0.03  # 默认永续增长率3%
        self.forecast_years = 5  # 预测年数
    
    def calculate_dcf_value(self, financial_data: pd.DataFrame, 
                           growth_forecast: Dict,
                           discount_rate: Optional[float] = None,
                           terminal_growth: Optional[float] = None) -> Dict:
        """计算DCF估值"""
        try:
            if financial_data.empty:
                return {'dcf_value': 0, 'error': '财务数据为空'}
            
            discount_rate = discount_rate or self.default_discount_rate
            terminal_growth = terminal_growth or self.default_terminal_growth
            
            # 获取最新现金流数据
            latest_data = financial_data.iloc[-1]
            base_fcf = self._calculate_free_cash_flow(latest_data)
            
            if base_fcf <= 0:
                return {'dcf_value': 0, 'error': '自由现金流为负或零'}
            
            # 预测未来现金流
            projected_fcf = self._project_cash_flows(
                base_fcf, growth_forecast, self.forecast_years
            )
            
            # 计算终值
            terminal_value = self._calculate_terminal_value(
                projected_fcf[-1], terminal_growth, discount_rate
            )
            
            # 折现计算
            present_values = []
            for i, fcf in enumerate(projected_fcf, 1):
                pv = fcf / ((1 + discount_rate) ** i)
                present_values.append(pv)
            
            # 终值折现
            terminal_pv = terminal_value / ((1 + discount_rate) ** self.forecast_years)
            
            # 企业价值
            enterprise_value = sum(present_values) + terminal_pv
            
            # 股权价值（假设无净债务，实际应减去净债务）
            equity_value = enterprise_value
            
            return {
                'dcf_value': equity_value,
                'enterprise_value': enterprise_value,
                'terminal_value': terminal_value,
                'projected_fcf': projected_fcf,
                'present_values': present_values,
                'discount_rate': discount_rate,
                'terminal_growth': terminal_growth
            }
            
        except Exception as e:
            logger.error(f"DCF估值计算失败: {e}")
            return {'dcf_value': 0, 'error': str(e)}
    
    def _calculate_free_cash_flow(self, financial_data: pd.Series) -> float:
        """计算自由现金流"""
        # 简化计算：净利润 + 折旧 - 资本支出 - 营运资金变化
        # 这里使用净利润作为近似值，实际应该更精确
        net_profit = financial_data.get('net_profit', 0)
        
        # 假设折旧为营收的2%，资本支出为营收的3%
        revenue = financial_data.get('total_revenue', 0)
        depreciation = revenue * 0.02
        capex = revenue * 0.03
        
        fcf = net_profit + depreciation - capex
        return max(fcf, 0)
    
    def _project_cash_flows(self, base_fcf: float, growth_forecast: Dict, years: int) -> List[float]:
        """预测未来现金流"""
        projected_fcf = []
        current_fcf = base_fcf
        
        # 获取增长率预测
        growth_rates = self._extract_growth_rates(growth_forecast)
        
        for year in range(years):
            # 使用递减的增长率
            if year < len(growth_rates):
                growth_rate = growth_rates[year]
            else:
                # 后续年份使用最后一年的增长率递减
                growth_rate = growth_rates[-1] * (0.8 ** (year - len(growth_rates) + 1))
            
            current_fcf *= (1 + growth_rate)
            projected_fcf.append(current_fcf)
        
        return projected_fcf
    
    def _extract_growth_rates(self, growth_forecast: Dict) -> List[float]:
        """从增长预测中提取增长率"""
        # 默认增长率序列
        default_rates = [0.15, 0.12, 0.10, 0.08, 0.06]
        
        if not growth_forecast:
            return default_rates
        
        # 尝试从预测数据中提取增长率
        growth_indicators = growth_forecast.get('growth_indicators', {})
        
        if 'profit_acceleration' in growth_indicators:
            base_growth = min(max(growth_indicators['profit_acceleration'] / 100, 0.05), 0.30)
            return [base_growth * (0.9 ** i) for i in range(5)]
        
        return default_rates
    
    def _calculate_terminal_value(self, final_fcf: float, terminal_growth: float, discount_rate: float) -> float:
        """计算终值"""
        if discount_rate <= terminal_growth:
            # 避免分母为负或零
            discount_rate = terminal_growth + 0.02
        
        terminal_fcf = final_fcf * (1 + terminal_growth)
        terminal_value = terminal_fcf / (discount_rate - terminal_growth)
        
        return terminal_value


class PEGValuationModel:
    """PEG估值模型"""
    
    def __init__(self):
        self.fair_peg_ratio = 1.0  # 合理PEG比率
        self.max_peg_ratio = 3.0   # 最大合理PEG比率
    
    def calculate_peg_valuation(self, current_pe: float, growth_rate: float, 
                               earnings_per_share: float) -> Dict:
        """计算PEG估值"""
        try:
            if growth_rate <= 0:
                return {'peg_ratio': float('inf'), 'fair_pe': 0, 'fair_price': 0}
            
            # 计算PEG比率
            peg_ratio = current_pe / growth_rate if growth_rate > 0 else float('inf')
            
            # 计算合理PE
            fair_pe = growth_rate * self.fair_peg_ratio
            
            # 计算合理价格
            fair_price = fair_pe * earnings_per_share
            
            # 估值判断
            if peg_ratio < 0.5:
                valuation_level = 'severely_undervalued'
            elif peg_ratio < 1.0:
                valuation_level = 'undervalued'
            elif peg_ratio < 1.5:
                valuation_level = 'fairly_valued'
            elif peg_ratio < 2.0:
                valuation_level = 'overvalued'
            else:
                valuation_level = 'severely_overvalued'
            
            return {
                'peg_ratio': peg_ratio,
                'fair_pe': fair_pe,
                'fair_price': fair_price,
                'current_pe': current_pe,
                'growth_rate': growth_rate,
                'valuation_level': valuation_level,
                'upside_potential': (fair_price / (current_pe * earnings_per_share) - 1) if current_pe > 0 else 0
            }
            
        except Exception as e:
            logger.error(f"PEG估值计算失败: {e}")
            return {'peg_ratio': float('inf'), 'error': str(e)}


class PBROEValuationModel:
    """PB-ROE估值模型"""
    
    def __init__(self):
        self.benchmark_roe = 0.15  # 基准ROE 15%
    
    def calculate_pb_roe_valuation(self, current_pb: float, roe: float, 
                                  book_value_per_share: float) -> Dict:
        """计算PB-ROE估值"""
        try:
            if roe <= 0:
                return {'fair_pb': 0, 'fair_price': 0, 'pb_roe_ratio': 0}
            
            # 计算合理PB（基于ROE）
            fair_pb = roe / self.benchmark_roe
            
            # 计算合理价格
            fair_price = fair_pb * book_value_per_share
            
            # PB-ROE比率
            pb_roe_ratio = current_pb / roe if roe > 0 else float('inf')
            
            # 估值判断
            pb_premium = current_pb / fair_pb - 1 if fair_pb > 0 else 0
            
            if pb_premium < -0.3:
                valuation_level = 'undervalued'
            elif pb_premium < 0.1:
                valuation_level = 'fairly_valued'
            else:
                valuation_level = 'overvalued'
            
            return {
                'fair_pb': fair_pb,
                'fair_price': fair_price,
                'current_pb': current_pb,
                'pb_roe_ratio': pb_roe_ratio,
                'roe': roe,
                'valuation_level': valuation_level,
                'pb_premium': pb_premium,
                'upside_potential': fair_price / (current_pb * book_value_per_share) - 1 if current_pb > 0 else 0
            }
            
        except Exception as e:
            logger.error(f"PB-ROE估值计算失败: {e}")
            return {'fair_pb': 0, 'error': str(e)}


class RelativeValuationAnalyzer:
    """相对估值比较分析器"""
    
    def __init__(self):
        self.industry_benchmarks = {}  # 行业基准数据
    
    def analyze_relative_valuation(self, stock_metrics: Dict, 
                                 industry_metrics: Dict,
                                 market_metrics: Dict) -> Dict:
        """分析相对估值"""
        try:
            relative_analysis = {}
            
            # PE相对估值
            relative_analysis['pe_analysis'] = self._analyze_pe_relative(
                stock_metrics, industry_metrics, market_metrics
            )
            
            # PB相对估值
            relative_analysis['pb_analysis'] = self._analyze_pb_relative(
                stock_metrics, industry_metrics, market_metrics
            )
            
            # PS相对估值
            relative_analysis['ps_analysis'] = self._analyze_ps_relative(
                stock_metrics, industry_metrics, market_metrics
            )
            
            # 综合相对估值评分
            relative_analysis['overall_score'] = self._calculate_relative_score(
                relative_analysis
            )
            
            return relative_analysis
            
        except Exception as e:
            logger.error(f"相对估值分析失败: {e}")
            return {}
    
    def _analyze_pe_relative(self, stock: Dict, industry: Dict, market: Dict) -> Dict:
        """PE相对估值分析"""
        stock_pe = stock.get('pe_ratio', 0)
        industry_pe = industry.get('avg_pe', 0)
        market_pe = market.get('avg_pe', 0)
        
        analysis = {
            'stock_pe': stock_pe,
            'industry_pe': industry_pe,
            'market_pe': market_pe
        }
        
        if industry_pe > 0:
            analysis['industry_discount'] = (stock_pe / industry_pe - 1) * 100
            analysis['vs_industry'] = 'discount' if stock_pe < industry_pe else 'premium'
        
        if market_pe > 0:
            analysis['market_discount'] = (stock_pe / market_pe - 1) * 100
            analysis['vs_market'] = 'discount' if stock_pe < market_pe else 'premium'
        
        # PE分位数（如果有历史数据）
        if 'pe_percentile' in stock:
            analysis['historical_percentile'] = stock['pe_percentile']
        
        return analysis
    
    def _analyze_pb_relative(self, stock: Dict, industry: Dict, market: Dict) -> Dict:
        """PB相对估值分析"""
        stock_pb = stock.get('pb_ratio', 0)
        industry_pb = industry.get('avg_pb', 0)
        market_pb = market.get('avg_pb', 0)
        
        analysis = {
            'stock_pb': stock_pb,
            'industry_pb': industry_pb,
            'market_pb': market_pb
        }
        
        if industry_pb > 0:
            analysis['industry_discount'] = (stock_pb / industry_pb - 1) * 100
            analysis['vs_industry'] = 'discount' if stock_pb < industry_pb else 'premium'
        
        if market_pb > 0:
            analysis['market_discount'] = (stock_pb / market_pb - 1) * 100
            analysis['vs_market'] = 'discount' if stock_pb < market_pb else 'premium'
        
        return analysis
    
    def _analyze_ps_relative(self, stock: Dict, industry: Dict, market: Dict) -> Dict:
        """PS相对估值分析"""
        stock_ps = stock.get('ps_ratio', 0)
        industry_ps = industry.get('avg_ps', 0)
        market_ps = market.get('avg_ps', 0)
        
        analysis = {
            'stock_ps': stock_ps,
            'industry_ps': industry_ps,
            'market_ps': market_ps
        }
        
        if industry_ps > 0:
            analysis['industry_discount'] = (stock_ps / industry_ps - 1) * 100
            analysis['vs_industry'] = 'discount' if stock_ps < industry_ps else 'premium'
        
        if market_ps > 0:
            analysis['market_discount'] = (stock_ps / market_ps - 1) * 100
            analysis['vs_market'] = 'discount' if stock_ps < market_ps else 'premium'
        
        return analysis
    
    def _calculate_relative_score(self, analysis: Dict) -> float:
        """计算相对估值综合评分"""
        score = 0.5  # 基础分数
        
        # PE评分
        pe_analysis = analysis.get('pe_analysis', {})
        if 'industry_discount' in pe_analysis:
            # 折价越多分数越高
            discount = pe_analysis['industry_discount']
            if discount < -30:
                score += 0.2
            elif discount < -15:
                score += 0.1
            elif discount > 30:
                score -= 0.2
            elif discount > 15:
                score -= 0.1
        
        # PB评分
        pb_analysis = analysis.get('pb_analysis', {})
        if 'industry_discount' in pb_analysis:
            discount = pb_analysis['industry_discount']
            if discount < -30:
                score += 0.15
            elif discount < -15:
                score += 0.075
            elif discount > 30:
                score -= 0.15
            elif discount > 15:
                score -= 0.075
        
        # PS评分
        ps_analysis = analysis.get('ps_analysis', {})
        if 'industry_discount' in ps_analysis:
            discount = ps_analysis['industry_discount']
            if discount < -30:
                score += 0.15
            elif discount < -15:
                score += 0.075
            elif discount > 30:
                score -= 0.15
            elif discount > 15:
                score -= 0.075
        
        return min(max(score, 0), 1)


class ValuationOpportunityDetector:
    """估值修复机会识别系统"""
    
    def __init__(self):
        self.opportunity_thresholds = {
            'deep_value': -0.4,      # 深度价值（40%以上折价）
            'value': -0.2,           # 价值股（20%以上折价）
            'growth_at_value': -0.1,  # 成长价值股（10%以上折价）
            'catalyst_driven': 0.0    # 催化剂驱动（任何折价）
        }
    
    def detect_opportunities(self, valuation_data: Dict, 
                           fundamental_data: Dict,
                           market_data: Dict) -> List[ValuationOpportunity]:
        """检测估值修复机会"""
        opportunities = []
        
        try:
            symbol = valuation_data.get('symbol', 'UNKNOWN')
            
            # 深度价值机会
            deep_value_opp = self._detect_deep_value(valuation_data, fundamental_data)
            if deep_value_opp:
                opportunities.append(deep_value_opp)
            
            # 成长价值机会
            growth_value_opp = self._detect_growth_value(valuation_data, fundamental_data)
            if growth_value_opp:
                opportunities.append(growth_value_opp)
            
            # 催化剂驱动机会
            catalyst_opp = self._detect_catalyst_opportunity(valuation_data, fundamental_data, market_data)
            if catalyst_opp:
                opportunities.append(catalyst_opp)
            
            # 相对估值机会
            relative_opp = self._detect_relative_opportunity(valuation_data, market_data)
            if relative_opp:
                opportunities.append(relative_opp)
            
            return opportunities
            
        except Exception as e:
            logger.error(f"估值机会检测失败: {e}")
            return []
    
    def _detect_deep_value(self, valuation_data: Dict, fundamental_data: Dict) -> Optional[ValuationOpportunity]:
        """检测深度价值机会"""
        dcf_data = valuation_data.get('dcf_analysis', {})
        pb_roe_data = valuation_data.get('pb_roe_analysis', {})
        
        # 检查DCF折价
        dcf_upside = dcf_data.get('upside_potential', 0)
        pb_upside = pb_roe_data.get('upside_potential', 0)
        
        avg_upside = (dcf_upside + pb_upside) / 2
        
        if avg_upside > 0.4:  # 40%以上上涨空间
            # 检查基本面质量
            fundamental_score = fundamental_data.get('fundamental_score', 0)
            
            if fundamental_score > 0.3:  # 基本面不能太差
                return ValuationOpportunity(
                    symbol=valuation_data.get('symbol', ''),
                    opportunity_type='deep_value',
                    current_valuation=1.0,
                    fair_valuation=1.0 + avg_upside,
                    upside_potential=avg_upside,
                    confidence_level=0.7,
                    catalyst='估值修复',
                    risk_factors=['基本面恶化风险', '市场情绪风险']
                )
        
        return None
    
    def _detect_growth_value(self, valuation_data: Dict, fundamental_data: Dict) -> Optional[ValuationOpportunity]:
        """检测成长价值机会"""
        peg_data = valuation_data.get('peg_analysis', {})
        
        peg_ratio = peg_data.get('peg_ratio', float('inf'))
        growth_rate = peg_data.get('growth_rate', 0)
        
        # PEG < 1 且增长率 > 15%
        if peg_ratio < 1.0 and growth_rate > 15:
            upside = peg_data.get('upside_potential', 0)
            
            if upside > 0.1:  # 至少10%上涨空间
                return ValuationOpportunity(
                    symbol=valuation_data.get('symbol', ''),
                    opportunity_type='growth_at_value',
                    current_valuation=1.0,
                    fair_valuation=1.0 + upside,
                    upside_potential=upside,
                    confidence_level=0.8,
                    catalyst='业绩增长',
                    risk_factors=['增长不及预期风险']
                )
        
        return None
    
    def _detect_catalyst_opportunity(self, valuation_data: Dict, 
                                   fundamental_data: Dict, 
                                   market_data: Dict) -> Optional[ValuationOpportunity]:
        """检测催化剂驱动机会"""
        # 检查是否有明确的催化剂
        catalysts = []
        
        # 业绩拐点
        growth_indicators = fundamental_data.get('growth_indicators', {})
        if growth_indicators.get('profit_acceleration', 0) > 20:
            catalysts.append('业绩拐点')
        
        # ROE改善
        if growth_indicators.get('roe_improvement', 0) > 3:
            catalysts.append('ROE改善')
        
        # 估值修复
        relative_analysis = valuation_data.get('relative_analysis', {})
        overall_score = relative_analysis.get('overall_score', 0.5)
        if overall_score > 0.7:
            catalysts.append('相对估值优势')
        
        if catalysts:
            # 计算综合上涨空间
            dcf_upside = valuation_data.get('dcf_analysis', {}).get('upside_potential', 0)
            peg_upside = valuation_data.get('peg_analysis', {}).get('upside_potential', 0)
            pb_upside = valuation_data.get('pb_roe_analysis', {}).get('upside_potential', 0)
            
            avg_upside = np.mean([x for x in [dcf_upside, peg_upside, pb_upside] if x > 0])
            
            if avg_upside > 0.05:  # 至少5%上涨空间
                return ValuationOpportunity(
                    symbol=valuation_data.get('symbol', ''),
                    opportunity_type='catalyst_driven',
                    current_valuation=1.0,
                    fair_valuation=1.0 + avg_upside,
                    upside_potential=avg_upside,
                    confidence_level=0.6,
                    catalyst=', '.join(catalysts),
                    risk_factors=['催化剂不及预期']
                )
        
        return None
    
    def _detect_relative_opportunity(self, valuation_data: Dict, market_data: Dict) -> Optional[ValuationOpportunity]:
        """检测相对估值机会"""
        relative_analysis = valuation_data.get('relative_analysis', {})
        
        # 检查行业折价
        pe_analysis = relative_analysis.get('pe_analysis', {})
        pb_analysis = relative_analysis.get('pb_analysis', {})
        
        pe_discount = pe_analysis.get('industry_discount', 0)
        pb_discount = pb_analysis.get('industry_discount', 0)
        
        avg_discount = (pe_discount + pb_discount) / 2
        
        if avg_discount < -20:  # 相对行业折价20%以上
            upside_potential = abs(avg_discount) / 100
            
            return ValuationOpportunity(
                symbol=valuation_data.get('symbol', ''),
                opportunity_type='relative_value',
                current_valuation=1.0,
                fair_valuation=1.0 + upside_potential,
                upside_potential=upside_potential,
                confidence_level=0.5,
                catalyst='相对估值修复',
                risk_factors=['行业整体下跌风险']
            )
        
        return None


class ValuationAlertSystem:
    """估值预警和监控系统"""
    
    def __init__(self):
        self.alert_rules = {
            'pe_extreme_high': {'threshold': 50, 'direction': 'above'},
            'pe_extreme_low': {'threshold': 5, 'direction': 'below'},
            'pb_extreme_low': {'threshold': 0.5, 'direction': 'below'},
            'peg_attractive': {'threshold': 0.5, 'direction': 'below'},
            'dcf_upside_high': {'threshold': 0.5, 'direction': 'above'}
        }
    
    def monitor_valuation_alerts(self, valuation_data: Dict, symbol: str) -> List[Dict]:
        """监控估值预警"""
        alerts = []
        
        try:
            # PE预警
            pe_ratio = valuation_data.get('pe_ratio', 0)
            if pe_ratio > self.alert_rules['pe_extreme_high']['threshold']:
                alerts.append({
                    'type': 'pe_extreme_high',
                    'symbol': symbol,
                    'value': pe_ratio,
                    'threshold': self.alert_rules['pe_extreme_high']['threshold'],
                    'message': f'PE比率过高: {pe_ratio:.1f}',
                    'severity': 'high'
                })
            elif pe_ratio < self.alert_rules['pe_extreme_low']['threshold'] and pe_ratio > 0:
                alerts.append({
                    'type': 'pe_extreme_low',
                    'symbol': symbol,
                    'value': pe_ratio,
                    'threshold': self.alert_rules['pe_extreme_low']['threshold'],
                    'message': f'PE比率极低: {pe_ratio:.1f}',
                    'severity': 'medium'
                })
            
            # PB预警
            pb_ratio = valuation_data.get('pb_ratio', 0)
            if pb_ratio < self.alert_rules['pb_extreme_low']['threshold'] and pb_ratio > 0:
                alerts.append({
                    'type': 'pb_extreme_low',
                    'symbol': symbol,
                    'value': pb_ratio,
                    'threshold': self.alert_rules['pb_extreme_low']['threshold'],
                    'message': f'PB比率极低: {pb_ratio:.2f}',
                    'severity': 'medium'
                })
            
            # PEG预警
            peg_analysis = valuation_data.get('peg_analysis', {})
            peg_ratio = peg_analysis.get('peg_ratio', float('inf'))
            if peg_ratio < self.alert_rules['peg_attractive']['threshold']:
                alerts.append({
                    'type': 'peg_attractive',
                    'symbol': symbol,
                    'value': peg_ratio,
                    'threshold': self.alert_rules['peg_attractive']['threshold'],
                    'message': f'PEG比率具有吸引力: {peg_ratio:.2f}',
                    'severity': 'low'
                })
            
            # DCF上涨空间预警
            dcf_analysis = valuation_data.get('dcf_analysis', {})
            dcf_upside = dcf_analysis.get('upside_potential', 0)
            if dcf_upside > self.alert_rules['dcf_upside_high']['threshold']:
                alerts.append({
                    'type': 'dcf_upside_high',
                    'symbol': symbol,
                    'value': dcf_upside,
                    'threshold': self.alert_rules['dcf_upside_high']['threshold'],
                    'message': f'DCF显示高上涨空间: {dcf_upside:.1%}',
                    'severity': 'low'
                })
            
            return alerts
            
        except Exception as e:
            logger.error(f"估值预警监控失败 {symbol}: {e}")
            return []
    
    def generate_alert_report(self, all_alerts: List[Dict]) -> Dict:
        """生成预警报告"""
        if not all_alerts:
            return {'total_alerts': 0, 'summary': '无预警'}
        
        # 按严重程度分类
        high_alerts = [a for a in all_alerts if a['severity'] == 'high']
        medium_alerts = [a for a in all_alerts if a['severity'] == 'medium']
        low_alerts = [a for a in all_alerts if a['severity'] == 'low']
        
        # 按类型统计
        alert_types = {}
        for alert in all_alerts:
            alert_type = alert['type']
            if alert_type not in alert_types:
                alert_types[alert_type] = 0
            alert_types[alert_type] += 1
        
        return {
            'total_alerts': len(all_alerts),
            'high_severity': len(high_alerts),
            'medium_severity': len(medium_alerts),
            'low_severity': len(low_alerts),
            'alert_types': alert_types,
            'top_alerts': sorted(all_alerts, key=lambda x: {'high': 3, 'medium': 2, 'low': 1}[x['severity']], reverse=True)[:10],
            'report_time': datetime.now()
        }


class ValuationAnalyzer:
    """估值分析特征计算主类"""
    
    def __init__(self):
        self.dcf_model = DCFValuationModel()
        self.peg_model = PEGValuationModel()
        self.pb_roe_model = PBROEValuationModel()
        self.relative_analyzer = RelativeValuationAnalyzer()
        self.opportunity_detector = ValuationOpportunityDetector()
        self.alert_system = ValuationAlertSystem()
        
        logger.info("估值分析器初始化完成")
    
    def calculate_comprehensive_valuation(self, financial_data: pd.DataFrame,
                                        market_data: Dict,
                                        growth_forecast: Dict,
                                        symbol: str) -> Dict:
        """计算综合估值分析"""
        try:
            if financial_data.empty:
                return {'error': '财务数据为空'}
            
            latest_data = financial_data.iloc[-1]
            
            # 基础估值指标
            basic_metrics = self._calculate_basic_metrics(latest_data, market_data)
            
            # DCF估值
            dcf_analysis = self.dcf_model.calculate_dcf_value(
                financial_data, growth_forecast
            )
            
            # PEG估值
            peg_analysis = self.peg_model.calculate_peg_valuation(
                basic_metrics['pe_ratio'],
                growth_forecast.get('growth_indicators', {}).get('profit_acceleration', 10),
                basic_metrics['eps']
            )
            
            # PB-ROE估值
            pb_roe_analysis = self.pb_roe_model.calculate_pb_roe_valuation(
                basic_metrics['pb_ratio'],
                basic_metrics['roe'],
                basic_metrics['bvps']
            )
            
            # 相对估值分析
            relative_analysis = self.relative_analyzer.analyze_relative_valuation(
                basic_metrics,
                market_data.get('industry_metrics', {}),
                market_data.get('market_metrics', {})
            )
            
            # 综合估值评分
            valuation_score = self._calculate_valuation_score(
                dcf_analysis, peg_analysis, pb_roe_analysis, relative_analysis
            )
            
            # 构建结果
            result = {
                'symbol': symbol,
                'basic_metrics': basic_metrics,
                'dcf_analysis': dcf_analysis,
                'peg_analysis': peg_analysis,
                'pb_roe_analysis': pb_roe_analysis,
                'relative_analysis': relative_analysis,
                'valuation_score': valuation_score,
                'analysis_date': datetime.now()
            }
            
            logger.info(f"成功计算综合估值 {symbol}, 评分: {valuation_score:.3f}")
            return result
            
        except Exception as e:
            logger.error(f"综合估值计算失败 {symbol}: {e}")
            return {'error': str(e)}
    
    def _calculate_basic_metrics(self, latest_data: pd.Series, market_data: Dict) -> Dict:
        """计算基础估值指标"""
        current_price = market_data.get('current_price', 0)
        shares_outstanding = market_data.get('shares_outstanding', 1)
        
        # 基础财务数据
        net_profit = latest_data.get('net_profit', 0)
        total_equity = latest_data.get('total_equity', 0)
        total_revenue = latest_data.get('total_revenue', 0)
        
        # 计算每股指标
        eps = net_profit / shares_outstanding if shares_outstanding > 0 else 0
        bvps = total_equity / shares_outstanding if shares_outstanding > 0 else 0
        
        # 计算估值比率
        pe_ratio = current_price / eps if eps > 0 else 0
        pb_ratio = current_price / bvps if bvps > 0 else 0
        ps_ratio = (current_price * shares_outstanding) / total_revenue if total_revenue > 0 else 0
        
        # ROE
        roe = latest_data.get('roe', 0) / 100  # 转换为小数
        
        return {
            'current_price': current_price,
            'eps': eps,
            'bvps': bvps,
            'pe_ratio': pe_ratio,
            'pb_ratio': pb_ratio,
            'ps_ratio': ps_ratio,
            'roe': roe,
            'market_cap': current_price * shares_outstanding
        }
    
    def _calculate_valuation_score(self, dcf_analysis: Dict, peg_analysis: Dict,
                                 pb_roe_analysis: Dict, relative_analysis: Dict) -> float:
        """计算综合估值评分"""
        score = 0.5  # 基础分数
        
        # DCF评分 (权重30%)
        dcf_upside = dcf_analysis.get('upside_potential', 0)
        if dcf_upside > 0.5:
            score += 0.15
        elif dcf_upside > 0.2:
            score += 0.10
        elif dcf_upside > 0:
            score += 0.05
        elif dcf_upside < -0.2:
            score -= 0.10
        
        # PEG评分 (权重25%)
        peg_ratio = peg_analysis.get('peg_ratio', float('inf'))
        if peg_ratio < 0.5:
            score += 0.125
        elif peg_ratio < 1.0:
            score += 0.10
        elif peg_ratio < 1.5:
            score += 0.05
        elif peg_ratio > 3.0:
            score -= 0.10
        
        # PB-ROE评分 (权重25%)
        pb_upside = pb_roe_analysis.get('upside_potential', 0)
        if pb_upside > 0.3:
            score += 0.125
        elif pb_upside > 0.1:
            score += 0.10
        elif pb_upside > 0:
            score += 0.05
        elif pb_upside < -0.2:
            score -= 0.10
        
        # 相对估值评分 (权重20%)
        relative_score = relative_analysis.get('overall_score', 0.5)
        score += (relative_score - 0.5) * 0.4  # 转换为-0.2到+0.2的范围
        
        return min(max(score, 0), 1)
    
    def detect_valuation_opportunities(self, valuation_analysis: Dict,
                                     fundamental_data: Dict,
                                     market_data: Dict) -> List[ValuationOpportunity]:
        """检测估值机会"""
        return self.opportunity_detector.detect_opportunities(
            valuation_analysis, fundamental_data, market_data
        )
    
    def monitor_valuation_alerts(self, valuation_analysis: Dict, symbol: str) -> List[Dict]:
        """监控估值预警"""
        return self.alert_system.monitor_valuation_alerts(valuation_analysis, symbol)
    
    def batch_valuation_analysis(self, financial_data_dict: Dict[str, pd.DataFrame],
                                market_data_dict: Dict[str, Dict],
                                growth_forecasts: Dict[str, Dict]) -> Dict[str, Dict]:
        """批量估值分析"""
        results = {}
        
        for symbol in financial_data_dict.keys():
            try:
                financial_data = financial_data_dict[symbol]
                market_data = market_data_dict.get(symbol, {})
                growth_forecast = growth_forecasts.get(symbol, {})
                
                analysis = self.calculate_comprehensive_valuation(
                    financial_data, market_data, growth_forecast, symbol
                )
                
                if 'error' not in analysis:
                    results[symbol] = analysis
                    
            except Exception as e:
                logger.error(f"批量估值分析失败 {symbol}: {e}")
                continue
        
        logger.info(f"批量估值分析完成，成功分析 {len(results)} 只股票")
        return results
    
    def get_top_value_stocks(self, analysis_results: Dict[str, Dict],
                           top_n: int = 20) -> List[Tuple[str, float]]:
        """获取估值最具吸引力的股票"""
        scores = {symbol: data['valuation_score']
                 for symbol, data in analysis_results.items()
                 if 'valuation_score' in data}
        
        sorted_stocks = sorted(scores.items(), key=lambda x: x[1], reverse=True)
        return sorted_stocks[:top_n]