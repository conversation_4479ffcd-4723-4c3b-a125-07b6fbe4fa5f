"""
六维度特征工程系统集成引擎

整合基本面、估值、技术面、情绪、风险、大盘六个维度的特征分析
为股票筛选AI模型提供全面的特征数据
专注于识别爆发股的综合特征
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union
from datetime import datetime, timedelta
import logging
from dataclasses import dataclass

from .fundamental_analyzer import FundamentalAnalyzer
from .valuation_analyzer import ValuationAnalyzer
from .technical_analyzer import TechnicalAnalyzer
from .sentiment_analyzer import SentimentAnalyzer
from .risk_analyzer import RiskAnalyzer
from .market_analyzer import MarketAnalyzer

logger = logging.getLogger(__name__)


@dataclass
class ComprehensiveFeatures:
    """综合特征数据结构"""
    symbol: str
    analysis_date: datetime
    
    # 六维度评分
    fundamental_score: float
    valuation_score: float
    technical_score: float
    sentiment_score: float
    risk_score: float
    market_score: float
    
    # 综合评分
    overall_score: float
    explosive_potential: float
    
    # 详细特征
    fundamental_features: Dict
    valuation_features: Dict
    technical_features: Dict
    sentiment_features: Dict
    risk_features: Dict
    market_features: Dict
    
    # 投资建议
    investment_recommendation: str
    confidence_level: float
    key_factors: List[str]
    risk_warnings: List[str]


class FeatureEngine:
    """六维度特征工程系统主引擎"""
    
    def __init__(self):
        # 初始化各维度分析器
        self.fundamental_analyzer = FundamentalAnalyzer()
        self.valuation_analyzer = ValuationAnalyzer()
        self.technical_analyzer = TechnicalAnalyzer()
        self.sentiment_analyzer = SentimentAnalyzer()
        self.risk_analyzer = RiskAnalyzer()
        self.market_analyzer = MarketAnalyzer()
        
        # 六维度权重配置（专注爆发股识别）
        self.dimension_weights = {
            'fundamental': 0.20,    # 基本面权重20%
            'valuation': 0.15,      # 估值权重15%
            'technical': 0.25,      # 技术面权重25%（重要）
            'sentiment': 0.15,      # 情绪权重15%
            'risk': -0.10,          # 风险权重-10%（负向）
            'market': 0.15          # 大盘权重15%
        }
        
        # 爆发股识别权重（更激进的配置）
        self.explosive_weights = {
            'fundamental': 0.15,    # 基本面15%
            'valuation': 0.10,      # 估值10%
            'technical': 0.35,      # 技术面35%（最重要）
            'sentiment': 0.20,      # 情绪20%（重要）
            'risk': -0.05,          # 风险-5%（容忍更高风险）
            'market': 0.25          # 大盘25%（重要）
        }
        
        logger.info("六维度特征工程系统初始化完成")
    
    def extract_comprehensive_features(self, 
                                     symbol: str,
                                     financial_data: pd.DataFrame,
                                     price_data: pd.DataFrame,
                                     market_data: Dict,
                                     news_data: List = None,
                                     social_data: List = None,
                                     macro_data: Dict = None,
                                     flow_data: Dict = None,
                                     policy_data: Dict = None) -> ComprehensiveFeatures:
        """提取综合特征"""
        try:
            logger.info(f"开始提取 {symbol} 的六维度特征")
            
            # 1. 基本面分析
            fundamental_features = self.fundamental_analyzer.extract_fundamental_features(
                financial_data, symbol
            )
            fundamental_score = fundamental_features.get('fundamental_score', 0.5)
            
            # 2. 估值分析
            growth_forecast = fundamental_features.get('forecast_data', {})
            valuation_features = self.valuation_analyzer.calculate_comprehensive_valuation(
                financial_data, market_data, growth_forecast, symbol
            )
            valuation_score = valuation_features.get('valuation_score', 0.5)
            
            # 3. 技术分析
            technical_features = self.technical_analyzer.extract_technical_features(
                price_data, symbol=symbol
            )
            technical_score = technical_features.get('technical_score', 0.5)
            
            # 4. 情绪分析
            sentiment_features = self.sentiment_analyzer.extract_sentiment_features(
                news_data or [], social_data or [], market_data, symbol
            )
            sentiment_score = sentiment_features.get('overall_sentiment_score', 0.5)
            
            # 5. 风险分析
            technical_indicators = technical_features.get('latest_features', {})
            risk_features = self.risk_analyzer.extract_risk_features(
                price_data, financial_data, market_data, technical_indicators, symbol
            )
            risk_score = 1.0 - risk_features.get('overall_risk_score', 0.5)  # 风险取反
            
            # 6. 大盘分析（如果有相关数据）
            if macro_data and flow_data and policy_data:
                # 使用大盘指数数据（这里简化处理）
                index_data = price_data.copy()  # 实际应该是大盘指数数据
                market_forecast = self.market_analyzer.predict_market_trend(
                    macro_data, market_data, index_data, flow_data, policy_data
                )
                market_score = self._convert_market_trend_to_score(market_forecast.trend_direction)
                market_features = {
                    'trend_direction': market_forecast.trend_direction.name,
                    'market_phase': market_forecast.market_phase.value,
                    'confidence_level': market_forecast.confidence_level,
                    'key_factors': market_forecast.key_factors
                }
            else:
                market_score = 0.5
                market_features = {'trend_direction': 'NEUTRAL', 'market_phase': 'neutral'}
            
            # 计算综合评分
            overall_score = self._calculate_overall_score(
                fundamental_score, valuation_score, technical_score,
                sentiment_score, risk_score, market_score
            )
            
            # 计算爆发潜力评分
            explosive_potential = self._calculate_explosive_potential(
                fundamental_score, valuation_score, technical_score,
                sentiment_score, risk_score, market_score
            )
            
            # 生成投资建议
            investment_recommendation = self._generate_investment_recommendation(
                overall_score, explosive_potential, risk_features
            )
            
            # 计算置信度
            confidence_level = self._calculate_confidence_level(
                fundamental_features, valuation_features, technical_features,
                sentiment_features, risk_features
            )
            
            # 识别关键因素
            key_factors = self._identify_key_factors(
                fundamental_features, valuation_features, technical_features,
                sentiment_features, market_features
            )
            
            # 识别风险警告
            risk_warnings = self._identify_risk_warnings(risk_features)
            
            # 构建综合特征对象
            comprehensive_features = ComprehensiveFeatures(
                symbol=symbol,
                analysis_date=datetime.now(),
                fundamental_score=fundamental_score,
                valuation_score=valuation_score,
                technical_score=technical_score,
                sentiment_score=sentiment_score,
                risk_score=risk_score,
                market_score=market_score,
                overall_score=overall_score,
                explosive_potential=explosive_potential,
                fundamental_features=fundamental_features,
                valuation_features=valuation_features,
                technical_features=technical_features,
                sentiment_features=sentiment_features,
                risk_features=risk_features,
                market_features=market_features,
                investment_recommendation=investment_recommendation,
                confidence_level=confidence_level,
                key_factors=key_factors,
                risk_warnings=risk_warnings
            )
            
            logger.info(f"成功提取 {symbol} 的六维度特征，综合评分: {overall_score:.3f}, "
                       f"爆发潜力: {explosive_potential:.3f}")
            
            return comprehensive_features
            
        except Exception as e:
            logger.error(f"提取 {symbol} 综合特征失败: {e}")
            # 返回默认特征
            return self._create_default_features(symbol)
    
    def _calculate_overall_score(self, fundamental: float, valuation: float,
                               technical: float, sentiment: float,
                               risk: float, market: float) -> float:
        """计算综合评分"""
        overall_score = (
            fundamental * self.dimension_weights['fundamental'] +
            valuation * self.dimension_weights['valuation'] +
            technical * self.dimension_weights['technical'] +
            sentiment * self.dimension_weights['sentiment'] +
            risk * self.dimension_weights['risk'] +
            market * self.dimension_weights['market']
        )
        
        return max(min(overall_score, 1.0), 0.0)
    
    def _calculate_explosive_potential(self, fundamental: float, valuation: float,
                                     technical: float, sentiment: float,
                                     risk: float, market: float) -> float:
        """计算爆发潜力评分"""
        explosive_score = (
            fundamental * self.explosive_weights['fundamental'] +
            valuation * self.explosive_weights['valuation'] +
            technical * self.explosive_weights['technical'] +
            sentiment * self.explosive_weights['sentiment'] +
            risk * self.explosive_weights['risk'] +
            market * self.explosive_weights['market']
        )
        
        return max(min(explosive_score, 1.0), 0.0)
    
    def _convert_market_trend_to_score(self, trend_direction) -> float:
        """将市场趋势转换为评分"""
        trend_scores = {
            'STRONG_BULL': 1.0,
            'BULL': 0.8,
            'NEUTRAL': 0.5,
            'BEAR': 0.2,
            'STRONG_BEAR': 0.0
        }
        return trend_scores.get(trend_direction.name if hasattr(trend_direction, 'name') else str(trend_direction), 0.5)
    
    def _generate_investment_recommendation(self, overall_score: float,
                                          explosive_potential: float,
                                          risk_features: Dict) -> str:
        """生成投资建议"""
        risk_level = risk_features.get('risk_level', 'MEDIUM')
        
        if explosive_potential > 0.8 and overall_score > 0.7:
            if risk_level in ['VERY_HIGH', 'HIGH']:
                return "强烈推荐-高风险高收益"
            else:
                return "强烈推荐-优质爆发股"
        elif explosive_potential > 0.6 and overall_score > 0.6:
            return "推荐-具备爆发潜力"
        elif overall_score > 0.6:
            return "推荐-综合表现良好"
        elif overall_score > 0.4:
            return "中性-可适度关注"
        else:
            return "不推荐-综合表现较差"
    
    def _calculate_confidence_level(self, fundamental: Dict, valuation: Dict,
                                  technical: Dict, sentiment: Dict,
                                  risk: Dict) -> float:
        """计算置信度"""
        # 基于各维度数据质量和一致性计算置信度
        data_quality_scores = []
        
        # 基本面数据质量
        if fundamental and 'error' not in fundamental:
            data_quality_scores.append(0.9)
        else:
            data_quality_scores.append(0.3)
        
        # 估值数据质量
        if valuation and 'error' not in valuation:
            data_quality_scores.append(0.9)
        else:
            data_quality_scores.append(0.3)
        
        # 技术面数据质量
        if technical and 'error' not in technical:
            data_quality_scores.append(0.9)
        else:
            data_quality_scores.append(0.3)
        
        # 情绪数据质量
        if sentiment and 'error' not in sentiment:
            data_quality_scores.append(0.8)  # 情绪数据相对不稳定
        else:
            data_quality_scores.append(0.4)
        
        # 风险数据质量
        if risk and 'error' not in risk:
            data_quality_scores.append(0.9)
        else:
            data_quality_scores.append(0.3)
        
        # 计算平均置信度
        confidence = np.mean(data_quality_scores)
        return max(min(confidence, 0.95), 0.3)
    
    def _identify_key_factors(self, fundamental: Dict, valuation: Dict,
                            technical: Dict, sentiment: Dict,
                            market: Dict) -> List[str]:
        """识别关键因素"""
        key_factors = []
        
        # 基本面关键因素
        if fundamental.get('fundamental_score', 0) > 0.7:
            key_factors.append("基本面强劲")
        
        # 估值关键因素
        if valuation.get('valuation_score', 0) > 0.7:
            key_factors.append("估值具有吸引力")
        
        # 技术面关键因素
        if technical.get('technical_score', 0) > 0.7:
            key_factors.append("技术面强势")
        
        # 情绪关键因素
        if sentiment.get('overall_sentiment_score', 0) > 0.7:
            key_factors.append("市场情绪积极")
        
        # 大盘关键因素
        if market.get('trend_direction') in ['STRONG_BULL', 'BULL']:
            key_factors.append("大盘趋势向好")
        
        return key_factors[:5]  # 限制数量
    
    def _identify_risk_warnings(self, risk_features: Dict) -> List[str]:
        """识别风险警告"""
        warnings = []
        
        risk_level = risk_features.get('risk_level', 'MEDIUM')
        if risk_level in ['VERY_HIGH', 'HIGH']:
            warnings.append(f"整体风险等级{risk_level}")
        
        # 从风险汇总中提取风险因素
        risk_summary = risk_features.get('risk_summary', {})
        risk_factors = risk_summary.get('risk_factors', [])
        
        # 添加主要风险因素
        for factor in risk_factors[:3]:
            warnings.append(factor)
        
        return warnings
    
    def _create_default_features(self, symbol: str) -> ComprehensiveFeatures:
        """创建默认特征（当分析失败时）"""
        return ComprehensiveFeatures(
            symbol=symbol,
            analysis_date=datetime.now(),
            fundamental_score=0.5,
            valuation_score=0.5,
            technical_score=0.5,
            sentiment_score=0.5,
            risk_score=0.5,
            market_score=0.5,
            overall_score=0.5,
            explosive_potential=0.5,
            fundamental_features={},
            valuation_features={},
            technical_features={},
            sentiment_features={},
            risk_features={},
            market_features={},
            investment_recommendation="数据不足-无法评估",
            confidence_level=0.3,
            key_factors=["数据不足"],
            risk_warnings=["分析失败"]
        )
    
    def batch_feature_extraction(self, stock_data_dict: Dict[str, Dict]) -> Dict[str, ComprehensiveFeatures]:
        """批量特征提取"""
        results = {}
        
        for symbol, data in stock_data_dict.items():
            try:
                features = self.extract_comprehensive_features(
                    symbol=symbol,
                    financial_data=data.get('financial_data', pd.DataFrame()),
                    price_data=data.get('price_data', pd.DataFrame()),
                    market_data=data.get('market_data', {}),
                    news_data=data.get('news_data', []),
                    social_data=data.get('social_data', []),
                    macro_data=data.get('macro_data'),
                    flow_data=data.get('flow_data'),
                    policy_data=data.get('policy_data')
                )
                results[symbol] = features
                
            except Exception as e:
                logger.error(f"批量特征提取失败 {symbol}: {e}")
                results[symbol] = self._create_default_features(symbol)
        
        logger.info(f"批量特征提取完成，成功处理 {len(results)} 只股票")
        return results
    
    def get_top_explosive_stocks(self, features_dict: Dict[str, ComprehensiveFeatures],
                               top_n: int = 20,
                               min_confidence: float = 0.6) -> List[Tuple[str, float, float]]:
        """获取爆发潜力最高的股票"""
        candidates = []
        
        for symbol, features in features_dict.items():
            if features.confidence_level >= min_confidence:
                candidates.append((
                    symbol,
                    features.explosive_potential,
                    features.confidence_level
                ))
        
        # 按爆发潜力排序
        candidates.sort(key=lambda x: x[1], reverse=True)
        return candidates[:top_n]
    
    def get_comprehensive_ranking(self, features_dict: Dict[str, ComprehensiveFeatures],
                                top_n: int = 50) -> List[Tuple[str, float, str]]:
        """获取综合排名"""
        candidates = []
        
        for symbol, features in features_dict.items():
            candidates.append((
                symbol,
                features.overall_score,
                features.investment_recommendation
            ))
        
        # 按综合评分排序
        candidates.sort(key=lambda x: x[1], reverse=True)
        return candidates[:top_n]
    
    def generate_feature_report(self, features: ComprehensiveFeatures) -> Dict:
        """生成特征分析报告"""
        return {
            'basic_info': {
                'symbol': features.symbol,
                'analysis_date': features.analysis_date.strftime('%Y-%m-%d %H:%M:%S'),
                'overall_score': f"{features.overall_score:.3f}",
                'explosive_potential': f"{features.explosive_potential:.3f}",
                'confidence_level': f"{features.confidence_level:.1%}",
                'investment_recommendation': features.investment_recommendation
            },
            'dimension_scores': {
                'fundamental': f"{features.fundamental_score:.3f}",
                'valuation': f"{features.valuation_score:.3f}",
                'technical': f"{features.technical_score:.3f}",
                'sentiment': f"{features.sentiment_score:.3f}",
                'risk': f"{features.risk_score:.3f}",
                'market': f"{features.market_score:.3f}"
            },
            'key_factors': features.key_factors,
            'risk_warnings': features.risk_warnings,
            'detailed_analysis': {
                'fundamental': features.fundamental_features,
                'valuation': features.valuation_features,
                'technical': features.technical_features,
                'sentiment': features.sentiment_features,
                'risk': features.risk_features,
                'market': features.market_features
            }
        }
    
    def update_weights(self, new_weights: Dict[str, float], weight_type: str = 'overall'):
        """更新权重配置"""
        if weight_type == 'overall':
            self.dimension_weights.update(new_weights)
        elif weight_type == 'explosive':
            self.explosive_weights.update(new_weights)
        
        logger.info(f"已更新{weight_type}权重配置: {new_weights}")
    
    def get_feature_statistics(self, features_dict: Dict[str, ComprehensiveFeatures]) -> Dict:
        """获取特征统计信息"""
        if not features_dict:
            return {}
        
        # 收集各维度评分
        scores = {
            'fundamental': [f.fundamental_score for f in features_dict.values()],
            'valuation': [f.valuation_score for f in features_dict.values()],
            'technical': [f.technical_score for f in features_dict.values()],
            'sentiment': [f.sentiment_score for f in features_dict.values()],
            'risk': [f.risk_score for f in features_dict.values()],
            'market': [f.market_score for f in features_dict.values()],
            'overall': [f.overall_score for f in features_dict.values()],
            'explosive': [f.explosive_potential for f in features_dict.values()]
        }
        
        # 计算统计信息
        statistics = {}
        for dimension, score_list in scores.items():
            statistics[dimension] = {
                'mean': np.mean(score_list),
                'std': np.std(score_list),
                'min': np.min(score_list),
                'max': np.max(score_list),
                'median': np.median(score_list)
            }
        
        # 投资建议分布
        recommendations = [f.investment_recommendation for f in features_dict.values()]
        recommendation_counts = {}
        for rec in recommendations:
            recommendation_counts[rec] = recommendation_counts.get(rec, 0) + 1
        
        statistics['recommendation_distribution'] = recommendation_counts
        statistics['total_stocks'] = len(features_dict)
        
        return statistics