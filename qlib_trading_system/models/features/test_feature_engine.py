"""
六维度特征工程系统集成测试
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# 直接导入模块，避免包导入问题
current_dir = os.path.dirname(__file__)
sys.path.insert(0, current_dir)

from feature_engine import FeatureEngine, ComprehensiveFeatures


def create_comprehensive_test_data():
    """创建综合测试数据"""
    # 财务数据
    financial_dates = pd.date_range(start='2020-01-01', end='2023-12-31', freq='QE')
    financial_data = pd.DataFrame({
        'date': financial_dates,
        'total_revenue': [1000 + i * 100 for i in range(len(financial_dates))],
        'net_profit': [100 + i * 15 for i in range(len(financial_dates))],
        'total_assets': [2000 + i * 200 for i in range(len(financial_dates))],
        'total_equity': [1200 + i * 120 for i in range(len(financial_dates))],
        'total_liabilities': [800 + i * 80 for i in range(len(financial_dates))],
        'current_assets': [800 + i * 80 for i in range(len(financial_dates))],
        'current_liabilities': [400 + i * 40 for i in range(len(financial_dates))],
        'gross_profit': [300 + i * 30 for i in range(len(financial_dates))],
        'eps': [1.0 + i * 0.15 for i in range(len(financial_dates))],
        'bvps': [12.0 + i * 1.2 for i in range(len(financial_dates))]
    }).set_index('date')
    
    # 价格数据
    price_dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
    np.random.seed(42)
    
    base_price = 25.0
    prices = [base_price]
    volumes = []
    
    for i in range(1, len(price_dates)):
        price_change = np.random.normal(0.001, 0.02)  # 轻微上升趋势
        new_price = prices[-1] * (1 + price_change)
        prices.append(max(new_price, 5.0))
        
        volume = np.random.uniform(800000, 2000000)
        volumes.append(volume)
    
    volumes.insert(0, 1000000)
    
    price_data = pd.DataFrame({
        'date': price_dates,
        'open': [p * (1 + np.random.normal(0, 0.005)) for p in prices],
        'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
        'close': prices,
        'volume': volumes
    }).set_index('date')
    
    # 市场数据
    market_data = {
        'current_price': 28.5,
        'shares_outstanding': 100,  # 100万股
        'industry_metrics': {
            'avg_pe': 22,
            'avg_pb': 2.8,
            'avg_ps': 3.5
        },
        'market_metrics': {
            'avg_pe': 20,
            'avg_pb': 2.5,
            'avg_ps': 3.0
        },
        'vix': 18.5,
        'put_call_ratio': 0.9,
        'advance_decline_ratio': 0.65
    }
    
    # 新闻数据（简化）
    news_data = [
        {
            'title': '公司业绩超预期，净利润大幅增长',
            'content': '公司发布财报显示业绩表现优异',
            'source': '财经新闻',
            'publish_time': datetime.now() - timedelta(days=1),
            'sentiment_score': 0.8,
            'keywords': ['业绩', '增长'],
            'impact_score': 0.7
        }
    ]
    
    # 社交媒体数据（简化）
    social_data = [
        {
            'platform': '雪球',
            'content': '这只股票技术面很强，看好后市',
            'author': '投资者A',
            'publish_time': datetime.now() - timedelta(hours=2),
            'likes': 150,
            'shares': 25,
            'comments': 30,
            'sentiment_score': 0.7,
            'influence_score': 0.6
        }
    ]
    
    # 宏观数据
    macro_data = {
        'gdp_growth': 5.5,
        'inflation_rate': 2.5,
        'interest_rate': 3.8,
        'unemployment_rate': 4.0,
        'pmi': 51.8,
        'consumer_confidence': 108.5
    }
    
    # 资金流向数据
    flow_data = {
        'northbound': {
            'net_inflow': 42.5,
            'daily_average': 9.8,
            'consistency_days': 5
        },
        'institutional': {
            'net_inflow': 156.8,
            'fund_inflow': 98.5,
            'insurance_inflow': 58.3
        },
        'retail': {
            'new_accounts': 32000,
            'trading_activity': 0.68
        }
    }
    
    # 政策数据
    policy_data = {
        'monetary': {
            'interest_rate_change': -0.05,
            'money_supply_growth': 11.2,
            'reserve_ratio_change': -0.25
        },
        'fiscal': {
            'deficit_ratio': 3.1,
            'tax_policy_change': -0.03,
            'infrastructure_spending': 0.12
        },
        'regulatory': {
            'intensity': 0.45,
            'new_regulations': 2,
            'enforcement_level': 0.55
        }
    }
    
    return {
        'financial_data': financial_data,
        'price_data': price_data,
        'market_data': market_data,
        'news_data': news_data,
        'social_data': social_data,
        'macro_data': macro_data,
        'flow_data': flow_data,
        'policy_data': policy_data
    }


def test_feature_engine_single_stock():
    """测试单只股票的特征提取"""
    print("=== 测试单只股票特征提取 ===")
    
    engine = FeatureEngine()
    test_data = create_comprehensive_test_data()
    
    try:
        features = engine.extract_comprehensive_features(
            symbol='TEST001',
            financial_data=test_data['financial_data'],
            price_data=test_data['price_data'],
            market_data=test_data['market_data'],
            news_data=test_data['news_data'],
            social_data=test_data['social_data'],
            macro_data=test_data['macro_data'],
            flow_data=test_data['flow_data'],
            policy_data=test_data['policy_data']
        )
        
        print(f"✓ 成功提取综合特征")
        print(f"✓ 股票代码: {features.symbol}")
        print(f"✓ 综合评分: {features.overall_score:.3f}")
        print(f"✓ 爆发潜力: {features.explosive_potential:.3f}")
        print(f"✓ 置信度: {features.confidence_level:.1%}")
        print(f"✓ 投资建议: {features.investment_recommendation}")
        
        # 显示六维度评分
        print("✓ 六维度评分:")
        print(f"   基本面: {features.fundamental_score:.3f}")
        print(f"   估值: {features.valuation_score:.3f}")
        print(f"   技术面: {features.technical_score:.3f}")
        print(f"   情绪: {features.sentiment_score:.3f}")
        print(f"   风险: {features.risk_score:.3f}")
        print(f"   大盘: {features.market_score:.3f}")
        
        # 显示关键因素
        if features.key_factors:
            print(f"✓ 关键因素: {', '.join(features.key_factors)}")
        
        # 显示风险警告
        if features.risk_warnings:
            print(f"✓ 风险警告: {', '.join(features.risk_warnings)}")
        
        return features
        
    except Exception as e:
        print(f"✗ 单只股票特征提取失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_batch_feature_extraction():
    """测试批量特征提取"""
    print("\n=== 测试批量特征提取 ===")
    
    engine = FeatureEngine()
    test_data = create_comprehensive_test_data()
    
    # 创建多只股票的测试数据
    batch_data = {}
    for i, symbol in enumerate(['TEST001', 'TEST002', 'TEST003']):
        # 为每只股票创建略有不同的数据
        stock_data = test_data.copy()
        
        # 调整价格数据
        price_adjustment = 1 + (i - 1) * 0.1  # -10%, 0%, +10%
        stock_data['price_data'] = stock_data['price_data'] * price_adjustment
        
        # 调整市场数据
        stock_data['market_data'] = stock_data['market_data'].copy()
        stock_data['market_data']['current_price'] *= price_adjustment
        
        batch_data[symbol] = stock_data
    
    try:
        batch_results = engine.batch_feature_extraction(batch_data)
        
        print(f"✓ 成功批量提取特征，处理了 {len(batch_results)} 只股票")
        
        # 显示每只股票的基本信息
        for symbol, features in batch_results.items():
            print(f"✓ {symbol}: 综合评分={features.overall_score:.3f}, "
                  f"爆发潜力={features.explosive_potential:.3f}, "
                  f"建议={features.investment_recommendation}")
        
        return batch_results
        
    except Exception as e:
        print(f"✗ 批量特征提取失败: {e}")
        return None


def test_ranking_and_selection():
    """测试排名和选股功能"""
    print("\n=== 测试排名和选股功能 ===")
    
    engine = FeatureEngine()
    
    # 使用批量提取的结果
    batch_results = test_batch_feature_extraction()
    if not batch_results:
        print("✗ 无法进行排名测试，批量提取失败")
        return
    
    try:
        # 获取爆发潜力最高的股票
        top_explosive = engine.get_top_explosive_stocks(batch_results, top_n=5, min_confidence=0.5)
        
        print("✓ 爆发潜力排名:")
        for i, (symbol, explosive_score, confidence) in enumerate(top_explosive, 1):
            print(f"   {i}. {symbol}: 爆发潜力={explosive_score:.3f}, 置信度={confidence:.1%}")
        
        # 获取综合排名
        comprehensive_ranking = engine.get_comprehensive_ranking(batch_results, top_n=5)
        
        print("✓ 综合评分排名:")
        for i, (symbol, overall_score, recommendation) in enumerate(comprehensive_ranking, 1):
            print(f"   {i}. {symbol}: 综合评分={overall_score:.3f}, 建议={recommendation}")
        
        return top_explosive, comprehensive_ranking
        
    except Exception as e:
        print(f"✗ 排名和选股测试失败: {e}")
        return None, None


def test_feature_report():
    """测试特征报告生成"""
    print("\n=== 测试特征报告生成 ===")
    
    engine = FeatureEngine()
    features = test_feature_engine_single_stock()
    
    if not features:
        print("✗ 无法生成报告，特征提取失败")
        return
    
    try:
        report = engine.generate_feature_report(features)
        
        print("✓ 成功生成特征分析报告")
        print("✓ 基本信息:")
        basic_info = report['basic_info']
        for key, value in basic_info.items():
            print(f"   {key}: {value}")
        
        print("✓ 维度评分:")
        dimension_scores = report['dimension_scores']
        for dimension, score in dimension_scores.items():
            print(f"   {dimension}: {score}")
        
        return report
        
    except Exception as e:
        print(f"✗ 特征报告生成失败: {e}")
        return None


def test_feature_statistics():
    """测试特征统计功能"""
    print("\n=== 测试特征统计功能 ===")
    
    engine = FeatureEngine()
    
    # 使用批量提取的结果
    batch_results = test_batch_feature_extraction()
    if not batch_results:
        print("✗ 无法进行统计测试，批量提取失败")
        return
    
    try:
        statistics = engine.get_feature_statistics(batch_results)
        
        print("✓ 成功生成特征统计信息")
        print(f"✓ 总股票数: {statistics['total_stocks']}")
        
        # 显示各维度统计
        print("✓ 各维度评分统计:")
        for dimension in ['fundamental', 'valuation', 'technical', 'sentiment', 'risk', 'market']:
            if dimension in statistics:
                stats = statistics[dimension]
                print(f"   {dimension}: 均值={stats['mean']:.3f}, "
                      f"标准差={stats['std']:.3f}, "
                      f"范围=[{stats['min']:.3f}, {stats['max']:.3f}]")
        
        # 显示投资建议分布
        print("✓ 投资建议分布:")
        rec_dist = statistics.get('recommendation_distribution', {})
        for recommendation, count in rec_dist.items():
            print(f"   {recommendation}: {count}只")
        
        return statistics
        
    except Exception as e:
        print(f"✗ 特征统计测试失败: {e}")
        return None


def test_weight_update():
    """测试权重更新功能"""
    print("\n=== 测试权重更新功能 ===")
    
    engine = FeatureEngine()
    
    try:
        # 显示原始权重
        print("✓ 原始综合评分权重:")
        for dimension, weight in engine.dimension_weights.items():
            print(f"   {dimension}: {weight}")
        
        # 更新权重
        new_weights = {
            'technical': 0.35,  # 提高技术面权重
            'sentiment': 0.20   # 提高情绪权重
        }
        
        engine.update_weights(new_weights, 'overall')
        
        print("✓ 更新后的综合评分权重:")
        for dimension, weight in engine.dimension_weights.items():
            print(f"   {dimension}: {weight}")
        
        # 测试爆发潜力权重更新
        explosive_weights = {
            'technical': 0.40,  # 进一步提高技术面权重
            'sentiment': 0.25   # 进一步提高情绪权重
        }
        
        engine.update_weights(explosive_weights, 'explosive')
        
        print("✓ 更新后的爆发潜力权重:")
        for dimension, weight in engine.explosive_weights.items():
            print(f"   {dimension}: {weight}")
        
        return True
        
    except Exception as e:
        print(f"✗ 权重更新测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始测试六维度特征工程系统集成引擎...")
    print("=" * 60)
    
    # 运行所有测试
    single_features = test_feature_engine_single_stock()
    batch_results = test_batch_feature_extraction()
    ranking_results = test_ranking_and_selection()
    report = test_feature_report()
    statistics = test_feature_statistics()
    weight_update = test_weight_update()
    
    print("\n" + "=" * 60)
    if all([single_features, batch_results, ranking_results[0] if ranking_results else None, 
            report, statistics, weight_update]):
        print("✓ 所有测试通过！六维度特征工程系统集成引擎工作正常")
        print("✓ 系统已准备好为股票筛选AI模型提供全面的特征数据")
    else:
        print("✗ 部分测试失败，请检查相关模块")


if __name__ == "__main__":
    main()