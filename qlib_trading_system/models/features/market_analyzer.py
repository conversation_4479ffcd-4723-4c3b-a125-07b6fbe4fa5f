"""
大盘走势预测特征模块

实现宏观经济指标分析模块
构建市场技术面综合评估系统
编写资金流向分析算法
实现政策影响因子量化评估
专注于预测大盘整体走势和市场环境
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union
from datetime import datetime, timedelta
import logging
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class MarketTrend(Enum):
    """市场趋势枚举"""
    STRONG_BULL = 5
    BULL = 4
    NEUTRAL = 3
    BEAR = 2
    STRONG_BEAR = 1


class MarketPhase(Enum):
    """市场阶段枚举"""
    BOTTOM = "bottom"
    RECOVERY = "recovery"
    EXPANSION = "expansion"
    PEAK = "peak"
    DECLINE = "decline"


@dataclass
class MarketForecast:
    """市场预测数据结构"""
    forecast_date: datetime
    trend_direction: MarketTrend
    market_phase: MarketPhase
    confidence_level: float
    time_horizon: str
    key_factors: List[str]
    risk_factors: List[str]
    target_range: Tuple[float, float]


class MacroEconomicAnalyzer:
    """宏观经济指标分析器"""
    
    def __init__(self):
        self.indicator_weights = {
            'gdp_growth': 0.25,
            'inflation_rate': 0.20,
            'interest_rate': 0.20,
            'unemployment_rate': 0.15,
            'pmi': 0.10,
            'consumer_confidence': 0.10
        }
        
        self.benchmark_values = {
            'gdp_growth': 6.0,      # 基准GDP增长率6%
            'inflation_rate': 3.0,   # 基准通胀率3%
            'interest_rate': 4.0,    # 基准利率4%
            'unemployment_rate': 5.0, # 基准失业率5%
            'pmi': 50.0,            # PMI基准值50
            'consumer_confidence': 100.0  # 消费者信心指数基准100
        }
    
    def analyze_macro_indicators(self, macro_data: Dict) -> Dict:
        """分析宏观经济指标"""
        try:
            indicator_scores = {}
            
            # 分析各项宏观指标
            for indicator, weight in self.indicator_weights.items():
                if indicator in macro_data:
                    score = self._score_indicator(indicator, macro_data[indicator])
                    indicator_scores[indicator] = score
                else:
                    indicator_scores[indicator] = 0.5  # 缺失数据给中性评分
            
            # 计算综合宏观经济评分
            macro_score = sum(score * self.indicator_weights[indicator] 
                            for indicator, score in indicator_scores.items())
            
            # 分析经济周期阶段
            economic_cycle = self._analyze_economic_cycle(macro_data)
            
            # 识别关键驱动因素
            key_drivers = self._identify_key_drivers(indicator_scores, macro_data)
            
            # 预测经济趋势
            economic_outlook = self._forecast_economic_trend(indicator_scores, macro_data)
            
            return {
                'macro_score': macro_score,
                'indicator_scores': indicator_scores,
                'economic_cycle': economic_cycle,
                'key_drivers': key_drivers,
                'economic_outlook': economic_outlook,
                'analysis_date': datetime.now()
            }
            
        except Exception as e:
            logger.error(f"宏观经济分析失败: {e}")
            return {'macro_score': 0.5, 'error': str(e)}
    
    def _score_indicator(self, indicator: str, value: float) -> float:
        """对单个宏观指标评分"""
        benchmark = self.benchmark_values.get(indicator, 0)
        
        if indicator == 'gdp_growth':
            # GDP增长率：越高越好，但过高可能有通胀风险
            if value > 8:
                return 0.8  # 过热
            elif value > 6:
                return 1.0  # 理想
            elif value > 4:
                return 0.7  # 良好
            elif value > 2:
                return 0.4  # 偏低
            else:
                return 0.2  # 衰退
        
        elif indicator == 'inflation_rate':
            # 通胀率：适中最好
            if abs(value - 3.0) < 1:
                return 1.0  # 理想范围2-4%
            elif abs(value - 3.0) < 2:
                return 0.7  # 可接受范围1-5%
            elif value > 6:
                return 0.2  # 高通胀
            elif value < 0:
                return 0.3  # 通缩
            else:
                return 0.5
        
        elif indicator == 'interest_rate':
            # 利率：适中最好，过高过低都不利
            if 3 <= value <= 5:
                return 1.0
            elif 2 <= value <= 6:
                return 0.7
            elif value > 8:
                return 0.2  # 过高抑制经济
            elif value < 1:
                return 0.4  # 过低可能有泡沫风险
            else:
                return 0.5
        
        elif indicator == 'unemployment_rate':
            # 失业率：越低越好
            if value < 4:
                return 1.0
            elif value < 6:
                return 0.7
            elif value < 8:
                return 0.4
            else:
                return 0.2
        
        elif indicator == 'pmi':
            # PMI：50以上扩张，50以下收缩
            if value > 55:
                return 1.0
            elif value > 50:
                return 0.8
            elif value > 45:
                return 0.4
            else:
                return 0.2
        
        elif indicator == 'consumer_confidence':
            # 消费者信心指数：越高越好
            if value > 110:
                return 1.0
            elif value > 100:
                return 0.8
            elif value > 90:
                return 0.6
            elif value > 80:
                return 0.4
            else:
                return 0.2
        
        return 0.5  # 默认中性评分
    
    def _analyze_economic_cycle(self, macro_data: Dict) -> str:
        """分析经济周期阶段"""
        gdp_growth = macro_data.get('gdp_growth', 0)
        inflation = macro_data.get('inflation_rate', 0)
        unemployment = macro_data.get('unemployment_rate', 5)
        
        if gdp_growth > 6 and inflation < 4 and unemployment < 5:
            return 'expansion'  # 扩张期
        elif gdp_growth > 4 and inflation > 4:
            return 'peak'  # 过热期
        elif gdp_growth < 2 and unemployment > 6:
            return 'recession'  # 衰退期
        elif gdp_growth > 2 and unemployment > 6:
            return 'recovery'  # 复苏期
        else:
            return 'stable'  # 稳定期
    
    def _identify_key_drivers(self, indicator_scores: Dict, macro_data: Dict) -> List[str]:
        """识别关键驱动因素"""
        drivers = []
        
        # 找出评分最高和最低的指标
        sorted_indicators = sorted(indicator_scores.items(), key=lambda x: x[1], reverse=True)
        
        # 正面驱动因素
        if sorted_indicators[0][1] > 0.8:
            drivers.append(f"{sorted_indicators[0][0]}表现强劲")
        
        # 负面驱动因素
        if sorted_indicators[-1][1] < 0.3:
            drivers.append(f"{sorted_indicators[-1][0]}表现疲弱")
        
        # 特殊情况分析
        if macro_data.get('inflation_rate', 0) > 5:
            drivers.append('通胀压力较大')
        
        if macro_data.get('interest_rate', 0) > 6:
            drivers.append('货币政策偏紧')
        
        return drivers
    
    def _forecast_economic_trend(self, indicator_scores: Dict, macro_data: Dict) -> str:
        """预测经济趋势"""
        avg_score = np.mean(list(indicator_scores.values()))
        
        # 分析趋势变化
        gdp_trend = macro_data.get('gdp_growth_trend', 0)  # 假设有趋势数据
        inflation_trend = macro_data.get('inflation_trend', 0)
        
        if avg_score > 0.7:
            if gdp_trend > 0:
                return 'accelerating_growth'
            else:
                return 'stable_growth'
        elif avg_score > 0.5:
            return 'moderate_growth'
        elif avg_score > 0.3:
            return 'slow_growth'
        else:
            return 'economic_slowdown'


class MarketTechnicalAnalyzer:
    """市场技术面综合评估系统"""
    
    def __init__(self):
        self.technical_weights = {
            'trend_strength': 0.25,
            'momentum': 0.20,
            'breadth': 0.20,
            'volume': 0.15,
            'volatility': 0.10,
            'sentiment': 0.10
        }
    
    def analyze_market_technical(self, market_data: Dict, index_data: pd.DataFrame) -> Dict:
        """分析市场技术面"""
        try:
            technical_scores = {}
            
            # 趋势强度分析
            trend_analysis = self._analyze_trend_strength(index_data)
            technical_scores['trend_strength'] = trend_analysis['score']
            
            # 动量分析
            momentum_analysis = self._analyze_momentum(index_data)
            technical_scores['momentum'] = momentum_analysis['score']
            
            # 市场广度分析
            breadth_analysis = self._analyze_market_breadth(market_data)
            technical_scores['breadth'] = breadth_analysis['score']
            
            # 成交量分析
            volume_analysis = self._analyze_volume_pattern(index_data)
            technical_scores['volume'] = volume_analysis['score']
            
            # 波动率分析
            volatility_analysis = self._analyze_volatility(index_data)
            technical_scores['volatility'] = volatility_analysis['score']
            
            # 市场情绪分析
            sentiment_analysis = self._analyze_market_sentiment(market_data)
            technical_scores['sentiment'] = sentiment_analysis['score']
            
            # 计算综合技术面评分
            technical_score = sum(score * self.technical_weights[indicator] 
                                for indicator, score in technical_scores.items())
            
            # 识别技术形态
            technical_pattern = self._identify_technical_pattern(index_data)
            
            # 支撑阻力分析
            support_resistance = self._analyze_support_resistance(index_data)
            
            return {
                'technical_score': technical_score,
                'component_scores': technical_scores,
                'trend_analysis': trend_analysis,
                'momentum_analysis': momentum_analysis,
                'breadth_analysis': breadth_analysis,
                'volume_analysis': volume_analysis,
                'volatility_analysis': volatility_analysis,
                'sentiment_analysis': sentiment_analysis,
                'technical_pattern': technical_pattern,
                'support_resistance': support_resistance,
                'analysis_date': datetime.now()
            }
            
        except Exception as e:
            logger.error(f"市场技术面分析失败: {e}")
            return {'technical_score': 0.5, 'error': str(e)}
    
    def _analyze_trend_strength(self, data: pd.DataFrame) -> Dict:
        """分析趋势强度"""
        if data.empty or 'close' not in data.columns:
            return {'score': 0.5, 'direction': 'neutral', 'strength': 0}
        
        # 计算多个时间周期的移动平均线
        data['ma5'] = data['close'].rolling(5).mean()
        data['ma20'] = data['close'].rolling(20).mean()
        data['ma60'] = data['close'].rolling(60).mean()
        
        latest = data.iloc[-1]
        current_price = latest['close']
        
        # 均线排列分析
        ma_alignment_score = 0
        if current_price > latest['ma5'] > latest['ma20'] > latest['ma60']:
            ma_alignment_score = 1.0  # 多头排列
            direction = 'bullish'
        elif current_price < latest['ma5'] < latest['ma20'] < latest['ma60']:
            ma_alignment_score = 0.0  # 空头排列
            direction = 'bearish'
        else:
            ma_alignment_score = 0.5  # 混乱排列
            direction = 'neutral'
        
        # 趋势斜率分析
        if len(data) >= 20:
            ma20_slope = (latest['ma20'] - data['ma20'].iloc[-20]) / data['ma20'].iloc[-20]
            slope_score = max(min((ma20_slope + 0.1) / 0.2, 1), 0)  # 标准化到0-1
        else:
            slope_score = 0.5
        
        # 综合趋势强度
        trend_strength = (ma_alignment_score * 0.6 + slope_score * 0.4)
        
        return {
            'score': trend_strength,
            'direction': direction,
            'strength': abs(ma20_slope) if 'ma20_slope' in locals() else 0,
            'ma_alignment': ma_alignment_score
        }
    
    def _analyze_momentum(self, data: pd.DataFrame) -> Dict:
        """分析市场动量"""
        if data.empty or len(data) < 14:
            return {'score': 0.5, 'momentum': 'neutral'}
        
        # 计算RSI
        delta = data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        latest_rsi = rsi.iloc[-1] if not pd.isna(rsi.iloc[-1]) else 50
        
        # RSI评分
        if 40 <= latest_rsi <= 60:
            rsi_score = 1.0  # 健康区间
        elif 30 <= latest_rsi <= 70:
            rsi_score = 0.8  # 正常区间
        elif latest_rsi > 80:
            rsi_score = 0.2  # 超买
        elif latest_rsi < 20:
            rsi_score = 0.2  # 超卖
        else:
            rsi_score = 0.6
        
        # 价格动量
        if len(data) >= 20:
            price_momentum = (data['close'].iloc[-1] - data['close'].iloc[-20]) / data['close'].iloc[-20]
            momentum_score = max(min((price_momentum + 0.1) / 0.2, 1), 0)
        else:
            momentum_score = 0.5
        
        overall_momentum = (rsi_score * 0.5 + momentum_score * 0.5)
        
        return {
            'score': overall_momentum,
            'rsi': latest_rsi,
            'price_momentum': price_momentum if 'price_momentum' in locals() else 0,
            'momentum': 'bullish' if overall_momentum > 0.6 else 'bearish' if overall_momentum < 0.4 else 'neutral'
        }
    
    def _analyze_market_breadth(self, market_data: Dict) -> Dict:
        """分析市场广度"""
        # 上涨下跌股票比例
        advance_decline_ratio = market_data.get('advance_decline_ratio', 0.5)
        
        # 涨停跌停比例
        limit_up_count = market_data.get('limit_up_count', 0)
        limit_down_count = market_data.get('limit_down_count', 0)
        total_stocks = market_data.get('total_stocks', 4000)
        
        limit_ratio = (limit_up_count - limit_down_count) / total_stocks if total_stocks > 0 else 0
        
        # 新高新低比例
        new_high_count = market_data.get('new_high_count', 0)
        new_low_count = market_data.get('new_low_count', 0)
        new_high_low_ratio = (new_high_count - new_low_count) / total_stocks if total_stocks > 0 else 0
        
        # 综合广度评分
        breadth_score = (
            advance_decline_ratio * 0.5 +
            max(min((limit_ratio + 0.02) / 0.04, 1), 0) * 0.3 +
            max(min((new_high_low_ratio + 0.01) / 0.02, 1), 0) * 0.2
        )
        
        return {
            'score': breadth_score,
            'advance_decline_ratio': advance_decline_ratio,
            'limit_ratio': limit_ratio,
            'new_high_low_ratio': new_high_low_ratio,
            'breadth': 'strong' if breadth_score > 0.7 else 'weak' if breadth_score < 0.3 else 'moderate'
        }
    
    def _analyze_volume_pattern(self, data: pd.DataFrame) -> Dict:
        """分析成交量模式"""
        if data.empty or 'volume' not in data.columns:
            return {'score': 0.5, 'pattern': 'neutral'}
        
        # 计算成交量移动平均
        data['vol_ma20'] = data['volume'].rolling(20).mean()
        
        # 最近成交量与平均值比较
        recent_volume = data['volume'].tail(5).mean()
        avg_volume = data['vol_ma20'].iloc[-1] if not pd.isna(data['vol_ma20'].iloc[-1]) else recent_volume
        
        volume_ratio = recent_volume / avg_volume if avg_volume > 0 else 1
        
        # 量价关系分析
        price_change = data['close'].pct_change().tail(5).mean()
        volume_change = data['volume'].pct_change().tail(5).mean()
        
        # 量价配合度
        if price_change > 0 and volume_change > 0:
            volume_price_sync = 1.0  # 价涨量增
        elif price_change < 0 and volume_change < 0:
            volume_price_sync = 0.8  # 价跌量减
        elif price_change > 0 and volume_change < 0:
            volume_price_sync = 0.3  # 价涨量减（背离）
        elif price_change < 0 and volume_change > 0:
            volume_price_sync = 0.2  # 价跌量增（背离）
        else:
            volume_price_sync = 0.5
        
        # 综合成交量评分
        volume_score = (
            max(min(volume_ratio / 2, 1), 0) * 0.6 +
            volume_price_sync * 0.4
        )
        
        return {
            'score': volume_score,
            'volume_ratio': volume_ratio,
            'volume_price_sync': volume_price_sync,
            'pattern': 'healthy' if volume_score > 0.6 else 'concerning' if volume_score < 0.4 else 'neutral'
        }
    
    def _analyze_volatility(self, data: pd.DataFrame) -> Dict:
        """分析波动率"""
        if data.empty or len(data) < 20:
            return {'score': 0.5, 'level': 'normal'}
        
        # 计算历史波动率
        returns = data['close'].pct_change().dropna()
        volatility = returns.std() * np.sqrt(252)  # 年化波动率
        
        # 波动率评分（适中的波动率最好）
        if 0.15 <= volatility <= 0.25:
            vol_score = 1.0  # 理想波动率
        elif 0.10 <= volatility <= 0.35:
            vol_score = 0.8  # 可接受波动率
        elif volatility > 0.5:
            vol_score = 0.2  # 过高波动率
        elif volatility < 0.05:
            vol_score = 0.4  # 过低波动率（可能缺乏活力）
        else:
            vol_score = 0.6
        
        # 波动率趋势
        recent_vol = returns.tail(20).std() * np.sqrt(252)
        historical_vol = returns.head(-20).std() * np.sqrt(252) if len(returns) > 40 else volatility
        
        vol_trend = 'increasing' if recent_vol > historical_vol * 1.2 else 'decreasing' if recent_vol < historical_vol * 0.8 else 'stable'
        
        return {
            'score': vol_score,
            'volatility': volatility,
            'recent_volatility': recent_vol,
            'trend': vol_trend,
            'level': 'high' if volatility > 0.3 else 'low' if volatility < 0.1 else 'normal'
        }
    
    def _analyze_market_sentiment(self, market_data: Dict) -> Dict:
        """分析市场情绪"""
        # VIX恐慌指数
        vix = market_data.get('vix', 20)
        vix_score = max(min((30 - vix) / 20, 1), 0)  # VIX越低情绪越好
        
        # 看跌看涨比率
        put_call_ratio = market_data.get('put_call_ratio', 1.0)
        put_call_score = max(min((1.2 - put_call_ratio) / 0.4, 1), 0)  # 比率越低情绪越好
        
        # 融资融券比例
        margin_ratio = market_data.get('margin_ratio', 0.5)
        margin_score = max(min(margin_ratio / 0.8, 1), 0)  # 融资比例适中最好
        
        # 综合情绪评分
        sentiment_score = (vix_score * 0.4 + put_call_score * 0.4 + margin_score * 0.2)
        
        return {
            'score': sentiment_score,
            'vix': vix,
            'put_call_ratio': put_call_ratio,
            'margin_ratio': margin_ratio,
            'sentiment': 'bullish' if sentiment_score > 0.6 else 'bearish' if sentiment_score < 0.4 else 'neutral'
        }
    
    def _identify_technical_pattern(self, data: pd.DataFrame) -> str:
        """识别技术形态"""
        if data.empty or len(data) < 50:
            return 'insufficient_data'
        
        # 简化的形态识别
        recent_high = data['close'].tail(20).max()
        recent_low = data['close'].tail(20).min()
        current_price = data['close'].iloc[-1]
        
        # 计算价格位置
        price_position = (current_price - recent_low) / (recent_high - recent_low) if recent_high != recent_low else 0.5
        
        # 趋势判断
        ma20 = data['close'].rolling(20).mean().iloc[-1]
        ma60 = data['close'].rolling(60).mean().iloc[-1]
        
        if current_price > ma20 > ma60 and price_position > 0.7:
            return 'uptrend_strong'
        elif current_price > ma20 > ma60:
            return 'uptrend'
        elif current_price < ma20 < ma60 and price_position < 0.3:
            return 'downtrend_strong'
        elif current_price < ma20 < ma60:
            return 'downtrend'
        elif 0.4 <= price_position <= 0.6:
            return 'consolidation'
        else:
            return 'neutral'
    
    def _analyze_support_resistance(self, data: pd.DataFrame) -> Dict:
        """分析支撑阻力位"""
        if data.empty or len(data) < 50:
            return {'support': 0, 'resistance': 0, 'current_position': 0.5}
        
        # 计算近期高低点
        recent_data = data.tail(60)
        
        # 支撑位（近期低点）
        support_level = recent_data['low'].min()
        
        # 阻力位（近期高点）
        resistance_level = recent_data['high'].max()
        
        # 当前价格位置
        current_price = data['close'].iloc[-1]
        if resistance_level != support_level:
            current_position = (current_price - support_level) / (resistance_level - support_level)
        else:
            current_position = 0.5
        
        return {
            'support': support_level,
            'resistance': resistance_level,
            'current_position': current_position,
            'distance_to_support': (current_price - support_level) / current_price if current_price > 0 else 0,
            'distance_to_resistance': (resistance_level - current_price) / current_price if current_price > 0 else 0
        }


class FundFlowAnalyzer:
    """资金流向分析算法"""
    
    def __init__(self):
        self.flow_categories = {
            'northbound': 0.30,      # 北向资金权重
            'institutional': 0.25,   # 机构资金权重
            'retail': 0.20,          # 散户资金权重
            'hot_money': 0.15,       # 游资权重
            'margin': 0.10           # 融资资金权重
        }
    
    def analyze_fund_flow(self, flow_data: Dict) -> Dict:
        """分析资金流向"""
        try:
            flow_scores = {}
            
            # 北向资金分析
            northbound_analysis = self._analyze_northbound_flow(flow_data.get('northbound', {}))
            flow_scores['northbound'] = northbound_analysis['score']
            
            # 机构资金分析
            institutional_analysis = self._analyze_institutional_flow(flow_data.get('institutional', {}))
            flow_scores['institutional'] = institutional_analysis['score']
            
            # 散户资金分析
            retail_analysis = self._analyze_retail_flow(flow_data.get('retail', {}))
            flow_scores['retail'] = retail_analysis['score']
            
            # 游资分析
            hot_money_analysis = self._analyze_hot_money_flow(flow_data.get('hot_money', {}))
            flow_scores['hot_money'] = hot_money_analysis['score']
            
            # 融资资金分析
            margin_analysis = self._analyze_margin_flow(flow_data.get('margin', {}))
            flow_scores['margin'] = margin_analysis['score']
            
            # 计算综合资金流向评分
            overall_flow_score = sum(score * self.flow_categories[category] 
                                   for category, score in flow_scores.items())
            
            # 识别主导资金类型
            dominant_fund_type = max(flow_scores.items(), key=lambda x: x[1])
            
            # 资金流向趋势分析
            flow_trend = self._analyze_flow_trend(flow_data)
            
            return {
                'overall_flow_score': overall_flow_score,
                'flow_scores': flow_scores,
                'northbound_analysis': northbound_analysis,
                'institutional_analysis': institutional_analysis,
                'retail_analysis': retail_analysis,
                'hot_money_analysis': hot_money_analysis,
                'margin_analysis': margin_analysis,
                'dominant_fund_type': dominant_fund_type[0],
                'flow_trend': flow_trend,
                'analysis_date': datetime.now()
            }
            
        except Exception as e:
            logger.error(f"资金流向分析失败: {e}")
            return {'overall_flow_score': 0.5, 'error': str(e)}
    
    def _analyze_northbound_flow(self, northbound_data: Dict) -> Dict:
        """分析北向资金流向"""
        net_inflow = northbound_data.get('net_inflow', 0)  # 净流入金额（亿元）
        daily_average = northbound_data.get('daily_average', 0)  # 日均流入
        
        # 北向资金评分
        if net_inflow > 50:
            score = 1.0  # 大幅净流入
        elif net_inflow > 20:
            score = 0.8  # 显著净流入
        elif net_inflow > 0:
            score = 0.6  # 净流入
        elif net_inflow > -20:
            score = 0.4  # 小幅净流出
        else:
            score = 0.2  # 大幅净流出
        
        # 持续性分析
        consistency = northbound_data.get('consistency_days', 0)  # 连续流入天数
        if consistency > 5:
            score = min(score + 0.1, 1.0)
        elif consistency < -5:
            score = max(score - 0.1, 0.0)
        
        return {
            'score': score,
            'net_inflow': net_inflow,
            'daily_average': daily_average,
            'consistency': consistency,
            'trend': 'inflow' if net_inflow > 0 else 'outflow'
        }
    
    def _analyze_institutional_flow(self, institutional_data: Dict) -> Dict:
        """分析机构资金流向"""
        net_inflow = institutional_data.get('net_inflow', 0)
        fund_inflow = institutional_data.get('fund_inflow', 0)  # 基金净流入
        insurance_inflow = institutional_data.get('insurance_inflow', 0)  # 保险资金
        
        # 机构资金评分
        total_inflow = net_inflow + fund_inflow + insurance_inflow
        
        if total_inflow > 100:
            score = 1.0
        elif total_inflow > 50:
            score = 0.8
        elif total_inflow > 0:
            score = 0.6
        elif total_inflow > -50:
            score = 0.4
        else:
            score = 0.2
        
        return {
            'score': score,
            'total_inflow': total_inflow,
            'fund_inflow': fund_inflow,
            'insurance_inflow': insurance_inflow,
            'trend': 'bullish' if total_inflow > 0 else 'bearish'
        }
    
    def _analyze_retail_flow(self, retail_data: Dict) -> Dict:
        """分析散户资金流向"""
        new_accounts = retail_data.get('new_accounts', 0)  # 新开户数
        trading_activity = retail_data.get('trading_activity', 0.5)  # 交易活跃度
        
        # 散户情绪评分（反向指标，散户过度乐观时要谨慎）
        if new_accounts > 50000:  # 新开户过多
            score = 0.3  # 可能是顶部信号
        elif new_accounts > 20000:
            score = 0.6
        elif new_accounts > 10000:
            score = 0.8
        else:
            score = 0.5
        
        # 交易活跃度调整
        if trading_activity > 0.8:
            score *= 0.8  # 过度活跃降低评分
        elif trading_activity < 0.3:
            score *= 1.2  # 低活跃度提高评分
        
        return {
            'score': min(score, 1.0),
            'new_accounts': new_accounts,
            'trading_activity': trading_activity,
            'sentiment': 'euphoric' if new_accounts > 50000 else 'normal'
        }
    
    def _analyze_hot_money_flow(self, hot_money_data: Dict) -> Dict:
        """分析游资流向"""
        net_inflow = hot_money_data.get('net_inflow', 0)
        activity_level = hot_money_data.get('activity_level', 0.5)  # 活跃程度
        
        # 游资评分
        if net_inflow > 30:
            score = 0.8  # 游资大幅流入通常是短期利好
        elif net_inflow > 10:
            score = 0.7
        elif net_inflow > 0:
            score = 0.6
        else:
            score = 0.4
        
        # 活跃度调整
        if activity_level > 0.8:
            score = min(score + 0.1, 1.0)
        
        return {
            'score': score,
            'net_inflow': net_inflow,
            'activity_level': activity_level,
            'trend': 'active' if activity_level > 0.6 else 'quiet'
        }
    
    def _analyze_margin_flow(self, margin_data: Dict) -> Dict:
        """分析融资资金流向"""
        margin_balance = margin_data.get('balance', 0)  # 融资余额
        margin_buy = margin_data.get('buy_amount', 0)  # 融资买入额
        margin_repay = margin_data.get('repay_amount', 0)  # 融资偿还额
        
        net_margin = margin_buy - margin_repay
        
        # 融资评分
        if net_margin > 50:
            score = 0.8  # 融资净买入
        elif net_margin > 0:
            score = 0.6
        elif net_margin > -50:
            score = 0.4
        else:
            score = 0.2  # 大幅偿还
        
        return {
            'score': score,
            'net_margin': net_margin,
            'balance': margin_balance,
            'trend': 'expanding' if net_margin > 0 else 'contracting'
        }
    
    def _analyze_flow_trend(self, flow_data: Dict) -> str:
        """分析资金流向趋势"""
        # 综合各类资金流向判断整体趋势
        northbound_trend = flow_data.get('northbound', {}).get('net_inflow', 0)
        institutional_trend = flow_data.get('institutional', {}).get('net_inflow', 0)
        
        total_smart_money = northbound_trend + institutional_trend
        
        if total_smart_money > 100:
            return 'strong_inflow'
        elif total_smart_money > 50:
            return 'moderate_inflow'
        elif total_smart_money > 0:
            return 'weak_inflow'
        elif total_smart_money > -50:
            return 'weak_outflow'
        else:
            return 'strong_outflow'


class PolicyImpactAnalyzer:
    """政策影响因子量化评估器"""
    
    def __init__(self):
        self.policy_categories = {
            'monetary_policy': 0.30,     # 货币政策
            'fiscal_policy': 0.25,       # 财政政策
            'regulatory_policy': 0.20,   # 监管政策
            'industry_policy': 0.15,     # 产业政策
            'trade_policy': 0.10         # 贸易政策
        }
        
        self.policy_impact_weights = {
            'positive': 1.0,
            'neutral': 0.5,
            'negative': 0.0
        }
    
    def analyze_policy_impact(self, policy_data: Dict) -> Dict:
        """分析政策影响"""
        try:
            policy_scores = {}
            
            # 货币政策分析
            monetary_analysis = self._analyze_monetary_policy(policy_data.get('monetary', {}))
            policy_scores['monetary_policy'] = monetary_analysis['score']
            
            # 财政政策分析
            fiscal_analysis = self._analyze_fiscal_policy(policy_data.get('fiscal', {}))
            policy_scores['fiscal_policy'] = fiscal_analysis['score']
            
            # 监管政策分析
            regulatory_analysis = self._analyze_regulatory_policy(policy_data.get('regulatory', {}))
            policy_scores['regulatory_policy'] = regulatory_analysis['score']
            
            # 产业政策分析
            industry_analysis = self._analyze_industry_policy(policy_data.get('industry', {}))
            policy_scores['industry_policy'] = industry_analysis['score']
            
            # 贸易政策分析
            trade_analysis = self._analyze_trade_policy(policy_data.get('trade', {}))
            policy_scores['trade_policy'] = trade_analysis['score']
            
            # 计算综合政策影响评分
            overall_policy_score = sum(score * self.policy_categories[category] 
                                     for category, score in policy_scores.items())
            
            # 识别关键政策驱动因素
            key_policy_drivers = self._identify_key_policy_drivers(
                monetary_analysis, fiscal_analysis, regulatory_analysis, 
                industry_analysis, trade_analysis
            )
            
            # 政策预期分析
            policy_expectation = self._analyze_policy_expectation(policy_data)
            
            return {
                'overall_policy_score': overall_policy_score,
                'policy_scores': policy_scores,
                'monetary_analysis': monetary_analysis,
                'fiscal_analysis': fiscal_analysis,
                'regulatory_analysis': regulatory_analysis,
                'industry_analysis': industry_analysis,
                'trade_analysis': trade_analysis,
                'key_policy_drivers': key_policy_drivers,
                'policy_expectation': policy_expectation,
                'analysis_date': datetime.now()
            }
            
        except Exception as e:
            logger.error(f"政策影响分析失败: {e}")
            return {'overall_policy_score': 0.5, 'error': str(e)}
    
    def _analyze_monetary_policy(self, monetary_data: Dict) -> Dict:
        """分析货币政策"""
        interest_rate_change = monetary_data.get('interest_rate_change', 0)
        money_supply_growth = monetary_data.get('money_supply_growth', 0)
        reserve_ratio_change = monetary_data.get('reserve_ratio_change', 0)
        
        # 货币政策评分
        score = 0.5  # 基础分数
        
        # 利率变化影响
        if interest_rate_change < -0.25:
            score += 0.3  # 大幅降息利好
        elif interest_rate_change < 0:
            score += 0.2  # 降息利好
        elif interest_rate_change > 0.25:
            score -= 0.3  # 大幅加息利空
        elif interest_rate_change > 0:
            score -= 0.2  # 加息利空
        
        # 货币供应量影响
        if money_supply_growth > 12:
            score += 0.2  # 宽松货币政策
        elif money_supply_growth < 8:
            score -= 0.1  # 紧缩货币政策
        
        # 准备金率影响
        if reserve_ratio_change < 0:
            score += 0.1  # 降准利好
        elif reserve_ratio_change > 0:
            score -= 0.1  # 提准利空
        
        score = max(min(score, 1.0), 0.0)
        
        return {
            'score': score,
            'interest_rate_change': interest_rate_change,
            'money_supply_growth': money_supply_growth,
            'reserve_ratio_change': reserve_ratio_change,
            'stance': 'accommodative' if score > 0.6 else 'restrictive' if score < 0.4 else 'neutral'
        }
    
    def _analyze_fiscal_policy(self, fiscal_data: Dict) -> Dict:
        """分析财政政策"""
        deficit_ratio = fiscal_data.get('deficit_ratio', 3.0)  # 财政赤字率
        tax_policy_change = fiscal_data.get('tax_policy_change', 0)  # 税收政策变化
        infrastructure_spending = fiscal_data.get('infrastructure_spending', 0)  # 基建投资
        
        # 财政政策评分
        score = 0.5
        
        # 财政赤字率影响
        if deficit_ratio > 3.5:
            score += 0.2  # 积极财政政策
        elif deficit_ratio < 2.5:
            score -= 0.1  # 保守财政政策
        
        # 税收政策影响
        if tax_policy_change < 0:
            score += 0.2  # 减税利好
        elif tax_policy_change > 0:
            score -= 0.2  # 增税利空
        
        # 基建投资影响
        if infrastructure_spending > 0.1:
            score += 0.1  # 基建投资增加
        
        score = max(min(score, 1.0), 0.0)
        
        return {
            'score': score,
            'deficit_ratio': deficit_ratio,
            'tax_policy_change': tax_policy_change,
            'infrastructure_spending': infrastructure_spending,
            'stance': 'expansionary' if score > 0.6 else 'contractionary' if score < 0.4 else 'neutral'
        }
    
    def _analyze_regulatory_policy(self, regulatory_data: Dict) -> Dict:
        """分析监管政策"""
        regulatory_intensity = regulatory_data.get('intensity', 0.5)  # 监管强度
        new_regulations = regulatory_data.get('new_regulations', 0)  # 新法规数量
        enforcement_level = regulatory_data.get('enforcement_level', 0.5)  # 执法力度
        
        # 监管政策评分（监管趋严通常对市场不利）
        score = 1.0 - regulatory_intensity  # 反向评分
        
        # 新法规影响
        if new_regulations > 10:
            score -= 0.2
        elif new_regulations > 5:
            score -= 0.1
        
        # 执法力度影响
        if enforcement_level > 0.8:
            score -= 0.1
        
        score = max(min(score, 1.0), 0.0)
        
        return {
            'score': score,
            'regulatory_intensity': regulatory_intensity,
            'new_regulations': new_regulations,
            'enforcement_level': enforcement_level,
            'stance': 'strict' if score < 0.4 else 'lenient' if score > 0.6 else 'moderate'
        }
    
    def _analyze_industry_policy(self, industry_data: Dict) -> Dict:
        """分析产业政策"""
        support_policies = industry_data.get('support_policies', 0)  # 支持政策数量
        restriction_policies = industry_data.get('restriction_policies', 0)  # 限制政策数量
        subsidy_amount = industry_data.get('subsidy_amount', 0)  # 补贴金额
        
        # 产业政策评分
        net_policy_support = support_policies - restriction_policies
        
        if net_policy_support > 5:
            score = 0.9
        elif net_policy_support > 2:
            score = 0.7
        elif net_policy_support > 0:
            score = 0.6
        elif net_policy_support > -2:
            score = 0.4
        else:
            score = 0.2
        
        # 补贴影响
        if subsidy_amount > 1000:  # 亿元
            score = min(score + 0.1, 1.0)
        
        return {
            'score': score,
            'net_policy_support': net_policy_support,
            'subsidy_amount': subsidy_amount,
            'stance': 'supportive' if score > 0.6 else 'restrictive' if score < 0.4 else 'neutral'
        }
    
    def _analyze_trade_policy(self, trade_data: Dict) -> Dict:
        """分析贸易政策"""
        tariff_change = trade_data.get('tariff_change', 0)  # 关税变化
        trade_agreement = trade_data.get('trade_agreement', 0)  # 贸易协定
        export_policy = trade_data.get('export_policy', 0)  # 出口政策
        
        # 贸易政策评分
        score = 0.5
        
        # 关税影响
        if tariff_change < 0:
            score += 0.2  # 降低关税利好
        elif tariff_change > 0:
            score -= 0.2  # 提高关税利空
        
        # 贸易协定影响
        if trade_agreement > 0:
            score += 0.2  # 签署贸易协定利好
        
        # 出口政策影响
        if export_policy > 0:
            score += 0.1  # 支持出口政策
        
        score = max(min(score, 1.0), 0.0)
        
        return {
            'score': score,
            'tariff_change': tariff_change,
            'trade_agreement': trade_agreement,
            'export_policy': export_policy,
            'stance': 'open' if score > 0.6 else 'protectionist' if score < 0.4 else 'balanced'
        }
    
    def _identify_key_policy_drivers(self, monetary, fiscal, regulatory, industry, trade) -> List[str]:
        """识别关键政策驱动因素"""
        drivers = []
        
        # 检查各政策领域的显著影响
        if monetary['score'] > 0.7:
            drivers.append(f"宽松货币政策({monetary['stance']})")
        elif monetary['score'] < 0.3:
            drivers.append(f"紧缩货币政策({monetary['stance']})")
        
        if fiscal['score'] > 0.7:
            drivers.append(f"积极财政政策({fiscal['stance']})")
        elif fiscal['score'] < 0.3:
            drivers.append(f"保守财政政策({fiscal['stance']})")
        
        if regulatory['score'] < 0.3:
            drivers.append(f"严格监管政策({regulatory['stance']})")
        elif regulatory['score'] > 0.7:
            drivers.append(f"宽松监管环境({regulatory['stance']})")
        
        if industry['score'] > 0.7:
            drivers.append(f"产业支持政策({industry['stance']})")
        elif industry['score'] < 0.3:
            drivers.append(f"产业限制政策({industry['stance']})")
        
        return drivers
    
    def _analyze_policy_expectation(self, policy_data: Dict) -> str:
        """分析政策预期"""
        # 基于当前政策态势预测未来政策方向
        overall_stance = policy_data.get('overall_stance', 'neutral')
        economic_pressure = policy_data.get('economic_pressure', 0.5)
        
        if economic_pressure > 0.7:
            return 'more_supportive'  # 经济压力大，政策可能更加支持
        elif economic_pressure < 0.3:
            return 'more_restrictive'  # 经济过热，政策可能收紧
        else:
            return 'stable'  # 政策保持稳定


class MarketAnalyzer:
    """大盘走势预测特征模块主类"""
    
    def __init__(self):
        self.macro_analyzer = MacroEconomicAnalyzer()
        self.technical_analyzer = MarketTechnicalAnalyzer()
        self.fund_flow_analyzer = FundFlowAnalyzer()
        self.policy_analyzer = PolicyImpactAnalyzer()
        
        logger.info("大盘走势预测系统初始化完成")
    
    def predict_market_trend(self, macro_data: Dict, market_data: Dict, 
                           index_data: pd.DataFrame, flow_data: Dict,
                           policy_data: Dict, time_horizon: str = '1M') -> MarketForecast:
        """预测市场走势"""
        try:
            # 宏观经济分析
            macro_analysis = self.macro_analyzer.analyze_macro_indicators(macro_data)
            
            # 市场技术面分析
            technical_analysis = self.technical_analyzer.analyze_market_technical(market_data, index_data)
            
            # 资金流向分析
            fund_flow_analysis = self.fund_flow_analyzer.analyze_fund_flow(flow_data)
            
            # 政策影响分析
            policy_analysis = self.policy_analyzer.analyze_policy_impact(policy_data)
            
            # 综合评分计算
            overall_score = self._calculate_comprehensive_score(
                macro_analysis, technical_analysis, fund_flow_analysis, policy_analysis
            )
            
            # 确定市场趋势方向
            trend_direction = self._determine_trend_direction(overall_score)
            
            # 识别市场阶段
            market_phase = self._identify_market_phase(
                macro_analysis, technical_analysis, fund_flow_analysis
            )
            
            # 计算预测置信度
            confidence_level = self._calculate_confidence_level(
                macro_analysis, technical_analysis, fund_flow_analysis, policy_analysis
            )
            
            # 识别关键因素和风险
            key_factors, risk_factors = self._identify_key_risk_factors(
                macro_analysis, technical_analysis, fund_flow_analysis, policy_analysis
            )
            
            # 预测目标区间
            target_range = self._estimate_target_range(overall_score, index_data, time_horizon)
            
            forecast = MarketForecast(
                forecast_date=datetime.now(),
                trend_direction=trend_direction,
                market_phase=market_phase,
                confidence_level=confidence_level,
                time_horizon=time_horizon,
                key_factors=key_factors,
                risk_factors=risk_factors,
                target_range=target_range
            )
            
            logger.info(f"市场走势预测完成: {trend_direction.name}, 置信度: {confidence_level:.2f}")
            return forecast
            
        except Exception as e:
            logger.error(f"市场走势预测失败: {e}")
            return MarketForecast(
                forecast_date=datetime.now(),
                trend_direction=MarketTrend.NEUTRAL,
                market_phase=MarketPhase.BOTTOM,
                confidence_level=0.5,
                time_horizon=time_horizon,
                key_factors=['预测失败'],
                risk_factors=['数据不足'],
                target_range=(0, 0)
            )
    
    def _calculate_comprehensive_score(self, macro_analysis: Dict, technical_analysis: Dict,
                                     fund_flow_analysis: Dict, policy_analysis: Dict) -> float:
        """计算综合评分"""
        weights = {
            'macro': 0.30,
            'technical': 0.25,
            'fund_flow': 0.25,
            'policy': 0.20
        }
        
        macro_score = macro_analysis.get('macro_score', 0.5)
        technical_score = technical_analysis.get('technical_score', 0.5)
        fund_flow_score = fund_flow_analysis.get('overall_flow_score', 0.5)
        policy_score = policy_analysis.get('overall_policy_score', 0.5)
        
        comprehensive_score = (
            macro_score * weights['macro'] +
            technical_score * weights['technical'] +
            fund_flow_score * weights['fund_flow'] +
            policy_score * weights['policy']
        )
        
        return max(min(comprehensive_score, 1.0), 0.0)
    
    def _determine_trend_direction(self, overall_score: float) -> MarketTrend:
        """确定市场趋势方向"""
        if overall_score > 0.8:
            return MarketTrend.STRONG_BULL
        elif overall_score > 0.6:
            return MarketTrend.BULL
        elif overall_score > 0.4:
            return MarketTrend.NEUTRAL
        elif overall_score > 0.2:
            return MarketTrend.BEAR
        else:
            return MarketTrend.STRONG_BEAR
    
    def _identify_market_phase(self, macro_analysis: Dict, technical_analysis: Dict,
                             fund_flow_analysis: Dict) -> MarketPhase:
        """识别市场阶段"""
        # 基于多个指标综合判断市场所处阶段
        macro_cycle = macro_analysis.get('economic_cycle', 'stable')
        technical_pattern = technical_analysis.get('technical_pattern', 'neutral')
        fund_trend = fund_flow_analysis.get('flow_trend', 'neutral')
        
        # 简化的阶段判断逻辑
        if macro_cycle == 'recession' and technical_pattern in ['downtrend_strong', 'downtrend']:
            return MarketPhase.DECLINE
        elif macro_cycle == 'recovery' and fund_trend in ['strong_inflow', 'moderate_inflow']:
            return MarketPhase.RECOVERY
        elif macro_cycle == 'expansion' and technical_pattern in ['uptrend', 'uptrend_strong']:
            return MarketPhase.EXPANSION
        elif technical_pattern == 'uptrend_strong' and fund_trend == 'strong_inflow':
            return MarketPhase.PEAK
        elif technical_pattern in ['downtrend_strong'] and fund_trend == 'strong_outflow':
            return MarketPhase.BOTTOM
        else:
            return MarketPhase.RECOVERY  # 默认为复苏阶段
    
    def _calculate_confidence_level(self, macro_analysis: Dict, technical_analysis: Dict,
                                  fund_flow_analysis: Dict, policy_analysis: Dict) -> float:
        """计算预测置信度"""
        # 基于各分析模块的一致性计算置信度
        scores = [
            macro_analysis.get('macro_score', 0.5),
            technical_analysis.get('technical_score', 0.5),
            fund_flow_analysis.get('overall_flow_score', 0.5),
            policy_analysis.get('overall_policy_score', 0.5)
        ]
        
        # 计算分数的标准差，一致性越高置信度越高
        score_std = np.std(scores)
        base_confidence = 1 - score_std  # 标准差越小置信度越高
        
        # 调整置信度范围到0.3-0.9
        confidence = 0.3 + (base_confidence * 0.6)
        
        return max(min(confidence, 0.9), 0.3)
    
    def _identify_key_risk_factors(self, macro_analysis: Dict, technical_analysis: Dict,
                                 fund_flow_analysis: Dict, policy_analysis: Dict) -> Tuple[List[str], List[str]]:
        """识别关键因素和风险因素"""
        key_factors = []
        risk_factors = []
        
        # 从宏观分析中提取
        key_factors.extend(macro_analysis.get('key_drivers', []))
        
        # 从技术分析中提取
        if technical_analysis.get('technical_score', 0.5) > 0.7:
            key_factors.append('技术面强势')
        elif technical_analysis.get('technical_score', 0.5) < 0.3:
            risk_factors.append('技术面疲弱')
        
        # 从资金流向中提取
        dominant_fund = fund_flow_analysis.get('dominant_fund_type', '')
        if dominant_fund == 'northbound':
            key_factors.append('北向资金主导')
        elif dominant_fund == 'institutional':
            key_factors.append('机构资金主导')
        
        # 从政策分析中提取
        key_factors.extend(policy_analysis.get('key_policy_drivers', []))
        
        # 识别风险因素
        if macro_analysis.get('economic_cycle', '') == 'recession':
            risk_factors.append('经济衰退风险')
        
        if policy_analysis.get('overall_policy_score', 0.5) < 0.3:
            risk_factors.append('政策收紧风险')
        
        return key_factors[:5], risk_factors[:5]  # 限制数量
    
    def _estimate_target_range(self, overall_score: float, index_data: pd.DataFrame,
                             time_horizon: str) -> Tuple[float, float]:
        """估算目标区间"""
        if index_data.empty:
            return (0, 0)
        
        current_price = index_data['close'].iloc[-1]
        
        # 基于综合评分估算涨跌幅
        if time_horizon == '1W':
            base_range = 0.05  # 1周基础波动5%
        elif time_horizon == '1M':
            base_range = 0.15  # 1月基础波动15%
        elif time_horizon == '3M':
            base_range = 0.30  # 3月基础波动30%
        else:
            base_range = 0.15
        
        # 根据评分调整范围
        if overall_score > 0.7:
            upside = base_range * 1.5
            downside = base_range * 0.5
        elif overall_score > 0.5:
            upside = base_range
            downside = base_range * 0.8
        elif overall_score > 0.3:
            upside = base_range * 0.8
            downside = base_range
        else:
            upside = base_range * 0.5
            downside = base_range * 1.5
        
        target_high = current_price * (1 + upside)
        target_low = current_price * (1 - downside)
        
        return (target_low, target_high)
    
    def generate_market_report(self, forecast: MarketForecast, 
                             analysis_details: Dict) -> Dict:
        """生成市场分析报告"""
        return {
            'forecast_summary': {
                'trend_direction': forecast.trend_direction.name,
                'market_phase': forecast.market_phase.value,
                'confidence_level': f"{forecast.confidence_level:.1%}",
                'time_horizon': forecast.time_horizon,
                'target_range': f"{forecast.target_range[0]:.0f} - {forecast.target_range[1]:.0f}"
            },
            'key_factors': forecast.key_factors,
            'risk_factors': forecast.risk_factors,
            'detailed_analysis': analysis_details,
            'investment_suggestion': self._generate_investment_suggestion(forecast),
            'report_date': forecast.forecast_date.strftime('%Y-%m-%d %H:%M:%S')
        }
    
    def _generate_investment_suggestion(self, forecast: MarketForecast) -> str:
        """生成投资建议"""
        if forecast.trend_direction in [MarketTrend.STRONG_BULL, MarketTrend.BULL]:
            if forecast.confidence_level > 0.7:
                return "建议积极配置，关注成长股机会"
            else:
                return "谨慎乐观，适度增加仓位"
        elif forecast.trend_direction == MarketTrend.NEUTRAL:
            return "保持中性仓位，关注结构性机会"
        elif forecast.trend_direction in [MarketTrend.BEAR, MarketTrend.STRONG_BEAR]:
            if forecast.confidence_level > 0.7:
                return "建议降低仓位，关注防御性品种"
            else:
                return "谨慎操作，控制风险"
        else:
            return "密切关注市场变化，灵活调整策略"