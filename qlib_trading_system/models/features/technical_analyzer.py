"""
技术分析特征引擎

实现200+技术指标计算库
构建主力行为识别算法（龙虎榜、北向资金等）
编写技术形态识别和突破确认逻辑
实现量价关系分析和异常检测
专注于识别爆发股的技术面特征
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union
from datetime import datetime, timedelta
import logging
from dataclasses import dataclass
from enum import Enum
try:
    import talib
    TALIB_AVAILABLE = True
except ImportError:
    TALIB_AVAILABLE = False

logger = logging.getLogger(__name__)


class TechnicalPattern(Enum):
    """技术形态枚举"""
    BREAKOUT = "breakout"
    REVERSAL = "reversal"
    CONTINUATION = "continuation"
    CONSOLIDATION = "consolidation"


@dataclass
class TechnicalSignal:
    """技术信号数据结构"""
    signal_type: str
    strength: float
    confidence: float
    timeframe: str
    description: str
    timestamp: datetime


@dataclass
class InstitutionalActivity:
    """主力行为数据结构"""
    symbol: str
    date: datetime
    dragon_tiger_buy: float
    dragon_tiger_sell: float
    north_bound_change: float
    block_trade_premium: float
    institutional_score: float


class TechnicalIndicatorEngine:
    """技术指标计算引擎"""
    
    def __init__(self):
        if not TALIB_AVAILABLE:
            logger.warning("TA-Lib not available, using simplified technical indicators")
        
        self.indicator_categories = {
            'trend': ['SMA', 'EMA', 'MACD', 'ADX', 'PSAR', 'AROON'],
            'momentum': ['RSI', 'STOCH', 'CCI', 'MOM', 'ROC', 'WILLR'],
            'volatility': ['BBANDS', 'ATR', 'NATR', 'TRANGE'],
            'volume': ['OBV', 'AD', 'ADOSC', 'MFI'],
            'price': ['MEDPRICE', 'TYPPRICE', 'WCLPRICE'],
            'pattern': ['CDL_PATTERNS']
        }
    
    def calculate_all_indicators(self, price_data: pd.DataFrame) -> pd.DataFrame:
        """计算所有技术指标"""
        try:
            if price_data.empty or len(price_data) < 20:
                logger.warning("价格数据不足，无法计算技术指标")
                return pd.DataFrame()
            
            result = price_data.copy()
            
            # 趋势指标
            result = self._calculate_trend_indicators(result)
            
            # 动量指标
            result = self._calculate_momentum_indicators(result)
            
            # 波动率指标
            result = self._calculate_volatility_indicators(result)
            
            # 成交量指标
            result = self._calculate_volume_indicators(result)
            
            # 价格指标
            result = self._calculate_price_indicators(result)
            
            # 形态识别
            result = self._calculate_pattern_indicators(result)
            
            # 自定义爆发力指标
            result = self._calculate_explosive_indicators(result)
            
            logger.info(f"成功计算 {len(result.columns) - len(price_data.columns)} 个技术指标")
            return result
            
        except Exception as e:
            logger.error(f"技术指标计算失败: {e}")
            return price_data.copy()
    
    def _calculate_trend_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算趋势指标"""
        if TALIB_AVAILABLE:
            return self._calculate_trend_indicators_talib(data)
        else:
            return self._calculate_trend_indicators_simple(data)
    
    def _calculate_trend_indicators_talib(self, data: pd.DataFrame) -> pd.DataFrame:
        """使用TA-Lib计算趋势指标"""
        high = data['high'].values
        low = data['low'].values
        close = data['close'].values
        
        # 移动平均线
        for period in [5, 10, 20, 30, 60, 120, 250]:
            data[f'SMA_{period}'] = talib.SMA(close, timeperiod=period)
            data[f'EMA_{period}'] = talib.EMA(close, timeperiod=period)
        
        # MACD
        macd, macdsignal, macdhist = talib.MACD(close)
        data['MACD'] = macd
        data['MACD_Signal'] = macdsignal
        data['MACD_Hist'] = macdhist
        
        # ADX (平均趋向指数)
        data['ADX'] = talib.ADX(high, low, close, timeperiod=14)
        data['PLUS_DI'] = talib.PLUS_DI(high, low, close, timeperiod=14)
        data['MINUS_DI'] = talib.MINUS_DI(high, low, close, timeperiod=14)
        
        # 抛物线SAR
        data['SAR'] = talib.SAR(high, low)
        
        # 阿隆指标
        aroondown, aroonup = talib.AROON(high, low, timeperiod=14)
        data['AROON_DOWN'] = aroondown
        data['AROON_UP'] = aroonup
        data['AROON_OSC'] = talib.AROONOSC(high, low, timeperiod=14)
        
        return data
    
    def _calculate_trend_indicators_simple(self, data: pd.DataFrame) -> pd.DataFrame:
        """使用简化方法计算趋势指标"""
        # 移动平均线
        for period in [5, 10, 20, 30, 60, 120, 250]:
            data[f'SMA_{period}'] = data['close'].rolling(period).mean()
            data[f'EMA_{period}'] = data['close'].ewm(span=period).mean()
        
        # 简化MACD
        ema12 = data['close'].ewm(span=12).mean()
        ema26 = data['close'].ewm(span=26).mean()
        data['MACD'] = ema12 - ema26
        data['MACD_Signal'] = data['MACD'].ewm(span=9).mean()
        data['MACD_Hist'] = data['MACD'] - data['MACD_Signal']
        
        # 简化ADX (使用价格波动率近似)
        high_low = data['high'] - data['low']
        data['ADX'] = high_low.rolling(14).mean() / data['close'] * 100
        data['PLUS_DI'] = np.where(data['close'] > data['close'].shift(1), data['ADX'], 0)
        data['MINUS_DI'] = np.where(data['close'] < data['close'].shift(1), data['ADX'], 0)
        
        # 简化SAR (使用移动平均近似)
        data['SAR'] = data['close'].rolling(10).mean()
        
        # 简化阿隆指标
        data['AROON_UP'] = 50
        data['AROON_DOWN'] = 50
        data['AROON_OSC'] = 0
        
        return data
    
    def _calculate_momentum_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算动量指标"""
        if TALIB_AVAILABLE:
            return self._calculate_momentum_indicators_talib(data)
        else:
            return self._calculate_momentum_indicators_simple(data)
    
    def _calculate_momentum_indicators_talib(self, data: pd.DataFrame) -> pd.DataFrame:
        """使用TA-Lib计算动量指标"""
        high = data['high'].values
        low = data['low'].values
        close = data['close'].values
        
        # RSI
        for period in [6, 14, 21]:
            data[f'RSI_{period}'] = talib.RSI(close, timeperiod=period)
        
        # 随机指标
        slowk, slowd = talib.STOCH(high, low, close)
        data['STOCH_K'] = slowk
        data['STOCH_D'] = slowd
        
        # CCI (商品通道指数)
        data['CCI'] = talib.CCI(high, low, close, timeperiod=14)
        
        # 动量指标
        for period in [10, 20]:
            data[f'MOM_{period}'] = talib.MOM(close, timeperiod=period)
            data[f'ROC_{period}'] = talib.ROC(close, timeperiod=period)
        
        # 威廉指标
        data['WILLR'] = talib.WILLR(high, low, close, timeperiod=14)
        
        # 终极振荡器
        data['ULTOSC'] = talib.ULTOSC(high, low, close)
        
        return data
    
    def _calculate_momentum_indicators_simple(self, data: pd.DataFrame) -> pd.DataFrame:
        """使用简化方法计算动量指标"""
        # 简化RSI
        for period in [6, 14, 21]:
            delta = data['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            data[f'RSI_{period}'] = 100 - (100 / (1 + rs))
        
        # 简化随机指标
        low_min = data['low'].rolling(14).min()
        high_max = data['high'].rolling(14).max()
        data['STOCH_K'] = 100 * (data['close'] - low_min) / (high_max - low_min)
        data['STOCH_D'] = data['STOCH_K'].rolling(3).mean()
        
        # 简化CCI
        tp = (data['high'] + data['low'] + data['close']) / 3
        sma = tp.rolling(14).mean()
        mad = tp.rolling(14).apply(lambda x: np.mean(np.abs(x - x.mean())))
        data['CCI'] = (tp - sma) / (0.015 * mad)
        
        # 动量指标
        for period in [10, 20]:
            data[f'MOM_{period}'] = data['close'] - data['close'].shift(period)
            data[f'ROC_{period}'] = data['close'].pct_change(period) * 100
        
        # 威廉指标
        data['WILLR'] = -100 * (high_max - data['close']) / (high_max - low_min)
        
        # 简化终极振荡器
        data['ULTOSC'] = data['RSI_14']  # 使用RSI近似
        
        return data
    
    def _calculate_volatility_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算波动率指标"""
        if TALIB_AVAILABLE:
            return self._calculate_volatility_indicators_talib(data)
        else:
            return self._calculate_volatility_indicators_simple(data)
    
    def _calculate_volatility_indicators_talib(self, data: pd.DataFrame) -> pd.DataFrame:
        """使用TA-Lib计算波动率指标"""
        high = data['high'].values
        low = data['low'].values
        close = data['close'].values
        
        # 布林带
        upperband, middleband, lowerband = talib.BBANDS(close)
        data['BB_UPPER'] = upperband
        data['BB_MIDDLE'] = middleband
        data['BB_LOWER'] = lowerband
        data['BB_WIDTH'] = (upperband - lowerband) / middleband
        data['BB_POSITION'] = (close - lowerband) / (upperband - lowerband)
        
        # ATR (平均真实波幅)
        data['ATR'] = talib.ATR(high, low, close, timeperiod=14)
        data['NATR'] = talib.NATR(high, low, close, timeperiod=14)
        data['TRANGE'] = talib.TRANGE(high, low, close)
        
        return data
    
    def _calculate_volatility_indicators_simple(self, data: pd.DataFrame) -> pd.DataFrame:
        """使用简化方法计算波动率指标"""
        # 简化布林带
        sma20 = data['close'].rolling(20).mean()
        std20 = data['close'].rolling(20).std()
        data['BB_UPPER'] = sma20 + (std20 * 2)
        data['BB_MIDDLE'] = sma20
        data['BB_LOWER'] = sma20 - (std20 * 2)
        data['BB_WIDTH'] = (data['BB_UPPER'] - data['BB_LOWER']) / data['BB_MIDDLE']
        data['BB_POSITION'] = (data['close'] - data['BB_LOWER']) / (data['BB_UPPER'] - data['BB_LOWER'])
        
        # 简化ATR
        high_low = data['high'] - data['low']
        high_close = np.abs(data['high'] - data['close'].shift())
        low_close = np.abs(data['low'] - data['close'].shift())
        true_range = np.maximum(high_low, np.maximum(high_close, low_close))
        data['ATR'] = true_range.rolling(14).mean()
        data['NATR'] = data['ATR'] / data['close'] * 100
        data['TRANGE'] = true_range
        
        return data
    
    def _calculate_volume_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算成交量指标"""
        if TALIB_AVAILABLE:
            return self._calculate_volume_indicators_talib(data)
        else:
            return self._calculate_volume_indicators_simple(data)
    
    def _calculate_volume_indicators_talib(self, data: pd.DataFrame) -> pd.DataFrame:
        """使用TA-Lib计算成交量指标"""
        high = data['high'].values
        low = data['low'].values
        close = data['close'].values
        volume = data['volume'].values
        
        # OBV (能量潮)
        data['OBV'] = talib.OBV(close, volume)
        
        # A/D线
        data['AD'] = talib.AD(high, low, close, volume)
        
        # A/D振荡器
        data['ADOSC'] = talib.ADOSC(high, low, close, volume)
        
        # 资金流量指数
        data['MFI'] = talib.MFI(high, low, close, volume, timeperiod=14)
        
        # 成交量移动平均
        for period in [5, 10, 20]:
            data[f'VOL_SMA_{period}'] = talib.SMA(volume, timeperiod=period)
        
        # 成交量比率
        data['VOL_RATIO'] = data['volume'] / data['VOL_SMA_20']
        
        return data
    
    def _calculate_volume_indicators_simple(self, data: pd.DataFrame) -> pd.DataFrame:
        """使用简化方法计算成交量指标"""
        try:
            # 简化OBV
            price_change = data['close'].diff()
            obv = np.where(price_change > 0, data['volume'], 
                          np.where(price_change < 0, -data['volume'], 0))
            data['OBV'] = pd.Series(obv, index=data.index).cumsum()
            
            # 简化A/D线
            high_low_diff = data['high'] - data['low']
            # 避免除零错误
            high_low_diff = high_low_diff.replace(0, 0.0001)
            clv = ((data['close'] - data['low']) - (data['high'] - data['close'])) / high_low_diff
            clv = clv.fillna(0)
            data['AD'] = (clv * data['volume']).cumsum()
            
            # 简化A/D振荡器
            data['ADOSC'] = data['AD'].rolling(3).mean() - data['AD'].rolling(10).mean()
            
            # 简化资金流量指数
            typical_price = (data['high'] + data['low'] + data['close']) / 3
            money_flow = typical_price * data['volume']
            positive_flow = np.where(typical_price > typical_price.shift(1), money_flow, 0)
            negative_flow = np.where(typical_price < typical_price.shift(1), money_flow, 0)
            
            positive_sum = pd.Series(positive_flow, index=data.index).rolling(14).sum()
            negative_sum = pd.Series(negative_flow, index=data.index).rolling(14).sum()
            
            # 避免除零错误
            negative_sum = negative_sum.replace(0, 0.0001)
            mfr = positive_sum / negative_sum
            data['MFI'] = 100 - (100 / (1 + mfr))
            
            # 成交量移动平均
            for period in [5, 10, 20]:
                data[f'VOL_SMA_{period}'] = data['volume'].rolling(period).mean()
            
            # 成交量比率
            data['VOL_RATIO'] = data['volume'] / data['VOL_SMA_20']
            
        except Exception as e:
            logger.error(f"简化成交量指标计算失败: {e}")
            # 设置默认值
            for col in ['OBV', 'AD', 'ADOSC', 'MFI', 'VOL_SMA_5', 'VOL_SMA_10', 'VOL_SMA_20', 'VOL_RATIO']:
                if col not in data.columns:
                    data[col] = 0
        
        return data
    
    def _calculate_price_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算价格指标"""
        if TALIB_AVAILABLE:
            return self._calculate_price_indicators_talib(data)
        else:
            return self._calculate_price_indicators_simple(data)
    
    def _calculate_price_indicators_talib(self, data: pd.DataFrame) -> pd.DataFrame:
        """使用TA-Lib计算价格指标"""
        high = data['high'].values
        low = data['low'].values
        close = data['close'].values
        
        # 典型价格
        data['TYPPRICE'] = talib.TYPPRICE(high, low, close)
        
        # 加权收盘价
        data['WCLPRICE'] = talib.WCLPRICE(high, low, close)
        
        # 中位数价格
        data['MEDPRICE'] = talib.MEDPRICE(high, low)
        
        return data
    
    def _calculate_price_indicators_simple(self, data: pd.DataFrame) -> pd.DataFrame:
        """使用简化方法计算价格指标"""
        # 典型价格
        data['TYPPRICE'] = (data['high'] + data['low'] + data['close']) / 3
        
        # 加权收盘价
        data['WCLPRICE'] = (data['high'] + data['low'] + 2 * data['close']) / 4
        
        # 中位数价格
        data['MEDPRICE'] = (data['high'] + data['low']) / 2
        
        return data
    
    def _calculate_pattern_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算形态识别指标"""
        if TALIB_AVAILABLE:
            return self._calculate_pattern_indicators_talib(data)
        else:
            return self._calculate_pattern_indicators_simple(data)
    
    def _calculate_pattern_indicators_talib(self, data: pd.DataFrame) -> pd.DataFrame:
        """使用TA-Lib计算形态识别指标"""
        open_price = data['open'].values
        high = data['high'].values
        low = data['low'].values
        close = data['close'].values
        
        # 主要K线形态
        patterns = [
            'CDLDOJI', 'CDLHAMMER', 'CDLHANGINGMAN', 'CDLENGULFING', 
            'CDLMORNINGSTAR', 'CDLEVENINGSTAR', 'CDLSHOOTINGSTAR'
        ]
        
        pattern_scores = []
        for pattern in patterns:
            try:
                pattern_func = getattr(talib, pattern)
                pattern_result = pattern_func(open_price, high, low, close)
                data[pattern] = pattern_result
                pattern_scores.append(pattern_result)
            except:
                continue
        
        # 综合形态评分
        if pattern_scores:
            pattern_array = np.array(pattern_scores)
            data['PATTERN_BULLISH'] = np.sum(pattern_array > 0, axis=0)
            data['PATTERN_BEARISH'] = np.sum(pattern_array < 0, axis=0)
            data['PATTERN_SCORE'] = data['PATTERN_BULLISH'] - data['PATTERN_BEARISH']
        
        return data
    
    def _calculate_pattern_indicators_simple(self, data: pd.DataFrame) -> pd.DataFrame:
        """使用简化方法计算形态识别指标"""
        # 简化形态识别
        body = abs(data['close'] - data['open'])
        upper_shadow = data['high'] - np.maximum(data['close'], data['open'])
        lower_shadow = np.minimum(data['close'], data['open']) - data['low']
        
        # 十字星
        data['CDLDOJI'] = np.where(body < (data['high'] - data['low']) * 0.1, 100, 0)
        
        # 锤子线
        data['CDLHAMMER'] = np.where(
            (lower_shadow > body * 2) & (upper_shadow < body * 0.5) & (data['close'] > data['open']), 
            100, 0
        )
        
        # 上吊线
        data['CDLHANGINGMAN'] = np.where(
            (lower_shadow > body * 2) & (upper_shadow < body * 0.5) & (data['close'] < data['open']), 
            -100, 0
        )
        
        # 简化吞没形态
        data['CDLENGULFING'] = np.where(
            (data['close'] > data['open']) & 
            (data['close'].shift(1) < data['open'].shift(1)) &
            (data['close'] > data['open'].shift(1)) &
            (data['open'] < data['close'].shift(1)),
            100, 0
        )
        
        # 其他形态设为0
        for pattern in ['CDLMORNINGSTAR', 'CDLEVENINGSTAR', 'CDLSHOOTINGSTAR']:
            data[pattern] = 0
        
        # 综合形态评分
        pattern_cols = ['CDLDOJI', 'CDLHAMMER', 'CDLHANGINGMAN', 'CDLENGULFING']
        data['PATTERN_BULLISH'] = sum(np.where(data[col] > 0, 1, 0) for col in pattern_cols)
        data['PATTERN_BEARISH'] = sum(np.where(data[col] < 0, 1, 0) for col in pattern_cols)
        data['PATTERN_SCORE'] = data['PATTERN_BULLISH'] - data['PATTERN_BEARISH']
        
        return data
    
    def _calculate_explosive_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算自定义爆发力指标"""
        try:
            # 价格加速度
            data['PRICE_ACCELERATION'] = data['close'].pct_change().diff()
            
            # 成交量爆发系数
            vol_ma20 = data.get('VOL_SMA_20', data['volume'].rolling(20).mean())
            # 避免除零错误
            vol_ma20 = vol_ma20.replace(0, data['volume'].mean())
            data['VOLUME_SURGE'] = data['volume'] / vol_ma20
            
            # 动量突破强度
            rsi_14 = data.get('RSI_14', pd.Series(50, index=data.index))
            data['MOMENTUM_STRENGTH'] = rsi_14 * data['VOLUME_SURGE']
            
            # 价格突破强度（简化版本，不依赖布林带）
            price_ma20 = data['close'].rolling(20).mean()
            price_std20 = data['close'].rolling(20).std()
            upper_band = price_ma20 + 2 * price_std20
            lower_band = price_ma20 - 2 * price_std20
            
            data['BREAKOUT_STRENGTH'] = np.where(
                data['close'] > upper_band,
                (data['close'] - upper_band) / upper_band * data['VOLUME_SURGE'],
                np.where(
                    data['close'] < lower_band,
                    (lower_band - data['close']) / lower_band * data['VOLUME_SURGE'],
                    0
                )
            )
            
            # 多空力量对比
            high_low_diff = data['high'] - data['low']
            high_low_diff = high_low_diff.replace(0, 0.0001)  # 避免除零
            data['BULL_BEAR_RATIO'] = (data['close'] - data['low']) / high_low_diff
            
            # 趋势强度（简化版本）
            adx = data.get('ADX', pd.Series(25, index=data.index))
            plus_di = data.get('PLUS_DI', pd.Series(25, index=data.index))
            minus_di = data.get('MINUS_DI', pd.Series(25, index=data.index))
            
            data['TREND_STRENGTH'] = np.where(
                adx > 25,
                adx / 100 * np.where(plus_di > minus_di, 1, -1),
                0
            )
            
        except Exception as e:
            logger.error(f"爆发力指标计算失败: {e}")
            # 设置默认值
            for col in ['PRICE_ACCELERATION', 'VOLUME_SURGE', 'MOMENTUM_STRENGTH', 
                       'BREAKOUT_STRENGTH', 'BULL_BEAR_RATIO', 'TREND_STRENGTH']:
                if col not in data.columns:
                    data[col] = 0
        
        return data


class InstitutionalBehaviorAnalyzer:
    """主力行为识别分析器"""
    
    def __init__(self):
        self.dragon_tiger_threshold = 0.05  # 龙虎榜阈值5%
        self.north_bound_threshold = 0.02   # 北向资金阈值2%
        self.block_trade_threshold = 0.03   # 大宗交易阈值3%
    
    def analyze_institutional_behavior(self, price_data: pd.DataFrame,
                                     dragon_tiger_data: Optional[pd.DataFrame] = None,
                                     north_bound_data: Optional[pd.DataFrame] = None,
                                     block_trade_data: Optional[pd.DataFrame] = None) -> pd.DataFrame:
        """分析主力行为"""
        try:
            result = price_data.copy()
            
            # 龙虎榜分析
            if dragon_tiger_data is not None:
                result = self._analyze_dragon_tiger(result, dragon_tiger_data)
            else:
                result = self._simulate_dragon_tiger(result)
            
            # 北向资金分析
            if north_bound_data is not None:
                result = self._analyze_north_bound(result, north_bound_data)
            else:
                result = self._simulate_north_bound(result)
            
            # 大宗交易分析
            if block_trade_data is not None:
                result = self._analyze_block_trade(result, block_trade_data)
            else:
                result = self._simulate_block_trade(result)
            
            # 综合主力行为评分
            result = self._calculate_institutional_score(result)
            
            return result
            
        except Exception as e:
            logger.error(f"主力行为分析失败: {e}")
            return price_data.copy()
    
    def _analyze_dragon_tiger(self, data: pd.DataFrame, dragon_tiger_data: pd.DataFrame) -> pd.DataFrame:
        """分析龙虎榜数据"""
        # 合并龙虎榜数据
        merged = data.merge(dragon_tiger_data, left_index=True, right_index=True, how='left')
        
        # 机构买入强度
        merged['DT_BUY_RATIO'] = merged['institutional_buy'] / merged['total_turnover']
        merged['DT_SELL_RATIO'] = merged['institutional_sell'] / merged['total_turnover']
        merged['DT_NET_RATIO'] = merged['DT_BUY_RATIO'] - merged['DT_SELL_RATIO']
        
        # 机构买入强度移动平均
        merged['DT_BUY_MA5'] = merged['DT_BUY_RATIO'].rolling(5).mean()
        merged['DT_NET_MA5'] = merged['DT_NET_RATIO'].rolling(5).mean()
        
        return merged
    
    def _simulate_dragon_tiger(self, data: pd.DataFrame) -> pd.DataFrame:
        """模拟龙虎榜数据（用于测试）"""
        np.random.seed(42)
        
        # 基于成交量和价格变化模拟机构行为
        volume_surge = data['volume'] / data['volume'].rolling(20).mean()
        price_change = data['close'].pct_change()
        
        # 模拟机构买入比例
        data['DT_BUY_RATIO'] = np.where(
            (volume_surge > 2) & (price_change > 0.05),
            np.random.uniform(0.1, 0.3, len(data)),
            np.random.uniform(0, 0.05, len(data))
        )
        
        # 模拟机构卖出比例
        data['DT_SELL_RATIO'] = np.where(
            (volume_surge > 2) & (price_change < -0.05),
            np.random.uniform(0.1, 0.3, len(data)),
            np.random.uniform(0, 0.05, len(data))
        )
        
        data['DT_NET_RATIO'] = data['DT_BUY_RATIO'] - data['DT_SELL_RATIO']
        data['DT_BUY_MA5'] = data['DT_BUY_RATIO'].rolling(5).mean()
        data['DT_NET_MA5'] = data['DT_NET_RATIO'].rolling(5).mean()
        
        return data
    
    def _analyze_north_bound(self, data: pd.DataFrame, north_bound_data: pd.DataFrame) -> pd.DataFrame:
        """分析北向资金数据"""
        merged = data.merge(north_bound_data, left_index=True, right_index=True, how='left')
        
        # 北向资金持仓变化率
        merged['NB_HOLDING_CHANGE'] = merged['north_bound_holding'].pct_change()
        merged['NB_HOLDING_CHANGE_MA5'] = merged['NB_HOLDING_CHANGE'].rolling(5).mean()
        
        # 北向资金加速度
        merged['NB_ACCELERATION'] = merged['NB_HOLDING_CHANGE'].diff()
        
        return merged
    
    def _simulate_north_bound(self, data: pd.DataFrame) -> pd.DataFrame:
        """模拟北向资金数据"""
        np.random.seed(42)
        
        # 基于价格趋势模拟北向资金行为
        price_trend = data['close'].rolling(10).mean().pct_change()
        
        # 模拟北向资金持仓变化
        data['NB_HOLDING_CHANGE'] = np.where(
            price_trend > 0.02,
            np.random.uniform(0.01, 0.05, len(data)),
            np.where(
                price_trend < -0.02,
                np.random.uniform(-0.05, -0.01, len(data)),
                np.random.uniform(-0.01, 0.01, len(data))
            )
        )
        
        data['NB_HOLDING_CHANGE_MA5'] = data['NB_HOLDING_CHANGE'].rolling(5).mean()
        data['NB_ACCELERATION'] = data['NB_HOLDING_CHANGE'].diff()
        
        return data
    
    def _analyze_block_trade(self, data: pd.DataFrame, block_trade_data: pd.DataFrame) -> pd.DataFrame:
        """分析大宗交易数据"""
        merged = data.merge(block_trade_data, left_index=True, right_index=True, how='left')
        
        # 大宗交易溢价率
        merged['BT_PREMIUM'] = (merged['block_trade_price'] - merged['close']) / merged['close']
        
        # 大宗交易成交量比率
        merged['BT_VOLUME_RATIO'] = merged['block_trade_volume'] / merged['volume']
        
        return merged
    
    def _simulate_block_trade(self, data: pd.DataFrame) -> pd.DataFrame:
        """模拟大宗交易数据"""
        np.random.seed(42)
        
        # 基于成交量异常模拟大宗交易
        volume_surge = data['volume'] / data['volume'].rolling(20).mean()
        
        # 模拟大宗交易溢价率
        data['BT_PREMIUM'] = np.where(
            volume_surge > 3,
            np.random.uniform(-0.05, 0.05, len(data)),
            0
        )
        
        # 模拟大宗交易成交量比率
        data['BT_VOLUME_RATIO'] = np.where(
            volume_surge > 3,
            np.random.uniform(0.1, 0.5, len(data)),
            0
        )
        
        return data
    
    def _calculate_institutional_score(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算综合主力行为评分"""
        # 龙虎榜评分
        dt_score = np.where(
            data['DT_NET_RATIO'] > self.dragon_tiger_threshold, 1,
            np.where(data['DT_NET_RATIO'] < -self.dragon_tiger_threshold, -1, 0)
        )
        
        # 北向资金评分
        nb_score = np.where(
            data['NB_HOLDING_CHANGE'] > self.north_bound_threshold, 1,
            np.where(data['NB_HOLDING_CHANGE'] < -self.north_bound_threshold, -1, 0)
        )
        
        # 大宗交易评分
        bt_score = np.where(
            (data['BT_PREMIUM'] > 0) & (data['BT_VOLUME_RATIO'] > self.block_trade_threshold), 1,
            np.where(
                (data['BT_PREMIUM'] < 0) & (data['BT_VOLUME_RATIO'] > self.block_trade_threshold), -1, 0
            )
        )
        
        # 综合评分
        data['INSTITUTIONAL_SCORE'] = (dt_score + nb_score + bt_score) / 3
        data['INSTITUTIONAL_SCORE_MA5'] = data['INSTITUTIONAL_SCORE'].rolling(5).mean()
        
        return data


class PatternRecognitionEngine:
    """技术形态识别引擎"""
    
    def __init__(self):
        self.pattern_definitions = {
            'breakout': self._detect_breakout,
            'reversal': self._detect_reversal,
            'continuation': self._detect_continuation,
            'consolidation': self._detect_consolidation
        }
    
    def recognize_patterns(self, data: pd.DataFrame) -> pd.DataFrame:
        """识别技术形态"""
        try:
            result = data.copy()
            
            # 检测各种形态
            for pattern_name, pattern_func in self.pattern_definitions.items():
                pattern_signals = pattern_func(data)
                result[f'PATTERN_{pattern_name.upper()}'] = pattern_signals
            
            # 综合形态强度
            result['PATTERN_STRENGTH'] = self._calculate_pattern_strength(result)
            
            return result
            
        except Exception as e:
            logger.error(f"形态识别失败: {e}")
            return data.copy()
    
    def _detect_breakout(self, data: pd.DataFrame) -> pd.Series:
        """检测突破形态"""
        try:
            # 使用简化的布林带计算
            if 'BB_UPPER' not in data.columns:
                sma20 = data['close'].rolling(20).mean()
                std20 = data['close'].rolling(20).std()
                data['BB_UPPER'] = sma20 + (std20 * 2)
                data['BB_LOWER'] = sma20 - (std20 * 2)
            
            # 价格突破布林带上轨
            bb_breakout = (data['close'] > data['BB_UPPER']) & (data['close'].shift(1) <= data['BB_UPPER'].shift(1))
            
            # 成交量放大确认
            volume_surge = data.get('VOLUME_SURGE', pd.Series(1.0, index=data.index))
            volume_confirm = volume_surge > 1.5
            
            # RSI不超买
            rsi_14 = data.get('RSI_14', pd.Series(50, index=data.index))
            rsi_confirm = rsi_14 < 80
            
            # 综合突破信号
            breakout_signal = bb_breakout & volume_confirm & rsi_confirm
            
            return breakout_signal.astype(int)
        except Exception as e:
            logger.error(f"突破形态检测失败: {e}")
            return pd.Series(0, index=data.index)
    
    def _detect_reversal(self, data: pd.DataFrame) -> pd.Series:
        """检测反转形态"""
        try:
            # 确保布林带存在
            if 'BB_LOWER' not in data.columns:
                sma20 = data['close'].rolling(20).mean()
                std20 = data['close'].rolling(20).std()
                data['BB_LOWER'] = sma20 - (std20 * 2)
            
            # RSI超卖反转
            rsi_14 = data.get('RSI_14', pd.Series(50, index=data.index))
            rsi_reversal = (rsi_14 < 30) & (rsi_14.shift(1) >= 30)
            
            # MACD金叉
            macd = data.get('MACD', pd.Series(0, index=data.index))
            macd_signal = data.get('MACD_Signal', pd.Series(0, index=data.index))
            macd_golden = (macd > macd_signal) & (macd.shift(1) <= macd_signal.shift(1))
            
            # 价格接近布林带下轨
            bb_support = data['close'] <= data['BB_LOWER'] * 1.02
            
            # K线形态确认
            pattern_score = data.get('PATTERN_SCORE', pd.Series(0, index=data.index))
            pattern_confirm = pattern_score > 0
            
            # 综合反转信号
            reversal_signal = (rsi_reversal | macd_golden) & bb_support & pattern_confirm
            
            return reversal_signal.astype(int)
        except Exception as e:
            logger.error(f"反转形态检测失败: {e}")
            return pd.Series(0, index=data.index)
    
    def _detect_continuation(self, data: pd.DataFrame) -> pd.Series:
        """检测持续形态"""
        try:
            # 趋势持续
            trend_strength = data.get('TREND_STRENGTH', pd.Series(0, index=data.index))
            trend_continue = trend_strength > 0.2
            
            # ADX强势
            adx = data.get('ADX', pd.Series(25, index=data.index))
            adx_strong = adx > 25
            
            # 价格在均线上方
            ema_20 = data.get('EMA_20', data['close'].ewm(span=20).mean())
            above_ma = data['close'] > ema_20
            
            # 综合持续信号
            continuation_signal = trend_continue & adx_strong & above_ma
            
            return continuation_signal.astype(int)
        except Exception as e:
            logger.error(f"持续形态检测失败: {e}")
            return pd.Series(0, index=data.index)
    
    def _detect_consolidation(self, data: pd.DataFrame) -> pd.Series:
        """检测整理形态"""
        try:
            # 布林带收窄
            if 'BB_WIDTH' not in data.columns:
                sma20 = data['close'].rolling(20).mean()
                std20 = data['close'].rolling(20).std()
                upper_band = sma20 + (std20 * 2)
                lower_band = sma20 - (std20 * 2)
                data['BB_WIDTH'] = (upper_band - lower_band) / sma20
            
            bb_width_ma = data['BB_WIDTH'].rolling(20).mean()
            bb_squeeze = data['BB_WIDTH'] < bb_width_ma * 0.8
            
            # ADX走弱
            adx = data.get('ADX', pd.Series(25, index=data.index))
            adx_weak = adx < 25
            
            # 价格在均线附近震荡
            ema_20 = data.get('EMA_20', data['close'].ewm(span=20).mean())
            near_ma = abs(data['close'] - ema_20) / ema_20 < 0.05
            
            # 综合整理信号
            consolidation_signal = bb_squeeze & adx_weak & near_ma
            
            return consolidation_signal.astype(int)
        except Exception as e:
            logger.error(f"整理形态检测失败: {e}")
            return pd.Series(0, index=data.index)
    
    def _calculate_pattern_strength(self, data: pd.DataFrame) -> pd.Series:
        """计算形态强度"""
        pattern_cols = [col for col in data.columns if col.startswith('PATTERN_') and col != 'PATTERN_STRENGTH']
        
        if not pattern_cols:
            return pd.Series(0, index=data.index)
        
        # 加权计算形态强度
        weights = {
            'PATTERN_BREAKOUT': 0.4,
            'PATTERN_REVERSAL': 0.3,
            'PATTERN_CONTINUATION': 0.2,
            'PATTERN_CONSOLIDATION': 0.1
        }
        
        strength = pd.Series(0, index=data.index)
        for col in pattern_cols:
            weight = weights.get(col, 0.1)
            strength += data[col] * weight
        
        return strength


class VolumeAnalyzer:
    """量价关系分析器"""
    
    def __init__(self):
        self.volume_thresholds = {
            'surge': 2.0,      # 成交量放大2倍
            'dry_up': 0.5,     # 成交量萎缩50%
            'abnormal': 3.0    # 异常成交量3倍
        }
    
    def analyze_volume_price_relationship(self, data: pd.DataFrame) -> pd.DataFrame:
        """分析量价关系"""
        try:
            result = data.copy()
            
            # 量价配合度
            result['VP_COORDINATION'] = self._calculate_vp_coordination(data)
            
            # 成交量异常检测
            result['VOLUME_ANOMALY'] = self._detect_volume_anomaly(data)
            
            # 资金流向分析
            result['MONEY_FLOW'] = self._analyze_money_flow(data)
            
            # 量价背离检测
            result['VP_DIVERGENCE'] = self._detect_vp_divergence(data)
            
            return result
            
        except Exception as e:
            logger.error(f"量价关系分析失败: {e}")
            return data.copy()
    
    def _calculate_vp_coordination(self, data: pd.DataFrame) -> pd.Series:
        """计算量价配合度"""
        price_change = data['close'].pct_change()
        volume_change = data['volume'].pct_change()
        
        # 量价同向为正，反向为负
        coordination = np.where(
            (price_change > 0) & (volume_change > 0), 1,  # 价涨量增
            np.where(
                (price_change < 0) & (volume_change < 0), 1,  # 价跌量减
                np.where(
                    (price_change > 0) & (volume_change < 0), -1,  # 价涨量减
                    np.where(
                        (price_change < 0) & (volume_change > 0), -1,  # 价跌量增
                        0
                    )
                )
            )
        )
        
        return pd.Series(coordination, index=data.index)
    
    def _detect_volume_anomaly(self, data: pd.DataFrame) -> pd.Series:
        """检测成交量异常"""
        vol_ma20 = data['volume'].rolling(20).mean()
        vol_ratio = data['volume'] / vol_ma20
        
        # 异常成交量标记
        anomaly = np.where(
            vol_ratio > self.volume_thresholds['abnormal'], 1,  # 异常放大
            np.where(
                vol_ratio < self.volume_thresholds['dry_up'], -1,  # 异常萎缩
                0
            )
        )
        
        return pd.Series(anomaly, index=data.index)
    
    def _analyze_money_flow(self, data: pd.DataFrame) -> pd.Series:
        """分析资金流向"""
        # 简化的资金流向计算
        typical_price = (data['high'] + data['low'] + data['close']) / 3
        money_flow = typical_price * data['volume']
        
        # 正负资金流
        positive_flow = np.where(typical_price > typical_price.shift(1), money_flow, 0)
        negative_flow = np.where(typical_price < typical_price.shift(1), money_flow, 0)
        
        # 资金流向比率
        flow_ratio = (positive_flow - negative_flow) / (positive_flow + negative_flow + 1e-10)
        
        return pd.Series(flow_ratio, index=data.index)
    
    def _detect_vp_divergence(self, data: pd.DataFrame) -> pd.Series:
        """检测量价背离"""
        # 价格趋势
        price_trend = data['close'].rolling(5).mean().diff()
        
        # 成交量趋势
        volume_trend = data['volume'].rolling(5).mean().diff()
        
        # 背离检测
        divergence = np.where(
            (price_trend > 0) & (volume_trend < 0), -1,  # 价格上涨但成交量下降
            np.where(
                (price_trend < 0) & (volume_trend > 0), 1,  # 价格下跌但成交量上升
                0
            )
        )
        
        return pd.Series(divergence, index=data.index)


class TechnicalAnalyzer:
    """技术分析特征引擎主类"""
    
    def __init__(self):
        self.indicator_engine = TechnicalIndicatorEngine()
        self.institutional_analyzer = InstitutionalBehaviorAnalyzer()
        self.pattern_engine = PatternRecognitionEngine()
        self.volume_analyzer = VolumeAnalyzer()
        
        logger.info("技术分析引擎初始化完成")
    
    def extract_technical_features(self, price_data: pd.DataFrame,
                                 institutional_data: Optional[Dict] = None,
                                 symbol: str = '') -> Dict:
        """提取技术分析特征"""
        try:
            if price_data.empty:
                return {'error': '价格数据为空'}
            
            # 计算技术指标
            technical_data = self.indicator_engine.calculate_all_indicators(price_data)
            
            # 主力行为分析
            institutional_data_dict = institutional_data or {}
            technical_data = self.institutional_analyzer.analyze_institutional_behavior(
                technical_data,
                institutional_data_dict.get('dragon_tiger'),
                institutional_data_dict.get('north_bound'),
                institutional_data_dict.get('block_trade')
            )
            
            # 形态识别
            technical_data = self.pattern_engine.recognize_patterns(technical_data)
            
            # 量价关系分析
            technical_data = self.volume_analyzer.analyze_volume_price_relationship(technical_data)
            
            # 计算技术面综合评分
            technical_score = self._calculate_technical_score(technical_data)
            
            # 提取最新特征
            latest_features = self._extract_latest_features(technical_data)
            
            result = {
                'symbol': symbol,
                'technical_score': technical_score,
                'latest_features': latest_features,
                'signal_summary': self._generate_signal_summary(technical_data),
                'analysis_date': datetime.now()
            }
            
            logger.info(f"成功提取技术特征 {symbol}, 评分: {technical_score:.3f}")
            return result
            
        except Exception as e:
            logger.error(f"技术特征提取失败 {symbol}: {e}")
            return {'error': str(e)}
    
    def _calculate_technical_score(self, data: pd.DataFrame) -> float:
        """计算技术面综合评分"""
        if data.empty:
            return 0
        
        latest = data.iloc[-1]
        score = 0.5  # 基础分数
        
        # 趋势评分 (权重30%)
        if latest.get('TREND_STRENGTH', 0) > 0.2:
            score += 0.15
        elif latest.get('TREND_STRENGTH', 0) > 0:
            score += 0.10
        elif latest.get('TREND_STRENGTH', 0) < -0.2:
            score -= 0.15
        
        # 动量评分 (权重25%)
        rsi = latest.get('RSI_14', 50)
        if 30 < rsi < 70:
            score += 0.125
        elif rsi > 80:
            score -= 0.10
        elif rsi < 20:
            score -= 0.05
        
        # 成交量评分 (权重20%)
        vol_surge = latest.get('VOLUME_SURGE', 1)
        if vol_surge > 2:
            score += 0.10
        elif vol_surge > 1.5:
            score += 0.05
        elif vol_surge < 0.5:
            score -= 0.10
        
        # 主力行为评分 (权重15%)
        institutional_score = latest.get('INSTITUTIONAL_SCORE', 0)
        score += institutional_score * 0.15
        
        # 形态评分 (权重10%)
        pattern_strength = latest.get('PATTERN_STRENGTH', 0)
        score += pattern_strength * 0.10
        
        return min(max(score, 0), 1)
    
    def _extract_latest_features(self, data: pd.DataFrame) -> Dict:
        """提取最新特征值"""
        if data.empty:
            return {}
        
        latest = data.iloc[-1]
        
        return {
            # 趋势指标
            'rsi_14': latest.get('RSI_14', 0),
            'macd': latest.get('MACD', 0),
            'adx': latest.get('ADX', 0),
            'trend_strength': latest.get('TREND_STRENGTH', 0),
            
            # 成交量指标
            'volume_surge': latest.get('VOLUME_SURGE', 0),
            'obv': latest.get('OBV', 0),
            'mfi': latest.get('MFI', 0),
            
            # 主力行为
            'institutional_score': latest.get('INSTITUTIONAL_SCORE', 0),
            'dt_net_ratio': latest.get('DT_NET_RATIO', 0),
            'nb_holding_change': latest.get('NB_HOLDING_CHANGE', 0),
            
            # 形态识别
            'pattern_strength': latest.get('PATTERN_STRENGTH', 0),
            'breakout_signal': latest.get('PATTERN_BREAKOUT', 0),
            'reversal_signal': latest.get('PATTERN_REVERSAL', 0),
            
            # 量价关系
            'vp_coordination': latest.get('VP_COORDINATION', 0),
            'volume_anomaly': latest.get('VOLUME_ANOMALY', 0),
            'money_flow': latest.get('MONEY_FLOW', 0)
        }
    
    def _generate_signal_summary(self, data: pd.DataFrame) -> Dict:
        """生成信号汇总"""
        if data.empty:
            return {}
        
        latest = data.iloc[-1]
        
        signals = []
        
        # 趋势信号
        if latest.get('TREND_STRENGTH', 0) > 0.3:
            signals.append({'type': 'trend', 'signal': 'strong_uptrend', 'strength': latest.get('TREND_STRENGTH', 0)})
        elif latest.get('TREND_STRENGTH', 0) < -0.3:
            signals.append({'type': 'trend', 'signal': 'strong_downtrend', 'strength': abs(latest.get('TREND_STRENGTH', 0))})
        
        # 突破信号
        if latest.get('PATTERN_BREAKOUT', 0) > 0:
            signals.append({'type': 'pattern', 'signal': 'breakout', 'strength': latest.get('BREAKOUT_STRENGTH', 0)})
        
        # 成交量异常
        if latest.get('VOLUME_ANOMALY', 0) > 0:
            signals.append({'type': 'volume', 'signal': 'volume_surge', 'strength': latest.get('VOLUME_SURGE', 0)})
        
        # 主力行为
        if latest.get('INSTITUTIONAL_SCORE', 0) > 0.5:
            signals.append({'type': 'institutional', 'signal': 'institutional_buying', 'strength': latest.get('INSTITUTIONAL_SCORE', 0)})
        elif latest.get('INSTITUTIONAL_SCORE', 0) < -0.5:
            signals.append({'type': 'institutional', 'signal': 'institutional_selling', 'strength': abs(latest.get('INSTITUTIONAL_SCORE', 0))})
        
        return {
            'total_signals': len(signals),
            'signals': signals,
            'overall_sentiment': 'bullish' if len([s for s in signals if 'buying' in s['signal'] or 'uptrend' in s['signal'] or 'breakout' in s['signal']]) > len(signals) / 2 else 'bearish'
        }
    
    def batch_technical_analysis(self, price_data_dict: Dict[str, pd.DataFrame],
                                institutional_data_dict: Optional[Dict[str, Dict]] = None) -> Dict[str, Dict]:
        """批量技术分析"""
        results = {}
        institutional_data_dict = institutional_data_dict or {}
        
        for symbol, price_data in price_data_dict.items():
            try:
                institutional_data = institutional_data_dict.get(symbol)
                analysis = self.extract_technical_features(price_data, institutional_data, symbol)
                
                if 'error' not in analysis:
                    results[symbol] = analysis
                    
            except Exception as e:
                logger.error(f"批量技术分析失败 {symbol}: {e}")
                continue
        
        logger.info(f"批量技术分析完成，成功分析 {len(results)} 只股票")
        return results
    
    def get_top_technical_stocks(self, analysis_results: Dict[str, Dict],
                               top_n: int = 20) -> List[Tuple[str, float]]:
        """获取技术面最强的股票"""
        scores = {symbol: data['technical_score']
                 for symbol, data in analysis_results.items()
                 if 'technical_score' in data}
        
        sorted_stocks = sorted(scores.items(), key=lambda x: x[1], reverse=True)
        return sorted_stocks[:top_n]