"""
情绪分析特征系统

实现新闻文本情绪分析NLP模型
构建社交媒体热度监控系统
编写市场情绪指标计算算法
实现舆论发酵程度量化评估
专注于识别市场情绪对股价的影响
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union
from datetime import datetime, timedelta
import logging
from dataclasses import dataclass
from enum import Enum
import re
import json

logger = logging.getLogger(__name__)


class SentimentLevel(Enum):
    """情绪水平枚举"""
    VERY_NEGATIVE = -2
    NEGATIVE = -1
    NEUTRAL = 0
    POSITIVE = 1
    VERY_POSITIVE = 2


@dataclass
class NewsItem:
    """新闻项目数据结构"""
    title: str
    content: str
    source: str
    publish_time: datetime
    sentiment_score: float
    keywords: List[str]
    impact_score: float


@dataclass
class SocialMediaPost:
    """社交媒体帖子数据结构"""
    platform: str
    content: str
    author: str
    publish_time: datetime
    likes: int
    shares: int
    comments: int
    sentiment_score: float
    influence_score: float


@dataclass
class MarketSentiment:
    """市场情绪数据结构"""
    symbol: str
    date: datetime
    news_sentiment: float
    social_sentiment: float
    search_heat: float
    discussion_volume: int
    overall_sentiment: float
    sentiment_change: float
    fermentation_level: float


class NewsTextAnalyzer:
    """新闻文本情绪分析器"""
    
    def __init__(self):
        # 情绪词典
        self.positive_words = {
            '利好', '上涨', '增长', '盈利', '突破', '创新高', '买入', '推荐', 
            '看好', '乐观', '强势', '反弹', '回升', '涨停', '大涨', '飙升',
            '业绩增长', '营收增长', '净利润增长', '订单增加', '合作', '中标',
            '重组', '并购', '分红', '送股', '高送转', '股权激励'
        }
        
        self.negative_words = {
            '利空', '下跌', '下滑', '亏损', '跌破', '创新低', '卖出', '减持',
            '看空', '悲观', '弱势', '暴跌', '跌停', '大跌', '暴跌', '闪崩',
            '业绩下滑', '营收下降', '净利润下降', '订单减少', '违约', '退市',
            '停牌', '调查', '处罚', '诉讼', '债务', '资金链', '爆雷'
        }
        
        # 强度修饰词
        self.intensity_modifiers = {
            '非常': 1.5, '极其': 1.5, '特别': 1.3, '很': 1.2, '比较': 0.8,
            '稍微': 0.6, '略微': 0.6, '有点': 0.7, '相当': 1.1, '十分': 1.3
        }
        
        # 否定词
        self.negation_words = {'不', '没', '无', '非', '未', '否', '别', '勿'}
    
    def analyze_news_sentiment(self, news_list: List[NewsItem]) -> Dict:
        """分析新闻情绪"""
        try:
            if not news_list:
                return {'overall_sentiment': 0, 'news_count': 0, 'sentiment_distribution': {}}
            
            sentiment_scores = []
            sentiment_distribution = {'positive': 0, 'negative': 0, 'neutral': 0}
            
            for news in news_list:
                # 分析单条新闻
                sentiment_score = self._analyze_single_news(news)
                news.sentiment_score = sentiment_score
                sentiment_scores.append(sentiment_score)
                
                # 统计情绪分布
                if sentiment_score > 0.1:
                    sentiment_distribution['positive'] += 1
                elif sentiment_score < -0.1:
                    sentiment_distribution['negative'] += 1
                else:
                    sentiment_distribution['neutral'] += 1
            
            # 计算整体情绪
            overall_sentiment = np.mean(sentiment_scores) if sentiment_scores else 0
            
            # 计算情绪强度（标准差）
            sentiment_intensity = np.std(sentiment_scores) if len(sentiment_scores) > 1 else 0
            
            return {
                'overall_sentiment': overall_sentiment,
                'sentiment_intensity': sentiment_intensity,
                'news_count': len(news_list),
                'sentiment_distribution': sentiment_distribution,
                'sentiment_scores': sentiment_scores
            }
            
        except Exception as e:
            logger.error(f"新闻情绪分析失败: {e}")
            return {'overall_sentiment': 0, 'news_count': 0, 'sentiment_distribution': {}}
    
    def _analyze_single_news(self, news: NewsItem) -> float:
        """分析单条新闻情绪"""
        text = f"{news.title} {news.content}"
        
        # 文本预处理
        text = self._preprocess_text(text)
        
        # 计算情绪得分
        sentiment_score = self._calculate_sentiment_score(text)
        
        # 根据新闻来源调整权重
        source_weight = self._get_source_weight(news.source)
        
        # 根据发布时间调整权重（越新权重越高）
        time_weight = self._get_time_weight(news.publish_time)
        
        final_score = sentiment_score * source_weight * time_weight
        
        return max(min(final_score, 1.0), -1.0)  # 限制在[-1, 1]范围内
    
    def _preprocess_text(self, text: str) -> str:
        """文本预处理"""
        # 去除特殊字符，保留中文、英文、数字
        text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s]', '', text)
        
        # 转换为小写（对英文）
        text = text.lower()
        
        return text
    
    def _calculate_sentiment_score(self, text: str) -> float:
        """计算情绪得分"""
        words = text.split()
        total_score = 0
        word_count = 0
        
        i = 0
        while i < len(words):
            word = words[i]
            
            # 检查否定词
            negation = False
            if i > 0 and words[i-1] in self.negation_words:
                negation = True
            
            # 检查强度修饰词
            intensity = 1.0
            if i > 0 and words[i-1] in self.intensity_modifiers:
                intensity = self.intensity_modifiers[words[i-1]]
            
            # 计算词汇情绪得分
            word_score = 0
            if word in self.positive_words:
                word_score = 1.0
            elif word in self.negative_words:
                word_score = -1.0
            
            # 应用修饰
            if word_score != 0:
                word_score *= intensity
                if negation:
                    word_score *= -1
                
                total_score += word_score
                word_count += 1
            
            i += 1
        
        # 计算平均情绪得分
        if word_count > 0:
            return total_score / word_count
        else:
            return 0
    
    def _get_source_weight(self, source: str) -> float:
        """获取新闻源权重"""
        source_weights = {
            '新华社': 1.2, '人民日报': 1.2, '央视新闻': 1.2,
            '证券时报': 1.1, '上海证券报': 1.1, '中国证券报': 1.1,
            '财经': 1.0, '第一财经': 1.0, '21世纪经济报道': 1.0,
            '新浪财经': 0.9, '腾讯财经': 0.9, '网易财经': 0.9,
            '东方财富': 0.8, '同花顺': 0.8, '雪球': 0.7
        }
        
        return source_weights.get(source, 0.8)  # 默认权重0.8
    
    def _get_time_weight(self, publish_time: datetime) -> float:
        """获取时间权重"""
        now = datetime.now()
        time_diff = (now - publish_time).total_seconds() / 3600  # 小时差
        
        if time_diff <= 1:
            return 1.2  # 1小时内权重最高
        elif time_diff <= 6:
            return 1.1  # 6小时内
        elif time_diff <= 24:
            return 1.0  # 24小时内
        elif time_diff <= 72:
            return 0.9  # 3天内
        else:
            return 0.7  # 3天以上


class SocialMediaAnalyzer:
    """社交媒体热度监控分析器"""
    
    def __init__(self):
        self.platform_weights = {
            '微博': 1.0,
            '微信': 0.9,
            '抖音': 0.8,
            '知乎': 1.1,
            '雪球': 1.2,
            '东方财富股吧': 0.7,
            '同花顺': 0.7
        }
    
    def analyze_social_sentiment(self, posts: List[SocialMediaPost]) -> Dict:
        """分析社交媒体情绪"""
        try:
            if not posts:
                return {'overall_sentiment': 0, 'heat_index': 0, 'influence_score': 0}
            
            sentiment_scores = []
            influence_scores = []
            total_engagement = 0
            
            for post in posts:
                # 分析单个帖子
                post_analysis = self._analyze_single_post(post)
                sentiment_scores.append(post_analysis['sentiment'])
                influence_scores.append(post_analysis['influence'])
                total_engagement += post_analysis['engagement']
            
            # 计算整体指标
            overall_sentiment = np.average(sentiment_scores, weights=influence_scores) if sentiment_scores else 0
            heat_index = self._calculate_heat_index(posts, total_engagement)
            influence_score = np.mean(influence_scores) if influence_scores else 0
            
            return {
                'overall_sentiment': overall_sentiment,
                'heat_index': heat_index,
                'influence_score': influence_score,
                'post_count': len(posts),
                'total_engagement': total_engagement,
                'sentiment_distribution': self._get_sentiment_distribution(sentiment_scores)
            }
            
        except Exception as e:
            logger.error(f"社交媒体情绪分析失败: {e}")
            return {'overall_sentiment': 0, 'heat_index': 0, 'influence_score': 0}
    
    def _analyze_single_post(self, post: SocialMediaPost) -> Dict:
        """分析单个社交媒体帖子"""
        # 简化的情绪分析（实际应该使用更复杂的NLP模型）
        content = post.content.lower()
        
        # 基于关键词的简单情绪分析
        positive_keywords = ['好', '涨', '牛', '买', '看好', '利好', '强势']
        negative_keywords = ['差', '跌', '熊', '卖', '看空', '利空', '弱势']
        
        positive_count = sum(1 for word in positive_keywords if word in content)
        negative_count = sum(1 for word in negative_keywords if word in content)
        
        if positive_count > negative_count:
            sentiment = 0.5 + (positive_count - negative_count) * 0.1
        elif negative_count > positive_count:
            sentiment = -0.5 - (negative_count - positive_count) * 0.1
        else:
            sentiment = 0
        
        sentiment = max(min(sentiment, 1.0), -1.0)
        
        # 计算互动度
        engagement = post.likes + post.shares * 2 + post.comments * 3
        
        # 计算影响力得分
        platform_weight = self.platform_weights.get(post.platform, 0.5)
        influence = (engagement / 100) * platform_weight  # 标准化
        influence = min(influence, 10.0)  # 限制最大值
        
        return {
            'sentiment': sentiment,
            'engagement': engagement,
            'influence': influence
        }
    
    def _calculate_heat_index(self, posts: List[SocialMediaPost], total_engagement: int) -> float:
        """计算热度指数"""
        if not posts:
            return 0
        
        # 基于帖子数量和互动度计算热度
        post_count_score = min(len(posts) / 100, 1.0)  # 帖子数量得分
        engagement_score = min(total_engagement / 10000, 1.0)  # 互动度得分
        
        # 时间衰减因子
        now = datetime.now()
        time_scores = []
        for post in posts:
            hours_ago = (now - post.publish_time).total_seconds() / 3600
            time_score = max(1 - hours_ago / 24, 0.1)  # 24小时内线性衰减
            time_scores.append(time_score)
        
        time_factor = np.mean(time_scores) if time_scores else 0.1
        
        heat_index = (post_count_score * 0.4 + engagement_score * 0.6) * time_factor
        
        return min(heat_index * 100, 100)  # 转换为0-100的热度指数
    
    def _get_sentiment_distribution(self, sentiment_scores: List[float]) -> Dict:
        """获取情绪分布"""
        if not sentiment_scores:
            return {'positive': 0, 'negative': 0, 'neutral': 0}
        
        positive = sum(1 for score in sentiment_scores if score > 0.1)
        negative = sum(1 for score in sentiment_scores if score < -0.1)
        neutral = len(sentiment_scores) - positive - negative
        
        total = len(sentiment_scores)
        return {
            'positive': positive / total,
            'negative': negative / total,
            'neutral': neutral / total
        }


class MarketSentimentIndicator:
    """市场情绪指标计算器"""
    
    def __init__(self):
        self.fear_greed_weights = {
            'vix': 0.25,        # 恐慌指数
            'put_call': 0.20,   # 看跌看涨比
            'momentum': 0.20,   # 市场动量
            'demand': 0.15,     # 市场需求
            'breadth': 0.20     # 市场广度
        }
    
    def calculate_market_sentiment_indicators(self, market_data: Dict) -> Dict:
        """计算市场情绪指标"""
        try:
            indicators = {}
            
            # 恐慌贪婪指数
            indicators['fear_greed_index'] = self._calculate_fear_greed_index(market_data)
            
            # 投资者情绪指数
            indicators['investor_sentiment'] = self._calculate_investor_sentiment(market_data)
            
            # 市场热度指数
            indicators['market_heat'] = self._calculate_market_heat(market_data)
            
            # 资金情绪指标
            indicators['money_sentiment'] = self._calculate_money_sentiment(market_data)
            
            # 板块轮动指标
            indicators['sector_rotation'] = self._calculate_sector_rotation(market_data)
            
            # 综合情绪评分
            indicators['overall_sentiment'] = self._calculate_overall_sentiment(indicators)
            
            return indicators
            
        except Exception as e:
            logger.error(f"市场情绪指标计算失败: {e}")
            return {}
    
    def _calculate_fear_greed_index(self, market_data: Dict) -> float:
        """计算恐慌贪婪指数"""
        # 模拟计算（实际需要真实的VIX、看跌看涨比等数据）
        
        # VIX指标（波动率指数）
        vix_score = 50  # 默认中性
        if 'vix' in market_data:
            vix = market_data['vix']
            if vix < 15:
                vix_score = 80  # 贪婪
            elif vix < 20:
                vix_score = 65
            elif vix > 30:
                vix_score = 20  # 恐慌
            elif vix > 25:
                vix_score = 35
        
        # 看跌看涨比
        put_call_score = 50
        if 'put_call_ratio' in market_data:
            ratio = market_data['put_call_ratio']
            if ratio < 0.8:
                put_call_score = 75  # 贪婪
            elif ratio > 1.2:
                put_call_score = 25  # 恐慌
        
        # 市场动量
        momentum_score = 50
        if 'market_momentum' in market_data:
            momentum = market_data['market_momentum']
            momentum_score = max(min(50 + momentum * 50, 100), 0)
        
        # 市场需求（新股认购倍数等）
        demand_score = 50
        if 'ipo_demand' in market_data:
            demand = market_data['ipo_demand']
            demand_score = max(min(demand * 10, 100), 0)
        
        # 市场广度（上涨股票比例）
        breadth_score = 50
        if 'advance_decline_ratio' in market_data:
            ratio = market_data['advance_decline_ratio']
            breadth_score = ratio * 100
        
        # 加权计算
        fear_greed_index = (
            vix_score * self.fear_greed_weights['vix'] +
            put_call_score * self.fear_greed_weights['put_call'] +
            momentum_score * self.fear_greed_weights['momentum'] +
            demand_score * self.fear_greed_weights['demand'] +
            breadth_score * self.fear_greed_weights['breadth']
        )
        
        return max(min(fear_greed_index, 100), 0)
    
    def _calculate_investor_sentiment(self, market_data: Dict) -> float:
        """计算投资者情绪指数"""
        # 基于多个指标综合计算
        sentiment_factors = []
        
        # 融资融券余额变化
        if 'margin_balance_change' in market_data:
            margin_change = market_data['margin_balance_change']
            sentiment_factors.append(max(min(margin_change * 100, 50), -50))
        
        # 新开户数变化
        if 'new_accounts_change' in market_data:
            accounts_change = market_data['new_accounts_change']
            sentiment_factors.append(max(min(accounts_change * 50, 30), -30))
        
        # 基金申购赎回比
        if 'fund_purchase_redemption_ratio' in market_data:
            ratio = market_data['fund_purchase_redemption_ratio']
            sentiment_factors.append((ratio - 1) * 50)
        
        # 计算平均值
        if sentiment_factors:
            return 50 + np.mean(sentiment_factors)  # 基准50，上下浮动
        else:
            return 50
    
    def _calculate_market_heat(self, market_data: Dict) -> float:
        """计算市场热度指数"""
        heat_factors = []
        
        # 成交量比率
        if 'volume_ratio' in market_data:
            vol_ratio = market_data['volume_ratio']
            heat_factors.append(min(vol_ratio * 50, 100))
        
        # 换手率
        if 'turnover_rate' in market_data:
            turnover = market_data['turnover_rate']
            heat_factors.append(min(turnover * 10, 100))
        
        # 涨停股票数量
        if 'limit_up_count' in market_data:
            limit_up = market_data['limit_up_count']
            heat_factors.append(min(limit_up * 2, 100))
        
        # 热门概念股表现
        if 'hot_concept_performance' in market_data:
            concept_perf = market_data['hot_concept_performance']
            heat_factors.append(max(min(concept_perf * 100, 100), 0))
        
        return np.mean(heat_factors) if heat_factors else 50
    
    def _calculate_money_sentiment(self, market_data: Dict) -> float:
        """计算资金情绪指标"""
        money_factors = []
        
        # 北向资金净流入
        if 'northbound_net_inflow' in market_data:
            inflow = market_data['northbound_net_inflow']
            money_factors.append(max(min(inflow / 1000000000 * 50, 50), -50))  # 以10亿为基准
        
        # 主力资金净流入
        if 'main_fund_net_inflow' in market_data:
            main_inflow = market_data['main_fund_net_inflow']
            money_factors.append(max(min(main_inflow / 1000000000 * 30, 30), -30))
        
        # 融资买入额变化
        if 'margin_buy_change' in market_data:
            margin_buy = market_data['margin_buy_change']
            money_factors.append(max(min(margin_buy * 20, 20), -20))
        
        return 50 + np.mean(money_factors) if money_factors else 50
    
    def _calculate_sector_rotation(self, market_data: Dict) -> float:
        """计算板块轮动指标"""
        if 'sector_performance' not in market_data:
            return 50
        
        sector_perf = market_data['sector_performance']
        
        # 计算板块表现的标准差（轮动强度）
        performances = list(sector_perf.values())
        if len(performances) < 2:
            return 50
        
        rotation_intensity = np.std(performances) * 100
        
        # 标准化到0-100
        return min(rotation_intensity, 100)
    
    def _calculate_overall_sentiment(self, indicators: Dict) -> float:
        """计算综合情绪评分"""
        weights = {
            'fear_greed_index': 0.30,
            'investor_sentiment': 0.25,
            'market_heat': 0.20,
            'money_sentiment': 0.15,
            'sector_rotation': 0.10
        }
        
        weighted_sum = 0
        total_weight = 0
        
        for indicator, weight in weights.items():
            if indicator in indicators:
                weighted_sum += indicators[indicator] * weight
                total_weight += weight
        
        return weighted_sum / total_weight if total_weight > 0 else 50


class FermentationAnalyzer:
    """舆论发酵程度量化评估器"""
    
    def __init__(self):
        self.fermentation_stages = {
            'initial': 0.2,      # 初始阶段
            'spreading': 0.4,    # 传播阶段
            'amplifying': 0.6,   # 放大阶段
            'peak': 0.8,         # 高峰阶段
            'declining': 0.3     # 衰减阶段
        }
    
    def analyze_fermentation_level(self, news_timeline: List[NewsItem],
                                 social_timeline: List[SocialMediaPost],
                                 time_window_hours: int = 72) -> Dict:
        """分析舆论发酵程度"""
        try:
            # 按时间排序
            all_items = []
            
            for news in news_timeline:
                all_items.append({
                    'time': news.publish_time,
                    'type': 'news',
                    'sentiment': news.sentiment_score,
                    'impact': news.impact_score
                })
            
            for post in social_timeline:
                all_items.append({
                    'time': post.publish_time,
                    'type': 'social',
                    'sentiment': post.sentiment_score,
                    'impact': post.influence_score
                })
            
            all_items.sort(key=lambda x: x['time'])
            
            # 分析发酵过程
            fermentation_analysis = self._analyze_fermentation_process(all_items, time_window_hours)
            
            # 计算发酵强度
            fermentation_intensity = self._calculate_fermentation_intensity(all_items)
            
            # 预测发酵趋势
            trend_prediction = self._predict_fermentation_trend(all_items)
            
            return {
                'fermentation_level': fermentation_analysis['level'],
                'fermentation_stage': fermentation_analysis['stage'],
                'intensity': fermentation_intensity,
                'trend_prediction': trend_prediction,
                'timeline_analysis': fermentation_analysis['timeline'],
                'peak_time': fermentation_analysis.get('peak_time'),
                'duration_hours': fermentation_analysis.get('duration_hours', 0)
            }
            
        except Exception as e:
            logger.error(f"舆论发酵分析失败: {e}")
            return {'fermentation_level': 0, 'fermentation_stage': 'initial'}
    
    def _analyze_fermentation_process(self, items: List[Dict], time_window_hours: int) -> Dict:
        """分析发酵过程"""
        if not items:
            return {'level': 0, 'stage': 'initial', 'timeline': []}
        
        # 按小时分组统计
        now = datetime.now()
        start_time = now - timedelta(hours=time_window_hours)
        
        hourly_stats = {}
        for hour in range(time_window_hours):
            hour_start = start_time + timedelta(hours=hour)
            hour_end = hour_start + timedelta(hours=1)
            
            hour_items = [item for item in items 
                         if hour_start <= item['time'] < hour_end]
            
            if hour_items:
                hourly_stats[hour] = {
                    'count': len(hour_items),
                    'avg_sentiment': np.mean([item['sentiment'] for item in hour_items]),
                    'total_impact': sum([item['impact'] for item in hour_items])
                }
            else:
                hourly_stats[hour] = {'count': 0, 'avg_sentiment': 0, 'total_impact': 0}
        
        # 识别发酵阶段
        counts = [stats['count'] for stats in hourly_stats.values()]
        impacts = [stats['total_impact'] for stats in hourly_stats.values()]
        
        # 找到峰值
        max_count_hour = max(range(len(counts)), key=lambda i: counts[i])
        max_impact_hour = max(range(len(impacts)), key=lambda i: impacts[i])
        
        # 判断发酵阶段
        current_hour = time_window_hours - 1
        recent_trend = self._calculate_trend(counts[-6:])  # 最近6小时趋势
        
        if recent_trend > 0.5:
            stage = 'amplifying'
        elif recent_trend > 0:
            stage = 'spreading'
        elif recent_trend < -0.5:
            stage = 'declining'
        elif max(counts) > 0:
            stage = 'peak' if current_hour == max_count_hour else 'declining'
        else:
            stage = 'initial'
        
        # 计算发酵水平
        max_count = max(counts) if counts else 0
        avg_impact = np.mean(impacts) if impacts else 0
        level = min((max_count * 0.1 + avg_impact * 0.1), 1.0)
        
        return {
            'level': level,
            'stage': stage,
            'timeline': hourly_stats,
            'peak_time': start_time + timedelta(hours=max_count_hour) if max_count > 0 else None,
            'duration_hours': len([c for c in counts if c > 0])
        }
    
    def _calculate_fermentation_intensity(self, items: List[Dict]) -> float:
        """计算发酵强度"""
        if not items:
            return 0
        
        # 时间密度
        time_span = (items[-1]['time'] - items[0]['time']).total_seconds() / 3600  # 小时
        time_density = len(items) / max(time_span, 1)
        
        # 情绪强度
        sentiments = [abs(item['sentiment']) for item in items]
        sentiment_intensity = np.mean(sentiments) if sentiments else 0
        
        # 影响力强度
        impacts = [item['impact'] for item in items]
        impact_intensity = np.mean(impacts) if impacts else 0
        
        # 综合强度
        intensity = (time_density * 0.4 + sentiment_intensity * 0.3 + impact_intensity * 0.3)
        
        return min(intensity, 1.0)
    
    def _predict_fermentation_trend(self, items: List[Dict]) -> str:
        """预测发酵趋势"""
        if len(items) < 6:
            return 'insufficient_data'
        
        # 分析最近趋势
        recent_items = items[-6:]  # 最近6个数据点
        
        # 计算数量趋势
        time_intervals = []
        for i in range(1, len(recent_items)):
            interval = (recent_items[i]['time'] - recent_items[i-1]['time']).total_seconds() / 3600
            time_intervals.append(interval)
        
        # 如果时间间隔越来越短，说明在加速发酵
        if len(time_intervals) >= 3:
            trend = self._calculate_trend(time_intervals)
            if trend < -0.3:
                return 'accelerating'
            elif trend > 0.3:
                return 'decelerating'
        
        # 分析情绪趋势
        recent_sentiments = [abs(item['sentiment']) for item in recent_items]
        sentiment_trend = self._calculate_trend(recent_sentiments)
        
        if sentiment_trend > 0.2:
            return 'intensifying'
        elif sentiment_trend < -0.2:
            return 'cooling'
        else:
            return 'stable'
    
    def _calculate_trend(self, values: List[float]) -> float:
        """计算趋势（简单线性回归斜率）"""
        if len(values) < 2:
            return 0
        
        n = len(values)
        x = list(range(n))
        
        # 计算线性回归斜率
        x_mean = np.mean(x)
        y_mean = np.mean(values)
        
        numerator = sum((x[i] - x_mean) * (values[i] - y_mean) for i in range(n))
        denominator = sum((x[i] - x_mean) ** 2 for i in range(n))
        
        if denominator == 0:
            return 0
        
        slope = numerator / denominator
        
        # 标准化斜率
        return max(min(slope, 1.0), -1.0)


class SentimentAnalyzer:
    """情绪分析特征系统主类"""
    
    def __init__(self):
        self.news_analyzer = NewsTextAnalyzer()
        self.social_analyzer = SocialMediaAnalyzer()
        self.market_indicator = MarketSentimentIndicator()
        self.fermentation_analyzer = FermentationAnalyzer()
        
        logger.info("情绪分析系统初始化完成")
    
    def extract_sentiment_features(self, news_data: List[NewsItem],
                                 social_data: List[SocialMediaPost],
                                 market_data: Dict,
                                 symbol: str = '') -> Dict:
        """提取情绪分析特征"""
        try:
            # 新闻情绪分析
            news_sentiment = self.news_analyzer.analyze_news_sentiment(news_data)
            
            # 社交媒体情绪分析
            social_sentiment = self.social_analyzer.analyze_social_sentiment(social_data)
            
            # 市场情绪指标
            market_sentiment = self.market_indicator.calculate_market_sentiment_indicators(market_data)
            
            # 舆论发酵分析
            fermentation_analysis = self.fermentation_analyzer.analyze_fermentation_level(
                news_data, social_data
            )
            
            # 计算综合情绪评分
            overall_sentiment_score = self._calculate_overall_sentiment_score(
                news_sentiment, social_sentiment, market_sentiment, fermentation_analysis
            )
            
            # 构建特征结果
            result = {
                'symbol': symbol,
                'news_sentiment': news_sentiment,
                'social_sentiment': social_sentiment,
                'market_sentiment': market_sentiment,
                'fermentation_analysis': fermentation_analysis,
                'overall_sentiment_score': overall_sentiment_score,
                'sentiment_summary': self._generate_sentiment_summary(
                    news_sentiment, social_sentiment, market_sentiment
                ),
                'analysis_date': datetime.now()
            }
            
            logger.info(f"成功提取情绪特征 {symbol}, 综合评分: {overall_sentiment_score:.3f}")
            return result
            
        except Exception as e:
            logger.error(f"情绪特征提取失败 {symbol}: {e}")
            return {'error': str(e)}
    
    def _calculate_overall_sentiment_score(self, news_sentiment: Dict,
                                         social_sentiment: Dict,
                                         market_sentiment: Dict,
                                         fermentation_analysis: Dict) -> float:
        """计算综合情绪评分"""
        # 权重分配
        weights = {
            'news': 0.35,
            'social': 0.25,
            'market': 0.30,
            'fermentation': 0.10
        }
        
        # 标准化各项得分到0-1范围
        news_score = (news_sentiment.get('overall_sentiment', 0) + 1) / 2  # -1~1 -> 0~1
        social_score = (social_sentiment.get('overall_sentiment', 0) + 1) / 2
        market_score = market_sentiment.get('overall_sentiment', 50) / 100  # 0~100 -> 0~1
        fermentation_score = fermentation_analysis.get('fermentation_level', 0)
        
        # 加权计算
        overall_score = (
            news_score * weights['news'] +
            social_score * weights['social'] +
            market_score * weights['market'] +
            fermentation_score * weights['fermentation']
        )
        
        return max(min(overall_score, 1.0), 0.0)
    
    def _generate_sentiment_summary(self, news_sentiment: Dict,
                                  social_sentiment: Dict,
                                  market_sentiment: Dict) -> Dict:
        """生成情绪汇总"""
        summary = {
            'dominant_sentiment': 'neutral',
            'sentiment_strength': 'weak',
            'key_factors': [],
            'risk_alerts': []
        }
        
        # 判断主导情绪
        news_sent = news_sentiment.get('overall_sentiment', 0)
        social_sent = social_sentiment.get('overall_sentiment', 0)
        market_sent = (market_sentiment.get('overall_sentiment', 50) - 50) / 50  # 标准化到-1~1
        
        avg_sentiment = (news_sent + social_sent + market_sent) / 3
        
        if avg_sentiment > 0.2:
            summary['dominant_sentiment'] = 'positive'
        elif avg_sentiment < -0.2:
            summary['dominant_sentiment'] = 'negative'
        
        # 判断情绪强度
        sentiment_intensity = abs(avg_sentiment)
        if sentiment_intensity > 0.6:
            summary['sentiment_strength'] = 'very_strong'
        elif sentiment_intensity > 0.4:
            summary['sentiment_strength'] = 'strong'
        elif sentiment_intensity > 0.2:
            summary['sentiment_strength'] = 'moderate'
        
        # 识别关键因素
        if news_sentiment.get('news_count', 0) > 10:
            summary['key_factors'].append('高新闻关注度')
        
        if social_sentiment.get('heat_index', 0) > 70:
            summary['key_factors'].append('社交媒体热议')
        
        if market_sentiment.get('fear_greed_index', 50) > 80:
            summary['key_factors'].append('市场贪婪情绪')
        elif market_sentiment.get('fear_greed_index', 50) < 20:
            summary['key_factors'].append('市场恐慌情绪')
        
        # 风险预警
        if abs(news_sent - social_sent) > 0.5:
            summary['risk_alerts'].append('新闻与社交媒体情绪分歧较大')
        
        if market_sentiment.get('fear_greed_index', 50) < 20:
            summary['risk_alerts'].append('市场恐慌情绪严重')
        
        return summary
    
    def batch_sentiment_analysis(self, data_dict: Dict[str, Dict]) -> Dict[str, Dict]:
        """批量情绪分析"""
        results = {}
        
        for symbol, data in data_dict.items():
            try:
                news_data = data.get('news', [])
                social_data = data.get('social', [])
                market_data = data.get('market', {})
                
                analysis = self.extract_sentiment_features(
                    news_data, social_data, market_data, symbol
                )
                
                if 'error' not in analysis:
                    results[symbol] = analysis
                    
            except Exception as e:
                logger.error(f"批量情绪分析失败 {symbol}: {e}")
                continue
        
        logger.info(f"批量情绪分析完成，成功分析 {len(results)} 只股票")
        return results
    
    def get_top_sentiment_stocks(self, analysis_results: Dict[str, Dict],
                               sentiment_type: str = 'positive',
                               top_n: int = 20) -> List[Tuple[str, float]]:
        """获取情绪最强的股票"""
        scores = {}
        
        for symbol, data in analysis_results.items():
            if 'overall_sentiment_score' in data:
                score = data['overall_sentiment_score']
                if sentiment_type == 'negative':
                    score = 1 - score  # 反转得分
                scores[symbol] = score
        
        sorted_stocks = sorted(scores.items(), key=lambda x: x[1], reverse=True)
        return sorted_stocks[:top_n]