"""
估值分析特征计算测试
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# 直接导入模块，避免包导入问题
current_dir = os.path.dirname(__file__)
sys.path.insert(0, current_dir)

from valuation_analyzer import (
    ValuationAnalyzer, DCFValuationModel, PEGValuationModel,
    PBROEValuationModel, RelativeValuationAnalyzer,
    ValuationOpportunityDetector, ValuationAlertSystem
)


def create_sample_data():
    """创建示例数据"""
    # 财务数据
    dates = pd.date_range(start='2020-01-01', end='2023-12-31', freq='QE')
    
    np.random.seed(42)
    base_revenue = 1000
    base_profit = 100
    
    financial_data = []
    for i, date in enumerate(dates):
        growth_factor = 1 + (i * 0.05) + np.random.normal(0, 0.02)
        
        revenue = base_revenue * growth_factor
        profit = base_profit * growth_factor * (1 + np.random.normal(0, 0.1))
        assets = revenue * 2
        equity = assets * 0.6
        
        financial_data.append({
            'date': date,
            'total_revenue': revenue,
            'net_profit': profit,
            'total_assets': assets,
            'total_equity': equity,
            'roe': (profit / equity * 100) if equity > 0 else 0
        })
    
    financial_df = pd.DataFrame(financial_data).set_index('date')
    
    # 市场数据
    market_data = {
        'current_price': 25.0,
        'shares_outstanding': 100,  # 100万股
        'industry_metrics': {
            'avg_pe': 20,
            'avg_pb': 2.5,
            'avg_ps': 3.0
        },
        'market_metrics': {
            'avg_pe': 18,
            'avg_pb': 2.0,
            'avg_ps': 2.5
        }
    }
    
    # 增长预测
    growth_forecast = {
        'growth_indicators': {
            'profit_acceleration': 25,
            'revenue_acceleration': 15,
            'roe_improvement': 2
        }
    }
    
    return financial_df, market_data, growth_forecast


def test_dcf_model():
    """测试DCF估值模型"""
    print("=== 测试DCF估值模型 ===")
    
    dcf_model = DCFValuationModel()
    financial_data, _, growth_forecast = create_sample_data()
    
    try:
        dcf_result = dcf_model.calculate_dcf_value(financial_data, growth_forecast)
        
        if 'error' not in dcf_result:
            print(f"✓ DCF估值计算成功")
            print(f"✓ DCF价值: {dcf_result['dcf_value']:.0f}")
            print(f"✓ 企业价值: {dcf_result['enterprise_value']:.0f}")
            print(f"✓ 终值: {dcf_result['terminal_value']:.0f}")
            print(f"✓ 折现率: {dcf_result['discount_rate']:.1%}")
            print(f"✓ 永续增长率: {dcf_result['terminal_growth']:.1%}")
            
            projected_fcf = dcf_result['projected_fcf']
            print(f"✓ 预测现金流: {[f'{fcf:.0f}' for fcf in projected_fcf[:3]]}")
            
            return dcf_result
        else:
            print(f"✗ DCF估值失败: {dcf_result['error']}")
            return None
            
    except Exception as e:
        print(f"✗ DCF模型测试异常: {e}")
        return None


def test_peg_model():
    """测试PEG估值模型"""
    print("\n=== 测试PEG估值模型 ===")
    
    peg_model = PEGValuationModel()
    
    try:
        # 测试数据
        current_pe = 15
        growth_rate = 25  # 25%增长率
        eps = 2.5
        
        peg_result = peg_model.calculate_peg_valuation(current_pe, growth_rate, eps)
        
        if 'error' not in peg_result:
            print(f"✓ PEG估值计算成功")
            print(f"✓ PEG比率: {peg_result['peg_ratio']:.2f}")
            print(f"✓ 合理PE: {peg_result['fair_pe']:.1f}")
            print(f"✓ 合理价格: {peg_result['fair_price']:.2f}")
            print(f"✓ 估值水平: {peg_result['valuation_level']}")
            print(f"✓ 上涨空间: {peg_result['upside_potential']:.1%}")
            
            return peg_result
        else:
            print(f"✗ PEG估值失败: {peg_result['error']}")
            return None
            
    except Exception as e:
        print(f"✗ PEG模型测试异常: {e}")
        return None


def test_pb_roe_model():
    """测试PB-ROE估值模型"""
    print("\n=== 测试PB-ROE估值模型 ===")
    
    pb_roe_model = PBROEValuationModel()
    
    try:
        # 测试数据
        current_pb = 2.0
        roe = 0.18  # 18% ROE
        bvps = 12.5
        
        pb_roe_result = pb_roe_model.calculate_pb_roe_valuation(current_pb, roe, bvps)
        
        if 'error' not in pb_roe_result:
            print(f"✓ PB-ROE估值计算成功")
            print(f"✓ 合理PB: {pb_roe_result['fair_pb']:.2f}")
            print(f"✓ 合理价格: {pb_roe_result['fair_price']:.2f}")
            print(f"✓ PB-ROE比率: {pb_roe_result['pb_roe_ratio']:.2f}")
            print(f"✓ 估值水平: {pb_roe_result['valuation_level']}")
            print(f"✓ PB溢价: {pb_roe_result['pb_premium']:.1%}")
            print(f"✓ 上涨空间: {pb_roe_result['upside_potential']:.1%}")
            
            return pb_roe_result
        else:
            print(f"✗ PB-ROE估值失败: {pb_roe_result['error']}")
            return None
            
    except Exception as e:
        print(f"✗ PB-ROE模型测试异常: {e}")
        return None


def test_relative_analyzer():
    """测试相对估值分析器"""
    print("\n=== 测试相对估值分析器 ===")
    
    relative_analyzer = RelativeValuationAnalyzer()
    
    try:
        # 测试数据
        stock_metrics = {
            'pe_ratio': 15,
            'pb_ratio': 2.0,
            'ps_ratio': 2.5
        }
        
        industry_metrics = {
            'avg_pe': 20,
            'avg_pb': 2.5,
            'avg_ps': 3.0
        }
        
        market_metrics = {
            'avg_pe': 18,
            'avg_pb': 2.2,
            'avg_ps': 2.8
        }
        
        relative_result = relative_analyzer.analyze_relative_valuation(
            stock_metrics, industry_metrics, market_metrics
        )
        
        if relative_result:
            print(f"✓ 相对估值分析成功")
            
            pe_analysis = relative_result.get('pe_analysis', {})
            print(f"✓ PE相对行业: {pe_analysis.get('vs_industry', 'N/A')} "
                  f"({pe_analysis.get('industry_discount', 0):.1f}%)")
            
            pb_analysis = relative_result.get('pb_analysis', {})
            print(f"✓ PB相对行业: {pb_analysis.get('vs_industry', 'N/A')} "
                  f"({pb_analysis.get('industry_discount', 0):.1f}%)")
            
            overall_score = relative_result.get('overall_score', 0)
            print(f"✓ 相对估值综合评分: {overall_score:.3f}")
            
            return relative_result
        else:
            print("✗ 相对估值分析失败")
            return None
            
    except Exception as e:
        print(f"✗ 相对估值分析异常: {e}")
        return None


def test_opportunity_detector():
    """测试估值机会检测器"""
    print("\n=== 测试估值机会检测器 ===")
    
    opportunity_detector = ValuationOpportunityDetector()
    
    try:
        # 构建测试数据
        valuation_data = {
            'symbol': 'TEST001',
            'dcf_analysis': {'upside_potential': 0.45},
            'peg_analysis': {'peg_ratio': 0.8, 'growth_rate': 20, 'upside_potential': 0.25},
            'pb_roe_analysis': {'upside_potential': 0.30},
            'relative_analysis': {'overall_score': 0.75}
        }
        
        fundamental_data = {
            'fundamental_score': 0.6,
            'growth_indicators': {
                'profit_acceleration': 25,
                'roe_improvement': 4
            }
        }
        
        market_data = {}
        
        opportunities = opportunity_detector.detect_opportunities(
            valuation_data, fundamental_data, market_data
        )
        
        if opportunities:
            print(f"✓ 检测到 {len(opportunities)} 个估值机会")
            for i, opp in enumerate(opportunities, 1):
                print(f"✓ 机会{i}: {opp.opportunity_type}")
                print(f"   - 上涨空间: {opp.upside_potential:.1%}")
                print(f"   - 置信度: {opp.confidence_level:.1%}")
                print(f"   - 催化剂: {opp.catalyst}")
                print(f"   - 风险因素: {', '.join(opp.risk_factors)}")
            
            return opportunities
        else:
            print("✓ 未检测到明显估值机会")
            return []
            
    except Exception as e:
        print(f"✗ 估值机会检测异常: {e}")
        return None


def test_alert_system():
    """测试估值预警系统"""
    print("\n=== 测试估值预警系统 ===")
    
    alert_system = ValuationAlertSystem()
    
    try:
        # 构建测试数据
        valuation_data = {
            'pe_ratio': 3.5,  # 极低PE
            'pb_ratio': 0.4,  # 极低PB
            'peg_analysis': {'peg_ratio': 0.3},  # 有吸引力的PEG
            'dcf_analysis': {'upside_potential': 0.6}  # 高DCF上涨空间
        }
        
        alerts = alert_system.monitor_valuation_alerts(valuation_data, 'TEST001')
        
        if alerts:
            print(f"✓ 触发 {len(alerts)} 个估值预警")
            for alert in alerts:
                print(f"✓ {alert['type']}: {alert['message']} (严重程度: {alert['severity']})")
            
            # 测试预警报告
            report = alert_system.generate_alert_report(alerts)
            print(f"✓ 预警报告生成成功")
            print(f"   - 总预警数: {report['total_alerts']}")
            print(f"   - 高严重程度: {report['high_severity']}")
            print(f"   - 中等严重程度: {report['medium_severity']}")
            print(f"   - 低严重程度: {report['low_severity']}")
            
            return alerts
        else:
            print("✓ 无预警触发")
            return []
            
    except Exception as e:
        print(f"✗ 估值预警系统异常: {e}")
        return None


def test_valuation_analyzer():
    """测试估值分析器主类"""
    print("\n=== 测试估值分析器主类 ===")
    
    analyzer = ValuationAnalyzer()
    financial_data, market_data, growth_forecast = create_sample_data()
    
    try:
        # 综合估值分析
        analysis = analyzer.calculate_comprehensive_valuation(
            financial_data, market_data, growth_forecast, 'TEST001'
        )
        
        if 'error' not in analysis:
            print(f"✓ 综合估值分析成功")
            print(f"✓ 股票代码: {analysis['symbol']}")
            print(f"✓ 估值综合评分: {analysis['valuation_score']:.3f}")
            
            basic_metrics = analysis['basic_metrics']
            print(f"✓ 基础指标: PE={basic_metrics['pe_ratio']:.1f}, "
                  f"PB={basic_metrics['pb_ratio']:.2f}, "
                  f"PS={basic_metrics['ps_ratio']:.2f}")
            
            # 检测估值机会
            opportunities = analyzer.detect_valuation_opportunities(
                analysis, {'fundamental_score': 0.6}, market_data
            )
            print(f"✓ 检测到 {len(opportunities)} 个估值机会")
            
            # 监控预警
            alerts = analyzer.monitor_valuation_alerts(analysis, 'TEST001')
            print(f"✓ 触发 {len(alerts)} 个预警")
            
            # 批量分析测试
            batch_financial = {'TEST001': financial_data, 'TEST002': financial_data.copy()}
            batch_market = {'TEST001': market_data, 'TEST002': market_data.copy()}
            batch_growth = {'TEST001': growth_forecast, 'TEST002': growth_forecast.copy()}
            
            batch_results = analyzer.batch_valuation_analysis(
                batch_financial, batch_market, batch_growth
            )
            print(f"✓ 批量分析完成，分析了 {len(batch_results)} 只股票")
            
            # 获取顶级价值股
            top_stocks = analyzer.get_top_value_stocks(batch_results, top_n=5)
            print("✓ 估值最具吸引力股票:")
            for i, (symbol, score) in enumerate(top_stocks, 1):
                print(f"   {i}. {symbol}: {score:.3f}")
            
            return analysis
        else:
            print(f"✗ 综合估值分析失败: {analysis['error']}")
            return None
            
    except Exception as e:
        print(f"✗ 估值分析器测试异常: {e}")
        return None


def main():
    """主测试函数"""
    print("开始测试估值分析特征计算模块...")
    print("=" * 50)
    
    # 运行所有测试
    dcf_result = test_dcf_model()
    peg_result = test_peg_model()
    pb_roe_result = test_pb_roe_model()
    relative_result = test_relative_analyzer()
    opportunities = test_opportunity_detector()
    alerts = test_alert_system()
    analysis = test_valuation_analyzer()
    
    print("\n" + "=" * 50)
    if all([dcf_result, peg_result, pb_roe_result, relative_result, 
            opportunities is not None, alerts is not None, analysis]):
        print("✓ 所有测试通过！估值分析特征计算模块工作正常")
    else:
        print("✗ 部分测试失败，请检查相关模块")


if __name__ == "__main__":
    main()