"""
风险评估特征模块

实现流动性风险评估算法
构建基本面风险预警系统
编写技术面风险度量工具
实现系统性风险监控机制
专注于识别和量化各类投资风险
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union
from datetime import datetime, timedelta
import logging
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class RiskLevel(Enum):
    """风险等级枚举"""
    VERY_LOW = 1
    LOW = 2
    MEDIUM = 3
    HIGH = 4
    VERY_HIGH = 5


@dataclass
class RiskMetrics:
    """风险指标数据结构"""
    symbol: str
    date: datetime
    liquidity_risk: float
    fundamental_risk: float
    technical_risk: float
    systematic_risk: float
    overall_risk: float
    risk_level: RiskLevel
    risk_factors: List[str]


@dataclass
class RiskAlert:
    """风险预警数据结构"""
    symbol: str
    risk_type: str
    severity: str
    message: str
    threshold: float
    current_value: float
    alert_time: datetime


class LiquidityRiskAnalyzer:
    """流动性风险评估分析器"""
    
    def __init__(self):
        self.risk_thresholds = {
            'daily_turnover_min': 50000000,    # 日成交额最低5000万
            'avg_spread_max': 0.005,           # 平均买卖价差最高0.5%
            'price_impact_max': 0.02,          # 价格冲击成本最高2%
            'volume_volatility_max': 2.0,      # 成交量波动率最高200%
            'market_depth_min': 1000000        # 市场深度最低100万
        }
    
    def assess_liquidity_risk(self, price_data: pd.DataFrame, 
                            order_book_data: Optional[pd.DataFrame] = None) -> Dict:
        """评估流动性风险"""
        try:
            if price_data.empty:
                return {'liquidity_risk_score': 1.0, 'risk_factors': ['数据不足']}
            
            risk_factors = []
            risk_scores = []
            
            # 成交量风险评估
            volume_risk = self._assess_volume_risk(price_data)
            risk_scores.append(volume_risk['score'])
            risk_factors.extend(volume_risk['factors'])
            
            # 买卖价差风险评估
            spread_risk = self._assess_spread_risk(price_data, order_book_data)
            risk_scores.append(spread_risk['score'])
            risk_factors.extend(spread_risk['factors'])
            
            # 综合流动性风险评分
            overall_score = np.mean(risk_scores) if risk_scores else 0.5
            
            return {
                'liquidity_risk_score': overall_score,
                'volume_risk': volume_risk['score'],
                'spread_risk': spread_risk['score'],
                'risk_factors': list(set(risk_factors))
            }
            
        except Exception as e:
            logger.error(f"流动性风险评估失败: {e}")
            return {'liquidity_risk_score': 0.5, 'risk_factors': ['评估失败']}
    
    def _assess_volume_risk(self, data: pd.DataFrame) -> Dict:
        """评估成交量风险"""
        factors = []
        
        # 计算日均成交额
        if 'volume' in data.columns and 'close' in data.columns:
            daily_turnover = (data['volume'] * data['close']).mean()
        else:
            daily_turnover = 0
        
        if daily_turnover < self.risk_thresholds['daily_turnover_min']:
            factors.append('日成交额过低')
            score = 0.8
        else:
            score = 0.2
        
        return {'score': score, 'factors': factors}
    
    def _assess_spread_risk(self, data: pd.DataFrame, 
                          order_book_data: Optional[pd.DataFrame] = None) -> Dict:
        """评估买卖价差风险"""
        factors = []
        
        # 使用价格波动率估算价差
        if 'close' in data.columns and len(data) > 1:
            returns = data['close'].pct_change().dropna()
            avg_spread = returns.std() * 2 if len(returns) > 0 else 0.01
        else:
            avg_spread = 0.01
        
        if avg_spread > self.risk_thresholds['avg_spread_max']:
            factors.append('买卖价差过大')
            score = 0.8
        else:
            score = 0.2
        
        return {'score': score, 'factors': factors}


class FundamentalRiskAnalyzer:
    """基本面风险预警分析器"""
    
    def __init__(self):
        self.risk_indicators = {
            'debt_ratio_max': 0.8,           # 资产负债率上限80%
            'current_ratio_min': 1.0,        # 流动比率下限1.0
            'roe_decline_threshold': -0.05,   # ROE下降阈值-5%
            'revenue_decline_threshold': -0.1, # 营收下降阈值-10%
            'profit_margin_min': 0.05,       # 净利率下限5%
            'cash_flow_negative_periods': 2   # 现金流连续负值期数
        }
    
    def assess_fundamental_risk(self, financial_data: pd.DataFrame) -> Dict:
        """评估基本面风险"""
        try:
            if financial_data.empty:
                return {'fundamental_risk_score': 0.5, 'risk_factors': ['财务数据不足']}
            
            risk_factors = []
            risk_scores = []
            
            # 财务健康度风险
            health_risk = self._assess_financial_health_risk(financial_data)
            risk_scores.append(health_risk['score'])
            risk_factors.extend(health_risk['factors'])
            
            # 盈利能力风险
            profitability_risk = self._assess_profitability_risk(financial_data)
            risk_scores.append(profitability_risk['score'])
            risk_factors.extend(profitability_risk['factors'])
            
            # 成长性风险
            growth_risk = self._assess_growth_risk(financial_data)
            risk_scores.append(growth_risk['score'])
            risk_factors.extend(growth_risk['factors'])
            
            # 综合基本面风险评分
            overall_score = np.mean(risk_scores) if risk_scores else 0.5
            
            return {
                'fundamental_risk_score': overall_score,
                'financial_health_risk': health_risk['score'],
                'profitability_risk': profitability_risk['score'],
                'growth_risk': growth_risk['score'],
                'risk_factors': list(set(risk_factors))
            }
            
        except Exception as e:
            logger.error(f"基本面风险评估失败: {e}")
            return {'fundamental_risk_score': 0.5, 'risk_factors': ['评估失败']}
    
    def _assess_financial_health_risk(self, data: pd.DataFrame) -> Dict:
        """评估财务健康度风险"""
        factors = []
        risk_score = 0.2  # 基础风险分数
        
        if data.empty:
            return {'score': 0.5, 'factors': ['数据不足']}
        
        latest = data.iloc[-1]
        
        # 资产负债率风险
        debt_ratio = latest.get('debt_ratio', 0) / 100  # 转换为小数
        if debt_ratio > self.risk_indicators['debt_ratio_max']:
            factors.append('资产负债率过高')
            risk_score += 0.3
        
        # 流动比率风险
        current_ratio = latest.get('current_ratio', 1.0)
        if current_ratio < self.risk_indicators['current_ratio_min']:
            factors.append('流动比率过低')
            risk_score += 0.2
        
        # 现金流风险
        if len(data) >= 2:
            recent_cash_flows = data['net_profit'].tail(2).tolist()  # 简化使用净利润代替现金流
            if all(cf < 0 for cf in recent_cash_flows):
                factors.append('现金流持续为负')
                risk_score += 0.3
        
        return {'score': min(risk_score, 1.0), 'factors': factors}
    
    def _assess_profitability_risk(self, data: pd.DataFrame) -> Dict:
        """评估盈利能力风险"""
        factors = []
        risk_score = 0.2
        
        if data.empty:
            return {'score': 0.5, 'factors': ['数据不足']}
        
        latest = data.iloc[-1]
        
        # 净利率风险
        net_margin = latest.get('net_margin', 0) / 100
        if net_margin < self.risk_indicators['profit_margin_min']:
            factors.append('净利率过低')
            risk_score += 0.3
        
        # ROE下降风险
        if len(data) >= 2:
            current_roe = latest.get('roe', 0) / 100
            previous_roe = data.iloc[-2].get('roe', 0) / 100
            roe_change = current_roe - previous_roe
            
            if roe_change < self.risk_indicators['roe_decline_threshold']:
                factors.append('ROE大幅下降')
                risk_score += 0.3
        
        return {'score': min(risk_score, 1.0), 'factors': factors}
    
    def _assess_growth_risk(self, data: pd.DataFrame) -> Dict:
        """评估成长性风险"""
        factors = []
        risk_score = 0.2
        
        if len(data) < 2:
            return {'score': 0.5, 'factors': ['历史数据不足']}
        
        # 营收增长风险
        latest_revenue = data.iloc[-1].get('total_revenue', 0)
        previous_revenue = data.iloc[-2].get('total_revenue', 1)
        revenue_growth = (latest_revenue - previous_revenue) / previous_revenue
        
        if revenue_growth < self.risk_indicators['revenue_decline_threshold']:
            factors.append('营收大幅下降')
            risk_score += 0.4
        
        # 利润增长风险
        latest_profit = data.iloc[-1].get('net_profit', 0)
        previous_profit = data.iloc[-2].get('net_profit', 1)
        
        if previous_profit > 0:
            profit_growth = (latest_profit - previous_profit) / previous_profit
            if profit_growth < -0.2:  # 利润下降超过20%
                factors.append('利润大幅下降')
                risk_score += 0.3
        elif latest_profit < 0:
            factors.append('利润转为亏损')
            risk_score += 0.4
        
        return {'score': min(risk_score, 1.0), 'factors': factors}


class TechnicalRiskAnalyzer:
    """技术面风险度量分析器"""
    
    def __init__(self):
        self.risk_thresholds = {
            'volatility_high': 0.05,         # 高波动率阈值5%
            'drawdown_max': 0.2,             # 最大回撤阈值20%
            'rsi_overbought': 80,             # RSI超买阈值
            'rsi_oversold': 20,               # RSI超卖阈值
            'volume_surge_threshold': 3.0,    # 成交量异常放大阈值
            'price_gap_threshold': 0.05       # 价格跳空阈值5%
        }
    
    def assess_technical_risk(self, price_data: pd.DataFrame, 
                            technical_indicators: Optional[Dict] = None) -> Dict:
        """评估技术面风险"""
        try:
            if price_data.empty:
                return {'technical_risk_score': 0.5, 'risk_factors': ['价格数据不足']}
            
            risk_factors = []
            risk_scores = []
            
            # 波动率风险
            volatility_risk = self._assess_volatility_risk(price_data)
            risk_scores.append(volatility_risk['score'])
            risk_factors.extend(volatility_risk['factors'])
            
            # 趋势风险
            trend_risk = self._assess_trend_risk(price_data)
            risk_scores.append(trend_risk['score'])
            risk_factors.extend(trend_risk['factors'])
            
            # 技术指标风险
            if technical_indicators:
                indicator_risk = self._assess_indicator_risk(technical_indicators)
                risk_scores.append(indicator_risk['score'])
                risk_factors.extend(indicator_risk['factors'])
            
            # 综合技术面风险评分
            overall_score = np.mean(risk_scores) if risk_scores else 0.5
            
            return {
                'technical_risk_score': overall_score,
                'volatility_risk': volatility_risk['score'],
                'trend_risk': trend_risk['score'],
                'indicator_risk': indicator_risk['score'] if technical_indicators else 0.5,
                'risk_factors': list(set(risk_factors))
            }
            
        except Exception as e:
            logger.error(f"技术面风险评估失败: {e}")
            return {'technical_risk_score': 0.5, 'risk_factors': ['评估失败']}
    
    def _assess_volatility_risk(self, data: pd.DataFrame) -> Dict:
        """评估波动率风险"""
        factors = []
        
        if 'close' in data.columns and len(data) > 1:
            returns = data['close'].pct_change().dropna()
            volatility = returns.std() if len(returns) > 0 else 0
        else:
            volatility = 0
        
        if volatility > self.risk_thresholds['volatility_high']:
            factors.append('价格波动率过高')
            score = 0.8
        elif volatility > self.risk_thresholds['volatility_high'] * 0.7:
            factors.append('价格波动率偏高')
            score = 0.6
        else:
            score = 0.2
        
        return {'score': score, 'factors': factors}
    
    def _assess_trend_risk(self, data: pd.DataFrame) -> Dict:
        """评估趋势风险"""
        factors = []
        
        if 'close' in data.columns and len(data) > 20:
            # 计算最大回撤
            prices = data['close']
            peak = prices.expanding().max()
            drawdown = (prices - peak) / peak
            max_drawdown = abs(drawdown.min())
        else:
            max_drawdown = 0
        
        if max_drawdown > self.risk_thresholds['drawdown_max']:
            factors.append('最大回撤过大')
            score = 0.8
        elif max_drawdown > self.risk_thresholds['drawdown_max'] * 0.7:
            factors.append('回撤偏大')
            score = 0.6
        else:
            score = 0.2
        
        return {'score': score, 'factors': factors}
    
    def _assess_indicator_risk(self, indicators: Dict) -> Dict:
        """评估技术指标风险"""
        factors = []
        risk_score = 0.2
        
        # RSI风险
        rsi = indicators.get('rsi_14', 50)
        if rsi > self.risk_thresholds['rsi_overbought']:
            factors.append('RSI显示超买')
            risk_score += 0.3
        elif rsi < self.risk_thresholds['rsi_oversold']:
            factors.append('RSI显示超卖')
            risk_score += 0.2
        
        # 成交量异常风险
        volume_surge = indicators.get('volume_surge', 1.0)
        if volume_surge > self.risk_thresholds['volume_surge_threshold']:
            factors.append('成交量异常放大')
            risk_score += 0.2
        
        return {'score': min(risk_score, 1.0), 'factors': factors}


class SystematicRiskAnalyzer:
    """系统性风险监控分析器"""
    
    def __init__(self):
        self.risk_factors = {
            'market_correlation_high': 0.8,   # 高市场相关性阈值
            'sector_concentration_max': 0.6,  # 行业集中度上限
            'beta_high': 1.5,                # 高Beta阈值
            'vix_high': 30,                   # VIX恐慌指数高位
            'policy_risk_score_max': 0.7      # 政策风险评分上限
        }
    
    def assess_systematic_risk(self, market_data: Dict, 
                             portfolio_data: Optional[Dict] = None) -> Dict:
        """评估系统性风险"""
        try:
            risk_factors = []
            risk_scores = []
            
            # 市场风险
            market_risk = self._assess_market_risk(market_data)
            risk_scores.append(market_risk['score'])
            risk_factors.extend(market_risk['factors'])
            
            # 政策风险
            policy_risk = self._assess_policy_risk(market_data)
            risk_scores.append(policy_risk['score'])
            risk_factors.extend(policy_risk['factors'])
            
            # 行业风险
            if portfolio_data:
                sector_risk = self._assess_sector_risk(portfolio_data)
                risk_scores.append(sector_risk['score'])
                risk_factors.extend(sector_risk['factors'])
            
            # 综合系统性风险评分
            overall_score = np.mean(risk_scores) if risk_scores else 0.5
            
            return {
                'systematic_risk_score': overall_score,
                'market_risk': market_risk['score'],
                'policy_risk': policy_risk['score'],
                'sector_risk': sector_risk['score'] if portfolio_data else 0.5,
                'risk_factors': list(set(risk_factors))
            }
            
        except Exception as e:
            logger.error(f"系统性风险评估失败: {e}")
            return {'systematic_risk_score': 0.5, 'risk_factors': ['评估失败']}
    
    def _assess_market_risk(self, market_data: Dict) -> Dict:
        """评估市场风险"""
        factors = []
        risk_score = 0.3  # 基础风险分数
        
        # VIX恐慌指数风险
        vix = market_data.get('vix', 20)
        if vix > self.risk_factors['vix_high']:
            factors.append('市场恐慌情绪高涨')
            risk_score += 0.3
        
        # 市场波动率风险
        market_volatility = market_data.get('market_volatility', 0.02)
        if market_volatility > 0.04:
            factors.append('市场波动率过高')
            risk_score += 0.2
        
        # 流动性风险
        liquidity_index = market_data.get('liquidity_index', 0.5)
        if liquidity_index < 0.3:
            factors.append('市场流动性不足')
            risk_score += 0.2
        
        return {'score': min(risk_score, 1.0), 'factors': factors}
    
    def _assess_policy_risk(self, market_data: Dict) -> Dict:
        """评估政策风险"""
        factors = []
        risk_score = 0.2
        
        # 货币政策风险
        interest_rate_trend = market_data.get('interest_rate_trend', 0)
        if interest_rate_trend > 0.02:  # 利率快速上升
            factors.append('货币政策收紧风险')
            risk_score += 0.3
        
        # 监管政策风险
        regulatory_intensity = market_data.get('regulatory_intensity', 0.3)
        if regulatory_intensity > 0.7:
            factors.append('监管政策趋严')
            risk_score += 0.3
        
        return {'score': min(risk_score, 1.0), 'factors': factors}
    
    def _assess_sector_risk(self, portfolio_data: Dict) -> Dict:
        """评估行业风险"""
        factors = []
        risk_score = 0.2
        
        # 行业集中度风险
        sector_weights = portfolio_data.get('sector_weights', {})
        if sector_weights:
            max_sector_weight = max(sector_weights.values())
            if max_sector_weight > self.risk_factors['sector_concentration_max']:
                factors.append('行业集中度过高')
                risk_score += 0.4
        
        return {'score': min(risk_score, 1.0), 'factors': factors}


class RiskAnalyzer:
    """风险评估特征模块主类"""
    
    def __init__(self):
        self.liquidity_analyzer = LiquidityRiskAnalyzer()
        self.fundamental_analyzer = FundamentalRiskAnalyzer()
        self.technical_analyzer = TechnicalRiskAnalyzer()
        self.systematic_analyzer = SystematicRiskAnalyzer()
        
        logger.info("风险评估系统初始化完成")
    
    def extract_risk_features(self, price_data: pd.DataFrame,
                            financial_data: pd.DataFrame,
                            market_data: Dict,
                            technical_indicators: Optional[Dict] = None,
                            symbol: str = '') -> Dict:
        """提取风险评估特征"""
        try:
            # 流动性风险评估
            liquidity_risk = self.liquidity_analyzer.assess_liquidity_risk(price_data)
            
            # 基本面风险评估
            fundamental_risk = self.fundamental_analyzer.assess_fundamental_risk(financial_data)
            
            # 技术面风险评估
            technical_risk = self.technical_analyzer.assess_technical_risk(
                price_data, technical_indicators
            )
            
            # 系统性风险评估
            systematic_risk = self.systematic_analyzer.assess_systematic_risk(market_data)
            
            # 计算综合风险评分
            overall_risk_score = self._calculate_overall_risk_score(
                liquidity_risk, fundamental_risk, technical_risk, systematic_risk
            )
            
            # 确定风险等级
            risk_level = self._determine_risk_level(overall_risk_score)
            
            # 生成风险预警
            risk_alerts = self._generate_risk_alerts(
                symbol, liquidity_risk, fundamental_risk, technical_risk, systematic_risk
            )
            
            result = {
                'symbol': symbol,
                'liquidity_risk': liquidity_risk,
                'fundamental_risk': fundamental_risk,
                'technical_risk': technical_risk,
                'systematic_risk': systematic_risk,
                'overall_risk_score': overall_risk_score,
                'risk_level': risk_level.name,
                'risk_alerts': risk_alerts,
                'risk_summary': self._generate_risk_summary(
                    liquidity_risk, fundamental_risk, technical_risk, systematic_risk
                ),
                'assessment_date': datetime.now()
            }
            
            logger.info(f"成功提取风险特征 {symbol}, 综合风险评分: {overall_risk_score:.3f}")
            return result
            
        except Exception as e:
            logger.error(f"风险特征提取失败 {symbol}: {e}")
            return {'error': str(e)}
    
    def _calculate_overall_risk_score(self, liquidity_risk: Dict, fundamental_risk: Dict,
                                    technical_risk: Dict, systematic_risk: Dict) -> float:
        """计算综合风险评分"""
        weights = {
            'liquidity': 0.25,
            'fundamental': 0.30,
            'technical': 0.25,
            'systematic': 0.20
        }
        
        liquidity_score = liquidity_risk.get('liquidity_risk_score', 0.5)
        fundamental_score = fundamental_risk.get('fundamental_risk_score', 0.5)
        technical_score = technical_risk.get('technical_risk_score', 0.5)
        systematic_score = systematic_risk.get('systematic_risk_score', 0.5)
        
        overall_score = (
            liquidity_score * weights['liquidity'] +
            fundamental_score * weights['fundamental'] +
            technical_score * weights['technical'] +
            systematic_score * weights['systematic']
        )
        
        return max(min(overall_score, 1.0), 0.0)
    
    def _determine_risk_level(self, risk_score: float) -> RiskLevel:
        """确定风险等级"""
        if risk_score < 0.2:
            return RiskLevel.VERY_LOW
        elif risk_score < 0.4:
            return RiskLevel.LOW
        elif risk_score < 0.6:
            return RiskLevel.MEDIUM
        elif risk_score < 0.8:
            return RiskLevel.HIGH
        else:
            return RiskLevel.VERY_HIGH
    
    def _generate_risk_alerts(self, symbol: str, liquidity_risk: Dict,
                            fundamental_risk: Dict, technical_risk: Dict,
                            systematic_risk: Dict) -> List[RiskAlert]:
        """生成风险预警"""
        alerts = []
        current_time = datetime.now()
        
        # 流动性风险预警
        if liquidity_risk.get('liquidity_risk_score', 0) > 0.7:
            alerts.append(RiskAlert(
                symbol=symbol,
                risk_type='liquidity',
                severity='high',
                message='流动性风险较高',
                threshold=0.7,
                current_value=liquidity_risk.get('liquidity_risk_score', 0),
                alert_time=current_time
            ))
        
        # 基本面风险预警
        if fundamental_risk.get('fundamental_risk_score', 0) > 0.7:
            alerts.append(RiskAlert(
                symbol=symbol,
                risk_type='fundamental',
                severity='high',
                message='基本面风险较高',
                threshold=0.7,
                current_value=fundamental_risk.get('fundamental_risk_score', 0),
                alert_time=current_time
            ))
        
        return alerts
    
    def _generate_risk_summary(self, liquidity_risk: Dict, fundamental_risk: Dict,
                             technical_risk: Dict, systematic_risk: Dict) -> Dict:
        """生成风险汇总"""
        all_factors = []
        all_factors.extend(liquidity_risk.get('risk_factors', []))
        all_factors.extend(fundamental_risk.get('risk_factors', []))
        all_factors.extend(technical_risk.get('risk_factors', []))
        all_factors.extend(systematic_risk.get('risk_factors', []))
        
        # 统计风险因素
        unique_factors = list(set(all_factors))
        
        # 识别主要风险类型
        risk_scores = {
            'liquidity': liquidity_risk.get('liquidity_risk_score', 0.5),
            'fundamental': fundamental_risk.get('fundamental_risk_score', 0.5),
            'technical': technical_risk.get('technical_risk_score', 0.5),
            'systematic': systematic_risk.get('systematic_risk_score', 0.5)
        }
        
        primary_risk = max(risk_scores.items(), key=lambda x: x[1])
        
        return {
            'total_risk_factors': len(unique_factors),
            'risk_factors': unique_factors,
            'primary_risk_type': primary_risk[0],
            'primary_risk_score': primary_risk[1],
            'risk_distribution': risk_scores
        }
    
    def batch_risk_analysis(self, data_dict: Dict[str, Dict]) -> Dict[str, Dict]:
        """批量风险分析"""
        results = {}
        
        for symbol, data in data_dict.items():
            try:
                price_data = data.get('price_data', pd.DataFrame())
                financial_data = data.get('financial_data', pd.DataFrame())
                market_data = data.get('market_data', {})
                technical_indicators = data.get('technical_indicators')
                
                analysis = self.extract_risk_features(
                    price_data, financial_data, market_data, technical_indicators, symbol
                )
                
                if 'error' not in analysis:
                    results[symbol] = analysis
                    
            except Exception as e:
                logger.error(f"批量风险分析失败 {symbol}: {e}")
                continue
        
        logger.info(f"批量风险分析完成，成功分析 {len(results)} 只股票")
        return results
    
    def get_high_risk_stocks(self, analysis_results: Dict[str, Dict],
                           risk_threshold: float = 0.7) -> List[Tuple[str, float]]:
        """获取高风险股票"""
        high_risk_stocks = []
        
        for symbol, data in analysis_results.items():
            if 'overall_risk_score' in data:
                risk_score = data['overall_risk_score']
                if risk_score >= risk_threshold:
                    high_risk_stocks.append((symbol, risk_score))
        
        # 按风险评分降序排列
        high_risk_stocks.sort(key=lambda x: x[1], reverse=True)
        return high_risk_stocks