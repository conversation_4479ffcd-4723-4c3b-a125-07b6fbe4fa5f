"""
基本面分析特征提取模块

实现财务数据解析、业绩预测、成长性分析等功能
专注于识别爆发股的基本面特征
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
import logging
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class FundamentalMetrics:
    """基本面指标数据结构"""
    symbol: str
    report_date: datetime
    revenue_growth: float
    profit_growth: float
    roe: float
    roa: float
    debt_ratio: float
    current_ratio: float
    gross_margin: float
    net_margin: float
    eps_growth: float
    bvps_growth: float


class FinancialDataParser:
    """财务数据解析器"""
    
    def __init__(self):
        self.required_fields = [
            'total_revenue', 'net_profit', 'total_assets', 'total_equity',
            'total_liabilities', 'current_assets', 'current_liabilities',
            'gross_profit', 'eps', 'bvps'
        ]
    
    def parse_financial_data(self, raw_data: pd.DataFrame) -> pd.DataFrame:
        """解析原始财务数据"""
        try:
            # 数据清洗和标准化
            cleaned_data = self._clean_financial_data(raw_data)
            
            # 计算基础财务比率
            financial_ratios = self._calculate_financial_ratios(cleaned_data)
            
            # 计算增长率指标
            growth_metrics = self._calculate_growth_metrics(cleaned_data)
            
            # 合并所有指标
            result = pd.concat([cleaned_data, financial_ratios, growth_metrics], axis=1)
            
            logger.info(f"成功解析财务数据，包含 {len(result)} 条记录")
            return result
            
        except Exception as e:
            logger.error(f"财务数据解析失败: {e}")
            raise
    
    def _clean_financial_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """清洗财务数据"""
        # 处理缺失值
        data = data.fillna(0)
        
        # 处理异常值（如负的总资产等）
        for col in ['total_assets', 'total_equity']:
            if col in data.columns:
                data[col] = data[col].clip(lower=0)
        
        # 确保必要字段存在
        for field in self.required_fields:
            if field not in data.columns:
                data[field] = 0
                logger.warning(f"缺失字段 {field}，已填充为0")
        
        return data
    
    def _calculate_financial_ratios(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算财务比率"""
        ratios = pd.DataFrame(index=data.index)
        
        # ROE (净资产收益率)
        ratios['roe'] = np.where(
            data['total_equity'] > 0,
            data['net_profit'] / data['total_equity'] * 100,
            0
        )
        
        # ROA (总资产收益率)
        ratios['roa'] = np.where(
            data['total_assets'] > 0,
            data['net_profit'] / data['total_assets'] * 100,
            0
        )
        
        # 资产负债率
        ratios['debt_ratio'] = np.where(
            data['total_assets'] > 0,
            data['total_liabilities'] / data['total_assets'] * 100,
            0
        )
        
        # 流动比率
        ratios['current_ratio'] = np.where(
            data['current_liabilities'] > 0,
            data['current_assets'] / data['current_liabilities'],
            0
        )
        
        # 毛利率
        ratios['gross_margin'] = np.where(
            data['total_revenue'] > 0,
            data['gross_profit'] / data['total_revenue'] * 100,
            0
        )
        
        # 净利率
        ratios['net_margin'] = np.where(
            data['total_revenue'] > 0,
            data['net_profit'] / data['total_revenue'] * 100,
            0
        )
        
        return ratios
    
    def _calculate_growth_metrics(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算增长率指标"""
        growth = pd.DataFrame(index=data.index)
        
        # 营收增长率（同比）
        growth['revenue_growth_yoy'] = data['total_revenue'].pct_change(4) * 100  # 假设季度数据
        
        # 净利润增长率（同比）
        growth['profit_growth_yoy'] = data['net_profit'].pct_change(4) * 100
        
        # EPS增长率
        growth['eps_growth_yoy'] = data['eps'].pct_change(4) * 100
        
        # BVPS增长率
        growth['bvps_growth_yoy'] = data['bvps'].pct_change(4) * 100
        
        # 营收增长率（环比）
        growth['revenue_growth_qoq'] = data['total_revenue'].pct_change(1) * 100
        
        # 净利润增长率（环比）
        growth['profit_growth_qoq'] = data['net_profit'].pct_change(1) * 100
        
        return growth


class PerformanceForecastEngine:
    """业绩预测和成长性分析引擎"""
    
    def __init__(self):
        self.forecast_periods = [1, 2, 4]  # 预测1、2、4个季度
    
    def forecast_performance(self, financial_data: pd.DataFrame, symbol: str) -> Dict:
        """预测业绩表现"""
        try:
            # 获取历史趋势
            trends = self._analyze_historical_trends(financial_data)
            
            # 计算成长性指标
            growth_indicators = self._calculate_growth_indicators(financial_data)
            
            # 业绩预测
            forecasts = self._generate_forecasts(financial_data, trends)
            
            # 成长性评分
            growth_score = self._calculate_growth_score(growth_indicators)
            
            return {
                'symbol': symbol,
                'trends': trends,
                'growth_indicators': growth_indicators,
                'forecasts': forecasts,
                'growth_score': growth_score,
                'forecast_date': datetime.now()
            }
            
        except Exception as e:
            logger.error(f"业绩预测失败 {symbol}: {e}")
            return {}
    
    def _analyze_historical_trends(self, data: pd.DataFrame) -> Dict:
        """分析历史趋势"""
        trends = {}
        
        # 营收趋势
        revenue_trend = self._calculate_trend(data['total_revenue'])
        trends['revenue_trend'] = revenue_trend
        
        # 利润趋势
        profit_trend = self._calculate_trend(data['net_profit'])
        trends['profit_trend'] = profit_trend
        
        # ROE趋势
        if 'roe' in data.columns:
            roe_trend = self._calculate_trend(data['roe'])
            trends['roe_trend'] = roe_trend
        
        # 毛利率趋势
        if 'gross_margin' in data.columns:
            margin_trend = self._calculate_trend(data['gross_margin'])
            trends['margin_trend'] = margin_trend
        
        return trends
    
    def _calculate_trend(self, series: pd.Series) -> Dict:
        """计算单个指标的趋势"""
        if len(series) < 3:
            return {'direction': 'unknown', 'strength': 0, 'consistency': 0}
        
        # 计算趋势方向
        recent_avg = series.tail(2).mean()
        historical_avg = series.head(-2).mean()
        
        if recent_avg > historical_avg * 1.1:
            direction = 'up'
        elif recent_avg < historical_avg * 0.9:
            direction = 'down'
        else:
            direction = 'stable'
        
        # 计算趋势强度
        strength = abs((recent_avg - historical_avg) / historical_avg) if historical_avg != 0 else 0
        
        # 计算趋势一致性
        changes = series.diff().dropna()
        consistency = len(changes[changes > 0]) / len(changes) if len(changes) > 0 else 0
        
        return {
            'direction': direction,
            'strength': min(strength, 1.0),
            'consistency': consistency
        }
    
    def _calculate_growth_indicators(self, data: pd.DataFrame) -> Dict:
        """计算成长性指标"""
        indicators = {}
        
        # 营收增长加速度
        if 'revenue_growth_yoy' in data.columns:
            revenue_acceleration = data['revenue_growth_yoy'].diff().tail(1).iloc[0]
            indicators['revenue_acceleration'] = revenue_acceleration
        
        # 利润增长加速度
        if 'profit_growth_yoy' in data.columns:
            profit_acceleration = data['profit_growth_yoy'].diff().tail(1).iloc[0]
            indicators['profit_acceleration'] = profit_acceleration
        
        # ROE改善程度
        if 'roe' in data.columns:
            roe_improvement = data['roe'].diff().tail(2).mean()
            indicators['roe_improvement'] = roe_improvement
        
        # 毛利率改善
        if 'gross_margin' in data.columns:
            margin_improvement = data['gross_margin'].diff().tail(2).mean()
            indicators['margin_improvement'] = margin_improvement
        
        # 资产负债率改善
        if 'debt_ratio' in data.columns:
            debt_improvement = -data['debt_ratio'].diff().tail(2).mean()  # 负值表示改善
            indicators['debt_improvement'] = debt_improvement
        
        return indicators
    
    def _generate_forecasts(self, data: pd.DataFrame, trends: Dict) -> Dict:
        """生成业绩预测"""
        forecasts = {}
        
        for period in self.forecast_periods:
            period_forecast = {}
            
            # 营收预测
            if 'revenue_trend' in trends:
                revenue_forecast = self._forecast_metric(
                    data['total_revenue'], 
                    trends['revenue_trend'], 
                    period
                )
                period_forecast['revenue'] = revenue_forecast
            
            # 利润预测
            if 'profit_trend' in trends:
                profit_forecast = self._forecast_metric(
                    data['net_profit'], 
                    trends['profit_trend'], 
                    period
                )
                period_forecast['profit'] = profit_forecast
            
            forecasts[f'{period}q'] = period_forecast
        
        return forecasts
    
    def _forecast_metric(self, series: pd.Series, trend: Dict, periods: int) -> float:
        """预测单个指标"""
        if len(series) == 0:
            return 0
        
        current_value = series.iloc[-1]
        
        # 基于趋势方向和强度进行预测
        if trend['direction'] == 'up':
            growth_rate = trend['strength'] * trend['consistency']
        elif trend['direction'] == 'down':
            growth_rate = -trend['strength'] * trend['consistency']
        else:
            growth_rate = 0
        
        # 应用复合增长
        forecast_value = current_value * ((1 + growth_rate) ** periods)
        
        return forecast_value
    
    def _calculate_growth_score(self, indicators: Dict) -> float:
        """计算综合成长性评分"""
        if not indicators:
            return 0
        
        score = 0
        weights = {
            'revenue_acceleration': 0.25,
            'profit_acceleration': 0.30,
            'roe_improvement': 0.20,
            'margin_improvement': 0.15,
            'debt_improvement': 0.10
        }
        
        for indicator, weight in weights.items():
            if indicator in indicators:
                # 标准化指标值到0-1范围
                normalized_value = self._normalize_indicator(indicators[indicator], indicator)
                score += normalized_value * weight
        
        return min(max(score, 0), 1)  # 限制在0-1范围
    
    def _normalize_indicator(self, value: float, indicator_type: str) -> float:
        """标准化指标值"""
        if pd.isna(value):
            return 0
        
        # 根据指标类型设置不同的标准化方法
        if 'acceleration' in indicator_type:
            # 增长加速度：正值好，负值差
            return max(min(value / 50, 1), 0)  # 假设50%为满分
        elif 'improvement' in indicator_type:
            # 改善指标：正值好，负值差
            return max(min(value / 10, 1), 0)  # 假设10个百分点为满分
        else:
            return max(min(abs(value) / 100, 1), 0)


class FundamentalScoringSystem:
    """基本面评分和排序系统"""
    
    def __init__(self):
        self.scoring_weights = {
            'profitability': 0.30,  # 盈利能力
            'growth': 0.35,         # 成长性
            'financial_health': 0.20,  # 财务健康度
            'efficiency': 0.15      # 运营效率
        }
    
    def calculate_fundamental_score(self, financial_data: pd.DataFrame, 
                                  forecast_data: Dict) -> float:
        """计算基本面综合评分"""
        try:
            # 盈利能力评分
            profitability_score = self._score_profitability(financial_data)
            
            # 成长性评分
            growth_score = forecast_data.get('growth_score', 0)
            
            # 财务健康度评分
            health_score = self._score_financial_health(financial_data)
            
            # 运营效率评分
            efficiency_score = self._score_efficiency(financial_data)
            
            # 加权综合评分
            total_score = (
                profitability_score * self.scoring_weights['profitability'] +
                growth_score * self.scoring_weights['growth'] +
                health_score * self.scoring_weights['financial_health'] +
                efficiency_score * self.scoring_weights['efficiency']
            )
            
            return min(max(total_score, 0), 1)
            
        except Exception as e:
            logger.error(f"基本面评分计算失败: {e}")
            return 0
    
    def _score_profitability(self, data: pd.DataFrame) -> float:
        """评分盈利能力"""
        if data.empty:
            return 0
        
        latest_data = data.iloc[-1]
        score = 0
        
        # ROE评分
        roe = latest_data.get('roe', 0)
        if roe > 20:
            score += 0.4
        elif roe > 15:
            score += 0.3
        elif roe > 10:
            score += 0.2
        elif roe > 5:
            score += 0.1
        
        # 净利率评分
        net_margin = latest_data.get('net_margin', 0)
        if net_margin > 20:
            score += 0.3
        elif net_margin > 15:
            score += 0.25
        elif net_margin > 10:
            score += 0.2
        elif net_margin > 5:
            score += 0.1
        
        # 毛利率评分
        gross_margin = latest_data.get('gross_margin', 0)
        if gross_margin > 50:
            score += 0.3
        elif gross_margin > 40:
            score += 0.25
        elif gross_margin > 30:
            score += 0.2
        elif gross_margin > 20:
            score += 0.1
        
        return min(score, 1.0)
    
    def _score_financial_health(self, data: pd.DataFrame) -> float:
        """评分财务健康度"""
        if data.empty:
            return 0
        
        latest_data = data.iloc[-1]
        score = 0
        
        # 资产负债率评分（越低越好）
        debt_ratio = latest_data.get('debt_ratio', 100)
        if debt_ratio < 30:
            score += 0.4
        elif debt_ratio < 50:
            score += 0.3
        elif debt_ratio < 70:
            score += 0.2
        elif debt_ratio < 80:
            score += 0.1
        
        # 流动比率评分
        current_ratio = latest_data.get('current_ratio', 0)
        if current_ratio > 2:
            score += 0.3
        elif current_ratio > 1.5:
            score += 0.25
        elif current_ratio > 1.2:
            score += 0.2
        elif current_ratio > 1:
            score += 0.1
        
        # ROA评分
        roa = latest_data.get('roa', 0)
        if roa > 10:
            score += 0.3
        elif roa > 7:
            score += 0.25
        elif roa > 5:
            score += 0.2
        elif roa > 3:
            score += 0.1
        
        return min(score, 1.0)
    
    def _score_efficiency(self, data: pd.DataFrame) -> float:
        """评分运营效率"""
        if data.empty:
            return 0
        
        # 这里可以添加更多运营效率指标
        # 如资产周转率、存货周转率等
        # 目前基于已有数据进行简单评分
        
        latest_data = data.iloc[-1]
        score = 0
        
        # 基于ROA作为效率指标
        roa = latest_data.get('roa', 0)
        if roa > 8:
            score += 0.5
        elif roa > 5:
            score += 0.4
        elif roa > 3:
            score += 0.3
        elif roa > 1:
            score += 0.2
        
        # 基于净利率作为效率指标
        net_margin = latest_data.get('net_margin', 0)
        if net_margin > 15:
            score += 0.5
        elif net_margin > 10:
            score += 0.4
        elif net_margin > 5:
            score += 0.3
        elif net_margin > 2:
            score += 0.2
        
        return min(score, 1.0)
    
    def rank_stocks(self, stock_scores: Dict[str, float], top_n: int = 20) -> List[Tuple[str, float]]:
        """对股票进行排序"""
        sorted_stocks = sorted(stock_scores.items(), key=lambda x: x[1], reverse=True)
        return sorted_stocks[:top_n]


class FundamentalChangeMonitor:
    """基本面变化监控机制"""
    
    def __init__(self):
        self.alert_thresholds = {
            'revenue_growth_change': 20,  # 营收增长率变化超过20%
            'profit_growth_change': 30,   # 利润增长率变化超过30%
            'roe_change': 5,              # ROE变化超过5%
            'debt_ratio_change': 10       # 资产负债率变化超过10%
        }
    
    def monitor_changes(self, current_data: pd.DataFrame, 
                       previous_data: pd.DataFrame, symbol: str) -> Dict:
        """监控基本面变化"""
        try:
            changes = {}
            alerts = []
            
            # 检查各项指标变化
            for metric in ['revenue_growth_yoy', 'profit_growth_yoy', 'roe', 'debt_ratio']:
                if metric in current_data.columns and metric in previous_data.columns:
                    current_value = current_data[metric].iloc[-1]
                    previous_value = previous_data[metric].iloc[-1]
                    
                    change = current_value - previous_value
                    change_pct = (change / previous_value * 100) if previous_value != 0 else 0
                    
                    changes[metric] = {
                        'current': current_value,
                        'previous': previous_value,
                        'change': change,
                        'change_pct': change_pct
                    }
                    
                    # 检查是否触发警报
                    threshold_key = f"{metric}_change"
                    if threshold_key in self.alert_thresholds:
                        if abs(change_pct) > self.alert_thresholds[threshold_key]:
                            alerts.append({
                                'metric': metric,
                                'change_pct': change_pct,
                                'threshold': self.alert_thresholds[threshold_key],
                                'severity': 'high' if abs(change_pct) > self.alert_thresholds[threshold_key] * 2 else 'medium'
                            })
            
            return {
                'symbol': symbol,
                'changes': changes,
                'alerts': alerts,
                'monitor_date': datetime.now()
            }
            
        except Exception as e:
            logger.error(f"基本面变化监控失败 {symbol}: {e}")
            return {}
    
    def generate_alert_summary(self, monitor_results: List[Dict]) -> Dict:
        """生成警报汇总"""
        high_priority_alerts = []
        medium_priority_alerts = []
        
        for result in monitor_results:
            for alert in result.get('alerts', []):
                alert['symbol'] = result['symbol']
                if alert['severity'] == 'high':
                    high_priority_alerts.append(alert)
                else:
                    medium_priority_alerts.append(alert)
        
        return {
            'high_priority': high_priority_alerts,
            'medium_priority': medium_priority_alerts,
            'total_alerts': len(high_priority_alerts) + len(medium_priority_alerts),
            'summary_date': datetime.now()
        }


class FundamentalAnalyzer:
    """基本面分析特征提取主类"""
    
    def __init__(self):
        self.parser = FinancialDataParser()
        self.forecast_engine = PerformanceForecastEngine()
        self.scoring_system = FundamentalScoringSystem()
        self.change_monitor = FundamentalChangeMonitor()
        
        logger.info("基本面分析器初始化完成")
    
    def extract_fundamental_features(self, raw_financial_data: pd.DataFrame, 
                                   symbol: str) -> Dict:
        """提取基本面特征"""
        try:
            # 解析财务数据
            parsed_data = self.parser.parse_financial_data(raw_financial_data)
            
            # 业绩预测和成长性分析
            forecast_data = self.forecast_engine.forecast_performance(parsed_data, symbol)
            
            # 计算基本面评分
            fundamental_score = self.scoring_system.calculate_fundamental_score(
                parsed_data, forecast_data
            )
            
            # 构建特征字典
            features = {
                'symbol': symbol,
                'fundamental_score': fundamental_score,
                'financial_ratios': self._extract_latest_ratios(parsed_data),
                'growth_metrics': forecast_data.get('growth_indicators', {}),
                'forecast_data': forecast_data.get('forecasts', {}),
                'trends': forecast_data.get('trends', {}),
                'extraction_date': datetime.now()
            }
            
            logger.info(f"成功提取基本面特征 {symbol}, 评分: {fundamental_score:.3f}")
            return features
            
        except Exception as e:
            logger.error(f"基本面特征提取失败 {symbol}: {e}")
            return {}
    
    def _extract_latest_ratios(self, data: pd.DataFrame) -> Dict:
        """提取最新的财务比率"""
        if data.empty:
            return {}
        
        latest = data.iloc[-1]
        return {
            'roe': latest.get('roe', 0),
            'roa': latest.get('roa', 0),
            'debt_ratio': latest.get('debt_ratio', 0),
            'current_ratio': latest.get('current_ratio', 0),
            'gross_margin': latest.get('gross_margin', 0),
            'net_margin': latest.get('net_margin', 0)
        }
    
    def batch_analyze(self, financial_data_dict: Dict[str, pd.DataFrame]) -> Dict[str, Dict]:
        """批量分析多只股票的基本面"""
        results = {}
        
        for symbol, data in financial_data_dict.items():
            try:
                features = self.extract_fundamental_features(data, symbol)
                if features:
                    results[symbol] = features
            except Exception as e:
                logger.error(f"批量分析失败 {symbol}: {e}")
                continue
        
        logger.info(f"批量基本面分析完成，成功分析 {len(results)} 只股票")
        return results
    
    def get_top_fundamental_stocks(self, analysis_results: Dict[str, Dict], 
                                 top_n: int = 20) -> List[Tuple[str, float]]:
        """获取基本面评分最高的股票"""
        scores = {symbol: data['fundamental_score'] 
                 for symbol, data in analysis_results.items() 
                 if 'fundamental_score' in data}
        
        return self.scoring_system.rank_stocks(scores, top_n)