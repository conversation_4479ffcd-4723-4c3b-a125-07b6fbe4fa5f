"""
基本面分析特征提取测试
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# 直接导入模块，避免包导入问题
current_dir = os.path.dirname(__file__)
sys.path.insert(0, current_dir)

from fundamental_analyzer import (
    FundamentalAnalyzer, FinancialDataParser, PerformanceForecastEngine,
    FundamentalScoringSystem, FundamentalChangeMonitor
)


def create_sample_financial_data():
    """创建示例财务数据"""
    dates = pd.date_range(start='2020-01-01', end='2023-12-31', freq='Q')
    
    # 模拟一只成长股的财务数据
    np.random.seed(42)
    base_revenue = 1000
    base_profit = 100
    
    data = []
    for i, date in enumerate(dates):
        # 模拟增长趋势
        growth_factor = 1 + (i * 0.05) + np.random.normal(0, 0.02)
        
        revenue = base_revenue * growth_factor
        profit = base_profit * growth_factor * (1 + np.random.normal(0, 0.1))
        assets = revenue * 2
        equity = assets * 0.6
        liabilities = assets - equity
        
        data.append({
            'date': date,
            'symbol': 'TEST001',
            'total_revenue': revenue,
            'net_profit': profit,
            'total_assets': assets,
            'total_equity': equity,
            'total_liabilities': liabilities,
            'current_assets': assets * 0.4,
            'current_liabilities': liabilities * 0.5,
            'gross_profit': revenue * 0.3,
            'eps': profit / 100,  # 假设100万股
            'bvps': equity / 100
        })
    
    return pd.DataFrame(data).set_index('date')


def test_financial_data_parser():
    """测试财务数据解析器"""
    print("=== 测试财务数据解析器 ===")
    
    parser = FinancialDataParser()
    sample_data = create_sample_financial_data()
    
    try:
        parsed_data = parser.parse_financial_data(sample_data)
        print(f"✓ 成功解析财务数据，包含 {len(parsed_data)} 条记录")
        print(f"✓ 包含字段: {list(parsed_data.columns)}")
        
        # 检查关键指标
        latest = parsed_data.iloc[-1]
        print(f"✓ 最新ROE: {latest['roe']:.2f}%")
        print(f"✓ 最新ROA: {latest['roa']:.2f}%")
        print(f"✓ 最新资产负债率: {latest['debt_ratio']:.2f}%")
        print(f"✓ 最新营收增长率: {latest['revenue_growth_yoy']:.2f}%")
        
        return parsed_data
        
    except Exception as e:
        print(f"✗ 财务数据解析失败: {e}")
        return None


def test_performance_forecast():
    """测试业绩预测引擎"""
    print("\n=== 测试业绩预测引擎 ===")
    
    forecast_engine = PerformanceForecastEngine()
    sample_data = create_sample_financial_data()
    parser = FinancialDataParser()
    parsed_data = parser.parse_financial_data(sample_data)
    
    try:
        forecast_result = forecast_engine.forecast_performance(parsed_data, 'TEST001')
        
        if forecast_result:
            print(f"✓ 成功生成业绩预测")
            print(f"✓ 成长性评分: {forecast_result['growth_score']:.3f}")
            
            # 显示趋势分析
            trends = forecast_result.get('trends', {})
            for trend_name, trend_data in trends.items():
                print(f"✓ {trend_name}: {trend_data['direction']} (强度: {trend_data['strength']:.3f})")
            
            # 显示预测结果
            forecasts = forecast_result.get('forecasts', {})
            for period, forecast in forecasts.items():
                print(f"✓ {period} 预测: 营收={forecast.get('revenue', 0):.0f}, 利润={forecast.get('profit', 0):.0f}")
            
            return forecast_result
        else:
            print("✗ 业绩预测失败")
            return None
            
    except Exception as e:
        print(f"✗ 业绩预测异常: {e}")
        return None


def test_scoring_system():
    """测试评分系统"""
    print("\n=== 测试基本面评分系统 ===")
    
    scoring_system = FundamentalScoringSystem()
    parser = FinancialDataParser()
    forecast_engine = PerformanceForecastEngine()
    
    sample_data = create_sample_financial_data()
    parsed_data = parser.parse_financial_data(sample_data)
    forecast_data = forecast_engine.forecast_performance(parsed_data, 'TEST001')
    
    try:
        score = scoring_system.calculate_fundamental_score(parsed_data, forecast_data)
        print(f"✓ 基本面综合评分: {score:.3f}")
        
        # 测试股票排序
        test_scores = {
            'TEST001': score,
            'TEST002': 0.75,
            'TEST003': 0.85,
            'TEST004': 0.65
        }
        
        ranked_stocks = scoring_system.rank_stocks(test_scores, top_n=3)
        print("✓ 股票排序结果:")
        for i, (symbol, score) in enumerate(ranked_stocks, 1):
            print(f"   {i}. {symbol}: {score:.3f}")
        
        return score
        
    except Exception as e:
        print(f"✗ 评分系统测试失败: {e}")
        return None


def test_change_monitor():
    """测试变化监控"""
    print("\n=== 测试基本面变化监控 ===")
    
    monitor = FundamentalChangeMonitor()
    parser = FinancialDataParser()
    
    # 创建两个时期的数据
    sample_data = create_sample_financial_data()
    parsed_data = parser.parse_financial_data(sample_data)
    
    # 分割数据模拟前后两个时期
    mid_point = len(parsed_data) // 2
    previous_data = parsed_data.iloc[:mid_point]
    current_data = parsed_data.iloc[mid_point:]
    
    try:
        monitor_result = monitor.monitor_changes(current_data, previous_data, 'TEST001')
        
        if monitor_result:
            print(f"✓ 成功监控基本面变化")
            
            changes = monitor_result.get('changes', {})
            for metric, change_data in changes.items():
                print(f"✓ {metric}: {change_data['previous']:.2f} -> {change_data['current']:.2f} "
                      f"(变化: {change_data['change_pct']:.1f}%)")
            
            alerts = monitor_result.get('alerts', [])
            if alerts:
                print(f"✓ 触发 {len(alerts)} 个警报:")
                for alert in alerts:
                    print(f"   - {alert['metric']}: 变化{alert['change_pct']:.1f}% "
                          f"(阈值: {alert['threshold']}%, 严重程度: {alert['severity']})")
            else:
                print("✓ 无警报触发")
            
            return monitor_result
        else:
            print("✗ 变化监控失败")
            return None
            
    except Exception as e:
        print(f"✗ 变化监控异常: {e}")
        return None


def test_fundamental_analyzer():
    """测试基本面分析器主类"""
    print("\n=== 测试基本面分析器主类 ===")
    
    analyzer = FundamentalAnalyzer()
    sample_data = create_sample_financial_data()
    
    try:
        # 单只股票分析
        features = analyzer.extract_fundamental_features(sample_data, 'TEST001')
        
        if features:
            print(f"✓ 成功提取基本面特征")
            print(f"✓ 股票代码: {features['symbol']}")
            print(f"✓ 基本面评分: {features['fundamental_score']:.3f}")
            
            ratios = features.get('financial_ratios', {})
            print(f"✓ 财务比率: ROE={ratios.get('roe', 0):.2f}%, "
                  f"ROA={ratios.get('roa', 0):.2f}%, "
                  f"负债率={ratios.get('debt_ratio', 0):.2f}%")
            
            # 批量分析测试
            batch_data = {
                'TEST001': sample_data,
                'TEST002': sample_data.copy(),  # 使用相同数据模拟
            }
            
            batch_results = analyzer.batch_analyze(batch_data)
            print(f"✓ 批量分析完成，分析了 {len(batch_results)} 只股票")
            
            # 获取顶级股票
            top_stocks = analyzer.get_top_fundamental_stocks(batch_results, top_n=5)
            print("✓ 基本面最佳股票:")
            for i, (symbol, score) in enumerate(top_stocks, 1):
                print(f"   {i}. {symbol}: {score:.3f}")
            
            return features
        else:
            print("✗ 基本面特征提取失败")
            return None
            
    except Exception as e:
        print(f"✗ 基本面分析器测试失败: {e}")
        return None


def main():
    """主测试函数"""
    print("开始测试基本面分析特征提取模块...")
    print("=" * 50)
    
    # 运行所有测试
    parsed_data = test_financial_data_parser()
    if parsed_data is not None:
        forecast_result = test_performance_forecast()
        score = test_scoring_system()
        monitor_result = test_change_monitor()
        features = test_fundamental_analyzer()
        
        print("\n" + "=" * 50)
        if all([parsed_data is not None, forecast_result, score, monitor_result, features]):
            print("✓ 所有测试通过！基本面分析特征提取模块工作正常")
        else:
            print("✗ 部分测试失败，请检查相关模块")
    else:
        print("✗ 基础测试失败，无法继续")


if __name__ == "__main__":
    main()