"""
大盘走势预测特征模块测试
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# 直接导入模块，避免包导入问题
current_dir = os.path.dirname(__file__)
sys.path.insert(0, current_dir)

from market_analyzer import (
    MarketAnalyzer, MacroEconomicAnalyzer, MarketTechnicalAnalyzer,
    FundFlowAnalyzer, PolicyImpactAnalyzer, MarketTrend, MarketPhase
)


def create_sample_macro_data():
    """创建示例宏观经济数据"""
    return {
        'gdp_growth': 5.2,          # GDP增长率5.2%
        'inflation_rate': 2.8,      # 通胀率2.8%
        'interest_rate': 3.5,       # 利率3.5%
        'unemployment_rate': 4.2,   # 失业率4.2%
        'pmi': 52.3,               # PMI 52.3
        'consumer_confidence': 105.2, # 消费者信心指数
        'gdp_growth_trend': 0.2,    # GDP增长趋势
        'inflation_trend': -0.1     # 通胀趋势
    }


def create_sample_market_data():
    """创建示例市场数据"""
    return {
        'advance_decline_ratio': 0.62,  # 上涨下跌比例
        'limit_up_count': 35,           # 涨停股票数
        'limit_down_count': 8,          # 跌停股票数
        'total_stocks': 4000,           # 总股票数
        'new_high_count': 120,          # 创新高股票数
        'new_low_count': 45,            # 创新低股票数
        'vix': 22.5,                    # VIX恐慌指数
        'put_call_ratio': 0.85,         # 看跌看涨比率
        'margin_ratio': 0.65            # 融资比例
    }


def create_sample_index_data():
    """创建示例指数数据"""
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
    
    np.random.seed(42)
    
    # 模拟指数走势
    base_price = 3000
    prices = [base_price]
    volumes = []
    
    for i in range(1, len(dates)):
        # 添加趋势和随机波动
        if i < 100:
            trend = 0.0008  # 上升趋势
        elif i < 200:
            trend = -0.0005  # 下降趋势
        else:
            trend = 0.0003   # 温和上升
        
        noise = np.random.normal(0, 0.015)
        new_price = prices[-1] * (1 + trend + noise)
        prices.append(max(new_price, 1000))  # 确保价格合理
        
        # 模拟成交量
        base_volume = 500000000  # 5亿
        volume_noise = np.random.uniform(0.7, 1.5)
        volume = base_volume * volume_noise
        volumes.append(volume)
    
    volumes.insert(0, 500000000)  # 第一天成交量
    
    # 生成OHLC数据
    data = []
    for i, (date, close, volume) in enumerate(zip(dates, prices, volumes)):
        if i == 0:
            open_price = close
        else:
            open_price = prices[i-1] * (1 + np.random.normal(0, 0.005))
        
        high = max(open_price, close) * (1 + abs(np.random.normal(0, 0.008)))
        low = min(open_price, close) * (1 - abs(np.random.normal(0, 0.008)))
        
        data.append({
            'date': date,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
    
    return pd.DataFrame(data).set_index('date')


def create_sample_flow_data():
    """创建示例资金流向数据"""
    return {
        'northbound': {
            'net_inflow': 35.8,        # 北向资金净流入35.8亿
            'daily_average': 8.2,      # 日均流入8.2亿
            'consistency_days': 7      # 连续流入7天
        },
        'institutional': {
            'net_inflow': 125.6,       # 机构净流入125.6亿
            'fund_inflow': 85.3,       # 基金净流入85.3亿
            'insurance_inflow': 40.3   # 保险资金净流入40.3亿
        },
        'retail': {
            'new_accounts': 28000,     # 新开户2.8万
            'trading_activity': 0.65   # 交易活跃度65%
        },
        'hot_money': {
            'net_inflow': 18.5,        # 游资净流入18.5亿
            'activity_level': 0.72     # 活跃程度72%
        },
        'margin': {
            'balance': 1850.6,         # 融资余额1850.6亿
            'buy_amount': 95.3,        # 融资买入95.3亿
            'repay_amount': 78.9       # 融资偿还78.9亿
        }
    }


def create_sample_policy_data():
    """创建示例政策数据"""
    return {
        'monetary': {
            'interest_rate_change': -0.1,    # 降息10个基点
            'money_supply_growth': 10.5,     # 货币供应量增长10.5%
            'reserve_ratio_change': -0.5     # 降准50个基点
        },
        'fiscal': {
            'deficit_ratio': 3.2,            # 财政赤字率3.2%
            'tax_policy_change': -0.05,      # 减税5%
            'infrastructure_spending': 0.15  # 基建投资增长15%
        },
        'regulatory': {
            'intensity': 0.4,                # 监管强度40%
            'new_regulations': 3,            # 新法规3个
            'enforcement_level': 0.5         # 执法力度50%
        },
        'industry': {
            'support_policies': 8,           # 支持政策8个
            'restriction_policies': 2,       # 限制政策2个
            'subsidy_amount': 1200          # 补贴金额1200亿
        },
        'trade': {
            'tariff_change': -0.02,          # 关税下降2%
            'trade_agreement': 1,            # 签署1个贸易协定
            'export_policy': 0.1             # 出口政策支持度10%
        },
        'overall_stance': 'supportive',
        'economic_pressure': 0.6
    }


def test_macro_economic_analyzer():
    """测试宏观经济分析器"""
    print("=== 测试宏观经济分析器 ===")
    
    analyzer = MacroEconomicAnalyzer()
    macro_data = create_sample_macro_data()
    
    try:
        analysis = analyzer.analyze_macro_indicators(macro_data)
        
        if 'error' not in analysis:
            print(f"✓ 成功分析宏观经济指标")
            print(f"✓ 宏观经济综合评分: {analysis['macro_score']:.3f}")
            print(f"✓ 经济周期阶段: {analysis['economic_cycle']}")
            print(f"✓ 经济前景: {analysis['economic_outlook']}")
            
            # 显示各指标评分
            indicator_scores = analysis['indicator_scores']
            print("✓ 各指标评分:")
            for indicator, score in indicator_scores.items():
                print(f"   {indicator}: {score:.3f}")
            
            # 显示关键驱动因素
            key_drivers = analysis.get('key_drivers', [])
            if key_drivers:
                print(f"✓ 关键驱动因素: {', '.join(key_drivers)}")
            
            return analysis
        else:
            print(f"✗ 宏观经济分析失败: {analysis['error']}")
            return None
            
    except Exception as e:
        print(f"✗ 宏观经济分析异常: {e}")
        return None


def test_market_technical_analyzer():
    """测试市场技术面分析器"""
    print("\n=== 测试市场技术面分析器 ===")
    
    analyzer = MarketTechnicalAnalyzer()
    market_data = create_sample_market_data()
    index_data = create_sample_index_data()
    
    try:
        analysis = analyzer.analyze_market_technical(market_data, index_data)
        
        if 'error' not in analysis:
            print(f"✓ 成功分析市场技术面")
            print(f"✓ 技术面综合评分: {analysis['technical_score']:.3f}")
            print(f"✓ 技术形态: {analysis['technical_pattern']}")
            
            # 显示各组件评分
            component_scores = analysis['component_scores']
            print("✓ 技术面组件评分:")
            for component, score in component_scores.items():
                print(f"   {component}: {score:.3f}")
            
            # 显示趋势分析
            trend_analysis = analysis['trend_analysis']
            print(f"✓ 趋势方向: {trend_analysis['direction']}")
            print(f"✓ 趋势强度: {trend_analysis['strength']:.3f}")
            
            # 显示支撑阻力
            support_resistance = analysis['support_resistance']
            print(f"✓ 支撑位: {support_resistance['support']:.0f}")
            print(f"✓ 阻力位: {support_resistance['resistance']:.0f}")
            print(f"✓ 当前位置: {support_resistance['current_position']:.1%}")
            
            return analysis
        else:
            print(f"✗ 市场技术面分析失败: {analysis['error']}")
            return None
            
    except Exception as e:
        print(f"✗ 市场技术面分析异常: {e}")
        return None


def test_fund_flow_analyzer():
    """测试资金流向分析器"""
    print("\n=== 测试资金流向分析器 ===")
    
    analyzer = FundFlowAnalyzer()
    flow_data = create_sample_flow_data()
    
    try:
        analysis = analyzer.analyze_fund_flow(flow_data)
        
        if 'error' not in analysis:
            print(f"✓ 成功分析资金流向")
            print(f"✓ 资金流向综合评分: {analysis['overall_flow_score']:.3f}")
            print(f"✓ 主导资金类型: {analysis['dominant_fund_type']}")
            print(f"✓ 资金流向趋势: {analysis['flow_trend']}")
            
            # 显示各类资金评分
            flow_scores = analysis['flow_scores']
            print("✓ 各类资金评分:")
            for fund_type, score in flow_scores.items():
                print(f"   {fund_type}: {score:.3f}")
            
            # 显示北向资金详情
            northbound = analysis['northbound_analysis']
            print(f"✓ 北向资金净流入: {northbound['net_inflow']:.1f}亿元")
            print(f"✓ 北向资金趋势: {northbound['trend']}")
            
            return analysis
        else:
            print(f"✗ 资金流向分析失败: {analysis['error']}")
            return None
            
    except Exception as e:
        print(f"✗ 资金流向分析异常: {e}")
        return None


def test_policy_impact_analyzer():
    """测试政策影响分析器"""
    print("\n=== 测试政策影响分析器 ===")
    
    analyzer = PolicyImpactAnalyzer()
    policy_data = create_sample_policy_data()
    
    try:
        analysis = analyzer.analyze_policy_impact(policy_data)
        
        if 'error' not in analysis:
            print(f"✓ 成功分析政策影响")
            print(f"✓ 政策影响综合评分: {analysis['overall_policy_score']:.3f}")
            print(f"✓ 政策预期: {analysis['policy_expectation']}")
            
            # 显示各政策领域评分
            policy_scores = analysis['policy_scores']
            print("✓ 各政策领域评分:")
            for policy_type, score in policy_scores.items():
                print(f"   {policy_type}: {score:.3f}")
            
            # 显示关键政策驱动因素
            key_drivers = analysis.get('key_policy_drivers', [])
            if key_drivers:
                print(f"✓ 关键政策驱动因素: {', '.join(key_drivers)}")
            
            # 显示货币政策详情
            monetary = analysis['monetary_analysis']
            print(f"✓ 货币政策立场: {monetary['stance']}")
            
            return analysis
        else:
            print(f"✗ 政策影响分析失败: {analysis['error']}")
            return None
            
    except Exception as e:
        print(f"✗ 政策影响分析异常: {e}")
        return None


def test_market_analyzer():
    """测试市场分析器主类"""
    print("\n=== 测试市场分析器主类 ===")
    
    analyzer = MarketAnalyzer()
    macro_data = create_sample_macro_data()
    market_data = create_sample_market_data()
    index_data = create_sample_index_data()
    flow_data = create_sample_flow_data()
    policy_data = create_sample_policy_data()
    
    try:
        # 市场走势预测
        forecast = analyzer.predict_market_trend(
            macro_data, market_data, index_data, flow_data, policy_data, '1M'
        )
        
        print(f"✓ 成功预测市场走势")
        print(f"✓ 趋势方向: {forecast.trend_direction.name}")
        print(f"✓ 市场阶段: {forecast.market_phase.value}")
        print(f"✓ 预测置信度: {forecast.confidence_level:.1%}")
        print(f"✓ 时间周期: {forecast.time_horizon}")
        print(f"✓ 目标区间: {forecast.target_range[0]:.0f} - {forecast.target_range[1]:.0f}")
        
        # 显示关键因素
        if forecast.key_factors:
            print(f"✓ 关键支撑因素: {', '.join(forecast.key_factors[:3])}")
        
        # 显示风险因素
        if forecast.risk_factors:
            print(f"✓ 主要风险因素: {', '.join(forecast.risk_factors[:3])}")
        
        # 生成市场报告
        analysis_details = {
            'macro_analysis': create_sample_macro_data(),
            'technical_analysis': {'technical_score': 0.65},
            'fund_flow_analysis': {'overall_flow_score': 0.72},
            'policy_analysis': {'overall_policy_score': 0.68}
        }
        
        market_report = analyzer.generate_market_report(forecast, analysis_details)
        
        print("✓ 市场分析报告生成成功")
        print(f"✓ 投资建议: {market_report['investment_suggestion']}")
        
        return forecast
        
    except Exception as e:
        print(f"✗ 市场分析器测试异常: {e}")
        return None


def main():
    """主测试函数"""
    print("开始测试大盘走势预测特征模块...")
    print("=" * 50)
    
    # 运行所有测试
    macro_analysis = test_macro_economic_analyzer()
    technical_analysis = test_market_technical_analyzer()
    flow_analysis = test_fund_flow_analyzer()
    policy_analysis = test_policy_impact_analyzer()
    market_forecast = test_market_analyzer()
    
    print("\n" + "=" * 50)
    if all([macro_analysis, technical_analysis, flow_analysis, 
            policy_analysis, market_forecast]):
        print("✓ 所有测试通过！大盘走势预测特征模块工作正常")
    else:
        print("✗ 部分测试失败，请检查相关模块")


if __name__ == "__main__":
    main()