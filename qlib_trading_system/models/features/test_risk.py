"""
风险评估特征模块测试
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# 直接导入模块，避免包导入问题
current_dir = os.path.dirname(__file__)
sys.path.insert(0, current_dir)

try:
    from risk_analyzer import (
        RiskAnalyzer, LiquidityRiskAnalyzer, FundamentalRiskAnalyzer,
        TechnicalRiskAnalyzer, SystematicRiskAnalyzer, RiskLevel
    )
except ImportError as e:
    print(f"Import error: {e}")
    # Try importing individual classes
    import risk_analyzer
    RiskAnalyzer = risk_analyzer.RiskAnalyzer
    LiquidityRiskAnalyzer = risk_analyzer.LiquidityRiskAnalyzer
    FundamentalRiskAnalyzer = risk_analyzer.FundamentalRiskAnalyzer
    TechnicalRiskAnalyzer = risk_analyzer.TechnicalRiskAnalyzer
    SystematicRiskAnalyzer = risk_analyzer.SystematicRiskAnalyzer
    RiskLevel = risk_analyzer.RiskLevel


def create_sample_price_data():
    """创建示例价格数据"""
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
    
    np.random.seed(42)
    
    # 模拟股价走势（包含一些风险特征）
    base_price = 20.0
    prices = []
    volumes = []
    
    for i, date in enumerate(dates):
        # 添加趋势和随机波动
        if i < 100:
            trend = 0.001  # 上升趋势
            volatility = 0.02
        elif i < 200:
            trend = -0.002  # 下降趋势（增加风险）
            volatility = 0.04  # 高波动率
        else:
            trend = 0.0005
            volatility = 0.015
        
        noise = np.random.normal(0, volatility)
        
        if i == 0:
            price = base_price
        else:
            price = prices[-1] * (1 + trend + noise)
        
        prices.append(max(price, 1.0))  # 确保价格为正
        
        # 模拟成交量（包含异常放大）
        base_volume = 1000000
        if i % 50 == 0:  # 每50天有一次成交量异常放大
            volume_multiplier = np.random.uniform(3, 5)
        else:
            volume_multiplier = np.random.uniform(0.5, 2.0)
        
        volume = max(base_volume * volume_multiplier, 100000)
        volumes.append(volume)
    
    # 生成OHLC数据
    data = []
    for i, (date, close, volume) in enumerate(zip(dates, prices, volumes)):
        if i == 0:
            open_price = close
        else:
            open_price = prices[i-1] * (1 + np.random.normal(0, 0.01))
        
        high = max(open_price, close) * (1 + abs(np.random.normal(0, 0.01)))
        low = min(open_price, close) * (1 - abs(np.random.normal(0, 0.01)))
        
        data.append({
            'date': date,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
    
    return pd.DataFrame(data).set_index('date')


def create_sample_financial_data():
    """创建示例财务数据"""
    dates = pd.date_range(start='2020-01-01', end='2023-12-31', freq='QE')
    
    np.random.seed(42)
    
    data = []
    for i, date in enumerate(dates):
        # 模拟财务数据恶化趋势（增加基本面风险）
        base_revenue = 1000 * (1 + i * 0.02)  # 基础增长
        
        if i > 10:  # 后期业绩恶化
            revenue_decline = (i - 10) * 0.05
            base_revenue *= (1 - revenue_decline)
        
        revenue = max(base_revenue * (1 + np.random.normal(0, 0.1)), 100)
        
        # 利润率逐渐下降
        profit_margin = max(0.15 - i * 0.005, 0.02)
        profit = revenue * profit_margin * (1 + np.random.normal(0, 0.2))
        
        # 资产负债率逐渐上升
        debt_ratio = min(30 + i * 2, 85)  # 从30%上升到85%
        
        # 流动比率逐渐下降
        current_ratio = max(2.0 - i * 0.05, 0.8)
        
        data.append({
            'date': date,
            'total_revenue': revenue,
            'net_profit': profit,
            'debt_ratio': debt_ratio,
            'current_ratio': current_ratio,
            'roe': profit / (revenue * 0.6) * 100,  # 简化计算
            'net_margin': profit / revenue * 100
        })
    
    return pd.DataFrame(data).set_index('date')


def create_sample_market_data():
    """创建示例市场数据"""
    return {
        'vix': 35.0,  # 高恐慌指数
        'market_volatility': 0.045,  # 高市场波动率
        'liquidity_index': 0.25,  # 低流动性
        'interest_rate_trend': 0.025,  # 利率快速上升
        'regulatory_intensity': 0.8,  # 监管趋严
        'sector_weights': {
            '科技': 0.7,  # 高行业集中度
            '医药': 0.2,
            '消费': 0.1
        }
    }


def create_sample_technical_indicators():
    """创建示例技术指标"""
    return {
        'rsi_14': 85,  # 超买
        'volume_surge': 4.5,  # 成交量异常放大
        'volatility': 0.06,  # 高波动率
        'max_drawdown': 0.25,  # 大回撤
        'trend_strength': -0.3  # 下降趋势
    }


def test_liquidity_risk_analyzer():
    """测试流动性风险分析器"""
    print("=== 测试流动性风险分析器 ===")
    
    analyzer = LiquidityRiskAnalyzer()
    price_data = create_sample_price_data()
    
    try:
        analysis = analyzer.assess_liquidity_risk(price_data)
        
        if analysis:
            print(f"✓ 成功评估流动性风险")
            print(f"✓ 流动性风险评分: {analysis['liquidity_risk_score']:.3f}")
            print(f"✓ 成交量风险: {analysis['volume_risk']:.3f}")
            print(f"✓ 价差风险: {analysis['spread_risk']:.3f}")
            
            risk_factors = analysis.get('risk_factors', [])
            if risk_factors:
                print(f"✓ 风险因素: {', '.join(risk_factors)}")
            else:
                print("✓ 无明显流动性风险因素")
            
            return analysis
        else:
            print("✗ 流动性风险评估失败")
            return None
            
    except Exception as e:
        print(f"✗ 流动性风险分析异常: {e}")
        return None


def test_fundamental_risk_analyzer():
    """测试基本面风险分析器"""
    print("\n=== 测试基本面风险分析器 ===")
    
    analyzer = FundamentalRiskAnalyzer()
    financial_data = create_sample_financial_data()
    
    try:
        analysis = analyzer.assess_fundamental_risk(financial_data)
        
        if analysis:
            print(f"✓ 成功评估基本面风险")
            print(f"✓ 基本面风险评分: {analysis['fundamental_risk_score']:.3f}")
            print(f"✓ 财务健康风险: {analysis['financial_health_risk']:.3f}")
            print(f"✓ 盈利能力风险: {analysis['profitability_risk']:.3f}")
            print(f"✓ 成长性风险: {analysis['growth_risk']:.3f}")
            
            risk_factors = analysis.get('risk_factors', [])
            if risk_factors:
                print(f"✓ 风险因素: {', '.join(risk_factors)}")
            else:
                print("✓ 无明显基本面风险因素")
            
            return analysis
        else:
            print("✗ 基本面风险评估失败")
            return None
            
    except Exception as e:
        print(f"✗ 基本面风险分析异常: {e}")
        return None


def test_technical_risk_analyzer():
    """测试技术面风险分析器"""
    print("\n=== 测试技术面风险分析器 ===")
    
    analyzer = TechnicalRiskAnalyzer()
    price_data = create_sample_price_data()
    technical_indicators = create_sample_technical_indicators()
    
    try:
        analysis = analyzer.assess_technical_risk(price_data, technical_indicators)
        
        if analysis:
            print(f"✓ 成功评估技术面风险")
            print(f"✓ 技术面风险评分: {analysis['technical_risk_score']:.3f}")
            print(f"✓ 波动率风险: {analysis['volatility_risk']:.3f}")
            print(f"✓ 趋势风险: {analysis['trend_risk']:.3f}")
            print(f"✓ 指标风险: {analysis['indicator_risk']:.3f}")
            
            risk_factors = analysis.get('risk_factors', [])
            if risk_factors:
                print(f"✓ 风险因素: {', '.join(risk_factors)}")
            else:
                print("✓ 无明显技术面风险因素")
            
            return analysis
        else:
            print("✗ 技术面风险评估失败")
            return None
            
    except Exception as e:
        print(f"✗ 技术面风险分析异常: {e}")
        return None


def test_systematic_risk_analyzer():
    """测试系统性风险分析器"""
    print("\n=== 测试系统性风险分析器 ===")
    
    analyzer = SystematicRiskAnalyzer()
    market_data = create_sample_market_data()
    
    try:
        analysis = analyzer.assess_systematic_risk(market_data, market_data)
        
        if analysis:
            print(f"✓ 成功评估系统性风险")
            print(f"✓ 系统性风险评分: {analysis['systematic_risk_score']:.3f}")
            print(f"✓ 市场风险: {analysis['market_risk']:.3f}")
            print(f"✓ 政策风险: {analysis['policy_risk']:.3f}")
            print(f"✓ 行业风险: {analysis['sector_risk']:.3f}")
            
            risk_factors = analysis.get('risk_factors', [])
            if risk_factors:
                print(f"✓ 风险因素: {', '.join(risk_factors)}")
            else:
                print("✓ 无明显系统性风险因素")
            
            return analysis
        else:
            print("✗ 系统性风险评估失败")
            return None
            
    except Exception as e:
        print(f"✗ 系统性风险分析异常: {e}")
        return None


def test_risk_analyzer():
    """测试风险分析器主类"""
    print("\n=== 测试风险分析器主类 ===")
    
    analyzer = RiskAnalyzer()
    price_data = create_sample_price_data()
    financial_data = create_sample_financial_data()
    market_data = create_sample_market_data()
    technical_indicators = create_sample_technical_indicators()
    
    try:
        # 单只股票风险分析
        analysis = analyzer.extract_risk_features(
            price_data, financial_data, market_data, technical_indicators, 'TEST001'
        )
        
        if 'error' not in analysis:
            print(f"✓ 成功提取风险特征")
            print(f"✓ 股票代码: {analysis['symbol']}")
            print(f"✓ 综合风险评分: {analysis['overall_risk_score']:.3f}")
            print(f"✓ 风险等级: {analysis['risk_level']}")
            
            # 显示各维度风险
            liquidity_score = analysis['liquidity_risk']['liquidity_risk_score']
            fundamental_score = analysis['fundamental_risk']['fundamental_risk_score']
            technical_score = analysis['technical_risk']['technical_risk_score']
            systematic_score = analysis['systematic_risk']['systematic_risk_score']
            
            print(f"✓ 流动性风险: {liquidity_score:.3f}")
            print(f"✓ 基本面风险: {fundamental_score:.3f}")
            print(f"✓ 技术面风险: {technical_score:.3f}")
            print(f"✓ 系统性风险: {systematic_score:.3f}")
            
            # 显示风险预警
            risk_alerts = analysis.get('risk_alerts', [])
            if risk_alerts:
                print(f"✓ 风险预警数量: {len(risk_alerts)}")
                for alert in risk_alerts:
                    print(f"   - {alert.risk_type}: {alert.message} (当前值: {alert.current_value:.3f})")
            else:
                print("✓ 无高风险预警")
            
            # 显示风险汇总
            risk_summary = analysis['risk_summary']
            print(f"✓ 风险因素总数: {risk_summary['total_risk_factors']}")
            print(f"✓ 主要风险类型: {risk_summary['primary_risk_type']}")
            print(f"✓ 主要风险评分: {risk_summary['primary_risk_score']:.3f}")
            
            # 批量分析测试
            batch_data = {
                'TEST001': {
                    'price_data': price_data,
                    'financial_data': financial_data,
                    'market_data': market_data,
                    'technical_indicators': technical_indicators
                },
                'TEST002': {
                    'price_data': price_data.copy(),
                    'financial_data': financial_data.copy(),
                    'market_data': market_data.copy(),
                    'technical_indicators': {**technical_indicators, 'rsi_14': 25}  # 不同的RSI
                }
            }
            
            batch_results = analyzer.batch_risk_analysis(batch_data)
            print(f"✓ 批量分析完成，分析了 {len(batch_results)} 只股票")
            
            # 获取高风险股票
            high_risk_stocks = analyzer.get_high_risk_stocks(batch_results, risk_threshold=0.6)
            if high_risk_stocks:
                print("✓ 高风险股票:")
                for i, (symbol, risk_score) in enumerate(high_risk_stocks, 1):
                    print(f"   {i}. {symbol}: {risk_score:.3f}")
            else:
                print("✓ 无高风险股票")
            
            return analysis
        else:
            print(f"✗ 风险特征提取失败: {analysis['error']}")
            return None
            
    except Exception as e:
        print(f"✗ 风险分析器测试异常: {e}")
        return None


def main():
    """主测试函数"""
    print("开始测试风险评估特征模块...")
    print("=" * 50)
    
    # 运行所有测试
    liquidity_analysis = test_liquidity_risk_analyzer()
    fundamental_analysis = test_fundamental_risk_analyzer()
    technical_analysis = test_technical_risk_analyzer()
    systematic_analysis = test_systematic_risk_analyzer()
    risk_analysis = test_risk_analyzer()
    
    print("\n" + "=" * 50)
    if all([liquidity_analysis, fundamental_analysis, technical_analysis, 
            systematic_analysis, risk_analysis]):
        print("✓ 所有测试通过！风险评估特征模块工作正常")
    else:
        print("✗ 部分测试失败，请检查相关模块")


if __name__ == "__main__":
    main()