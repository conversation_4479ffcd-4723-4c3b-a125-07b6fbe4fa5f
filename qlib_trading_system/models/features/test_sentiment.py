"""
情绪分析特征系统测试
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# 直接导入模块，避免包导入问题
current_dir = os.path.dirname(__file__)
sys.path.insert(0, current_dir)

from sentiment_analyzer import (
    SentimentAnalyzer, NewsTextAnalyzer, SocialMediaAnalyzer,
    MarketSentimentIndicator, FermentationAnalyzer,
    NewsItem, SocialMediaPost
)


def create_sample_news_data():
    """创建示例新闻数据"""
    news_items = []
    
    # 正面新闻
    positive_news = [
        {"title": "公司业绩大幅增长，净利润同比上涨50%", "content": "公司发布年报，营收和净利润均创历史新高，看好未来发展前景"},
        {"title": "重大合作协议签署，订单金额超10亿", "content": "公司与知名企业签署战略合作协议，预计将带来丰厚收益"},
        {"title": "技术突破获得认可，产品竞争力大幅提升", "content": "公司核心技术获得行业专家高度评价，市场前景广阔"}
    ]
    
    # 负面新闻
    negative_news = [
        {"title": "监管部门立案调查，股价大跌", "content": "公司因涉嫌违规被监管部门立案调查，投资者担心后续影响"},
        {"title": "主要客户取消订单，业绩预警", "content": "公司最大客户取消大额订单，预计将对业绩造成重大不利影响"}
    ]
    
    # 中性新闻
    neutral_news = [
        {"title": "公司召开股东大会，讨论年度报告", "content": "公司按计划召开年度股东大会，审议通过各项议案"},
        {"title": "行业会议召开，探讨发展趋势", "content": "相关行业协会召开年度会议，公司参与讨论行业发展方向"}
    ]
    
    all_news = positive_news + negative_news + neutral_news
    
    for i, news in enumerate(all_news):
        news_item = NewsItem(
            title=news["title"],
            content=news["content"],
            source="财经新闻" if i % 2 == 0 else "证券时报",
            publish_time=datetime.now() - timedelta(hours=i*2),
            sentiment_score=0,  # 将由分析器计算
            keywords=[],
            impact_score=np.random.uniform(0.3, 0.8)
        )
        news_items.append(news_item)
    
    return news_items


def create_sample_social_data():
    """创建示例社交媒体数据"""
    social_posts = []
    
    # 正面帖子
    positive_posts = [
        "这只股票真的很强势，技术面和基本面都很好，继续看好！",
        "公司业绩超预期，果断买入，目标价看到30元",
        "主力资金持续流入，成交量放大，准备起飞了",
        "利好消息不断，这波行情才刚开始"
    ]
    
    # 负面帖子
    negative_posts = [
        "这公司有问题，业绩造假的可能性很大，建议远离",
        "技术面已经破位，止损出局，等待更好的机会",
        "主力在出货，散户还在接盘，太危险了"
    ]
    
    # 中性帖子
    neutral_posts = [
        "今天成交量一般，观望为主",
        "等待财报公布，再做决定",
        "市场震荡，保持谨慎"
    ]
    
    all_posts = positive_posts + negative_posts + neutral_posts
    
    for i, content in enumerate(all_posts):
        post = SocialMediaPost(
            platform="微博" if i % 3 == 0 else "雪球" if i % 3 == 1 else "知乎",
            content=content,
            author=f"用户{i+1}",
            publish_time=datetime.now() - timedelta(hours=i),
            likes=np.random.randint(10, 500),
            shares=np.random.randint(5, 100),
            comments=np.random.randint(3, 50),
            sentiment_score=0,  # 将由分析器计算
            influence_score=0   # 将由分析器计算
        )
        social_posts.append(post)
    
    return social_posts


def create_sample_market_data():
    """创建示例市场数据"""
    return {
        'vix': 18.5,
        'put_call_ratio': 0.9,
        'market_momentum': 0.15,
        'ipo_demand': 8.5,
        'advance_decline_ratio': 0.65,
        'margin_balance_change': 0.05,
        'new_accounts_change': 0.12,
        'fund_purchase_redemption_ratio': 1.3,
        'volume_ratio': 1.8,
        'turnover_rate': 3.2,
        'limit_up_count': 45,
        'hot_concept_performance': 0.08,
        'northbound_net_inflow': **********,  # 25亿
        'main_fund_net_inflow': **********,   # 18亿
        'margin_buy_change': 0.08,
        'sector_performance': {
            '科技': 0.05,
            '医药': 0.03,
            '消费': -0.01,
            '金融': -0.02,
            '地产': -0.04
        }
    }


def test_news_text_analyzer():
    """测试新闻文本分析器"""
    print("=== 测试新闻文本分析器 ===")
    
    analyzer = NewsTextAnalyzer()
    news_data = create_sample_news_data()
    
    try:
        analysis = analyzer.analyze_news_sentiment(news_data)
        
        if analysis:
            print(f"✓ 成功分析新闻情绪")
            print(f"✓ 新闻总数: {analysis['news_count']}")
            print(f"✓ 整体情绪: {analysis['overall_sentiment']:.3f}")
            print(f"✓ 情绪强度: {analysis['sentiment_intensity']:.3f}")
            
            distribution = analysis['sentiment_distribution']
            print(f"✓ 情绪分布: 正面={distribution['positive']}, "
                  f"负面={distribution['negative']}, 中性={distribution['neutral']}")
            
            # 显示单条新闻分析结果
            print("✓ 单条新闻情绪得分:")
            for i, (news, score) in enumerate(zip(news_data[:3], analysis['sentiment_scores'][:3])):
                print(f"   {i+1}. {news.title[:20]}... -> {score:.3f}")
            
            return analysis
        else:
            print("✗ 新闻文本分析失败")
            return None
            
    except Exception as e:
        print(f"✗ 新闻文本分析异常: {e}")
        return None


def test_social_media_analyzer():
    """测试社交媒体分析器"""
    print("\n=== 测试社交媒体分析器 ===")
    
    analyzer = SocialMediaAnalyzer()
    social_data = create_sample_social_data()
    
    try:
        analysis = analyzer.analyze_social_sentiment(social_data)
        
        if analysis:
            print(f"✓ 成功分析社交媒体情绪")
            print(f"✓ 帖子总数: {analysis['post_count']}")
            print(f"✓ 整体情绪: {analysis['overall_sentiment']:.3f}")
            print(f"✓ 热度指数: {analysis['heat_index']:.1f}")
            print(f"✓ 影响力得分: {analysis['influence_score']:.3f}")
            print(f"✓ 总互动数: {analysis['total_engagement']}")
            
            distribution = analysis['sentiment_distribution']
            print(f"✓ 情绪分布: 正面={distribution['positive']:.1%}, "
                  f"负面={distribution['negative']:.1%}, 中性={distribution['neutral']:.1%}")
            
            return analysis
        else:
            print("✗ 社交媒体分析失败")
            return None
            
    except Exception as e:
        print(f"✗ 社交媒体分析异常: {e}")
        return None


def test_market_sentiment_indicator():
    """测试市场情绪指标"""
    print("\n=== 测试市场情绪指标 ===")
    
    indicator = MarketSentimentIndicator()
    market_data = create_sample_market_data()
    
    try:
        analysis = indicator.calculate_market_sentiment_indicators(market_data)
        
        if analysis:
            print(f"✓ 成功计算市场情绪指标")
            print(f"✓ 恐慌贪婪指数: {analysis['fear_greed_index']:.1f}")
            print(f"✓ 投资者情绪指数: {analysis['investor_sentiment']:.1f}")
            print(f"✓ 市场热度指数: {analysis['market_heat']:.1f}")
            print(f"✓ 资金情绪指标: {analysis['money_sentiment']:.1f}")
            print(f"✓ 板块轮动指标: {analysis['sector_rotation']:.1f}")
            print(f"✓ 综合情绪评分: {analysis['overall_sentiment']:.1f}")
            
            # 情绪解读
            overall = analysis['overall_sentiment']
            if overall > 70:
                sentiment_desc = "极度乐观"
            elif overall > 60:
                sentiment_desc = "乐观"
            elif overall > 40:
                sentiment_desc = "中性偏乐观"
            elif overall > 30:
                sentiment_desc = "中性偏悲观"
            elif overall > 20:
                sentiment_desc = "悲观"
            else:
                sentiment_desc = "极度悲观"
            
            print(f"✓ 市场情绪: {sentiment_desc}")
            
            return analysis
        else:
            print("✗ 市场情绪指标计算失败")
            return None
            
    except Exception as e:
        print(f"✗ 市场情绪指标异常: {e}")
        return None


def test_fermentation_analyzer():
    """测试舆论发酵分析器"""
    print("\n=== 测试舆论发酵分析器 ===")
    
    analyzer = FermentationAnalyzer()
    news_data = create_sample_news_data()
    social_data = create_sample_social_data()
    
    try:
        analysis = analyzer.analyze_fermentation_level(news_data, social_data)
        
        if analysis:
            print(f"✓ 成功分析舆论发酵程度")
            print(f"✓ 发酵水平: {analysis['fermentation_level']:.3f}")
            print(f"✓ 发酵阶段: {analysis['fermentation_stage']}")
            print(f"✓ 发酵强度: {analysis['intensity']:.3f}")
            print(f"✓ 趋势预测: {analysis['trend_prediction']}")
            print(f"✓ 持续时长: {analysis['duration_hours']} 小时")
            
            if analysis.get('peak_time'):
                print(f"✓ 峰值时间: {analysis['peak_time'].strftime('%Y-%m-%d %H:%M')}")
            
            return analysis
        else:
            print("✗ 舆论发酵分析失败")
            return None
            
    except Exception as e:
        print(f"✗ 舆论发酵分析异常: {e}")
        return None


def test_sentiment_analyzer():
    """测试情绪分析器主类"""
    print("\n=== 测试情绪分析器主类 ===")
    
    analyzer = SentimentAnalyzer()
    news_data = create_sample_news_data()
    social_data = create_sample_social_data()
    market_data = create_sample_market_data()
    
    try:
        # 单只股票情绪分析
        analysis = analyzer.extract_sentiment_features(
            news_data, social_data, market_data, 'TEST001'
        )
        
        if 'error' not in analysis:
            print(f"✓ 成功提取情绪特征")
            print(f"✓ 股票代码: {analysis['symbol']}")
            print(f"✓ 综合情绪评分: {analysis['overall_sentiment_score']:.3f}")
            
            # 显示各维度情绪
            news_sentiment = analysis['news_sentiment']['overall_sentiment']
            social_sentiment = analysis['social_sentiment']['overall_sentiment']
            market_sentiment = analysis['market_sentiment']['overall_sentiment']
            
            print(f"✓ 新闻情绪: {news_sentiment:.3f}")
            print(f"✓ 社交情绪: {social_sentiment:.3f}")
            print(f"✓ 市场情绪: {market_sentiment:.1f}")
            
            # 显示情绪汇总
            summary = analysis['sentiment_summary']
            print(f"✓ 主导情绪: {summary['dominant_sentiment']}")
            print(f"✓ 情绪强度: {summary['sentiment_strength']}")
            
            if summary['key_factors']:
                print(f"✓ 关键因素: {', '.join(summary['key_factors'])}")
            
            if summary['risk_alerts']:
                print(f"✓ 风险预警: {', '.join(summary['risk_alerts'])}")
            
            # 批量分析测试
            batch_data = {
                'TEST001': {
                    'news': news_data,
                    'social': social_data,
                    'market': market_data
                },
                'TEST002': {
                    'news': news_data[:3],  # 较少新闻
                    'social': social_data[:5],  # 较少社交数据
                    'market': market_data
                }
            }
            
            batch_results = analyzer.batch_sentiment_analysis(batch_data)
            print(f"✓ 批量分析完成，分析了 {len(batch_results)} 只股票")
            
            # 获取情绪最积极的股票
            top_positive = analyzer.get_top_sentiment_stocks(batch_results, 'positive', 5)
            print("✓ 情绪最积极股票:")
            for i, (symbol, score) in enumerate(top_positive, 1):
                print(f"   {i}. {symbol}: {score:.3f}")
            
            # 获取情绪最消极的股票
            top_negative = analyzer.get_top_sentiment_stocks(batch_results, 'negative', 5)
            print("✓ 情绪最消极股票:")
            for i, (symbol, score) in enumerate(top_negative, 1):
                print(f"   {i}. {symbol}: {score:.3f}")
            
            return analysis
        else:
            print(f"✗ 情绪特征提取失败: {analysis['error']}")
            return None
            
    except Exception as e:
        print(f"✗ 情绪分析器测试异常: {e}")
        return None


def main():
    """主测试函数"""
    print("开始测试情绪分析特征系统...")
    print("=" * 50)
    
    # 运行所有测试
    news_analysis = test_news_text_analyzer()
    social_analysis = test_social_media_analyzer()
    market_analysis = test_market_sentiment_indicator()
    fermentation_analysis = test_fermentation_analyzer()
    sentiment_analysis = test_sentiment_analyzer()
    
    print("\n" + "=" * 50)
    if all([news_analysis, social_analysis, market_analysis, 
            fermentation_analysis, sentiment_analysis]):
        print("✓ 所有测试通过！情绪分析特征系统工作正常")
    else:
        print("✗ 部分测试失败，请检查相关模块")


if __name__ == "__main__":
    main()