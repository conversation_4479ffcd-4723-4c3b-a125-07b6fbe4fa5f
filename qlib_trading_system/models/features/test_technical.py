"""
技术分析特征引擎测试
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# 直接导入模块，避免包导入问题
current_dir = os.path.dirname(__file__)
sys.path.insert(0, current_dir)

from technical_analyzer import (
    TechnicalAnalyzer, TechnicalIndicatorEngine, InstitutionalBehaviorAnalyzer,
    PatternRecognitionEngine, VolumeAnalyzer
)


def create_sample_price_data():
    """创建示例价格数据"""
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
    
    np.random.seed(42)
    
    # 模拟股价走势
    base_price = 20.0
    prices = []
    volumes = []
    
    for i, date in enumerate(dates):
        # 添加趋势和随机波动
        trend = 0.001 * i  # 轻微上升趋势
        noise = np.random.normal(0, 0.02)
        
        if i == 0:
            price = base_price
        else:
            price = prices[-1] * (1 + trend + noise)
        
        prices.append(price)
        
        # 模拟成交量
        base_volume = 1000000
        volume_noise = np.random.normal(1, 0.3)
        volume = max(base_volume * volume_noise, 100000)
        volumes.append(volume)
    
    # 生成OHLC数据
    data = []
    for i, (date, close, volume) in enumerate(zip(dates, prices, volumes)):
        # 模拟开盘价
        if i == 0:
            open_price = close
        else:
            open_price = prices[i-1] * (1 + np.random.normal(0, 0.01))
        
        # 模拟最高价和最低价
        high = max(open_price, close) * (1 + abs(np.random.normal(0, 0.01)))
        low = min(open_price, close) * (1 - abs(np.random.normal(0, 0.01)))
        
        data.append({
            'date': date,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
    
    return pd.DataFrame(data).set_index('date')


def test_technical_indicator_engine():
    """测试技术指标计算引擎"""
    print("=== 测试技术指标计算引擎 ===")
    
    engine = TechnicalIndicatorEngine()
    price_data = create_sample_price_data()
    
    try:
        indicators = engine.calculate_all_indicators(price_data)
        
        if not indicators.empty:
            print(f"✓ 成功计算技术指标，数据行数: {len(indicators)}")
            print(f"✓ 总指标数量: {len(indicators.columns)}")
            
            # 检查关键指标
            latest = indicators.iloc[-1]
            print(f"✓ 最新RSI_14: {latest.get('RSI_14', 0):.2f}")
            print(f"✓ 最新MACD: {latest.get('MACD', 0):.4f}")
            print(f"✓ 最新ADX: {latest.get('ADX', 0):.2f}")
            print(f"✓ 最新成交量放大倍数: {latest.get('VOLUME_SURGE', 0):.2f}")
            print(f"✓ 最新价格加速度: {latest.get('PRICE_ACCELERATION', 0):.4f}")
            
            # 检查指标分类
            trend_indicators = [col for col in indicators.columns if any(x in col for x in ['SMA', 'EMA', 'MACD', 'ADX'])]
            momentum_indicators = [col for col in indicators.columns if any(x in col for x in ['RSI', 'STOCH', 'CCI'])]
            volume_indicators = [col for col in indicators.columns if any(x in col for x in ['OBV', 'MFI', 'VOL'])]
            
            print(f"✓ 趋势指标: {len(trend_indicators)} 个")
            print(f"✓ 动量指标: {len(momentum_indicators)} 个")
            print(f"✓ 成交量指标: {len(volume_indicators)} 个")
            
            return indicators
        else:
            print("✗ 技术指标计算失败")
            return None
            
    except Exception as e:
        print(f"✗ 技术指标引擎测试异常: {e}")
        return None


def test_institutional_behavior_analyzer():
    """测试主力行为分析器"""
    print("\n=== 测试主力行为分析器 ===")
    
    analyzer = InstitutionalBehaviorAnalyzer()
    price_data = create_sample_price_data()
    
    try:
        # 不提供真实主力数据，使用模拟数据
        institutional_data = analyzer.analyze_institutional_behavior(price_data)
        
        if not institutional_data.empty:
            print(f"✓ 成功分析主力行为")
            
            latest = institutional_data.iloc[-1]
            print(f"✓ 龙虎榜净买入比例: {latest.get('DT_NET_RATIO', 0):.4f}")
            print(f"✓ 北向资金持仓变化: {latest.get('NB_HOLDING_CHANGE', 0):.4f}")
            print(f"✓ 大宗交易溢价率: {latest.get('BT_PREMIUM', 0):.4f}")
            print(f"✓ 综合主力评分: {latest.get('INSTITUTIONAL_SCORE', 0):.3f}")
            
            # 统计主力行为信号
            positive_signals = len(institutional_data[institutional_data['INSTITUTIONAL_SCORE'] > 0.3])
            negative_signals = len(institutional_data[institutional_data['INSTITUTIONAL_SCORE'] < -0.3])
            
            print(f"✓ 主力买入信号: {positive_signals} 天")
            print(f"✓ 主力卖出信号: {negative_signals} 天")
            
            return institutional_data
        else:
            print("✗ 主力行为分析失败")
            return None
            
    except Exception as e:
        print(f"✗ 主力行为分析异常: {e}")
        return None


def test_pattern_recognition_engine():
    """测试形态识别引擎"""
    print("\n=== 测试形态识别引擎 ===")
    
    engine = PatternRecognitionEngine()
    price_data = create_sample_price_data()
    
    # 先计算技术指标，因为形态识别需要这些指标
    indicator_engine = TechnicalIndicatorEngine()
    technical_data = indicator_engine.calculate_all_indicators(price_data)
    
    try:
        pattern_data = engine.recognize_patterns(technical_data)
        
        if not pattern_data.empty:
            print(f"✓ 成功识别技术形态")
            
            latest = pattern_data.iloc[-1]
            print(f"✓ 突破形态信号: {latest.get('PATTERN_BREAKOUT', 0)}")
            print(f"✓ 反转形态信号: {latest.get('PATTERN_REVERSAL', 0)}")
            print(f"✓ 持续形态信号: {latest.get('PATTERN_CONTINUATION', 0)}")
            print(f"✓ 整理形态信号: {latest.get('PATTERN_CONSOLIDATION', 0)}")
            print(f"✓ 综合形态强度: {latest.get('PATTERN_STRENGTH', 0):.3f}")
            
            # 统计各种形态出现次数
            breakout_count = len(pattern_data[pattern_data['PATTERN_BREAKOUT'] > 0])
            reversal_count = len(pattern_data[pattern_data['PATTERN_REVERSAL'] > 0])
            continuation_count = len(pattern_data[pattern_data['PATTERN_CONTINUATION'] > 0])
            
            print(f"✓ 突破形态出现: {breakout_count} 次")
            print(f"✓ 反转形态出现: {reversal_count} 次")
            print(f"✓ 持续形态出现: {continuation_count} 次")
            
            return pattern_data
        else:
            print("✗ 形态识别失败")
            return None
            
    except Exception as e:
        print(f"✗ 形态识别引擎异常: {e}")
        return None


def test_volume_analyzer():
    """测试量价关系分析器"""
    print("\n=== 测试量价关系分析器 ===")
    
    analyzer = VolumeAnalyzer()
    price_data = create_sample_price_data()
    
    try:
        volume_data = analyzer.analyze_volume_price_relationship(price_data)
        
        if not volume_data.empty:
            print(f"✓ 成功分析量价关系")
            
            latest = volume_data.iloc[-1]
            print(f"✓ 量价配合度: {latest.get('VP_COORDINATION', 0)}")
            print(f"✓ 成交量异常: {latest.get('VOLUME_ANOMALY', 0)}")
            print(f"✓ 资金流向: {latest.get('MONEY_FLOW', 0):.4f}")
            print(f"✓ 量价背离: {latest.get('VP_DIVERGENCE', 0)}")
            
            # 统计量价关系
            coordination_positive = len(volume_data[volume_data['VP_COORDINATION'] > 0])
            coordination_negative = len(volume_data[volume_data['VP_COORDINATION'] < 0])
            volume_surge_days = len(volume_data[volume_data['VOLUME_ANOMALY'] > 0])
            
            print(f"✓ 量价配合天数: {coordination_positive} 天")
            print(f"✓ 量价背离天数: {coordination_negative} 天")
            print(f"✓ 成交量异常放大天数: {volume_surge_days} 天")
            
            return volume_data
        else:
            print("✗ 量价关系分析失败")
            return None
            
    except Exception as e:
        print(f"✗ 量价关系分析异常: {e}")
        return None


def test_technical_analyzer():
    """测试技术分析器主类"""
    print("\n=== 测试技术分析器主类 ===")
    
    analyzer = TechnicalAnalyzer()
    price_data = create_sample_price_data()
    
    try:
        # 单只股票技术分析
        analysis = analyzer.extract_technical_features(price_data, symbol='TEST001')
        
        if 'error' not in analysis:
            print(f"✓ 成功提取技术特征")
            print(f"✓ 股票代码: {analysis['symbol']}")
            print(f"✓ 技术面评分: {analysis['technical_score']:.3f}")
            
            # 显示最新特征
            latest_features = analysis['latest_features']
            print(f"✓ RSI_14: {latest_features.get('rsi_14', 0):.2f}")
            print(f"✓ 趋势强度: {latest_features.get('trend_strength', 0):.3f}")
            print(f"✓ 成交量放大: {latest_features.get('volume_surge', 0):.2f}")
            print(f"✓ 主力评分: {latest_features.get('institutional_score', 0):.3f}")
            print(f"✓ 形态强度: {latest_features.get('pattern_strength', 0):.3f}")
            
            # 显示信号汇总
            signal_summary = analysis['signal_summary']
            print(f"✓ 信号总数: {signal_summary.get('total_signals', 0)}")
            print(f"✓ 整体情绪: {signal_summary.get('overall_sentiment', 'neutral')}")
            
            signals = signal_summary.get('signals', [])
            if signals:
                print("✓ 主要信号:")
                for signal in signals[:3]:  # 显示前3个信号
                    print(f"   - {signal['type']}: {signal['signal']} (强度: {signal['strength']:.3f})")
            
            # 批量分析测试
            batch_data = {'TEST001': price_data, 'TEST002': price_data.copy()}
            batch_results = analyzer.batch_technical_analysis(batch_data)
            print(f"✓ 批量分析完成，分析了 {len(batch_results)} 只股票")
            
            # 获取技术面最强股票
            top_stocks = analyzer.get_top_technical_stocks(batch_results, top_n=5)
            print("✓ 技术面最强股票:")
            for i, (symbol, score) in enumerate(top_stocks, 1):
                print(f"   {i}. {symbol}: {score:.3f}")
            
            return analysis
        else:
            print(f"✗ 技术特征提取失败: {analysis['error']}")
            return None
            
    except Exception as e:
        print(f"✗ 技术分析器测试异常: {e}")
        return None


def main():
    """主测试函数"""
    print("开始测试技术分析特征引擎...")
    print("=" * 50)
    
    # 运行所有测试
    indicators = test_technical_indicator_engine()
    institutional_data = test_institutional_behavior_analyzer()
    pattern_data = test_pattern_recognition_engine()
    volume_data = test_volume_analyzer()
    analysis = test_technical_analyzer()
    
    print("\n" + "=" * 50)
    if all([indicators is not None, institutional_data is not None, 
            pattern_data is not None, volume_data is not None, analysis]):
        print("✓ 所有测试通过！技术分析特征引擎工作正常")
    else:
        print("✗ 部分测试失败，请检查相关模块")


if __name__ == "__main__":
    main()