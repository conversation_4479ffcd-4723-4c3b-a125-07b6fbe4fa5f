"""
模型训练和更新系统集成模块
整合增量学习、版本管理、性能监控和A/B测试功能
"""

import os
import json
import logging
import threading
import schedule
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Callable
from dataclasses import dataclass, asdict
from pathlib import Path
import numpy as np
import pandas as pd
from enum import Enum

from qlib_trading_system.utils.logging.logger import get_logger
from qlib_trading_system.models.stock_selection.incremental_trainer import (
    IncrementalTrainer, IncrementalConfig
)
from qlib_trading_system.models.stock_selection.model_version_manager import (
    ModelVersionManager, ModelMetadata, ModelStatus
)
from qlib_trading_system.models.stock_selection.performance_monitor_advanced import (
    PerformanceMonitorAdvanced, PerformanceMetric, AlertRule, AlertLevel, MetricType
)
from qlib_trading_system.models.stock_selection.ab_testing_framework import (
    ABTestingFramework, ExperimentConfig, ExperimentVariant, TrafficSplitMethod
)

logger = get_logger(__name__)


class SystemStatus(Enum):
    """系统状态枚举"""
    IDLE = "idle"
    TRAINING = "training"
    TESTING = "testing"
    DEPLOYING = "deploying"
    MONITORING = "monitoring"
    ERROR = "error"


@dataclass
class TrainingConfig:
    """训练配置"""
    # 增量学习配置
    incremental_config: IncrementalConfig
    
    # 训练调度
    training_schedule: str = "daily"  # daily, weekly, hourly
    auto_deploy_threshold: float = 0.05  # 自动部署阈值
    
    # A/B测试配置
    enable_ab_testing: bool = True
    ab_test_duration_days: int = 7
    ab_test_traffic_split: float = 0.1  # 新模型流量比例
    
    # 性能监控配置
    enable_monitoring: bool = True
    monitoring_interval: int = 300  # 监控间隔（秒）
    
    # 模型保留策略
    max_model_versions: int = 20
    model_retention_days: int = 90


class ModelTrainingSystem:
    """模型训练和更新系统"""
    
    def __init__(self, config: TrainingConfig, base_dir: str = "models/training_system"):
        self.config = config
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化子系统
        self.incremental_trainer = IncrementalTrainer(
            config.incremental_config,
            str(self.base_dir / "incremental")
        )
        
        self.version_manager = ModelVersionManager(
            str(self.base_dir / "versions")
        )
        
        self.performance_monitor = PerformanceMonitorAdvanced(
            str(self.base_dir / "monitor_config.json")
        )
        
        self.ab_framework = ABTestingFramework(
            str(self.base_dir / "experiments")
        )
        
        # 系统状态
        self.status = SystemStatus.IDLE
        self.current_experiment_id = None
        self.training_history = []
        
        # 调度器
        self.scheduler_thread = None
        self.is_running = False
        
        # 回调函数
        self.callbacks = {
            'on_training_start': [],
            'on_training_complete': [],
            'on_deployment': [],
            'on_alert': []
        }
        
        # 设置默认报警规则
        self._setup_default_alert_rules()
        
        logger.info("模型训练和更新系统初始化完成")
    
    def initialize_base_model(self, X_train: np.ndarray, y_train: np.ndarray,
                            X_val: np.ndarray = None, y_val: np.ndarray = None,
                            model_name: str = "explosive_stock_model",
                            description: str = "爆发股识别模型") -> str:
        """初始化基础模型"""
        try:
            logger.info("开始初始化基础模型...")
            self.status = SystemStatus.TRAINING
            
            # 训练基础模型
            result = self.incremental_trainer.initialize_base_model(
                X_train, y_train, X_val, y_val
            )
            
            # 创建模型元数据
            metadata = ModelMetadata(
                version="",  # 将自动生成
                name=model_name,
                description=description,
                created_time=datetime.now(),
                model_type="LightGBM",
                framework="lightgbm",
                train_score=result['train_score'],
                validation_score=result['val_score'] or 0.0,
                hyperparameters=self.config.incremental_config.__dict__,
                feature_count=X_train.shape[1],
                training_samples=len(X_train),
                status=ModelStatus.TRAINING
            )
            
            # 创建版本
            version = self.version_manager.create_version(
                self.incremental_trainer.current_model, metadata
            )
            
            # 部署为生产版本
            self.version_manager.deploy_version(version)
            
            # 设置性能基线
            if result['val_score']:
                self.performance_monitor.set_baseline(
                    MetricType.ACCURACY, result['val_score'], version
                )
            
            # 记录训练历史
            self.training_history.append({
                'timestamp': datetime.now(),
                'type': 'base_model_initialization',
                'version': version,
                'train_score': result['train_score'],
                'val_score': result['val_score']
            })
            
            self.status = SystemStatus.IDLE
            self._trigger_callbacks('on_training_complete', {
                'version': version,
                'type': 'base_model',
                'result': result
            })
            
            logger.info(f"基础模型初始化完成，版本: {version}")
            return version
            
        except Exception as e:
            self.status = SystemStatus.ERROR
            logger.error(f"基础模型初始化失败: {e}")
            raise
    
    def add_training_data(self, features: np.ndarray, labels: np.ndarray,
                         timestamps: List[datetime] = None, weights: np.ndarray = None):
        """添加训练数据"""
        try:
            # 添加到增量训练器
            self.incremental_trainer.add_training_data(features, labels, timestamps, weights)
            
            # 记录性能指标
            if len(features) > 0:
                avg_label = np.mean(labels)
                self.performance_monitor.add_metric(
                    PerformanceMetric(
                        name="training_data_quality",
                        value=avg_label,
                        timestamp=datetime.now(),
                        model_version=self.version_manager.get_active_version() or "unknown",
                        data_source="training_pipeline"
                    )
                )
            
            logger.info(f"添加了 {len(features)} 个训练样本")
            
        except Exception as e:
            logger.error(f"添加训练数据失败: {e}")
            raise
    
    def trigger_incremental_update(self, force: bool = False) -> Dict:
        """触发增量更新"""
        try:
            if self.status == SystemStatus.TRAINING and not force:
                return {'status': 'busy', 'message': '系统正在训练中'}
            
            logger.info("开始增量模型更新...")
            self.status = SystemStatus.TRAINING
            
            self._trigger_callbacks('on_training_start', {'type': 'incremental'})
            
            # 执行增量更新
            update_result = self.incremental_trainer.perform_incremental_update()
            
            if update_result['status'] == 'success':
                # 创建新版本
                current_version = self.version_manager.get_active_version()
                current_metadata = self.version_manager.get_version_info(current_version)
                
                # 创建新版本元数据
                new_metadata = ModelMetadata(
                    version="",  # 将自动生成
                    name=current_metadata.name,
                    description=f"增量更新版本 - 基于 {current_version}",
                    created_time=datetime.now(),
                    model_type=current_metadata.model_type,
                    framework=current_metadata.framework,
                    train_score=update_result['new_score'],
                    validation_score=update_result['new_score'],
                    hyperparameters=current_metadata.hyperparameters,
                    feature_count=current_metadata.feature_count,
                    training_samples=current_metadata.training_samples,
                    parent_version=current_version,
                    status=ModelStatus.TRAINING
                )
                
                # 创建新版本
                new_version = self.version_manager.create_version(
                    self.incremental_trainer.current_model, new_metadata
                )
                
                # 决定是否自动部署或启动A/B测试
                improvement = update_result['improvement']
                
                if improvement > self.config.auto_deploy_threshold:
                    if self.config.enable_ab_testing:
                        # 启动A/B测试
                        experiment_id = self._start_ab_test(current_version, new_version)
                        result = {
                            'status': 'ab_test_started',
                            'new_version': new_version,
                            'improvement': improvement,
                            'experiment_id': experiment_id
                        }
                    else:
                        # 直接部署
                        self.version_manager.deploy_version(new_version)
                        result = {
                            'status': 'deployed',
                            'new_version': new_version,
                            'improvement': improvement
                        }
                        
                        self._trigger_callbacks('on_deployment', {
                            'version': new_version,
                            'type': 'auto_deploy'
                        })
                else:
                    result = {
                        'status': 'improvement_insufficient',
                        'new_version': new_version,
                        'improvement': improvement,
                        'threshold': self.config.auto_deploy_threshold
                    }
                
                # 记录训练历史
                self.training_history.append({
                    'timestamp': datetime.now(),
                    'type': 'incremental_update',
                    'old_version': current_version,
                    'new_version': new_version,
                    'improvement': improvement,
                    'action': result['status']
                })
                
            else:
                result = update_result
            
            self.status = SystemStatus.IDLE
            self._trigger_callbacks('on_training_complete', {
                'type': 'incremental',
                'result': result
            })
            
            return result
            
        except Exception as e:
            self.status = SystemStatus.ERROR
            logger.error(f"增量更新失败: {e}")
            return {'status': 'error', 'message': str(e)}
    
    def start_monitoring(self):
        """启动性能监控"""
        if self.config.enable_monitoring:
            self.performance_monitor.start_monitoring(self.config.monitoring_interval)
            logger.info("性能监控已启动")
    
    def stop_monitoring(self):
        """停止性能监控"""
        self.performance_monitor.stop_monitoring()
        logger.info("性能监控已停止")
    
    def start_scheduler(self):
        """启动训练调度器"""
        if self.is_running:
            logger.warning("调度器已在运行中")
            return
        
        self.is_running = True
        
        # 设置调度任务
        if self.config.training_schedule == "daily":
            schedule.every().day.at("02:00").do(self._scheduled_training)
        elif self.config.training_schedule == "weekly":
            schedule.every().week.do(self._scheduled_training)
        elif self.config.training_schedule == "hourly":
            schedule.every().hour.do(self._scheduled_training)
        
        # 启动调度线程
        self.scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
        self.scheduler_thread.start()
        
        logger.info(f"训练调度器已启动，调度频率: {self.config.training_schedule}")
    
    def stop_scheduler(self):
        """停止训练调度器"""
        self.is_running = False
        schedule.clear()
        
        if self.scheduler_thread:
            self.scheduler_thread.join(timeout=5)
        
        logger.info("训练调度器已停止")
    
    def predict_with_monitoring(self, features: np.ndarray, 
                              model_version: str = None) -> np.ndarray:
        """带监控的预测"""
        try:
            start_time = datetime.now()
            
            # 获取模型
            if model_version:
                model, metadata = self.version_manager.load_model(model_version)
            else:
                model, metadata = self.version_manager.load_active_model()
                model_version = metadata.version
            
            # 执行预测
            predictions = model.predict(features)
            
            # 记录延迟指标
            latency = (datetime.now() - start_time).total_seconds() * 1000  # 毫秒
            self.performance_monitor.add_metric(
                PerformanceMetric(
                    name="prediction_latency",
                    value=latency,
                    timestamp=datetime.now(),
                    model_version=model_version,
                    data_source="prediction_service"
                )
            )
            
            return predictions
            
        except Exception as e:
            logger.error(f"预测失败: {e}")
            raise
    
    def record_prediction_feedback(self, features: np.ndarray, 
                                 predictions: np.ndarray, 
                                 true_labels: np.ndarray,
                                 model_version: str = None):
        """记录预测反馈"""
        try:
            if model_version is None:
                model_version = self.version_manager.get_active_version()
            
            # 计算准确率
            accuracy = np.mean(np.abs(predictions - true_labels) < 0.1)  # 简化的准确率计算
            
            # 记录性能指标
            self.performance_monitor.add_metric(
                PerformanceMetric(
                    name="accuracy",
                    value=accuracy,
                    timestamp=datetime.now(),
                    model_version=model_version,
                    data_source="feedback_loop"
                )
            )
            
            # 添加到增量学习
            self.add_training_data(features, true_labels)
            
            logger.debug(f"记录预测反馈: 准确率={accuracy:.4f}")
            
        except Exception as e:
            logger.error(f"记录预测反馈失败: {e}")
    
    def get_system_status(self) -> Dict:
        """获取系统状态"""
        active_version = self.version_manager.get_active_version()
        
        return {
            'system_status': self.status.value,
            'active_model_version': active_version,
            'current_experiment': self.current_experiment_id,
            'training_history_count': len(self.training_history),
            'incremental_trainer_info': self.incremental_trainer.get_model_info(),
            'performance_stats': self.performance_monitor.performance_stats,
            'active_alerts_count': len(self.performance_monitor.get_active_alerts()),
            'is_monitoring': self.performance_monitor.is_monitoring,
            'is_scheduler_running': self.is_running
        }
    
    def generate_system_report(self, output_path: str) -> bool:
        """生成系统报告"""
        try:
            active_version = self.version_manager.get_active_version()
            
            report = {
                'report_time': datetime.now().isoformat(),
                'system_status': self.get_system_status(),
                'model_versions': [v.to_dict() for v in self.version_manager.list_versions(limit=10)],
                'training_history': self.training_history[-20:],  # 最近20次训练
                'performance_report': self.performance_monitor.generate_performance_report(
                    active_version, 86400  # 最近24小时
                ) if active_version else {},
                'active_alerts': [alert.to_dict() for alert in self.performance_monitor.get_active_alerts()],
                'experiments': self.ab_framework.list_experiments()
            }
            
            # 保存报告
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False, default=str)
            
            logger.info(f"系统报告已生成: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"生成系统报告失败: {e}")
            return False
    
    def rollback_model(self, target_version: str, reason: str = "") -> bool:
        """回滚模型"""
        try:
            logger.info(f"开始回滚到版本: {target_version}")
            
            # 停止当前A/B测试
            if self.current_experiment_id:
                self.ab_framework.stop_experiment(
                    self.current_experiment_id, 
                    f"模型回滚: {reason}"
                )
                self.current_experiment_id = None
            
            # 执行回滚
            success = self.version_manager.rollback_to_version(target_version, reason)
            
            if success:
                # 记录回滚事件
                self.training_history.append({
                    'timestamp': datetime.now(),
                    'type': 'rollback',
                    'target_version': target_version,
                    'reason': reason
                })
                
                self._trigger_callbacks('on_deployment', {
                    'version': target_version,
                    'type': 'rollback',
                    'reason': reason
                })
            
            return success
            
        except Exception as e:
            logger.error(f"模型回滚失败: {e}")
            return False
    
    def cleanup_old_versions(self):
        """清理旧版本"""
        try:
            # 归档旧版本
            archived = self.version_manager.archive_old_versions(
                keep_count=self.config.max_model_versions,
                keep_days=self.config.model_retention_days
            )
            
            if archived:
                logger.info(f"已归档 {len(archived)} 个旧版本")
            
        except Exception as e:
            logger.error(f"清理旧版本失败: {e}")
    
    def add_callback(self, event: str, callback: Callable):
        """添加回调函数"""
        if event in self.callbacks:
            self.callbacks[event].append(callback)
            logger.info(f"添加回调函数: {event}")
    
    def _start_ab_test(self, control_version: str, treatment_version: str) -> str:
        """启动A/B测试"""
        try:
            logger.info(f"启动A/B测试: {control_version} vs {treatment_version}")
            self.status = SystemStatus.TESTING
            
            # 创建实验配置
            config = ExperimentConfig(
                name=f"模型对比实验_{treatment_version}",
                description=f"对比版本 {control_version} 和 {treatment_version}",
                start_time=datetime.now(),
                end_time=datetime.now() + timedelta(days=self.config.ab_test_duration_days),
                traffic_split_method=TrafficSplitMethod.HASH_BASED,
                traffic_allocation={
                    "control": 1.0 - self.config.ab_test_traffic_split,
                    "treatment": self.config.ab_test_traffic_split
                }
            )
            
            # 创建变体
            variants = [
                ExperimentVariant(
                    name="control",
                    description=f"当前生产模型 {control_version}",
                    model_version=control_version,
                    model_config={},
                    is_control=True
                ),
                ExperimentVariant(
                    name="treatment",
                    description=f"新模型 {treatment_version}",
                    model_version=treatment_version,
                    model_config={},
                    is_control=False
                )
            ]
            
            # 创建实验
            experiment_id = self.ab_framework.create_experiment(config, variants)
            self.ab_framework.start_experiment(experiment_id)
            
            self.current_experiment_id = experiment_id
            self.status = SystemStatus.MONITORING
            
            logger.info(f"A/B测试已启动: {experiment_id}")
            return experiment_id
            
        except Exception as e:
            self.status = SystemStatus.ERROR
            logger.error(f"启动A/B测试失败: {e}")
            raise
    
    def _scheduled_training(self):
        """定时训练任务"""
        try:
            logger.info("执行定时训练任务")
            
            # 检查是否有足够的新数据
            buffer_size = self.incremental_trainer.data_buffer.size()
            min_samples = self.config.incremental_config.min_samples_for_update
            
            if buffer_size >= min_samples:
                self.trigger_incremental_update()
            else:
                logger.info(f"数据不足，跳过训练。当前: {buffer_size}, 需要: {min_samples}")
            
            # 清理旧版本
            self.cleanup_old_versions()
            
        except Exception as e:
            logger.error(f"定时训练任务失败: {e}")
    
    def _scheduler_loop(self):
        """调度器循环"""
        while self.is_running:
            try:
                schedule.run_pending()
                threading.Event().wait(60)  # 每分钟检查一次
            except Exception as e:
                logger.error(f"调度器循环异常: {e}")
    
    def _setup_default_alert_rules(self):
        """设置默认报警规则"""
        # 准确率下降报警
        accuracy_rule = AlertRule(
            name="accuracy_degradation",
            metric_type=MetricType.ACCURACY,
            condition="lt",
            threshold=0.7,
            alert_level=AlertLevel.WARNING,
            consecutive_violations=3
        )
        self.performance_monitor.add_alert_rule(accuracy_rule)
        
        # 延迟过高报警
        latency_rule = AlertRule(
            name="high_latency",
            metric_type=MetricType.PREDICTION_LATENCY,
            condition="gt",
            threshold=1000,  # 1秒
            alert_level=AlertLevel.ERROR,
            consecutive_violations=5
        )
        self.performance_monitor.add_alert_rule(latency_rule)
        
        logger.info("默认报警规则已设置")
    
    def _trigger_callbacks(self, event: str, data: Dict):
        """触发回调函数"""
        try:
            for callback in self.callbacks.get(event, []):
                callback(data)
        except Exception as e:
            logger.error(f"触发回调函数失败: {e}")


# 使用示例和测试
if __name__ == "__main__":
    # 创建训练配置
    incremental_config = IncrementalConfig(
        batch_size=500,
        learning_rate_decay=0.98,
        min_samples_for_update=200,
        update_frequency='daily'
    )
    
    training_config = TrainingConfig(
        incremental_config=incremental_config,
        training_schedule="daily",
        auto_deploy_threshold=0.03,
        enable_ab_testing=True,
        ab_test_duration_days=3,
        ab_test_traffic_split=0.2
    )
    
    # 创建训练系统
    training_system = ModelTrainingSystem(training_config)
    
    # 添加回调函数
    def on_training_complete(data):
        print(f"训练完成回调: {data}")
    
    def on_deployment(data):
        print(f"部署回调: {data}")
    
    training_system.add_callback('on_training_complete', on_training_complete)
    training_system.add_callback('on_deployment', on_deployment)
    
    # 模拟初始化基础模型
    np.random.seed(42)
    X_train = np.random.randn(2000, 20)
    y_train = np.random.randn(2000)
    X_val = np.random.randn(400, 20)
    y_val = np.random.randn(400)
    
    # 初始化基础模型
    base_version = training_system.initialize_base_model(
        X_train, y_train, X_val, y_val,
        model_name="爆发股识别模型",
        description="基于LightGBM的爆发股识别模型"
    )
    print(f"基础模型版本: {base_version}")
    
    # 启动监控和调度
    training_system.start_monitoring()
    
    # 模拟增量数据
    for i in range(3):
        X_new = np.random.randn(300, 20)
        y_new = np.random.randn(300)
        training_system.add_training_data(X_new, y_new)
        
        # 触发增量更新
        result = training_system.trigger_incremental_update()
        print(f"增量更新 {i+1} 结果: {result}")
    
    # 模拟预测和反馈
    X_test = np.random.randn(100, 20)
    predictions = training_system.predict_with_monitoring(X_test)
    y_true = np.random.randn(100)
    training_system.record_prediction_feedback(X_test, predictions, y_true)
    
    # 获取系统状态
    status = training_system.get_system_status()
    print(f"系统状态: {json.dumps(status, indent=2, default=str)}")
    
    # 生成系统报告
    training_system.generate_system_report("system_report.json")
    
    # 停止监控
    training_system.stop_monitoring()
    
    print("模型训练和更新系统测试完成")