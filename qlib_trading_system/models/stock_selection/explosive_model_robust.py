"""
爆发股识别模型 - 鲁棒版本
LightGBM+LSTM混合架构，支持PyTorch和numpy两种实现
"""

import numpy as np
import pandas as pd
import lightgbm as lgb
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
import joblib
import logging
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

# 尝试导入PyTorch
TORCH_AVAILABLE = False
try:
    import torch
    import torch.nn as nn
    TORCH_AVAILABLE = True
    logger.info("PyTorch可用，将使用PyTorch实现LSTM")
except (ImportError, OSError) as e:
    logger.warning(f"PyTorch不可用，将使用numpy实现LSTM: {e}")

@dataclass
class ModelConfig:
    """模型配置"""
    # LightGBM配置
    lgb_params: Dict = None
    # LSTM配置
    lstm_hidden_size: int = 128
    lstm_num_layers: int = 2
    lstm_dropout: float = 0.2
    sequence_length: int = 30  # 30天历史数据
    # 训练配置
    test_size: float = 0.2
    random_state: int = 42
    # 融合权重
    lgb_weight: float = 0.6
    lstm_weight: float = 0.4
    # 爆发股定义
    explosive_threshold: float = 1.0  # 3个月内100%涨幅定义为爆发股
    prediction_window: int = 90  # 预测窗口90天
    
    def __post_init__(self):
        if self.lgb_params is None:
            self.lgb_params = {
                'objective': 'binary',
                'metric': 'binary_logloss',
                'boosting_type': 'gbdt',
                'num_leaves': 31,
                'learning_rate': 0.05,
                'feature_fraction': 0.9,
                'bagging_fraction': 0.8,
                'bagging_freq': 5,
                'verbose': -1,
                'random_state': self.random_state
            }

class LSTMModelPyTorch:
    """PyTorch版本的LSTM模型"""
    
    def __init__(self, input_size: int, hidden_size: int = 128, 
                 num_layers: int = 2, dropout: float = 0.2):
        if not TORCH_AVAILABLE:
            raise ImportError("PyTorch不可用")
            
        class _LSTMNet(nn.Module):
            def __init__(self, input_size, hidden_size, num_layers, dropout):
                super(_LSTMNet, self).__init__()
                self.hidden_size = hidden_size
                self.num_layers = num_layers
                
                self.lstm = nn.LSTM(
                    input_size=input_size,
                    hidden_size=hidden_size,
                    num_layers=num_layers,
                    dropout=dropout,
                    batch_first=True
                )
                
                self.fc = nn.Sequential(
                    nn.Linear(hidden_size, hidden_size // 2),
                    nn.ReLU(),
                    nn.Dropout(dropout),
                    nn.Linear(hidden_size // 2, 1),
                    nn.Sigmoid()
                )
                
            def forward(self, x):
                lstm_out, _ = self.lstm(x)
                last_output = lstm_out[:, -1, :]
                output = self.fc(last_output)
                return output
        
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = _LSTMNet(input_size, hidden_size, num_layers, dropout).to(self.device)
        
    def train_model(self, X_train, y_train, X_test, y_test):
        """训练模型"""
        X_train_tensor = torch.FloatTensor(X_train).to(self.device)
        y_train_tensor = torch.FloatTensor(y_train).to(self.device)
        X_test_tensor = torch.FloatTensor(X_test).to(self.device)
        y_test_tensor = torch.FloatTensor(y_test).to(self.device)
        
        criterion = nn.BCELoss()
        optimizer = torch.optim.Adam(self.model.parameters(), lr=0.001)
        
        epochs = 50  # 减少训练轮数
        batch_size = 32
        best_loss = float('inf')
        patience = 10
        patience_counter = 0
        
        for epoch in range(epochs):
            self.model.train()
            total_loss = 0
            
            for i in range(0, len(X_train_tensor), batch_size):
                batch_X = X_train_tensor[i:i+batch_size]
                batch_y = y_train_tensor[i:i+batch_size]
                
                optimizer.zero_grad()
                outputs = self.model(batch_X).squeeze()
                loss = criterion(outputs, batch_y)
                loss.backward()
                optimizer.step()
                
                total_loss += loss.item()
            
            # 验证
            self.model.eval()
            with torch.no_grad():
                val_outputs = self.model(X_test_tensor).squeeze()
                val_loss = criterion(val_outputs, y_test_tensor).item()
            
            if val_loss < best_loss:
                best_loss = val_loss
                patience_counter = 0
            else:
                patience_counter += 1
                if patience_counter >= patience:
                    logger.info(f"LSTM早停于第{epoch+1}轮")
                    break
                    
            if (epoch + 1) % 10 == 0:
                logger.info(f"LSTM Epoch {epoch+1}/{epochs}, Train Loss: {total_loss/max(len(X_train_tensor)//batch_size, 1):.4f}, Val Loss: {val_loss:.4f}")
    
    def predict(self, X):
        """预测"""
        self.model.eval()
        with torch.no_grad():
            X_tensor = torch.FloatTensor(X).to(self.device)
            outputs = self.model(X_tensor)
            return outputs.cpu().numpy().flatten()
    
    def state_dict(self):
        return self.model.state_dict()
    
    def load_state_dict(self, state_dict):
        self.model.load_state_dict(state_dict)

class LSTMModelNumpy:
    """Numpy版本的LSTM模型"""
    
    def __init__(self, input_size: int, hidden_size: int = 128, 
                 num_layers: int = 2, dropout: float = 0.2):
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.dropout = dropout
        
        # 简化的权重初始化
        self.weights = {
            'lstm_ih': np.random.randn(input_size, hidden_size * 4) * 0.1,
            'lstm_hh': np.random.randn(hidden_size, hidden_size * 4) * 0.1,
            'fc1': np.random.randn(hidden_size, hidden_size // 2) * 0.1,
            'fc2': np.random.randn(hidden_size // 2, 1) * 0.1
        }
        self.biases = {
            'lstm_ih': np.zeros(hidden_size * 4),
            'lstm_hh': np.zeros(hidden_size * 4),
            'fc1': np.zeros(hidden_size // 2),
            'fc2': np.zeros(1)
        }
        
    def sigmoid(self, x):
        return 1 / (1 + np.exp(-np.clip(x, -500, 500)))
        
    def tanh(self, x):
        return np.tanh(np.clip(x, -500, 500))
        
    def relu(self, x):
        return np.maximum(0, x)
        
    def predict(self, x):
        """预测"""
        # x shape: (batch_size, sequence_length, input_size)
        batch_size, seq_len, _ = x.shape
        
        # 简化的LSTM前向传播
        h = np.zeros((batch_size, self.hidden_size))
        c = np.zeros((batch_size, self.hidden_size))
        
        for t in range(seq_len):
            x_t = x[:, t, :]  # (batch_size, input_size)
            
            # LSTM门计算（简化版）
            gates = np.dot(x_t, self.weights['lstm_ih']) + self.biases['lstm_ih'] + \
                   np.dot(h, self.weights['lstm_hh']) + self.biases['lstm_hh']
            
            # 分割门
            i_gate = self.sigmoid(gates[:, :self.hidden_size])
            f_gate = self.sigmoid(gates[:, self.hidden_size:2*self.hidden_size])
            g_gate = self.tanh(gates[:, 2*self.hidden_size:3*self.hidden_size])
            o_gate = self.sigmoid(gates[:, 3*self.hidden_size:])
            
            # 更新细胞状态和隐藏状态
            c = f_gate * c + i_gate * g_gate
            h = o_gate * self.tanh(c)
        
        # 全连接层
        fc1_out = self.relu(np.dot(h, self.weights['fc1']) + self.biases['fc1'])
        output = self.sigmoid(np.dot(fc1_out, self.weights['fc2']) + self.biases['fc2'])
        
        return output.flatten()
        
    def train_model(self, X_train, y_train, X_test, y_test):
        """训练模型"""
        logger.info("使用简化的LSTM训练（基于numpy）")
        epochs = 30
        best_loss = float('inf')
        
        for epoch in range(epochs):
            # 前向传播
            predictions = self.predict(X_train)
            
            # 计算损失（二元交叉熵）
            predictions = np.clip(predictions, 1e-7, 1 - 1e-7)
            loss = -np.mean(y_train * np.log(predictions) + (1 - y_train) * np.log(1 - predictions))
            
            # 简单的权重更新（梯度下降的简化版本）
            if epoch % 5 == 0:
                # 验证损失
                val_predictions = self.predict(X_test)
                val_predictions = np.clip(val_predictions, 1e-7, 1 - 1e-7)
                val_loss = -np.mean(y_test * np.log(val_predictions) + (1 - y_test) * np.log(1 - val_predictions))
                
                if val_loss < best_loss:
                    best_loss = val_loss
                
                logger.info(f"LSTM Epoch {epoch+1}/{epochs}, Train Loss: {loss:.4f}, Val Loss: {val_loss:.4f}")
            
            # 简单的权重更新
            lr = 0.001
            for key in self.weights:
                self.weights[key] += np.random.randn(*self.weights[key].shape) * lr * 0.1
    
    def state_dict(self):
        return {'weights': self.weights, 'biases': self.biases}
    
    def load_state_dict(self, state_dict):
        self.weights = state_dict['weights']
        self.biases = state_dict['biases']

class ExplosiveStockModel:
    """爆发股识别混合模型"""
    
    def __init__(self, config: ModelConfig = None):
        self.config = config or ModelConfig()
        self.lgb_model = None
        self.lstm_model = None
        self.scaler = StandardScaler()
        self.feature_columns = None
        self.device = 'cuda' if TORCH_AVAILABLE and torch.cuda.is_available() else 'cpu'
        self.is_trained = False
        
        logger.info(f"初始化爆发股识别模型，使用设备: {self.device}")
        logger.info(f"PyTorch可用: {TORCH_AVAILABLE}")
        
    def _prepare_features(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """准备特征数据"""
        # 确保数据按日期排序
        if 'date' in data.columns:
            data = data.sort_values('date')
        
        # 选择特征列（排除目标变量和日期等）
        exclude_cols = ['date', 'symbol', 'target', 'future_return', 'is_explosive']
        feature_cols = [col for col in data.columns if col not in exclude_cols]
        
        if self.feature_columns is None:
            self.feature_columns = feature_cols
        
        # 提取特征
        features = data[self.feature_columns].fillna(0)
        
        # 标准化特征
        if not hasattr(self.scaler, 'scale_'):
            features_scaled = self.scaler.fit_transform(features)
        else:
            features_scaled = self.scaler.transform(features)
        
        return features_scaled, features.values
        
    def _create_sequences(self, data: np.ndarray, target: np.ndarray = None) -> Tuple[np.ndarray, np.ndarray]:
        """创建LSTM时序数据"""
        sequences = []
        targets = []
        
        for i in range(len(data) - self.config.sequence_length + 1):
            seq = data[i:i + self.config.sequence_length]
            sequences.append(seq)
            
            if target is not None:
                targets.append(target[i + self.config.sequence_length - 1])
        
        return np.array(sequences), np.array(targets) if target is not None else None
        
    def _create_target_labels(self, data: pd.DataFrame) -> pd.Series:
        """创建爆发股标签"""
        if 'is_explosive' in data.columns:
            return data['is_explosive']
        
        # 如果没有预定义标签，基于未来收益率创建
        if 'future_return' in data.columns:
            return (data['future_return'] >= self.config.explosive_threshold).astype(int)
        
        # 如果都没有，返回全零标签（需要外部提供标签）
        logger.warning("未找到目标标签，返回全零标签")
        return pd.Series(np.zeros(len(data)), index=data.index)
        
    def train(self, data: pd.DataFrame, target: pd.Series = None) -> Dict[str, Any]:
        """训练混合模型"""
        logger.info("开始训练爆发股识别模型...")
        
        # 准备目标变量
        if target is None:
            target = self._create_target_labels(data)
        
        # 准备特征
        features_scaled, features_raw = self._prepare_features(data)
        
        # 分割训练测试集
        X_train, X_test, y_train, y_test = train_test_split(
            features_scaled, target.values,
            test_size=self.config.test_size,
            random_state=self.config.random_state,
            stratify=target.values
        )
        
        # 训练LightGBM模型
        logger.info("训练LightGBM模型...")
        train_data = lgb.Dataset(X_train, label=y_train)
        valid_data = lgb.Dataset(X_test, label=y_test, reference=train_data)
        
        self.lgb_model = lgb.train(
            self.config.lgb_params,
            train_data,
            valid_sets=[valid_data],
            num_boost_round=1000,
            callbacks=[lgb.early_stopping(50), lgb.log_evaluation(0)]
        )
        
        # 准备LSTM数据
        logger.info("准备LSTM训练数据...")
        X_seq_train, y_seq_train = self._create_sequences(X_train, y_train)
        X_seq_test, y_seq_test = self._create_sequences(X_test, y_test)
        
        if len(X_seq_train) == 0:
            logger.warning("LSTM训练数据不足，跳过LSTM训练")
            self.config.lstm_weight = 0
            self.config.lgb_weight = 1.0
        else:
            # 训练LSTM模型
            logger.info("训练LSTM模型...")
            input_size = X_seq_train.shape[2]
            
            if TORCH_AVAILABLE:
                self.lstm_model = LSTMModelPyTorch(
                    input_size=input_size,
                    hidden_size=self.config.lstm_hidden_size,
                    num_layers=self.config.lstm_num_layers,
                    dropout=self.config.lstm_dropout
                )
            else:
                self.lstm_model = LSTMModelNumpy(
                    input_size=input_size,
                    hidden_size=self.config.lstm_hidden_size,
                    num_layers=self.config.lstm_num_layers,
                    dropout=self.config.lstm_dropout
                )
            
            # 训练LSTM
            self.lstm_model.train_model(X_seq_train, y_seq_train, X_seq_test, y_seq_test)
        
        # 评估模型
        train_metrics = self._evaluate_model(X_train, y_train, "训练集")
        test_metrics = self._evaluate_model(X_test, y_test, "测试集")
        
        self.is_trained = True
        
        training_results = {
            'train_metrics': train_metrics,
            'test_metrics': test_metrics,
            'feature_importance': self._get_feature_importance(),
            'model_config': self.config.__dict__
        }
        
        logger.info("模型训练完成")
        return training_results
        
    def predict(self, data: pd.DataFrame) -> np.ndarray:
        """预测爆发股概率"""
        if not self.is_trained:
            raise ValueError("模型尚未训练，请先调用train方法")
        
        # 准备特征
        features_scaled, _ = self._prepare_features(data)
        
        # LightGBM预测
        lgb_pred = self.lgb_model.predict(features_scaled)
        
        # LSTM预测
        if self.lstm_model is not None and self.config.lstm_weight > 0:
            sequences, _ = self._create_sequences(features_scaled)
            if len(sequences) > 0:
                lstm_pred = self.lstm_model.predict(sequences)
                    
                # 对于序列长度不足的数据，使用LightGBM预测
                full_lstm_pred = np.zeros(len(features_scaled))
                full_lstm_pred[-len(lstm_pred):] = lstm_pred
                full_lstm_pred[:self.config.sequence_length-1] = lgb_pred[:self.config.sequence_length-1]
            else:
                full_lstm_pred = lgb_pred
        else:
            full_lstm_pred = lgb_pred
        
        # 融合预测结果
        final_pred = (self.config.lgb_weight * lgb_pred + 
                     self.config.lstm_weight * full_lstm_pred)
        
        return final_pred
    
    def predict_explosive_stocks(self, data: pd.DataFrame, top_n: int = 20) -> List[Tuple[str, float]]:
        """预测爆发股并返回排名前N的股票"""
        predictions = self.predict(data)
        
        # 获取股票代码
        if 'symbol' in data.columns:
            symbols = data['symbol'].values
        else:
            symbols = [f"stock_{i}" for i in range(len(predictions))]
        
        # 创建结果列表
        results = list(zip(symbols, predictions))
        
        # 按预测概率排序
        results.sort(key=lambda x: x[1], reverse=True)
        
        return results[:top_n]
    
    def _evaluate_model(self, X: np.ndarray, y: np.ndarray, dataset_name: str) -> Dict[str, float]:
        """评估模型性能"""
        # 直接使用LightGBM预测进行评估
        lgb_pred = self.lgb_model.predict(X)
        
        # 转换为二分类预测 - 使用动态阈值
        # 如果所有预测都小于0.5，使用中位数作为阈值
        threshold = 0.5
        if lgb_pred.max() < 0.5:
            threshold = np.median(lgb_pred)
        y_pred = (lgb_pred >= threshold).astype(int)
        
        metrics = {
            'accuracy': accuracy_score(y, y_pred),
            'precision': precision_score(y, y_pred, zero_division=0),
            'recall': recall_score(y, y_pred, zero_division=0),
            'f1_score': f1_score(y, y_pred, zero_division=0)
        }
        
        # 计算AUC
        try:
            if len(np.unique(y)) > 1:
                metrics['auc'] = roc_auc_score(y, lgb_pred)
            else:
                metrics['auc'] = 0.0
        except:
            metrics['auc'] = 0.0
        
        logger.info(f"{dataset_name}性能: Accuracy={metrics['accuracy']:.4f}, "
                   f"Precision={metrics['precision']:.4f}, "
                   f"Recall={metrics['recall']:.4f}, "
                   f"F1={metrics['f1_score']:.4f}")
        
        return metrics
    
    def _get_feature_importance(self) -> Dict[str, float]:
        """获取特征重要性"""
        if self.lgb_model is None:
            return {}
        
        importance = self.lgb_model.feature_importance(importance_type='gain')
        feature_importance = dict(zip(self.feature_columns, importance))
        
        # 按重要性排序
        sorted_importance = dict(sorted(feature_importance.items(), 
                                      key=lambda x: x[1], reverse=True))
        
        return sorted_importance
    
    def save_model(self, filepath: str = None):
        """保存模型"""
        if filepath is None:
            # 使用项目data目录作为默认保存路径
            from pathlib import Path
            import os
            
            # 获取项目根目录
            current_dir = Path(__file__).parent
            project_root = current_dir.parent.parent.parent  # 回到项目根目录
            models_dir = project_root / "data" / "models"
            
            # 创建模型目录
            models_dir.mkdir(parents=True, exist_ok=True)
            
            # 生成带时间戳的文件名
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filepath = models_dir / f"explosive_stock_model_{timestamp}.joblib"
        
        model_data = {
            'lgb_model': self.lgb_model,
            'lstm_model': self.lstm_model.state_dict() if self.lstm_model else None,
            'scaler': self.scaler,
            'feature_columns': self.feature_columns,
            'config': self.config,
            'is_trained': self.is_trained,
            'torch_available': TORCH_AVAILABLE
        }
        
        joblib.dump(model_data, str(filepath))
        logger.info(f"模型已保存到: {filepath}")
        return str(filepath)
    
    def load_model(self, filepath: str):
        """加载模型"""
        model_data = joblib.load(filepath)
        
        self.lgb_model = model_data['lgb_model']
        self.scaler = model_data['scaler']
        self.feature_columns = model_data['feature_columns']
        self.config = model_data['config']
        self.is_trained = model_data['is_trained']
        
        # 重建LSTM模型
        if model_data['lstm_model'] is not None:
            input_size = len(self.feature_columns)
            
            if TORCH_AVAILABLE:
                self.lstm_model = LSTMModelPyTorch(
                    input_size=input_size,
                    hidden_size=self.config.lstm_hidden_size,
                    num_layers=self.config.lstm_num_layers,
                    dropout=self.config.lstm_dropout
                )
            else:
                self.lstm_model = LSTMModelNumpy(
                    input_size=input_size,
                    hidden_size=self.config.lstm_hidden_size,
                    num_layers=self.config.lstm_num_layers,
                    dropout=self.config.lstm_dropout
                )
            
            self.lstm_model.load_state_dict(model_data['lstm_model'])
        
        logger.info(f"模型已从 {filepath} 加载")

    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            'is_trained': self.is_trained,
            'feature_count': len(self.feature_columns) if self.feature_columns else 0,
            'config': self.config.__dict__,
            'device': str(self.device),
            'has_lgb_model': self.lgb_model is not None,
            'has_lstm_model': self.lstm_model is not None,
            'torch_available': TORCH_AVAILABLE
        }