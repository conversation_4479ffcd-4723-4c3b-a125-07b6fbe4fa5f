# 模型训练和更新系统使用指南

## 概述

本文档详细介绍如何使用qlib交易系统的模型训练和更新系统。该系统集成了增量学习、版本管理、性能监控和A/B测试功能，为股票筛选模型提供完整的MLOps解决方案。

## 系统架构

```
模型训练和更新系统
├── 增量学习模块 (IncrementalTrainer)
│   ├── 在线学习
│   ├── 数据缓冲区管理
│   └── 模型增量更新
├── 版本管理模块 (ModelVersionManager)
│   ├── 版本创建和部署
│   ├── 模型回滚
│   └── 版本比较
├── 性能监控模块 (PerformanceMonitorAdvanced)
│   ├── 实时性能监控
│   ├── 模型漂移检测
│   └── 自动报警
├── A/B测试模块 (ABTestingFramework)
│   ├── 实验设计
│   ├── 流量分配
│   └── 统计分析
└── 配置管理模块 (TrainingConfigManager)
    ├── 统一配置管理
    ├── 配置验证
    └── 配置导入导出
```

## 快速开始

### 1. 基础设置

```python
import numpy as np
from qlib_trading_system.models.stock_selection.model_training_system import (
    ModelTrainingSystem, TrainingConfig
)
from qlib_trading_system.models.stock_selection.training_config_manager import (
    TrainingConfigManager
)
from qlib_trading_system.models.stock_selection.incremental_trainer import IncrementalConfig

# 创建配置管理器
config_manager = TrainingConfigManager("config/training")

# 获取训练配置
training_config = config_manager.get_training_config()

# 创建训练系统
training_system = ModelTrainingSystem(training_config, "models/training_system")
```

### 2. 初始化基础模型

```python
# 准备训练数据
X_train = np.random.randn(5000, 100)  # 5000个样本，100个特征
y_train = np.random.randn(5000)       # 目标值
X_val = np.random.randn(1000, 100)   # 验证集
y_val = np.random.randn(1000)

# 初始化基础模型
base_version = training_system.initialize_base_model(
    X_train, y_train, X_val, y_val,
    model_name="爆发股识别模型v1.0",
    description="基于LightGBM的爆发股识别模型"
)

print(f"基础模型版本: {base_version}")
```

### 3. 启动监控和调度

```python
# 启动性能监控
training_system.start_monitoring()

# 启动训练调度器（可选）
training_system.start_scheduler()

print("系统监控和调度已启动")
```

## 核心功能使用

### 增量学习

#### 添加新的训练数据

```python
# 模拟新的市场数据
X_new = np.random.randn(500, 100)
y_new = np.random.randn(500)
timestamps = [datetime.now()] * 500

# 添加到训练缓冲区
training_system.add_training_data(X_new, y_new, timestamps)
```

#### 触发增量更新

```python
# 手动触发增量更新
update_result = training_system.trigger_incremental_update()

print(f"更新结果: {update_result}")

# 可能的结果状态:
# - 'success': 更新成功并直接部署
# - 'ab_test_started': 启动A/B测试
# - 'improvement_insufficient': 改进不足，不部署
# - 'rejected': 性能下降，拒绝更新
```

#### 在线预测和学习

```python
# 带监控的预测
X_test = np.random.randn(100, 100)
predictions = training_system.predict_with_monitoring(X_test)

# 收到真实标签后，记录反馈用于持续学习
y_true = np.random.randn(100)
training_system.record_prediction_feedback(X_test, predictions, y_true)
```

### 版本管理

#### 查看版本列表

```python
# 获取所有版本
versions = training_system.version_manager.list_versions()

for version in versions[:5]:  # 显示最新5个版本
    print(f"版本: {version.version}")
    print(f"  创建时间: {version.created_time}")
    print(f"  训练分数: {version.train_score:.4f}")
    print(f"  验证分数: {version.validation_score:.4f}")
    print(f"  状态: {version.status.value}")
    print(f"  是否生产: {version.is_production}")
    print()
```

#### 版本比较

```python
# 比较两个版本
if len(versions) >= 2:
    comparison = training_system.version_manager.compare_versions(
        versions[0].version, versions[1].version
    )
    
    print("版本比较结果:")
    print(f"  性能差异: {comparison['performance_diff']}")
    print(f"  时间差异: {comparison['time_diff']} 秒")
    print(f"  特征数差异: {comparison['feature_count_diff']}")
```

#### 模型回滚

```python
# 回滚到指定版本
target_version = versions[1].version  # 回滚到第二个版本
success = training_system.rollback_model(
    target_version, 
    reason="生产环境性能下降"
)

if success:
    print(f"成功回滚到版本: {target_version}")
else:
    print("回滚失败")
```

#### 版本导出和导入

```python
# 导出版本
export_path = "exported_models/model_v1.0"
success = training_system.version_manager.export_version(
    base_version, export_path
)

# 导入版本
imported_version = training_system.version_manager.import_version(export_path)
```

### 性能监控

#### 查看实时性能

```python
from qlib_trading_system.models.stock_selection.performance_monitor_advanced import MetricType

# 获取当前性能统计
active_version = training_system.version_manager.get_active_version()
performance = training_system.performance_monitor.get_current_performance(
    MetricType.ACCURACY, active_version, time_window=3600  # 最近1小时
)

print("当前性能统计:")
print(f"  样本数: {performance['count']}")
print(f"  平均值: {performance['mean']:.4f}")
print(f"  标准差: {performance['std']:.4f}")
print(f"  最新值: {performance['latest']:.4f}")
print(f"  趋势: {performance['trend']}")
```

#### 模型漂移检测

```python
# 检测模型漂移
drift_result = training_system.performance_monitor.detect_model_drift(
    MetricType.ACCURACY, active_version
)

if drift_result['status'] == 'success':
    print("漂移检测结果:")
    print(f"  是否漂移: {drift_result['is_drift']}")
    print(f"  p值: {drift_result['p_value']:.6f}")
    print(f"  漂移分数: {drift_result['drift_score']:.4f}")
    print(f"  均值偏移: {drift_result['mean_shift']:.4f}")
```

#### 查看活跃报警

```python
# 获取活跃报警
active_alerts = training_system.performance_monitor.get_active_alerts()

print(f"当前有 {len(active_alerts)} 个活跃报警:")
for alert in active_alerts:
    print(f"  报警ID: {alert.id}")
    print(f"  级别: {alert.alert_level.value}")
    print(f"  消息: {alert.message}")
    print(f"  时间: {alert.timestamp}")
    print()
```

#### 生成性能报告

```python
# 生成详细性能报告
report = training_system.performance_monitor.generate_performance_report(
    active_version, time_range=86400  # 最近24小时
)

print("性能报告摘要:")
print(f"  模型版本: {report['model_version']}")
print(f"  报告时间: {report['report_time']}")
print(f"  指标数量: {len(report['metrics_summary'])}")
print(f"  总报警数: {report['alerts_summary']['total_alerts']}")
```

### A/B测试

#### 手动创建A/B测试

```python
from qlib_trading_system.models.stock_selection.ab_testing_framework import (
    ExperimentConfig, ExperimentVariant, TrafficSplitMethod
)
from datetime import datetime, timedelta

# 创建实验配置
experiment_config = ExperimentConfig(
    name="模型v2.0对比实验",
    description="测试新特征工程对模型性能的影响",
    start_time=datetime.now(),
    end_time=datetime.now() + timedelta(days=7),
    traffic_split_method=TrafficSplitMethod.HASH_BASED,
    traffic_allocation={
        "control": 0.8,    # 80%流量使用当前模型
        "treatment": 0.2   # 20%流量使用新模型
    }
)

# 创建实验变体
variants = [
    ExperimentVariant(
        name="control",
        description="当前生产模型",
        model_version=active_version,
        model_config={},
        is_control=True
    ),
    ExperimentVariant(
        name="treatment", 
        description="新优化模型",
        model_version="v20241201_123456_abcd1234",  # 新模型版本
        model_config={},
        is_control=False,
        expected_improvement=0.05
    )
]

# 创建并启动实验
experiment_id = training_system.ab_framework.create_experiment(
    experiment_config, variants
)
training_system.ab_framework.start_experiment(experiment_id)

print(f"A/B测试已启动: {experiment_id}")
```

#### 模拟用户流量和指标收集

```python
# 模拟用户请求
for i in range(1000):
    user_id = f"user_{i}"
    
    # 分配用户到实验变体
    variant = training_system.ab_framework.assign_variant(experiment_id, user_id)
    
    # 模拟预测和结果
    # 这里应该是真实的模型预测和业务指标
    if variant == "control":
        accuracy = np.random.normal(0.75, 0.05)  # 当前模型性能
        profit = np.random.normal(0.08, 0.03)
    else:  # treatment
        accuracy = np.random.normal(0.78, 0.05)  # 新模型性能稍好
        profit = np.random.normal(0.10, 0.03)
    
    # 记录指标
    training_system.ab_framework.record_metric(
        experiment_id, user_id, "accuracy", accuracy
    )
    training_system.ab_framework.record_metric(
        experiment_id, user_id, "profit", profit
    )
```

#### 分析实验结果

```python
# 分析准确率指标
accuracy_analysis = training_system.ab_framework.analyze_experiment(
    experiment_id, "accuracy"
)

if accuracy_analysis['status'] == 'success':
    print("准确率分析结果:")
    
    # 变体结果
    for variant_name, result in accuracy_analysis['variant_results'].items():
        print(f"  {variant_name}:")
        print(f"    样本数: {result['sample_size']}")
        print(f"    均值: {result['mean']:.4f}")
        print(f"    置信区间: [{result['confidence_interval'][0]:.4f}, {result['confidence_interval'][1]:.4f}]")
    
    # 统计检验结果
    for test_name, test_result in accuracy_analysis['statistical_tests'].items():
        print(f"  统计检验 ({test_name}):")
        print(f"    p值: {test_result['p_value']:.6f}")
        print(f"    效应量: {test_result['effect_size']:.4f}")
        print(f"    显著性: {'是' if test_result['is_significant'] else '否'}")
    
    # 建议
    print("  建议:")
    for recommendation in accuracy_analysis['recommendations']:
        print(f"    - {recommendation}")
```

#### 停止实验和决策

```python
# 根据分析结果决定是否部署新模型
if accuracy_analysis.get('status') == 'success':
    # 检查是否有显著改进
    has_significant_improvement = any(
        test['is_significant'] and test['effect_size'] > 0.1
        for test in accuracy_analysis['statistical_tests'].values()
    )
    
    if has_significant_improvement:
        print("检测到显著改进，部署新模型")
        # 这里可以自动部署新模型
        # training_system.version_manager.deploy_version(new_version)
    else:
        print("未检测到显著改进，保持当前模型")

# 停止实验
training_system.ab_framework.stop_experiment(
    experiment_id, "实验完成，已做出部署决策"
)
```

### 配置管理

#### 查看和修改配置

```python
# 查看当前配置
model_config = config_manager.get_model_config()
print(f"当前模型类型: {model_config.model_type}")
print(f"学习率: {model_config.learning_rate}")

# 修改配置
new_config = {
    "model_type": "lightgbm",
    "learning_rate": 0.05,
    "num_leaves": 50,
    "feature_fraction": 0.8
}

config_manager.update_config("model", new_config)
print("模型配置已更新")
```

#### 配置验证

```python
# 验证所有配置
validation_results = config_manager.validate_all_configs()

for config_type, errors in validation_results.items():
    if errors:
        print(f"配置 {config_type} 有错误:")
        for error in errors:
            print(f"  - {error}")
    else:
        print(f"配置 {config_type} 验证通过")
```

#### 配置导出和导入

```python
# 导出所有配置
config_manager.export_config("backup/all_configs.json")

# 导入配置
# config_manager.import_config("backup/all_configs.json")
```

## 系统监控和维护

### 系统状态检查

```python
# 获取系统整体状态
status = training_system.get_system_status()

print("系统状态:")
print(f"  当前状态: {status['system_status']}")
print(f"  活跃模型版本: {status['active_model_version']}")
print(f"  当前实验: {status['current_experiment']}")
print(f"  训练历史数量: {status['training_history_count']}")
print(f"  活跃报警数量: {status['active_alerts_count']}")
print(f"  监控状态: {'运行中' if status['is_monitoring'] else '已停止'}")
print(f"  调度器状态: {'运行中' if status['is_scheduler_running'] else '已停止'}")
```

### 生成系统报告

```python
# 生成完整系统报告
report_path = "reports/system_report.json"
success = training_system.generate_system_report(report_path)

if success:
    print(f"系统报告已生成: {report_path}")
    
    # 读取并显示报告摘要
    with open(report_path, 'r', encoding='utf-8') as f:
        report = json.load(f)
    
    print("报告摘要:")
    print(f"  报告时间: {report['report_time']}")
    print(f"  模型版本数: {len(report['model_versions'])}")
    print(f"  训练历史数: {len(report['training_history'])}")
    print(f"  活跃报警数: {len(report['active_alerts'])}")
    print(f"  实验数量: {len(report['experiments'])}")
```

### 系统清理和维护

```python
# 清理旧版本
training_system.cleanup_old_versions()

# 手动触发垃圾回收
import gc
gc.collect()

print("系统清理完成")
```

### 回调函数设置

```python
# 设置训练完成回调
def on_training_complete(data):
    print(f"训练完成通知: {data}")
    # 可以在这里发送通知、更新数据库等

# 设置部署回调
def on_deployment(data):
    print(f"模型部署通知: {data}")
    # 可以在这里更新服务配置、通知相关人员等

# 设置报警回调
def on_alert(data):
    print(f"系统报警: {data}")
    # 可以在这里发送紧急通知

# 注册回调函数
training_system.add_callback('on_training_complete', on_training_complete)
training_system.add_callback('on_deployment', on_deployment)
training_system.add_callback('on_alert', on_alert)
```

## 最佳实践

### 1. 数据质量管理

```python
# 在添加训练数据前进行质量检查
def validate_training_data(X, y):
    """验证训练数据质量"""
    # 检查缺失值
    if np.isnan(X).any() or np.isnan(y).any():
        raise ValueError("训练数据包含缺失值")
    
    # 检查数据分布
    if np.std(y) < 1e-6:
        raise ValueError("目标变量方差过小")
    
    # 检查特征数量
    if X.shape[1] != 100:  # 假设期望100个特征
        raise ValueError(f"特征数量不匹配，期望100，实际{X.shape[1]}")
    
    return True

# 使用验证函数
try:
    validate_training_data(X_new, y_new)
    training_system.add_training_data(X_new, y_new)
except ValueError as e:
    print(f"数据质量检查失败: {e}")
```

### 2. 模型性能监控

```python
# 设置性能阈值监控
from qlib_trading_system.models.stock_selection.performance_monitor_advanced import (
    AlertRule, AlertLevel, MetricType
)

# 添加自定义报警规则
custom_rules = [
    AlertRule(
        name="accuracy_critical_drop",
        metric_type=MetricType.ACCURACY,
        condition="lt",
        threshold=0.6,  # 准确率低于60%
        alert_level=AlertLevel.CRITICAL,
        consecutive_violations=2
    ),
    AlertRule(
        name="prediction_latency_high",
        metric_type=MetricType.PREDICTION_LATENCY,
        condition="gt", 
        threshold=500,  # 延迟超过500ms
        alert_level=AlertLevel.WARNING,
        consecutive_violations=5
    )
]

for rule in custom_rules:
    training_system.performance_monitor.add_alert_rule(rule)
```

### 3. 实验设计原则

```python
# A/B测试实验设计最佳实践
def design_ab_experiment(control_version, treatment_version, 
                        expected_improvement=0.05, power=0.8):
    """设计A/B测试实验"""
    
    # 计算所需样本量（简化计算）
    effect_size = expected_improvement
    alpha = 0.05
    
    # 这里应该使用统计功效分析计算实际所需样本量
    estimated_sample_size = int(16 / (effect_size ** 2))  # 简化公式
    
    # 设置合理的流量分配
    if estimated_sample_size > 10000:
        traffic_split = 0.1  # 大样本需求，使用较小流量
    else:
        traffic_split = 0.2  # 小样本需求，可以使用更多流量
    
    return {
        'estimated_sample_size': estimated_sample_size,
        'recommended_traffic_split': traffic_split,
        'recommended_duration_days': max(7, estimated_sample_size // 1000)
    }

# 使用实验设计函数
experiment_design = design_ab_experiment(
    active_version, "new_version", expected_improvement=0.03
)
print(f"实验设计建议: {experiment_design}")
```

### 4. 错误处理和恢复

```python
# 实现健壮的错误处理
def safe_model_update():
    """安全的模型更新流程"""
    try:
        # 备份当前状态
        current_version = training_system.version_manager.get_active_version()
        
        # 执行更新
        result = training_system.trigger_incremental_update()
        
        if result['status'] == 'error':
            # 更新失败，记录错误
            logger.error(f"模型更新失败: {result.get('message', 'Unknown error')}")
            return False
        
        # 验证新模型
        if 'new_version' in result:
            new_version = result['new_version']
            
            # 简单的健康检查
            try:
                X_test = np.random.randn(10, 100)
                predictions = training_system.predict_with_monitoring(X_test)
                
                if not np.all(np.isfinite(predictions)):
                    # 预测结果异常，回滚
                    training_system.rollback_model(
                        current_version, "新模型预测结果异常"
                    )
                    return False
                    
            except Exception as e:
                # 预测失败，回滚
                training_system.rollback_model(
                    current_version, f"新模型预测失败: {e}"
                )
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"模型更新过程异常: {e}")
        return False

# 使用安全更新函数
if safe_model_update():
    print("模型更新成功")
else:
    print("模型更新失败，已回滚")
```

## 故障排除

### 常见问题和解决方案

#### 1. 增量训练失败

```python
# 检查数据缓冲区状态
buffer_info = training_system.incremental_trainer.get_model_info()
print(f"缓冲区大小: {buffer_info['buffer_size']}")
print(f"最小更新样本数: {training_system.config.incremental_config.min_samples_for_update}")

# 如果缓冲区数据不足
if buffer_info['buffer_size'] < training_system.config.incremental_config.min_samples_for_update:
    print("数据不足，需要添加更多训练样本")
```

#### 2. 性能监控异常

```python
# 检查监控状态
if not training_system.performance_monitor.is_monitoring:
    print("性能监控未启动，正在重新启动...")
    training_system.start_monitoring()

# 检查报警规则
alert_rules = training_system.performance_monitor.alert_rules
print(f"当前报警规则数量: {len(alert_rules)}")
for rule_name, rule in alert_rules.items():
    print(f"  {rule_name}: {'启用' if rule.enabled else '禁用'}")
```

#### 3. A/B测试问题

```python
# 检查实验状态
if training_system.current_experiment_id:
    experiment_status = training_system.ab_framework.get_experiment_status(
        training_system.current_experiment_id
    )
    print(f"当前实验状态: {experiment_status}")
    
    # 检查样本分配
    assignment_stats = experiment_status.get('assignment_stats', {})
    if assignment_stats.get('total_assignments', 0) < 100:
        print("实验样本量过少，建议延长实验时间")
```

#### 4. 版本管理问题

```python
# 检查版本一致性
try:
    active_version = training_system.version_manager.get_active_version()
    model, metadata = training_system.version_manager.load_active_model()
    print(f"活跃版本: {active_version}")
    print(f"模型加载成功: {model is not None}")
except Exception as e:
    print(f"版本管理异常: {e}")
    
    # 尝试修复
    versions = training_system.version_manager.list_versions()
    if versions:
        latest_version = versions[0].version
        print(f"尝试切换到最新版本: {latest_version}")
        training_system.version_manager.deploy_version(latest_version)
```

## 系统关闭

```python
# 优雅关闭系统
def shutdown_system():
    """优雅关闭训练系统"""
    print("正在关闭模型训练系统...")
    
    # 停止监控
    training_system.stop_monitoring()
    print("性能监控已停止")
    
    # 停止调度器
    training_system.stop_scheduler()
    print("训练调度器已停止")
    
    # 停止当前实验
    if training_system.current_experiment_id:
        training_system.ab_framework.stop_experiment(
            training_system.current_experiment_id, "系统关闭"
        )
        print("A/B测试已停止")
    
    # 生成最终报告
    final_report_path = "reports/final_system_report.json"
    training_system.generate_system_report(final_report_path)
    print(f"最终系统报告已生成: {final_report_path}")
    
    print("系统关闭完成")

# 在程序结束时调用
shutdown_system()
```

## 总结

本指南涵盖了模型训练和更新系统的主要功能和使用方法。该系统提供了：

1. **自动化的模型生命周期管理** - 从训练到部署的全流程自动化
2. **实时性能监控** - 持续监控模型性能和系统健康状态
3. **科学的A/B测试** - 基于统计学的模型对比和决策支持
4. **灵活的配置管理** - 统一的配置管理和验证机制
5. **完善的版本控制** - 模型版本管理和回滚机制

通过合理使用这些功能，可以构建一个稳定、高效的机器学习生产系统，确保模型在生产环境中的持续优化和稳定运行。