"""
训练配置管理器
统一管理模型训练和更新系统的所有配置
"""

import os
import json
import yaml
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict, field
from pathlib import Path
from enum import Enum

from qlib_trading_system.utils.logging.logger import get_logger
from qlib_trading_system.models.stock_selection.incremental_trainer import IncrementalConfig
from qlib_trading_system.models.stock_selection.model_training_system import TrainingConfig
from qlib_trading_system.models.stock_selection.performance_monitor_advanced import AlertRule, AlertLevel, MetricType
from qlib_trading_system.models.stock_selection.ab_testing_framework import TrafficSplitMethod

logger = get_logger(__name__)


class ConfigFormat(Enum):
    """配置文件格式"""
    JSON = "json"
    YAML = "yaml"
    TOML = "toml"


@dataclass
class ModelConfig:
    """模型配置"""
    # 基础模型参数
    model_type: str = "lightgbm"
    objective: str = "regression"
    metric: str = "rmse"
    boosting_type: str = "gbdt"
    num_leaves: int = 31
    learning_rate: float = 0.1
    feature_fraction: float = 0.9
    bagging_fraction: float = 0.8
    bagging_freq: int = 5
    verbose: int = -1
    random_state: int = 42
    
    # 训练参数
    num_boost_round: int = 200
    early_stopping_rounds: int = 50
    
    # 特征工程参数
    feature_selection: bool = True
    feature_importance_threshold: float = 0.001
    max_features: int = 200
    
    def to_lightgbm_params(self) -> Dict:
        """转换为LightGBM参数"""
        return {
            'objective': self.objective,
            'metric': self.metric,
            'boosting_type': self.boosting_type,
            'num_leaves': self.num_leaves,
            'learning_rate': self.learning_rate,
            'feature_fraction': self.feature_fraction,
            'bagging_fraction': self.bagging_fraction,
            'bagging_freq': self.bagging_freq,
            'verbose': self.verbose,
            'random_state': self.random_state
        }


@dataclass
class DataConfig:
    """数据配置"""
    # 数据源配置
    primary_data_source: str = "itick"
    backup_data_sources: List[str] = field(default_factory=lambda: ["joinquant", "ricequant"])
    
    # 数据处理配置
    data_retention_days: int = 365
    validation_split: float = 0.2
    test_split: float = 0.1
    
    # 特征配置
    feature_dimensions: List[str] = field(default_factory=lambda: [
        "fundamental", "valuation", "technical", "sentiment", "risk", "market"
    ])
    
    # 数据质量配置
    missing_value_threshold: float = 0.1
    outlier_detection: bool = True
    outlier_method: str = "iqr"  # iqr, zscore, isolation_forest
    
    # 缓存配置
    enable_cache: bool = True
    cache_ttl: int = 3600  # 秒


@dataclass
class MonitoringConfig:
    """监控配置"""
    # 基础监控配置
    enable_monitoring: bool = True
    monitoring_interval: int = 300  # 秒
    
    # 性能指标配置
    metrics_to_monitor: List[str] = field(default_factory=lambda: [
        "accuracy", "precision", "recall", "f1_score", "prediction_latency"
    ])
    
    # 报警配置
    enable_alerts: bool = True
    alert_channels: List[str] = field(default_factory=lambda: ["email", "log"])
    
    # 邮件报警配置
    email_config: Dict = field(default_factory=lambda: {
        "enabled": False,
        "smtp_server": "smtp.gmail.com",
        "smtp_port": 587,
        "username": "",
        "password": "",
        "from_email": "",
        "to_emails": []
    })
    
    # 数据保留配置
    metrics_retention_days: int = 30
    alerts_retention_days: int = 90
    
    # 漂移检测配置
    drift_detection: bool = True
    drift_threshold: float = 0.05
    drift_window_hours: int = 24


@dataclass
class ExperimentConfig:
    """实验配置"""
    # A/B测试配置
    enable_ab_testing: bool = True
    default_test_duration_days: int = 7
    default_traffic_split: float = 0.1
    traffic_split_method: str = "hash_based"
    
    # 统计配置
    significance_level: float = 0.05
    minimum_sample_size: int = 1000
    power: float = 0.8
    
    # 早停配置
    early_stopping: bool = True
    early_stopping_threshold: float = 0.01
    
    # 实验管理配置
    max_concurrent_experiments: int = 3
    experiment_retention_days: int = 180


@dataclass
class DeploymentConfig:
    """部署配置"""
    # 自动部署配置
    enable_auto_deploy: bool = True
    auto_deploy_threshold: float = 0.05
    
    # 部署策略
    deployment_strategy: str = "blue_green"  # blue_green, canary, rolling
    canary_traffic_percentage: float = 0.05
    
    # 回滚配置
    enable_auto_rollback: bool = True
    rollback_threshold: float = -0.02
    rollback_window_minutes: int = 30
    
    # 版本管理配置
    max_model_versions: int = 20
    model_retention_days: int = 90
    
    # 健康检查配置
    health_check_interval: int = 60
    health_check_timeout: int = 30


@dataclass
class SystemConfig:
    """系统配置"""
    # 基础配置
    system_name: str = "qlib_trading_system"
    environment: str = "development"  # development, staging, production
    log_level: str = "INFO"
    
    # 资源配置
    max_workers: int = 4
    memory_limit_gb: int = 8
    cpu_limit_cores: int = 4
    
    # 存储配置
    base_data_dir: str = "data"
    base_model_dir: str = "models"
    base_log_dir: str = "logs"
    
    # 安全配置
    enable_authentication: bool = False
    api_key_required: bool = False
    rate_limit_per_minute: int = 1000


class TrainingConfigManager:
    """训练配置管理器"""
    
    def __init__(self, config_dir: str = "config/training"):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # 配置文件路径
        self.config_files = {
            'model': self.config_dir / "model_config.json",
            'data': self.config_dir / "data_config.json",
            'monitoring': self.config_dir / "monitoring_config.json",
            'experiment': self.config_dir / "experiment_config.json",
            'deployment': self.config_dir / "deployment_config.json",
            'system': self.config_dir / "system_config.json"
        }
        
        # 配置对象
        self.configs = {}
        
        # 加载配置
        self._load_all_configs()
        
        logger.info(f"训练配置管理器初始化完成，配置目录: {self.config_dir}")
    
    def get_model_config(self) -> ModelConfig:
        """获取模型配置"""
        return self.configs.get('model', ModelConfig())
    
    def get_data_config(self) -> DataConfig:
        """获取数据配置"""
        return self.configs.get('data', DataConfig())
    
    def get_monitoring_config(self) -> MonitoringConfig:
        """获取监控配置"""
        return self.configs.get('monitoring', MonitoringConfig())
    
    def get_experiment_config(self) -> ExperimentConfig:
        """获取实验配置"""
        return self.configs.get('experiment', ExperimentConfig())
    
    def get_deployment_config(self) -> DeploymentConfig:
        """获取部署配置"""
        return self.configs.get('deployment', DeploymentConfig())
    
    def get_system_config(self) -> SystemConfig:
        """获取系统配置"""
        return self.configs.get('system', SystemConfig())
    
    def get_incremental_config(self) -> IncrementalConfig:
        """获取增量学习配置"""
        model_config = self.get_model_config()
        data_config = self.get_data_config()
        
        return IncrementalConfig(
            batch_size=1000,
            learning_rate_decay=0.95,
            min_samples_for_update=500,
            max_memory_samples=10000,
            update_frequency='daily',
            performance_threshold=0.05,
            data_retention_days=data_config.data_retention_days,
            validation_split=data_config.validation_split,
            n_estimators_increment=50,
            max_total_estimators=1000
        )
    
    def get_training_config(self) -> TrainingConfig:
        """获取完整训练配置"""
        deployment_config = self.get_deployment_config()
        experiment_config = self.get_experiment_config()
        monitoring_config = self.get_monitoring_config()
        
        return TrainingConfig(
            incremental_config=self.get_incremental_config(),
            training_schedule="daily",
            auto_deploy_threshold=deployment_config.auto_deploy_threshold,
            enable_ab_testing=experiment_config.enable_ab_testing,
            ab_test_duration_days=experiment_config.default_test_duration_days,
            ab_test_traffic_split=experiment_config.default_traffic_split,
            enable_monitoring=monitoring_config.enable_monitoring,
            monitoring_interval=monitoring_config.monitoring_interval,
            max_model_versions=deployment_config.max_model_versions,
            model_retention_days=deployment_config.model_retention_days
        )
    
    def get_alert_rules(self) -> List[AlertRule]:
        """获取报警规则配置"""
        monitoring_config = self.get_monitoring_config()
        
        rules = []
        
        # 准确率下降报警
        rules.append(AlertRule(
            name="accuracy_degradation",
            metric_type=MetricType.ACCURACY,
            condition="lt",
            threshold=0.7,
            alert_level=AlertLevel.WARNING,
            consecutive_violations=3,
            enabled=monitoring_config.enable_alerts
        ))
        
        # 延迟过高报警
        rules.append(AlertRule(
            name="high_prediction_latency",
            metric_type=MetricType.PREDICTION_LATENCY,
            condition="gt",
            threshold=1000,  # 1秒
            alert_level=AlertLevel.ERROR,
            consecutive_violations=5,
            enabled=monitoring_config.enable_alerts
        ))
        
        # 模型漂移报警
        rules.append(AlertRule(
            name="model_drift_detection",
            metric_type=MetricType.MODEL_DRIFT,
            condition="gt",
            threshold=monitoring_config.drift_threshold,
            alert_level=AlertLevel.WARNING,
            consecutive_violations=1,
            enabled=monitoring_config.drift_detection
        ))
        
        return rules
    
    def update_config(self, config_type: str, config_data: Dict):
        """更新配置"""
        try:
            if config_type not in self.config_files:
                raise ValueError(f"不支持的配置类型: {config_type}")
            
            # 验证配置数据
            self._validate_config(config_type, config_data)
            
            # 保存配置
            config_file = self.config_files[config_type]
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            # 重新加载配置
            self._load_config(config_type)
            
            logger.info(f"配置已更新: {config_type}")
            
        except Exception as e:
            logger.error(f"更新配置失败: {e}")
            raise
    
    def export_config(self, output_path: str, format: ConfigFormat = ConfigFormat.JSON):
        """导出配置"""
        try:
            all_configs = {
                'model': asdict(self.get_model_config()),
                'data': asdict(self.get_data_config()),
                'monitoring': asdict(self.get_monitoring_config()),
                'experiment': asdict(self.get_experiment_config()),
                'deployment': asdict(self.get_deployment_config()),
                'system': asdict(self.get_system_config())
            }
            
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            if format == ConfigFormat.JSON:
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(all_configs, f, indent=2, ensure_ascii=False)
            elif format == ConfigFormat.YAML:
                with open(output_path, 'w', encoding='utf-8') as f:
                    yaml.dump(all_configs, f, default_flow_style=False, allow_unicode=True)
            
            logger.info(f"配置已导出: {output_path}")
            
        except Exception as e:
            logger.error(f"导出配置失败: {e}")
            raise
    
    def import_config(self, input_path: str, format: ConfigFormat = ConfigFormat.JSON):
        """导入配置"""
        try:
            input_path = Path(input_path)
            if not input_path.exists():
                raise FileNotFoundError(f"配置文件不存在: {input_path}")
            
            if format == ConfigFormat.JSON:
                with open(input_path, 'r', encoding='utf-8') as f:
                    all_configs = json.load(f)
            elif format == ConfigFormat.YAML:
                with open(input_path, 'r', encoding='utf-8') as f:
                    all_configs = yaml.safe_load(f)
            
            # 更新各个配置
            for config_type, config_data in all_configs.items():
                if config_type in self.config_files:
                    self.update_config(config_type, config_data)
            
            logger.info(f"配置已导入: {input_path}")
            
        except Exception as e:
            logger.error(f"导入配置失败: {e}")
            raise
    
    def validate_all_configs(self) -> Dict[str, List[str]]:
        """验证所有配置"""
        validation_results = {}
        
        for config_type in self.config_files.keys():
            try:
                config_data = asdict(self.configs.get(config_type, {}))
                errors = self._validate_config(config_type, config_data)
                validation_results[config_type] = errors
            except Exception as e:
                validation_results[config_type] = [str(e)]
        
        return validation_results
    
    def get_config_summary(self) -> Dict:
        """获取配置摘要"""
        return {
            'config_dir': str(self.config_dir),
            'config_files': {k: str(v) for k, v in self.config_files.items()},
            'loaded_configs': list(self.configs.keys()),
            'last_updated': datetime.now().isoformat()
        }
    
    def reset_to_defaults(self, config_type: str = None):
        """重置为默认配置"""
        try:
            if config_type:
                # 重置指定配置
                default_config = self._get_default_config(config_type)
                self.update_config(config_type, asdict(default_config))
            else:
                # 重置所有配置
                for config_type in self.config_files.keys():
                    default_config = self._get_default_config(config_type)
                    self.update_config(config_type, asdict(default_config))
            
            logger.info(f"配置已重置为默认值: {config_type or 'all'}")
            
        except Exception as e:
            logger.error(f"重置配置失败: {e}")
            raise
    
    def _load_all_configs(self):
        """加载所有配置"""
        for config_type in self.config_files.keys():
            self._load_config(config_type)
    
    def _load_config(self, config_type: str):
        """加载指定配置"""
        try:
            config_file = self.config_files[config_type]
            
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                # 转换为配置对象
                config_class = self._get_config_class(config_type)
                self.configs[config_type] = config_class(**config_data)
            else:
                # 使用默认配置
                self.configs[config_type] = self._get_default_config(config_type)
                
                # 保存默认配置
                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump(asdict(self.configs[config_type]), f, indent=2, ensure_ascii=False)
            
            logger.debug(f"配置已加载: {config_type}")
            
        except Exception as e:
            logger.error(f"加载配置失败 {config_type}: {e}")
            # 使用默认配置
            self.configs[config_type] = self._get_default_config(config_type)
    
    def _get_config_class(self, config_type: str):
        """获取配置类"""
        config_classes = {
            'model': ModelConfig,
            'data': DataConfig,
            'monitoring': MonitoringConfig,
            'experiment': ExperimentConfig,
            'deployment': DeploymentConfig,
            'system': SystemConfig
        }
        return config_classes.get(config_type)
    
    def _get_default_config(self, config_type: str):
        """获取默认配置"""
        config_class = self._get_config_class(config_type)
        return config_class() if config_class else {}
    
    def _validate_config(self, config_type: str, config_data: Dict) -> List[str]:
        """验证配置"""
        errors = []
        
        try:
            if config_type == 'model':
                if config_data.get('learning_rate', 0) <= 0:
                    errors.append("学习率必须大于0")
                if config_data.get('num_leaves', 0) <= 0:
                    errors.append("叶子节点数必须大于0")
            
            elif config_type == 'data':
                if not (0 < config_data.get('validation_split', 0) < 1):
                    errors.append("验证集比例必须在0-1之间")
                if config_data.get('data_retention_days', 0) <= 0:
                    errors.append("数据保留天数必须大于0")
            
            elif config_type == 'monitoring':
                if config_data.get('monitoring_interval', 0) <= 0:
                    errors.append("监控间隔必须大于0")
                if config_data.get('drift_threshold', 0) <= 0:
                    errors.append("漂移阈值必须大于0")
            
            elif config_type == 'experiment':
                if not (0 < config_data.get('significance_level', 0) < 1):
                    errors.append("显著性水平必须在0-1之间")
                if config_data.get('minimum_sample_size', 0) <= 0:
                    errors.append("最小样本量必须大于0")
            
            elif config_type == 'deployment':
                if not (0 < config_data.get('auto_deploy_threshold', 0) < 1):
                    errors.append("自动部署阈值必须在0-1之间")
                if config_data.get('max_model_versions', 0) <= 0:
                    errors.append("最大模型版本数必须大于0")
            
        except Exception as e:
            errors.append(f"配置验证异常: {e}")
        
        return errors


# 使用示例
if __name__ == "__main__":
    # 创建配置管理器
    config_manager = TrainingConfigManager()
    
    # 获取各种配置
    model_config = config_manager.get_model_config()
    print(f"模型配置: {asdict(model_config)}")
    
    training_config = config_manager.get_training_config()
    print(f"训练配置: {asdict(training_config)}")
    
    alert_rules = config_manager.get_alert_rules()
    print(f"报警规则数量: {len(alert_rules)}")
    
    # 验证配置
    validation_results = config_manager.validate_all_configs()
    print(f"配置验证结果: {validation_results}")
    
    # 导出配置
    config_manager.export_config("all_configs.json")
    
    # 获取配置摘要
    summary = config_manager.get_config_summary()
    print(f"配置摘要: {json.dumps(summary, indent=2, ensure_ascii=False)}")