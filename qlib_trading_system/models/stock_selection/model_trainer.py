"""
模型训练器 - 支持增量学习和在线更新
"""

import os
import json
import pickle
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from pathlib import Path
import joblib
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
import lightgbm as lgb

from .explosive_model import ExplosiveStockModel
from .feature_pipeline import FeaturePipeline
from ..features.feature_engine import FeatureEngine


@dataclass
class TrainingConfig:
    """训练配置"""
    model_name: str
    version: str
    training_data_days: int = 252  # 训练数据天数
    validation_split: float = 0.2
    incremental_batch_size: int = 1000
    max_incremental_batches: int = 10
    performance_threshold: float = 0.6  # 性能阈值
    auto_retrain_threshold: float = 0.05  # 性能下降阈值
    save_intermediate: bool = True
    
    
@dataclass
class TrainingResult:
    """训练结果"""
    model_id: str
    version: str
    timestamp: datetime
    metrics: Dict[str, float]
    training_samples: int
    validation_samples: int
    training_time: float
    model_path: str
    config: Dict[str, Any]
    

class IncrementalLearner:
    """增量学习器"""
    
    def __init__(self, base_model: ExplosiveStockModel, config: TrainingConfig):
        self.base_model = base_model
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 增量学习状态
        self.incremental_buffer = []
        self.buffer_size = config.incremental_batch_size
        self.processed_batches = 0
        
        # 性能跟踪
        self.performance_history = []
        self.last_performance = None
        
    def add_sample(self, features: np.ndarray, label: float, weight: float = 1.0):
        """添加单个样本到增量学习缓冲区"""
        self.incremental_buffer.append({
            'features': features,
            'label': label,
            'weight': weight,
            'timestamp': datetime.now()
        })
        
        # 当缓冲区满时触发增量更新
        if len(self.incremental_buffer) >= self.buffer_size:
            self._process_incremental_batch()
            
    def add_batch(self, features: np.ndarray, labels: np.ndarray, weights: Optional[np.ndarray] = None):
        """批量添加样本"""
        if weights is None:
            weights = np.ones(len(features))
            
        for i in range(len(features)):
            self.add_sample(features[i], labels[i], weights[i])
            
    def _process_incremental_batch(self):
        """处理增量学习批次"""
        if not self.incremental_buffer:
            return
            
        self.logger.info(f"处理增量学习批次，样本数: {len(self.incremental_buffer)}")
        
        # 准备增量数据
        features = np.array([sample['features'] for sample in self.incremental_buffer])
        labels = np.array([sample['label'] for sample in self.incremental_buffer])
        weights = np.array([sample['weight'] for sample in self.incremental_buffer])
        
        # 执行增量更新
        try:
            self.base_model.incremental_fit(features, labels, sample_weight=weights)
            self.processed_batches += 1
            
            # 评估性能
            performance = self._evaluate_incremental_performance(features, labels)
            self.performance_history.append({
                'batch': self.processed_batches,
                'timestamp': datetime.now(),
                'performance': performance,
                'samples': len(features)
            })
            
            self.logger.info(f"增量学习批次 {self.processed_batches} 完成，性能: {performance:.4f}")
            
        except Exception as e:
            self.logger.error(f"增量学习失败: {e}")
            
        finally:
            # 清空缓冲区
            self.incremental_buffer.clear()
            
    def _evaluate_incremental_performance(self, features: np.ndarray, labels: np.ndarray) -> float:
        """评估增量学习性能"""
        try:
            predictions = self.base_model.predict(features)
            # 使用准确率作为性能指标
            binary_predictions = (predictions > 0.5).astype(int)
            binary_labels = (labels > 0.5).astype(int)
            return accuracy_score(binary_labels, binary_predictions)
        except Exception as e:
            self.logger.error(f"性能评估失败: {e}")
            return 0.0
            
    def should_retrain(self) -> bool:
        """判断是否需要重新训练"""
        if len(self.performance_history) < 2:
            return False
            
        # 检查性能下降
        recent_performance = np.mean([h['performance'] for h in self.performance_history[-3:]])
        if self.last_performance is not None:
            performance_drop = self.last_performance - recent_performance
            if performance_drop > self.config.auto_retrain_threshold:
                self.logger.warning(f"性能下降 {performance_drop:.4f}，触发重新训练")
                return True
                
        # 检查批次数量
        if self.processed_batches >= self.config.max_incremental_batches:
            self.logger.info(f"达到最大增量批次数 {self.processed_batches}，触发重新训练")
            return True
            
        return False
        
    def reset(self):
        """重置增量学习状态"""
        self.incremental_buffer.clear()
        self.processed_batches = 0
        self.performance_history.clear()
        self.last_performance = None


class ModelTrainer:
    """模型训练器 - 支持完整训练和增量学习"""
    
    def __init__(self, config: TrainingConfig, model_dir: str = "models/checkpoints"):
        self.config = config
        self.model_dir = Path(model_dir)
        self.model_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger = logging.getLogger(__name__)
        
        # 初始化组件
        self.feature_engine = FeatureEngine()
        self.feature_pipeline = FeaturePipeline()
        
        # 增量学习器
        self.incremental_learner = None
        
        # 训练历史
        self.training_history = []
        
    def train_full_model(self, 
                        train_data: pd.DataFrame, 
                        labels: pd.Series,
                        validation_data: Optional[pd.DataFrame] = None,
                        validation_labels: Optional[pd.Series] = None) -> TrainingResult:
        """完整模型训练"""
        start_time = datetime.now()
        self.logger.info(f"开始完整模型训练: {self.config.model_name} v{self.config.version}")
        
        try:
            # 数据预处理
            processed_features = self._preprocess_training_data(train_data)
            
            # 划分训练验证集
            if validation_data is None:
                train_features, val_features, train_labels, val_labels = self._split_data(
                    processed_features, labels, self.config.validation_split
                )
            else:
                train_features = processed_features
                train_labels = labels
                val_features = self._preprocess_training_data(validation_data)
                val_labels = validation_labels
                
            # 创建模型
            model = ExplosiveStockModel()
            
            # 训练模型
            model.fit(train_features, train_labels)
            
            # 评估模型
            metrics = self._evaluate_model(model, val_features, val_labels)
            
            # 保存模型
            model_path = self._save_model(model, metrics)
            
            # 创建增量学习器
            self.incremental_learner = IncrementalLearner(model, self.config)
            
            # 记录训练结果
            training_time = (datetime.now() - start_time).total_seconds()
            result = TrainingResult(
                model_id=f"{self.config.model_name}_{self.config.version}",
                version=self.config.version,
                timestamp=start_time,
                metrics=metrics,
                training_samples=len(train_features),
                validation_samples=len(val_features),
                training_time=training_time,
                model_path=model_path,
                config=asdict(self.config)
            )
            
            self.training_history.append(result)
            self.logger.info(f"模型训练完成，性能: {metrics}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"模型训练失败: {e}")
            raise
            
    def incremental_update(self, new_data: pd.DataFrame, new_labels: pd.Series):
        """增量更新模型"""
        if self.incremental_learner is None:
            raise ValueError("增量学习器未初始化，请先进行完整训练")
            
        self.logger.info(f"增量更新模型，新样本数: {len(new_data)}")
        
        # 预处理新数据
        processed_features = self._preprocess_training_data(new_data)
        
        # 添加到增量学习器
        self.incremental_learner.add_batch(processed_features, new_labels.values)
        
        # 检查是否需要重新训练
        if self.incremental_learner.should_retrain():
            self.logger.info("触发模型重新训练")
            # 这里可以触发完整重训练的逻辑
            return True
            
        return False
        
    def _preprocess_training_data(self, data: pd.DataFrame) -> np.ndarray:
        """预处理训练数据"""
        try:
            # 使用特征工程管道
            features = self.feature_pipeline.transform(data)
            return features
        except Exception as e:
            self.logger.error(f"数据预处理失败: {e}")
            # 返回简单的数值特征
            numeric_cols = data.select_dtypes(include=[np.number]).columns
            return data[numeric_cols].fillna(0).values
            
    def _split_data(self, features: np.ndarray, labels: pd.Series, validation_split: float) -> Tuple:
        """划分训练验证集"""
        split_idx = int(len(features) * (1 - validation_split))
        
        train_features = features[:split_idx]
        val_features = features[split_idx:]
        train_labels = labels.iloc[:split_idx]
        val_labels = labels.iloc[split_idx:]
        
        return train_features, val_features, train_labels, val_labels
        
    def _evaluate_model(self, model: ExplosiveStockModel, features: np.ndarray, labels: pd.Series) -> Dict[str, float]:
        """评估模型性能"""
        try:
            predictions = model.predict(features)
            probabilities = model.predict_proba(features)
            
            # 转换为二分类
            binary_predictions = (predictions > 0.5).astype(int)
            binary_labels = (labels > 0.5).astype(int)
            
            metrics = {
                'accuracy': accuracy_score(binary_labels, binary_predictions),
                'precision': precision_score(binary_labels, binary_predictions, average='weighted', zero_division=0),
                'recall': recall_score(binary_labels, binary_predictions, average='weighted', zero_division=0),
                'f1_score': f1_score(binary_labels, binary_predictions, average='weighted', zero_division=0),
                'mean_prediction': float(np.mean(predictions)),
                'prediction_std': float(np.std(predictions))
            }
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"模型评估失败: {e}")
            return {'accuracy': 0.0, 'precision': 0.0, 'recall': 0.0, 'f1_score': 0.0}
            
    def _save_model(self, model: ExplosiveStockModel, metrics: Dict[str, float]) -> str:
        """保存模型"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_filename = f"{self.config.model_name}_v{self.config.version}_{timestamp}.pkl"
        model_path = self.model_dir / model_filename
        
        # 保存模型
        with open(model_path, 'wb') as f:
            pickle.dump(model, f)
            
        # 保存元数据
        metadata = {
            'model_name': self.config.model_name,
            'version': self.config.version,
            'timestamp': timestamp,
            'metrics': metrics,
            'config': asdict(self.config)
        }
        
        metadata_path = model_path.with_suffix('.json')
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2, default=str)
            
        self.logger.info(f"模型已保存: {model_path}")
        return str(model_path)
        
    def load_model(self, model_path: str) -> ExplosiveStockModel:
        """加载模型"""
        with open(model_path, 'rb') as f:
            model = pickle.load(f)
        return model
        
    def get_training_history(self) -> List[TrainingResult]:
        """获取训练历史"""
        return self.training_history.copy()
        
    def cleanup_old_models(self, keep_versions: int = 5):
        """清理旧模型文件"""
        model_files = list(self.model_dir.glob(f"{self.config.model_name}_*.pkl"))
        model_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        
        # 保留最新的几个版本
        for old_model in model_files[keep_versions:]:
            try:
                old_model.unlink()
                # 同时删除元数据文件
                metadata_file = old_model.with_suffix('.json')
                if metadata_file.exists():
                    metadata_file.unlink()
                self.logger.info(f"删除旧模型: {old_model}")
            except Exception as e:
                self.logger.error(f"删除模型失败: {e}")


class OnlineTrainer:
    """在线训练器 - 支持实时模型更新"""
    
    def __init__(self, trainer: ModelTrainer, update_interval: int = 3600):
        self.trainer = trainer
        self.update_interval = update_interval  # 更新间隔（秒）
        self.logger = logging.getLogger(__name__)
        
        self.last_update = datetime.now()
        self.pending_samples = []
        
    def add_real_sample(self, features: Dict[str, Any], actual_result: float, prediction: float):
        """添加实际交易样本"""
        sample = {
            'features': features,
            'actual_result': actual_result,
            'prediction': prediction,
            'timestamp': datetime.now(),
            'error': abs(actual_result - prediction)
        }
        
        self.pending_samples.append(sample)
        self.logger.debug(f"添加实际样本，预测: {prediction:.4f}, 实际: {actual_result:.4f}")
        
        # 检查是否需要更新
        if self._should_update():
            self._trigger_online_update()
            
    def _should_update(self) -> bool:
        """判断是否应该进行在线更新"""
        # 时间间隔检查
        time_elapsed = (datetime.now() - self.last_update).total_seconds()
        if time_elapsed < self.update_interval:
            return False
            
        # 样本数量检查
        if len(self.pending_samples) < 100:
            return False
            
        # 错误率检查
        recent_errors = [s['error'] for s in self.pending_samples[-50:]]
        avg_error = np.mean(recent_errors)
        if avg_error > 0.3:  # 错误率过高
            self.logger.warning(f"检测到高错误率: {avg_error:.4f}，触发在线更新")
            return True
            
        return True
        
    def _trigger_online_update(self):
        """触发在线更新"""
        if not self.pending_samples:
            return
            
        self.logger.info(f"开始在线更新，样本数: {len(self.pending_samples)}")
        
        try:
            # 准备更新数据
            features_list = []
            labels_list = []
            
            for sample in self.pending_samples:
                # 这里需要将特征字典转换为数组格式
                feature_array = self._convert_features_to_array(sample['features'])
                features_list.append(feature_array)
                labels_list.append(sample['actual_result'])
                
            if features_list:
                features_array = np.array(features_list)
                labels_array = np.array(labels_list)
                
                # 创建DataFrame用于增量更新
                feature_df = pd.DataFrame(features_array)
                labels_series = pd.Series(labels_array)
                
                # 执行增量更新
                self.trainer.incremental_update(feature_df, labels_series)
                
            # 清空待处理样本
            self.pending_samples.clear()
            self.last_update = datetime.now()
            
            self.logger.info("在线更新完成")
            
        except Exception as e:
            self.logger.error(f"在线更新失败: {e}")
            
    def _convert_features_to_array(self, features_dict: Dict[str, Any]) -> np.ndarray:
        """将特征字典转换为数组"""
        # 这里需要根据实际特征结构进行转换
        # 简化实现，假设特征已经是数值型
        try:
            if isinstance(features_dict, dict):
                return np.array(list(features_dict.values()))
            elif isinstance(features_dict, (list, np.ndarray)):
                return np.array(features_dict)
            else:
                return np.array([features_dict])
        except Exception as e:
            self.logger.error(f"特征转换失败: {e}")
            return np.zeros(10)  # 返回默认特征向量
            
    def get_update_statistics(self) -> Dict[str, Any]:
        """获取更新统计信息"""
        if not self.pending_samples:
            return {'pending_samples': 0, 'avg_error': 0.0}
            
        errors = [s['error'] for s in self.pending_samples]
        return {
            'pending_samples': len(self.pending_samples),
            'avg_error': np.mean(errors),
            'max_error': np.max(errors),
            'min_error': np.min(errors),
            'last_update': self.last_update.isoformat()
        }