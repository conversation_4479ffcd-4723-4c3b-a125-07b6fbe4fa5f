"""
A/B测试框架用于模型对比
实现模型A/B测试、流量分配和效果评估功能
"""

import os
import json
import uuid
import logging
import hashlib
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Callable
from dataclasses import dataclass, asdict
from pathlib import Path
from collections import defaultdict, deque
import numpy as np
import pandas as pd
from enum import Enum
from scipy import stats
import matplotlib.pyplot as plt
import seaborn as sns

from qlib_trading_system.utils.logging.logger import get_logger

logger = get_logger(__name__)


class ExperimentStatus(Enum):
    """实验状态枚举"""
    DRAFT = "draft"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class TrafficSplitMethod(Enum):
    """流量分配方法"""
    RANDOM = "random"
    HASH_BASED = "hash_based"
    WEIGHTED = "weighted"
    STRATIFIED = "stratified"


@dataclass
class ExperimentConfig:
    """实验配置"""
    name: str
    description: str
    start_time: datetime
    end_time: datetime
    
    # 流量分配
    traffic_split_method: TrafficSplitMethod
    traffic_allocation: Dict[str, float]  # 变体名称 -> 流量比例
    
    # 统计设置
    significance_level: float = 0.05
    minimum_sample_size: int = 1000
    power: float = 0.8
    
    # 监控设置
    early_stopping: bool = True
    monitoring_interval: int = 3600  # 秒
    
    # 其他设置
    randomization_unit: str = "user"  # user, session, request
    stratification_features: List[str] = None


@dataclass
class ExperimentVariant:
    """实验变体"""
    name: str
    description: str
    model_version: str
    model_config: Dict
    is_control: bool = False
    expected_improvement: float = 0.0  # 预期改进幅度


@dataclass
class ExperimentResult:
    """实验结果"""
    variant_name: str
    metric_name: str
    sample_size: int
    mean: float
    std: float
    confidence_interval: Tuple[float, float]
    
    def to_dict(self) -> Dict:
        return asdict(self)


@dataclass
class StatisticalTest:
    """统计检验结果"""
    test_name: str
    statistic: float
    p_value: float
    effect_size: float
    confidence_interval: Tuple[float, float]
    is_significant: bool
    power: float
    
    def to_dict(self) -> Dict:
        return asdict(self)


class TrafficSplitter:
    """流量分配器"""
    
    def __init__(self, config: ExperimentConfig):
        self.config = config
        self.variants = list(config.traffic_allocation.keys())
        self.weights = list(config.traffic_allocation.values())
        
        # 验证权重总和
        if abs(sum(self.weights) - 1.0) > 1e-6:
            raise ValueError("流量分配权重总和必须为1.0")
    
    def assign_variant(self, unit_id: str, context: Dict = None) -> str:
        """分配变体"""
        if self.config.traffic_split_method == TrafficSplitMethod.RANDOM:
            return self._random_assignment()
        elif self.config.traffic_split_method == TrafficSplitMethod.HASH_BASED:
            return self._hash_based_assignment(unit_id)
        elif self.config.traffic_split_method == TrafficSplitMethod.WEIGHTED:
            return self._weighted_assignment(unit_id)
        elif self.config.traffic_split_method == TrafficSplitMethod.STRATIFIED:
            return self._stratified_assignment(unit_id, context)
        else:
            raise ValueError(f"不支持的流量分配方法: {self.config.traffic_split_method}")
    
    def _random_assignment(self) -> str:
        """随机分配"""
        return np.random.choice(self.variants, p=self.weights)
    
    def _hash_based_assignment(self, unit_id: str) -> str:
        """基于哈希的分配（确保同一用户总是分配到同一变体）"""
        hash_value = int(hashlib.md5(unit_id.encode()).hexdigest(), 16)
        normalized_hash = (hash_value % 10000) / 10000.0
        
        cumulative_weight = 0
        for i, weight in enumerate(self.weights):
            cumulative_weight += weight
            if normalized_hash <= cumulative_weight:
                return self.variants[i]
        
        return self.variants[-1]  # 兜底
    
    def _weighted_assignment(self, unit_id: str) -> str:
        """加权分配"""
        # 可以根据用户特征调整权重
        return self._hash_based_assignment(unit_id)
    
    def _stratified_assignment(self, unit_id: str, context: Dict) -> str:
        """分层分配"""
        # 根据分层特征进行分配
        if not context or not self.config.stratification_features:
            return self._hash_based_assignment(unit_id)
        
        # 构建分层键
        strata_key = "_".join([
            str(context.get(feature, "unknown"))
            for feature in self.config.stratification_features
        ])
        
        # 基于分层键和用户ID进行哈希分配
        combined_key = f"{unit_id}_{strata_key}"
        return self._hash_based_assignment(combined_key)


class ExperimentTracker:
    """实验跟踪器"""
    
    def __init__(self, experiment_id: str, config: ExperimentConfig):
        self.experiment_id = experiment_id
        self.config = config
        self.variants = {}
        self.traffic_splitter = TrafficSplitter(config)
        
        # 数据存储
        self.assignments = {}  # unit_id -> variant_name
        self.metrics_data = defaultdict(list)  # variant_name -> [metrics]
        self.events_log = deque()
        
        # 统计跟踪
        self.variant_stats = defaultdict(lambda: defaultdict(list))
        
        logger.info(f"实验跟踪器初始化: {experiment_id}")
    
    def add_variant(self, variant: ExperimentVariant):
        """添加实验变体"""
        self.variants[variant.name] = variant
        logger.info(f"添加实验变体: {variant.name}")
    
    def assign_and_track(self, unit_id: str, context: Dict = None) -> str:
        """分配变体并跟踪"""
        # 检查是否已经分配过
        if unit_id in self.assignments:
            return self.assignments[unit_id]
        
        # 分配新变体
        variant_name = self.traffic_splitter.assign_variant(unit_id, context)
        self.assignments[unit_id] = variant_name
        
        # 记录分配事件
        self.events_log.append({
            'timestamp': datetime.now(),
            'event_type': 'assignment',
            'unit_id': unit_id,
            'variant_name': variant_name,
            'context': context
        })
        
        logger.debug(f"用户 {unit_id} 分配到变体: {variant_name}")
        return variant_name
    
    def record_metric(self, unit_id: str, metric_name: str, value: float, 
                     timestamp: datetime = None):
        """记录指标数据"""
        if unit_id not in self.assignments:
            logger.warning(f"用户 {unit_id} 未分配变体，无法记录指标")
            return
        
        variant_name = self.assignments[unit_id]
        
        metric_record = {
            'unit_id': unit_id,
            'variant_name': variant_name,
            'metric_name': metric_name,
            'value': value,
            'timestamp': timestamp or datetime.now()
        }
        
        self.metrics_data[variant_name].append(metric_record)
        self.variant_stats[variant_name][metric_name].append(value)
        
        # 记录指标事件
        self.events_log.append({
            'timestamp': metric_record['timestamp'],
            'event_type': 'metric',
            'unit_id': unit_id,
            'variant_name': variant_name,
            'metric_name': metric_name,
            'value': value
        })
        
        logger.debug(f"记录指标: {unit_id} -> {variant_name} -> {metric_name}={value}")
    
    def get_variant_results(self, variant_name: str, metric_name: str) -> ExperimentResult:
        """获取变体结果"""
        if variant_name not in self.variant_stats:
            raise ValueError(f"变体 {variant_name} 不存在")
        
        if metric_name not in self.variant_stats[variant_name]:
            raise ValueError(f"指标 {metric_name} 不存在")
        
        values = self.variant_stats[variant_name][metric_name]
        
        if not values:
            raise ValueError(f"变体 {variant_name} 的指标 {metric_name} 没有数据")
        
        mean_val = np.mean(values)
        std_val = np.std(values, ddof=1) if len(values) > 1 else 0
        n = len(values)
        
        # 计算置信区间
        if n > 1:
            t_critical = stats.t.ppf(1 - self.config.significance_level/2, n-1)
            margin_error = t_critical * std_val / np.sqrt(n)
            ci = (mean_val - margin_error, mean_val + margin_error)
        else:
            ci = (mean_val, mean_val)
        
        return ExperimentResult(
            variant_name=variant_name,
            metric_name=metric_name,
            sample_size=n,
            mean=mean_val,
            std=std_val,
            confidence_interval=ci
        )
    
    def get_assignment_stats(self) -> Dict:
        """获取分配统计"""
        assignment_counts = defaultdict(int)
        for variant_name in self.assignments.values():
            assignment_counts[variant_name] += 1
        
        total_assignments = len(self.assignments)
        
        stats_result = {
            'total_assignments': total_assignments,
            'variant_counts': dict(assignment_counts),
            'variant_ratios': {
                variant: count / total_assignments if total_assignments > 0 else 0
                for variant, count in assignment_counts.items()
            }
        }
        
        return stats_result


class StatisticalAnalyzer:
    """统计分析器"""
    
    def __init__(self, significance_level: float = 0.05):
        self.significance_level = significance_level
    
    def t_test(self, control_values: List[float], 
               treatment_values: List[float]) -> StatisticalTest:
        """t检验"""
        if len(control_values) < 2 or len(treatment_values) < 2:
            raise ValueError("每组至少需要2个样本")
        
        # 执行独立样本t检验
        statistic, p_value = stats.ttest_ind(treatment_values, control_values)
        
        # 计算效应量（Cohen's d）
        pooled_std = np.sqrt(
            ((len(control_values) - 1) * np.var(control_values, ddof=1) +
             (len(treatment_values) - 1) * np.var(treatment_values, ddof=1)) /
            (len(control_values) + len(treatment_values) - 2)
        )
        
        effect_size = (np.mean(treatment_values) - np.mean(control_values)) / pooled_std
        
        # 计算置信区间
        diff_mean = np.mean(treatment_values) - np.mean(control_values)
        se_diff = pooled_std * np.sqrt(1/len(control_values) + 1/len(treatment_values))
        df = len(control_values) + len(treatment_values) - 2
        t_critical = stats.t.ppf(1 - self.significance_level/2, df)
        
        ci_lower = diff_mean - t_critical * se_diff
        ci_upper = diff_mean + t_critical * se_diff
        
        # 计算统计功效
        power = self._calculate_power(
            effect_size, len(control_values), len(treatment_values)
        )
        
        return StatisticalTest(
            test_name="t_test",
            statistic=statistic,
            p_value=p_value,
            effect_size=effect_size,
            confidence_interval=(ci_lower, ci_upper),
            is_significant=p_value < self.significance_level,
            power=power
        )
    
    def mann_whitney_u_test(self, control_values: List[float], 
                           treatment_values: List[float]) -> StatisticalTest:
        """Mann-Whitney U检验（非参数检验）"""
        statistic, p_value = stats.mannwhitneyu(
            treatment_values, control_values, alternative='two-sided'
        )
        
        # 计算效应量（rank-biserial correlation）
        n1, n2 = len(control_values), len(treatment_values)
        effect_size = 2 * statistic / (n1 * n2) - 1
        
        return StatisticalTest(
            test_name="mann_whitney_u",
            statistic=statistic,
            p_value=p_value,
            effect_size=effect_size,
            confidence_interval=(0, 0),  # 非参数检验不计算置信区间
            is_significant=p_value < self.significance_level,
            power=0.0  # 非参数检验功效计算复杂
        )
    
    def chi_square_test(self, control_counts: Dict, 
                       treatment_counts: Dict) -> StatisticalTest:
        """卡方检验（用于分类指标）"""
        # 构建列联表
        categories = set(control_counts.keys()) | set(treatment_counts.keys())
        
        control_array = [control_counts.get(cat, 0) for cat in categories]
        treatment_array = [treatment_counts.get(cat, 0) for cat in categories]
        
        contingency_table = np.array([control_array, treatment_array])
        
        statistic, p_value, dof, expected = stats.chi2_contingency(contingency_table)
        
        # 计算Cramér's V作为效应量
        n = np.sum(contingency_table)
        effect_size = np.sqrt(statistic / (n * (min(contingency_table.shape) - 1)))
        
        return StatisticalTest(
            test_name="chi_square",
            statistic=statistic,
            p_value=p_value,
            effect_size=effect_size,
            confidence_interval=(0, 0),
            is_significant=p_value < self.significance_level,
            power=0.0
        )
    
    def _calculate_power(self, effect_size: float, n1: int, n2: int) -> float:
        """计算统计功效"""
        try:
            from scipy.stats import norm
            
            # 简化的功效计算
            pooled_n = 2 / (1/n1 + 1/n2)
            ncp = effect_size * np.sqrt(pooled_n / 2)  # 非中心参数
            
            critical_value = norm.ppf(1 - self.significance_level/2)
            power = 1 - norm.cdf(critical_value - ncp) + norm.cdf(-critical_value - ncp)
            
            return max(0, min(1, power))
        except:
            return 0.0


class ABTestingFramework:
    """A/B测试框架"""
    
    def __init__(self, base_dir: str = "experiments"):
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(parents=True, exist_ok=True)
        
        # 实验管理
        self.experiments = {}  # experiment_id -> ExperimentTracker
        self.experiment_configs = {}  # experiment_id -> ExperimentConfig
        
        # 统计分析器
        self.analyzer = StatisticalAnalyzer()
        
        # 加载现有实验
        self._load_experiments()
        
        logger.info(f"A/B测试框架初始化完成，基础目录: {self.base_dir}")
    
    def create_experiment(self, config: ExperimentConfig, 
                         variants: List[ExperimentVariant]) -> str:
        """创建新实验"""
        try:
            # 生成实验ID
            experiment_id = f"exp_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}"
            
            # 验证配置
            self._validate_experiment_config(config, variants)
            
            # 创建实验跟踪器
            tracker = ExperimentTracker(experiment_id, config)
            
            # 添加变体
            for variant in variants:
                tracker.add_variant(variant)
            
            # 保存实验
            self.experiments[experiment_id] = tracker
            self.experiment_configs[experiment_id] = config
            
            # 持久化
            self._save_experiment(experiment_id)
            
            logger.info(f"创建实验成功: {experiment_id}")
            return experiment_id
            
        except Exception as e:
            logger.error(f"创建实验失败: {e}")
            raise
    
    def start_experiment(self, experiment_id: str) -> bool:
        """启动实验"""
        try:
            if experiment_id not in self.experiments:
                raise ValueError(f"实验 {experiment_id} 不存在")
            
            config = self.experiment_configs[experiment_id]
            
            # 检查时间
            now = datetime.now()
            if now < config.start_time:
                raise ValueError(f"实验尚未到开始时间: {config.start_time}")
            
            if now > config.end_time:
                raise ValueError(f"实验已过结束时间: {config.end_time}")
            
            logger.info(f"实验 {experiment_id} 已启动")
            return True
            
        except Exception as e:
            logger.error(f"启动实验失败: {e}")
            return False
    
    def assign_variant(self, experiment_id: str, unit_id: str, 
                      context: Dict = None) -> str:
        """分配实验变体"""
        if experiment_id not in self.experiments:
            raise ValueError(f"实验 {experiment_id} 不存在")
        
        tracker = self.experiments[experiment_id]
        return tracker.assign_and_track(unit_id, context)
    
    def record_metric(self, experiment_id: str, unit_id: str, 
                     metric_name: str, value: float):
        """记录实验指标"""
        if experiment_id not in self.experiments:
            raise ValueError(f"实验 {experiment_id} 不存在")
        
        tracker = self.experiments[experiment_id]
        tracker.record_metric(unit_id, metric_name, value)
    
    def analyze_experiment(self, experiment_id: str, 
                          metric_name: str) -> Dict:
        """分析实验结果"""
        try:
            if experiment_id not in self.experiments:
                raise ValueError(f"实验 {experiment_id} 不存在")
            
            tracker = self.experiments[experiment_id]
            config = self.experiment_configs[experiment_id]
            
            # 获取所有变体的结果
            variant_results = {}
            control_variant = None
            
            for variant_name in tracker.variants.keys():
                try:
                    result = tracker.get_variant_results(variant_name, metric_name)
                    variant_results[variant_name] = result
                    
                    # 找到对照组
                    if tracker.variants[variant_name].is_control:
                        control_variant = variant_name
                except ValueError:
                    continue  # 跳过没有数据的变体
            
            if not variant_results:
                return {'status': 'no_data', 'message': '没有足够的数据进行分析'}
            
            # 统计检验
            statistical_tests = {}
            
            if control_variant and control_variant in variant_results:
                control_values = tracker.variant_stats[control_variant][metric_name]
                
                for variant_name, result in variant_results.items():
                    if variant_name == control_variant:
                        continue
                    
                    treatment_values = tracker.variant_stats[variant_name][metric_name]
                    
                    # 执行t检验
                    try:
                        test_result = self.analyzer.t_test(control_values, treatment_values)
                        statistical_tests[f"{control_variant}_vs_{variant_name}"] = test_result
                    except ValueError as e:
                        logger.warning(f"统计检验失败: {e}")
            
            # 生成分析报告
            analysis_result = {
                'experiment_id': experiment_id,
                'metric_name': metric_name,
                'analysis_time': datetime.now().isoformat(),
                'variant_results': {name: result.to_dict() for name, result in variant_results.items()},
                'statistical_tests': {name: test.to_dict() for name, test in statistical_tests.items()},
                'assignment_stats': tracker.get_assignment_stats(),
                'recommendations': self._generate_recommendations(variant_results, statistical_tests)
            }
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"分析实验失败: {e}")
            return {'status': 'error', 'message': str(e)}
    
    def stop_experiment(self, experiment_id: str, reason: str = "") -> bool:
        """停止实验"""
        try:
            if experiment_id not in self.experiments:
                raise ValueError(f"实验 {experiment_id} 不存在")
            
            # 记录停止事件
            tracker = self.experiments[experiment_id]
            tracker.events_log.append({
                'timestamp': datetime.now(),
                'event_type': 'stop',
                'reason': reason
            })
            
            # 保存最终状态
            self._save_experiment(experiment_id)
            
            logger.info(f"实验 {experiment_id} 已停止，原因: {reason}")
            return True
            
        except Exception as e:
            logger.error(f"停止实验失败: {e}")
            return False
    
    def get_experiment_status(self, experiment_id: str) -> Dict:
        """获取实验状态"""
        if experiment_id not in self.experiments:
            return {'status': 'not_found'}
        
        tracker = self.experiments[experiment_id]
        config = self.experiment_configs[experiment_id]
        
        now = datetime.now()
        
        # 确定实验状态
        if now < config.start_time:
            status = ExperimentStatus.DRAFT
        elif now > config.end_time:
            status = ExperimentStatus.COMPLETED
        else:
            status = ExperimentStatus.RUNNING
        
        return {
            'experiment_id': experiment_id,
            'status': status.value,
            'config': asdict(config),
            'assignment_stats': tracker.get_assignment_stats(),
            'total_events': len(tracker.events_log),
            'variants': {name: asdict(variant) for name, variant in tracker.variants.items()}
        }
    
    def list_experiments(self) -> List[Dict]:
        """列出所有实验"""
        experiments_list = []
        
        for experiment_id in self.experiments.keys():
            status = self.get_experiment_status(experiment_id)
            experiments_list.append(status)
        
        return experiments_list
    
    def export_experiment_data(self, experiment_id: str, 
                              output_path: str) -> bool:
        """导出实验数据"""
        try:
            if experiment_id not in self.experiments:
                raise ValueError(f"实验 {experiment_id} 不存在")
            
            tracker = self.experiments[experiment_id]
            
            # 准备导出数据
            export_data = {
                'experiment_id': experiment_id,
                'config': asdict(self.experiment_configs[experiment_id]),
                'variants': {name: asdict(variant) for name, variant in tracker.variants.items()},
                'assignments': tracker.assignments,
                'metrics_data': {variant: data for variant, data in tracker.metrics_data.items()},
                'events_log': list(tracker.events_log),
                'export_time': datetime.now().isoformat()
            }
            
            # 保存到文件
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False, default=str)
            
            logger.info(f"实验数据已导出: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"导出实验数据失败: {e}")
            return False
    
    def create_experiment_report(self, experiment_id: str, 
                               output_path: str) -> bool:
        """创建实验报告"""
        try:
            if experiment_id not in self.experiments:
                raise ValueError(f"实验 {experiment_id} 不存在")
            
            # 分析所有指标
            tracker = self.experiments[experiment_id]
            all_metrics = set()
            
            for variant_data in tracker.metrics_data.values():
                for record in variant_data:
                    all_metrics.add(record['metric_name'])
            
            # 生成报告
            report = {
                'experiment_id': experiment_id,
                'report_time': datetime.now().isoformat(),
                'experiment_status': self.get_experiment_status(experiment_id),
                'metric_analyses': {}
            }
            
            for metric_name in all_metrics:
                analysis = self.analyze_experiment(experiment_id, metric_name)
                if analysis.get('status') != 'no_data':
                    report['metric_analyses'][metric_name] = analysis
            
            # 保存报告
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False, default=str)
            
            # 生成可视化图表
            self._create_experiment_charts(experiment_id, output_path.parent)
            
            logger.info(f"实验报告已生成: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"生成实验报告失败: {e}")
            return False
    
    def _validate_experiment_config(self, config: ExperimentConfig, 
                                  variants: List[ExperimentVariant]):
        """验证实验配置"""
        # 检查时间
        if config.start_time >= config.end_time:
            raise ValueError("开始时间必须早于结束时间")
        
        # 检查流量分配
        variant_names = [v.name for v in variants]
        config_variants = set(config.traffic_allocation.keys())
        actual_variants = set(variant_names)
        
        if config_variants != actual_variants:
            raise ValueError("流量分配配置与实际变体不匹配")
        
        # 检查对照组
        control_count = sum(1 for v in variants if v.is_control)
        if control_count != 1:
            raise ValueError("必须有且仅有一个对照组")
    
    def _generate_recommendations(self, variant_results: Dict, 
                                statistical_tests: Dict) -> List[str]:
        """生成建议"""
        recommendations = []
        
        # 检查样本量
        for variant_name, result in variant_results.items():
            if result.sample_size < 1000:
                recommendations.append(f"变体 {variant_name} 样本量较小 ({result.sample_size})，建议增加样本量")
        
        # 检查显著性
        significant_tests = [name for name, test in statistical_tests.items() if test.is_significant]
        
        if significant_tests:
            recommendations.append(f"发现显著差异的对比: {', '.join(significant_tests)}")
        else:
            recommendations.append("未发现显著差异，建议延长实验时间或增加样本量")
        
        # 检查效应量
        for test_name, test in statistical_tests.items():
            if abs(test.effect_size) > 0.8:
                recommendations.append(f"{test_name} 具有大效应量 ({test.effect_size:.3f})")
            elif abs(test.effect_size) > 0.5:
                recommendations.append(f"{test_name} 具有中等效应量 ({test.effect_size:.3f})")
        
        return recommendations
    
    def _save_experiment(self, experiment_id: str):
        """保存实验数据"""
        try:
            experiment_dir = self.base_dir / experiment_id
            experiment_dir.mkdir(exist_ok=True)
            
            # 保存配置
            config_file = experiment_dir / "config.json"
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(asdict(self.experiment_configs[experiment_id]), f, 
                         indent=2, ensure_ascii=False, default=str)
            
            # 保存数据
            tracker = self.experiments[experiment_id]
            data_file = experiment_dir / "data.json"
            
            data = {
                'assignments': tracker.assignments,
                'metrics_data': {variant: data for variant, data in tracker.metrics_data.items()},
                'events_log': list(tracker.events_log),
                'variants': {name: asdict(variant) for name, variant in tracker.variants.items()}
            }
            
            with open(data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False, default=str)
                
        except Exception as e:
            logger.error(f"保存实验数据失败: {e}")
    
    def _load_experiments(self):
        """加载现有实验"""
        try:
            if not self.base_dir.exists():
                return
            
            for experiment_dir in self.base_dir.iterdir():
                if not experiment_dir.is_dir():
                    continue
                
                experiment_id = experiment_dir.name
                config_file = experiment_dir / "config.json"
                data_file = experiment_dir / "data.json"
                
                if not (config_file.exists() and data_file.exists()):
                    continue
                
                try:
                    # 加载配置
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config_data = json.load(f)
                    
                    # 处理datetime字段
                    config_data['start_time'] = datetime.fromisoformat(config_data['start_time'])
                    config_data['end_time'] = datetime.fromisoformat(config_data['end_time'])
                    config_data['traffic_split_method'] = TrafficSplitMethod(config_data['traffic_split_method'])
                    
                    config = ExperimentConfig(**config_data)
                    
                    # 加载数据
                    with open(data_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    # 重建实验跟踪器
                    tracker = ExperimentTracker(experiment_id, config)
                    
                    # 恢复变体
                    for variant_name, variant_data in data['variants'].items():
                        variant = ExperimentVariant(**variant_data)
                        tracker.add_variant(variant)
                    
                    # 恢复分配
                    tracker.assignments = data['assignments']
                    
                    # 恢复指标数据
                    for variant_name, metrics_list in data['metrics_data'].items():
                        tracker.metrics_data[variant_name] = metrics_list
                        
                        # 重建统计数据
                        for metric_record in metrics_list:
                            metric_name = metric_record['metric_name']
                            value = metric_record['value']
                            tracker.variant_stats[variant_name][metric_name].append(value)
                    
                    # 恢复事件日志
                    tracker.events_log = deque(data['events_log'])
                    
                    # 注册实验
                    self.experiments[experiment_id] = tracker
                    self.experiment_configs[experiment_id] = config
                    
                    logger.info(f"加载实验: {experiment_id}")
                    
                except Exception as e:
                    logger.error(f"加载实验 {experiment_id} 失败: {e}")
                    
        except Exception as e:
            logger.error(f"加载实验失败: {e}")
    
    def _create_experiment_charts(self, experiment_id: str, output_dir: Path):
        """创建实验图表"""
        try:
            tracker = self.experiments[experiment_id]
            
            # 创建图表目录
            charts_dir = output_dir / "charts"
            charts_dir.mkdir(exist_ok=True)
            
            # 获取所有指标
            all_metrics = set()
            for variant_data in tracker.metrics_data.values():
                for record in variant_data:
                    all_metrics.add(record['metric_name'])
            
            # 为每个指标创建对比图
            for metric_name in all_metrics:
                try:
                    self._create_metric_comparison_chart(
                        tracker, metric_name, charts_dir / f"{metric_name}_comparison.png"
                    )
                except Exception as e:
                    logger.error(f"创建指标 {metric_name} 图表失败: {e}")
            
            # 创建流量分配图
            self._create_traffic_allocation_chart(
                tracker, charts_dir / "traffic_allocation.png"
            )
            
        except Exception as e:
            logger.error(f"创建实验图表失败: {e}")
    
    def _create_metric_comparison_chart(self, tracker: ExperimentTracker, 
                                      metric_name: str, output_path: Path):
        """创建指标对比图"""
        plt.figure(figsize=(12, 8))
        
        # 收集数据
        variant_data = {}
        for variant_name in tracker.variants.keys():
            if metric_name in tracker.variant_stats[variant_name]:
                values = tracker.variant_stats[variant_name][metric_name]
                if values:
                    variant_data[variant_name] = values
        
        if not variant_data:
            plt.text(0.5, 0.5, 'No Data', ha='center', va='center', transform=plt.gca().transAxes)
            plt.title(f'{metric_name} Comparison')
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            plt.close()
            return
        
        # 创建箱线图
        plt.subplot(2, 2, 1)
        data_for_boxplot = [values for values in variant_data.values()]
        labels = list(variant_data.keys())
        plt.boxplot(data_for_boxplot, labels=labels)
        plt.title(f'{metric_name} Distribution')
        plt.xticks(rotation=45)
        
        # 创建均值对比图
        plt.subplot(2, 2, 2)
        means = [np.mean(values) for values in variant_data.values()]
        stds = [np.std(values) for values in variant_data.values()]
        plt.bar(labels, means, yerr=stds, capsize=5, alpha=0.7)
        plt.title(f'{metric_name} Mean Comparison')
        plt.xticks(rotation=45)
        
        # 创建时间序列图
        plt.subplot(2, 1, 2)
        for variant_name, values in variant_data.items():
            # 简化的时间序列（使用索引作为时间）
            plt.plot(range(len(values)), values, label=variant_name, alpha=0.7)
        
        plt.title(f'{metric_name} Time Series')
        plt.xlabel('Sample Index')
        plt.ylabel(metric_name)
        plt.legend()
        
        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
    
    def _create_traffic_allocation_chart(self, tracker: ExperimentTracker, 
                                       output_path: Path):
        """创建流量分配图"""
        assignment_stats = tracker.get_assignment_stats()
        
        if not assignment_stats['variant_counts']:
            return
        
        plt.figure(figsize=(10, 6))
        
        # 饼图
        plt.subplot(1, 2, 1)
        plt.pie(assignment_stats['variant_counts'].values(), 
                labels=assignment_stats['variant_counts'].keys(),
                autopct='%1.1f%%')
        plt.title('Traffic Allocation')
        
        # 柱状图
        plt.subplot(1, 2, 2)
        variants = list(assignment_stats['variant_counts'].keys())
        counts = list(assignment_stats['variant_counts'].values())
        plt.bar(variants, counts, alpha=0.7)
        plt.title('Assignment Counts')
        plt.xlabel('Variant')
        plt.ylabel('Count')
        plt.xticks(rotation=45)
        
        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()


# 使用示例
if __name__ == "__main__":
    # 创建A/B测试框架
    ab_framework = ABTestingFramework()
    
    # 创建实验配置
    config = ExperimentConfig(
        name="股票筛选模型对比实验",
        description="对比新旧股票筛选模型的性能",
        start_time=datetime.now(),
        end_time=datetime.now() + timedelta(days=7),
        traffic_split_method=TrafficSplitMethod.HASH_BASED,
        traffic_allocation={
            "control": 0.5,
            "treatment": 0.5
        },
        significance_level=0.05,
        minimum_sample_size=1000
    )
    
    # 创建实验变体
    variants = [
        ExperimentVariant(
            name="control",
            description="当前生产模型",
            model_version="v1.0",
            model_config={"model_type": "lightgbm_v1"},
            is_control=True
        ),
        ExperimentVariant(
            name="treatment",
            description="新优化模型",
            model_version="v2.0",
            model_config={"model_type": "lightgbm_v2"},
            is_control=False,
            expected_improvement=0.05
        )
    ]
    
    # 创建实验
    experiment_id = ab_framework.create_experiment(config, variants)
    print(f"创建实验: {experiment_id}")
    
    # 启动实验
    success = ab_framework.start_experiment(experiment_id)
    print(f"启动实验: {success}")
    
    # 模拟用户分配和指标记录
    import random
    
    for i in range(2000):
        user_id = f"user_{i}"
        variant = ab_framework.assign_variant(experiment_id, user_id)
        
        # 模拟不同变体的性能差异
        if variant == "control":
            accuracy = random.gauss(0.75, 0.1)
            profit = random.gauss(0.08, 0.05)
        else:  # treatment
            accuracy = random.gauss(0.78, 0.1)  # 稍好的性能
            profit = random.gauss(0.10, 0.05)
        
        ab_framework.record_metric(experiment_id, user_id, "accuracy", accuracy)
        ab_framework.record_metric(experiment_id, user_id, "profit", profit)
    
    # 分析实验结果
    analysis = ab_framework.analyze_experiment(experiment_id, "accuracy")
    print(f"准确率分析结果: {json.dumps(analysis, indent=2, default=str)}")
    
    # 生成实验报告
    report_path = f"experiment_report_{experiment_id}.json"
    ab_framework.create_experiment_report(experiment_id, report_path)
    print(f"实验报告已生成: {report_path}")
    
    # 获取实验状态
    status = ab_framework.get_experiment_status(experiment_id)
    print(f"实验状态: {json.dumps(status, indent=2, default=str)}")