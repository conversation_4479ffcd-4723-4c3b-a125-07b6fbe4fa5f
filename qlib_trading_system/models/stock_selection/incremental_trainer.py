"""
增量学习和在线更新机制
实现模型的增量训练和在线学习功能
"""

import os
import pickle
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from pathlib import Path
import joblib
import lightgbm as lgb
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score

from qlib_trading_system.utils.logging.logger import get_logger
from qlib_trading_system.models.features.feature_engine import FeatureEngine

logger = get_logger(__name__)


@dataclass
class IncrementalConfig:
    """增量学习配置"""
    # 增量学习参数
    batch_size: int = 1000  # 批次大小
    learning_rate_decay: float = 0.95  # 学习率衰减
    min_samples_for_update: int = 100  # 最小更新样本数
    max_memory_samples: int = 10000  # 最大内存样本数
    
    # 模型更新策略
    update_frequency: str = 'daily'  # 更新频率: daily, weekly, monthly
    performance_threshold: float = 0.05  # 性能下降阈值
    
    # 数据管理
    data_retention_days: int = 365  # 数据保留天数
    validation_split: float = 0.2  # 验证集比例
    
    # 模型参数
    n_estimators_increment: int = 50  # 每次增量的树数量
    max_total_estimators: int = 1000  # 最大总树数量


class IncrementalDataBuffer:
    """增量数据缓冲区"""
    
    def __init__(self, max_size: int = 10000):
        self.max_size = max_size
        self.features_buffer = []
        self.labels_buffer = []
        self.timestamps_buffer = []
        self.weights_buffer = []
        
    def add_sample(self, features: np.ndarray, label: float, 
                   timestamp: datetime, weight: float = 1.0):
        """添加新样本"""
        self.features_buffer.append(features)
        self.labels_buffer.append(label)
        self.timestamps_buffer.append(timestamp)
        self.weights_buffer.append(weight)
        
        # 如果超过最大大小，移除最旧的样本
        if len(self.features_buffer) > self.max_size:
            self.features_buffer.pop(0)
            self.labels_buffer.pop(0)
            self.timestamps_buffer.pop(0)
            self.weights_buffer.pop(0)
    
    def get_recent_samples(self, n_samples: int = None) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """获取最近的样本"""
        if n_samples is None:
            n_samples = len(self.features_buffer)
        
        n_samples = min(n_samples, len(self.features_buffer))
        if n_samples == 0:
            return np.array([]), np.array([]), np.array([])
        
        features = np.array(self.features_buffer[-n_samples:])
        labels = np.array(self.labels_buffer[-n_samples:])
        weights = np.array(self.weights_buffer[-n_samples:])
        
        return features, labels, weights
    
    def clear(self):
        """清空缓冲区"""
        self.features_buffer.clear()
        self.labels_buffer.clear()
        self.timestamps_buffer.clear()
        self.weights_buffer.clear()
    
    def size(self) -> int:
        """获取缓冲区大小"""
        return len(self.features_buffer)


class IncrementalTrainer:
    """增量学习训练器"""
    
    def __init__(self, config: IncrementalConfig, model_dir: str = "models/incremental"):
        self.config = config
        self.model_dir = Path(model_dir)
        self.model_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化组件
        self.feature_engine = FeatureEngine()
        self.data_buffer = IncrementalDataBuffer(config.max_memory_samples)
        
        # 模型相关
        self.base_model = None
        self.current_model = None
        self.model_history = []
        
        # 性能跟踪
        self.performance_history = []
        self.last_update_time = None
        
        # 学习率管理
        self.current_learning_rate = 0.1
        self.update_count = 0
        
        logger.info(f"增量学习训练器初始化完成，模型目录: {self.model_dir}")
    
    def initialize_base_model(self, X_train: np.ndarray, y_train: np.ndarray, 
                            X_val: np.ndarray = None, y_val: np.ndarray = None) -> Dict:
        """初始化基础模型"""
        try:
            logger.info("开始初始化基础模型...")
            
            # LightGBM参数
            params = {
                'objective': 'regression',
                'metric': 'rmse',
                'boosting_type': 'gbdt',
                'num_leaves': 31,
                'learning_rate': self.current_learning_rate,
                'feature_fraction': 0.9,
                'bagging_fraction': 0.8,
                'bagging_freq': 5,
                'verbose': -1,
                'random_state': 42
            }
            
            # 创建数据集
            train_data = lgb.Dataset(X_train, label=y_train)
            valid_data = None
            if X_val is not None and y_val is not None:
                valid_data = lgb.Dataset(X_val, label=y_val, reference=train_data)
            
            # 训练基础模型
            self.base_model = lgb.train(
                params,
                train_data,
                num_boost_round=200,
                valid_sets=[valid_data] if valid_data else None,
                callbacks=[lgb.early_stopping(50)] if valid_data else None,
                verbose_eval=False
            )
            
            self.current_model = self.base_model
            
            # 评估初始性能
            train_pred = self.base_model.predict(X_train)
            train_score = self._calculate_score(y_train, train_pred)
            
            val_score = None
            if X_val is not None:
                val_pred = self.base_model.predict(X_val)
                val_score = self._calculate_score(y_val, val_pred)
            
            # 保存基础模型
            self._save_model(self.base_model, "base_model")
            
            result = {
                'train_score': train_score,
                'val_score': val_score,
                'model_path': self.model_dir / "base_model.pkl"
            }
            
            logger.info(f"基础模型初始化完成: 训练分数={train_score:.4f}, 验证分数={val_score}")
            return result
            
        except Exception as e:
            logger.error(f"基础模型初始化失败: {e}")
            raise
    
    def add_training_data(self, features: np.ndarray, labels: np.ndarray, 
                         timestamps: List[datetime] = None, weights: np.ndarray = None):
        """添加新的训练数据到缓冲区"""
        try:
            if timestamps is None:
                timestamps = [datetime.now()] * len(features)
            if weights is None:
                weights = np.ones(len(features))
            
            # 逐个添加样本到缓冲区
            for i in range(len(features)):
                self.data_buffer.add_sample(
                    features[i], labels[i], timestamps[i], weights[i]
                )
            
            logger.info(f"添加了 {len(features)} 个训练样本到缓冲区，当前缓冲区大小: {self.data_buffer.size()}")
            
            # 检查是否需要触发增量更新
            if self._should_trigger_update():
                self.perform_incremental_update()
                
        except Exception as e:
            logger.error(f"添加训练数据失败: {e}")
            raise
    
    def perform_incremental_update(self) -> Dict:
        """执行增量更新"""
        try:
            if self.current_model is None:
                raise ValueError("需要先初始化基础模型")
            
            logger.info("开始执行增量更新...")
            
            # 获取缓冲区数据
            X_new, y_new, weights_new = self.data_buffer.get_recent_samples()
            
            if len(X_new) < self.config.min_samples_for_update:
                logger.warning(f"样本数量不足，需要至少 {self.config.min_samples_for_update} 个样本")
                return {'status': 'skipped', 'reason': 'insufficient_samples'}
            
            # 分割训练和验证数据
            split_idx = int(len(X_new) * (1 - self.config.validation_split))
            X_train, X_val = X_new[:split_idx], X_new[split_idx:]
            y_train, y_val = y_new[:split_idx], y_new[split_idx:]
            w_train, w_val = weights_new[:split_idx], weights_new[split_idx:]
            
            # 更新学习率
            self.current_learning_rate *= self.config.learning_rate_decay
            
            # 增量训练参数
            params = {
                'objective': 'regression',
                'metric': 'rmse',
                'boosting_type': 'gbdt',
                'num_leaves': 31,
                'learning_rate': self.current_learning_rate,
                'feature_fraction': 0.9,
                'bagging_fraction': 0.8,
                'bagging_freq': 5,
                'verbose': -1,
                'random_state': 42
            }
            
            # 创建增量数据集
            train_data = lgb.Dataset(X_train, label=y_train, weight=w_train)
            valid_data = lgb.Dataset(X_val, label=y_val, weight=w_val, reference=train_data)
            
            # 基于当前模型进行增量训练
            updated_model = lgb.train(
                params,
                train_data,
                num_boost_round=self.config.n_estimators_increment,
                init_model=self.current_model,
                valid_sets=[valid_data],
                callbacks=[lgb.early_stopping(20)],
                verbose_eval=False
            )
            
            # 评估更新后的模型
            val_pred_old = self.current_model.predict(X_val)
            val_pred_new = updated_model.predict(X_val)
            
            old_score = self._calculate_score(y_val, val_pred_old)
            new_score = self._calculate_score(y_val, val_pred_new)
            
            # 决定是否接受更新
            improvement = new_score - old_score
            if improvement > -self.config.performance_threshold:
                # 接受更新
                self.current_model = updated_model
                self.update_count += 1
                self.last_update_time = datetime.now()
                
                # 保存更新后的模型
                model_name = f"incremental_model_{self.update_count}"
                self._save_model(updated_model, model_name)
                
                # 记录性能历史
                self.performance_history.append({
                    'timestamp': self.last_update_time,
                    'old_score': old_score,
                    'new_score': new_score,
                    'improvement': improvement,
                    'samples_used': len(X_new),
                    'learning_rate': self.current_learning_rate
                })
                
                # 清空部分缓冲区（保留一些历史数据）
                self.data_buffer.clear()
                
                logger.info(f"增量更新成功: 性能提升={improvement:.4f}, 新分数={new_score:.4f}")
                
                return {
                    'status': 'success',
                    'old_score': old_score,
                    'new_score': new_score,
                    'improvement': improvement,
                    'model_path': self.model_dir / f"{model_name}.pkl"
                }
            else:
                logger.warning(f"增量更新被拒绝: 性能下降={improvement:.4f}")
                return {
                    'status': 'rejected',
                    'old_score': old_score,
                    'new_score': new_score,
                    'improvement': improvement,
                    'reason': 'performance_degradation'
                }
                
        except Exception as e:
            logger.error(f"增量更新失败: {e}")
            raise
    
    def online_predict_and_learn(self, features: np.ndarray, 
                               true_labels: np.ndarray = None) -> np.ndarray:
        """在线预测并学习"""
        try:
            if self.current_model is None:
                raise ValueError("模型未初始化")
            
            # 进行预测
            predictions = self.current_model.predict(features)
            
            # 如果提供了真实标签，添加到学习缓冲区
            if true_labels is not None:
                timestamps = [datetime.now()] * len(features)
                self.add_training_data(features, true_labels, timestamps)
            
            return predictions
            
        except Exception as e:
            logger.error(f"在线预测和学习失败: {e}")
            raise
    
    def _should_trigger_update(self) -> bool:
        """判断是否应该触发增量更新"""
        # 检查缓冲区大小
        if self.data_buffer.size() < self.config.min_samples_for_update:
            return False
        
        # 检查时间间隔
        if self.last_update_time is not None:
            time_since_update = datetime.now() - self.last_update_time
            
            if self.config.update_frequency == 'daily':
                return time_since_update >= timedelta(days=1)
            elif self.config.update_frequency == 'weekly':
                return time_since_update >= timedelta(weeks=1)
            elif self.config.update_frequency == 'monthly':
                return time_since_update >= timedelta(days=30)
        
        return True
    
    def _calculate_score(self, y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """计算模型评分"""
        # 使用负均方误差作为评分（越大越好）
        mse = np.mean((y_true - y_pred) ** 2)
        return -mse
    
    def _save_model(self, model, name: str):
        """保存模型"""
        model_path = self.model_dir / f"{name}.pkl"
        joblib.dump(model, model_path)
        logger.info(f"模型已保存: {model_path}")
    
    def load_model(self, model_name: str):
        """加载模型"""
        model_path = self.model_dir / f"{model_name}.pkl"
        if model_path.exists():
            self.current_model = joblib.load(model_path)
            logger.info(f"模型已加载: {model_path}")
        else:
            raise FileNotFoundError(f"模型文件不存在: {model_path}")
    
    def get_performance_history(self) -> List[Dict]:
        """获取性能历史"""
        return self.performance_history.copy()
    
    def get_model_info(self) -> Dict:
        """获取模型信息"""
        return {
            'update_count': self.update_count,
            'last_update_time': self.last_update_time,
            'current_learning_rate': self.current_learning_rate,
            'buffer_size': self.data_buffer.size(),
            'model_exists': self.current_model is not None
        }


class OnlineLearningScheduler:
    """在线学习调度器"""
    
    def __init__(self, trainer: IncrementalTrainer):
        self.trainer = trainer
        self.is_running = False
        self.schedule_config = {
            'market_open': '09:30',
            'market_close': '15:00',
            'update_intervals': [
                '10:00', '11:00', '13:30', '14:30'  # 盘中更新时间点
            ]
        }
    
    def start_online_learning(self):
        """启动在线学习"""
        self.is_running = True
        logger.info("在线学习调度器已启动")
    
    def stop_online_learning(self):
        """停止在线学习"""
        self.is_running = False
        logger.info("在线学习调度器已停止")
    
    def should_update_now(self) -> bool:
        """判断当前是否应该更新"""
        if not self.is_running:
            return False
        
        current_time = datetime.now().strftime('%H:%M')
        return current_time in self.schedule_config['update_intervals']
    
    def process_market_data(self, market_data: Dict):
        """处理市场数据并触发学习"""
        if not self.is_running:
            return
        
        try:
            # 提取特征和标签
            features = market_data.get('features')
            labels = market_data.get('labels')
            
            if features is not None and labels is not None:
                # 添加到训练缓冲区
                self.trainer.add_training_data(features, labels)
                
                # 检查是否需要立即更新
                if self.should_update_now():
                    self.trainer.perform_incremental_update()
                    
        except Exception as e:
            logger.error(f"处理市场数据失败: {e}")


# 使用示例
if __name__ == "__main__":
    # 配置增量学习
    config = IncrementalConfig(
        batch_size=500,
        learning_rate_decay=0.98,
        min_samples_for_update=200,
        update_frequency='daily'
    )
    
    # 创建增量训练器
    trainer = IncrementalTrainer(config)
    
    # 模拟初始化基础模型
    np.random.seed(42)
    X_init = np.random.randn(1000, 10)
    y_init = np.random.randn(1000)
    X_val = np.random.randn(200, 10)
    y_val = np.random.randn(200)
    
    # 初始化基础模型
    result = trainer.initialize_base_model(X_init, y_init, X_val, y_val)
    print(f"基础模型初始化结果: {result}")
    
    # 模拟增量数据
    for i in range(5):
        X_new = np.random.randn(100, 10)
        y_new = np.random.randn(100)
        trainer.add_training_data(X_new, y_new)
        
        # 手动触发更新
        if trainer.data_buffer.size() >= config.min_samples_for_update:
            update_result = trainer.perform_incremental_update()
            print(f"增量更新 {i+1} 结果: {update_result}")
    
    # 查看性能历史
    history = trainer.get_performance_history()
    print(f"性能历史: {history}")
    
    # 查看模型信息
    info = trainer.get_model_info()
    print(f"模型信息: {info}")