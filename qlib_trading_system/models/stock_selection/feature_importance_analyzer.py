"""
特征重要性分析工具

提供多种特征重要性分析方法
为特征选择和模型解释提供支持
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.ensemble import RandomForestRegressor, ExtraTreesRegressor
from sklearn.linear_model import LassoCV, ElasticNetCV
from sklearn.inspection import permutation_importance
from sklearn.model_selection import cross_val_score
import shap
import logging
from dataclasses import dataclass
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)


@dataclass
class ImportanceResult:
    """重要性分析结果"""
    feature_name: str
    importance_score: float
    rank: int
    category: str
    method: str
    confidence_interval: Optional[Tuple[float, float]] = None
    p_value: Optional[float] = None


@dataclass
class ImportanceAnalysisReport:
    """重要性分析报告"""
    analysis_date: datetime
    method: str
    total_features: int
    top_features: List[ImportanceResult]
    category_summary: Dict[str, Dict]
    stability_score: float
    analysis_time: float


class FeatureImportanceAnalyzer:
    """特征重要性分析器"""
    
    def __init__(self):
        self.importance_history = []
        self.category_mapping = self._create_category_mapping()
    
    def _create_category_mapping(self) -> Dict[str, str]:
        """创建特征类别映射"""
        return {
            'fundamental': '基本面',
            'valuation': '估值',
            'technical': '技术面',
            'sentiment': '情绪',
            'risk': '风险',
            'market': '大盘',
            'overall': '综合',
            'explosive': '爆发力'
        }
    
    def analyze_tree_importance(self, X: pd.DataFrame, y: pd.Series,
                              model_type: str = 'random_forest',
                              n_estimators: int = 200) -> List[ImportanceResult]:
        """基于树模型的特征重要性分析"""
        logger.info(f"开始树模型重要性分析，模型: {model_type}")
        
        # 选择模型
        if model_type == 'random_forest':
            model = RandomForestRegressor(
                n_estimators=n_estimators,
                random_state=42,
                n_jobs=-1
            )
        elif model_type == 'extra_trees':
            model = ExtraTreesRegressor(
                n_estimators=n_estimators,
                random_state=42,
                n_jobs=-1
            )
        else:
            raise ValueError(f"不支持的模型类型: {model_type}")
        
        # 训练模型
        model.fit(X, y)
        
        # 获取特征重要性
        importances = model.feature_importances_
        
        # 计算标准差（如果是随机森林）
        if hasattr(model, 'estimators_'):
            std = np.std([tree.feature_importances_ for tree in model.estimators_], axis=0)
        else:
            std = np.zeros_like(importances)
        
        # 创建重要性结果
        results = []
        for i, (feature, importance, std_val) in enumerate(zip(X.columns, importances, std)):
            category = self._get_feature_category(feature)
            results.append(ImportanceResult(
                feature_name=feature,
                importance_score=importance,
                rank=i + 1,
                category=category,
                method=f'tree_{model_type}',
                confidence_interval=(importance - std_val, importance + std_val)
            ))
        
        # 按重要性排序
        results.sort(key=lambda x: x.importance_score, reverse=True)
        
        # 更新排名
        for i, result in enumerate(results):
            result.rank = i + 1
        
        logger.info(f"树模型重要性分析完成，分析了 {len(results)} 个特征")
        return results
    
    def analyze_permutation_importance(self, X: pd.DataFrame, y: pd.Series,
                                     model=None, n_repeats: int = 10) -> List[ImportanceResult]:
        """排列重要性分析"""
        logger.info("开始排列重要性分析")
        
        if model is None:
            model = RandomForestRegressor(n_estimators=100, random_state=42)
            model.fit(X, y)
        
        # 计算排列重要性
        perm_importance = permutation_importance(
            model, X, y,
            n_repeats=n_repeats,
            random_state=42,
            n_jobs=-1
        )
        
        # 创建重要性结果
        results = []
        for i, feature in enumerate(X.columns):
            importance = perm_importance.importances_mean[i]
            std = perm_importance.importances_std[i]
            category = self._get_feature_category(feature)
            
            results.append(ImportanceResult(
                feature_name=feature,
                importance_score=importance,
                rank=i + 1,
                category=category,
                method='permutation',
                confidence_interval=(importance - std, importance + std)
            ))
        
        # 按重要性排序
        results.sort(key=lambda x: x.importance_score, reverse=True)
        
        # 更新排名
        for i, result in enumerate(results):
            result.rank = i + 1
        
        logger.info(f"排列重要性分析完成，分析了 {len(results)} 个特征")
        return results
    
    def analyze_shap_importance(self, X: pd.DataFrame, y: pd.Series,
                              model=None, sample_size: int = 1000) -> List[ImportanceResult]:
        """SHAP重要性分析"""
        logger.info("开始SHAP重要性分析")
        
        try:
            if model is None:
                model = RandomForestRegressor(n_estimators=100, random_state=42)
                model.fit(X, y)
            
            # 采样数据（SHAP计算较慢）
            if len(X) > sample_size:
                sample_indices = np.random.choice(len(X), sample_size, replace=False)
                X_sample = X.iloc[sample_indices]
            else:
                X_sample = X
            
            # 创建SHAP解释器
            explainer = shap.TreeExplainer(model)
            shap_values = explainer.shap_values(X_sample)
            
            # 计算特征重要性（平均绝对SHAP值）
            feature_importance = np.mean(np.abs(shap_values), axis=0)
            
            # 创建重要性结果
            results = []
            for i, (feature, importance) in enumerate(zip(X.columns, feature_importance)):
                category = self._get_feature_category(feature)
                results.append(ImportanceResult(
                    feature_name=feature,
                    importance_score=importance,
                    rank=i + 1,
                    category=category,
                    method='shap'
                ))
            
            # 按重要性排序
            results.sort(key=lambda x: x.importance_score, reverse=True)
            
            # 更新排名
            for i, result in enumerate(results):
                result.rank = i + 1
            
            logger.info(f"SHAP重要性分析完成，分析了 {len(results)} 个特征")
            return results
            
        except Exception as e:
            logger.error(f"SHAP分析失败: {e}")
            # 回退到树模型重要性
            return self.analyze_tree_importance(X, y)
    
    def analyze_correlation_importance(self, X: pd.DataFrame, y: pd.Series) -> List[ImportanceResult]:
        """相关性重要性分析"""
        logger.info("开始相关性重要性分析")
        
        # 计算与目标变量的相关性
        correlations = X.corrwith(y).abs()
        
        # 创建重要性结果
        results = []
        for i, (feature, correlation) in enumerate(correlations.items()):
            if pd.isna(correlation):
                correlation = 0.0
            
            category = self._get_feature_category(feature)
            results.append(ImportanceResult(
                feature_name=feature,
                importance_score=correlation,
                rank=i + 1,
                category=category,
                method='correlation'
            ))
        
        # 按重要性排序
        results.sort(key=lambda x: x.importance_score, reverse=True)
        
        # 更新排名
        for i, result in enumerate(results):
            result.rank = i + 1
        
        logger.info(f"相关性重要性分析完成，分析了 {len(results)} 个特征")
        return results
    
    def analyze_regularization_importance(self, X: pd.DataFrame, y: pd.Series,
                                        method: str = 'lasso') -> List[ImportanceResult]:
        """正则化重要性分析"""
        logger.info(f"开始正则化重要性分析，方法: {method}")
        
        from sklearn.preprocessing import StandardScaler
        
        # 标准化特征
        scaler = StandardScaler()
        X_scaled = pd.DataFrame(
            scaler.fit_transform(X),
            columns=X.columns,
            index=X.index
        )
        
        # 选择模型
        if method == 'lasso':
            model = LassoCV(cv=5, random_state=42)
        elif method == 'elastic_net':
            model = ElasticNetCV(cv=5, random_state=42)
        else:
            raise ValueError(f"不支持的方法: {method}")
        
        # 训练模型
        model.fit(X_scaled, y)
        
        # 获取系数
        coefficients = np.abs(model.coef_)
        
        # 创建重要性结果
        results = []
        for i, (feature, coef) in enumerate(zip(X.columns, coefficients)):
            category = self._get_feature_category(feature)
            results.append(ImportanceResult(
                feature_name=feature,
                importance_score=coef,
                rank=i + 1,
                category=category,
                method=f'regularization_{method}'
            ))
        
        # 按重要性排序
        results.sort(key=lambda x: x.importance_score, reverse=True)
        
        # 更新排名
        for i, result in enumerate(results):
            result.rank = i + 1
        
        logger.info(f"正则化重要性分析完成，分析了 {len(results)} 个特征")
        return results
    
    def comprehensive_importance_analysis(self, X: pd.DataFrame, y: pd.Series,
                                        methods: List[str] = None) -> ImportanceAnalysisReport:
        """综合重要性分析"""
        start_time = datetime.now()
        
        if methods is None:
            methods = ['random_forest', 'permutation', 'correlation', 'lasso']
        
        logger.info(f"开始综合重要性分析，方法: {methods}")
        
        all_results = {}
        
        # 执行各种重要性分析
        for method in methods:
            try:
                if method == 'random_forest':
                    results = self.analyze_tree_importance(X, y, 'random_forest')
                elif method == 'extra_trees':
                    results = self.analyze_tree_importance(X, y, 'extra_trees')
                elif method == 'permutation':
                    results = self.analyze_permutation_importance(X, y)
                elif method == 'shap':
                    results = self.analyze_shap_importance(X, y)
                elif method == 'correlation':
                    results = self.analyze_correlation_importance(X, y)
                elif method == 'lasso':
                    results = self.analyze_regularization_importance(X, y, 'lasso')
                elif method == 'elastic_net':
                    results = self.analyze_regularization_importance(X, y, 'elastic_net')
                else:
                    logger.warning(f"未知的重要性分析方法: {method}")
                    continue
                
                all_results[method] = results
                
            except Exception as e:
                logger.error(f"重要性分析方法 {method} 失败: {e}")
                continue
        
        # 合并结果
        combined_results = self._combine_importance_results(all_results)
        
        # 计算类别汇总
        category_summary = self._calculate_category_summary(combined_results)
        
        # 计算稳定性分数
        stability_score = self._calculate_stability_score(all_results)
        
        # 选择top特征
        top_features = combined_results[:50]  # 取前50个特征
        
        analysis_time = (datetime.now() - start_time).total_seconds()
        
        # 创建报告
        report = ImportanceAnalysisReport(
            analysis_date=datetime.now(),
            method='comprehensive',
            total_features=len(combined_results),
            top_features=top_features,
            category_summary=category_summary,
            stability_score=stability_score,
            analysis_time=analysis_time
        )
        
        # 保存到历史记录
        self.importance_history.append(report)
        
        logger.info(f"综合重要性分析完成，分析时间: {analysis_time:.2f}秒")
        return report
    
    def _combine_importance_results(self, all_results: Dict[str, List[ImportanceResult]]) -> List[ImportanceResult]:
        """合并多种方法的重要性结果"""
        if not all_results:
            return []
        
        # 收集所有特征
        all_features = set()
        for results in all_results.values():
            for result in results:
                all_features.add(result.feature_name)
        
        # 计算每个特征的综合重要性
        combined_results = []
        for feature in all_features:
            scores = []
            ranks = []
            categories = []
            
            for method, results in all_results.items():
                for result in results:
                    if result.feature_name == feature:
                        # 标准化分数（排名倒数）
                        normalized_score = 1.0 / (result.rank + 1)
                        scores.append(normalized_score)
                        ranks.append(result.rank)
                        categories.append(result.category)
                        break
            
            if scores:
                # 计算平均分数
                avg_score = np.mean(scores)
                avg_rank = np.mean(ranks)
                category = categories[0] if categories else 'unknown'
                
                combined_results.append(ImportanceResult(
                    feature_name=feature,
                    importance_score=avg_score,
                    rank=int(avg_rank),
                    category=category,
                    method='combined'
                ))
        
        # 按综合重要性排序
        combined_results.sort(key=lambda x: x.importance_score, reverse=True)
        
        # 更新排名
        for i, result in enumerate(combined_results):
            result.rank = i + 1
        
        return combined_results
    
    def _calculate_category_summary(self, results: List[ImportanceResult]) -> Dict[str, Dict]:
        """计算类别汇总"""
        category_summary = {}
        
        # 按类别分组
        category_groups = {}
        for result in results:
            if result.category not in category_groups:
                category_groups[result.category] = []
            category_groups[result.category].append(result)
        
        # 计算每个类别的统计信息
        for category, group_results in category_groups.items():
            scores = [r.importance_score for r in group_results]
            ranks = [r.rank for r in group_results]
            
            category_summary[category] = {
                'feature_count': len(group_results),
                'avg_importance': np.mean(scores),
                'max_importance': np.max(scores),
                'min_importance': np.min(scores),
                'avg_rank': np.mean(ranks),
                'best_rank': np.min(ranks),
                'top_feature': min(group_results, key=lambda x: x.rank).feature_name,
                'category_name': self.category_mapping.get(category, category)
            }
        
        return category_summary
    
    def _calculate_stability_score(self, all_results: Dict[str, List[ImportanceResult]]) -> float:
        """计算重要性稳定性分数"""
        if len(all_results) < 2:
            return 1.0
        
        # 获取所有方法的top-20特征
        top_features_by_method = {}
        for method, results in all_results.items():
            top_features_by_method[method] = set([r.feature_name for r in results[:20]])
        
        # 计算方法间的重叠度
        methods = list(top_features_by_method.keys())
        overlaps = []
        
        for i in range(len(methods)):
            for j in range(i + 1, len(methods)):
                method1, method2 = methods[i], methods[j]
                features1 = top_features_by_method[method1]
                features2 = top_features_by_method[method2]
                
                intersection = len(features1.intersection(features2))
                union = len(features1.union(features2))
                
                if union > 0:
                    overlap = intersection / union
                    overlaps.append(overlap)
        
        # 返回平均重叠度作为稳定性分数
        return np.mean(overlaps) if overlaps else 0.0
    
    def _get_feature_category(self, feature_name: str) -> str:
        """获取特征类别"""
        feature_lower = feature_name.lower()
        
        if 'fundamental' in feature_lower:
            return 'fundamental'
        elif 'valuation' in feature_lower:
            return 'valuation'
        elif 'technical' in feature_lower:
            return 'technical'
        elif 'sentiment' in feature_lower:
            return 'sentiment'
        elif 'risk' in feature_lower:
            return 'risk'
        elif 'market' in feature_lower:
            return 'market'
        elif 'overall' in feature_lower:
            return 'overall'
        elif 'explosive' in feature_lower:
            return 'explosive'
        else:
            return 'other'
    
    def plot_importance_comparison(self, all_results: Dict[str, List[ImportanceResult]],
                                 top_n: int = 20, save_path: str = None):
        """绘制重要性比较图"""
        try:
            # 获取所有方法的top-n特征
            fig, axes = plt.subplots(2, 2, figsize=(15, 12))
            axes = axes.flatten()
            
            for i, (method, results) in enumerate(all_results.items()):
                if i >= 4:  # 最多显示4个方法
                    break
                
                top_results = results[:top_n]
                features = [r.feature_name for r in top_results]
                scores = [r.importance_score for r in top_results]
                
                # 截断特征名称
                features = [f[:20] + '...' if len(f) > 20 else f for f in features]
                
                axes[i].barh(range(len(features)), scores)
                axes[i].set_yticks(range(len(features)))
                axes[i].set_yticklabels(features)
                axes[i].set_title(f'{method} - Top {top_n} Features')
                axes[i].set_xlabel('Importance Score')
                
                # 反转y轴，使最重要的特征在顶部
                axes[i].invert_yaxis()
            
            # 隐藏多余的子图
            for i in range(len(all_results), 4):
                axes[i].set_visible(False)
            
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                logger.info(f"重要性比较图已保存到: {save_path}")
            
            plt.show()
            
        except Exception as e:
            logger.error(f"绘制重要性比较图失败: {e}")
    
    def plot_category_importance(self, category_summary: Dict[str, Dict],
                               save_path: str = None):
        """绘制类别重要性图"""
        try:
            categories = list(category_summary.keys())
            avg_importance = [summary['avg_importance'] for summary in category_summary.values()]
            feature_counts = [summary['feature_count'] for summary in category_summary.values()]
            
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
            
            # 平均重要性
            bars1 = ax1.bar(categories, avg_importance)
            ax1.set_title('各类别平均重要性')
            ax1.set_ylabel('平均重要性分数')
            ax1.tick_params(axis='x', rotation=45)
            
            # 添加数值标签
            for bar, value in zip(bars1, avg_importance):
                ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.001,
                        f'{value:.3f}', ha='center', va='bottom')
            
            # 特征数量
            bars2 = ax2.bar(categories, feature_counts)
            ax2.set_title('各类别特征数量')
            ax2.set_ylabel('特征数量')
            ax2.tick_params(axis='x', rotation=45)
            
            # 添加数值标签
            for bar, value in zip(bars2, feature_counts):
                ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                        str(value), ha='center', va='bottom')
            
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                logger.info(f"类别重要性图已保存到: {save_path}")
            
            plt.show()
            
        except Exception as e:
            logger.error(f"绘制类别重要性图失败: {e}")
    
    def export_importance_report(self, report: ImportanceAnalysisReport,
                               filepath: str):
        """导出重要性分析报告"""
        try:
            # 创建Excel写入器
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                # 基本信息
                basic_info = pd.DataFrame([{
                    '分析日期': report.analysis_date.strftime('%Y-%m-%d %H:%M:%S'),
                    '分析方法': report.method,
                    '总特征数': report.total_features,
                    '稳定性分数': f"{report.stability_score:.3f}",
                    '分析时间(秒)': f"{report.analysis_time:.2f}"
                }])
                basic_info.to_excel(writer, sheet_name='基本信息', index=False)
                
                # Top特征
                top_features_data = []
                for result in report.top_features:
                    top_features_data.append({
                        '排名': result.rank,
                        '特征名称': result.feature_name,
                        '重要性分数': f"{result.importance_score:.6f}",
                        '类别': result.category,
                        '方法': result.method
                    })
                
                top_features_df = pd.DataFrame(top_features_data)
                top_features_df.to_excel(writer, sheet_name='Top特征', index=False)
                
                # 类别汇总
                category_data = []
                for category, summary in report.category_summary.items():
                    category_data.append({
                        '类别': summary.get('category_name', category),
                        '特征数量': summary['feature_count'],
                        '平均重要性': f"{summary['avg_importance']:.6f}",
                        '最高重要性': f"{summary['max_importance']:.6f}",
                        '平均排名': f"{summary['avg_rank']:.1f}",
                        '最佳排名': summary['best_rank'],
                        '最重要特征': summary['top_feature']
                    })
                
                category_df = pd.DataFrame(category_data)
                category_df.to_excel(writer, sheet_name='类别汇总', index=False)
            
            logger.info(f"重要性分析报告已导出到: {filepath}")
            
        except Exception as e:
            logger.error(f"导出重要性分析报告失败: {e}")
    
    def get_importance_trends(self, feature_name: str) -> List[float]:
        """获取特征重要性趋势"""
        trends = []
        for report in self.importance_history:
            for result in report.top_features:
                if result.feature_name == feature_name:
                    trends.append(result.importance_score)
                    break
            else:
                trends.append(0.0)  # 如果特征不在top列表中
        
        return trends