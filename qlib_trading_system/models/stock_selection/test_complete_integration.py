"""
完整的模型训练和更新系统集成测试
测试真实模块的完整集成功能
"""

import os
import sys
import json
import tempfile
import shutil
import time
from datetime import datetime, timedelta
from pathlib import Path
import numpy as np

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent.parent.parent))

# 简单的日志记录
class SimpleLogger:
    def info(self, msg): print(f"[INFO] {msg}")
    def error(self, msg): print(f"[ERROR] {msg}")
    def warning(self, msg): print(f"[WARNING] {msg}")
    def debug(self, msg): print(f"[DEBUG] {msg}")

logger = SimpleLogger()


def test_real_incremental_trainer():
    """测试真实的增量训练器"""
    print("🔄 测试真实增量训练器...")
    
    temp_dir = tempfile.mkdtemp()
    
    try:
        # 导入真实的增量训练器
        from qlib_trading_system.models.stock_selection.incremental_trainer import (
            IncrementalTrainer, IncrementalConfig
        )
        
        # 创建配置
        config = IncrementalConfig(
            batch_size=100,
            min_samples_for_update=50,
            update_frequency='daily'
        )
        
        # 创建训练器
        trainer = IncrementalTrainer(config, temp_dir)
        
        # 生成测试数据
        np.random.seed(42)
        X_train = np.random.randn(500, 10)
        y_train = np.random.randn(500)
        X_val = np.random.randn(100, 10)
        y_val = np.random.randn(100)
        
        # 初始化基础模型
        result = trainer.initialize_base_model(X_train, y_train, X_val, y_val)
        assert 'train_score' in result
        print(f"  ✅ 基础模型初始化成功，训练分数: {result['train_score']:.4f}")
        
        # 添加增量数据
        X_new = np.random.randn(60, 10)
        y_new = np.random.randn(60)
        trainer.add_training_data(X_new, y_new)
        
        buffer_size = trainer.data_buffer.size()
        assert buffer_size == 60
        print(f"  ✅ 增量数据添加成功，缓冲区大小: {buffer_size}")
        
        # 执行增量更新
        update_result = trainer.perform_incremental_update()
        assert update_result['status'] in ['success', 'rejected']
        print(f"  ✅ 增量更新完成，状态: {update_result['status']}")
        
        # 测试在线预测
        X_test = np.random.randn(10, 10)
        predictions = trainer.online_predict_and_learn(X_test)
        assert len(predictions) == 10
        print(f"  ✅ 在线预测成功，预测数量: {len(predictions)}")
        
        # 获取模型信息
        model_info = trainer.get_model_info()
        assert model_info['model_exists']
        print(f"  ✅ 模型信息获取成功: {model_info}")
        
        print("  ✅ 真实增量训练器测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 真实增量训练器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        shutil.rmtree(temp_dir, ignore_errors=True)


def test_real_version_manager():
    """测试真实的版本管理器"""
    print("📦 测试真实版本管理器...")
    
    temp_dir = tempfile.mkdtemp()
    
    try:
        # 导入真实的版本管理器
        from qlib_trading_system.models.stock_selection.model_version_manager import (
            ModelVersionManager, ModelMetadata, ModelStatus
        )
        
        # 创建版本管理器
        version_manager = ModelVersionManager(temp_dir)
        
        # 创建测试模型
        from sklearn.ensemble import RandomForestRegressor
        model = RandomForestRegressor(n_estimators=10, random_state=42)
        
        # 训练模型
        X = np.random.randn(100, 5)
        y = np.random.randn(100)
        model.fit(X, y)
        
        # 创建元数据
        metadata = ModelMetadata(
            version="",
            name="test_model",
            description="测试模型",
            created_time=datetime.now(),
            model_type="RandomForest",
            framework="sklearn",
            train_score=0.85,
            validation_score=0.78,
            feature_count=5,
            training_samples=100
        )
        
        # 创建版本
        version = version_manager.create_version(model, metadata)
        assert version is not None
        print(f"  ✅ 版本创建成功: {version}")
        
        # 部署版本
        success = version_manager.deploy_version(version)
        assert success
        print(f"  ✅ 版本部署成功")
        
        # 获取活跃版本
        active_version = version_manager.get_active_version()
        assert active_version == version
        print(f"  ✅ 活跃版本获取成功: {active_version}")
        
        # 加载模型
        loaded_model, loaded_metadata = version_manager.load_active_model()
        assert loaded_model is not None
        assert loaded_metadata.name == "test_model"
        print(f"  ✅ 模型加载成功")
        
        # 创建第二个版本
        model2 = RandomForestRegressor(n_estimators=15, random_state=42)
        model2.fit(X, y)
        
        metadata2 = ModelMetadata(
            version="",
            name="test_model_v2",
            description="测试模型v2",
            created_time=datetime.now(),
            model_type="RandomForest",
            framework="sklearn",
            train_score=0.87,
            validation_score=0.80,
            feature_count=5,
            training_samples=100
        )
        
        time.sleep(0.1)  # 确保时间戳不同
        version2 = version_manager.create_version(model2, metadata2)
        version_manager.deploy_version(version2)
        
        # 测试回滚
        success = version_manager.rollback_to_version(version, "测试回滚")
        assert success
        assert version_manager.get_active_version() == version
        print(f"  ✅ 版本回滚成功")
        
        # 列出版本
        versions = version_manager.list_versions()
        assert len(versions) >= 2
        print(f"  ✅ 版本列表获取成功，数量: {len(versions)}")
        
        # 版本比较
        comparison = version_manager.compare_versions(version, version2)
        assert 'performance_diff' in comparison
        print(f"  ✅ 版本比较成功")
        
        print("  ✅ 真实版本管理器测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 真实版本管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        shutil.rmtree(temp_dir, ignore_errors=True)


def test_real_config_manager():
    """测试真实的配置管理器"""
    print("⚙️ 测试真实配置管理器...")
    
    temp_dir = tempfile.mkdtemp()
    
    try:
        # 导入真实的配置管理器
        from qlib_trading_system.models.stock_selection.training_config_manager import (
            TrainingConfigManager
        )
        
        # 创建配置管理器
        config_manager = TrainingConfigManager(temp_dir)
        
        # 获取各种配置
        model_config = config_manager.get_model_config()
        assert model_config.model_type == "lightgbm"
        print(f"  ✅ 模型配置获取成功: {model_config.model_type}")
        
        data_config = config_manager.get_data_config()
        assert data_config.primary_data_source == "itick"
        print(f"  ✅ 数据配置获取成功: {data_config.primary_data_source}")
        
        training_config = config_manager.get_training_config()
        assert training_config.incremental_config is not None
        print(f"  ✅ 训练配置获取成功")
        
        # 验证配置
        validation_results = config_manager.validate_all_configs()
        all_valid = all(len(errors) == 0 for errors in validation_results.values())
        assert all_valid
        print(f"  ✅ 配置验证通过")
        
        # 导出配置
        export_path = Path(temp_dir) / "exported_config.json"
        config_manager.export_config(str(export_path))
        assert export_path.exists()
        print(f"  ✅ 配置导出成功")
        
        # 测试配置更新
        new_model_config = {
            "model_type": "xgboost",
            "learning_rate": 0.05,
            "num_leaves": 50
        }
        config_manager.update_config("model", new_model_config)
        updated_config = config_manager.get_model_config()
        assert updated_config.model_type == "xgboost"
        print(f"  ✅ 配置更新成功")
        
        print("  ✅ 真实配置管理器测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 真实配置管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        shutil.rmtree(temp_dir, ignore_errors=True)


def test_system_integration():
    """测试系统集成"""
    print("🔗 测试系统集成...")
    
    temp_dir = tempfile.mkdtemp()
    
    try:
        # 导入所有必要的模块
        from qlib_trading_system.models.stock_selection.training_config_manager import (
            TrainingConfigManager
        )
        from qlib_trading_system.models.stock_selection.incremental_trainer import (
            IncrementalTrainer, IncrementalConfig
        )
        from qlib_trading_system.models.stock_selection.model_version_manager import (
            ModelVersionManager, ModelMetadata
        )
        
        # 创建集成测试系统
        class IntegratedTestSystem:
            def __init__(self, base_dir):
                self.base_dir = Path(base_dir)
                self.base_dir.mkdir(parents=True, exist_ok=True)
                
                # 创建配置管理器
                self.config_manager = TrainingConfigManager(str(self.base_dir / "config"))
                
                # 创建增量训练器
                incremental_config = self.config_manager.get_incremental_config()
                self.trainer = IncrementalTrainer(
                    incremental_config, 
                    str(self.base_dir / "trainer")
                )
                
                # 创建版本管理器
                self.version_manager = ModelVersionManager(
                    str(self.base_dir / "versions")
                )
                
                self.active_version = None
                self.training_history = []
            
            def initialize_system(self, X_train, y_train, X_val=None, y_val=None):
                """初始化系统"""
                # 初始化基础模型
                result = self.trainer.initialize_base_model(X_train, y_train, X_val, y_val)
                
                # 创建版本元数据
                metadata = ModelMetadata(
                    version="",
                    name="integrated_test_model",
                    description="集成测试模型",
                    created_time=datetime.now(),
                    model_type="LightGBM",
                    framework="lightgbm",
                    train_score=result['train_score'],
                    validation_score=result.get('val_score', 0.0),
                    feature_count=X_train.shape[1],
                    training_samples=len(X_train)
                )
                
                # 创建版本
                version = self.version_manager.create_version(
                    self.trainer.current_model, metadata
                )
                
                # 部署版本
                self.version_manager.deploy_version(version)
                self.active_version = version
                
                # 记录历史
                self.training_history.append({
                    'timestamp': datetime.now(),
                    'action': 'initialize',
                    'version': version,
                    'train_score': result['train_score']
                })
                
                return version
            
            def perform_incremental_update(self, X_new, y_new):
                """执行增量更新"""
                # 添加数据
                self.trainer.add_training_data(X_new, y_new)
                
                # 执行更新
                update_result = self.trainer.perform_incremental_update()
                
                if update_result['status'] == 'success':
                    # 创建新版本
                    metadata = ModelMetadata(
                        version="",
                        name="integrated_test_model_updated",
                        description="集成测试模型更新版",
                        created_time=datetime.now(),
                        model_type="LightGBM",
                        framework="lightgbm",
                        train_score=update_result['new_score'],
                        validation_score=update_result['new_score'],
                        feature_count=X_new.shape[1],
                        training_samples=len(X_new),
                        parent_version=self.active_version
                    )
                    
                    new_version = self.version_manager.create_version(
                        self.trainer.current_model, metadata
                    )
                    
                    # 如果改进足够，部署新版本
                    if update_result['improvement'] > 0.01:
                        self.version_manager.deploy_version(new_version)
                        self.active_version = new_version
                    
                    # 记录历史
                    self.training_history.append({
                        'timestamp': datetime.now(),
                        'action': 'incremental_update',
                        'old_version': self.active_version,
                        'new_version': new_version,
                        'improvement': update_result['improvement']
                    })
                    
                    return new_version
                
                return None
            
            def get_system_status(self):
                """获取系统状态"""
                return {
                    'active_version': self.active_version,
                    'training_history_count': len(self.training_history),
                    'buffer_size': self.trainer.data_buffer.size(),
                    'model_exists': self.trainer.current_model is not None,
                    'version_count': len(self.version_manager.list_versions())
                }
        
        # 测试集成系统
        system = IntegratedTestSystem(temp_dir)
        
        # 生成测试数据
        np.random.seed(42)
        X_train = np.random.randn(300, 8)
        y_train = np.random.randn(300)
        X_val = np.random.randn(60, 8)
        y_val = np.random.randn(60)
        
        # 初始化系统
        base_version = system.initialize_system(X_train, y_train, X_val, y_val)
        assert base_version is not None
        print(f"  ✅ 系统初始化成功，基础版本: {base_version}")
        
        # 检查系统状态
        status = system.get_system_status()
        assert status['active_version'] == base_version
        assert status['model_exists']
        assert status['training_history_count'] == 1
        print(f"  ✅ 系统状态正常: {status}")
        
        # 执行增量更新
        X_new = np.random.randn(80, 8)
        y_new = np.random.randn(80)
        
        new_version = system.perform_incremental_update(X_new, y_new)
        if new_version:
            print(f"  ✅ 增量更新成功，新版本: {new_version}")
        else:
            print(f"  ⚠️ 增量更新未产生新版本（可能改进不足）")
        
        # 检查最终状态
        final_status = system.get_system_status()
        assert final_status['training_history_count'] >= 1
        assert final_status['version_count'] >= 1
        print(f"  ✅ 最终系统状态: {final_status}")
        
        # 测试版本管理功能
        versions = system.version_manager.list_versions()
        assert len(versions) >= 1
        print(f"  ✅ 版本管理功能正常，版本数: {len(versions)}")
        
        # 测试配置管理功能
        training_config = system.config_manager.get_training_config()
        assert training_config is not None
        print(f"  ✅ 配置管理功能正常")
        
        print("  ✅ 系统集成测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 系统集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        shutil.rmtree(temp_dir, ignore_errors=True)


def main():
    """运行完整集成测试"""
    print("🚀 开始完整模型训练和更新系统集成测试")
    print("=" * 60)
    
    test_results = []
    
    # 运行各个测试
    test_functions = [
        ("真实增量训练器", test_real_incremental_trainer),
        ("真实版本管理器", test_real_version_manager),
        ("真实配置管理器", test_real_config_manager),
        ("系统集成", test_system_integration)
    ]
    
    for test_name, test_func in test_functions:
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"  ❌ {test_name}测试异常: {e}")
            test_results.append((test_name, False))
        
        print()  # 空行分隔
    
    # 输出测试结果汇总
    print("=" * 60)
    print("📋 完整集成测试结果汇总:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 所有完整集成测试通过！")
        print("模型训练和更新系统的完整功能已验证正常工作")
        print("\n📝 测试覆盖的完整功能:")
        print("  - ✅ 真实增量学习模块集成")
        print("  - ✅ 真实版本管理模块集成")
        print("  - ✅ 真实配置管理模块集成")
        print("  - ✅ 完整系统组件协作")
        print("  - ✅ 端到端工作流验证")
        return True
    else:
        print(f"\n⚠️ 有 {total - passed} 个测试失败，请检查相关功能")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)