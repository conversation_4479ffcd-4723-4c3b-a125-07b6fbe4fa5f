"""
爆发股识别模型测试
测试LightGBM+LSTM混合模型的各个组件
"""

import numpy as np
import pandas as pd
import pytest
import logging
from datetime import datetime, timedelta
import tempfile
import os
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 导入模型组件
from .explosive_model import ExplosiveStockModel, ModelConfig
from .training_framework import ModelTrainingFramework, TrainingConfig
from .hyperparameter_optimizer import HyperparameterOptimizer, OptimizationConfig
from .performance_monitor import PerformanceMonitor, AlertConfig, PerformanceMetrics

class TestExplosiveStockModel:
    """测试爆发股识别模型"""
    
    @pytest.fixture
    def sample_data(self):
        """生成测试数据"""
        np.random.seed(42)
        n_samples = 1000
        n_features = 50
        
        # 生成特征数据
        features = np.random.randn(n_samples, n_features)
        feature_names = [f'feature_{i}' for i in range(n_features)]
        
        # 生成目标变量（爆发股标签）
        # 使用特征的线性组合加噪声
        weights = np.random.randn(n_features) * 0.1
        target_continuous = features @ weights + np.random.randn(n_samples) * 0.5
        target = (target_continuous > np.percentile(target_continuous, 80)).astype(int)
        
        # 创建DataFrame
        data = pd.DataFrame(features, columns=feature_names)
        data['symbol'] = [f'stock_{i:04d}' for i in range(n_samples)]
        data['date'] = pd.date_range('2020-01-01', periods=n_samples, freq='D')
        data['is_explosive'] = target
        
        return data
    
    @pytest.fixture
    def model_config(self):
        """模型配置"""
        return ModelConfig(
            lstm_hidden_size=64,
            lstm_num_layers=1,
            sequence_length=10,
            test_size=0.2,
            random_state=42
        )
    
    def test_model_initialization(self, model_config):
        """测试模型初始化"""
        model = ExplosiveStockModel(model_config)
        
        assert model.config == model_config
        assert not model.is_trained
        assert model.lgb_model is None
        assert model.lstm_model is None
        
        logger.info("✓ 模型初始化测试通过")
    
    def test_model_training(self, sample_data, model_config):
        """测试模型训练"""
        model = ExplosiveStockModel(model_config)
        
        # 准备训练数据
        features = sample_data.drop(columns=['is_explosive'])
        target = sample_data['is_explosive']
        
        # 训练模型
        training_results = model.train(features, target)
        
        # 验证训练结果
        assert model.is_trained
        assert model.lgb_model is not None
        assert 'train_metrics' in training_results
        assert 'test_metrics' in training