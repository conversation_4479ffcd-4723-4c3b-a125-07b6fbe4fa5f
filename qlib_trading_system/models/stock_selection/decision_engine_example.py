"""
决策引擎使用示例
展示如何使用AI驱动的股票评分、动态切换决策、持仓决策和置信度评估功能
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List
import warnings
warnings.filterwarnings('ignore')

from decision_engine import (
    DecisionEngine,
    StockScoringSystem,
    DynamicSwitchingEngine,
    PositionDecisionEngine,
    ModelConfidenceEvaluator,
    StockScore,
    TradingDecision,
    DecisionType,
    ConfidenceLevel
)


def create_sample_stock_features(symbol: str, base_score: float = 0.6) -> Dict:
    """
    创建示例股票特征数据
    
    Args:
        symbol: 股票代码
        base_score: 基础评分
        
    Returns:
        Dict: 股票特征数据
    """
    # 基于基础评分生成相关特征
    noise = np.random.normal(0, 0.1)
    multiplier = base_score + noise
    
    return {
        'symbol': symbol,
        # 基本面特征
        'roe_growth': max(0, 0.2 * multiplier + np.random.normal(0, 0.05)),
        'revenue_growth': max(0, 0.15 * multiplier + np.random.normal(0, 0.03)),
        'profit_growth': max(0, 0.25 * multiplier + np.random.normal(0, 0.08)),
        'debt_ratio': max(0.1, min(0.8, 0.4 - 0.2 * multiplier + np.random.normal(0, 0.1))),
        
        # 估值特征
        'pe_ratio': max(5, 20 - 10 * multiplier + np.random.normal(0, 3)),
        'industry_pe': 25,
        'pb_ratio': max(0.5, 3 - multiplier + np.random.normal(0, 0.5)),
        'peg_ratio': max(0.3, 1.5 - multiplier + np.random.normal(0, 0.3)),
        
        # 技术面特征
        'momentum_20': (multiplier - 0.5) * 0.3 + np.random.normal(0, 0.05),
        'volume_ratio': max(0.5, 1 + multiplier + np.random.normal(0, 0.5)),
        'rsi': max(20, min(80, 50 + (multiplier - 0.5) * 40 + np.random.normal(0, 10))),
        'breakout_signal': max(0, min(1, multiplier + np.random.normal(0, 0.2))),
        
        # 情绪特征
        'news_sentiment': max(-1, min(1, (multiplier - 0.5) * 2 + np.random.normal(0, 0.3))),
        'money_flow': (multiplier - 0.5) * 0.6 + np.random.normal(0, 0.2),
        'institution_attention': max(0, min(1, multiplier + np.random.normal(0, 0.2))),
        
        # 风险特征
        'volatility': max(0.1, 0.4 - 0.2 * multiplier + np.random.normal(0, 0.1)),
        'avg_turnover': max(0.01, 0.05 * multiplier + np.random.normal(0, 0.02)),
        'st_risk': max(0, 0.3 - 0.4 * multiplier + np.random.normal(0, 0.1)),
        'max_drawdown': max(0, 0.3 - 0.2 * multiplier + np.random.normal(0, 0.1)),
        
        # 市场特征
        'market_trend': np.random.normal(0, 0.1),
        'industry_performance': np.random.normal(0, 0.05),
        
        # 数据质量
        'data_completeness': max(0.7, min(1.0, 0.9 + np.random.normal(0, 0.05))),
        
        # 爆发潜力相关
        'volume_surge': max(0, multiplier + np.random.normal(0, 0.2)),
        'catalyst_score': max(0, min(1, multiplier + np.random.normal(0, 0.2))),
        'timing_score': max(0, min(1, 0.5 + np.random.normal(0, 0.2)))
    }


def demo_stock_scoring_system():
    """演示股票评分系统"""
    print("=" * 60)
    print("1. 股票评分系统演示")
    print("=" * 60)
    
    # 初始化评分系统
    scoring_system = StockScoringSystem()
    
    # 创建不同质量的股票样本
    stocks = [
        ('000001.SZ', '平安银行', 0.8),  # 高质量股票
        ('000002.SZ', '万科A', 0.6),     # 中等质量股票
        ('000003.SZ', '某ST股', 0.3)     # 低质量股票
    ]
    
    print("股票评分结果:")
    print("-" * 60)
    
    for symbol, name, base_score in stocks:
        features = create_sample_stock_features(symbol, base_score)
        score = scoring_system.calculate_stock_score(features)
        
        print(f"股票: {name} ({symbol})")
        print(f"  综合评分: {score.total_score:.3f}")
        print(f"  爆发潜力: {score.explosive_potential:.3f}")
        print(f"  置信度: {score.confidence:.3f}")
        print(f"  风险等级: {score.risk_level}")
        print(f"  维度评分:")
        for dim, dim_score in score.dimension_scores.items():
            print(f"    {dim}: {dim_score:.3f}")
        print()


def demo_dynamic_switching_engine():
    """演示动态切换引擎"""
    print("=" * 60)
    print("2. 动态切换引擎演示")
    print("=" * 60)
    
    # 初始化切换引擎
    switching_engine = DynamicSwitchingEngine()
    scoring_system = StockScoringSystem()
    
    # 创建当前持有股票和候选股票
    current_stock = '000001.SZ'
    current_features = create_sample_stock_features(current_stock, 0.6)
    current_score = scoring_system.calculate_stock_score(current_features)
    
    # 创建候选股票列表
    candidates = []
    candidate_symbols = ['000002.SZ', '000003.SZ', '000004.SZ']
    candidate_base_scores = [0.8, 0.5, 0.9]  # 其中000004.SZ是更优的选择
    
    for symbol, base_score in zip(candidate_symbols, candidate_base_scores):
        features = create_sample_stock_features(symbol, base_score)
        score = scoring_system.calculate_stock_score(features)
        candidates.append(score)
    
    print(f"当前持有: {current_stock}")
    print(f"当前评分: {current_score.total_score:.3f}")
    print(f"当前爆发潜力: {current_score.explosive_potential:.3f}")
    print()
    
    print("候选股票:")
    for candidate in candidates:
        print(f"  {candidate.symbol}: 评分={candidate.total_score:.3f}, 爆发潜力={candidate.explosive_potential:.3f}")
    print()
    
    # 测试不同持有天数的切换决策
    for holding_days in [1, 3, 7]:
        should_switch, target, reason = switching_engine.should_switch_stock(
            current_stock, current_score, candidates, holding_days
        )
        
        print(f"持有{holding_days}天的切换决策:")
        print(f"  是否切换: {should_switch}")
        if should_switch:
            print(f"  目标股票: {target}")
        print(f"  决策原因: {reason}")
        print()


def demo_position_decision_engine():
    """演示持仓决策引擎"""
    print("=" * 60)
    print("3. 持仓决策引擎演示")
    print("=" * 60)
    
    # 初始化决策引擎
    position_engine = PositionDecisionEngine()
    scoring_system = StockScoringSystem()
    
    # 创建不同风险等级的股票
    test_stocks = [
        ('000001.SZ', '低风险股票', 0.7, 'LOW'),
        ('000002.SZ', '中风险股票', 0.6, 'MEDIUM'),
        ('000003.SZ', '高风险股票', 0.8, 'HIGH')
    ]
    
    # 不同的账户状态
    account_scenarios = [
        ('正常账户', {'current_position': 0.5, 'current_drawdown': 0.05, 'liquidity_score': 0.8, 'market_beta': 1.0}),
        ('高回撤账户', {'current_position': 0.3, 'current_drawdown': 0.20, 'liquidity_score': 0.6, 'market_beta': 1.2}),
        ('低流动性账户', {'current_position': 0.8, 'current_drawdown': 0.10, 'liquidity_score': 0.4, 'market_beta': 0.8})
    ]
    
    # 不同的市场环境
    market_scenarios = [
        ('牛市', {'trend': 'BULL', 'volatility': 0.2}),
        ('熊市', {'trend': 'BEAR', 'volatility': 0.4}),
        ('震荡市', {'trend': 'NEUTRAL', 'volatility': 0.3})
    ]
    
    print("持仓决策结果:")
    print("-" * 60)
    
    for stock_symbol, stock_name, base_score, risk_level in test_stocks:
        features = create_sample_stock_features(stock_symbol, base_score)
        stock_score = scoring_system.calculate_stock_score(features)
        stock_score.risk_level = risk_level  # 手动设置风险等级用于演示
        
        print(f"\n股票: {stock_name} ({stock_symbol}) - 风险等级: {risk_level}")
        
        for account_name, account_info in account_scenarios:
            for market_name, market_condition in market_scenarios:
                decision = position_engine.calculate_target_position(
                    stock_score, account_info, market_condition
                )
                
                print(f"  {account_name} + {market_name}: 目标仓位={decision.target_position:.3f}, 决策={decision.decision_type.value}")


def demo_model_confidence_evaluator():
    """演示模型置信度评估器"""
    print("=" * 60)
    print("4. 模型置信度评估器演示")
    print("=" * 60)
    
    # 初始化置信度评估器
    confidence_evaluator = ModelConfidenceEvaluator()
    
    # 创建模拟的预测历史数据
    print("创建模拟预测数据...")
    
    # 场景1: 高质量预测（一致性好，准确率高）
    high_quality_predictions = []
    high_quality_results = []
    
    for i in range(10):
        base_score = 0.8 + np.random.normal(0, 0.05)  # 高分且一致
        prediction = {
            'symbol': f'HQ{i:03d}.SZ',
            'score': max(0, min(1, base_score)),
            'timestamp': datetime.now() - timedelta(days=i)
        }
        high_quality_predictions.append(prediction)
        
        # 高分预测通常有正收益
        actual_return = 0.1 if base_score > 0.7 else -0.02
        actual_return += np.random.normal(0, 0.03)
        high_quality_results.append({
            'symbol': prediction['symbol'],
            'return': actual_return
        })
    
    # 场景2: 低质量预测（不一致，准确率低）
    low_quality_predictions = []
    low_quality_results = []
    
    for i in range(10):
        base_score = np.random.uniform(0.2, 0.9)  # 分数随机，不一致
        prediction = {
            'symbol': f'LQ{i:03d}.SZ',
            'score': base_score,
            'timestamp': datetime.now() - timedelta(days=i)
        }
        low_quality_predictions.append(prediction)
        
        # 随机收益，与预测无关
        actual_return = np.random.normal(0, 0.1)
        low_quality_results.append({
            'symbol': prediction['symbol'],
            'return': actual_return
        })
    
    # 评估两种场景的置信度
    print("\n高质量预测场景:")
    high_confidence = confidence_evaluator.evaluate_model_confidence(
        high_quality_predictions, high_quality_results
    )
    
    print(f"  综合置信度: {high_confidence['overall']:.3f}")
    print(f"  预测一致性: {high_confidence['consistency']:.3f}")
    print(f"  预测稳定性: {high_confidence['stability']:.3f}")
    print(f"  预测准确率: {high_confidence['accuracy']:.3f}")
    print(f"  数据质量: {high_confidence['data_quality']:.3f}")
    print(f"  置信度等级: {high_confidence['level']:.3f}")
    
    print("\n低质量预测场景:")
    low_confidence = confidence_evaluator.evaluate_model_confidence(
        low_quality_predictions, low_quality_results
    )
    
    print(f"  综合置信度: {low_confidence['overall']:.3f}")
    print(f"  预测一致性: {low_confidence['consistency']:.3f}")
    print(f"  预测稳定性: {low_confidence['stability']:.3f}")
    print(f"  预测准确率: {low_confidence['accuracy']:.3f}")
    print(f"  数据质量: {low_confidence['data_quality']:.3f}")
    print(f"  置信度等级: {low_confidence['level']:.3f}")


def demo_integrated_decision_engine():
    """演示集成决策引擎"""
    print("=" * 60)
    print("5. 集成决策引擎演示")
    print("=" * 60)
    
    # 初始化主决策引擎
    decision_engine = DecisionEngine()
    
    # 创建股票池
    stock_pool = [
        ('000001.SZ', '平安银行', 0.8),
        ('000002.SZ', '万科A', 0.7),
        ('000858.SZ', '五粮液', 0.9),
        ('000063.SZ', '中兴通讯', 0.6),
        ('000725.SZ', '京东方A', 0.5)
    ]
    
    # 创建股票特征数据
    stocks_features = []
    for symbol, name, base_score in stock_pool:
        features = create_sample_stock_features(symbol, base_score)
        stocks_features.append(features)
    
    print("1. 批量股票评估:")
    print("-" * 30)
    
    # 批量评估股票
    top_stocks = decision_engine.batch_evaluate_stocks(stocks_features, top_n=3)
    
    for i, stock_score in enumerate(top_stocks, 1):
        stock_name = next(name for sym, name, _ in stock_pool if sym == stock_score.symbol)
        print(f"  第{i}名: {stock_name} ({stock_score.symbol})")
        print(f"    综合评分: {stock_score.total_score:.3f}")
        print(f"    爆发潜力: {stock_score.explosive_potential:.3f}")
        print(f"    置信度: {stock_score.confidence:.3f}")
        print(f"    风险等级: {stock_score.risk_level}")
    
    print("\n2. 交易决策制定:")
    print("-" * 30)
    
    # 为最佳股票制定交易决策
    if top_stocks:
        best_stock = top_stocks[0]
        best_features = next(f for f in stocks_features if f['symbol'] == best_stock.symbol)
        
        # 模拟账户信息
        account_info = {
            'current_position': 0.3,
            'current_drawdown': 0.08,
            'liquidity_score': 0.8,
            'market_beta': 1.1
        }
        
        # 模拟市场状况
        market_condition = {
            'trend': 'BULL',
            'volatility': 0.25
        }
        
        # 制定交易决策
        decision = decision_engine.make_trading_decision(
            best_features, account_info, market_condition
        )
        
        stock_name = next(name for sym, name, _ in stock_pool if sym == decision.symbol)
        print(f"  目标股票: {stock_name} ({decision.symbol})")
        print(f"  决策类型: {decision.decision_type.value}")
        print(f"  目标仓位: {decision.target_position:.3f}")
        print(f"  决策置信度: {decision.confidence:.3f}")
        print(f"  预期收益: {decision.expected_return:.3f}")
        print(f"  最大损失: {decision.max_loss:.3f}")
        print(f"  预期持有期: {decision.holding_period}天")
        print(f"  决策原因: {decision.reasoning}")
        
        print(f"\n  风险评估:")
        for risk_type, risk_value in decision.risk_assessment.items():
            print(f"    {risk_type}: {risk_value:.3f}")
    
    print("\n3. 决策历史统计:")
    print("-" * 30)
    
    # 生成更多决策以展示统计功能
    for features in stocks_features[:3]:
        decision_engine.make_trading_decision(
            features, account_info, market_condition
        )
    
    # 获取决策摘要
    summary = decision_engine.get_decision_summary(days=1)
    
    print(f"  总决策数: {summary['total_decisions']}")
    if 'decision_types' in summary:
        print(f"  决策类型分布:")
        for decision_type, count in summary['decision_types'].items():
            print(f"    {decision_type}: {count}")
    
    if 'avg_confidence' in summary:
        print(f"  平均置信度: {summary['avg_confidence']:.3f}")
    
    if 'risk_levels' in summary:
        print(f"  风险等级分布:")
        for risk_level, count in summary['risk_levels'].items():
            print(f"    {risk_level}: {count}")


def demo_small_capital_strategy():
    """演示小资金全仓单股+做T策略"""
    print("=" * 60)
    print("6. 小资金策略演示（10万资金全仓单股+做T）")
    print("=" * 60)
    
    # 小资金专用配置
    small_capital_config = {
        'position_config': {
            'max_single_position': 1.0,    # 允许全仓单股
            'base_position_ratio': 0.75,   # 75%做底仓
            't_position_ratio': 0.20,      # 20%做T
            'cash_reserve_ratio': 0.05,    # 5%现金储备
            'risk_position_factor': 0.9    # 小资金可承受更高风险
        },
        'switching_config': {
            'score_improvement': 0.20,     # 更高的切换阈值
            'confidence_min': 0.7,         # 更高的置信度要求
            'holding_days_min': 2,         # 更短的最小持有期
            'risk_increase_max': 0.3       # 允许更高风险
        }
    }
    
    # 初始化小资金专用决策引擎
    small_capital_engine = DecisionEngine(small_capital_config)
    
    # 模拟小资金账户
    small_account = {
        'total_capital': 100000,        # 10万资金
        'current_position': 0.0,        # 当前空仓
        'current_drawdown': 0.0,        # 无回撤
        'liquidity_score': 0.9,         # 高流动性要求
        'market_beta': 1.0,
        'capital_tier': 'small'         # 小资金标识
    }
    
    # 创建适合小资金的股票候选（高爆发潜力）
    explosive_stocks = [
        ('000858.SZ', '五粮液', 0.9),      # 高爆发潜力
        ('002415.SZ', '海康威视', 0.85),   # 科技龙头
        ('000002.SZ', '万科A', 0.8),       # 地产龙头
        ('600519.SZ', '贵州茅台', 0.95),   # 超级龙头
        ('000001.SZ', '平安银行', 0.75)    # 金融龙头
    ]
    
    print("小资金策略股票筛选:")
    print("-" * 40)
    
    # 为小资金策略筛选最佳股票
    explosive_features = []
    for symbol, name, base_score in explosive_stocks:
        features = create_sample_stock_features(symbol, base_score)
        # 增强爆发潜力特征
        features['explosive_potential'] = base_score
        features['volume_surge'] = min(1.0, base_score + 0.1)
        features['catalyst_score'] = min(1.0, base_score + 0.05)
        explosive_features.append(features)
    
    # 批量评估并选择最佳标的
    top_explosive = small_capital_engine.batch_evaluate_stocks(explosive_features, top_n=1)
    
    if top_explosive:
        best_explosive = top_explosive[0]
        best_name = next(name for sym, name, _ in explosive_stocks if sym == best_explosive.symbol)
        
        print(f"选中股票: {best_name} ({best_explosive.symbol})")
        print(f"  综合评分: {best_explosive.total_score:.3f}")
        print(f"  爆发潜力: {best_explosive.explosive_potential:.3f}")
        print(f"  置信度: {best_explosive.confidence:.3f}")
        print(f"  风险等级: {best_explosive.risk_level}")
        
        # 制定小资金交易决策
        market_condition = {'trend': 'BULL', 'volatility': 0.3}
        best_features = next(f for f in explosive_features if f['symbol'] == best_explosive.symbol)
        
        decision = small_capital_engine.make_trading_decision(
            best_features, small_account, market_condition
        )
        
        print(f"\n小资金交易决策:")
        print(f"  决策类型: {decision.decision_type.value}")
        print(f"  目标仓位: {decision.target_position:.1%} (约{decision.target_position * 100000:.0f}元)")
        print(f"  底仓建议: {decision.target_position * 0.75:.1%} (约{decision.target_position * 0.75 * 100000:.0f}元)")
        print(f"  做T资金: {decision.target_position * 0.20:.1%} (约{decision.target_position * 0.20 * 100000:.0f}元)")
        print(f"  现金储备: 5% (约5000元)")
        print(f"  预期收益: {decision.expected_return:.1%}")
        print(f"  最大损失: {decision.max_loss:.1%}")
        print(f"  持有期: {decision.holding_period}天")
        print(f"  决策原因: {decision.reasoning}")
        
        print(f"\n做T策略说明:")
        print(f"  - 用75%资金({decision.target_position * 0.75 * 100000:.0f}元)建立底仓长期持有")
        print(f"  - 用20%资金({decision.target_position * 0.20 * 100000:.0f}元)进行日内T+0操作")
        print(f"  - 保留5%现金(5000元)应对极端情况")
        print(f"  - 通过做T降低持仓成本，等待股票爆发")
        print(f"  - AI模型实时监控，发现更优标的时切换底仓")


def main():
    """主演示函数"""
    print("🚀 qlib交易系统 - 决策引擎演示")
    print("=" * 80)
    print("本演示展示AI驱动的股票评分、动态切换决策、持仓决策和置信度评估功能")
    print("=" * 80)
    
    # 设置随机种子以获得可重现的结果
    np.random.seed(42)
    
    try:
        # 1. 股票评分系统演示
        demo_stock_scoring_system()
        
        # 2. 动态切换引擎演示
        demo_dynamic_switching_engine()
        
        # 3. 持仓决策引擎演示
        demo_position_decision_engine()
        
        # 4. 模型置信度评估器演示
        demo_model_confidence_evaluator()
        
        # 5. 集成决策引擎演示
        demo_integrated_decision_engine()
        
        # 6. 小资金策略演示
        demo_small_capital_strategy()
        
        print("\n" + "=" * 80)
        print("✅ 决策引擎演示完成！")
        print("=" * 80)
        print("\n核心功能总结:")
        print("1. ✅ AI驱动的股票评分系统 - 六维度综合评分")
        print("2. ✅ 动态股票切换决策逻辑 - 智能标的切换")
        print("3. ✅ 持仓决策和风险评估算法 - 精准仓位管理")
        print("4. ✅ 模型置信度评估机制 - 可靠性保障")
        print("5. ✅ 小资金全仓单股+做T策略 - 专为小资金优化")
        print("\n系统已准备就绪，可以开始实盘交易！🎯")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()