"""
模型性能评估和监控系统
提供实时性能监控、模型退化检测、性能报告生成等功能
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any, Callable
from dataclasses import dataclass, field
import logging
from datetime import datetime, timedelta
import json
import sqlite3
from pathlib import Path
# 可选的可视化依赖
try:
    import matplotlib.pyplot as plt
    import seaborn as sns
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    logger = logging.getLogger(__name__)
    logger.warning("matplotlib/seaborn不可用，可视化功能将被禁用")
from collections import deque, defaultdict
import warnings
warnings.filterwarnings('ignore')

from explosive_model import ExplosiveStockModel

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """性能指标"""
    accuracy: float = 0.0
    precision: float = 0.0
    recall: float = 0.0
    f1_score: float = 0.0
    auc: float = 0.0
    
    # 业务指标
    hit_rate: float = 0.0  # 爆发股命中率
    false_positive_rate: float = 0.0  # 误报率
    coverage: float = 0.0  # 覆盖率
    
    # 时间相关
    timestamp: datetime = field(default_factory=datetime.now)
    evaluation_period: str = "daily"
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'accuracy': self.accuracy,
            'precision': self.precision,
            'recall': self.recall,
            'f1_score': self.f1_score,
            'auc': self.auc,
            'hit_rate': self.hit_rate,
            'false_positive_rate': self.false_positive_rate,
            'coverage': self.coverage,
            'timestamp': self.timestamp.isoformat(),
            'evaluation_period': self.evaluation_period
        }

@dataclass
class AlertConfig:
    """告警配置"""
    # 性能阈值
    min_accuracy: float = 0.55
    min_precision: float = 0.45
    min_recall: float = 0.35
    min_f1_score: float = 0.4
    min_hit_rate: float = 0.3
    max_false_positive_rate: float = 0.7
    
    # 退化检测
    performance_drop_threshold: float = 0.05  # 5%性能下降
    consecutive_bad_days: int = 3
    
    # 数据质量
    min_daily_predictions: int = 10
    max_missing_data_ratio: float = 0.1

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, model: ExplosiveStockModel, alert_config: AlertConfig = None):
        self.model = model
        self.alert_config = alert_config or AlertConfig()
        
        # 性能历史
        self.performance_history = deque(maxlen=365)  # 保留一年历史
        self.daily_metrics = {}
        
        # 告警状态
        self.active_alerts = []
        self.alert_history = []
        
        # 数据库连接 - 使用项目data目录
        from pathlib import Path
        import os
        
        # 获取项目根目录
        current_dir = Path(__file__).parent
        project_root = current_dir.parent.parent.parent  # 回到项目根目录
        data_dir = project_root / "data" / "monitoring"
        
        # 创建监控数据目录
        data_dir.mkdir(parents=True, exist_ok=True)
        
        self.db_path = str(data_dir / "performance_monitor.db")
        self._init_database()
        
        logger.info("初始化性能监控器")
    
    def _init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建性能指标表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS performance_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                accuracy REAL,
                precision REAL,
                recall REAL,
                f1_score REAL,
                auc REAL,
                hit_rate REAL,
                false_positive_rate REAL,
                coverage REAL,
                evaluation_period TEXT,
                model_version TEXT
            )
        ''')
        
        # 创建预测记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS prediction_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                symbol TEXT NOT NULL,
                prediction_score REAL,
                actual_result INTEGER,
                evaluation_date TEXT,
                days_to_result INTEGER
            )
        ''')
        
        # 创建告警记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS alert_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                alert_type TEXT NOT NULL,
                severity TEXT NOT NULL,
                message TEXT,
                resolved BOOLEAN DEFAULT FALSE,
                resolved_timestamp TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def evaluate_daily_performance(self, predictions: pd.DataFrame, 
                                 actual_results: pd.DataFrame) -> PerformanceMetrics:
        """评估日度性能"""
        logger.info("评估日度性能...")
        
        # 合并预测和实际结果
        merged_data = predictions.merge(
            actual_results, 
            on=['symbol', 'date'], 
            how='inner'
        )
        
        if len(merged_data) == 0:
            logger.warning("没有匹配的预测和实际结果数据")
            return PerformanceMetrics()
        
        # 计算基础指标
        y_true = merged_data['actual_explosive'].values
        y_pred_prob = merged_data['prediction_score'].values
        y_pred = (y_pred_prob >= 0.5).astype(int)
        
        metrics = self._calculate_detailed_metrics(y_true, y_pred, y_pred_prob)
        
        # 计算业务指标
        business_metrics = self._calculate_business_metrics(merged_data)
        
        # 合并指标
        performance = PerformanceMetrics(
            accuracy=metrics['accuracy'],
            precision=metrics['precision'],
            recall=metrics['recall'],
            f1_score=metrics['f1_score'],
            auc=metrics['auc'],
            hit_rate=business_metrics['hit_rate'],
            false_positive_rate=business_metrics['false_positive_rate'],
            coverage=business_metrics['coverage'],
            evaluation_period="daily"
        )
        
        # 保存到历史记录
        self.performance_history.append(performance)
        
        # 保存到数据库
        self._save_performance_to_db(performance)
        
        # 检查告警
        self._check_performance_alerts(performance)
        
        logger.info(f"日度性能评估完成: Accuracy={performance.accuracy:.4f}, "
                   f"Hit Rate={performance.hit_rate:.4f}")
        
        return performance
    
    def _calculate_detailed_metrics(self, y_true: np.ndarray, y_pred: np.ndarray, 
                                  y_pred_prob: np.ndarray) -> Dict[str, float]:
        """计算详细指标"""
        from sklearn.metrics import (accuracy_score, precision_score, recall_score, 
                                   f1_score, roc_auc_score, confusion_matrix)
        
        metrics = {}
        
        try:
            metrics['accuracy'] = accuracy_score(y_true, y_pred)
            metrics['precision'] = precision_score(y_true, y_pred, zero_division=0)
            metrics['recall'] = recall_score(y_true, y_pred, zero_division=0)
            metrics['f1_score'] = f1_score(y_true, y_pred, zero_division=0)
            
            # AUC计算
            if len(np.unique(y_true)) > 1:
                metrics['auc'] = roc_auc_score(y_true, y_pred_prob)
            else:
                metrics['auc'] = 0.0
                
        except Exception as e:
            logger.error(f"计算指标时出错: {e}")
            metrics = {key: 0.0 for key in ['accuracy', 'precision', 'recall', 'f1_score', 'auc']}
        
        return metrics
    
    def _calculate_business_metrics(self, data: pd.DataFrame) -> Dict[str, float]:
        """计算业务指标"""
        total_predictions = len(data)
        
        # 命中率：预测为爆发股且实际爆发的比例
        predicted_explosive = data[data['prediction_score'] >= 0.5]
        if len(predicted_explosive) > 0:
            hit_rate = predicted_explosive['actual_explosive'].mean()
        else:
            hit_rate = 0.0
        
        # 误报率：预测为爆发股但实际未爆发的比例
        false_positives = len(predicted_explosive[predicted_explosive['actual_explosive'] == 0])
        false_positive_rate = false_positives / max(len(predicted_explosive), 1)
        
        # 覆盖率：实际爆发股中被预测到的比例
        actual_explosive = data[data['actual_explosive'] == 1]
        if len(actual_explosive) > 0:
            coverage = (actual_explosive['prediction_score'] >= 0.5).mean()
        else:
            coverage = 0.0
        
        return {
            'hit_rate': hit_rate,
            'false_positive_rate': false_positive_rate,
            'coverage': coverage
        }
    
    def _save_performance_to_db(self, performance: PerformanceMetrics):
        """保存性能指标到数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO performance_metrics 
            (timestamp, accuracy, precision, recall, f1_score, auc, 
             hit_rate, false_positive_rate, coverage, evaluation_period, model_version)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            performance.timestamp.isoformat(),
            performance.accuracy,
            performance.precision,
            performance.recall,
            performance.f1_score,
            performance.auc,
            performance.hit_rate,
            performance.false_positive_rate,
            performance.coverage,
            performance.evaluation_period,
            "v1.0"  # 模型版本
        ))
        
        conn.commit()
        conn.close()
    
    def _check_performance_alerts(self, performance: PerformanceMetrics):
        """检查性能告警"""
        alerts = []
        
        # 检查基础性能指标
        if performance.accuracy < self.alert_config.min_accuracy:
            alerts.append({
                'type': 'low_accuracy',
                'severity': 'high',
                'message': f'准确率过低: {performance.accuracy:.4f} < {self.alert_config.min_accuracy}'
            })
        
        if performance.precision < self.alert_config.min_precision:
            alerts.append({
                'type': 'low_precision',
                'severity': 'medium',
                'message': f'精确率过低: {performance.precision:.4f} < {self.alert_config.min_precision}'
            })
        
        if performance.recall < self.alert_config.min_recall:
            alerts.append({
                'type': 'low_recall',
                'severity': 'medium',
                'message': f'召回率过低: {performance.recall:.4f} < {self.alert_config.min_recall}'
            })
        
        # 检查业务指标
        if performance.hit_rate < self.alert_config.min_hit_rate:
            alerts.append({
                'type': 'low_hit_rate',
                'severity': 'high',
                'message': f'爆发股命中率过低: {performance.hit_rate:.4f} < {self.alert_config.min_hit_rate}'
            })
        
        if performance.false_positive_rate > self.alert_config.max_false_positive_rate:
            alerts.append({
                'type': 'high_false_positive',
                'severity': 'medium',
                'message': f'误报率过高: {performance.false_positive_rate:.4f} > {self.alert_config.max_false_positive_rate}'
            })
        
        # 检查性能退化
        degradation_alert = self._check_performance_degradation()
        if degradation_alert:
            alerts.append(degradation_alert)
        
        # 保存告警
        for alert in alerts:
            self._save_alert(alert)
            self.active_alerts.append(alert)
        
        if alerts:
            logger.warning(f"检测到 {len(alerts)} 个性能告警")
    
    def _check_performance_degradation(self) -> Optional[Dict[str, Any]]:
        """检查性能退化"""
        if len(self.performance_history) < 7:  # 需要至少一周数据
            return None
        
        # 计算最近7天和之前7天的平均性能
        recent_performances = list(self.performance_history)[-7:]
        previous_performances = list(self.performance_history)[-14:-7]
        
        recent_f1 = np.mean([p.f1_score for p in recent_performances])
        previous_f1 = np.mean([p.f1_score for p in previous_performances])
        
        performance_drop = previous_f1 - recent_f1
        
        if performance_drop > self.alert_config.performance_drop_threshold:
            return {
                'type': 'performance_degradation',
                'severity': 'high',
                'message': f'模型性能退化: F1下降 {performance_drop:.4f} '
                          f'(从 {previous_f1:.4f} 降至 {recent_f1:.4f})'
            }
        
        return None
    
    def _save_alert(self, alert: Dict[str, Any]):
        """保存告警到数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO alert_records (timestamp, alert_type, severity, message)
            VALUES (?, ?, ?, ?)
        ''', (
            datetime.now().isoformat(),
            alert['type'],
            alert['severity'],
            alert['message']
        ))
        
        conn.commit()
        conn.close()
        
        self.alert_history.append(alert)
    
    def record_predictions(self, predictions: pd.DataFrame):
        """记录预测结果"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        for _, row in predictions.iterrows():
            cursor.execute('''
                INSERT INTO prediction_records 
                (timestamp, symbol, prediction_score, evaluation_date)
                VALUES (?, ?, ?, ?)
            ''', (
                datetime.now().isoformat(),
                row['symbol'],
                row['prediction_score'],
                row.get('evaluation_date', '')
            ))
        
        conn.commit()
        conn.close()
        
        logger.info(f"记录了 {len(predictions)} 个预测结果")
    
    def update_prediction_results(self, results: pd.DataFrame):
        """更新预测结果"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        for _, row in results.iterrows():
            cursor.execute('''
                UPDATE prediction_records 
                SET actual_result = ?, days_to_result = ?
                WHERE symbol = ? AND evaluation_date = ?
            ''', (
                int(row['actual_explosive']),
                row.get('days_to_result', 0),
                row['symbol'],
                row['evaluation_date']
            ))
        
        conn.commit()
        conn.close()
        
        logger.info(f"更新了 {len(results)} 个预测结果")
    
    def generate_performance_report(self, days: int = 30) -> str:
        """生成性能报告"""
        # 获取历史数据
        recent_performances = list(self.performance_history)[-days:]
        
        if not recent_performances:
            return "暂无性能数据"
        
        report = []
        report.append(f"=== 模型性能报告 (最近{days}天) ===\n")
        
        # 基础统计
        accuracies = [p.accuracy for p in recent_performances]
        hit_rates = [p.hit_rate for p in recent_performances]
        f1_scores = [p.f1_score for p in recent_performances]
        
        report.append("基础性能指标:")
        report.append(f"  平均准确率: {np.mean(accuracies):.4f} ± {np.std(accuracies):.4f}")
        report.append(f"  平均命中率: {np.mean(hit_rates):.4f} ± {np.std(hit_rates):.4f}")
        report.append(f"  平均F1得分: {np.mean(f1_scores):.4f} ± {np.std(f1_scores):.4f}")
        
        # 最佳和最差表现
        best_day = max(recent_performances, key=lambda x: x.f1_score)
        worst_day = min(recent_performances, key=lambda x: x.f1_score)
        
        report.append(f"\n最佳表现日期: {best_day.timestamp.strftime('%Y-%m-%d')}")
        report.append(f"  F1得分: {best_day.f1_score:.4f}, 命中率: {best_day.hit_rate:.4f}")
        
        report.append(f"\n最差表现日期: {worst_day.timestamp.strftime('%Y-%m-%d')}")
        report.append(f"  F1得分: {worst_day.f1_score:.4f}, 命中率: {worst_day.hit_rate:.4f}")
        
        # 趋势分析
        if len(recent_performances) >= 7:
            first_week = recent_performances[:7]
            last_week = recent_performances[-7:]
            
            first_week_f1 = np.mean([p.f1_score for p in first_week])
            last_week_f1 = np.mean([p.f1_score for p in last_week])
            
            trend = "上升" if last_week_f1 > first_week_f1 else "下降"
            change = abs(last_week_f1 - first_week_f1)
            
            report.append(f"\n性能趋势: {trend} ({change:.4f})")
        
        # 告警统计
        recent_alerts = [a for a in self.alert_history 
                        if datetime.fromisoformat(a.get('timestamp', '1970-01-01')) 
                        > datetime.now() - timedelta(days=days)]
        
        report.append(f"\n告警统计 (最近{days}天):")
        report.append(f"  总告警数: {len(recent_alerts)}")
        
        alert_types = defaultdict(int)
        for alert in recent_alerts:
            alert_types[alert['type']] += 1
        
        for alert_type, count in alert_types.items():
            report.append(f"  {alert_type}: {count}")
        
        return "\n".join(report)
    
    def plot_performance_trends(self, days: int = 30, save_path: str = None):
        """绘制性能趋势图"""
        if not MATPLOTLIB_AVAILABLE:
            logger.warning("matplotlib不可用，跳过图表绘制")
            return
            
        recent_performances = list(self.performance_history)[-days:]
        
        if len(recent_performances) < 2:
            logger.warning("数据不足，无法绘制趋势图")
            return
        
        # 准备数据
        dates = [p.timestamp for p in recent_performances]
        accuracies = [p.accuracy for p in recent_performances]
        hit_rates = [p.hit_rate for p in recent_performances]
        f1_scores = [p.f1_score for p in recent_performances]
        
        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 准确率趋势
        axes[0, 0].plot(dates, accuracies, marker='o', linewidth=2)
        axes[0, 0].set_title('准确率趋势')
        axes[0, 0].set_ylabel('准确率')
        axes[0, 0].grid(True, alpha=0.3)
        axes[0, 0].tick_params(axis='x', rotation=45)
        
        # 命中率趋势
        axes[0, 1].plot(dates, hit_rates, marker='s', color='orange', linewidth=2)
        axes[0, 1].set_title('爆发股命中率趋势')
        axes[0, 1].set_ylabel('命中率')
        axes[0, 1].grid(True, alpha=0.3)
        axes[0, 1].tick_params(axis='x', rotation=45)
        
        # F1得分趋势
        axes[1, 0].plot(dates, f1_scores, marker='^', color='green', linewidth=2)
        axes[1, 0].set_title('F1得分趋势')
        axes[1, 0].set_ylabel('F1得分')
        axes[1, 0].grid(True, alpha=0.3)
        axes[1, 0].tick_params(axis='x', rotation=45)
        
        # 综合性能雷达图
        metrics = ['准确率', '精确率', '召回率', 'F1得分', '命中率']
        latest_performance = recent_performances[-1]
        values = [
            latest_performance.accuracy,
            latest_performance.precision,
            latest_performance.recall,
            latest_performance.f1_score,
            latest_performance.hit_rate
        ]
        
        angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False)
        values += values[:1]  # 闭合图形
        angles = np.concatenate((angles, [angles[0]]))
        
        axes[1, 1] = plt.subplot(2, 2, 4, projection='polar')
        axes[1, 1].plot(angles, values, 'o-', linewidth=2)
        axes[1, 1].fill(angles, values, alpha=0.25)
        axes[1, 1].set_xticks(angles[:-1])
        axes[1, 1].set_xticklabels(metrics)
        axes[1, 1].set_ylim(0, 1)
        axes[1, 1].set_title('最新性能雷达图')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"性能趋势图已保存: {save_path}")
        
        plt.show()
    
    def get_active_alerts(self) -> List[Dict[str, Any]]:
        """获取活跃告警"""
        return self.active_alerts
    
    def resolve_alert(self, alert_type: str):
        """解决告警"""
        self.active_alerts = [a for a in self.active_alerts if a['type'] != alert_type]
        
        # 更新数据库
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE alert_records 
            SET resolved = TRUE, resolved_timestamp = ?
            WHERE alert_type = ? AND resolved = FALSE
        ''', (datetime.now().isoformat(), alert_type))
        
        conn.commit()
        conn.close()
        
        logger.info(f"已解决告警: {alert_type}")
    
    def get_model_health_score(self) -> float:
        """获取模型健康度评分"""
        if not self.performance_history:
            return 0.0
        
        # 使用最近7天的平均性能
        recent_performances = list(self.performance_history)[-7:]
        
        if not recent_performances:
            return 0.0
        
        # 计算各项指标的平均值
        avg_accuracy = np.mean([p.accuracy for p in recent_performances])
        avg_hit_rate = np.mean([p.hit_rate for p in recent_performances])
        avg_f1 = np.mean([p.f1_score for p in recent_performances])
        
        # 计算告警惩罚
        alert_penalty = min(len(self.active_alerts) * 0.1, 0.3)
        
        # 综合健康度评分
        health_score = (avg_accuracy * 0.3 + avg_hit_rate * 0.4 + avg_f1 * 0.3) - alert_penalty
        
        return max(0.0, min(1.0, health_score))

class ModelComparisonAnalyzer:
    """模型对比分析器"""
    
    def __init__(self):
        self.model_performances = {}
    
    def add_model_performance(self, model_name: str, performance_data: List[PerformanceMetrics]):
        """添加模型性能数据"""
        self.model_performances[model_name] = performance_data
    
    def compare_models(self) -> Dict[str, Any]:
        """对比模型性能"""
        if len(self.model_performances) < 2:
            logger.warning("需要至少两个模型进行对比")
            return {}
        
        comparison_results = {}
        
        for model_name, performances in self.model_performances.items():
            if not performances:
                continue
                
            # 计算平均性能
            avg_metrics = {
                'accuracy': np.mean([p.accuracy for p in performances]),
                'precision': np.mean([p.precision for p in performances]),
                'recall': np.mean([p.recall for p in performances]),
                'f1_score': np.mean([p.f1_score for p in performances]),
                'hit_rate': np.mean([p.hit_rate for p in performances]),
                'stability': np.std([p.f1_score for p in performances])  # 稳定性
            }
            
            comparison_results[model_name] = avg_metrics
        
        # 找出最佳模型
        best_model = max(comparison_results.keys(), 
                        key=lambda x: comparison_results[x]['f1_score'])
        
        return {
            'model_performances': comparison_results,
            'best_model': best_model,
            'comparison_date': datetime.now().isoformat()
        }
    
    def generate_comparison_report(self) -> str:
        """生成对比报告"""
        comparison = self.compare_models()
        
        if not comparison:
            return "暂无模型对比数据"
        
        report = []
        report.append("=== 模型性能对比报告 ===\n")
        
        report.append(f"最佳模型: {comparison['best_model']}\n")
        
        report.append("各模型性能对比:")
        for model_name, metrics in comparison['model_performances'].items():
            report.append(f"\n{model_name}:")
            report.append(f"  准确率: {metrics['accuracy']:.4f}")
            report.append(f"  精确率: {metrics['precision']:.4f}")
            report.append(f"  召回率: {metrics['recall']:.4f}")
            report.append(f"  F1得分: {metrics['f1_score']:.4f}")
            report.append(f"  命中率: {metrics['hit_rate']:.4f}")
            report.append(f"  稳定性: {metrics['stability']:.4f}")
        
        return "\n".join(report)