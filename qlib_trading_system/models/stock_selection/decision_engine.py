"""
模型决策引擎
实现AI驱动的股票评分、动态切换决策、持仓决策和风险评估
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
import logging
from enum import Enum
import warnings
warnings.filterwarnings('ignore')

try:
    from qlib_trading_system.utils.logging.logger import get_logger
    logger = get_logger(__name__)
    
    def get_logger_safe(name):
        return get_logger(name)
except ImportError:
    import logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    def get_logger_safe(name):
        return logging.getLogger(name)


class DecisionType(Enum):
    """决策类型枚举"""
    HOLD = "HOLD"           # 持有
    BUY = "BUY"             # 买入
    SELL = "SELL"           # 卖出
    SWITCH = "SWITCH"       # 切换
    REDUCE = "REDUCE"       # 减仓
    INCREASE = "INCREASE"   # 加仓


class ConfidenceLevel(Enum):
    """置信度等级"""
    VERY_LOW = 0.2
    LOW = 0.4
    MEDIUM = 0.6
    HIGH = 0.8
    VERY_HIGH = 0.9


@dataclass
class StockScore:
    """股票评分结果"""
    symbol: str
    total_score: float
    dimension_scores: Dict[str, float]
    confidence: float
    risk_level: str
    explosive_potential: float
    timestamp: datetime
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            'symbol': self.symbol,
            'total_score': self.total_score,
            'dimension_scores': self.dimension_scores,
            'confidence': self.confidence,
            'risk_level': self.risk_level,
            'explosive_potential': self.explosive_potential,
            'timestamp': self.timestamp.isoformat()
        }


@dataclass
class TradingDecision:
    """交易决策结果"""
    symbol: str
    decision_type: DecisionType
    confidence: float
    target_position: float  # 目标仓位比例 (0-1)
    reasoning: str
    risk_assessment: Dict[str, float]
    expected_return: float
    max_loss: float
    holding_period: int  # 预期持有天数
    timestamp: datetime
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            'symbol': self.symbol,
            'decision_type': self.decision_type.value,
            'confidence': self.confidence,
            'target_position': self.target_position,
            'reasoning': self.reasoning,
            'risk_assessment': self.risk_assessment,
            'expected_return': self.expected_return,
            'max_loss': self.max_loss,
            'holding_period': self.holding_period,
            'timestamp': self.timestamp.isoformat()
        }


class StockScoringSystem:
    """AI驱动的股票评分系统"""
    
    def __init__(self, config: Dict = None):
        """
        初始化股票评分系统
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        self.logger = get_logger_safe(f"{__name__}.StockScoringSystem")
        
        # 六维度权重配置
        self.dimension_weights = self.config.get('dimension_weights', {
            'fundamental': 0.25,    # 基本面权重
            'valuation': 0.20,      # 估值权重  
            'technical': 0.25,      # 技术面权重
            'sentiment': 0.15,      # 情绪权重
            'risk': -0.10,          # 风险权重（负向）
            'market': 0.05          # 大盘权重
        })
        
        # 评分阈值配置
        self.score_thresholds = self.config.get('score_thresholds', {
            'excellent': 0.8,       # 优秀
            'good': 0.6,           # 良好
            'average': 0.4,        # 一般
            'poor': 0.2            # 较差
        })
        
        # 爆发潜力评估参数
        self.explosive_params = self.config.get('explosive_params', {
            'momentum_weight': 0.3,
            'volume_weight': 0.2,
            'catalyst_weight': 0.3,
            'timing_weight': 0.2
        })
        
        self.logger.info("股票评分系统初始化完成")
    
    def calculate_stock_score(self, features: Dict[str, Any]) -> StockScore:
        """
        计算股票综合评分
        
        Args:
            features: 股票特征数据
            
        Returns:
            StockScore: 评分结果
        """
        try:
            symbol = features.get('symbol', 'UNKNOWN')
            
            # 计算各维度评分
            dimension_scores = {}
            
            # 基本面评分
            dimension_scores['fundamental'] = self._calculate_fundamental_score(features)
            
            # 估值评分
            dimension_scores['valuation'] = self._calculate_valuation_score(features)
            
            # 技术面评分
            dimension_scores['technical'] = self._calculate_technical_score(features)
            
            # 情绪评分
            dimension_scores['sentiment'] = self._calculate_sentiment_score(features)
            
            # 风险评分
            dimension_scores['risk'] = self._calculate_risk_score(features)
            
            # 大盘评分
            dimension_scores['market'] = self._calculate_market_score(features)
            
            # 计算加权总分
            total_score = 0
            for dimension, score in dimension_scores.items():
                weight = self.dimension_weights.get(dimension, 0)
                total_score += score * weight
            
            # 限制评分范围
            total_score = max(0, min(1, total_score))
            
            # 计算置信度
            confidence = self._calculate_confidence(dimension_scores, features)
            
            # 评估风险等级
            risk_level = self._assess_risk_level(dimension_scores['risk'])
            
            # 计算爆发潜力
            explosive_potential = self._calculate_explosive_potential(features)
            
            score_result = StockScore(
                symbol=symbol,
                total_score=total_score,
                dimension_scores=dimension_scores,
                confidence=confidence,
                risk_level=risk_level,
                explosive_potential=explosive_potential,
                timestamp=datetime.now()
            )
            
            self.logger.debug(f"股票 {symbol} 评分完成: {total_score:.3f}")
            return score_result
            
        except Exception as e:
            self.logger.error(f"计算股票评分失败: {e}")
            # 返回默认评分
            return StockScore(
                symbol=features.get('symbol', 'UNKNOWN'),
                total_score=0.5,
                dimension_scores={},
                confidence=0.1,
                risk_level='HIGH',
                explosive_potential=0.0,
                timestamp=datetime.now()
            )
    
    def _calculate_fundamental_score(self, features: Dict) -> float:
        """计算基本面评分"""
        try:
            score = 0.5  # 基础分
            
            # ROE增长率
            roe_growth = features.get('roe_growth', 0)
            if roe_growth > 0.2:
                score += 0.2
            elif roe_growth > 0.1:
                score += 0.1
            
            # 营收增长率
            revenue_growth = features.get('revenue_growth', 0)
            if revenue_growth > 0.3:
                score += 0.2
            elif revenue_growth > 0.15:
                score += 0.1
            
            # 净利润增长率
            profit_growth = features.get('profit_growth', 0)
            if profit_growth > 0.5:
                score += 0.2
            elif profit_growth > 0.2:
                score += 0.1
            
            # 财务质量
            debt_ratio = features.get('debt_ratio', 0.5)
            if debt_ratio < 0.3:
                score += 0.1
            elif debt_ratio > 0.7:
                score -= 0.1
            
            return max(0, min(1, score))
            
        except Exception as e:
            self.logger.warning(f"基本面评分计算失败: {e}")
            return 0.5
    
    def _calculate_valuation_score(self, features: Dict) -> float:
        """计算估值评分"""
        try:
            score = 0.5
            
            # PE估值
            pe_ratio = features.get('pe_ratio', 20)
            industry_pe = features.get('industry_pe', 25)
            
            if pe_ratio < industry_pe * 0.7:  # 显著低估
                score += 0.3
            elif pe_ratio < industry_pe * 0.9:  # 轻微低估
                score += 0.1
            elif pe_ratio > industry_pe * 1.5:  # 高估
                score -= 0.2
            
            # PB估值
            pb_ratio = features.get('pb_ratio', 2)
            if pb_ratio < 1.5:
                score += 0.2
            elif pb_ratio > 5:
                score -= 0.2
            
            # PEG比率
            peg_ratio = features.get('peg_ratio', 1)
            if peg_ratio < 0.8:
                score += 0.2
            elif peg_ratio > 2:
                score -= 0.1
            
            return max(0, min(1, score))
            
        except Exception as e:
            self.logger.warning(f"估值评分计算失败: {e}")
            return 0.5
    
    def _calculate_technical_score(self, features: Dict) -> float:
        """计算技术面评分"""
        try:
            score = 0.5
            
            # 价格动量
            momentum_20 = features.get('momentum_20', 0)
            if momentum_20 > 0.1:
                score += 0.2
            elif momentum_20 < -0.1:
                score -= 0.2
            
            # 成交量放大
            volume_ratio = features.get('volume_ratio', 1)
            if volume_ratio > 2:
                score += 0.2
            elif volume_ratio < 0.5:
                score -= 0.1
            
            # 技术指标
            rsi = features.get('rsi', 50)
            if 30 < rsi < 70:  # 正常区间
                score += 0.1
            elif rsi > 80 or rsi < 20:  # 极端区间
                score -= 0.1
            
            # 突破信号
            breakout_signal = features.get('breakout_signal', 0)
            if breakout_signal > 0.7:
                score += 0.3
            
            return max(0, min(1, score))
            
        except Exception as e:
            self.logger.warning(f"技术面评分计算失败: {e}")
            return 0.5
    
    def _calculate_sentiment_score(self, features: Dict) -> float:
        """计算情绪评分"""
        try:
            score = 0.5
            
            # 新闻情绪
            news_sentiment = features.get('news_sentiment', 0)
            if news_sentiment > 0.6:
                score += 0.3
            elif news_sentiment < -0.3:
                score -= 0.2
            
            # 资金流向
            money_flow = features.get('money_flow', 0)
            if money_flow > 0.2:
                score += 0.2
            elif money_flow < -0.2:
                score -= 0.2
            
            # 机构关注度
            institution_attention = features.get('institution_attention', 0)
            if institution_attention > 0.7:
                score += 0.2
            
            return max(0, min(1, score))
            
        except Exception as e:
            self.logger.warning(f"情绪评分计算失败: {e}")
            return 0.5
    
    def _calculate_risk_score(self, features: Dict) -> float:
        """计算风险评分（越高风险越大）"""
        try:
            risk_score = 0.5
            
            # 波动率风险
            volatility = features.get('volatility', 0.3)
            if volatility > 0.5:
                risk_score += 0.2
            elif volatility < 0.2:
                risk_score -= 0.1
            
            # 流动性风险
            liquidity = features.get('avg_turnover', 0)
            if liquidity < 0.01:  # 成交额过低
                risk_score += 0.3
            
            # 基本面风险
            st_risk = features.get('st_risk', 0)
            if st_risk > 0.5:
                risk_score += 0.4
            
            # 技术面风险
            drawdown = features.get('max_drawdown', 0)
            if drawdown > 0.3:
                risk_score += 0.2
            
            return max(0, min(1, risk_score))
            
        except Exception as e:
            self.logger.warning(f"风险评分计算失败: {e}")
            return 0.5
    
    def _calculate_market_score(self, features: Dict) -> float:
        """计算大盘环境评分"""
        try:
            score = 0.5
            
            # 大盘趋势
            market_trend = features.get('market_trend', 0)
            if market_trend > 0.1:
                score += 0.3
            elif market_trend < -0.1:
                score -= 0.2
            
            # 行业表现
            industry_performance = features.get('industry_performance', 0)
            if industry_performance > 0.05:
                score += 0.2
            elif industry_performance < -0.05:
                score -= 0.1
            
            return max(0, min(1, score))
            
        except Exception as e:
            self.logger.warning(f"大盘评分计算失败: {e}")
            return 0.5
    
    def _calculate_confidence(self, dimension_scores: Dict, features: Dict) -> float:
        """计算评分置信度"""
        try:
            # 基于数据完整性和一致性计算置信度
            data_completeness = features.get('data_completeness', 0.8)
            
            # 各维度评分的一致性
            scores = list(dimension_scores.values())
            score_std = np.std(scores) if len(scores) > 1 else 0
            consistency = max(0, 1 - score_std * 2)
            
            # 综合置信度
            confidence = (data_completeness * 0.6 + consistency * 0.4)
            
            return max(0.1, min(0.95, confidence))
            
        except Exception as e:
            self.logger.warning(f"置信度计算失败: {e}")
            return 0.5
    
    def _assess_risk_level(self, risk_score: float) -> str:
        """评估风险等级"""
        if risk_score < 0.3:
            return 'LOW'
        elif risk_score < 0.6:
            return 'MEDIUM'
        else:
            return 'HIGH'
    
    def _calculate_explosive_potential(self, features: Dict) -> float:
        """计算爆发潜力"""
        try:
            potential = 0
            
            # 动量因子
            momentum = features.get('momentum_20', 0)
            potential += momentum * self.explosive_params['momentum_weight']
            
            # 成交量因子
            volume_surge = features.get('volume_surge', 0)
            potential += volume_surge * self.explosive_params['volume_weight']
            
            # 催化剂因子
            catalyst_score = features.get('catalyst_score', 0)
            potential += catalyst_score * self.explosive_params['catalyst_weight']
            
            # 时机因子
            timing_score = features.get('timing_score', 0)
            potential += timing_score * self.explosive_params['timing_weight']
            
            return max(0, min(1, potential))
            
        except Exception as e:
            self.logger.warning(f"爆发潜力计算失败: {e}")
            return 0.0


class DynamicSwitchingEngine:
    """动态股票切换决策引擎"""
    
    def __init__(self, config: Dict = None):
        """
        初始化动态切换引擎
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        self.logger = get_logger_safe(f"{__name__}.DynamicSwitchingEngine")
        
        # 切换阈值配置
        self.switch_thresholds = self.config.get('switch_thresholds', {
            'score_improvement': 0.15,  # 评分提升阈值
            'confidence_min': 0.6,      # 最小置信度
            'holding_days_min': 3,      # 最小持有天数
            'risk_increase_max': 0.2    # 最大风险增加
        })
        
        # 持仓历史记录
        self.holding_history = {}
        
        self.logger.info("动态切换引擎初始化完成")
    
    def should_switch_stock(self, 
                           current_stock: str,
                           current_score: StockScore,
                           candidate_scores: List[StockScore],
                           holding_days: int) -> Tuple[bool, Optional[str], str]:
        """
        判断是否应该切换股票
        
        Args:
            current_stock: 当前持有股票
            current_score: 当前股票评分
            candidate_scores: 候选股票评分列表
            holding_days: 已持有天数
            
        Returns:
            Tuple[bool, Optional[str], str]: (是否切换, 目标股票, 切换原因)
        """
        try:
            # 检查最小持有期
            if holding_days < self.switch_thresholds['holding_days_min']:
                return False, None, f"未达到最小持有期 ({holding_days}/{self.switch_thresholds['holding_days_min']}天)"
            
            # 检查当前股票置信度
            if current_score.confidence < self.switch_thresholds['confidence_min']:
                # 寻找最佳替代品
                best_candidate = self._find_best_candidate(candidate_scores)
                if best_candidate:
                    return True, best_candidate.symbol, f"当前股票置信度过低 ({current_score.confidence:.3f})"
            
            # 寻找显著更优的候选股票
            for candidate in candidate_scores:
                if candidate.symbol == current_stock:
                    continue
                
                # 评分提升检查
                score_improvement = candidate.total_score - current_score.total_score
                if score_improvement < self.switch_thresholds['score_improvement']:
                    continue
                
                # 置信度检查
                if candidate.confidence < self.switch_thresholds['confidence_min']:
                    continue
                
                # 风险增加检查
                current_risk = current_score.dimension_scores.get('risk', 0.5)
                candidate_risk = candidate.dimension_scores.get('risk', 0.5)
                risk_increase = candidate_risk - current_risk
                
                if risk_increase > self.switch_thresholds['risk_increase_max']:
                    continue
                
                # 爆发潜力检查
                if candidate.explosive_potential > current_score.explosive_potential * 1.2:
                    reason = f"发现更优标的: 评分提升{score_improvement:.3f}, 爆发潜力提升{candidate.explosive_potential - current_score.explosive_potential:.3f}"
                    return True, candidate.symbol, reason
            
            return False, None, "未发现更优替代标的"
            
        except Exception as e:
            self.logger.error(f"股票切换判断失败: {e}")
            return False, None, f"判断失败: {e}"
    
    def _find_best_candidate(self, candidates: List[StockScore]) -> Optional[StockScore]:
        """寻找最佳候选股票"""
        if not candidates:
            return None
        
        # 按综合评分排序
        valid_candidates = [
            c for c in candidates 
            if c.confidence >= self.switch_thresholds['confidence_min']
        ]
        
        if not valid_candidates:
            return None
        
        # 综合评分 = 总评分 * 置信度 + 爆发潜力 * 0.3
        best_candidate = max(
            valid_candidates,
            key=lambda x: x.total_score * x.confidence + x.explosive_potential * 0.3
        )
        
        return best_candidate
    
    def update_holding_history(self, symbol: str, action: str, score: StockScore):
        """更新持仓历史记录"""
        if symbol not in self.holding_history:
            self.holding_history[symbol] = []
        
        self.holding_history[symbol].append({
            'timestamp': datetime.now(),
            'action': action,
            'score': score.total_score,
            'confidence': score.confidence,
            'explosive_potential': score.explosive_potential
        })
        
        # 保留最近30条记录
        if len(self.holding_history[symbol]) > 30:
            self.holding_history[symbol] = self.holding_history[symbol][-30:]


class PositionDecisionEngine:
    """持仓决策引擎"""
    
    def __init__(self, config: Dict = None):
        """
        初始化持仓决策引擎
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        self.logger = get_logger_safe(f"{__name__}.PositionDecisionEngine")
        
        # 仓位配置
        self.position_config = self.config.get('position_config', {
            'max_single_position': 1.0,    # 最大单股仓位（小资金模式）
            'base_position_ratio': 0.75,   # 底仓比例
            't_position_ratio': 0.20,      # 做T仓位比例
            'cash_reserve_ratio': 0.05,    # 现金储备比例
            'risk_position_factor': 0.8    # 风险仓位调整因子
        })
        
        self.logger.info("持仓决策引擎初始化完成")
    
    def calculate_target_position(self, 
                                 stock_score: StockScore,
                                 account_info: Dict,
                                 market_condition: Dict) -> TradingDecision:
        """
        计算目标仓位
        
        Args:
            stock_score: 股票评分
            account_info: 账户信息
            market_condition: 市场状况
            
        Returns:
            TradingDecision: 交易决策
        """
        try:
            # 基础仓位计算
            base_position = self._calculate_base_position(stock_score, account_info)
            
            # 风险调整
            risk_adjusted_position = self._apply_risk_adjustment(
                base_position, stock_score, account_info
            )
            
            # 市场环境调整
            market_adjusted_position = self._apply_market_adjustment(
                risk_adjusted_position, market_condition
            )
            
            # 生成决策
            decision = self._generate_position_decision(
                stock_score, market_adjusted_position, account_info
            )
            
            self.logger.debug(f"股票 {stock_score.symbol} 目标仓位: {market_adjusted_position:.3f}")
            return decision
            
        except Exception as e:
            self.logger.error(f"仓位计算失败: {e}")
            return self._generate_default_decision(stock_score)
    
    def _calculate_base_position(self, stock_score: StockScore, account_info: Dict) -> float:
        """计算基础仓位"""
        # 基于评分和爆发潜力计算基础仓位
        score_factor = stock_score.total_score
        explosive_factor = stock_score.explosive_potential
        confidence_factor = stock_score.confidence
        
        # 综合因子
        combined_factor = (score_factor * 0.4 + 
                          explosive_factor * 0.4 + 
                          confidence_factor * 0.2)
        
        # 基础仓位
        base_position = combined_factor * self.position_config['max_single_position']
        
        return min(base_position, self.position_config['max_single_position'])
    
    def _apply_risk_adjustment(self, base_position: float, 
                              stock_score: StockScore, 
                              account_info: Dict) -> float:
        """应用风险调整"""
        # 风险等级调整
        risk_multiplier = {
            'LOW': 1.0,
            'MEDIUM': 0.8,
            'HIGH': 0.6
        }.get(stock_score.risk_level, 0.8)
        
        # 账户风险调整
        current_drawdown = account_info.get('current_drawdown', 0)
        if current_drawdown > 0.15:  # 回撤超过15%
            risk_multiplier *= 0.7
        elif current_drawdown > 0.25:  # 回撤超过25%
            risk_multiplier *= 0.5
        
        return base_position * risk_multiplier
    
    def _apply_market_adjustment(self, position: float, market_condition: Dict) -> float:
        """应用市场环境调整"""
        market_trend = market_condition.get('trend', 'NEUTRAL')
        volatility = market_condition.get('volatility', 0.3)
        
        # 市场趋势调整
        trend_multiplier = {
            'BULL': 1.2,      # 牛市加仓
            'BEAR': 0.6,      # 熊市减仓
            'NEUTRAL': 1.0    # 震荡市保持
        }.get(market_trend, 1.0)
        
        # 波动率调整（高波动率适度减仓）
        if volatility > 0.5:
            volatility_multiplier = 0.8
        elif volatility > 0.7:
            volatility_multiplier = 0.6
        else:
            volatility_multiplier = 1.0
        
        adjusted_position = position * trend_multiplier * volatility_multiplier
        
        return min(adjusted_position, self.position_config['max_single_position'])
    
    def _generate_position_decision(self, 
                                   stock_score: StockScore,
                                   target_position: float,
                                   account_info: Dict) -> TradingDecision:
        """生成持仓决策"""
        current_position = account_info.get('current_position', 0)
        position_diff = target_position - current_position
        
        # 决策类型判断
        if abs(position_diff) < 0.05:  # 差异小于5%
            decision_type = DecisionType.HOLD
        elif position_diff > 0.1:  # 需要加仓
            decision_type = DecisionType.INCREASE
        elif position_diff < -0.1:  # 需要减仓
            decision_type = DecisionType.REDUCE
        elif target_position > 0.8:  # 高仓位买入
            decision_type = DecisionType.BUY
        elif target_position < 0.2:  # 低仓位卖出
            decision_type = DecisionType.SELL
        else:
            decision_type = DecisionType.HOLD
        
        # 风险评估
        risk_assessment = {
            'position_risk': target_position * stock_score.dimension_scores.get('risk', 0.5),
            'concentration_risk': target_position,  # 集中度风险
            'liquidity_risk': 1 - account_info.get('liquidity_score', 0.8),
            'market_risk': account_info.get('market_beta', 1.0)
        }
        
        # 预期收益和最大损失
        expected_return = stock_score.explosive_potential * target_position
        max_loss = target_position * stock_score.dimension_scores.get('risk', 0.5) * 0.3
        
        # 预期持有期
        holding_period = self._estimate_holding_period(stock_score)
        
        # 决策原因
        reasoning = self._generate_reasoning(stock_score, target_position, decision_type)
        
        return TradingDecision(
            symbol=stock_score.symbol,
            decision_type=decision_type,
            confidence=stock_score.confidence,
            target_position=target_position,
            reasoning=reasoning,
            risk_assessment=risk_assessment,
            expected_return=expected_return,
            max_loss=max_loss,
            holding_period=holding_period,
            timestamp=datetime.now()
        )
    
    def _estimate_holding_period(self, stock_score: StockScore) -> int:
        """估算持有期"""
        # 基于爆发潜力和技术指标估算持有期
        explosive_potential = stock_score.explosive_potential
        
        if explosive_potential > 0.8:
            return 30  # 高爆发潜力，短期持有
        elif explosive_potential > 0.6:
            return 60  # 中等潜力，中期持有
        else:
            return 90  # 低潜力，长期持有
    
    def _generate_reasoning(self, stock_score: StockScore, 
                           target_position: float, 
                           decision_type: DecisionType) -> str:
        """生成决策原因"""
        reasons = []
        
        # 评分原因
        if stock_score.total_score > 0.8:
            reasons.append(f"综合评分优秀({stock_score.total_score:.3f})")
        elif stock_score.total_score > 0.6:
            reasons.append(f"综合评分良好({stock_score.total_score:.3f})")
        else:
            reasons.append(f"综合评分一般({stock_score.total_score:.3f})")
        
        # 爆发潜力原因
        if stock_score.explosive_potential > 0.7:
            reasons.append(f"爆发潜力强({stock_score.explosive_potential:.3f})")
        
        # 风险原因
        if stock_score.risk_level == 'HIGH':
            reasons.append("风险等级较高")
        elif stock_score.risk_level == 'LOW':
            reasons.append("风险等级较低")
        
        # 置信度原因
        if stock_score.confidence > 0.8:
            reasons.append(f"模型置信度高({stock_score.confidence:.3f})")
        elif stock_score.confidence < 0.5:
            reasons.append(f"模型置信度低({stock_score.confidence:.3f})")
        
        return "; ".join(reasons)
    
    def _generate_default_decision(self, stock_score: StockScore) -> TradingDecision:
        """生成默认决策"""
        return TradingDecision(
            symbol=stock_score.symbol,
            decision_type=DecisionType.HOLD,
            confidence=0.1,
            target_position=0.0,
            reasoning="决策计算失败，采用保守策略",
            risk_assessment={'total_risk': 1.0},
            expected_return=0.0,
            max_loss=0.0,
            holding_period=1,
            timestamp=datetime.now()
        )


class ModelConfidenceEvaluator:
    """模型置信度评估器"""
    
    def __init__(self, config: Dict = None):
        """
        初始化置信度评估器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        self.logger = get_logger_safe(f"{__name__}.ModelConfidenceEvaluator")
        
        # 历史预测记录
        self.prediction_history = []
        
        # 置信度评估参数
        self.confidence_params = self.config.get('confidence_params', {
            'history_window': 30,      # 历史窗口天数
            'accuracy_weight': 0.4,    # 准确率权重
            'consistency_weight': 0.3, # 一致性权重
            'stability_weight': 0.3    # 稳定性权重
        })
        
        self.logger.info("模型置信度评估器初始化完成")
    
    def evaluate_model_confidence(self, 
                                 model_predictions: List[Dict],
                                 actual_results: List[Dict] = None) -> Dict[str, float]:
        """
        评估模型置信度
        
        Args:
            model_predictions: 模型预测结果
            actual_results: 实际结果（用于计算准确率）
            
        Returns:
            Dict[str, float]: 置信度评估结果
        """
        try:
            confidence_metrics = {}
            
            # 预测一致性评估
            consistency_score = self._evaluate_prediction_consistency(model_predictions)
            confidence_metrics['consistency'] = consistency_score
            
            # 预测稳定性评估
            stability_score = self._evaluate_prediction_stability(model_predictions)
            confidence_metrics['stability'] = stability_score
            
            # 如果有实际结果，计算准确率
            if actual_results:
                accuracy_score = self._calculate_prediction_accuracy(
                    model_predictions, actual_results
                )
                confidence_metrics['accuracy'] = accuracy_score
            else:
                confidence_metrics['accuracy'] = 0.5  # 默认值
            
            # 数据质量评估
            data_quality_score = self._evaluate_data_quality(model_predictions)
            confidence_metrics['data_quality'] = data_quality_score
            
            # 综合置信度计算
            overall_confidence = self._calculate_overall_confidence(confidence_metrics)
            confidence_metrics['overall'] = overall_confidence
            
            # 置信度等级
            confidence_level = self._determine_confidence_level(overall_confidence)
            confidence_metrics['level'] = confidence_level.value
            
            self.logger.debug(f"模型置信度评估完成: {overall_confidence:.3f}")
            return confidence_metrics
            
        except Exception as e:
            self.logger.error(f"置信度评估失败: {e}")
            return {
                'consistency': 0.5,
                'stability': 0.5,
                'accuracy': 0.5,
                'data_quality': 0.5,
                'overall': 0.5,
                'level': ConfidenceLevel.MEDIUM.value
            }
    
    def _evaluate_prediction_consistency(self, predictions: List[Dict]) -> float:
        """评估预测一致性"""
        if len(predictions) < 2:
            return 0.5
        
        try:
            # 计算预测分数的标准差
            scores = [p.get('score', 0.5) for p in predictions]
            score_std = np.std(scores)
            
            # 一致性分数（标准差越小一致性越高）
            consistency = max(0, 1 - score_std * 2)
            
            return min(1, consistency)
            
        except Exception as e:
            self.logger.warning(f"一致性评估失败: {e}")
            return 0.5
    
    def _evaluate_prediction_stability(self, predictions: List[Dict]) -> float:
        """评估预测稳定性"""
        if len(predictions) < 3:
            return 0.5
        
        try:
            # 计算预测趋势的稳定性
            scores = [p.get('score', 0.5) for p in predictions]
            
            # 计算相邻预测的变化率
            changes = []
            for i in range(1, len(scores)):
                change = abs(scores[i] - scores[i-1])
                changes.append(change)
            
            # 稳定性分数（变化率越小稳定性越高）
            avg_change = np.mean(changes)
            stability = max(0, 1 - avg_change * 5)
            
            return min(1, stability)
            
        except Exception as e:
            self.logger.warning(f"稳定性评估失败: {e}")
            return 0.5
    
    def _calculate_prediction_accuracy(self, 
                                     predictions: List[Dict],
                                     actual_results: List[Dict]) -> float:
        """计算预测准确率"""
        if not predictions or not actual_results:
            return 0.5
        
        try:
            correct_predictions = 0
            total_predictions = min(len(predictions), len(actual_results))
            
            for i in range(total_predictions):
                pred_score = predictions[i].get('score', 0.5)
                actual_return = actual_results[i].get('return', 0)
                
                # 简单的准确率计算：预测分数高且实际收益为正
                if pred_score > 0.6 and actual_return > 0:
                    correct_predictions += 1
                elif pred_score <= 0.6 and actual_return <= 0:
                    correct_predictions += 1
            
            accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0.5
            return min(1, accuracy)
            
        except Exception as e:
            self.logger.warning(f"准确率计算失败: {e}")
            return 0.5
    
    def _evaluate_data_quality(self, predictions: List[Dict]) -> float:
        """评估数据质量"""
        if not predictions:
            return 0.5
        
        try:
            quality_score = 0.5
            
            # 检查数据完整性
            complete_predictions = sum(
                1 for p in predictions 
                if all(key in p for key in ['symbol', 'score', 'timestamp'])
            )
            completeness = complete_predictions / len(predictions)
            quality_score += completeness * 0.3
            
            # 检查数据合理性
            reasonable_scores = sum(
                1 for p in predictions 
                if 0 <= p.get('score', 0.5) <= 1
            )
            reasonableness = reasonable_scores / len(predictions)
            quality_score += reasonableness * 0.2
            
            return min(1, quality_score)
            
        except Exception as e:
            self.logger.warning(f"数据质量评估失败: {e}")
            return 0.5
    
    def _calculate_overall_confidence(self, metrics: Dict[str, float]) -> float:
        """计算综合置信度"""
        weights = self.confidence_params
        
        overall = (
            metrics['accuracy'] * weights['accuracy_weight'] +
            metrics['consistency'] * weights['consistency_weight'] +
            metrics['stability'] * weights['stability_weight']
        )
        
        # 数据质量调整
        overall *= metrics['data_quality']
        
        return max(0.1, min(0.95, overall))
    
    def _determine_confidence_level(self, confidence: float) -> ConfidenceLevel:
        """确定置信度等级"""
        if confidence >= 0.8:
            return ConfidenceLevel.VERY_HIGH
        elif confidence >= 0.7:
            return ConfidenceLevel.HIGH
        elif confidence >= 0.5:
            return ConfidenceLevel.MEDIUM
        elif confidence >= 0.3:
            return ConfidenceLevel.LOW
        else:
            return ConfidenceLevel.VERY_LOW
    
    def update_prediction_history(self, prediction: Dict, actual_result: Dict = None):
        """更新预测历史记录"""
        record = {
            'timestamp': datetime.now(),
            'prediction': prediction,
            'actual_result': actual_result
        }
        
        self.prediction_history.append(record)
        
        # 保留指定窗口的历史记录
        window_size = self.confidence_params['history_window']
        if len(self.prediction_history) > window_size:
            self.prediction_history = self.prediction_history[-window_size:]


class DecisionEngine:
    """主决策引擎 - 整合所有决策组件"""
    
    def __init__(self, config: Dict = None):
        """
        初始化决策引擎
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        self.logger = get_logger_safe(f"{__name__}.DecisionEngine")
        
        # 初始化各个子引擎
        self.scoring_system = StockScoringSystem(
            self.config.get('scoring_config', {})
        )
        
        self.switching_engine = DynamicSwitchingEngine(
            self.config.get('switching_config', {})
        )
        
        self.position_engine = PositionDecisionEngine(
            self.config.get('position_config', {})
        )
        
        self.confidence_evaluator = ModelConfidenceEvaluator(
            self.config.get('confidence_config', {})
        )
        
        # 决策历史记录
        self.decision_history = []
        
        self.logger.info("主决策引擎初始化完成")
    
    def make_trading_decision(self, 
                             stock_features: Dict[str, Any],
                             account_info: Dict,
                             market_condition: Dict,
                             current_holdings: Dict = None) -> TradingDecision:
        """
        制定交易决策
        
        Args:
            stock_features: 股票特征数据
            account_info: 账户信息
            market_condition: 市场状况
            current_holdings: 当前持仓
            
        Returns:
            TradingDecision: 交易决策
        """
        try:
            # 1. 计算股票评分
            stock_score = self.scoring_system.calculate_stock_score(stock_features)
            
            # 2. 评估模型置信度
            confidence_metrics = self.confidence_evaluator.evaluate_model_confidence([{
                'symbol': stock_score.symbol,
                'score': stock_score.total_score,
                'timestamp': datetime.now()
            }])
            
            # 更新股票评分的置信度
            stock_score.confidence = min(stock_score.confidence, confidence_metrics['overall'])
            
            # 3. 检查是否需要切换股票
            should_switch = False
            switch_target = None
            switch_reason = ""
            
            if current_holdings:
                current_stock = list(current_holdings.keys())[0] if current_holdings else None
                if current_stock and current_stock != stock_score.symbol:
                    # 获取当前股票评分
                    current_features = stock_features.copy()
                    current_features['symbol'] = current_stock
                    current_score = self.scoring_system.calculate_stock_score(current_features)
                    
                    # 判断是否切换
                    holding_days = current_holdings.get(current_stock, {}).get('holding_days', 0)
                    should_switch, switch_target, switch_reason = self.switching_engine.should_switch_stock(
                        current_stock, current_score, [stock_score], holding_days
                    )
            
            # 4. 计算目标仓位
            if should_switch and switch_target:
                # 如果需要切换，先清仓再建仓
                decision = TradingDecision(
                    symbol=switch_target,
                    decision_type=DecisionType.SWITCH,
                    confidence=stock_score.confidence,
                    target_position=0.8,  # 切换后的目标仓位
                    reasoning=f"股票切换: {switch_reason}",
                    risk_assessment={'switch_risk': 0.3},
                    expected_return=stock_score.explosive_potential,
                    max_loss=0.2,
                    holding_period=60,
                    timestamp=datetime.now()
                )
            else:
                # 正常仓位决策
                decision = self.position_engine.calculate_target_position(
                    stock_score, account_info, market_condition
                )
            
            # 5. 记录决策历史
            self._record_decision(decision, stock_score, confidence_metrics)
            
            self.logger.info(f"交易决策完成: {decision.symbol} - {decision.decision_type.value}")
            return decision
            
        except Exception as e:
            self.logger.error(f"交易决策制定失败: {e}")
            # 返回保守决策
            return TradingDecision(
                symbol=stock_features.get('symbol', 'UNKNOWN'),
                decision_type=DecisionType.HOLD,
                confidence=0.1,
                target_position=0.0,
                reasoning=f"决策失败: {e}",
                risk_assessment={'error_risk': 1.0},
                expected_return=0.0,
                max_loss=0.0,
                holding_period=1,
                timestamp=datetime.now()
            )
    
    def batch_evaluate_stocks(self, 
                             stocks_features: List[Dict],
                             top_n: int = 10) -> List[StockScore]:
        """
        批量评估股票并返回排序结果
        
        Args:
            stocks_features: 股票特征数据列表
            top_n: 返回前N只股票
            
        Returns:
            List[StockScore]: 排序后的股票评分列表
        """
        try:
            scores = []
            
            for features in stocks_features:
                try:
                    score = self.scoring_system.calculate_stock_score(features)
                    scores.append(score)
                except Exception as e:
                    self.logger.warning(f"股票 {features.get('symbol', 'UNKNOWN')} 评分失败: {e}")
                    continue
            
            # 按综合评分排序
            scores.sort(key=lambda x: x.total_score * x.confidence + x.explosive_potential * 0.3, 
                       reverse=True)
            
            return scores[:top_n]
            
        except Exception as e:
            self.logger.error(f"批量股票评估失败: {e}")
            return []
    
    def get_decision_summary(self, days: int = 7) -> Dict:
        """
        获取决策摘要统计
        
        Args:
            days: 统计天数
            
        Returns:
            Dict: 决策摘要
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            recent_decisions = [
                d for d in self.decision_history 
                if d['timestamp'] >= cutoff_date
            ]
            
            if not recent_decisions:
                return {'total_decisions': 0}
            
            # 统计决策类型分布
            decision_types = {}
            for decision in recent_decisions:
                dt = decision['decision'].decision_type.value
                decision_types[dt] = decision_types.get(dt, 0) + 1
            
            # 统计平均置信度
            avg_confidence = np.mean([
                d['decision'].confidence for d in recent_decisions
            ])
            
            # 统计风险分布
            risk_levels = {}
            for decision in recent_decisions:
                risk = decision['stock_score'].risk_level
                risk_levels[risk] = risk_levels.get(risk, 0) + 1
            
            return {
                'total_decisions': len(recent_decisions),
                'decision_types': decision_types,
                'avg_confidence': avg_confidence,
                'risk_levels': risk_levels,
                'period_days': days
            }
            
        except Exception as e:
            self.logger.error(f"决策摘要统计失败: {e}")
            return {'error': str(e)}
    
    def _record_decision(self, decision: TradingDecision, 
                        stock_score: StockScore,
                        confidence_metrics: Dict):
        """记录决策历史"""
        record = {
            'timestamp': datetime.now(),
            'decision': decision,
            'stock_score': stock_score,
            'confidence_metrics': confidence_metrics
        }
        
        self.decision_history.append(record)
        
        # 保留最近100条记录
        if len(self.decision_history) > 100:
            self.decision_history = self.decision_history[-100:]


# 导出主要类
__all__ = [
    'DecisionEngine',
    'StockScoringSystem', 
    'DynamicSwitchingEngine',
    'PositionDecisionEngine',
    'ModelConfidenceEvaluator',
    'StockScore',
    'TradingDecision',
    'DecisionType',
    'ConfidenceLevel'
]