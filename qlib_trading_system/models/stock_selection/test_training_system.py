"""
模型训练和更新系统完整测试
测试增量学习、版本管理、性能监控和A/B测试的集成功能
"""

import os
import sys
import json
import time
import unittest
import tempfile
import shutil
from datetime import datetime, timedelta
from pathlib import Path
import numpy as np
import pandas as pd

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent.parent.parent))

from qlib_trading_system.models.stock_selection.model_training_system import (
    ModelTrainingSystem, TrainingConfig, SystemStatus
)
from qlib_trading_system.models.stock_selection.training_config_manager import (
    TrainingConfigManager, ModelConfig, DataConfig, MonitoringConfig
)
from qlib_trading_system.models.stock_selection.incremental_trainer import IncrementalConfig
from qlib_trading_system.models.stock_selection.performance_monitor_advanced import (
    PerformanceMetric, MetricType
)
from qlib_trading_system.utils.logging.logger import get_logger

logger = get_logger(__name__)


class TestModelTrainingSystem(unittest.TestCase):
    """模型训练系统测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时目录
        self.temp_dir = tempfile.mkdtemp()
        self.test_dir = Path(self.temp_dir) / "test_training_system"
        self.test_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建测试配置
        self.config_manager = TrainingConfigManager(str(self.test_dir / "config"))
        
        # 创建训练系统
        training_config = self.config_manager.get_training_config()
        training_config.enable_ab_testing = True
        training_config.ab_test_duration_days = 1  # 缩短测试时间
        
        self.training_system = ModelTrainingSystem(
            training_config, 
            str(self.test_dir / "training_system")
        )
        
        # 生成测试数据
        np.random.seed(42)
        self.X_train = np.random.randn(1000, 20)
        self.y_train = np.random.randn(1000)
        self.X_val = np.random.randn(200, 20)
        self.y_val = np.random.randn(200)
        
        logger.info(f"测试环境准备完成，临时目录: {self.temp_dir}")
    
    def tearDown(self):
        """测试后清理"""
        try:
            # 停止所有服务
            self.training_system.stop_monitoring()
            self.training_system.stop_scheduler()
            
            # 清理临时目录
            shutil.rmtree(self.temp_dir, ignore_errors=True)
            logger.info("测试环境清理完成")
        except Exception as e:
            logger.error(f"清理测试环境失败: {e}")
    
    def test_01_config_manager(self):
        """测试配置管理器"""
        logger.info("开始测试配置管理器...")
        
        # 测试获取配置
        model_config = self.config_manager.get_model_config()
        self.assertIsInstance(model_config, ModelConfig)
        self.assertEqual(model_config.model_type, "lightgbm")
        
        data_config = self.config_manager.get_data_config()
        self.assertIsInstance(data_config, DataConfig)
        self.assertEqual(data_config.primary_data_source, "itick")
        
        # 测试配置验证
        validation_results = self.config_manager.validate_all_configs()
        for config_type, errors in validation_results.items():
            self.assertEqual(len(errors), 0, f"配置验证失败 {config_type}: {errors}")
        
        # 测试配置导出和导入
        export_path = self.test_dir / "exported_config.json"
        self.config_manager.export_config(str(export_path))
        self.assertTrue(export_path.exists())
        
        # 测试配置更新
        new_model_config = {
            "model_type": "xgboost",
            "learning_rate": 0.05,
            "num_leaves": 50
        }
        self.config_manager.update_config("model", new_model_config)
        updated_config = self.config_manager.get_model_config()
        self.assertEqual(updated_config.model_type, "xgboost")
        self.assertEqual(updated_config.learning_rate, 0.05)
        
        logger.info("配置管理器测试通过")
    
    def test_02_base_model_initialization(self):
        """测试基础模型初始化"""
        logger.info("开始测试基础模型初始化...")
        
        # 初始化基础模型
        base_version = self.training_system.initialize_base_model(
            self.X_train, self.y_train, self.X_val, self.y_val,
            model_name="测试爆发股模型",
            description="用于测试的爆发股识别模型"
        )
        
        # 验证结果
        self.assertIsNotNone(base_version)
        self.assertTrue(base_version.startswith("v"))
        
        # 验证系统状态
        status = self.training_system.get_system_status()
        self.assertEqual(status['system_status'], SystemStatus.IDLE.value)
        self.assertEqual(status['active_model_version'], base_version)
        self.assertTrue(status['incremental_trainer_info']['model_exists'])
        
        # 验证版本管理器
        active_version = self.training_system.version_manager.get_active_version()
        self.assertEqual(active_version, base_version)
        
        # 验证模型可以加载
        model, metadata = self.training_system.version_manager.load_active_model()
        self.assertIsNotNone(model)
        self.assertEqual(metadata.name, "测试爆发股模型")
        
        logger.info(f"基础模型初始化测试通过，版本: {base_version}")
    
    def test_03_incremental_training(self):
        """测试增量训练"""
        logger.info("开始测试增量训练...")
        
        # 先初始化基础模型
        base_version = self.training_system.initialize_base_model(
            self.X_train, self.y_train, self.X_val, self.y_val
        )
        
        # 添加增量数据
        X_new = np.random.randn(300, 20)
        y_new = np.random.randn(300)
        self.training_system.add_training_data(X_new, y_new)
        
        # 检查数据缓冲区
        buffer_size = self.training_system.incremental_trainer.data_buffer.size()
        self.assertEqual(buffer_size, 300)
        
        # 触发增量更新
        update_result = self.training_system.trigger_incremental_update()
        
        # 验证更新结果
        self.assertIn('status', update_result)
        self.assertIn(update_result['status'], ['success', 'ab_test_started', 'improvement_insufficient'])
        
        if update_result['status'] == 'success':
            self.assertIn('new_version', update_result)
            self.assertIn('improvement', update_result)
        
        # 验证训练历史
        history = self.training_system.training_history
        self.assertGreater(len(history), 0)
        
        logger.info(f"增量训练测试通过，结果: {update_result['status']}")
    
    def test_04_performance_monitoring(self):
        """测试性能监控"""
        logger.info("开始测试性能监控...")
        
        # 初始化基础模型
        base_version = self.training_system.initialize_base_model(
            self.X_train, self.y_train, self.X_val, self.y_val
        )
        
        # 启动监控
        self.training_system.start_monitoring()
        self.assertTrue(self.training_system.performance_monitor.is_monitoring)
        
        # 添加性能指标
        for i in range(10):
            metric = PerformanceMetric(
                name="accuracy",
                value=0.75 + np.random.normal(0, 0.05),
                timestamp=datetime.now(),
                model_version=base_version,
                data_source="test"
            )
            self.training_system.performance_monitor.add_metric(metric)
        
        # 等待一小段时间让监控处理
        time.sleep(1)
        
        # 获取性能统计
        performance = self.training_system.performance_monitor.get_current_performance(
            MetricType.ACCURACY, base_version, 3600
        )
        
        self.assertEqual(performance['status'], 'success')
        self.assertEqual(performance['count'], 10)
        self.assertGreater(performance['mean'], 0.7)
        
        # 测试漂移检测
        drift_result = self.training_system.performance_monitor.detect_model_drift(
            MetricType.ACCURACY, base_version
        )
        # 由于数据量少，可能返回insufficient_data
        self.assertIn(drift_result['status'], ['success', 'insufficient_data'])
        
        # 停止监控
        self.training_system.stop_monitoring()
        self.assertFalse(self.training_system.performance_monitor.is_monitoring)
        
        logger.info("性能监控测试通过")
    
    def test_05_ab_testing(self):
        """测试A/B测试"""
        logger.info("开始测试A/B测试...")
        
        # 初始化基础模型
        base_version = self.training_system.initialize_base_model(
            self.X_train, self.y_train, self.X_val, self.y_val
        )
        
        # 创建第二个版本用于A/B测试
        X_new = np.random.randn(500, 20)
        y_new = np.random.randn(500)
        self.training_system.add_training_data(X_new, y_new)
        
        # 强制触发增量更新
        update_result = self.training_system.trigger_incremental_update(force=True)
        
        # 如果启动了A/B测试
        if update_result['status'] == 'ab_test_started':
            experiment_id = update_result['experiment_id']
            self.assertIsNotNone(experiment_id)
            self.assertEqual(self.training_system.current_experiment_id, experiment_id)
            
            # 测试用户分配
            user_assignments = {}
            for i in range(100):
                user_id = f"user_{i}"
                variant = self.training_system.ab_framework.assign_variant(
                    experiment_id, user_id
                )
                user_assignments[user_id] = variant
                
                # 模拟记录指标
                accuracy = 0.75 + (0.05 if variant == "treatment" else 0.0) + np.random.normal(0, 0.02)
                self.training_system.ab_framework.record_metric(
                    experiment_id, user_id, "accuracy", accuracy
                )
            
            # 检查分配统计
            assignment_stats = self.training_system.ab_framework.experiments[experiment_id].get_assignment_stats()
            self.assertEqual(assignment_stats['total_assignments'], 100)
            self.assertIn('control', assignment_stats['variant_counts'])
            self.assertIn('treatment', assignment_stats['variant_counts'])
            
            # 分析实验结果
            analysis = self.training_system.ab_framework.analyze_experiment(
                experiment_id, "accuracy"
            )
            
            if analysis.get('status') != 'no_data':
                self.assertIn('variant_results', analysis)
                self.assertIn('statistical_tests', analysis)
            
            # 停止实验
            success = self.training_system.ab_framework.stop_experiment(
                experiment_id, "测试完成"
            )
            self.assertTrue(success)
        
        logger.info("A/B测试测试通过")
    
    def test_06_version_management(self):
        """测试版本管理"""
        logger.info("开始测试版本管理...")
        
        # 初始化基础模型
        base_version = self.training_system.initialize_base_model(
            self.X_train, self.y_train, self.X_val, self.y_val
        )
        
        # 创建多个版本
        versions = [base_version]
        for i in range(3):
            X_new = np.random.randn(200, 20)
            y_new = np.random.randn(200)
            self.training_system.add_training_data(X_new, y_new)
            
            result = self.training_system.trigger_incremental_update(force=True)
            if 'new_version' in result:
                versions.append(result['new_version'])
        
        # 测试版本列表
        version_list = self.training_system.version_manager.list_versions()
        self.assertGreaterEqual(len(version_list), 1)
        
        # 测试版本比较
        if len(versions) >= 2:
            comparison = self.training_system.version_manager.compare_versions(
                versions[0], versions[1]
            )
            self.assertIn('performance_diff', comparison)
            self.assertIn('time_diff', comparison)
        
        # 测试版本回滚
        if len(versions) >= 2:
            rollback_success = self.training_system.rollback_model(
                versions[0], "测试回滚"
            )
            self.assertTrue(rollback_success)
            
            # 验证回滚后的活跃版本
            active_version = self.training_system.version_manager.get_active_version()
            self.assertEqual(active_version, versions[0])
        
        # 测试版本导出
        if versions:
            export_path = self.test_dir / "exported_version"
            success = self.training_system.version_manager.export_version(
                versions[0], str(export_path)
            )
            self.assertTrue(success)
            self.assertTrue(export_path.exists())
        
        logger.info("版本管理测试通过")
    
    def test_07_prediction_with_monitoring(self):
        """测试带监控的预测"""
        logger.info("开始测试带监控的预测...")
        
        # 初始化基础模型
        base_version = self.training_system.initialize_base_model(
            self.X_train, self.y_train, self.X_val, self.y_val
        )
        
        # 启动监控
        self.training_system.start_monitoring()
        
        # 执行预测
        X_test = np.random.randn(50, 20)
        predictions = self.training_system.predict_with_monitoring(X_test)
        
        # 验证预测结果
        self.assertEqual(len(predictions), 50)
        self.assertTrue(np.all(np.isfinite(predictions)))
        
        # 模拟预测反馈
        y_true = np.random.randn(50)
        self.training_system.record_prediction_feedback(
            X_test, predictions, y_true
        )
        
        # 检查是否记录了延迟指标
        time.sleep(1)  # 等待指标处理
        
        performance = self.training_system.performance_monitor.get_current_performance(
            MetricType.PREDICTION_LATENCY, base_version, 3600
        )
        
        if performance['status'] == 'success':
            self.assertGreater(performance['count'], 0)
            self.assertGreater(performance['mean'], 0)
        
        self.training_system.stop_monitoring()
        
        logger.info("带监控的预测测试通过")
    
    def test_08_system_report(self):
        """测试系统报告生成"""
        logger.info("开始测试系统报告生成...")
        
        # 初始化基础模型
        base_version = self.training_system.initialize_base_model(
            self.X_train, self.y_train, self.X_val, self.y_val
        )
        
        # 添加一些活动数据
        X_new = np.random.randn(100, 20)
        y_new = np.random.randn(100)
        self.training_system.add_training_data(X_new, y_new)
        
        # 生成系统报告
        report_path = self.test_dir / "system_report.json"
        success = self.training_system.generate_system_report(str(report_path))
        
        self.assertTrue(success)
        self.assertTrue(report_path.exists())
        
        # 验证报告内容
        with open(report_path, 'r', encoding='utf-8') as f:
            report = json.load(f)
        
        self.assertIn('report_time', report)
        self.assertIn('system_status', report)
        self.assertIn('model_versions', report)
        self.assertIn('training_history', report)
        
        # 验证系统状态
        system_status = report['system_status']
        self.assertEqual(system_status['active_model_version'], base_version)
        self.assertGreaterEqual(system_status['training_history_count'], 1)
        
        logger.info("系统报告生成测试通过")
    
    def test_09_error_handling(self):
        """测试错误处理"""
        logger.info("开始测试错误处理...")
        
        # 测试无效数据的处理
        try:
            invalid_X = np.array([])  # 空数组
            invalid_y = np.array([])
            self.training_system.initialize_base_model(invalid_X, invalid_y)
            self.fail("应该抛出异常")
        except Exception as e:
            logger.info(f"正确捕获异常: {e}")
        
        # 测试无效版本的处理
        try:
            self.training_system.rollback_model("invalid_version")
            self.fail("应该抛出异常")
        except Exception as e:
            logger.info(f"正确捕获异常: {e}")
        
        # 测试系统状态检查
        status = self.training_system.get_system_status()
        self.assertIn('system_status', status)
        
        logger.info("错误处理测试通过")
    
    def test_10_cleanup_and_maintenance(self):
        """测试清理和维护功能"""
        logger.info("开始测试清理和维护功能...")
        
        # 初始化基础模型
        base_version = self.training_system.initialize_base_model(
            self.X_train, self.y_train, self.X_val, self.y_val
        )
        
        # 创建多个版本
        for i in range(5):
            X_new = np.random.randn(100, 20)
            y_new = np.random.randn(100)
            self.training_system.add_training_data(X_new, y_new)
            self.training_system.trigger_incremental_update(force=True)
        
        # 测试清理旧版本
        initial_versions = len(self.training_system.version_manager.list_versions())
        self.training_system.cleanup_old_versions()
        
        # 验证版本数量（可能没有变化，因为版本都是新创建的）
        final_versions = len(self.training_system.version_manager.list_versions())
        self.assertLessEqual(final_versions, initial_versions)
        
        # 测试获取系统状态
        status = self.training_system.get_system_status()
        self.assertIsInstance(status, dict)
        self.assertIn('system_status', status)
        
        logger.info("清理和维护功能测试通过")


def run_integration_test():
    """运行集成测试"""
    logger.info("开始运行模型训练系统集成测试...")
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试用例（按顺序执行）
    test_cases = [
        'test_01_config_manager',
        'test_02_base_model_initialization',
        'test_03_incremental_training',
        'test_04_performance_monitoring',
        'test_05_ab_testing',
        'test_06_version_management',
        'test_07_prediction_with_monitoring',
        'test_08_system_report',
        'test_09_error_handling',
        'test_10_cleanup_and_maintenance'
    ]
    
    for test_case in test_cases:
        test_suite.addTest(TestModelTrainingSystem(test_case))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果
    if result.wasSuccessful():
        logger.info("所有测试通过！")
        print("\n" + "="*50)
        print("🎉 模型训练和更新系统集成测试全部通过！")
        print("="*50)
        return True
    else:
        logger.error(f"测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误")
        print("\n" + "="*50)
        print("❌ 部分测试失败，请检查日志")
        print(f"失败: {len(result.failures)}, 错误: {len(result.errors)}")
        print("="*50)
        return False


def run_performance_benchmark():
    """运行性能基准测试"""
    logger.info("开始运行性能基准测试...")
    
    # 创建临时测试环境
    temp_dir = tempfile.mkdtemp()
    test_dir = Path(temp_dir) / "benchmark"
    test_dir.mkdir(parents=True, exist_ok=True)
    
    try:
        # 创建配置和系统
        config_manager = TrainingConfigManager(str(test_dir / "config"))
        training_config = config_manager.get_training_config()
        training_system = ModelTrainingSystem(training_config, str(test_dir / "system"))
        
        # 生成大规模测试数据
        np.random.seed(42)
        X_train = np.random.randn(10000, 50)  # 更大的数据集
        y_train = np.random.randn(10000)
        X_val = np.random.randn(2000, 50)
        y_val = np.random.randn(2000)
        
        # 基准测试结果
        benchmark_results = {}
        
        # 1. 基础模型训练时间
        start_time = time.time()
        base_version = training_system.initialize_base_model(
            X_train, y_train, X_val, y_val
        )
        base_training_time = time.time() - start_time
        benchmark_results['base_model_training_time'] = base_training_time
        
        # 2. 增量训练时间
        X_new = np.random.randn(1000, 50)
        y_new = np.random.randn(1000)
        training_system.add_training_data(X_new, y_new)
        
        start_time = time.time()
        update_result = training_system.trigger_incremental_update(force=True)
        incremental_training_time = time.time() - start_time
        benchmark_results['incremental_training_time'] = incremental_training_time
        
        # 3. 预测时间
        X_test = np.random.randn(1000, 50)
        start_time = time.time()
        predictions = training_system.predict_with_monitoring(X_test)
        prediction_time = time.time() - start_time
        benchmark_results['prediction_time'] = prediction_time
        benchmark_results['prediction_throughput'] = len(X_test) / prediction_time
        
        # 4. 版本管理操作时间
        start_time = time.time()
        versions = training_system.version_manager.list_versions()
        version_list_time = time.time() - start_time
        benchmark_results['version_list_time'] = version_list_time
        
        # 输出基准测试结果
        print("\n" + "="*50)
        print("📊 性能基准测试结果")
        print("="*50)
        print(f"基础模型训练时间: {base_training_time:.2f} 秒")
        print(f"增量训练时间: {incremental_training_time:.2f} 秒")
        print(f"预测时间: {prediction_time:.4f} 秒")
        print(f"预测吞吐量: {benchmark_results['prediction_throughput']:.0f} 样本/秒")
        print(f"版本列表查询时间: {version_list_time:.4f} 秒")
        print("="*50)
        
        # 保存基准测试结果
        benchmark_file = test_dir / "benchmark_results.json"
        with open(benchmark_file, 'w', encoding='utf-8') as f:
            json.dump(benchmark_results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"性能基准测试完成，结果保存到: {benchmark_file}")
        
    finally:
        # 清理
        shutil.rmtree(temp_dir, ignore_errors=True)


if __name__ == "__main__":
    # 设置日志级别
    import logging
    logging.basicConfig(level=logging.INFO)
    
    print("🚀 开始模型训练和更新系统完整测试")
    print("="*60)
    
    # 运行集成测试
    integration_success = run_integration_test()
    
    # 运行性能基准测试
    if integration_success:
        print("\n🔥 开始性能基准测试...")
        run_performance_benchmark()
    
    print("\n✅ 所有测试完成！")