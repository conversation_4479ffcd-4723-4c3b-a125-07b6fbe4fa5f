"""
简化的特征工程管道测试

测试核心功能，不依赖外部机器学习库
"""

import pandas as pd
import numpy as np
import sys
import os
from datetime import datetime
import logging

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_feature_engine_integration():
    """测试特征引擎集成"""
    logger.info("=" * 60)
    logger.info("开始测试特征引擎集成")
    logger.info("=" * 60)
    
    try:
        # 导入特征引擎
        from qlib_trading_system.models.features.feature_engine import FeatureEngine
        
        # 创建特征引擎
        feature_engine = FeatureEngine()
        logger.info("✓ 特征引擎创建成功")
        
        # 创建模拟数据
        stock_data_dict = create_mock_data()
        logger.info("✓ 模拟数据创建成功")
        
        # 批量特征提取
        comprehensive_features = feature_engine.batch_feature_extraction(stock_data_dict)
        logger.info(f"✓ 批量特征提取成功，处理了 {len(comprehensive_features)} 只股票")
        
        # 获取爆发潜力最高的股票
        features_list = [(symbol, features) for symbol, features in comprehensive_features.items()]
        top_explosive = feature_engine.get_top_explosive_stocks(
            comprehensive_features, top_n=10, min_confidence=0.3
        )
        logger.info(f"✓ 爆发股筛选成功，找到 {len(top_explosive)} 只潜力股")
        
        # 显示结果
        if top_explosive:
            logger.info("Top 5 爆发潜力股票:")
            for i, (symbol, explosive_potential, confidence) in enumerate(top_explosive[:5]):
                logger.info(f"  {i+1}. {symbol}: 爆发潜力={explosive_potential:.3f}, 置信度={confidence:.3f}")
        
        # 获取特征统计
        stats = feature_engine.get_feature_statistics(comprehensive_features)
        if stats:
            logger.info("特征统计:")
            logger.info(f"  总股票数: {stats['total_stocks']}")
            logger.info(f"  平均综合评分: {stats['overall']['mean']:.3f}")
            logger.info(f"  平均爆发潜力: {stats['explosive']['mean']:.3f}")
        
        return True
        
    except Exception as e:
        logger.error(f"特征引擎集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def create_mock_data():
    """创建模拟数据"""
    stock_data_dict = {}
    
    for i in range(10):
        symbol = f"00000{i:02d}.SZ"
        
        # 创建价格数据
        dates = pd.date_range(start='2023-01-01', periods=50, freq='D')
        base_price = 10 + np.random.random() * 20
        
        price_data = pd.DataFrame({
            'open': base_price + np.random.normal(0, 0.5, 50),
            'high': base_price + np.random.normal(1, 0.5, 50),
            'low': base_price + np.random.normal(-1, 0.5, 50),
            'close': base_price + np.random.normal(0, 0.5, 50),
            'volume': np.random.lognormal(15, 1, 50),
            'amount': np.random.lognormal(18, 1, 50)
        }, index=dates)
        
        # 创建财务数据
        financial_data = pd.DataFrame({
            'revenue': [1e9 + np.random.normal(0, 1e8)],
            'net_profit': [1e8 + np.random.normal(0, 1e7)],
            'total_assets': [5e9 + np.random.normal(0, 1e9)],
            'total_equity': [2e9 + np.random.normal(0, 5e8)],
            'roe': [0.1 + np.random.normal(0, 0.05)],
            'roa': [0.05 + np.random.normal(0, 0.02)]
        })
        
        # 创建市场数据
        market_data = {
            'market_cap': base_price * 1e9,
            'pe_ratio': 15 + np.random.normal(0, 5),
            'pb_ratio': 1.5 + np.random.normal(0, 0.5),
            'dividend_yield': 0.02 + np.random.normal(0, 0.01)
        }
        
        stock_data_dict[symbol] = {
            'financial_data': financial_data,
            'price_data': price_data,
            'market_data': market_data,
            'news_data': [],
            'social_data': []
        }
    
    return stock_data_dict


def test_feature_pipeline_config():
    """测试特征管道配置"""
    logger.info("=" * 60)
    logger.info("开始测试特征管道配置")
    logger.info("=" * 60)
    
    try:
        # 测试配置类的导入和创建
        from feature_pipeline import FeaturePipelineConfig
        
        # 创建默认配置
        default_config = FeaturePipelineConfig()
        logger.info("✓ 默认配置创建成功")
        logger.info(f"  特征选择方法: {default_config.feature_selection_method}")
        logger.info(f"  最大特征数: {default_config.max_features}")
        logger.info(f"  标准化方法: {default_config.scaler_type}")
        
        # 创建自定义配置
        custom_config = FeaturePipelineConfig(
            feature_selection_method='random_forest',
            max_features=150,
            scaler_type='standard',
            enable_pca=True,
            enable_monitoring=True
        )
        logger.info("✓ 自定义配置创建成功")
        logger.info(f"  PCA启用: {custom_config.enable_pca}")
        logger.info(f"  监控启用: {custom_config.enable_monitoring}")
        
        return True
        
    except Exception as e:
        logger.error(f"特征管道配置测试失败: {e}")
        return False


def test_data_structures():
    """测试数据结构"""
    logger.info("=" * 60)
    logger.info("开始测试数据结构")
    logger.info("=" * 60)
    
    try:
        from feature_pipeline import PipelineMetrics
        from feature_importance_analyzer import ImportanceResult
        
        # 测试管道指标
        metrics = PipelineMetrics(
            processing_time=10.5,
            feature_count_before=200,
            feature_count_after=100,
            missing_value_ratio=0.05,
            outlier_ratio=0.02,
            feature_correlation_max=0.85,
            data_quality_score=0.92
        )
        logger.info("✓ 管道指标数据结构测试成功")
        logger.info(f"  处理时间: {metrics.processing_time}秒")
        logger.info(f"  数据质量分数: {metrics.data_quality_score}")
        
        # 测试重要性结果
        importance = ImportanceResult(
            feature_name="technical_rsi",
            importance_score=0.85,
            rank=1,
            category="technical",
            method="random_forest"
        )
        logger.info("✓ 重要性结果数据结构测试成功")
        logger.info(f"  特征名称: {importance.feature_name}")
        logger.info(f"  重要性分数: {importance.importance_score}")
        
        return True
        
    except Exception as e:
        logger.error(f"数据结构测试失败: {e}")
        return False


def test_basic_functionality():
    """测试基础功能"""
    logger.info("=" * 60)
    logger.info("开始测试基础功能")
    logger.info("=" * 60)
    
    success_count = 0
    total_tests = 3
    
    # 测试1: 特征引擎集成
    if test_feature_engine_integration():
        success_count += 1
        logger.info("✓ 特征引擎集成测试通过")
    else:
        logger.error("✗ 特征引擎集成测试失败")
    
    # 测试2: 配置测试
    if test_feature_pipeline_config():
        success_count += 1
        logger.info("✓ 特征管道配置测试通过")
    else:
        logger.error("✗ 特征管道配置测试失败")
    
    # 测试3: 数据结构测试
    if test_data_structures():
        success_count += 1
        logger.info("✓ 数据结构测试通过")
    else:
        logger.error("✗ 数据结构测试失败")
    
    # 总结
    logger.info("=" * 60)
    logger.info(f"测试完成: {success_count}/{total_tests} 通过")
    logger.info("=" * 60)
    
    if success_count == total_tests:
        logger.info("🎉 所有基础功能测试通过！")
        return True
    else:
        logger.error("❌ 部分测试失败！")
        return False


if __name__ == "__main__":
    # 运行基础功能测试
    success = test_basic_functionality()
    
    if success:
        logger.info("特征工程管道基础功能验证成功！")
        logger.info("任务 4.1 实现特征工程管道 - 完成")
        exit(0)
    else:
        logger.error("特征工程管道基础功能验证失败！")
        exit(1)