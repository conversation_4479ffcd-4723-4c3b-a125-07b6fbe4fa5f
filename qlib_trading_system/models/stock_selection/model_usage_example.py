"""
爆发股识别模型使用示例
演示如何加载和使用已训练的模型进行预测
"""

import numpy as np
import pandas as pd
import logging
from pathlib import Path
import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_latest_model():
    """加载最新的模型"""
    from explosive_model_robust import ExplosiveStockModel
    
    # 获取项目根目录
    current_dir = Path(__file__).parent
    project_root = current_dir.parent.parent.parent
    models_dir = project_root / "data" / "models"
    
    # 查找最新的模型文件
    model_files = list(models_dir.glob("explosive_stock_model_*.joblib"))
    
    if not model_files:
        logger.error("未找到已保存的模型文件")
        return None
    
    # 按修改时间排序，获取最新的模型
    latest_model_file = max(model_files, key=lambda x: x.stat().st_mtime)
    
    logger.info(f"加载模型: {latest_model_file}")
    
    # 创建模型实例并加载
    model = ExplosiveStockModel()
    model.load_model(str(latest_model_file))
    
    return model

def create_sample_prediction_data(n_stocks=10):
    """创建示例预测数据"""
    np.random.seed(123)  # 使用不同的随机种子
    
    # 生成50个特征的数据
    feature_names = []
    
    # 技术指标特征 (20个)
    for i in range(20):
        if i < 5:
            feature_names.append(f'price_feature_{i}')
        elif i < 10:
            feature_names.append(f'volume_feature_{i}')
        elif i < 15:
            feature_names.append(f'technical_feature_{i}')
        else:
            feature_names.append(f'momentum_feature_{i}')
    
    # 基本面特征 (15个)
    for i in range(15):
        if i < 5:
            feature_names.append(f'fundamental_feature_{i}')
        elif i < 10:
            feature_names.append(f'valuation_feature_{i}')
        else:
            feature_names.append(f'growth_feature_{i}')
    
    # 市场情绪特征 (10个)
    for i in range(10):
        feature_names.append(f'sentiment_feature_{i}')
    
    # 风险特征 (5个)
    for i in range(5):
        feature_names.append(f'risk_feature_{i}')
    
    # 生成随机数据
    data = {}
    for feature in feature_names:
        if 'price' in feature or 'momentum' in feature:
            # 价格和动量特征可能有趋势
            data[feature] = np.random.normal(0.5, 1.0, n_stocks)
        elif 'volume' in feature:
            # 成交量特征使用对数正态分布
            data[feature] = np.random.lognormal(0, 0.5, n_stocks)
        else:
            # 其他特征使用正态分布
            data[feature] = np.random.normal(0, 0.8, n_stocks)
    
    # 添加股票代码和日期
    data['symbol'] = [f'00{i:04d}.SZ' for i in range(n_stocks)]
    data['date'] = pd.Timestamp('2024-01-15')  # 使用固定日期
    
    df = pd.DataFrame(data)
    
    logger.info(f"创建了 {n_stocks} 只股票的预测数据")
    return df

def demonstrate_model_usage():
    """演示模型使用"""
    logger.info("=" * 60)
    logger.info("爆发股识别模型使用示例")
    logger.info("=" * 60)
    
    try:
        # 1. 加载最新模型
        logger.info("步骤 1: 加载最新训练的模型")
        model = load_latest_model()
        
        if model is None:
            logger.error("无法加载模型，请先运行训练")
            return False
        
        # 显示模型信息
        model_info = model.get_model_info()
        logger.info("模型信息:")
        logger.info(f"  是否已训练: {model_info['is_trained']}")
        logger.info(f"  特征数量: {model_info['feature_count']}")
        logger.info(f"  PyTorch可用: {model_info['torch_available']}")
        logger.info(f"  有LightGBM模型: {model_info['has_lgb_model']}")
        logger.info(f"  有LSTM模型: {model_info['has_lstm_model']}")
        
        # 2. 创建预测数据
        logger.info("\n步骤 2: 创建示例预测数据")
        prediction_data = create_sample_prediction_data(n_stocks=20)
        
        # 3. 进行预测
        logger.info("\n步骤 3: 进行爆发股预测")
        predictions = model.predict(prediction_data)
        
        logger.info(f"预测完成，共预测了 {len(predictions)} 只股票")
        logger.info(f"预测概率范围: [{predictions.min():.4f}, {predictions.max():.4f}]")
        
        # 4. 获取爆发股排名
        logger.info("\n步骤 4: 获取爆发股排名")
        top_stocks = model.predict_explosive_stocks(prediction_data, top_n=10)
        
        logger.info("Top 10 爆发潜力股票:")
        for i, (symbol, score) in enumerate(top_stocks):
            logger.info(f"  {i+1:2d}. {symbol}: {score:.4f}")
        
        # 5. 特征重要性分析
        logger.info("\n步骤 5: 特征重要性分析")
        feature_importance = model._get_feature_importance()
        
        if feature_importance:
            top_features = list(feature_importance.items())[:10]
            logger.info("Top 10 重要特征:")
            for i, (feature, importance) in enumerate(top_features):
                logger.info(f"  {i+1:2d}. {feature}: {importance:.2f}")
        
        # 6. 保存预测结果
        logger.info("\n步骤 6: 保存预测结果")
        
        # 创建预测结果DataFrame
        results_df = pd.DataFrame({
            'symbol': prediction_data['symbol'],
            'date': prediction_data['date'],
            'prediction_score': predictions,
            'rank': range(1, len(predictions) + 1)
        })
        
        # 按预测得分排序
        results_df = results_df.sort_values('prediction_score', ascending=False).reset_index(drop=True)
        results_df['rank'] = range(1, len(results_df) + 1)
        
        # 保存到项目data目录
        current_dir = Path(__file__).parent
        project_root = current_dir.parent.parent.parent
        results_dir = project_root / "data" / "predictions"
        results_dir.mkdir(parents=True, exist_ok=True)
        
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = results_dir / f"explosive_stock_predictions_{timestamp}.csv"
        
        results_df.to_csv(results_file, index=False, encoding='utf-8-sig')
        logger.info(f"预测结果已保存到: {results_file}")
        
        # 7. 使用建议
        logger.info("\n步骤 7: 使用建议")
        logger.info("模型使用建议:")
        logger.info("1. 定期重新训练模型以适应市场变化")
        logger.info("2. 结合基本面分析验证模型预测结果")
        logger.info("3. 设置合理的风险控制措施")
        logger.info("4. 关注Top 10-20的股票，避免过度集中")
        logger.info("5. 监控模型性能，及时调整参数")
        
        return True
        
    except Exception as e:
        logger.error(f"模型使用演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def batch_prediction_example():
    """批量预测示例"""
    logger.info("\n" + "=" * 60)
    logger.info("批量预测示例")
    logger.info("=" * 60)
    
    try:
        # 加载模型
        model = load_latest_model()
        if model is None:
            return False
        
        # 模拟多日预测数据
        all_predictions = []
        dates = pd.date_range('2024-01-15', periods=5, freq='D')
        
        for date in dates:
            logger.info(f"预测日期: {date.strftime('%Y-%m-%d')}")
            
            # 创建当日数据
            daily_data = create_sample_prediction_data(n_stocks=50)
            daily_data['date'] = date
            
            # 进行预测
            predictions = model.predict(daily_data)
            top_stocks = model.predict_explosive_stocks(daily_data, top_n=5)
            
            # 记录结果
            daily_result = {
                'date': date,
                'total_stocks': len(predictions),
                'avg_score': predictions.mean(),
                'max_score': predictions.max(),
                'top_5_stocks': [stock for stock, _ in top_stocks]
            }
            
            all_predictions.append(daily_result)
            
            logger.info(f"  总股票数: {daily_result['total_stocks']}")
            logger.info(f"  平均得分: {daily_result['avg_score']:.4f}")
            logger.info(f"  最高得分: {daily_result['max_score']:.4f}")
            logger.info(f"  Top 5: {', '.join(daily_result['top_5_stocks'])}")
        
        logger.info("\n批量预测完成！")
        return True
        
    except Exception as e:
        logger.error(f"批量预测失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("开始爆发股识别模型使用演示")
    
    # 基础使用演示
    success1 = demonstrate_model_usage()
    
    # 批量预测演示
    success2 = batch_prediction_example()
    
    if success1 and success2:
        logger.info("\n🎉 模型使用演示完成！")
        logger.info("您现在可以:")
        logger.info("1. 查看保存的预测结果文件")
        logger.info("2. 根据预测结果进行投资决策")
        logger.info("3. 定期重新训练模型")
        logger.info("4. 监控模型性能表现")
        return True
    else:
        logger.error("❌ 模型使用演示失败！")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)