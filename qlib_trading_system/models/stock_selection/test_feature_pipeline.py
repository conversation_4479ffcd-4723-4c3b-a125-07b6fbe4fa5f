"""
特征工程管道测试

测试特征提取、预处理、选择和监控的完整流程
验证各个组件的功能和集成效果
"""

import pandas as pd
import numpy as np
import sys
import os
from datetime import datetime, timedelta
import logging

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

from feature_pipeline import FeaturePipeline, FeaturePipelineConfig
from feature_selector import AdvancedFeatureSelector
from feature_importance_analyzer import FeatureImportanceAnalyzer
from feature_monitor import FeatureMonitoringSystem

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_mock_stock_data(n_stocks: int = 50, n_days: int = 100) -> dict:
    """创建模拟股票数据"""
    logger.info(f"创建模拟数据：{n_stocks}只股票，{n_days}天数据")
    
    stock_data_dict = {}
    
    for i in range(n_stocks):
        symbol = f"00000{i:02d}.SZ"
        
        # 创建价格数据
        dates = pd.date_range(start='2023-01-01', periods=n_days, freq='D')
        base_price = 10 + np.random.random() * 20
        
        price_data = pd.DataFrame({
            'date': dates,
            'open': base_price + np.random.normal(0, 0.5, n_days),
            'high': base_price + np.random.normal(1, 0.5, n_days),
            'low': base_price + np.random.normal(-1, 0.5, n_days),
            'close': base_price + np.random.normal(0, 0.5, n_days),
            'volume': np.random.lognormal(15, 1, n_days),
            'amount': np.random.lognormal(18, 1, n_days)
        })
        price_data.set_index('date', inplace=True)
        
        # 创建财务数据
        financial_data = pd.DataFrame({
            'report_date': ['2023-03-31', '2023-06-30', '2023-09-30', '2023-12-31'],
            'revenue': np.random.lognormal(20, 0.5, 4),
            'net_profit': np.random.lognormal(18, 0.8, 4),
            'total_assets': np.random.lognormal(22, 0.3, 4),
            'total_equity': np.random.lognormal(21, 0.4, 4),
            'roe': np.random.normal(0.1, 0.05, 4),
            'roa': np.random.normal(0.05, 0.03, 4),
            'debt_ratio': np.random.normal(0.4, 0.1, 4)
        })
        
        # 创建市场数据
        market_data = {
            'market_cap': base_price * np.random.lognormal(18, 0.5),
            'pe_ratio': np.random.normal(25, 10),
            'pb_ratio': np.random.normal(2, 1),
            'dividend_yield': np.random.normal(0.02, 0.01),
            'beta': np.random.normal(1, 0.3)
        }
        
        # 创建新闻数据（模拟）
        news_data = [
            {'title': f'股票{symbol}业绩预告', 'sentiment': np.random.normal(0, 1)},
            {'title': f'机构调研{symbol}', 'sentiment': np.random.normal(0.2, 0.8)},
            {'title': f'{symbol}技术突破', 'sentiment': np.random.normal(0.5, 0.5)}
        ]
        
        stock_data_dict[symbol] = {
            'price_data': price_data,
            'financial_data': financial_data,
            'market_data': market_data,
            'news_data': news_data,
            'social_data': [],
            'macro_data': {'gdp_growth': 0.05, 'inflation': 0.02},
            'flow_data': {'north_bound_flow': np.random.normal(0, 1e8)},
            'policy_data': {'policy_score': np.random.normal(0, 1)}
        }
    
    logger.info("模拟数据创建完成")
    return stock_data_dict


def create_mock_target_data(stock_symbols: list) -> dict:
    """创建模拟目标数据（未来3个月收益率）"""
    logger.info("创建模拟目标数据")
    
    target_data = {}
    for symbol in stock_symbols:
        # 模拟未来3个月收益率，部分股票有爆发潜力
        if np.random.random() < 0.2:  # 20%的股票是爆发股
            target_return = np.random.lognormal(0.5, 0.5)  # 爆发股收益率
        else:
            target_return = np.random.normal(0.1, 0.3)  # 普通股票收益率
        
        target_data[symbol] = max(target_return, -0.5)  # 限制最大亏损
    
    logger.info(f"目标数据创建完成，包含{len(target_data)}只股票")
    return target_data


def test_feature_pipeline():
    """测试特征工程管道"""
    logger.info("=" * 60)
    logger.info("开始测试特征工程管道")
    logger.info("=" * 60)
    
    # 1. 创建模拟数据
    stock_data_dict = create_mock_stock_data(n_stocks=30, n_days=100)
    target_data = create_mock_target_data(list(stock_data_dict.keys()))
    
    # 2. 配置管道
    config = FeaturePipelineConfig(
        feature_selection_method='mutual_info',
        max_features=100,
        scaler_type='robust',
        handle_missing='interpolate',
        enable_pca=False,
        enable_monitoring=True,
        enable_cache=True
    )
    
    # 3. 创建管道
    pipeline = FeaturePipeline(config)
    
    # 4. 训练和转换
    try:
        features_df, pipeline_info = pipeline.fit_transform(stock_data_dict, target_data)
        
        logger.info(f"特征工程完成:")
        logger.info(f"  最终特征数量: {len(features_df.columns)}")
        logger.info(f"  样本数量: {len(features_df)}")
        logger.info(f"  管道指标: {pipeline_info['metrics']}")
        
        # 显示部分特征
        logger.info(f"前10个特征: {list(features_df.columns[:10])}")
        
        return True, features_df, pipeline_info
        
    except Exception as e:
        logger.error(f"特征工程管道测试失败: {e}")
        return False, None, None


def test_feature_selection():
    """测试特征选择"""
    logger.info("=" * 60)
    logger.info("开始测试特征选择")
    logger.info("=" * 60)
    
    # 创建模拟特征数据
    n_samples, n_features = 100, 200
    X = pd.DataFrame(
        np.random.randn(n_samples, n_features),
        columns=[f'feature_{i}' for i in range(n_features)]
    )
    
    # 创建目标变量（部分特征与目标相关）
    important_features = X.iloc[:, :20]  # 前20个特征重要
    y = important_features.sum(axis=1) + np.random.randn(n_samples) * 0.1
    
    # 测试自动特征选择
    selector = AdvancedFeatureSelector()
    
    try:
        result = selector.auto_select_features(X, y)
        
        logger.info(f"自动特征选择完成:")
        logger.info(f"  最佳方法: {selector.best_method}")
        logger.info(f"  最佳CV分数: {selector.best_score:.4f}")
        logger.info(f"  选择特征数: {result.n_features_after}")
        
        # 获取比较结果
        comparison = selector.get_selection_comparison()
        logger.info(f"方法比较:")
        for _, row in comparison.iterrows():
            logger.info(f"  {row['method']}: {row['n_features']}个特征, "
                       f"CV分数: {row['cv_score']:.4f}")
        
        return True, result
        
    except Exception as e:
        logger.error(f"特征选择测试失败: {e}")
        return False, None


def test_importance_analysis():
    """测试特征重要性分析"""
    logger.info("=" * 60)
    logger.info("开始测试特征重要性分析")
    logger.info("=" * 60)
    
    # 创建模拟数据
    n_samples, n_features = 200, 100
    
    # 创建不同类别的特征
    feature_names = []
    for category in ['fundamental', 'technical', 'sentiment', 'valuation', 'risk']:
        for i in range(20):
            feature_names.append(f'{category}_feature_{i}')
    
    X = pd.DataFrame(
        np.random.randn(n_samples, n_features),
        columns=feature_names
    )
    
    # 让某些类别的特征更重要
    important_features = X.filter(regex='fundamental|technical').iloc[:, :10]
    y = important_features.sum(axis=1) + np.random.randn(n_samples) * 0.5
    
    # 测试重要性分析
    analyzer = FeatureImportanceAnalyzer()
    
    try:
        report = analyzer.comprehensive_importance_analysis(X, y)
        
        logger.info(f"重要性分析完成:")
        logger.info(f"  分析方法: {report.method}")
        logger.info(f"  总特征数: {report.total_features}")
        logger.info(f"  稳定性分数: {report.stability_score:.3f}")
        logger.info(f"  分析时间: {report.analysis_time:.2f}秒")
        
        # 显示top特征
        logger.info(f"Top 10 重要特征:")
        for i, result in enumerate(report.top_features[:10]):
            logger.info(f"  {i+1}. {result.feature_name}: {result.importance_score:.4f} ({result.category})")
        
        # 显示类别汇总
        logger.info(f"类别汇总:")
        for category, summary in report.category_summary.items():
            logger.info(f"  {summary['category_name']}: "
                       f"{summary['feature_count']}个特征, "
                       f"平均重要性: {summary['avg_importance']:.4f}")
        
        return True, report
        
    except Exception as e:
        logger.error(f"重要性分析测试失败: {e}")
        return False, None


def test_feature_monitoring():
    """测试特征监控"""
    logger.info("=" * 60)
    logger.info("开始测试特征监控")
    logger.info("=" * 60)
    
    # 创建模拟数据
    n_samples, n_features = 150, 50
    X = pd.DataFrame(
        np.random.randn(n_samples, n_features),
        columns=[f'feature_{i}' for i in range(n_features)]
    )
    y = pd.Series(np.random.randn(n_samples), name='target')
    
    # 创建监控系统
    monitoring_system = FeatureMonitoringSystem()
    
    try:
        # 第一次监控（设置基线）
        results1 = monitoring_system.full_monitoring_cycle(X, y)
        logger.info("第一次监控完成（基线设置）")
        
        # 创建有漂移的数据
        X_drift = X.copy()
        X_drift.iloc[:, :10] += 2.0  # 前10个特征发生均值漂移
        X_drift.iloc[:, 10:20] *= 2.0  # 接下来10个特征发生方差漂移
        
        # 第二次监控（检测漂移）
        results2 = monitoring_system.full_monitoring_cycle(X_drift, y)
        
        # 生成监控报告
        report = monitoring_system.generate_monitoring_report(results2)
        logger.info("监控报告:")
        logger.info(report)
        
        # 获取监控趋势
        trends = monitoring_system.get_monitoring_trends(days=1)
        if trends:
            logger.info(f"监控趋势:")
            logger.info(f"  监控频率: {trends['monitoring_frequency']:.2f}次/天")
            logger.info(f"  质量趋势: {trends['quality_trend']}")
            logger.info(f"  漂移趋势: {trends['drift_trend']}")
        
        return True, results2
        
    except Exception as e:
        logger.error(f"特征监控测试失败: {e}")
        return False, None


def test_integration():
    """集成测试"""
    logger.info("=" * 60)
    logger.info("开始集成测试")
    logger.info("=" * 60)
    
    try:
        # 1. 特征工程管道测试
        success1, features_df, pipeline_info = test_feature_pipeline()
        if not success1:
            return False
        
        # 2. 特征选择测试
        success2, selection_result = test_feature_selection()
        if not success2:
            return False
        
        # 3. 重要性分析测试
        success3, importance_report = test_importance_analysis()
        if not success3:
            return False
        
        # 4. 特征监控测试
        success4, monitoring_results = test_feature_monitoring()
        if not success4:
            return False
        
        logger.info("=" * 60)
        logger.info("所有测试通过！特征工程管道集成成功")
        logger.info("=" * 60)
        
        # 输出总结
        logger.info("测试总结:")
        logger.info(f"✓ 特征工程管道: 生成了{len(features_df.columns)}个特征")
        logger.info(f"✓ 特征选择: 最佳方法CV分数{selection_result.cross_val_score:.4f}")
        logger.info(f"✓ 重要性分析: 稳定性分数{importance_report.stability_score:.3f}")
        logger.info(f"✓ 特征监控: 检测到{monitoring_results.get('drift', {}).get('drifted_features', 0)}个漂移特征")
        
        return True
        
    except Exception as e:
        logger.error(f"集成测试失败: {e}")
        return False


if __name__ == "__main__":
    # 运行集成测试
    success = test_integration()
    
    if success:
        logger.info("🎉 特征工程管道测试全部通过！")
        exit(0)
    else:
        logger.error("❌ 特征工程管道测试失败！")
        exit(1)