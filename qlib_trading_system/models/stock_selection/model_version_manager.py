"""
模型版本管理和回滚系统
实现模型的版本控制、回滚和管理功能
"""

import os
import json
import shutil
import hashlib
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from pathlib import Path
import joblib
import numpy as np
import pandas as pd
from enum import Enum

from qlib_trading_system.utils.logging.logger import get_logger

logger = get_logger(__name__)


class ModelStatus(Enum):
    """模型状态枚举"""
    TRAINING = "training"
    ACTIVE = "active"
    DEPRECATED = "deprecated"
    ARCHIVED = "archived"
    FAILED = "failed"


@dataclass
class ModelMetadata:
    """模型元数据"""
    version: str
    name: str
    description: str
    created_time: datetime
    model_type: str
    framework: str
    
    # 性能指标
    train_score: float
    validation_score: float
    test_score: Optional[float] = None
    
    # 模型参数
    hyperparameters: Dict = None
    feature_count: int = 0
    training_samples: int = 0
    
    # 状态信息
    status: ModelStatus = ModelStatus.TRAINING
    is_production: bool = False
    
    # 文件路径
    model_path: str = ""
    config_path: str = ""
    
    # 依赖信息
    dependencies: Dict = None
    parent_version: Optional[str] = None
    
    # 部署信息
    deployment_time: Optional[datetime] = None
    rollback_count: int = 0
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        data = asdict(self)
        # 处理datetime对象
        if self.created_time:
            data['created_time'] = self.created_time.isoformat()
        if self.deployment_time:
            data['deployment_time'] = self.deployment_time.isoformat()
        # 处理枚举
        data['status'] = self.status.value
        return data
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'ModelMetadata':
        """从字典创建"""
        # 处理datetime字符串
        if 'created_time' in data and isinstance(data['created_time'], str):
            data['created_time'] = datetime.fromisoformat(data['created_time'])
        if 'deployment_time' in data and isinstance(data['deployment_time'], str):
            data['deployment_time'] = datetime.fromisoformat(data['deployment_time'])
        # 处理枚举
        if 'status' in data and isinstance(data['status'], str):
            data['status'] = ModelStatus(data['status'])
        
        return cls(**data)


class ModelVersionManager:
    """模型版本管理器"""
    
    def __init__(self, base_dir: str = "models/versions"):
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(parents=True, exist_ok=True)
        
        # 版本存储目录
        self.versions_dir = self.base_dir / "versions"
        self.versions_dir.mkdir(exist_ok=True)
        
        # 元数据文件
        self.metadata_file = self.base_dir / "metadata.json"
        self.active_version_file = self.base_dir / "active_version.txt"
        
        # 加载现有元数据
        self.metadata_registry = self._load_metadata_registry()
        
        logger.info(f"模型版本管理器初始化完成，基础目录: {self.base_dir}")
    
    def create_version(self, model_obj: Any, metadata: ModelMetadata) -> str:
        """创建新的模型版本"""
        try:
            # 生成版本号
            version = self._generate_version_id(metadata)
            metadata.version = version
            
            # 创建版本目录
            version_dir = self.versions_dir / version
            version_dir.mkdir(exist_ok=True)
            
            # 保存模型文件
            model_path = version_dir / "model.pkl"
            joblib.dump(model_obj, model_path)
            metadata.model_path = str(model_path)
            
            # 保存配置文件
            config_path = version_dir / "config.json"
            config_data = {
                'hyperparameters': metadata.hyperparameters or {},
                'dependencies': metadata.dependencies or {},
                'feature_count': metadata.feature_count,
                'training_samples': metadata.training_samples
            }
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            metadata.config_path = str(config_path)
            
            # 计算模型文件哈希
            model_hash = self._calculate_file_hash(model_path)
            
            # 更新元数据
            metadata.created_time = datetime.now()
            
            # 注册版本
            self.metadata_registry[version] = metadata
            self._save_metadata_registry()
            
            logger.info(f"模型版本 {version} 创建成功")
            return version
            
        except Exception as e:
            logger.error(f"创建模型版本失败: {e}")
            raise
    
    def deploy_version(self, version: str, force: bool = False) -> bool:
        """部署指定版本到生产环境"""
        try:
            if version not in self.metadata_registry:
                raise ValueError(f"版本 {version} 不存在")
            
            metadata = self.metadata_registry[version]
            
            # 检查版本状态
            if metadata.status == ModelStatus.FAILED and not force:
                raise ValueError(f"版本 {version} 状态为失败，无法部署")
            
            # 停用当前生产版本
            current_version = self.get_active_version()
            if current_version:
                self.metadata_registry[current_version].is_production = False
                logger.info(f"停用当前生产版本: {current_version}")
            
            # 部署新版本
            metadata.is_production = True
            metadata.status = ModelStatus.ACTIVE
            metadata.deployment_time = datetime.now()
            
            # 更新活跃版本文件
            with open(self.active_version_file, 'w') as f:
                f.write(version)
            
            # 保存元数据
            self._save_metadata_registry()
            
            logger.info(f"版本 {version} 已成功部署到生产环境")
            return True
            
        except Exception as e:
            logger.error(f"部署版本 {version} 失败: {e}")
            return False
    
    def rollback_to_version(self, version: str, reason: str = "") -> bool:
        """回滚到指定版本"""
        try:
            if version not in self.metadata_registry:
                raise ValueError(f"版本 {version} 不存在")
            
            target_metadata = self.metadata_registry[version]
            
            # 检查目标版本是否可用
            if target_metadata.status == ModelStatus.FAILED:
                raise ValueError(f"目标版本 {version} 状态为失败，无法回滚")
            
            # 获取当前活跃版本
            current_version = self.get_active_version()
            
            # 记录回滚信息
            rollback_info = {
                'timestamp': datetime.now().isoformat(),
                'from_version': current_version,
                'to_version': version,
                'reason': reason
            }
            
            # 保存回滚记录
            rollback_file = self.base_dir / "rollback_history.json"
            rollback_history = []
            if rollback_file.exists():
                with open(rollback_file, 'r', encoding='utf-8') as f:
                    rollback_history = json.load(f)
            
            rollback_history.append(rollback_info)
            with open(rollback_file, 'w', encoding='utf-8') as f:
                json.dump(rollback_history, f, indent=2, ensure_ascii=False)
            
            # 执行回滚
            if current_version:
                self.metadata_registry[current_version].is_production = False
            
            target_metadata.is_production = True
            target_metadata.status = ModelStatus.ACTIVE
            target_metadata.rollback_count += 1
            
            # 更新活跃版本
            with open(self.active_version_file, 'w') as f:
                f.write(version)
            
            self._save_metadata_registry()
            
            logger.info(f"成功回滚到版本 {version}，原因: {reason}")
            return True
            
        except Exception as e:
            logger.error(f"回滚到版本 {version} 失败: {e}")
            return False
    
    def get_active_version(self) -> Optional[str]:
        """获取当前活跃版本"""
        try:
            if self.active_version_file.exists():
                with open(self.active_version_file, 'r') as f:
                    return f.read().strip()
            return None
        except Exception as e:
            logger.error(f"获取活跃版本失败: {e}")
            return None
    
    def load_active_model(self) -> Tuple[Any, ModelMetadata]:
        """加载当前活跃模型"""
        active_version = self.get_active_version()
        if not active_version:
            raise ValueError("没有活跃的模型版本")
        
        return self.load_model(active_version)
    
    def load_model(self, version: str) -> Tuple[Any, ModelMetadata]:
        """加载指定版本的模型"""
        try:
            if version not in self.metadata_registry:
                raise ValueError(f"版本 {version} 不存在")
            
            metadata = self.metadata_registry[version]
            model_path = Path(metadata.model_path)
            
            if not model_path.exists():
                raise FileNotFoundError(f"模型文件不存在: {model_path}")
            
            model = joblib.load(model_path)
            logger.info(f"成功加载模型版本: {version}")
            
            return model, metadata
            
        except Exception as e:
            logger.error(f"加载模型版本 {version} 失败: {e}")
            raise
    
    def list_versions(self, status_filter: Optional[ModelStatus] = None, 
                     limit: int = None) -> List[ModelMetadata]:
        """列出所有版本"""
        versions = list(self.metadata_registry.values())
        
        # 状态过滤
        if status_filter:
            versions = [v for v in versions if v.status == status_filter]
        
        # 按创建时间排序
        versions.sort(key=lambda x: x.created_time, reverse=True)
        
        # 限制数量
        if limit:
            versions = versions[:limit]
        
        return versions
    
    def get_version_info(self, version: str) -> Optional[ModelMetadata]:
        """获取版本信息"""
        return self.metadata_registry.get(version)
    
    def delete_version(self, version: str, force: bool = False) -> bool:
        """删除指定版本"""
        try:
            if version not in self.metadata_registry:
                raise ValueError(f"版本 {version} 不存在")
            
            metadata = self.metadata_registry[version]
            
            # 检查是否为生产版本
            if metadata.is_production and not force:
                raise ValueError(f"版本 {version} 正在生产环境中使用，无法删除")
            
            # 删除版本目录
            version_dir = self.versions_dir / version
            if version_dir.exists():
                shutil.rmtree(version_dir)
            
            # 从注册表中移除
            del self.metadata_registry[version]
            self._save_metadata_registry()
            
            logger.info(f"版本 {version} 已删除")
            return True
            
        except Exception as e:
            logger.error(f"删除版本 {version} 失败: {e}")
            return False
    
    def archive_old_versions(self, keep_count: int = 10, 
                           keep_days: int = 30) -> List[str]:
        """归档旧版本"""
        try:
            archived_versions = []
            cutoff_date = datetime.now() - timedelta(days=keep_days)
            
            # 获取所有非生产版本
            versions = [v for v in self.metadata_registry.values() 
                       if not v.is_production and v.status != ModelStatus.ACTIVE]
            
            # 按创建时间排序
            versions.sort(key=lambda x: x.created_time, reverse=True)
            
            # 保留最新的keep_count个版本
            versions_to_archive = versions[keep_count:]
            
            # 归档超过保留期的版本
            for metadata in versions_to_archive:
                if metadata.created_time < cutoff_date:
                    metadata.status = ModelStatus.ARCHIVED
                    archived_versions.append(metadata.version)
            
            if archived_versions:
                self._save_metadata_registry()
                logger.info(f"已归档 {len(archived_versions)} 个版本: {archived_versions}")
            
            return archived_versions
            
        except Exception as e:
            logger.error(f"归档旧版本失败: {e}")
            return []
    
    def compare_versions(self, version1: str, version2: str) -> Dict:
        """比较两个版本"""
        try:
            if version1 not in self.metadata_registry:
                raise ValueError(f"版本 {version1} 不存在")
            if version2 not in self.metadata_registry:
                raise ValueError(f"版本 {version2} 不存在")
            
            meta1 = self.metadata_registry[version1]
            meta2 = self.metadata_registry[version2]
            
            comparison = {
                'version1': version1,
                'version2': version2,
                'performance_diff': {
                    'train_score': meta2.train_score - meta1.train_score,
                    'validation_score': meta2.validation_score - meta1.validation_score,
                },
                'time_diff': (meta2.created_time - meta1.created_time).total_seconds(),
                'feature_count_diff': meta2.feature_count - meta1.feature_count,
                'training_samples_diff': meta2.training_samples - meta1.training_samples,
                'status_comparison': {
                    'version1_status': meta1.status.value,
                    'version2_status': meta2.status.value
                }
            }
            
            return comparison
            
        except Exception as e:
            logger.error(f"比较版本失败: {e}")
            raise
    
    def get_rollback_history(self) -> List[Dict]:
        """获取回滚历史"""
        rollback_file = self.base_dir / "rollback_history.json"
        if rollback_file.exists():
            with open(rollback_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return []
    
    def export_version(self, version: str, export_path: str) -> bool:
        """导出版本"""
        try:
            if version not in self.metadata_registry:
                raise ValueError(f"版本 {version} 不存在")
            
            export_dir = Path(export_path)
            export_dir.mkdir(parents=True, exist_ok=True)
            
            # 复制版本目录
            version_dir = self.versions_dir / version
            target_dir = export_dir / version
            
            if version_dir.exists():
                shutil.copytree(version_dir, target_dir, dirs_exist_ok=True)
            
            # 导出元数据
            metadata = self.metadata_registry[version]
            metadata_file = export_dir / f"{version}_metadata.json"
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata.to_dict(), f, indent=2, ensure_ascii=False)
            
            logger.info(f"版本 {version} 已导出到: {export_path}")
            return True
            
        except Exception as e:
            logger.error(f"导出版本 {version} 失败: {e}")
            return False
    
    def import_version(self, import_path: str) -> Optional[str]:
        """导入版本"""
        try:
            import_dir = Path(import_path)
            if not import_dir.exists():
                raise FileNotFoundError(f"导入路径不存在: {import_path}")
            
            # 查找元数据文件
            metadata_files = list(import_dir.glob("*_metadata.json"))
            if not metadata_files:
                raise FileNotFoundError("未找到元数据文件")
            
            metadata_file = metadata_files[0]
            with open(metadata_file, 'r', encoding='utf-8') as f:
                metadata_dict = json.load(f)
            
            metadata = ModelMetadata.from_dict(metadata_dict)
            version = metadata.version
            
            # 检查版本是否已存在
            if version in self.metadata_registry:
                raise ValueError(f"版本 {version} 已存在")
            
            # 复制版本目录
            source_dir = import_dir / version
            target_dir = self.versions_dir / version
            
            if source_dir.exists():
                shutil.copytree(source_dir, target_dir, dirs_exist_ok=True)
            
            # 更新路径
            metadata.model_path = str(target_dir / "model.pkl")
            metadata.config_path = str(target_dir / "config.json")
            
            # 注册版本
            self.metadata_registry[version] = metadata
            self._save_metadata_registry()
            
            logger.info(f"版本 {version} 已成功导入")
            return version
            
        except Exception as e:
            logger.error(f"导入版本失败: {e}")
            return None
    
    def _generate_version_id(self, metadata: ModelMetadata) -> str:
        """生成版本ID"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_hash = hashlib.md5(
            f"{metadata.name}_{metadata.model_type}_{timestamp}".encode()
        ).hexdigest()[:8]
        return f"v{timestamp}_{model_hash}"
    
    def _calculate_file_hash(self, file_path: Path) -> str:
        """计算文件哈希"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    def _load_metadata_registry(self) -> Dict[str, ModelMetadata]:
        """加载元数据注册表"""
        if self.metadata_file.exists():
            try:
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                registry = {}
                for version, metadata_dict in data.items():
                    registry[version] = ModelMetadata.from_dict(metadata_dict)
                
                logger.info(f"加载了 {len(registry)} 个模型版本的元数据")
                return registry
                
            except Exception as e:
                logger.error(f"加载元数据注册表失败: {e}")
                return {}
        
        return {}
    
    def _save_metadata_registry(self):
        """保存元数据注册表"""
        try:
            data = {}
            for version, metadata in self.metadata_registry.items():
                data[version] = metadata.to_dict()
            
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"保存元数据注册表失败: {e}")
            raise


# 使用示例
if __name__ == "__main__":
    # 创建版本管理器
    version_manager = ModelVersionManager()
    
    # 模拟创建模型版本
    from sklearn.ensemble import RandomForestRegressor
    
    # 创建模型
    model = RandomForestRegressor(n_estimators=100, random_state=42)
    
    # 模拟训练数据
    import numpy as np
    X_train = np.random.randn(1000, 10)
    y_train = np.random.randn(1000)
    model.fit(X_train, y_train)
    
    # 创建元数据
    metadata = ModelMetadata(
        version="",  # 将自动生成
        name="explosive_stock_model",
        description="爆发股识别模型 v1.0",
        created_time=datetime.now(),
        model_type="RandomForest",
        framework="sklearn",
        train_score=0.85,
        validation_score=0.78,
        hyperparameters={
            'n_estimators': 100,
            'random_state': 42
        },
        feature_count=10,
        training_samples=1000
    )
    
    # 创建版本
    version = version_manager.create_version(model, metadata)
    print(f"创建版本: {version}")
    
    # 部署版本
    success = version_manager.deploy_version(version)
    print(f"部署结果: {success}")
    
    # 获取活跃版本
    active_version = version_manager.get_active_version()
    print(f"活跃版本: {active_version}")
    
    # 加载模型
    loaded_model, loaded_metadata = version_manager.load_active_model()
    print(f"加载的模型类型: {type(loaded_model)}")
    
    # 列出版本
    versions = version_manager.list_versions()
    print(f"版本列表: {[v.version for v in versions]}")