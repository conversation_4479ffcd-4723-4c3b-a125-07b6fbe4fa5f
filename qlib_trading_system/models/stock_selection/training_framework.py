"""
模型训练和验证框架
提供完整的训练流程、验证机制和模型管理功能
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import logging
from pathlib import Path
import json
import joblib
from sklearn.model_selection import TimeSeriesSplit, ParameterGrid
from sklearn.metrics import classification_report, confusion_matrix
# 可选的可视化依赖
try:
    import matplotlib.pyplot as plt
    import seaborn as sns
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    logger = logging.getLogger(__name__)
    logger.warning("matplotlib/seaborn不可用，可视化功能将被禁用")

from explosive_model import ExplosiveStockModel, ModelConfig

logger = logging.getLogger(__name__)

@dataclass
class TrainingConfig:
    """训练配置"""
    # 数据配置
    train_start_date: str = "2020-01-01"
    train_end_date: str = "2023-12-31"
    validation_split: float = 0.2
    time_series_cv_splits: int = 5
    
    # 训练配置
    retrain_frequency: str = "daily"  # daily, weekly, monthly
    max_training_samples: int = 100000
    min_positive_samples: int = 100
    
    # 验证配置
    validation_metrics: List[str] = field(default_factory=lambda: [
        'accuracy', 'precision', 'recall', 'f1_score', 'auc'
    ])
    
    # 模型保存
    model_save_dir: str = "data/models/explosive_stock"
    keep_best_n_models: int = 5
    
    # 性能阈值
    min_accuracy: float = 0.6
    min_precision: float = 0.5
    min_recall: float = 0.4

class ModelTrainingFramework:
    """模型训练框架"""
    
    def __init__(self, config: TrainingConfig = None):
        self.config = config or TrainingConfig()
        self.model_history = []
        self.best_model = None
        self.best_score = 0.0
        
        # 创建模型保存目录
        Path(self.config.model_save_dir).mkdir(parents=True, exist_ok=True)
        
        logger.info("初始化模型训练框架")
    
    def prepare_training_data(self, data: pd.DataFrame, 
                            target_column: str = 'is_explosive') -> Tuple[pd.DataFrame, pd.Series]:
        """准备训练数据"""
        logger.info("准备训练数据...")
        
        # 数据清洗
        data = data.dropna(subset=[target_column])
        
        # 时间过滤
        if 'date' in data.columns:
            data['date'] = pd.to_datetime(data['date'])
            mask = (data['date'] >= self.config.train_start_date) & \
                   (data['date'] <= self.config.train_end_date)
            data = data[mask]
        
        # 样本平衡检查
        target = data[target_column]
        positive_count = target.sum()
        negative_count = len(target) - positive_count
        
        logger.info(f"正样本数量: {positive_count}, 负样本数量: {negative_count}")
        
        if positive_count < self.config.min_positive_samples:
            logger.warning(f"正样本数量不足 ({positive_count} < {self.config.min_positive_samples})")
        
        # 限制训练样本数量
        if len(data) > self.config.max_training_samples:
            data = data.sample(n=self.config.max_training_samples, random_state=42)
            target = data[target_column]
            logger.info(f"限制训练样本数量为: {len(data)}")
        
        # 移除目标列
        features = data.drop(columns=[target_column])
        
        return features, target
    
    def time_series_validation(self, data: pd.DataFrame, target: pd.Series,
                             model_config: ModelConfig) -> Dict[str, Any]:
        """时间序列交叉验证"""
        logger.info("执行时间序列交叉验证...")
        
        # 确保数据按时间排序
        if 'date' in data.columns:
            sort_idx = data['date'].argsort()
            data = data.iloc[sort_idx]
            target = target.iloc[sort_idx]
        
        tscv = TimeSeriesSplit(n_splits=self.config.time_series_cv_splits)
        cv_results = []
        
        for fold, (train_idx, val_idx) in enumerate(tscv.split(data)):
            logger.info(f"交叉验证 Fold {fold + 1}/{self.config.time_series_cv_splits}")
            
            # 分割数据
            X_train, X_val = data.iloc[train_idx], data.iloc[val_idx]
            y_train, y_val = target.iloc[train_idx], target.iloc[val_idx]
            
            # 训练模型
            model = ExplosiveStockModel(model_config)
            training_results = model.train(X_train, y_train)
            
            # 验证模型
            val_predictions = model.predict(X_val)
            val_metrics = self._calculate_metrics(y_val, val_predictions)
            
            cv_results.append({
                'fold': fold + 1,
                'train_size': len(X_train),
                'val_size': len(X_val),
                'metrics': val_metrics,
                'feature_importance': training_results['feature_importance']
            })
        
        # 汇总结果
        avg_metrics = self._aggregate_cv_results(cv_results)
        
        return {
            'cv_results': cv_results,
            'avg_metrics': avg_metrics,
            'model_config': model_config.__dict__
        }
    
    def train_and_validate(self, data: pd.DataFrame, target: pd.Series,
                          model_config: ModelConfig = None) -> Dict[str, Any]:
        """训练和验证模型"""
        if model_config is None:
            model_config = ModelConfig()
        
        logger.info("开始模型训练和验证...")
        
        # 时间序列验证
        cv_results = self.time_series_validation(data, target, model_config)
        
        # 训练最终模型
        logger.info("训练最终模型...")
        final_model = ExplosiveStockModel(model_config)
        training_results = final_model.train(data, target)
        
        # 评估模型性能
        performance_score = self._calculate_performance_score(cv_results['avg_metrics'])
        
        # 保存模型
        model_info = {
            'model': final_model,
            'config': model_config,
            'training_results': training_results,
            'cv_results': cv_results,
            'performance_score': performance_score,
            'training_date': datetime.now().isoformat(),
            'data_info': {
                'total_samples': len(data),
                'positive_samples': target.sum(),
                'feature_count': len(data.columns)
            }
        }
        
        # 检查是否为最佳模型
        if performance_score > self.best_score:
            self.best_model = model_info
            self.best_score = performance_score
            logger.info(f"发现新的最佳模型，性能得分: {performance_score:.4f}")
        
        # 保存模型历史
        self.model_history.append(model_info)
        
        # 保存到磁盘
        self._save_model_info(model_info)
        
        return model_info
    
    def incremental_training(self, new_data: pd.DataFrame, target: pd.Series) -> Dict[str, Any]:
        """增量训练"""
        logger.info("执行增量训练...")
        
        if self.best_model is None:
            logger.warning("没有基础模型，执行全量训练")
            return self.train_and_validate(new_data, target)
        
        # 获取当前最佳模型配置
        base_config = self.best_model['config']
        
        # 使用新数据训练
        return self.train_and_validate(new_data, target, base_config)
    
    def _calculate_metrics(self, y_true: pd.Series, y_pred: np.ndarray) -> Dict[str, float]:
        """计算评估指标"""
        from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
        
        # 转换为二分类预测
        y_pred_binary = (y_pred >= 0.5).astype(int)
        
        metrics = {
            'accuracy': accuracy_score(y_true, y_pred_binary),
            'precision': precision_score(y_true, y_pred_binary, zero_division=0),
            'recall': recall_score(y_true, y_pred_binary, zero_division=0),
            'f1_score': f1_score(y_true, y_pred_binary, zero_division=0)
        }
        
        # 计算AUC（如果有足够的正负样本）
        try:
            if len(np.unique(y_true)) > 1:
                metrics['auc'] = roc_auc_score(y_true, y_pred)
            else:
                metrics['auc'] = 0.0
        except:
            metrics['auc'] = 0.0
        
        return metrics
    
    def _aggregate_cv_results(self, cv_results: List[Dict]) -> Dict[str, float]:
        """汇总交叉验证结果"""
        metrics_list = [result['metrics'] for result in cv_results]
        
        avg_metrics = {}
        for metric in self.config.validation_metrics:
            if metric in metrics_list[0]:
                values = [m[metric] for m in metrics_list]
                avg_metrics[f'{metric}_mean'] = np.mean(values)
                avg_metrics[f'{metric}_std'] = np.std(values)
        
        return avg_metrics
    
    def _calculate_performance_score(self, metrics: Dict[str, float]) -> float:
        """计算综合性能得分"""
        # 权重配置
        weights = {
            'accuracy_mean': 0.3,
            'precision_mean': 0.25,
            'recall_mean': 0.25,
            'f1_score_mean': 0.2
        }
        
        score = 0.0
        for metric, weight in weights.items():
            if metric in metrics:
                score += metrics[metric] * weight
        
        return score
    
    def _save_model_info(self, model_info: Dict[str, Any]):
        """保存模型信息"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存模型
        model_path = Path(self.config.model_save_dir) / f"explosive_model_{timestamp}.joblib"
        model_info['model'].save_model(str(model_path))
        
        # 保存元信息
        meta_info = {k: v for k, v in model_info.items() if k != 'model'}
        meta_path = Path(self.config.model_save_dir) / f"model_meta_{timestamp}.json"
        
        with open(meta_path, 'w', encoding='utf-8') as f:
            json.dump(meta_info, f, indent=2, ensure_ascii=False, default=str)
        
        logger.info(f"模型已保存: {model_path}")
        
        # 清理旧模型
        self._cleanup_old_models()
    
    def _cleanup_old_models(self):
        """清理旧模型文件"""
        model_dir = Path(self.config.model_save_dir)
        model_files = list(model_dir.glob("explosive_model_*.joblib"))
        
        if len(model_files) > self.config.keep_best_n_models:
            # 按修改时间排序，保留最新的N个
            model_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            for old_file in model_files[self.config.keep_best_n_models:]:
                old_file.unlink()
                # 同时删除对应的元信息文件
                meta_file = model_dir / old_file.name.replace("explosive_model_", "model_meta_").replace(".joblib", ".json")
                if meta_file.exists():
                    meta_file.unlink()
                
                logger.info(f"删除旧模型文件: {old_file}")
    
    def load_best_model(self) -> Optional[ExplosiveStockModel]:
        """加载最佳模型"""
        if self.best_model is not None:
            return self.best_model['model']
        
        # 从磁盘加载最新模型
        model_dir = Path(self.config.model_save_dir)
        model_files = list(model_dir.glob("explosive_model_*.joblib"))
        
        if not model_files:
            logger.warning("未找到已保存的模型")
            return None
        
        # 加载最新模型
        latest_model_file = max(model_files, key=lambda x: x.stat().st_mtime)
        
        model = ExplosiveStockModel()
        model.load_model(str(latest_model_file))
        
        logger.info(f"已加载模型: {latest_model_file}")
        return model
    
    def get_training_history(self) -> List[Dict[str, Any]]:
        """获取训练历史"""
        return self.model_history
    
    def generate_training_report(self) -> str:
        """生成训练报告"""
        if not self.model_history:
            return "暂无训练历史"
        
        report = []
        report.append("=== 爆发股识别模型训练报告 ===\n")
        
        # 最佳模型信息
        if self.best_model:
            best = self.best_model
            report.append(f"最佳模型性能得分: {self.best_score:.4f}")
            report.append(f"训练时间: {best['training_date']}")
            report.append(f"数据样本数: {best['data_info']['total_samples']}")
            report.append(f"正样本数: {best['data_info']['positive_samples']}")
            report.append(f"特征数量: {best['data_info']['feature_count']}")
            
            # 性能指标
            avg_metrics = best['cv_results']['avg_metrics']
            report.append("\n最佳模型交叉验证结果:")
            for metric, value in avg_metrics.items():
                if 'mean' in metric:
                    std_metric = metric.replace('mean', 'std')
                    std_value = avg_metrics.get(std_metric, 0)
                    report.append(f"  {metric}: {value:.4f} ± {std_value:.4f}")
        
        # 训练历史统计
        report.append(f"\n总训练次数: {len(self.model_history)}")
        
        # 性能趋势
        scores = [info['performance_score'] for info in self.model_history]
        report.append(f"平均性能得分: {np.mean(scores):.4f}")
        report.append(f"最高性能得分: {max(scores):.4f}")
        report.append(f"最低性能得分: {min(scores):.4f}")
        
        return "\n".join(report)

class ModelValidator:
    """模型验证器"""
    
    def __init__(self):
        self.validation_results = []
    
    def validate_model_performance(self, model: ExplosiveStockModel, 
                                 test_data: pd.DataFrame, 
                                 test_target: pd.Series) -> Dict[str, Any]:
        """验证模型性能"""
        logger.info("验证模型性能...")
        
        # 预测
        predictions = model.predict(test_data)
        pred_binary = (predictions >= 0.5).astype(int)
        
        # 计算详细指标
        from sklearn.metrics import classification_report, confusion_matrix, roc_curve, auc
        
        # 基础指标
        metrics = self._calculate_basic_metrics(test_target, predictions)
        
        # 分类报告
        class_report = classification_report(test_target, pred_binary, output_dict=True)
        
        # 混淆矩阵
        conf_matrix = confusion_matrix(test_target, pred_binary)
        
        # ROC曲线
        fpr, tpr, thresholds = roc_curve(test_target, predictions)
        roc_auc = auc(fpr, tpr)
        
        validation_result = {
            'basic_metrics': metrics,
            'classification_report': class_report,
            'confusion_matrix': conf_matrix.tolist(),
            'roc_curve': {
                'fpr': fpr.tolist(),
                'tpr': tpr.tolist(),
                'thresholds': thresholds.tolist(),
                'auc': roc_auc
            },
            'validation_date': datetime.now().isoformat()
        }
        
        self.validation_results.append(validation_result)
        
        return validation_result
    
    def _calculate_basic_metrics(self, y_true: pd.Series, y_pred: np.ndarray) -> Dict[str, float]:
        """计算基础指标"""
        from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
        
        y_pred_binary = (y_pred >= 0.5).astype(int)
        
        return {
            'accuracy': accuracy_score(y_true, y_pred_binary),
            'precision': precision_score(y_true, y_pred_binary, zero_division=0),
            'recall': recall_score(y_true, y_pred_binary, zero_division=0),
            'f1_score': f1_score(y_true, y_pred_binary, zero_division=0),
            'positive_rate': y_pred_binary.mean(),
            'actual_positive_rate': y_true.mean()
        }
    
    def plot_validation_results(self, validation_result: Dict[str, Any], 
                              save_path: str = None):
        """绘制验证结果图表"""
        if not MATPLOTLIB_AVAILABLE:
            logger.warning("matplotlib不可用，跳过图表绘制")
            return
            
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 混淆矩阵
        conf_matrix = np.array(validation_result['confusion_matrix'])
        sns.heatmap(conf_matrix, annot=True, fmt='d', ax=axes[0, 0])
        axes[0, 0].set_title('混淆矩阵')
        axes[0, 0].set_xlabel('预测标签')
        axes[0, 0].set_ylabel('真实标签')
        
        # ROC曲线
        roc_data = validation_result['roc_curve']
        axes[0, 1].plot(roc_data['fpr'], roc_data['tpr'], 
                       label=f'ROC Curve (AUC = {roc_data["auc"]:.4f})')
        axes[0, 1].plot([0, 1], [0, 1], 'k--')
        axes[0, 1].set_xlabel('假正率')
        axes[0, 1].set_ylabel('真正率')
        axes[0, 1].set_title('ROC曲线')
        axes[0, 1].legend()
        
        # 性能指标柱状图
        metrics = validation_result['basic_metrics']
        metric_names = list(metrics.keys())
        metric_values = list(metrics.values())
        
        axes[1, 0].bar(metric_names, metric_values)
        axes[1, 0].set_title('性能指标')
        axes[1, 0].set_ylabel('得分')
        axes[1, 0].tick_params(axis='x', rotation=45)
        
        # 预测分布
        axes[1, 1].hist([0, 1], bins=20, alpha=0.7, label=['负样本', '正样本'])
        axes[1, 1].set_title('预测分布')
        axes[1, 1].set_xlabel('预测概率')
        axes[1, 1].set_ylabel('频次')
        axes[1, 1].legend()
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"验证结果图表已保存: {save_path}")
        
        plt.show()