"""
特征监控和更新机制

监控特征质量、检测特征漂移、管理特征更新
确保特征工程管道的稳定性和有效性
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union, Any
from datetime import datetime, timedelta
import logging
from dataclasses import dataclass, asdict
import json
import os
from scipy import stats
from sklearn.metrics import mutual_info_score
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)


@dataclass
class FeatureQualityMetrics:
    """特征质量指标"""
    feature_name: str
    missing_ratio: float
    outlier_ratio: float
    variance: float
    skewness: float
    kurtosis: float
    unique_ratio: float
    correlation_with_target: float
    stability_score: float
    quality_score: float
    timestamp: datetime


@dataclass
class DriftDetectionResult:
    """漂移检测结果"""
    feature_name: str
    drift_detected: bool
    drift_score: float
    drift_type: str  # 'mean', 'variance', 'distribution'
    p_value: float
    threshold: float
    old_stats: Dict
    new_stats: Dict
    timestamp: datetime


@dataclass
class FeatureUpdateEvent:
    """特征更新事件"""
    event_type: str  # 'add', 'remove', 'modify'
    feature_name: str
    reason: str
    old_value: Optional[Any]
    new_value: Optional[Any]
    timestamp: datetime
    impact_score: float


class FeatureQualityMonitor:
    """特征质量监控器"""
    
    def __init__(self, quality_threshold: float = 0.6):
        self.quality_threshold = quality_threshold
        self.quality_history = []
        self.quality_alerts = []
    
    def assess_feature_quality(self, X: pd.DataFrame, y: pd.Series = None) -> Dict[str, FeatureQualityMetrics]:
        """评估特征质量"""
        logger.info(f"开始评估特征质量，特征数量: {len(X.columns)}")
        
        quality_metrics = {}
        
        for feature in X.columns:
            try:
                feature_data = X[feature]
                
                # 计算基础统计指标
                missing_ratio = feature_data.isnull().sum() / len(feature_data)
                
                # 异常值比例（使用IQR方法）
                if feature_data.dtype in ['int64', 'float64']:
                    Q1 = feature_data.quantile(0.25)
                    Q3 = feature_data.quantile(0.75)
                    IQR = Q3 - Q1
                    outlier_count = ((feature_data < (Q1 - 1.5 * IQR)) | 
                                   (feature_data > (Q3 + 1.5 * IQR))).sum()
                    outlier_ratio = outlier_count / len(feature_data)
                    
                    # 方差
                    variance = feature_data.var()
                    
                    # 偏度和峰度
                    skewness = feature_data.skew()
                    kurtosis = feature_data.kurtosis()
                else:
                    outlier_ratio = 0.0
                    variance = 0.0
                    skewness = 0.0
                    kurtosis = 0.0
                
                # 唯一值比例
                unique_ratio = feature_data.nunique() / len(feature_data)
                
                # 与目标变量的相关性
                if y is not None and feature_data.dtype in ['int64', 'float64']:
                    correlation_with_target = abs(feature_data.corr(y))
                    if pd.isna(correlation_with_target):
                        correlation_with_target = 0.0
                else:
                    correlation_with_target = 0.0
                
                # 稳定性分数（基于历史数据）
                stability_score = self._calculate_stability_score(feature, feature_data)
                
                # 综合质量分数
                quality_score = self._calculate_quality_score(
                    missing_ratio, outlier_ratio, variance, unique_ratio,
                    correlation_with_target, stability_score
                )
                
                # 创建质量指标对象
                metrics = FeatureQualityMetrics(
                    feature_name=feature,
                    missing_ratio=missing_ratio,
                    outlier_ratio=outlier_ratio,
                    variance=variance,
                    skewness=skewness,
                    kurtosis=kurtosis,
                    unique_ratio=unique_ratio,
                    correlation_with_target=correlation_with_target,
                    stability_score=stability_score,
                    quality_score=quality_score,
                    timestamp=datetime.now()
                )
                
                quality_metrics[feature] = metrics
                
                # 检查质量警告
                if quality_score < self.quality_threshold:
                    self._add_quality_alert(feature, quality_score, "质量分数低于阈值")
                
            except Exception as e:
                logger.error(f"评估特征 {feature} 质量失败: {e}")
                continue
        
        # 保存到历史记录
        self.quality_history.append({
            'timestamp': datetime.now(),
            'metrics': quality_metrics
        })
        
        logger.info(f"特征质量评估完成，发现 {len(self.quality_alerts)} 个质量警告")
        return quality_metrics
    
    def _calculate_stability_score(self, feature_name: str, current_data: pd.Series) -> float:
        """计算特征稳定性分数"""
        if len(self.quality_history) < 2:
            return 1.0
        
        # 获取历史数据
        try:
            last_metrics = self.quality_history[-1]['metrics'].get(feature_name)
            if last_metrics is None:
                return 1.0
            
            # 比较统计特性的变化
            if current_data.dtype in ['int64', 'float64']:
                current_mean = current_data.mean()
                current_std = current_data.std()
                
                # 这里简化处理，实际应该从历史记录中获取
                # 假设稳定性分数基于均值和标准差的变化
                stability_score = 1.0  # 简化实现
            else:
                stability_score = 1.0
            
            return max(min(stability_score, 1.0), 0.0)
            
        except Exception:
            return 1.0
    
    def _calculate_quality_score(self, missing_ratio: float, outlier_ratio: float,
                               variance: float, unique_ratio: float,
                               correlation_with_target: float, stability_score: float) -> float:
        """计算综合质量分数"""
        # 权重配置
        weights = {
            'missing': 0.25,      # 缺失值权重
            'outlier': 0.15,      # 异常值权重
            'variance': 0.15,     # 方差权重
            'unique': 0.10,       # 唯一性权重
            'correlation': 0.20,  # 相关性权重
            'stability': 0.15     # 稳定性权重
        }
        
        # 计算各项分数（0-1范围）
        missing_score = 1.0 - missing_ratio
        outlier_score = 1.0 - min(outlier_ratio, 0.2) / 0.2  # 异常值比例超过20%认为很差
        
        # 方差分数（适中的方差最好）
        if variance == 0:
            variance_score = 0.0
        elif variance < 0.01:
            variance_score = variance / 0.01
        elif variance > 100:
            variance_score = max(0.0, 1.0 - (variance - 100) / 100)
        else:
            variance_score = 1.0
        
        # 唯一性分数（太少或太多都不好）
        if unique_ratio < 0.01:
            unique_score = unique_ratio / 0.01
        elif unique_ratio > 0.95:
            unique_score = (1.0 - unique_ratio) / 0.05
        else:
            unique_score = 1.0
        
        correlation_score = correlation_with_target
        stability_score = stability_score
        
        # 加权平均
        quality_score = (
            missing_score * weights['missing'] +
            outlier_score * weights['outlier'] +
            variance_score * weights['variance'] +
            unique_score * weights['unique'] +
            correlation_score * weights['correlation'] +
            stability_score * weights['stability']
        )
        
        return max(min(quality_score, 1.0), 0.0)
    
    def _add_quality_alert(self, feature_name: str, quality_score: float, reason: str):
        """添加质量警告"""
        alert = {
            'timestamp': datetime.now(),
            'feature_name': feature_name,
            'quality_score': quality_score,
            'reason': reason,
            'severity': 'high' if quality_score < 0.3 else 'medium'
        }
        self.quality_alerts.append(alert)
    
    def get_quality_report(self, metrics: Dict[str, FeatureQualityMetrics]) -> Dict:
        """生成质量报告"""
        if not metrics:
            return {}
        
        # 统计信息
        quality_scores = [m.quality_score for m in metrics.values()]
        
        report = {
            'summary': {
                'total_features': len(metrics),
                'avg_quality_score': np.mean(quality_scores),
                'min_quality_score': np.min(quality_scores),
                'max_quality_score': np.max(quality_scores),
                'low_quality_features': sum(1 for score in quality_scores if score < self.quality_threshold),
                'high_quality_features': sum(1 for score in quality_scores if score >= 0.8)
            },
            'alerts': self.quality_alerts[-10:],  # 最近10个警告
            'top_quality_features': sorted(
                [(name, m.quality_score) for name, m in metrics.items()],
                key=lambda x: x[1], reverse=True
            )[:10],
            'low_quality_features': sorted(
                [(name, m.quality_score) for name, m in metrics.items()],
                key=lambda x: x[1]
            )[:10]
        }
        
        return report


class FeatureDriftDetector:
    """特征漂移检测器"""
    
    def __init__(self, drift_threshold: float = 0.05):
        self.drift_threshold = drift_threshold
        self.baseline_stats = {}
        self.drift_history = []
    
    def set_baseline(self, X: pd.DataFrame):
        """设置基线统计"""
        logger.info("设置特征漂移检测基线")
        
        self.baseline_stats = {}
        for feature in X.columns:
            feature_data = X[feature].dropna()
            
            if feature_data.dtype in ['int64', 'float64']:
                stats_dict = {
                    'mean': feature_data.mean(),
                    'std': feature_data.std(),
                    'min': feature_data.min(),
                    'max': feature_data.max(),
                    'median': feature_data.median(),
                    'q25': feature_data.quantile(0.25),
                    'q75': feature_data.quantile(0.75),
                    'skew': feature_data.skew(),
                    'kurtosis': feature_data.kurtosis()
                }
            else:
                # 分类特征
                value_counts = feature_data.value_counts(normalize=True)
                stats_dict = {
                    'value_counts': value_counts.to_dict(),
                    'unique_count': feature_data.nunique(),
                    'mode': feature_data.mode().iloc[0] if len(feature_data.mode()) > 0 else None
                }
            
            self.baseline_stats[feature] = {
                'stats': stats_dict,
                'timestamp': datetime.now(),
                'sample_size': len(feature_data)
            }
        
        logger.info(f"基线设置完成，包含 {len(self.baseline_stats)} 个特征")
    
    def detect_drift(self, X: pd.DataFrame) -> Dict[str, DriftDetectionResult]:
        """检测特征漂移"""
        logger.info(f"开始检测特征漂移，特征数量: {len(X.columns)}")
        
        drift_results = {}
        
        for feature in X.columns:
            if feature not in self.baseline_stats:
                logger.warning(f"特征 {feature} 没有基线统计，跳过漂移检测")
                continue
            
            try:
                result = self._detect_single_feature_drift(feature, X[feature])
                drift_results[feature] = result
                
                if result.drift_detected:
                    logger.warning(f"检测到特征 {feature} 发生漂移，类型: {result.drift_type}")
                
            except Exception as e:
                logger.error(f"检测特征 {feature} 漂移失败: {e}")
                continue
        
        # 保存到历史记录
        self.drift_history.append({
            'timestamp': datetime.now(),
            'results': drift_results
        })
        
        drift_count = sum(1 for r in drift_results.values() if r.drift_detected)
        logger.info(f"漂移检测完成，发现 {drift_count} 个特征发生漂移")
        
        return drift_results
    
    def _detect_single_feature_drift(self, feature_name: str, current_data: pd.Series) -> DriftDetectionResult:
        """检测单个特征的漂移"""
        baseline = self.baseline_stats[feature_name]
        current_data = current_data.dropna()
        
        if len(current_data) == 0:
            return DriftDetectionResult(
                feature_name=feature_name,
                drift_detected=False,
                drift_score=0.0,
                drift_type='no_data',
                p_value=1.0,
                threshold=self.drift_threshold,
                old_stats={},
                new_stats={},
                timestamp=datetime.now()
            )
        
        if current_data.dtype in ['int64', 'float64']:
            return self._detect_numerical_drift(feature_name, current_data, baseline)
        else:
            return self._detect_categorical_drift(feature_name, current_data, baseline)
    
    def _detect_numerical_drift(self, feature_name: str, current_data: pd.Series, baseline: Dict) -> DriftDetectionResult:
        """检测数值特征漂移"""
        old_stats = baseline['stats']
        
        # 计算当前统计
        new_stats = {
            'mean': current_data.mean(),
            'std': current_data.std(),
            'min': current_data.min(),
            'max': current_data.max(),
            'median': current_data.median(),
            'q25': current_data.quantile(0.25),
            'q75': current_data.quantile(0.75),
            'skew': current_data.skew(),
            'kurtosis': current_data.kurtosis()
        }
        
        # 检测均值漂移（t检验）
        try:
            # 简化的t检验（假设方差相等）
            old_mean = old_stats['mean']
            old_std = old_stats['std']
            new_mean = new_stats['mean']
            new_std = new_stats['std']
            
            if old_std > 0 and new_std > 0:
                # 计算t统计量
                pooled_std = np.sqrt((old_std**2 + new_std**2) / 2)
                t_stat = abs(new_mean - old_mean) / pooled_std
                
                # 简化的p值计算
                p_value = 2 * (1 - stats.norm.cdf(t_stat))
                
                drift_detected = p_value < self.drift_threshold
                drift_type = 'mean' if drift_detected else 'none'
                drift_score = 1 - p_value
            else:
                drift_detected = False
                drift_type = 'none'
                drift_score = 0.0
                p_value = 1.0
                
        except Exception:
            drift_detected = False
            drift_type = 'none'
            drift_score = 0.0
            p_value = 1.0
        
        return DriftDetectionResult(
            feature_name=feature_name,
            drift_detected=drift_detected,
            drift_score=drift_score,
            drift_type=drift_type,
            p_value=p_value,
            threshold=self.drift_threshold,
            old_stats=old_stats,
            new_stats=new_stats,
            timestamp=datetime.now()
        )
    
    def _detect_categorical_drift(self, feature_name: str, current_data: pd.Series, baseline: Dict) -> DriftDetectionResult:
        """检测分类特征漂移"""
        old_stats = baseline['stats']
        
        # 计算当前统计
        current_value_counts = current_data.value_counts(normalize=True)
        new_stats = {
            'value_counts': current_value_counts.to_dict(),
            'unique_count': current_data.nunique(),
            'mode': current_data.mode().iloc[0] if len(current_data.mode()) > 0 else None
        }
        
        # 检测分布漂移（卡方检验）
        try:
            old_counts = old_stats.get('value_counts', {})
            new_counts = new_stats['value_counts']
            
            # 获取所有类别
            all_categories = set(old_counts.keys()) | set(new_counts.keys())
            
            if len(all_categories) > 1:
                old_probs = [old_counts.get(cat, 0) for cat in all_categories]
                new_probs = [new_counts.get(cat, 0) for cat in all_categories]
                
                # 卡方检验
                chi2_stat = sum((o - e)**2 / (e + 1e-10) for o, e in zip(new_probs, old_probs))
                
                # 简化的p值计算
                p_value = 1 - stats.chi2.cdf(chi2_stat, len(all_categories) - 1)
                
                drift_detected = p_value < self.drift_threshold
                drift_type = 'distribution' if drift_detected else 'none'
                drift_score = 1 - p_value
            else:
                drift_detected = False
                drift_type = 'none'
                drift_score = 0.0
                p_value = 1.0
                
        except Exception:
            drift_detected = False
            drift_type = 'none'
            drift_score = 0.0
            p_value = 1.0
        
        return DriftDetectionResult(
            feature_name=feature_name,
            drift_detected=drift_detected,
            drift_score=drift_score,
            drift_type=drift_type,
            p_value=p_value,
            threshold=self.drift_threshold,
            old_stats=old_stats,
            new_stats=new_stats,
            timestamp=datetime.now()
        )
    
    def get_drift_summary(self, drift_results: Dict[str, DriftDetectionResult]) -> Dict:
        """获取漂移检测摘要"""
        if not drift_results:
            return {}
        
        drifted_features = [r for r in drift_results.values() if r.drift_detected]
        
        summary = {
            'total_features': len(drift_results),
            'drifted_features': len(drifted_features),
            'drift_ratio': len(drifted_features) / len(drift_results),
            'drift_types': {},
            'high_drift_features': [],
            'drift_scores': [r.drift_score for r in drift_results.values()]
        }
        
        # 统计漂移类型
        for result in drifted_features:
            drift_type = result.drift_type
            if drift_type not in summary['drift_types']:
                summary['drift_types'][drift_type] = 0
            summary['drift_types'][drift_type] += 1
        
        # 高漂移特征
        summary['high_drift_features'] = sorted(
            [(r.feature_name, r.drift_score) for r in drifted_features],
            key=lambda x: x[1], reverse=True
        )[:10]
        
        return summary


class FeatureUpdateManager:
    """特征更新管理器"""
    
    def __init__(self, update_log_file: str = 'feature_updates.json'):
        self.update_log_file = update_log_file
        self.update_events = []
        self.load_update_history()
    
    def load_update_history(self):
        """加载更新历史"""
        if os.path.exists(self.update_log_file):
            try:
                with open(self.update_log_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.update_events = [
                        FeatureUpdateEvent(**event) for event in data
                    ]
            except Exception as e:
                logger.error(f"加载特征更新历史失败: {e}")
                self.update_events = []
    
    def save_update_history(self):
        """保存更新历史"""
        try:
            data = [asdict(event) for event in self.update_events]
            # 处理datetime序列化
            for item in data:
                if isinstance(item['timestamp'], datetime):
                    item['timestamp'] = item['timestamp'].isoformat()
            
            with open(self.update_log_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存特征更新历史失败: {e}")
    
    def add_feature(self, feature_name: str, feature_data: pd.Series, reason: str):
        """添加新特征"""
        event = FeatureUpdateEvent(
            event_type='add',
            feature_name=feature_name,
            reason=reason,
            old_value=None,
            new_value=f"Added feature with {len(feature_data)} samples",
            timestamp=datetime.now(),
            impact_score=self._calculate_impact_score('add', feature_name)
        )
        
        self.update_events.append(event)
        self.save_update_history()
        
        logger.info(f"添加新特征: {feature_name}, 原因: {reason}")
    
    def remove_feature(self, feature_name: str, reason: str):
        """移除特征"""
        event = FeatureUpdateEvent(
            event_type='remove',
            feature_name=feature_name,
            reason=reason,
            old_value=f"Feature {feature_name} existed",
            new_value=None,
            timestamp=datetime.now(),
            impact_score=self._calculate_impact_score('remove', feature_name)
        )
        
        self.update_events.append(event)
        self.save_update_history()
        
        logger.info(f"移除特征: {feature_name}, 原因: {reason}")
    
    def modify_feature(self, feature_name: str, old_value: Any, new_value: Any, reason: str):
        """修改特征"""
        event = FeatureUpdateEvent(
            event_type='modify',
            feature_name=feature_name,
            reason=reason,
            old_value=str(old_value),
            new_value=str(new_value),
            timestamp=datetime.now(),
            impact_score=self._calculate_impact_score('modify', feature_name)
        )
        
        self.update_events.append(event)
        self.save_update_history()
        
        logger.info(f"修改特征: {feature_name}, 原因: {reason}")
    
    def _calculate_impact_score(self, event_type: str, feature_name: str) -> float:
        """计算影响分数"""
        # 简化的影响分数计算
        base_scores = {
            'add': 0.3,
            'remove': 0.7,
            'modify': 0.5
        }
        
        # 根据特征重要性调整（这里简化处理）
        importance_factor = 1.0
        if 'score' in feature_name.lower():
            importance_factor = 1.5
        elif 'explosive' in feature_name.lower():
            importance_factor = 2.0
        
        return base_scores.get(event_type, 0.5) * importance_factor
    
    def get_recent_updates(self, days: int = 7) -> List[FeatureUpdateEvent]:
        """获取最近的更新事件"""
        cutoff_date = datetime.now() - timedelta(days=days)
        return [
            event for event in self.update_events
            if event.timestamp > cutoff_date
        ]
    
    def get_update_summary(self) -> Dict:
        """获取更新摘要"""
        if not self.update_events:
            return {}
        
        # 统计各类型事件
        event_counts = {}
        for event in self.update_events:
            event_type = event.event_type
            if event_type not in event_counts:
                event_counts[event_type] = 0
            event_counts[event_type] += 1
        
        # 最近更新
        recent_updates = self.get_recent_updates(7)
        
        # 高影响更新
        high_impact_updates = [
            event for event in self.update_events
            if event.impact_score > 1.0
        ]
        
        return {
            'total_updates': len(self.update_events),
            'event_counts': event_counts,
            'recent_updates_count': len(recent_updates),
            'high_impact_updates_count': len(high_impact_updates),
            'last_update': self.update_events[-1].timestamp if self.update_events else None,
            'avg_impact_score': np.mean([e.impact_score for e in self.update_events])
        }


class FeatureMonitoringSystem:
    """特征监控系统 - 集成所有监控组件"""
    
    def __init__(self, config: Dict = None):
        self.config = config or {}
        
        # 初始化各个组件
        self.quality_monitor = FeatureQualityMonitor(
            quality_threshold=self.config.get('quality_threshold', 0.6)
        )
        self.drift_detector = FeatureDriftDetector(
            drift_threshold=self.config.get('drift_threshold', 0.05)
        )
        self.update_manager = FeatureUpdateManager(
            update_log_file=self.config.get('update_log_file', 'feature_updates.json')
        )
        
        # 监控历史
        self.monitoring_history = []
        
        logger.info("特征监控系统初始化完成")
    
    def full_monitoring_cycle(self, X: pd.DataFrame, y: pd.Series = None) -> Dict:
        """完整的监控周期"""
        logger.info("开始完整的特征监控周期")
        
        monitoring_results = {
            'timestamp': datetime.now(),
            'feature_count': len(X.columns),
            'sample_count': len(X)
        }
        
        # 1. 质量监控
        try:
            quality_metrics = self.quality_monitor.assess_feature_quality(X, y)
            quality_report = self.quality_monitor.get_quality_report(quality_metrics)
            monitoring_results['quality'] = quality_report
        except Exception as e:
            logger.error(f"质量监控失败: {e}")
            monitoring_results['quality'] = {}
        
        # 2. 漂移检测
        try:
            if not self.drift_detector.baseline_stats:
                self.drift_detector.set_baseline(X)
                monitoring_results['drift'] = {'message': '基线已设置，下次监控将检测漂移'}
            else:
                drift_results = self.drift_detector.detect_drift(X)
                drift_summary = self.drift_detector.get_drift_summary(drift_results)
                monitoring_results['drift'] = drift_summary
        except Exception as e:
            logger.error(f"漂移检测失败: {e}")
            monitoring_results['drift'] = {}
        
        # 3. 更新管理
        try:
            update_summary = self.update_manager.get_update_summary()
            monitoring_results['updates'] = update_summary
        except Exception as e:
            logger.error(f"更新管理失败: {e}")
            monitoring_results['updates'] = {}
        
        # 保存监控历史
        self.monitoring_history.append(monitoring_results)
        
        logger.info("完整的特征监控周期完成")
        return monitoring_results
    
    def generate_monitoring_report(self, results: Dict) -> str:
        """生成监控报告"""
        report_lines = [
            "=" * 60,
            "特征监控报告",
            "=" * 60,
            f"监控时间: {results['timestamp'].strftime('%Y-%m-%d %H:%M:%S')}",
            f"特征数量: {results['feature_count']}",
            f"样本数量: {results['sample_count']}",
            ""
        ]
        
        # 质量报告
        if 'quality' in results and results['quality']:
            quality = results['quality']
            report_lines.extend([
                "质量监控:",
                f"  平均质量分数: {quality['summary'].get('avg_quality_score', 0):.3f}",
                f"  低质量特征数: {quality['summary'].get('low_quality_features', 0)}",
                f"  高质量特征数: {quality['summary'].get('high_quality_features', 0)}",
                f"  质量警告数: {len(quality.get('alerts', []))}",
                ""
            ])
        
        # 漂移报告
        if 'drift' in results and results['drift']:
            drift = results['drift']
            if 'drifted_features' in drift:
                report_lines.extend([
                    "漂移检测:",
                    f"  漂移特征数: {drift.get('drifted_features', 0)}",
                    f"  漂移比例: {drift.get('drift_ratio', 0):.1%}",
                    f"  漂移类型: {drift.get('drift_types', {})}",
                    ""
                ])
        
        # 更新报告
        if 'updates' in results and results['updates']:
            updates = results['updates']
            report_lines.extend([
                "更新管理:",
                f"  总更新次数: {updates.get('total_updates', 0)}",
                f"  最近更新数: {updates.get('recent_updates_count', 0)}",
                f"  高影响更新数: {updates.get('high_impact_updates_count', 0)}",
                ""
            ])
        
        report_lines.append("=" * 60)
        return "\n".join(report_lines)
    
    def export_monitoring_results(self, results: Dict, filepath: str):
        """导出监控结果"""
        try:
            # 处理datetime序列化
            export_data = results.copy()
            if isinstance(export_data['timestamp'], datetime):
                export_data['timestamp'] = export_data['timestamp'].isoformat()
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"监控结果已导出到: {filepath}")
            
        except Exception as e:
            logger.error(f"导出监控结果失败: {e}")
    
    def get_monitoring_trends(self, days: int = 30) -> Dict:
        """获取监控趋势"""
        cutoff_date = datetime.now() - timedelta(days=days)
        recent_history = [
            h for h in self.monitoring_history
            if h['timestamp'] > cutoff_date
        ]
        
        if not recent_history:
            return {}
        
        # 提取趋势数据
        timestamps = [h['timestamp'] for h in recent_history]
        quality_scores = [
            h.get('quality', {}).get('summary', {}).get('avg_quality_score', 0)
            for h in recent_history
        ]
        drift_ratios = [
            h.get('drift', {}).get('drift_ratio', 0)
            for h in recent_history
        ]
        
        return {
            'timestamps': timestamps,
            'quality_trend': quality_scores,
            'drift_trend': drift_ratios,
            'monitoring_frequency': len(recent_history) / days
        }