"""
决策引擎测试文件
测试AI驱动的股票评分、动态切换决策、持仓决策和置信度评估功能
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List
import warnings
warnings.filterwarnings('ignore')

from decision_engine import (
    DecisionEngine,
    StockScoringSystem,
    DynamicSwitchingEngine,
    PositionDecisionEngine,
    ModelConfidenceEvaluator,
    StockScore,
    TradingDecision,
    DecisionType,
    ConfidenceLevel
)


class TestStockScoringSystem:
    """股票评分系统测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.scoring_system = StockScoringSystem()
        
        # 模拟股票特征数据
        self.sample_features = {
            'symbol': '000001.SZ',
            'roe_growth': 0.25,
            'revenue_growth': 0.20,
            'profit_growth': 0.30,
            'debt_ratio': 0.35,
            'pe_ratio': 15.0,
            'industry_pe': 20.0,
            'pb_ratio': 2.0,
            'peg_ratio': 0.8,
            'momentum_20': 0.15,
            'volume_ratio': 2.5,
            'rsi': 60,
            'breakout_signal': 0.8,
            'news_sentiment': 0.7,
            'money_flow': 0.3,
            'institution_attention': 0.8,
            'volatility': 0.3,
            'avg_turnover': 0.05,
            'st_risk': 0.1,
            'max_drawdown': 0.15,
            'market_trend': 0.1,
            'industry_performance': 0.08,
            'data_completeness': 0.9,
            'volume_surge': 0.6,
            'catalyst_score': 0.7,
            'timing_score': 0.5
        }
    
    def test_calculate_stock_score(self):
        """测试股票评分计算"""
        score = self.scoring_system.calculate_stock_score(self.sample_features)
        
        assert isinstance(score, StockScore)
        assert score.symbol == '000001.SZ'
        assert 0 <= score.total_score <= 1
        assert 0 <= score.confidence <= 1
        assert score.risk_level in ['LOW', 'MEDIUM', 'HIGH']
        assert 0 <= score.explosive_potential <= 1
        assert isinstance(score.dimension_scores, dict)
        
        # 检查维度评分
        expected_dimensions = ['fundamental', 'valuation', 'technical', 'sentiment', 'risk', 'market']
        for dim in expected_dimensions:
            assert dim in score.dimension_scores
            assert 0 <= score.dimension_scores[dim] <= 1
        
        print(f"股票评分测试通过: {score.symbol} - 总分: {score.total_score:.3f}")
    
    def test_dimension_scoring(self):
        """测试各维度评分"""
        # 测试基本面评分
        fundamental_score = self.scoring_system._calculate_fundamental_score(self.sample_features)
        assert 0 <= fundamental_score <= 1
        
        # 测试估值评分
        valuation_score = self.scoring_system._calculate_valuation_score(self.sample_features)
        assert 0 <= valuation_score <= 1
        
        # 测试技术面评分
        technical_score = self.scoring_system._calculate_technical_score(self.sample_features)
        assert 0 <= technical_score <= 1
        
        # 测试情绪评分
        sentiment_score = self.scoring_system._calculate_sentiment_score(self.sample_features)
        assert 0 <= sentiment_score <= 1
        
        # 测试风险评分
        risk_score = self.scoring_system._calculate_risk_score(self.sample_features)
        assert 0 <= risk_score <= 1
        
        # 测试大盘评分
        market_score = self.scoring_system._calculate_market_score(self.sample_features)
        assert 0 <= market_score <= 1
        
        print("各维度评分测试通过")
    
    def test_explosive_potential(self):
        """测试爆发潜力计算"""
        explosive_potential = self.scoring_system._calculate_explosive_potential(self.sample_features)
        
        assert 0 <= explosive_potential <= 1
        assert isinstance(explosive_potential, float)
        
        print(f"爆发潜力测试通过: {explosive_potential:.3f}")
    
    def test_edge_cases(self):
        """测试边界情况"""
        # 测试空特征
        empty_features = {'symbol': 'TEST'}
        score = self.scoring_system.calculate_stock_score(empty_features)
        assert isinstance(score, StockScore)
        assert score.total_score >= 0
        
        # 测试异常值
        extreme_features = self.sample_features.copy()
        extreme_features.update({
            'roe_growth': 10.0,  # 极端值
            'pe_ratio': -5.0,    # 负值
            'rsi': 150           # 超出范围
        })
        
        score = self.scoring_system.calculate_stock_score(extreme_features)
        assert isinstance(score, StockScore)
        assert 0 <= score.total_score <= 1
        
        print("边界情况测试通过")


class TestDynamicSwitchingEngine:
    """动态切换引擎测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.switching_engine = DynamicSwitchingEngine()
        
        # 创建测试用的股票评分
        self.current_score = StockScore(
            symbol='000001.SZ',
            total_score=0.6,
            dimension_scores={'risk': 0.4},
            confidence=0.7,
            risk_level='MEDIUM',
            explosive_potential=0.5,
            timestamp=datetime.now()
        )
        
        self.candidate_scores = [
            StockScore(
                symbol='000002.SZ',
                total_score=0.8,
                dimension_scores={'risk': 0.3},
                confidence=0.8,
                risk_level='LOW',
                explosive_potential=0.7,
                timestamp=datetime.now()
            ),
            StockScore(
                symbol='000003.SZ',
                total_score=0.5,
                dimension_scores={'risk': 0.6},
                confidence=0.5,
                risk_level='HIGH',
                explosive_potential=0.3,
                timestamp=datetime.now()
            )
        ]
    
    def test_should_switch_stock(self):
        """测试股票切换判断"""
        # 测试正常切换情况
        should_switch, target, reason = self.switching_engine.should_switch_stock(
            '000001.SZ', self.current_score, self.candidate_scores, 5
        )
        
        assert isinstance(should_switch, bool)
        if should_switch:
            assert target is not None
            assert isinstance(reason, str)
            assert len(reason) > 0
        
        print(f"切换判断测试通过: {should_switch}, 目标: {target}, 原因: {reason}")
    
    def test_minimum_holding_period(self):
        """测试最小持有期限制"""
        # 持有天数不足
        should_switch, target, reason = self.switching_engine.should_switch_stock(
            '000001.SZ', self.current_score, self.candidate_scores, 1
        )
        
        assert not should_switch
        assert "最小持有期" in reason
        
        print("最小持有期测试通过")
    
    def test_find_best_candidate(self):
        """测试最佳候选股票选择"""
        best_candidate = self.switching_engine._find_best_candidate(self.candidate_scores)
        
        if best_candidate:
            assert isinstance(best_candidate, StockScore)
            assert best_candidate.confidence >= 0.6  # 最小置信度要求
        
        print(f"最佳候选股票测试通过: {best_candidate.symbol if best_candidate else 'None'}")
    
    def test_update_holding_history(self):
        """测试持仓历史更新"""
        symbol = '000001.SZ'
        initial_count = len(self.switching_engine.holding_history.get(symbol, []))
        
        self.switching_engine.update_holding_history(symbol, 'BUY', self.current_score)
        
        new_count = len(self.switching_engine.holding_history.get(symbol, []))
        assert new_count == initial_count + 1
        
        print("持仓历史更新测试通过")


class TestPositionDecisionEngine:
    """持仓决策引擎测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.position_engine = PositionDecisionEngine()
        
        self.stock_score = StockScore(
            symbol='000001.SZ',
            total_score=0.8,
            dimension_scores={'risk': 0.3},
            confidence=0.8,
            risk_level='LOW',
            explosive_potential=0.7,
            timestamp=datetime.now()
        )
        
        self.account_info = {
            'current_position': 0.5,
            'current_drawdown': 0.1,
            'liquidity_score': 0.8,
            'market_beta': 1.2
        }
        
        self.market_condition = {
            'trend': 'BULL',
            'volatility': 0.3
        }
    
    def test_calculate_target_position(self):
        """测试目标仓位计算"""
        decision = self.position_engine.calculate_target_position(
            self.stock_score, self.account_info, self.market_condition
        )
        
        assert isinstance(decision, TradingDecision)
        assert decision.symbol == '000001.SZ'
        assert isinstance(decision.decision_type, DecisionType)
        assert 0 <= decision.target_position <= 1
        assert 0 <= decision.confidence <= 1
        assert isinstance(decision.reasoning, str)
        assert isinstance(decision.risk_assessment, dict)
        assert decision.holding_period > 0
        
        print(f"目标仓位计算测试通过: {decision.target_position:.3f}")
    
    def test_base_position_calculation(self):
        """测试基础仓位计算"""
        base_position = self.position_engine._calculate_base_position(
            self.stock_score, self.account_info
        )
        
        assert 0 <= base_position <= 1
        assert isinstance(base_position, float)
        
        print(f"基础仓位计算测试通过: {base_position:.3f}")
    
    def test_risk_adjustment(self):
        """测试风险调整"""
        base_position = 0.8
        adjusted_position = self.position_engine._apply_risk_adjustment(
            base_position, self.stock_score, self.account_info
        )
        
        assert 0 <= adjusted_position <= 1
        assert isinstance(adjusted_position, float)
        
        print(f"风险调整测试通过: {base_position:.3f} -> {adjusted_position:.3f}")
    
    def test_market_adjustment(self):
        """测试市场环境调整"""
        position = 0.8
        adjusted_position = self.position_engine._apply_market_adjustment(
            position, self.market_condition
        )
        
        assert 0 <= adjusted_position <= 1
        assert isinstance(adjusted_position, float)
        
        print(f"市场调整测试通过: {position:.3f} -> {adjusted_position:.3f}")
    
    def test_holding_period_estimation(self):
        """测试持有期估算"""
        holding_period = self.position_engine._estimate_holding_period(self.stock_score)
        
        assert holding_period > 0
        assert isinstance(holding_period, int)
        
        print(f"持有期估算测试通过: {holding_period}天")


class TestModelConfidenceEvaluator:
    """模型置信度评估器测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.confidence_evaluator = ModelConfidenceEvaluator()
        
        self.model_predictions = [
            {'symbol': '000001.SZ', 'score': 0.8, 'timestamp': datetime.now()},
            {'symbol': '000002.SZ', 'score': 0.7, 'timestamp': datetime.now()},
            {'symbol': '000003.SZ', 'score': 0.6, 'timestamp': datetime.now()}
        ]
        
        self.actual_results = [
            {'symbol': '000001.SZ', 'return': 0.1},
            {'symbol': '000002.SZ', 'return': 0.05},
            {'symbol': '000003.SZ', 'return': -0.02}
        ]
    
    def test_evaluate_model_confidence(self):
        """测试模型置信度评估"""
        confidence_metrics = self.confidence_evaluator.evaluate_model_confidence(
            self.model_predictions, self.actual_results
        )
        
        assert isinstance(confidence_metrics, dict)
        
        expected_keys = ['consistency', 'stability', 'accuracy', 'data_quality', 'overall', 'level']
        for key in expected_keys:
            assert key in confidence_metrics
            if key != 'level':
                assert 0 <= confidence_metrics[key] <= 1
        
        print(f"置信度评估测试通过: {confidence_metrics['overall']:.3f}")
    
    def test_prediction_consistency(self):
        """测试预测一致性评估"""
        consistency = self.confidence_evaluator._evaluate_prediction_consistency(
            self.model_predictions
        )
        
        assert 0 <= consistency <= 1
        assert isinstance(consistency, float)
        
        print(f"预测一致性测试通过: {consistency:.3f}")
    
    def test_prediction_stability(self):
        """测试预测稳定性评估"""
        stability = self.confidence_evaluator._evaluate_prediction_stability(
            self.model_predictions
        )
        
        assert 0 <= stability <= 1
        assert isinstance(stability, float)
        
        print(f"预测稳定性测试通过: {stability:.3f}")
    
    def test_prediction_accuracy(self):
        """测试预测准确率计算"""
        accuracy = self.confidence_evaluator._calculate_prediction_accuracy(
            self.model_predictions, self.actual_results
        )
        
        assert 0 <= accuracy <= 1
        assert isinstance(accuracy, (float, int))  # 允许int类型（如1.0可能被识别为int）
        
        print(f"预测准确率测试通过: {accuracy:.3f}")
    
    def test_data_quality_evaluation(self):
        """测试数据质量评估"""
        quality = self.confidence_evaluator._evaluate_data_quality(self.model_predictions)
        
        assert 0 <= quality <= 1
        assert isinstance(quality, (float, int))
        
        print(f"数据质量评估测试通过: {quality:.3f}")
    
    def test_confidence_level_determination(self):
        """测试置信度等级判断"""
        # 测试不同置信度等级
        test_cases = [
            (0.9, ConfidenceLevel.VERY_HIGH),
            (0.75, ConfidenceLevel.HIGH),
            (0.55, ConfidenceLevel.MEDIUM),
            (0.35, ConfidenceLevel.LOW),
            (0.15, ConfidenceLevel.VERY_LOW)
        ]
        
        for confidence, expected_level in test_cases:
            level = self.confidence_evaluator._determine_confidence_level(confidence)
            assert level == expected_level
        
        print("置信度等级判断测试通过")
    
    def test_update_prediction_history(self):
        """测试预测历史更新"""
        initial_count = len(self.confidence_evaluator.prediction_history)
        
        prediction = {'symbol': '000001.SZ', 'score': 0.8}
        actual_result = {'symbol': '000001.SZ', 'return': 0.1}
        
        self.confidence_evaluator.update_prediction_history(prediction, actual_result)
        
        new_count = len(self.confidence_evaluator.prediction_history)
        assert new_count == initial_count + 1
        
        print("预测历史更新测试通过")


class TestDecisionEngine:
    """主决策引擎测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.decision_engine = DecisionEngine()
        
        self.stock_features = {
            'symbol': '000001.SZ',
            'roe_growth': 0.25,
            'revenue_growth': 0.20,
            'profit_growth': 0.30,
            'debt_ratio': 0.35,
            'pe_ratio': 15.0,
            'industry_pe': 20.0,
            'pb_ratio': 2.0,
            'peg_ratio': 0.8,
            'momentum_20': 0.15,
            'volume_ratio': 2.5,
            'rsi': 60,
            'breakout_signal': 0.8,
            'news_sentiment': 0.7,
            'money_flow': 0.3,
            'institution_attention': 0.8,
            'volatility': 0.3,
            'avg_turnover': 0.05,
            'st_risk': 0.1,
            'max_drawdown': 0.15,
            'market_trend': 0.1,
            'industry_performance': 0.08,
            'data_completeness': 0.9,
            'volume_surge': 0.6,
            'catalyst_score': 0.7,
            'timing_score': 0.5
        }
        
        self.account_info = {
            'current_position': 0.5,
            'current_drawdown': 0.1,
            'liquidity_score': 0.8,
            'market_beta': 1.2
        }
        
        self.market_condition = {
            'trend': 'BULL',
            'volatility': 0.3
        }
    
    def test_make_trading_decision(self):
        """测试交易决策制定"""
        decision = self.decision_engine.make_trading_decision(
            self.stock_features, self.account_info, self.market_condition
        )
        
        assert isinstance(decision, TradingDecision)
        assert decision.symbol == '000001.SZ'
        assert isinstance(decision.decision_type, DecisionType)
        assert 0 <= decision.target_position <= 1
        assert 0 <= decision.confidence <= 1
        assert isinstance(decision.reasoning, str)
        assert isinstance(decision.risk_assessment, dict)
        assert decision.holding_period > 0
        
        print(f"交易决策制定测试通过: {decision.decision_type.value} - {decision.target_position:.3f}")
    
    def test_batch_evaluate_stocks(self):
        """测试批量股票评估"""
        stocks_features = [
            self.stock_features,
            {**self.stock_features, 'symbol': '000002.SZ', 'total_score': 0.6},
            {**self.stock_features, 'symbol': '000003.SZ', 'total_score': 0.7}
        ]
        
        scores = self.decision_engine.batch_evaluate_stocks(stocks_features, top_n=3)
        
        assert isinstance(scores, list)
        assert len(scores) <= 3
        
        for score in scores:
            assert isinstance(score, StockScore)
            assert 0 <= score.total_score <= 1
        
        # 检查排序（应该按评分降序）
        if len(scores) > 1:
            for i in range(len(scores) - 1):
                combined_score_1 = scores[i].total_score * scores[i].confidence + scores[i].explosive_potential * 0.3
                combined_score_2 = scores[i+1].total_score * scores[i+1].confidence + scores[i+1].explosive_potential * 0.3
                assert combined_score_1 >= combined_score_2
        
        print(f"批量股票评估测试通过: 评估了{len(stocks_features)}只股票，返回{len(scores)}只")
    
    def test_get_decision_summary(self):
        """测试决策摘要统计"""
        # 先制定几个决策以生成历史记录
        for i in range(3):
            features = {**self.stock_features, 'symbol': f'00000{i+1}.SZ'}
            self.decision_engine.make_trading_decision(
                features, self.account_info, self.market_condition
            )
        
        summary = self.decision_engine.get_decision_summary(days=7)
        
        assert isinstance(summary, dict)
        assert 'total_decisions' in summary
        assert summary['total_decisions'] >= 0
        
        if summary['total_decisions'] > 0:
            assert 'decision_types' in summary
            assert 'avg_confidence' in summary
            assert 'risk_levels' in summary
            assert 'period_days' in summary
        
        print(f"决策摘要统计测试通过: {summary['total_decisions']}个决策")
    
    def test_decision_with_current_holdings(self):
        """测试带有当前持仓的决策"""
        current_holdings = {
            '000002.SZ': {
                'holding_days': 5,
                'position': 0.8
            }
        }
        
        decision = self.decision_engine.make_trading_decision(
            self.stock_features, self.account_info, self.market_condition, current_holdings
        )
        
        assert isinstance(decision, TradingDecision)
        
        print(f"持仓决策测试通过: {decision.decision_type.value}")
    
    def test_error_handling(self):
        """测试错误处理"""
        # 测试空特征数据
        empty_features = {}
        decision = self.decision_engine.make_trading_decision(
            empty_features, self.account_info, self.market_condition
        )
        
        assert isinstance(decision, TradingDecision)
        assert decision.confidence <= 0.5  # 应该是低置信度
        
        print("错误处理测试通过")


def run_comprehensive_test():
    """运行综合测试"""
    print("=" * 60)
    print("开始决策引擎综合测试")
    print("=" * 60)
    
    try:
        # 测试股票评分系统
        print("\n1. 测试股票评分系统")
        print("-" * 30)
        scoring_test = TestStockScoringSystem()
        scoring_test.setup_method()
        scoring_test.test_calculate_stock_score()
        scoring_test.test_dimension_scoring()
        scoring_test.test_explosive_potential()
        scoring_test.test_edge_cases()
        
        # 测试动态切换引擎
        print("\n2. 测试动态切换引擎")
        print("-" * 30)
        switching_test = TestDynamicSwitchingEngine()
        switching_test.setup_method()
        switching_test.test_should_switch_stock()
        switching_test.test_minimum_holding_period()
        switching_test.test_find_best_candidate()
        switching_test.test_update_holding_history()
        
        # 测试持仓决策引擎
        print("\n3. 测试持仓决策引擎")
        print("-" * 30)
        position_test = TestPositionDecisionEngine()
        position_test.setup_method()
        position_test.test_calculate_target_position()
        position_test.test_base_position_calculation()
        position_test.test_risk_adjustment()
        position_test.test_market_adjustment()
        position_test.test_holding_period_estimation()
        
        # 测试模型置信度评估器
        print("\n4. 测试模型置信度评估器")
        print("-" * 30)
        confidence_test = TestModelConfidenceEvaluator()
        confidence_test.setup_method()
        confidence_test.test_evaluate_model_confidence()
        confidence_test.test_prediction_consistency()
        confidence_test.test_prediction_stability()
        confidence_test.test_prediction_accuracy()
        confidence_test.test_data_quality_evaluation()
        confidence_test.test_confidence_level_determination()
        confidence_test.test_update_prediction_history()
        
        # 测试主决策引擎
        print("\n5. 测试主决策引擎")
        print("-" * 30)
        engine_test = TestDecisionEngine()
        engine_test.setup_method()
        engine_test.test_make_trading_decision()
        engine_test.test_batch_evaluate_stocks()
        engine_test.test_get_decision_summary()
        engine_test.test_decision_with_current_holdings()
        engine_test.test_error_handling()
        
        print("\n" + "=" * 60)
        print("✅ 所有测试通过！决策引擎功能正常")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_comprehensive_test()
    exit(0 if success else 1)