"""
爆发股识别模型简化测试
测试不依赖复杂库的核心功能
"""

import numpy as np
import pandas as pd
import logging
from datetime import datetime, timedelta
import sys
import os

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_sample_data(n_samples=1000, n_features=50):
    """创建测试数据"""
    np.random.seed(42)
    
    # 生成特征数据
    features = np.random.randn(n_samples, n_features)
    feature_names = [f'feature_{i}' for i in range(n_features)]
    
    # 生成目标变量（爆发股标签）
    # 使用特征的线性组合加噪声
    weights = np.random.randn(n_features) * 0.1
    target_continuous = features @ weights + np.random.randn(n_samples) * 0.5
    target = (target_continuous > np.percentile(target_continuous, 80)).astype(int)
    
    # 创建DataFrame
    data = pd.DataFrame(features, columns=feature_names)
    data['symbol'] = [f'stock_{i:04d}' for i in range(n_samples)]
    data['date'] = pd.date_range('2020-01-01', periods=n_samples, freq='D')
    data['is_explosive'] = target
    
    return data

def test_simplified_model():
    """测试简化模型"""
    logger.info("=" * 60)
    logger.info("测试爆发股识别模型（简化版）")
    logger.info("=" * 60)
    
    try:
        # 导入简化模型
        from explosive_model_simplified import ExplosiveStockModelSimplified, ModelConfig
        
        # 创建模型配置
        config = ModelConfig(
            test_size=0.2,
            random_state=42
        )
        
        # 创建模型
        model = ExplosiveStockModelSimplified(config)
        logger.info("✓ 简化模型创建成功")
        
        # 创建测试数据
        data = create_sample_data(n_samples=500, n_features=30)
        logger.info(f"✓ 测试数据创建成功: {len(data)} 样本, {len(data.columns)-3} 特征")
        
        # 准备训练数据
        features = data.drop(columns=['is_explosive'])
        target = data['is_explosive']
        
        logger.info(f"正样本数量: {target.sum()}, 负样本数量: {len(target) - target.sum()}")
        
        # 训练模型
        logger.info("开始训练模型...")
        training_results = model.train(features, target)
        
        # 验证训练结果
        assert model.is_trained, "模型应该已训练"
        assert model.lgb_model is not None, "LightGBM模型应该存在"
        assert 'train_metrics' in training_results, "应该有训练指标"
        assert 'test_metrics' in training_results, "应该有测试指标"
        
        logger.info("✓ 模型训练成功")
        logger.info(f"  训练集准确率: {training_results['train_metrics']['accuracy']:.4f}")
        logger.info(f"  测试集准确率: {training_results['test_metrics']['accuracy']:.4f}")
        logger.info(f"  测试集F1得分: {training_results['test_metrics']['f1_score']:.4f}")
        
        # 测试预测功能
        test_data = create_sample_data(n_samples=100, n_features=30)
        test_features = test_data.drop(columns=['is_explosive'])
        
        predictions = model.predict(test_features)
        logger.info(f"✓ 预测功能测试成功: 预测了 {len(predictions)} 个样本")
        logger.info(f"  预测概率范围: [{predictions.min():.4f}, {predictions.max():.4f}]")
        
        # 测试爆发股筛选
        top_stocks = model.predict_explosive_stocks(test_features, top_n=10)
        logger.info(f"✓ 爆发股筛选成功: 筛选出 {len(top_stocks)} 只股票")
        
        if top_stocks:
            logger.info("Top 5 爆发潜力股票:")
            for i, (symbol, score) in enumerate(top_stocks[:5]):
                logger.info(f"  {i+1}. {symbol}: {score:.4f}")
        
        # 测试特征重要性
        importance = model._get_feature_importance()
        if importance:
            top_features = list(importance.items())[:5]
            logger.info("Top 5 重要特征:")
            for feature, score in top_features:
                logger.info(f"  {feature}: {score:.2f}")
        
        # 测试模型信息
        model_info = model.get_model_info()
        logger.info("✓ 模型信息获取成功")
        logger.info(f"  特征数量: {model_info['feature_count']}")
        logger.info(f"  模型类型: {model_info['model_type']}")
        logger.info(f"  有LightGBM模型: {model_info['has_lgb_model']}")
        
        return True
        
    except Exception as e:
        logger.error(f"简化模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simple_training_framework():
    """测试简化训练框架"""
    logger.info("=" * 60)
    logger.info("测试简化训练框架")
    logger.info("=" * 60)
    
    try:
        from explosive_model_simplified import SimpleTrainingFramework, ModelConfig
        
        # 创建训练框架
        framework = SimpleTrainingFramework()
        logger.info("✓ 简化训练框架创建成功")
        
        # 创建测试数据
        data = create_sample_data(n_samples=300, n_features=20)
        features = data.drop(columns=['is_explosive'])
        target = data['is_explosive']
        
        # 创建模型配置
        model_config = ModelConfig(test_size=0.2)
        
        # 训练和验证
        model_info = framework.train_and_validate(features, target, model_config)
        logger.info("✓ 训练和验证成功")
        logger.info(f"  性能得分: {model_info['performance_score']:.4f}")
        logger.info(f"  数据样本数: {model_info['data_info']['total_samples']}")
        logger.info(f"  正样本数: {model_info['data_info']['positive_samples']}")
        
        # 获取最佳模型
        best_model = framework.get_best_model()
        if best_model:
            logger.info("✓ 最佳模型获取成功")
            logger.info(f"  最佳得分: {framework.best_score:.4f}")
        
        return True
        
    except Exception as e:
        logger.error(f"简化训练框架测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simple_hyperparameter_optimizer():
    """测试简化超参数优化器"""
    logger.info("=" * 60)
    logger.info("测试简化超参数优化器")
    logger.info("=" * 60)
    
    try:
        from explosive_model_simplified import SimpleHyperparameterOptimizer
        
        # 创建优化器
        optimizer = SimpleHyperparameterOptimizer(max_iterations=5)  # 减少迭代次数
        logger.info("✓ 简化超参数优化器创建成功")
        
        # 创建测试数据
        data = create_sample_data(n_samples=200, n_features=15)
        features = data.drop(columns=['is_explosive'])
        target = data['is_explosive']
        
        # 定义搜索空间
        search_space = optimizer.define_search_space()
        logger.info(f"✓ 搜索空间定义成功: {len(search_space)} 个参数组")
        
        # 执行随机搜索优化
        optimization_results = optimizer.random_search_optimization(features, target)
        logger.info("✓ 随机搜索优化成功")
        logger.info(f"  最佳得分: {optimization_results['best_score']:.4f}")
        logger.info(f"  总迭代次数: {optimization_results['total_iterations']}")
        
        if optimization_results['best_params']:
            logger.info("✓ 最佳参数获取成功")
            for param, value in optimization_results['best_params'].items():
                logger.info(f"  {param}: {value}")
        
        return True
        
    except Exception as e:
        logger.error(f"简化超参数优化器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simple_performance_monitor():
    """测试简化性能监控器"""
    logger.info("=" * 60)
    logger.info("测试简化性能监控器")
    logger.info("=" * 60)
    
    try:
        from explosive_model_simplified import (
            ExplosiveStockModelSimplified, 
            ModelConfig, 
            SimplePerformanceMonitor
        )
        
        # 创建并训练一个简单模型
        model_config = ModelConfig(test_size=0.2)
        model = ExplosiveStockModelSimplified(model_config)
        
        # 训练模型
        data = create_sample_data(n_samples=200, n_features=10)
        features = data.drop(columns=['is_explosive'])
        target = data['is_explosive']
        model.train(features, target)
        
        # 创建性能监控器
        monitor = SimplePerformanceMonitor(model)
        logger.info("✓ 简化性能监控器创建成功")
        
        # 创建测试数据
        test_data = create_sample_data(n_samples=100, n_features=10)
        test_features = test_data.drop(columns=['is_explosive'])
        test_target = test_data['is_explosive']
        
        # 评估性能
        performance = monitor.evaluate_performance(test_features, test_target)
        logger.info("✓ 性能评估成功")
        logger.info(f"  准确率: {performance['accuracy']:.4f}")
        logger.info(f"  F1得分: {performance['f1_score']:.4f}")
        logger.info(f"  AUC: {performance['auc']:.4f}")
        
        # 生成性能报告
        report = monitor.generate_report()
        logger.info("✓ 性能报告生成成功")
        
        return True
        
    except Exception as e:
        logger.error(f"简化性能监控器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_persistence():
    """测试模型持久化"""
    logger.info("=" * 60)
    logger.info("测试模型持久化")
    logger.info("=" * 60)
    
    try:
        from explosive_model_simplified import ExplosiveStockModelSimplified, ModelConfig
        import tempfile
        
        # 创建并训练模型
        config = ModelConfig()
        model = ExplosiveStockModelSimplified(config)
        
        data = create_sample_data(n_samples=200, n_features=10)
        features = data.drop(columns=['is_explosive'])
        target = data['is_explosive']
        model.train(features, target)
        
        # 保存模型
        with tempfile.NamedTemporaryFile(suffix='.joblib', delete=False) as tmp_file:
            model_path = tmp_file.name
        
        model.save_model(model_path)
        logger.info("✓ 模型保存成功")
        
        # 创建新模型并加载
        new_model = ExplosiveStockModelSimplified()
        new_model.load_model(model_path)
        logger.info("✓ 模型加载成功")
        
        # 验证加载的模型
        assert new_model.is_trained, "加载的模型应该已训练"
        assert new_model.lgb_model is not None, "加载的模型应该有LightGBM模型"
        
        # 测试预测一致性
        test_data = create_sample_data(n_samples=50, n_features=10)
        test_features = test_data.drop(columns=['is_explosive'])
        
        original_pred = model.predict(test_features)
        loaded_pred = new_model.predict(test_features)
        
        # 检查预测结果是否一致
        pred_diff = np.abs(original_pred - loaded_pred).max()
        assert pred_diff < 1e-10, f"预测结果不一致，最大差异: {pred_diff}"
        
        logger.info("✓ 模型持久化测试成功")
        
        # 清理临时文件
        os.unlink(model_path)
        
        return True
        
    except Exception as e:
        logger.error(f"模型持久化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    logger.info("开始爆发股识别模型简化版完整测试")
    
    tests = [
        ("简化模型基础功能", test_simplified_model),
        ("简化训练框架", test_simple_training_framework),
        ("简化超参数优化器", test_simple_hyperparameter_optimizer),
        ("简化性能监控器", test_simple_performance_monitor),
        ("模型持久化", test_model_persistence)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n开始测试: {test_name}")
        try:
            if test_func():
                logger.info(f"✓ {test_name} 测试通过")
                passed += 1
            else:
                logger.error(f"✗ {test_name} 测试失败")
        except Exception as e:
            logger.error(f"✗ {test_name} 测试异常: {e}")
    
    logger.info("=" * 60)
    logger.info(f"测试完成: {passed}/{total} 通过")
    logger.info("=" * 60)
    
    if passed == total:
        logger.info("🎉 所有测试通过！爆发股识别模型（简化版）实现完成！")
        logger.info("任务 4.2 开发爆发股识别模型 - 完成")
        return True
    else:
        logger.error("❌ 部分测试失败！")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)