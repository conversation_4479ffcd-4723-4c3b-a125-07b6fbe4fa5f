"""
爆发股识别模型 - 简化版本
专注于LightGBM实现，不依赖PyTorch和其他复杂依赖
"""

import numpy as np
import pandas as pd
import lightgbm as lgb
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
import joblib
import logging
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

@dataclass
class ModelConfig:
    """模型配置"""
    # LightGBM配置
    lgb_params: Dict = None
    # 训练配置
    test_size: float = 0.2
    random_state: int = 42
    # 爆发股定义
    explosive_threshold: float = 1.0  # 3个月内100%涨幅定义为爆发股
    prediction_window: int = 90  # 预测窗口90天
    
    def __post_init__(self):
        if self.lgb_params is None:
            self.lgb_params = {
                'objective': 'binary',
                'metric': 'binary_logloss',
                'boosting_type': 'gbdt',
                'num_leaves': 31,
                'learning_rate': 0.05,
                'feature_fraction': 0.9,
                'bagging_fraction': 0.8,
                'bagging_freq': 5,
                'verbose': -1,
                'random_state': self.random_state
            }

class ExplosiveStockModelSimplified:
    """爆发股识别模型 - 简化版本（仅LightGBM）"""
    
    def __init__(self, config: ModelConfig = None):
        self.config = config or ModelConfig()
        self.lgb_model = None
        self.scaler = StandardScaler()
        self.feature_columns = None
        self.is_trained = False
        
        logger.info("初始化爆发股识别模型（简化版）")
        
    def _prepare_features(self, data: pd.DataFrame) -> np.ndarray:
        """准备特征数据"""
        # 确保数据按日期排序
        if 'date' in data.columns:
            data = data.sort_values('date')
        
        # 选择特征列（排除目标变量和日期等）
        exclude_cols = ['date', 'symbol', 'target', 'future_return', 'is_explosive']
        feature_cols = [col for col in data.columns if col not in exclude_cols]
        
        if self.feature_columns is None:
            self.feature_columns = feature_cols
        
        # 提取特征
        features = data[self.feature_columns].fillna(0)
        
        # 标准化特征
        if not hasattr(self.scaler, 'scale_'):
            features_scaled = self.scaler.fit_transform(features)
        else:
            features_scaled = self.scaler.transform(features)
        
        return features_scaled
        
    def _create_target_labels(self, data: pd.DataFrame) -> pd.Series:
        """创建爆发股标签"""
        if 'is_explosive' in data.columns:
            return data['is_explosive']
        
        # 如果没有预定义标签，基于未来收益率创建
        if 'future_return' in data.columns:
            return (data['future_return'] >= self.config.explosive_threshold).astype(int)
        
        # 如果都没有，返回全零标签（需要外部提供标签）
        logger.warning("未找到目标标签，返回全零标签")
        return pd.Series(np.zeros(len(data)), index=data.index)
        
    def train(self, data: pd.DataFrame, target: pd.Series = None) -> Dict[str, Any]:
        """训练模型"""
        logger.info("开始训练爆发股识别模型...")
        
        # 准备目标变量
        if target is None:
            target = self._create_target_labels(data)
        
        # 准备特征
        features_scaled = self._prepare_features(data)
        
        # 分割训练测试集
        X_train, X_test, y_train, y_test = train_test_split(
            features_scaled, target.values,
            test_size=self.config.test_size,
            random_state=self.config.random_state,
            stratify=target.values
        )
        
        # 训练LightGBM模型
        logger.info("训练LightGBM模型...")
        train_data = lgb.Dataset(X_train, label=y_train)
        valid_data = lgb.Dataset(X_test, label=y_test, reference=train_data)
        
        self.lgb_model = lgb.train(
            self.config.lgb_params,
            train_data,
            valid_sets=[valid_data],
            num_boost_round=1000,
            callbacks=[lgb.early_stopping(50), lgb.log_evaluation(0)]
        )
        
        # 评估模型
        train_metrics = self._evaluate_model(X_train, y_train, "训练集")
        test_metrics = self._evaluate_model(X_test, y_test, "测试集")
        
        self.is_trained = True
        
        training_results = {
            'train_metrics': train_metrics,
            'test_metrics': test_metrics,
            'feature_importance': self._get_feature_importance(),
            'model_config': self.config.__dict__
        }
        
        logger.info("模型训练完成")
        return training_results
        
    def predict(self, data: pd.DataFrame) -> np.ndarray:
        """预测爆发股概率"""
        if not self.is_trained:
            raise ValueError("模型尚未训练，请先调用train方法")
        
        # 准备特征
        features_scaled = self._prepare_features(data)
        
        # LightGBM预测
        predictions = self.lgb_model.predict(features_scaled)
        
        return predictions
    
    def predict_explosive_stocks(self, data: pd.DataFrame, top_n: int = 20) -> List[Tuple[str, float]]:
        """预测爆发股并返回排名前N的股票"""
        predictions = self.predict(data)
        
        # 获取股票代码
        if 'symbol' in data.columns:
            symbols = data['symbol'].values
        else:
            symbols = [f"stock_{i}" for i in range(len(predictions))]
        
        # 创建结果列表
        results = list(zip(symbols, predictions))
        
        # 按预测概率排序
        results.sort(key=lambda x: x[1], reverse=True)
        
        return results[:top_n]
    
    def _evaluate_model(self, X: np.ndarray, y: np.ndarray, dataset_name: str) -> Dict[str, float]:
        """评估模型性能"""
        predictions = self.lgb_model.predict(X)
        
        # 转换为二分类预测
        y_pred = (predictions >= 0.5).astype(int)
        
        metrics = {
            'accuracy': accuracy_score(y, y_pred),
            'precision': precision_score(y, y_pred, zero_division=0),
            'recall': recall_score(y, y_pred, zero_division=0),
            'f1_score': f1_score(y, y_pred, zero_division=0)
        }
        
        # 计算AUC
        try:
            if len(np.unique(y)) > 1:
                metrics['auc'] = roc_auc_score(y, predictions)
            else:
                metrics['auc'] = 0.0
        except:
            metrics['auc'] = 0.0
        
        logger.info(f"{dataset_name}性能: Accuracy={metrics['accuracy']:.4f}, "
                   f"Precision={metrics['precision']:.4f}, "
                   f"Recall={metrics['recall']:.4f}, "
                   f"F1={metrics['f1_score']:.4f}")
        
        return metrics
    
    def _get_feature_importance(self) -> Dict[str, float]:
        """获取特征重要性"""
        if self.lgb_model is None:
            return {}
        
        importance = self.lgb_model.feature_importance(importance_type='gain')
        feature_importance = dict(zip(self.feature_columns, importance))
        
        # 按重要性排序
        sorted_importance = dict(sorted(feature_importance.items(), 
                                      key=lambda x: x[1], reverse=True))
        
        return sorted_importance
    
    def save_model(self, filepath: str):
        """保存模型"""
        model_data = {
            'lgb_model': self.lgb_model,
            'scaler': self.scaler,
            'feature_columns': self.feature_columns,
            'config': self.config,
            'is_trained': self.is_trained
        }
        
        joblib.dump(model_data, filepath)
        logger.info(f"模型已保存到: {filepath}")
    
    def load_model(self, filepath: str):
        """加载模型"""
        model_data = joblib.load(filepath)
        
        self.lgb_model = model_data['lgb_model']
        self.scaler = model_data['scaler']
        self.feature_columns = model_data['feature_columns']
        self.config = model_data['config']
        self.is_trained = model_data['is_trained']
        
        logger.info(f"模型已从 {filepath} 加载")

    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            'is_trained': self.is_trained,
            'feature_count': len(self.feature_columns) if self.feature_columns else 0,
            'config': self.config.__dict__,
            'has_lgb_model': self.lgb_model is not None,
            'model_type': 'LightGBM_only'
        }

class SimpleTrainingFramework:
    """简化的训练框架"""
    
    def __init__(self):
        self.model_history = []
        self.best_model = None
        self.best_score = 0.0
        
        logger.info("初始化简化训练框架")
    
    def train_and_validate(self, data: pd.DataFrame, target: pd.Series,
                          model_config: ModelConfig = None) -> Dict[str, Any]:
        """训练和验证模型"""
        if model_config is None:
            model_config = ModelConfig()
        
        logger.info("开始模型训练和验证...")
        
        # 训练模型
        model = ExplosiveStockModelSimplified(model_config)
        training_results = model.train(data, target)
        
        # 计算性能得分
        performance_score = training_results['test_metrics']['f1_score']
        
        # 保存模型信息
        model_info = {
            'model': model,
            'config': model_config,
            'training_results': training_results,
            'performance_score': performance_score,
            'training_date': datetime.now().isoformat(),
            'data_info': {
                'total_samples': len(data),
                'positive_samples': target.sum(),
                'feature_count': len(data.columns) - 1  # 排除目标列
            }
        }
        
        # 检查是否为最佳模型
        if performance_score > self.best_score:
            self.best_model = model_info
            self.best_score = performance_score
            logger.info(f"发现新的最佳模型，性能得分: {performance_score:.4f}")
        
        # 保存模型历史
        self.model_history.append(model_info)
        
        return model_info
    
    def get_best_model(self) -> Optional[ExplosiveStockModelSimplified]:
        """获取最佳模型"""
        if self.best_model is not None:
            return self.best_model['model']
        return None

class SimpleHyperparameterOptimizer:
    """简化的超参数优化器"""
    
    def __init__(self, max_iterations: int = 10):
        self.max_iterations = max_iterations
        self.optimization_history = []
        self.best_params = None
        self.best_score = -np.inf
        
        logger.info("初始化简化超参数优化器")
    
    def define_search_space(self) -> Dict[str, List]:
        """定义搜索空间"""
        return {
            'num_leaves': [31, 50, 100],
            'learning_rate': [0.05, 0.1, 0.15],
            'feature_fraction': [0.8, 0.9, 1.0],
            'bagging_fraction': [0.8, 0.9, 1.0],
            'explosive_threshold': [0.5, 1.0, 1.5]
        }
    
    def random_search_optimization(self, data: pd.DataFrame, target: pd.Series) -> Dict[str, Any]:
        """随机搜索优化"""
        logger.info("开始随机搜索优化...")
        
        search_space = self.define_search_space()
        best_score = -np.inf
        best_params = None
        results = []
        
        for i in range(self.max_iterations):
            logger.info(f"随机搜索迭代 {i+1}/{self.max_iterations}")
            
            # 随机采样参数
            params = {}
            for key, values in search_space.items():
                params[key] = np.random.choice(values)
            
            try:
                # 创建模型配置
                if 'explosive_threshold' in params:
                    explosive_threshold = params.pop('explosive_threshold')
                else:
                    explosive_threshold = 1.0
                
                lgb_params = {
                    'objective': 'binary',
                    'metric': 'binary_logloss',
                    'boosting_type': 'gbdt',
                    'verbose': -1,
                    'random_state': 42
                }
                lgb_params.update(params)
                
                model_config = ModelConfig(
                    lgb_params=lgb_params,
                    explosive_threshold=explosive_threshold
                )
                
                # 训练和评估模型
                framework = SimpleTrainingFramework()
                model_info = framework.train_and_validate(data, target, model_config)
                score = model_info['performance_score']
                
                result = {
                    'iteration': i + 1,
                    'params': params,
                    'score': score,
                    'timestamp': datetime.now().isoformat()
                }
                
                results.append(result)
                self.optimization_history.append(result)
                
                if score > best_score:
                    best_score = score
                    best_params = params
                    logger.info(f"发现更好的参数组合，得分: {score:.4f}")
                    
            except Exception as e:
                logger.error(f"评估参数组合时出错: {e}")
                continue
        
        self.best_params = best_params
        self.best_score = best_score
        
        return {
            'strategy': 'random_search',
            'best_params': best_params,
            'best_score': best_score,
            'total_iterations': len(results),
            'all_results': results
        }

class SimplePerformanceMonitor:
    """简化的性能监控器"""
    
    def __init__(self, model: ExplosiveStockModelSimplified):
        self.model = model
        self.performance_history = []
        
        logger.info("初始化简化性能监控器")
    
    def evaluate_performance(self, test_data: pd.DataFrame, test_target: pd.Series) -> Dict[str, float]:
        """评估模型性能"""
        logger.info("评估模型性能...")
        
        # 预测
        predictions = self.model.predict(test_data)
        pred_binary = (predictions >= 0.5).astype(int)
        
        # 计算指标
        metrics = {
            'accuracy': accuracy_score(test_target, pred_binary),
            'precision': precision_score(test_target, pred_binary, zero_division=0),
            'recall': recall_score(test_target, pred_binary, zero_division=0),
            'f1_score': f1_score(test_target, pred_binary, zero_division=0),
            'timestamp': datetime.now().isoformat()
        }
        
        # 计算AUC
        try:
            if len(np.unique(test_target)) > 1:
                metrics['auc'] = roc_auc_score(test_target, predictions)
            else:
                metrics['auc'] = 0.0
        except:
            metrics['auc'] = 0.0
        
        # 保存到历史记录
        self.performance_history.append(metrics)
        
        logger.info(f"性能评估完成: Accuracy={metrics['accuracy']:.4f}, "
                   f"F1={metrics['f1_score']:.4f}, AUC={metrics['auc']:.4f}")
        
        return metrics
    
    def generate_report(self) -> str:
        """生成性能报告"""
        if not self.performance_history:
            return "暂无性能数据"
        
        latest = self.performance_history[-1]
        
        report = []
        report.append("=== 爆发股识别模型性能报告 ===")
        report.append(f"评估时间: {latest['timestamp']}")
        report.append(f"准确率: {latest['accuracy']:.4f}")
        report.append(f"精确率: {latest['precision']:.4f}")
        report.append(f"召回率: {latest['recall']:.4f}")
        report.append(f"F1得分: {latest['f1_score']:.4f}")
        report.append(f"AUC: {latest['auc']:.4f}")
        
        if len(self.performance_history) > 1:
            prev = self.performance_history[-2]
            f1_change = latest['f1_score'] - prev['f1_score']
            trend = "上升" if f1_change > 0 else "下降"
            report.append(f"F1得分变化: {trend} ({f1_change:+.4f})")
        
        return "\n".join(report)