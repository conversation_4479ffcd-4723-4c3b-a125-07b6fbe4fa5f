"""
超参数优化算法
支持网格搜索、随机搜索、贝叶斯优化等多种优化策略
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any, Callable
from dataclasses import dataclass, field
import logging
from itertools import product
import json
from datetime import datetime
import joblib
from pathlib import Path

# 优化算法
from sklearn.model_selection import ParameterGrid, ParameterSampler
try:
    from skopt import gp_minimize
    from skopt.space import Real, Integer, Categorical
    from skopt.utils import use_named_args
    BAYESIAN_AVAILABLE = True
except ImportError:
    BAYESIAN_AVAILABLE = False
    logger = logging.getLogger(__name__)
    logger.warning("scikit-optimize未安装，贝叶斯优化不可用")

from explosive_model import ExplosiveStockModel, ModelConfig
from training_framework import ModelTrainingFramework, TrainingConfig

logger = logging.getLogger(__name__)

@dataclass
class OptimizationConfig:
    """优化配置"""
    # 优化策略
    strategy: str = "grid_search"  # grid_search, random_search, bayesian
    max_iterations: int = 50
    cv_folds: int = 3
    
    # 评估指标
    optimization_metric: str = "f1_score"  # accuracy, precision, recall, f1_score, auc
    
    # 早停配置
    early_stopping: bool = True
    patience: int = 10
    min_improvement: float = 0.001
    
    # 并行配置
    n_jobs: int = 1
    
    # 结果保存
    save_results: bool = True
    results_dir: str = "data/optimization_results"

class HyperparameterOptimizer:
    """超参数优化器"""
    
    def __init__(self, config: OptimizationConfig = None):
        self.config = config or OptimizationConfig()
        self.optimization_history = []
        self.best_params = None
        self.best_score = -np.inf
        
        # 创建结果保存目录
        if self.config.save_results:
            Path(self.config.results_dir).mkdir(parents=True, exist_ok=True)
        
        logger.info(f"初始化超参数优化器，策略: {self.config.strategy}")
    
    def define_search_space(self) -> Dict[str, Any]:
        """定义搜索空间"""
        search_space = {
            # LightGBM参数
            'lgb_params': {
                'num_leaves': [31, 50, 100, 200],
                'learning_rate': [0.01, 0.05, 0.1, 0.2],
                'feature_fraction': [0.8, 0.9, 1.0],
                'bagging_fraction': [0.8, 0.9, 1.0],
                'bagging_freq': [0, 5, 10],
                'min_child_samples': [20, 50, 100],
                'reg_alpha': [0, 0.1, 0.5, 1.0],
                'reg_lambda': [0, 0.1, 0.5, 1.0]
            },
            
            # LSTM参数
            'lstm_hidden_size': [64, 128, 256],
            'lstm_num_layers': [1, 2, 3],
            'lstm_dropout': [0.1, 0.2, 0.3, 0.5],
            'sequence_length': [15, 30, 60],
            
            # 融合权重
            'lgb_weight': [0.4, 0.5, 0.6, 0.7, 0.8],
            'lstm_weight': [0.2, 0.3, 0.4, 0.5, 0.6],
            
            # 其他参数
            'explosive_threshold': [0.5, 1.0, 1.5, 2.0],
            'test_size': [0.15, 0.2, 0.25]
        }
        
        return search_space
    
    def grid_search_optimization(self, data: pd.DataFrame, target: pd.Series) -> Dict[str, Any]:
        """网格搜索优化"""
        logger.info("开始网格搜索优化...")
        
        search_space = self.define_search_space()
        
        # 生成参数组合
        param_combinations = self._generate_param_combinations(search_space)
        
        # 限制搜索次数
        if len(param_combinations) > self.config.max_iterations:
            np.random.shuffle(param_combinations)
            param_combinations = param_combinations[:self.config.max_iterations]
        
        logger.info(f"总共需要评估 {len(param_combinations)} 个参数组合")
        
        best_score = -np.inf
        best_params = None
        results = []
        
        for i, params in enumerate(param_combinations):
            logger.info(f"评估参数组合 {i+1}/{len(param_combinations)}")
            
            try:
                score, metrics = self._evaluate_params(params, data, target)
                
                result = {
                    'iteration': i + 1,
                    'params': params,
                    'score': score,
                    'metrics': metrics,
                    'timestamp': datetime.now().isoformat()
                }
                
                results.append(result)
                self.optimization_history.append(result)
                
                if score > best_score:
                    best_score = score
                    best_params = params
                    logger.info(f"发现更好的参数组合，得分: {score:.4f}")
                
                # 早停检查
                if self.config.early_stopping and self._should_early_stop(results):
                    logger.info(f"早停于第 {i+1} 次迭代")
                    break
                    
            except Exception as e:
                logger.error(f"评估参数组合时出错: {e}")
                continue
        
        self.best_params = best_params
        self.best_score = best_score
        
        optimization_result = {
            'strategy': 'grid_search',
            'best_params': best_params,
            'best_score': best_score,
            'total_iterations': len(results),
            'all_results': results
        }
        
        if self.config.save_results:
            self._save_optimization_results(optimization_result)
        
        return optimization_result
    
    def random_search_optimization(self, data: pd.DataFrame, target: pd.Series) -> Dict[str, Any]:
        """随机搜索优化"""
        logger.info("开始随机搜索优化...")
        
        search_space = self.define_search_space()
        
        best_score = -np.inf
        best_params = None
        results = []
        
        for i in range(self.config.max_iterations):
            logger.info(f"随机搜索迭代 {i+1}/{self.config.max_iterations}")
            
            # 随机采样参数
            params = self._sample_random_params(search_space)
            
            try:
                score, metrics = self._evaluate_params(params, data, target)
                
                result = {
                    'iteration': i + 1,
                    'params': params,
                    'score': score,
                    'metrics': metrics,
                    'timestamp': datetime.now().isoformat()
                }
                
                results.append(result)
                self.optimization_history.append(result)
                
                if score > best_score:
                    best_score = score
                    best_params = params
                    logger.info(f"发现更好的参数组合，得分: {score:.4f}")
                
                # 早停检查
                if self.config.early_stopping and self._should_early_stop(results):
                    logger.info(f"早停于第 {i+1} 次迭代")
                    break
                    
            except Exception as e:
                logger.error(f"评估参数组合时出错: {e}")
                continue
        
        self.best_params = best_params
        self.best_score = best_score
        
        optimization_result = {
            'strategy': 'random_search',
            'best_params': best_params,
            'best_score': best_score,
            'total_iterations': len(results),
            'all_results': results
        }
        
        if self.config.save_results:
            self._save_optimization_results(optimization_result)
        
        return optimization_result
    
    def bayesian_optimization(self, data: pd.DataFrame, target: pd.Series) -> Dict[str, Any]:
        """贝叶斯优化"""
        if not BAYESIAN_AVAILABLE:
            logger.error("贝叶斯优化不可用，请安装scikit-optimize")
            return self.random_search_optimization(data, target)
        
        logger.info("开始贝叶斯优化...")
        
        # 定义搜索空间
        dimensions = [
            Integer(20, 200, name='num_leaves'),
            Real(0.01, 0.3, name='learning_rate'),
            Real(0.7, 1.0, name='feature_fraction'),
            Real(0.7, 1.0, name='bagging_fraction'),
            Integer(0, 10, name='bagging_freq'),
            Integer(64, 256, name='lstm_hidden_size'),
            Integer(1, 3, name='lstm_num_layers'),
            Real(0.1, 0.5, name='lstm_dropout'),
            Integer(15, 60, name='sequence_length'),
            Real(0.3, 0.8, name='lgb_weight'),
            Real(0.5, 2.0, name='explosive_threshold')
        ]
        
        # 目标函数
        @use_named_args(dimensions)
        def objective(**params):
            # 构建完整参数字典
            full_params = self._build_full_params_from_bayesian(params)
            
            try:
                score, _ = self._evaluate_params(full_params, data, target)
                # 贝叶斯优化是最小化，所以返回负值
                return -score
            except Exception as e:
                logger.error(f"贝叶斯优化评估出错: {e}")
                return 0  # 返回较差的分数
        
        # 执行优化
        result = gp_minimize(
            func=objective,
            dimensions=dimensions,
            n_calls=self.config.max_iterations,
            random_state=42,
            acq_func='EI'  # Expected Improvement
        )
        
        # 构建最佳参数
        best_params_bayesian = dict(zip([dim.name for dim in dimensions], result.x))
        best_params = self._build_full_params_from_bayesian(best_params_bayesian)
        best_score = -result.fun
        
        self.best_params = best_params
        self.best_score = best_score
        
        optimization_result = {
            'strategy': 'bayesian',
            'best_params': best_params,
            'best_score': best_score,
            'total_iterations': len(result.func_vals),
            'convergence': result.func_vals
        }
        
        if self.config.save_results:
            self._save_optimization_results(optimization_result)
        
        return optimization_result
    
    def optimize(self, data: pd.DataFrame, target: pd.Series) -> Dict[str, Any]:
        """执行优化"""
        if self.config.strategy == "grid_search":
            return self.grid_search_optimization(data, target)
        elif self.config.strategy == "random_search":
            return self.random_search_optimization(data, target)
        elif self.config.strategy == "bayesian":
            return self.bayesian_optimization(data, target)
        else:
            raise ValueError(f"不支持的优化策略: {self.config.strategy}")
    
    def _generate_param_combinations(self, search_space: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成参数组合"""
        # 处理嵌套的lgb_params
        lgb_params = search_space.pop('lgb_params')
        
        # 生成LightGBM参数组合
        lgb_combinations = list(ParameterGrid(lgb_params))
        
        # 生成其他参数组合
        other_combinations = list(ParameterGrid(search_space))
        
        # 组合所有参数
        all_combinations = []
        for lgb_combo in lgb_combinations:
            for other_combo in other_combinations:
                # 检查权重约束
                lgb_weight = other_combo.get('lgb_weight', 0.6)
                lstm_weight = other_combo.get('lstm_weight', 0.4)
                
                if abs(lgb_weight + lstm_weight - 1.0) < 0.1:  # 权重和接近1
                    combo = other_combo.copy()
                    combo['lgb_params'] = lgb_combo
                    all_combinations.append(combo)
        
        return all_combinations
    
    def _sample_random_params(self, search_space: Dict[str, Any]) -> Dict[str, Any]:
        """随机采样参数"""
        params = {}
        
        # 采样LightGBM参数
        lgb_params = search_space['lgb_params']
        params['lgb_params'] = {}
        for key, values in lgb_params.items():
            params['lgb_params'][key] = np.random.choice(values)
        
        # 采样其他参数
        for key, values in search_space.items():
            if key != 'lgb_params':
                params[key] = np.random.choice(values)
        
        # 调整权重使其和为1
        lgb_weight = params.get('lgb_weight', 0.6)
        params['lstm_weight'] = 1.0 - lgb_weight
        
        return params
    
    def _build_full_params_from_bayesian(self, bayesian_params: Dict[str, Any]) -> Dict[str, Any]:
        """从贝叶斯优化参数构建完整参数字典"""
        # 构建LightGBM参数
        lgb_params = {
            'objective': 'binary',
            'metric': 'binary_logloss',
            'boosting_type': 'gbdt',
            'num_leaves': int(bayesian_params['num_leaves']),
            'learning_rate': bayesian_params['learning_rate'],
            'feature_fraction': bayesian_params['feature_fraction'],
            'bagging_fraction': bayesian_params['bagging_fraction'],
            'bagging_freq': int(bayesian_params['bagging_freq']),
            'verbose': -1,
            'random_state': 42
        }
        
        # 构建完整参数
        full_params = {
            'lgb_params': lgb_params,
            'lstm_hidden_size': int(bayesian_params['lstm_hidden_size']),
            'lstm_num_layers': int(bayesian_params['lstm_num_layers']),
            'lstm_dropout': bayesian_params['lstm_dropout'],
            'sequence_length': int(bayesian_params['sequence_length']),
            'lgb_weight': bayesian_params['lgb_weight'],
            'lstm_weight': 1.0 - bayesian_params['lgb_weight'],
            'explosive_threshold': bayesian_params['explosive_threshold'],
            'test_size': 0.2,
            'random_state': 42
        }
        
        return full_params
    
    def _evaluate_params(self, params: Dict[str, Any], data: pd.DataFrame, 
                        target: pd.Series) -> Tuple[float, Dict[str, float]]:
        """评估参数组合"""
        # 创建模型配置
        model_config = ModelConfig()
        
        # 更新配置
        for key, value in params.items():
            if hasattr(model_config, key):
                setattr(model_config, key, value)
        
        # 创建训练框架
        training_config = TrainingConfig()
        training_config.time_series_cv_splits = self.config.cv_folds
        
        framework = ModelTrainingFramework(training_config)
        
        # 执行交叉验证
        cv_results = framework.time_series_validation(data, target, model_config)
        
        # 获取目标指标
        metric_key = f"{self.config.optimization_metric}_mean"
        score = cv_results['avg_metrics'].get(metric_key, 0.0)
        
        return score, cv_results['avg_metrics']
    
    def _should_early_stop(self, results: List[Dict[str, Any]]) -> bool:
        """检查是否应该早停"""
        if len(results) < self.config.patience:
            return False
        
        # 检查最近patience次迭代是否有改进
        recent_scores = [r['score'] for r in results[-self.config.patience:]]
        max_recent = max(recent_scores)
        
        # 与历史最佳比较
        historical_best = max([r['score'] for r in results[:-self.config.patience]])
        
        improvement = max_recent - historical_best
        
        return improvement < self.config.min_improvement
    
    def _save_optimization_results(self, results: Dict[str, Any]):
        """保存优化结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"optimization_{self.config.strategy}_{timestamp}.json"
        filepath = Path(self.config.results_dir) / filename
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)
        
        logger.info(f"优化结果已保存: {filepath}")
    
    def get_best_model_config(self) -> Optional[ModelConfig]:
        """获取最佳模型配置"""
        if self.best_params is None:
            return None
        
        config = ModelConfig()
        
        # 更新配置
        for key, value in self.best_params.items():
            if hasattr(config, key):
                setattr(config, key, value)
        
        return config
    
    def load_optimization_results(self, filepath: str) -> Dict[str, Any]:
        """加载优化结果"""
        with open(filepath, 'r', encoding='utf-8') as f:
            results = json.load(f)
        
        self.best_params = results['best_params']
        self.best_score = results['best_score']
        
        logger.info(f"已加载优化结果: {filepath}")
        return results
    
    def get_optimization_summary(self) -> str:
        """获取优化摘要"""
        if not self.optimization_history:
            return "暂无优化历史"
        
        summary = []
        summary.append("=== 超参数优化摘要 ===\n")
        
        summary.append(f"优化策略: {self.config.strategy}")
        summary.append(f"总迭代次数: {len(self.optimization_history)}")
        summary.append(f"最佳得分: {self.best_score:.4f}")
        summary.append(f"优化指标: {self.config.optimization_metric}")
        
        if self.best_params:
            summary.append("\n最佳参数:")
            for key, value in self.best_params.items():
                if isinstance(value, dict):
                    summary.append(f"  {key}:")
                    for sub_key, sub_value in value.items():
                        summary.append(f"    {sub_key}: {sub_value}")
                else:
                    summary.append(f"  {key}: {value}")
        
        # 收敛分析
        scores = [r['score'] for r in self.optimization_history]
        summary.append(f"\n收敛分析:")
        summary.append(f"  初始得分: {scores[0]:.4f}")
        summary.append(f"  最终得分: {scores[-1]:.4f}")
        summary.append(f"  改进幅度: {(max(scores) - scores[0]):.4f}")
        summary.append(f"  平均得分: {np.mean(scores):.4f}")
        summary.append(f"  得分标准差: {np.std(scores):.4f}")
        
        return "\n".join(summary)

class AutoMLOptimizer:
    """自动机器学习优化器"""
    
    def __init__(self):
        self.optimizers = {}
        self.best_strategy = None
        self.best_score = -np.inf
    
    def auto_optimize(self, data: pd.DataFrame, target: pd.Series, 
                     strategies: List[str] = None) -> Dict[str, Any]:
        """自动优化，尝试多种策略"""
        if strategies is None:
            strategies = ["random_search", "grid_search"]
            if BAYESIAN_AVAILABLE:
                strategies.append("bayesian")
        
        logger.info(f"开始自动优化，策略: {strategies}")
        
        results = {}
        
        for strategy in strategies:
            logger.info(f"尝试策略: {strategy}")
            
            config = OptimizationConfig(
                strategy=strategy,
                max_iterations=30 if strategy == "grid_search" else 50
            )
            
            optimizer = HyperparameterOptimizer(config)
            result = optimizer.optimize(data, target)
            
            results[strategy] = result
            self.optimizers[strategy] = optimizer
            
            if result['best_score'] > self.best_score:
                self.best_score = result['best_score']
                self.best_strategy = strategy
        
        logger.info(f"自动优化完成，最佳策略: {self.best_strategy}, 最佳得分: {self.best_score:.4f}")
        
        return {
            'best_strategy': self.best_strategy,
            'best_score': self.best_score,
            'all_results': results
        }
    
    def get_best_optimizer(self) -> Optional[HyperparameterOptimizer]:
        """获取最佳优化器"""
        if self.best_strategy is None:
            return None
        
        return self.optimizers.get(self.best_strategy)