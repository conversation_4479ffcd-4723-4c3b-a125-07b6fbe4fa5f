"""
模型训练和更新系统简化测试
测试核心功能的集成
"""

import os
import sys
import json
import tempfile
import shutil
from datetime import datetime, timedelta
from pathlib import Path
import numpy as np

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent.parent.parent))

from qlib_trading_system.models.stock_selection.incremental_trainer import (
    IncrementalTrainer, IncrementalConfig
)
from qlib_trading_system.models.stock_selection.model_version_manager import (
    ModelVersionManager, ModelMetadata, ModelStatus
)
from qlib_trading_system.models.stock_selection.performance_monitor_advanced import (
    PerformanceMonitorAdvanced, PerformanceMetric, MetricType
)
from qlib_trading_system.models.stock_selection.ab_testing_framework import (
    ABTestingFramework, ExperimentConfig, ExperimentVariant, TrafficSplitMethod
)
from qlib_trading_system.models.stock_selection.training_config_manager import (
    TrainingConfigManager
)


def test_incremental_trainer():
    """测试增量训练器"""
    print("🔄 测试增量训练器...")
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    
    try:
        # 创建配置
        config = IncrementalConfig(
            batch_size=100,
            min_samples_for_update=50,
            update_frequency='daily'
        )
        
        # 创建训练器
        trainer = IncrementalTrainer(config, temp_dir)
        
        # 生成测试数据
        np.random.seed(42)
        X_train = np.random.randn(500, 10)
        y_train = np.random.randn(500)
        X_val = np.random.randn(100, 10)
        y_val = np.random.randn(100)
        
        # 初始化基础模型
        result = trainer.initialize_base_model(X_train, y_train, X_val, y_val)
        assert result['train_score'] is not None
        print(f"  ✅ 基础模型初始化成功，训练分数: {result['train_score']:.4f}")
        
        # 添加增量数据
        X_new = np.random.randn(60, 10)
        y_new = np.random.randn(60)
        trainer.add_training_data(X_new, y_new)
        
        # 执行增量更新
        update_result = trainer.perform_incremental_update()
        assert update_result['status'] in ['success', 'rejected']
        print(f"  ✅ 增量更新完成，状态: {update_result['status']}")
        
        # 测试在线预测
        X_test = np.random.randn(10, 10)
        predictions = trainer.online_predict_and_learn(X_test)
        assert len(predictions) == 10
        print(f"  ✅ 在线预测成功，预测数量: {len(predictions)}")
        
        print("  ✅ 增量训练器测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 增量训练器测试失败: {e}")
        return False
    finally:
        shutil.rmtree(temp_dir, ignore_errors=True)


def test_version_manager():
    """测试版本管理器"""
    print("📦 测试版本管理器...")
    
    temp_dir = tempfile.mkdtemp()
    
    try:
        # 创建版本管理器
        version_manager = ModelVersionManager(temp_dir)
        
        # 创建模拟模型
        from sklearn.ensemble import RandomForestRegressor
        model = RandomForestRegressor(n_estimators=10, random_state=42)
        
        # 训练模型
        X = np.random.randn(100, 5)
        y = np.random.randn(100)
        model.fit(X, y)
        
        # 创建元数据
        metadata = ModelMetadata(
            version="",
            name="test_model",
            description="测试模型",
            created_time=datetime.now(),
            model_type="RandomForest",
            framework="sklearn",
            train_score=0.85,
            validation_score=0.78,
            feature_count=5,
            training_samples=100
        )
        
        # 创建版本
        version = version_manager.create_version(model, metadata)
        assert version is not None
        print(f"  ✅ 版本创建成功: {version}")
        
        # 部署版本
        success = version_manager.deploy_version(version)
        assert success
        print(f"  ✅ 版本部署成功")
        
        # 获取活跃版本
        active_version = version_manager.get_active_version()
        assert active_version == version
        print(f"  ✅ 活跃版本获取成功: {active_version}")
        
        # 加载模型
        loaded_model, loaded_metadata = version_manager.load_active_model()
        assert loaded_model is not None
        assert loaded_metadata.name == "test_model"
        print(f"  ✅ 模型加载成功")
        
        # 列出版本
        versions = version_manager.list_versions()
        assert len(versions) >= 1
        print(f"  ✅ 版本列表获取成功，数量: {len(versions)}")
        
        print("  ✅ 版本管理器测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 版本管理器测试失败: {e}")
        return False
    finally:
        shutil.rmtree(temp_dir, ignore_errors=True)


def test_performance_monitor():
    """测试性能监控器"""
    print("📊 测试性能监控器...")
    
    temp_dir = tempfile.mkdtemp()
    config_file = Path(temp_dir) / "monitor_config.json"
    
    try:
        # 创建监控配置
        monitor_config = {
            "max_history_size": 1000,
            "drift_threshold": 0.05,
            "data_retention_days": 30,
            "email_alerts": {"enabled": False}
        }
        
        with open(config_file, 'w') as f:
            json.dump(monitor_config, f)
        
        # 创建性能监控器
        monitor = PerformanceMonitorAdvanced(str(config_file))
        
        # 添加性能指标
        for i in range(20):
            metric = PerformanceMetric(
                name="accuracy",
                value=0.75 + np.random.normal(0, 0.05),
                timestamp=datetime.now(),
                model_version="test_v1",
                data_source="test"
            )
            monitor.add_metric(metric)
        
        print(f"  ✅ 添加了20个性能指标")
        
        # 获取性能统计
        performance = monitor.get_current_performance(
            MetricType.ACCURACY, "test_v1", 3600
        )
        
        assert performance['status'] == 'success'
        assert performance['count'] == 20
        print(f"  ✅ 性能统计获取成功，平均值: {performance['mean']:.4f}")
        
        # 设置基线
        monitor.set_baseline(MetricType.ACCURACY, 0.75, "test_v1")
        print(f"  ✅ 基线设置成功")
        
        # 测试漂移检测
        drift_result = monitor.detect_model_drift(MetricType.ACCURACY, "test_v1")
        print(f"  ✅ 漂移检测完成，状态: {drift_result['status']}")
        
        print("  ✅ 性能监控器测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 性能监控器测试失败: {e}")
        return False
    finally:
        shutil.rmtree(temp_dir, ignore_errors=True)


def test_ab_framework():
    """测试A/B测试框架"""
    print("🧪 测试A/B测试框架...")
    
    temp_dir = tempfile.mkdtemp()
    
    try:
        # 创建A/B测试框架
        ab_framework = ABTestingFramework(temp_dir)
        
        # 创建实验配置
        config = ExperimentConfig(
            name="测试实验",
            description="测试A/B框架",
            start_time=datetime.now(),
            end_time=datetime.now() + timedelta(days=1),
            traffic_split_method=TrafficSplitMethod.HASH_BASED,
            traffic_allocation={"control": 0.5, "treatment": 0.5}
        )
        
        # 创建变体
        variants = [
            ExperimentVariant(
                name="control",
                description="对照组",
                model_version="v1.0",
                model_config={},
                is_control=True
            ),
            ExperimentVariant(
                name="treatment",
                description="实验组",
                model_version="v2.0",
                model_config={},
                is_control=False
            )
        ]
        
        # 创建实验
        experiment_id = ab_framework.create_experiment(config, variants)
        assert experiment_id is not None
        print(f"  ✅ 实验创建成功: {experiment_id}")
        
        # 启动实验
        success = ab_framework.start_experiment(experiment_id)
        assert success
        print(f"  ✅ 实验启动成功")
        
        # 模拟用户分配和指标记录
        for i in range(100):
            user_id = f"user_{i}"
            variant = ab_framework.assign_variant(experiment_id, user_id)
            
            # 模拟不同变体的性能
            if variant == "control":
                accuracy = np.random.normal(0.75, 0.05)
            else:
                accuracy = np.random.normal(0.78, 0.05)  # 稍好的性能
            
            ab_framework.record_metric(experiment_id, user_id, "accuracy", accuracy)
        
        print(f"  ✅ 模拟了100个用户的分配和指标记录")
        
        # 分析实验结果
        analysis = ab_framework.analyze_experiment(experiment_id, "accuracy")
        
        if analysis.get('status') != 'no_data':
            assert 'variant_results' in analysis
            print(f"  ✅ 实验分析完成，变体数量: {len(analysis['variant_results'])}")
        else:
            print(f"  ⚠️ 实验分析数据不足")
        
        # 获取实验状态
        status = ab_framework.get_experiment_status(experiment_id)
        assert status['experiment_id'] == experiment_id
        print(f"  ✅ 实验状态获取成功")
        
        print("  ✅ A/B测试框架测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ A/B测试框架测试失败: {e}")
        return False
    finally:
        shutil.rmtree(temp_dir, ignore_errors=True)


def test_config_manager():
    """测试配置管理器"""
    print("⚙️ 测试配置管理器...")
    
    temp_dir = tempfile.mkdtemp()
    
    try:
        # 创建配置管理器
        config_manager = TrainingConfigManager(temp_dir)
        
        # 获取各种配置
        model_config = config_manager.get_model_config()
        assert model_config.model_type == "lightgbm"
        print(f"  ✅ 模型配置获取成功: {model_config.model_type}")
        
        data_config = config_manager.get_data_config()
        assert data_config.primary_data_source == "itick"
        print(f"  ✅ 数据配置获取成功: {data_config.primary_data_source}")
        
        training_config = config_manager.get_training_config()
        assert training_config.incremental_config is not None
        print(f"  ✅ 训练配置获取成功")
        
        # 验证配置
        validation_results = config_manager.validate_all_configs()
        all_valid = all(len(errors) == 0 for errors in validation_results.values())
        assert all_valid
        print(f"  ✅ 配置验证通过")
        
        # 导出配置
        export_path = Path(temp_dir) / "exported_config.json"
        config_manager.export_config(str(export_path))
        assert export_path.exists()
        print(f"  ✅ 配置导出成功")
        
        print("  ✅ 配置管理器测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 配置管理器测试失败: {e}")
        return False
    finally:
        shutil.rmtree(temp_dir, ignore_errors=True)


def main():
    """运行所有测试"""
    print("🚀 开始模型训练和更新系统测试")
    print("=" * 60)
    
    test_results = []
    
    # 运行各个测试
    test_functions = [
        ("增量训练器", test_incremental_trainer),
        ("版本管理器", test_version_manager),
        ("性能监控器", test_performance_monitor),
        ("A/B测试框架", test_ab_framework),
        ("配置管理器", test_config_manager)
    ]
    
    for test_name, test_func in test_functions:
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"  ❌ {test_name}测试异常: {e}")
            test_results.append((test_name, False))
        
        print()  # 空行分隔
    
    # 输出测试结果汇总
    print("=" * 60)
    print("📋 测试结果汇总:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！模型训练和更新系统功能正常")
        return True
    else:
        print(f"\n⚠️ 有 {total - passed} 个测试失败，请检查相关功能")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)