"""
基础模型测试 - 不依赖外部ML库的简单测试
"""

import numpy as np
import pandas as pd
import sys
import os
from datetime import datetime
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_mock_data():
    """创建模拟数据"""
    np.random.seed(42)
    n_samples = 100
    n_features = 20
    
    # 生成特征数据
    features = np.random.randn(n_samples, n_features)
    feature_names = [f'feature_{i}' for i in range(n_features)]
    
    # 生成目标变量
    weights = np.random.randn(n_features) * 0.1
    target_continuous = features @ weights + np.random.randn(n_samples) * 0.5
    target = (target_continuous > np.percentile(target_continuous, 80)).astype(int)
    
    # 创建DataFrame
    data = pd.DataFrame(features, columns=feature_names)
    data['symbol'] = [f'stock_{i:04d}' for i in range(n_samples)]
    data['date'] = pd.date_range('2020-01-01', periods=n_samples, freq='D')
    data['is_explosive'] = target
    
    return data

def test_model_config():
    """测试模型配置"""
    logger.info("测试模型配置...")
    
    try:
        from explosive_model import ModelConfig
        
        # 测试默认配置
        config = ModelConfig()
        assert config.lstm_hidden_size == 128
        assert config.sequence_length == 30
        assert config.explosive_threshold == 1.0
        
        # 测试自定义配置
        custom_config = ModelConfig(
            lstm_hidden_size=64,
            sequence_length=15,
            explosive_threshold=0.5
        )
        assert custom_config.lstm_hidden_size == 64
        assert custom_config.sequence_length == 15
        
        logger.info("✓ 模型配置测试通过")
        return True
        
    except Exception as e:
        logger.error(f"模型配置测试失败: {e}")
        return False

def test_model_initialization():
    """测试模型初始化"""
    logger.info("测试模型初始化...")
    
    try:
        from explosive_model import ExplosiveStockModel, ModelConfig
        
        config = ModelConfig(lstm_hidden_size=64, sequence_length=10)
        model = ExplosiveStockModel(config)
        
        assert model.config == config
        assert not model.is_trained
        assert model.lgb_model is None
        assert model.lstm_model is None
        
        logger.info("✓ 模型初始化测试通过")
        return True
        
    except Exception as e:
        logger.error(f"模型初始化测试失败: {e}")
        return False

def test_training_framework():
    """测试训练框架"""
    logger.info("测试训练框架...")
    
    try:
        from training_framework import TrainingConfig, ModelTrainingFramework
        
        # 测试配置
        config = TrainingConfig()
        assert config.train_start_date == "2020-01-01"
        assert config.validation_split == 0.2
        
        # 测试框架初始化
        framework = ModelTrainingFramework(config)
        assert framework.config == config
        assert framework.best_model is None
        
        logger.info("✓ 训练框架测试通过")
        return True
        
    except Exception as e:
        logger.error(f"训练框架测试失败: {e}")
        return False

def test_hyperparameter_optimizer():
    """测试超参数优化器"""
    logger.info("测试超参数优化器...")
    
    try:
        from hyperparameter_optimizer import OptimizationConfig, HyperparameterOptimizer
        
        # 测试配置
        config = OptimizationConfig(strategy="random_search", max_iterations=10)
        assert config.strategy == "random_search"
        assert config.max_iterations == 10
        
        # 测试优化器初始化
        optimizer = HyperparameterOptimizer(config)
        assert optimizer.config == config
        assert optimizer.best_params is None
        
        # 测试搜索空间定义
        search_space = optimizer.define_search_space()
        assert 'lgb_params' in search_space
        assert 'lstm_hidden_size' in search_space
        
        logger.info("✓ 超参数优化器测试通过")
        return True
        
    except Exception as e:
        logger.error(f"超参数优化器测试失败: {e}")
        return False

def test_performance_monitor():
    """测试性能监控器"""
    logger.info("测试性能监控器...")
    
    try:
        from performance_monitor import AlertConfig, PerformanceMetrics, PerformanceMonitor
        from explosive_model import ExplosiveStockModel, ModelConfig
        
        # 测试告警配置
        alert_config = AlertConfig()
        assert alert_config.min_accuracy == 0.55
        assert alert_config.min_hit_rate == 0.3
        
        # 测试性能指标
        metrics = PerformanceMetrics(
            accuracy=0.8,
            precision=0.7,
            recall=0.6,
            f1_score=0.65,
            hit_rate=0.5
        )
        assert metrics.accuracy == 0.8
        assert metrics.hit_rate == 0.5
        
        # 测试监控器初始化（需要模型）
        model_config = ModelConfig()
        model = ExplosiveStockModel(model_config)
        monitor = PerformanceMonitor(model, alert_config)
        
        assert monitor.model == model
        assert monitor.alert_config == alert_config
        
        logger.info("✓ 性能监控器测试通过")
        return True
        
    except Exception as e:
        logger.error(f"性能监控器测试失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    logger.info("=" * 60)
    logger.info("开始爆发股识别模型基础测试")
    logger.info("=" * 60)
    
    tests = [
        test_model_config,
        test_model_initialization,
        test_training_framework,
        test_hyperparameter_optimizer,
        test_performance_monitor
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            logger.error(f"测试 {test_func.__name__} 出现异常: {e}")
    
    logger.info("=" * 60)
    logger.info(f"测试结果: {passed}/{total} 通过")
    logger.info("=" * 60)
    
    if passed == total:
        logger.info("🎉 所有基础测试通过！")
        logger.info("任务 4.2 开发爆发股识别模型 - 基础组件完成")
        return True
    else:
        logger.error("❌ 部分测试失败！")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    exit(0 if success else 1)