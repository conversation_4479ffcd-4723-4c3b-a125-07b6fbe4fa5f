"""
高级模型性能监控和报警系统
实现实时性能监控、异常检测和自动报警功能
"""

import os
import json
import time
import smtplib
import logging
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Callable
from dataclasses import dataclass, asdict
from pathlib import Path
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart
from collections import deque, defaultdict
import numpy as np
import pandas as pd
from enum import Enum
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats

from qlib_trading_system.utils.logging.logger import get_logger

logger = get_logger(__name__)


class AlertLevel(Enum):
    """报警级别"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class MetricType(Enum):
    """指标类型"""
    ACCURACY = "accuracy"
    PRECISION = "precision"
    RECALL = "recall"
    F1_SCORE = "f1_score"
    AUC = "auc"
    MSE = "mse"
    MAE = "mae"
    RMSE = "rmse"
    SHARPE_RATIO = "sharpe_ratio"
    MAX_DRAWDOWN = "max_drawdown"
    WIN_RATE = "win_rate"
    PROFIT_FACTOR = "profit_factor"
    PREDICTION_LATENCY = "prediction_latency"
    MODEL_DRIFT = "model_drift"


@dataclass
class PerformanceMetric:
    """性能指标"""
    name: str
    value: float
    timestamp: datetime
    model_version: str
    data_source: str
    metadata: Dict = None
    
    def to_dict(self) -> Dict:
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        return data


@dataclass
class AlertRule:
    """报警规则"""
    name: str
    metric_type: MetricType
    condition: str  # 'gt', 'lt', 'eq', 'change_rate'
    threshold: float
    alert_level: AlertLevel
    enabled: bool = True
    consecutive_violations: int = 1  # 连续违规次数
    time_window: int = 300  # 时间窗口（秒）
    
    def check_violation(self, value: float, baseline: float = None) -> bool:
        """检查是否违规"""
        if not self.enabled:
            return False
        
        if self.condition == 'gt':
            return value > self.threshold
        elif self.condition == 'lt':
            return value < self.threshold
        elif self.condition == 'eq':
            return abs(value - self.threshold) < 1e-6
        elif self.condition == 'change_rate' and baseline is not None:
            change_rate = abs(value - baseline) / abs(baseline) if baseline != 0 else float('inf')
            return change_rate > self.threshold
        
        return False


@dataclass
class Alert:
    """报警信息"""
    id: str
    rule_name: str
    metric_type: MetricType
    alert_level: AlertLevel
    message: str
    value: float
    threshold: float
    timestamp: datetime
    model_version: str
    resolved: bool = False
    resolved_time: Optional[datetime] = None
    
    def to_dict(self) -> Dict:
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        data['alert_level'] = self.alert_level.value
        data['metric_type'] = self.metric_type.value
        if self.resolved_time:
            data['resolved_time'] = self.resolved_time.isoformat()
        return data


class PerformanceMonitorAdvanced:
    """高级性能监控器"""
    
    def __init__(self, config_path: str = "config/monitor_config.json"):
        self.config_path = Path(config_path)
        self.config = self._load_config()
        
        # 数据存储
        self.metrics_history = defaultdict(deque)  # 按指标类型存储历史数据
        self.alerts_history = deque(maxlen=1000)
        self.baseline_metrics = {}  # 基线指标
        
        # 报警规则
        self.alert_rules = self._load_alert_rules()
        self.violation_counters = defaultdict(int)  # 违规计数器
        
        # 监控状态
        self.is_monitoring = False
        self.monitor_thread = None
        
        # 报警通道
        self.alert_handlers = []
        self._setup_alert_handlers()
        
        # 性能统计
        self.performance_stats = {
            'total_predictions': 0,
            'total_alerts': 0,
            'avg_latency': 0.0,
            'last_update': datetime.now()
        }
        
        logger.info("高级性能监控器初始化完成")
    
    def add_metric(self, metric: PerformanceMetric):
        """添加性能指标"""
        try:
            # 存储指标
            metric_key = f"{metric.name}_{metric.model_version}"
            self.metrics_history[metric_key].append(metric)
            
            # 限制历史数据大小
            max_history = self.config.get('max_history_size', 10000)
            if len(self.metrics_history[metric_key]) > max_history:
                self.metrics_history[metric_key].popleft()
            
            # 检查报警规则
            self._check_alert_rules(metric)
            
            # 更新统计信息
            self.performance_stats['total_predictions'] += 1
            self.performance_stats['last_update'] = datetime.now()
            
            logger.debug(f"添加性能指标: {metric.name}={metric.value}")
            
        except Exception as e:
            logger.error(f"添加性能指标失败: {e}")
    
    def batch_add_metrics(self, metrics: List[PerformanceMetric]):
        """批量添加性能指标"""
        for metric in metrics:
            self.add_metric(metric)
    
    def set_baseline(self, metric_type: MetricType, value: float, 
                    model_version: str = "baseline"):
        """设置基线指标"""
        baseline_key = f"{metric_type.value}_{model_version}"
        self.baseline_metrics[baseline_key] = value
        logger.info(f"设置基线指标: {baseline_key}={value}")
    
    def get_current_performance(self, metric_type: MetricType, 
                              model_version: str, 
                              time_window: int = 3600) -> Dict:
        """获取当前性能统计"""
        try:
            metric_key = f"{metric_type.value}_{model_version}"
            metrics = self.metrics_history.get(metric_key, deque())
            
            if not metrics:
                return {'status': 'no_data'}
            
            # 过滤时间窗口内的数据
            cutoff_time = datetime.now() - timedelta(seconds=time_window)
            recent_metrics = [m for m in metrics if m.timestamp >= cutoff_time]
            
            if not recent_metrics:
                return {'status': 'no_recent_data'}
            
            values = [m.value for m in recent_metrics]
            
            stats_result = {
                'status': 'success',
                'count': len(values),
                'mean': np.mean(values),
                'std': np.std(values),
                'min': np.min(values),
                'max': np.max(values),
                'median': np.median(values),
                'latest': values[-1],
                'trend': self._calculate_trend(values),
                'time_window': time_window
            }
            
            # 与基线比较
            baseline_key = f"{metric_type.value}_{model_version}"
            if baseline_key in self.baseline_metrics:
                baseline = self.baseline_metrics[baseline_key]
                stats_result['baseline'] = baseline
                stats_result['vs_baseline'] = stats_result['mean'] - baseline
                stats_result['improvement_pct'] = (stats_result['mean'] - baseline) / abs(baseline) * 100
            
            return stats_result
            
        except Exception as e:
            logger.error(f"获取当前性能失败: {e}")
            return {'status': 'error', 'message': str(e)}
    
    def detect_model_drift(self, metric_type: MetricType, model_version: str,
                          reference_window: int = 7200,  # 2小时
                          current_window: int = 1800) -> Dict:  # 30分钟
        """检测模型漂移"""
        try:
            metric_key = f"{metric_type.value}_{model_version}"
            metrics = self.metrics_history.get(metric_key, deque())
            
            if len(metrics) < 100:  # 需要足够的历史数据
                return {'status': 'insufficient_data'}
            
            now = datetime.now()
            
            # 参考期数据
            ref_start = now - timedelta(seconds=reference_window + current_window)
            ref_end = now - timedelta(seconds=current_window)
            ref_metrics = [m for m in metrics if ref_start <= m.timestamp <= ref_end]
            
            # 当前期数据
            current_start = now - timedelta(seconds=current_window)
            current_metrics = [m for m in metrics if m.timestamp >= current_start]
            
            if len(ref_metrics) < 30 or len(current_metrics) < 30:
                return {'status': 'insufficient_data'}
            
            ref_values = [m.value for m in ref_metrics]
            current_values = [m.value for m in current_metrics]
            
            # 统计检验
            statistic, p_value = stats.ks_2samp(ref_values, current_values)
            
            # 计算分布差异
            ref_mean, ref_std = np.mean(ref_values), np.std(ref_values)
            current_mean, current_std = np.mean(current_values), np.std(current_values)
            
            mean_shift = abs(current_mean - ref_mean) / ref_std if ref_std > 0 else 0
            std_ratio = current_std / ref_std if ref_std > 0 else 1
            
            # 漂移判断
            drift_threshold = self.config.get('drift_threshold', 0.05)
            is_drift = p_value < drift_threshold or mean_shift > 2.0 or std_ratio > 2.0 or std_ratio < 0.5
            
            result = {
                'status': 'success',
                'is_drift': is_drift,
                'p_value': p_value,
                'ks_statistic': statistic,
                'mean_shift': mean_shift,
                'std_ratio': std_ratio,
                'reference_stats': {'mean': ref_mean, 'std': ref_std, 'count': len(ref_values)},
                'current_stats': {'mean': current_mean, 'std': current_std, 'count': len(current_values)},
                'drift_score': max(mean_shift / 2.0, abs(1 - std_ratio), 1 - p_value / drift_threshold)
            }
            
            # 如果检测到漂移，生成报警
            if is_drift:
                self._generate_drift_alert(metric_type, model_version, result)
            
            return result
            
        except Exception as e:
            logger.error(f"模型漂移检测失败: {e}")
            return {'status': 'error', 'message': str(e)}
    
    def start_monitoring(self, interval: int = 60):
        """启动监控"""
        if self.is_monitoring:
            logger.warning("监控已在运行中")
            return
        
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitoring_loop,
            args=(interval,),
            daemon=True
        )
        self.monitor_thread.start()
        logger.info(f"性能监控已启动，监控间隔: {interval}秒")
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        logger.info("性能监控已停止")
    
    def add_alert_rule(self, rule: AlertRule):
        """添加报警规则"""
        self.alert_rules[rule.name] = rule
        logger.info(f"添加报警规则: {rule.name}")
    
    def remove_alert_rule(self, rule_name: str):
        """移除报警规则"""
        if rule_name in self.alert_rules:
            del self.alert_rules[rule_name]
            logger.info(f"移除报警规则: {rule_name}")
    
    def get_active_alerts(self) -> List[Alert]:
        """获取活跃报警"""
        return [alert for alert in self.alerts_history if not alert.resolved]
    
    def resolve_alert(self, alert_id: str):
        """解决报警"""
        for alert in self.alerts_history:
            if alert.id == alert_id and not alert.resolved:
                alert.resolved = True
                alert.resolved_time = datetime.now()
                logger.info(f"报警已解决: {alert_id}")
                break
    
    def generate_performance_report(self, model_version: str, 
                                  time_range: int = 86400) -> Dict:
        """生成性能报告"""
        try:
            report = {
                'model_version': model_version,
                'report_time': datetime.now().isoformat(),
                'time_range_seconds': time_range,
                'metrics_summary': {},
                'alerts_summary': {},
                'drift_analysis': {},
                'recommendations': []
            }
            
            # 指标汇总
            for metric_type in MetricType:
                performance = self.get_current_performance(
                    metric_type, model_version, time_range
                )
                if performance['status'] == 'success':
                    report['metrics_summary'][metric_type.value] = performance
            
            # 报警汇总
            recent_alerts = [
                alert for alert in self.alerts_history
                if alert.model_version == model_version and
                alert.timestamp >= datetime.now() - timedelta(seconds=time_range)
            ]
            
            alert_counts = defaultdict(int)
            for alert in recent_alerts:
                alert_counts[alert.alert_level.value] += 1
            
            report['alerts_summary'] = {
                'total_alerts': len(recent_alerts),
                'by_level': dict(alert_counts),
                'active_alerts': len([a for a in recent_alerts if not a.resolved])
            }
            
            # 漂移分析
            for metric_type in [MetricType.ACCURACY, MetricType.MSE, MetricType.SHARPE_RATIO]:
                drift_result = self.detect_model_drift(metric_type, model_version)
                if drift_result['status'] == 'success':
                    report['drift_analysis'][metric_type.value] = drift_result
            
            # 生成建议
            report['recommendations'] = self._generate_recommendations(report)
            
            return report
            
        except Exception as e:
            logger.error(f"生成性能报告失败: {e}")
            return {'status': 'error', 'message': str(e)}
    
    def export_metrics(self, output_path: str, format: str = 'csv'):
        """导出指标数据"""
        try:
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 收集所有指标数据
            all_metrics = []
            for metric_key, metrics in self.metrics_history.items():
                for metric in metrics:
                    all_metrics.append(metric.to_dict())
            
            if format.lower() == 'csv':
                df = pd.DataFrame(all_metrics)
                df.to_csv(output_path, index=False)
            elif format.lower() == 'json':
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(all_metrics, f, indent=2, ensure_ascii=False)
            
            logger.info(f"指标数据已导出到: {output_path}")
            
        except Exception as e:
            logger.error(f"导出指标数据失败: {e}")
    
    def create_performance_dashboard(self, model_version: str, 
                                   output_path: str = "dashboard.html"):
        """创建性能仪表板"""
        try:
            # 生成图表
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle(f'Model Performance Dashboard - {model_version}', fontsize=16)
            
            # 准确率趋势
            self._plot_metric_trend(MetricType.ACCURACY, model_version, axes[0, 0], 'Accuracy Trend')
            
            # 延迟分布
            self._plot_metric_distribution(MetricType.PREDICTION_LATENCY, model_version, axes[0, 1], 'Latency Distribution')
            
            # 报警统计
            self._plot_alert_statistics(model_version, axes[1, 0], 'Alert Statistics')
            
            # 性能对比
            self._plot_performance_comparison(model_version, axes[1, 1], 'Performance vs Baseline')
            
            plt.tight_layout()
            plt.savefig(output_path.replace('.html', '.png'), dpi=300, bbox_inches='tight')
            plt.close()
            
            # 生成HTML报告
            html_content = self._generate_html_dashboard(model_version, output_path.replace('.html', '.png'))
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            logger.info(f"性能仪表板已生成: {output_path}")
            
        except Exception as e:
            logger.error(f"创建性能仪表板失败: {e}")
    
    def _check_alert_rules(self, metric: PerformanceMetric):
        """检查报警规则"""
        try:
            for rule_name, rule in self.alert_rules.items():
                if rule.metric_type.value != metric.name:
                    continue
                
                # 获取基线值
                baseline_key = f"{metric.name}_{metric.model_version}"
                baseline = self.baseline_metrics.get(baseline_key)
                
                # 检查违规
                is_violation = rule.check_violation(metric.value, baseline)
                
                if is_violation:
                    self.violation_counters[rule_name] += 1
                    
                    # 检查连续违规次数
                    if self.violation_counters[rule_name] >= rule.consecutive_violations:
                        self._generate_alert(rule, metric, baseline)
                        self.violation_counters[rule_name] = 0  # 重置计数器
                else:
                    self.violation_counters[rule_name] = 0  # 重置计数器
                    
        except Exception as e:
            logger.error(f"检查报警规则失败: {e}")
    
    def _generate_alert(self, rule: AlertRule, metric: PerformanceMetric, baseline: float = None):
        """生成报警"""
        try:
            alert_id = f"alert_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{rule.name}"
            
            message = f"指标 {metric.name} 触发报警规则 {rule.name}: "
            message += f"当前值 {metric.value:.4f}, 阈值 {rule.threshold:.4f}"
            
            if baseline is not None:
                message += f", 基线值 {baseline:.4f}"
            
            alert = Alert(
                id=alert_id,
                rule_name=rule.name,
                metric_type=MetricType(metric.name),
                alert_level=rule.alert_level,
                message=message,
                value=metric.value,
                threshold=rule.threshold,
                timestamp=datetime.now(),
                model_version=metric.model_version
            )
            
            self.alerts_history.append(alert)
            self.performance_stats['total_alerts'] += 1
            
            # 发送报警
            self._send_alert(alert)
            
            logger.warning(f"生成报警: {message}")
            
        except Exception as e:
            logger.error(f"生成报警失败: {e}")
    
    def _generate_drift_alert(self, metric_type: MetricType, model_version: str, drift_result: Dict):
        """生成漂移报警"""
        try:
            alert_id = f"drift_alert_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{metric_type.value}"
            
            message = f"检测到模型漂移 - 指标: {metric_type.value}, "
            message += f"漂移分数: {drift_result['drift_score']:.4f}, "
            message += f"p值: {drift_result['p_value']:.6f}"
            
            alert = Alert(
                id=alert_id,
                rule_name="model_drift_detection",
                metric_type=metric_type,
                alert_level=AlertLevel.WARNING,
                message=message,
                value=drift_result['drift_score'],
                threshold=0.5,
                timestamp=datetime.now(),
                model_version=model_version
            )
            
            self.alerts_history.append(alert)
            self._send_alert(alert)
            
            logger.warning(f"生成漂移报警: {message}")
            
        except Exception as e:
            logger.error(f"生成漂移报警失败: {e}")
    
    def _send_alert(self, alert: Alert):
        """发送报警"""
        for handler in self.alert_handlers:
            try:
                handler(alert)
            except Exception as e:
                logger.error(f"发送报警失败: {e}")
    
    def _setup_alert_handlers(self):
        """设置报警处理器"""
        # 邮件报警
        if self.config.get('email_alerts', {}).get('enabled', False):
            self.alert_handlers.append(self._email_alert_handler)
        
        # 日志报警
        self.alert_handlers.append(self._log_alert_handler)
        
        # 可以添加更多报警通道：钉钉、微信、Slack等
    
    def _email_alert_handler(self, alert: Alert):
        """邮件报警处理器"""
        try:
            email_config = self.config.get('email_alerts', {})
            
            msg = MimeMultipart()
            msg['From'] = email_config['from_email']
            msg['To'] = ', '.join(email_config['to_emails'])
            msg['Subject'] = f"[{alert.alert_level.value.upper()}] 模型性能报警 - {alert.rule_name}"
            
            body = f"""
            报警详情:
            - 报警ID: {alert.id}
            - 模型版本: {alert.model_version}
            - 指标类型: {alert.metric_type.value}
            - 报警级别: {alert.alert_level.value}
            - 当前值: {alert.value:.4f}
            - 阈值: {alert.threshold:.4f}
            - 时间: {alert.timestamp.strftime('%Y-%m-%d %H:%M:%S')}
            - 消息: {alert.message}
            """
            
            msg.attach(MimeText(body, 'plain', 'utf-8'))
            
            server = smtplib.SMTP(email_config['smtp_server'], email_config['smtp_port'])
            server.starttls()
            server.login(email_config['username'], email_config['password'])
            server.send_message(msg)
            server.quit()
            
            logger.info(f"邮件报警已发送: {alert.id}")
            
        except Exception as e:
            logger.error(f"发送邮件报警失败: {e}")
    
    def _log_alert_handler(self, alert: Alert):
        """日志报警处理器"""
        log_level = {
            AlertLevel.INFO: logger.info,
            AlertLevel.WARNING: logger.warning,
            AlertLevel.ERROR: logger.error,
            AlertLevel.CRITICAL: logger.critical
        }
        
        log_func = log_level.get(alert.alert_level, logger.info)
        log_func(f"[ALERT] {alert.message}")
    
    def _monitoring_loop(self, interval: int):
        """监控循环"""
        while self.is_monitoring:
            try:
                # 执行定期检查
                self._periodic_checks()
                time.sleep(interval)
            except Exception as e:
                logger.error(f"监控循环异常: {e}")
                time.sleep(interval)
    
    def _periodic_checks(self):
        """定期检查"""
        try:
            # 检查所有活跃模型的漂移
            active_models = set()
            for metric_key in self.metrics_history.keys():
                if '_' in metric_key:
                    model_version = metric_key.split('_', 1)[1]
                    active_models.add(model_version)
            
            for model_version in active_models:
                for metric_type in [MetricType.ACCURACY, MetricType.MSE]:
                    self.detect_model_drift(metric_type, model_version)
            
            # 清理过期数据
            self._cleanup_old_data()
            
        except Exception as e:
            logger.error(f"定期检查失败: {e}")
    
    def _cleanup_old_data(self):
        """清理过期数据"""
        try:
            retention_days = self.config.get('data_retention_days', 30)
            cutoff_time = datetime.now() - timedelta(days=retention_days)
            
            # 清理指标历史
            for metric_key in self.metrics_history:
                metrics = self.metrics_history[metric_key]
                while metrics and metrics[0].timestamp < cutoff_time:
                    metrics.popleft()
            
            # 清理报警历史
            while self.alerts_history and self.alerts_history[0].timestamp < cutoff_time:
                self.alerts_history.popleft()
                
        except Exception as e:
            logger.error(f"清理过期数据失败: {e}")
    
    def _calculate_trend(self, values: List[float]) -> str:
        """计算趋势"""
        if len(values) < 2:
            return 'stable'
        
        # 使用线性回归计算趋势
        x = np.arange(len(values))
        slope, _, _, _, _ = stats.linregress(x, values)
        
        if slope > 0.01:
            return 'increasing'
        elif slope < -0.01:
            return 'decreasing'
        else:
            return 'stable'
    
    def _plot_metric_trend(self, metric_type: MetricType, model_version: str, ax, title: str):
        """绘制指标趋势图"""
        metric_key = f"{metric_type.value}_{model_version}"
        metrics = self.metrics_history.get(metric_key, deque())
        
        if not metrics:
            ax.text(0.5, 0.5, 'No Data', ha='center', va='center', transform=ax.transAxes)
            ax.set_title(title)
            return
        
        timestamps = [m.timestamp for m in metrics]
        values = [m.value for m in metrics]
        
        ax.plot(timestamps, values, 'b-', alpha=0.7)
        ax.set_title(title)
        ax.set_xlabel('Time')
        ax.set_ylabel(metric_type.value)
        ax.tick_params(axis='x', rotation=45)
    
    def _plot_metric_distribution(self, metric_type: MetricType, model_version: str, ax, title: str):
        """绘制指标分布图"""
        metric_key = f"{metric_type.value}_{model_version}"
        metrics = self.metrics_history.get(metric_key, deque())
        
        if not metrics:
            ax.text(0.5, 0.5, 'No Data', ha='center', va='center', transform=ax.transAxes)
            ax.set_title(title)
            return
        
        values = [m.value for m in metrics]
        ax.hist(values, bins=30, alpha=0.7, edgecolor='black')
        ax.set_title(title)
        ax.set_xlabel(metric_type.value)
        ax.set_ylabel('Frequency')
    
    def _plot_alert_statistics(self, model_version: str, ax, title: str):
        """绘制报警统计图"""
        model_alerts = [a for a in self.alerts_history if a.model_version == model_version]
        
        if not model_alerts:
            ax.text(0.5, 0.5, 'No Alerts', ha='center', va='center', transform=ax.transAxes)
            ax.set_title(title)
            return
        
        alert_counts = defaultdict(int)
        for alert in model_alerts:
            alert_counts[alert.alert_level.value] += 1
        
        levels = list(alert_counts.keys())
        counts = list(alert_counts.values())
        
        ax.bar(levels, counts, alpha=0.7)
        ax.set_title(title)
        ax.set_xlabel('Alert Level')
        ax.set_ylabel('Count')
    
    def _plot_performance_comparison(self, model_version: str, ax, title: str):
        """绘制性能对比图"""
        # 简化的性能对比图
        metrics = ['Accuracy', 'Precision', 'Recall']
        current_values = []
        baseline_values = []
        
        for metric in metrics:
            metric_type = MetricType(metric.lower())
            perf = self.get_current_performance(metric_type, model_version)
            if perf['status'] == 'success':
                current_values.append(perf['mean'])
                baseline_values.append(perf.get('baseline', perf['mean']))
            else:
                current_values.append(0)
                baseline_values.append(0)
        
        x = np.arange(len(metrics))
        width = 0.35
        
        ax.bar(x - width/2, current_values, width, label='Current', alpha=0.7)
        ax.bar(x + width/2, baseline_values, width, label='Baseline', alpha=0.7)
        
        ax.set_title(title)
        ax.set_xlabel('Metrics')
        ax.set_ylabel('Value')
        ax.set_xticks(x)
        ax.set_xticklabels(metrics)
        ax.legend()
    
    def _generate_html_dashboard(self, model_version: str, chart_path: str) -> str:
        """生成HTML仪表板"""
        html_template = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Model Performance Dashboard - {model_version}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ text-align: center; color: #333; }}
                .chart {{ text-align: center; margin: 20px 0; }}
                .stats {{ display: flex; justify-content: space-around; margin: 20px 0; }}
                .stat-box {{ border: 1px solid #ddd; padding: 15px; border-radius: 5px; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Model Performance Dashboard</h1>
                <h2>{model_version}</h2>
                <p>Generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>
            
            <div class="chart">
                <img src="{chart_path}" alt="Performance Charts" style="max-width: 100%;">
            </div>
            
            <div class="stats">
                <div class="stat-box">
                    <h3>Total Predictions</h3>
                    <p>{self.performance_stats['total_predictions']}</p>
                </div>
                <div class="stat-box">
                    <h3>Total Alerts</h3>
                    <p>{self.performance_stats['total_alerts']}</p>
                </div>
                <div class="stat-box">
                    <h3>Active Alerts</h3>
                    <p>{len(self.get_active_alerts())}</p>
                </div>
            </div>
        </body>
        </html>
        """
        return html_template
    
    def _generate_recommendations(self, report: Dict) -> List[str]:
        """生成建议"""
        recommendations = []
        
        # 基于报警数量的建议
        total_alerts = report['alerts_summary']['total_alerts']
        if total_alerts > 10:
            recommendations.append("报警数量较多，建议检查模型性能和报警规则设置")
        
        # 基于漂移分析的建议
        for metric, drift_info in report['drift_analysis'].items():
            if drift_info.get('is_drift', False):
                recommendations.append(f"检测到{metric}指标漂移，建议重新训练模型")
        
        # 基于性能指标的建议
        for metric, perf_info in report['metrics_summary'].items():
            if 'vs_baseline' in perf_info and perf_info['vs_baseline'] < -0.05:
                recommendations.append(f"{metric}指标相比基线下降明显，建议优化模型")
        
        return recommendations
    
    def _load_config(self) -> Dict:
        """加载配置"""
        default_config = {
            'max_history_size': 10000,
            'data_retention_days': 30,
            'drift_threshold': 0.05,
            'email_alerts': {
                'enabled': False,
                'smtp_server': 'smtp.gmail.com',
                'smtp_port': 587,
                'from_email': '',
                'to_emails': [],
                'username': '',
                'password': ''
            }
        }
        
        if self.config_path.exists():
            try:
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                default_config.update(config)
            except Exception as e:
                logger.error(f"加载配置失败: {e}")
        
        return default_config
    
    def _load_alert_rules(self) -> Dict[str, AlertRule]:
        """加载报警规则"""
        default_rules = {
            'accuracy_low': AlertRule(
                name='accuracy_low',
                metric_type=MetricType.ACCURACY,
                condition='lt',
                threshold=0.6,
                alert_level=AlertLevel.WARNING
            ),
            'mse_high': AlertRule(
                name='mse_high',
                metric_type=MetricType.MSE,
                condition='gt',
                threshold=0.1,
                alert_level=AlertLevel.WARNING
            ),
            'latency_high': AlertRule(
                name='latency_high',
                metric_type=MetricType.PREDICTION_LATENCY,
                condition='gt',
                threshold=1.0,
                alert_level=AlertLevel.ERROR
            )
        }
        
        return default_rules


# 使用示例
if __name__ == "__main__":
    # 创建性能监控器
    monitor = PerformanceMonitorAdvanced()
    
    # 设置基线
    monitor.set_baseline(MetricType.ACCURACY, 0.75, "model_v1")
    monitor.set_baseline(MetricType.MSE, 0.05, "model_v1")
    
    # 模拟添加性能指标
    for i in range(100):
        # 模拟准确率指标
        accuracy_metric = PerformanceMetric(
            name=MetricType.ACCURACY.value,
            value=0.7 + np.random.normal(0, 0.05),
            timestamp=datetime.now() - timedelta(minutes=i),
            model_version="model_v1",
            data_source="test"
        )
        monitor.add_metric(accuracy_metric)
        
        # 模拟延迟指标
        latency_metric = PerformanceMetric(
            name=MetricType.PREDICTION_LATENCY.value,
            value=0.5 + np.random.exponential(0.2),
            timestamp=datetime.now() - timedelta(minutes=i),
            model_version="model_v1",
            data_source="test"
        )
        monitor.add_metric(latency_metric)
    
    # 启动监控
    monitor.start_monitoring(interval=30)
    
    # 生成性能报告
    report = monitor.generate_performance_report("model_v1")
    print("性能报告:", json.dumps(report, indent=2, ensure_ascii=False))
    
    # 检测模型漂移
    drift_result = monitor.detect_model_drift(MetricType.ACCURACY, "model_v1")
    print("漂移检测结果:", drift_result)
    
    # 创建仪表板
    monitor.create_performance_dashboard("model_v1", "dashboard.html")
    
    # 停止监控
    time.sleep(5)
    monitor.stop_monitoring()