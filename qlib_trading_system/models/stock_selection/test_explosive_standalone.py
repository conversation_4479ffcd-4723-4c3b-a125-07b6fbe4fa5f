"""
爆发股识别模型独立测试
测试LightGBM+LSTM混合模型的核心功能
"""

import numpy as np
import pandas as pd
import logging
from datetime import datetime, timedelta
import sys
import os

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_sample_data(n_samples=1000, n_features=50):
    """创建测试数据"""
    np.random.seed(42)
    
    # 生成特征数据
    features = np.random.randn(n_samples, n_features)
    feature_names = [f'feature_{i}' for i in range(n_features)]
    
    # 生成目标变量（爆发股标签）
    # 使用特征的线性组合加噪声
    weights = np.random.randn(n_features) * 0.1
    target_continuous = features @ weights + np.random.randn(n_samples) * 0.5
    target = (target_continuous > np.percentile(target_continuous, 80)).astype(int)
    
    # 创建DataFrame
    data = pd.DataFrame(features, columns=feature_names)
    data['symbol'] = [f'stock_{i:04d}' for i in range(n_samples)]
    data['date'] = pd.date_range('2020-01-01', periods=n_samples, freq='D')
    data['is_explosive'] = target
    
    return data

def test_model_basic_functionality():
    """测试模型基础功能"""
    logger.info("=" * 60)
    logger.info("测试爆发股识别模型基础功能")
    logger.info("=" * 60)
    
    try:
        # 导入模型
        from explosive_model import ExplosiveStockModel, ModelConfig
        
        # 创建模型配置
        config = ModelConfig(
            lstm_hidden_size=64,
            lstm_num_layers=1,
            sequence_length=10,
            test_size=0.2,
            random_state=42
        )
        
        # 创建模型
        model = ExplosiveStockModel(config)
        logger.info("✓ 模型创建成功")
        
        # 创建测试数据
        data = create_sample_data(n_samples=500, n_features=30)
        logger.info(f"✓ 测试数据创建成功: {len(data)} 样本, {len(data.columns)-3} 特征")
        
        # 准备训练数据
        features = data.drop(columns=['is_explosive'])
        target = data['is_explosive']
        
        logger.info(f"正样本数量: {target.sum()}, 负样本数量: {len(target) - target.sum()}")
        
        # 训练模型
        logger.info("开始训练模型...")
        training_results = model.train(features, target)
        
        # 验证训练结果
        assert model.is_trained, "模型应该已训练"
        assert model.lgb_model is not None, "LightGBM模型应该存在"
        assert 'train_metrics' in training_results, "应该有训练指标"
        assert 'test_metrics' in training_results, "应该有测试指标"
        
        logger.info("✓ 模型训练成功")
        logger.info(f"  训练集准确率: {training_results['train_metrics']['accuracy']:.4f}")
        logger.info(f"  测试集准确率: {training_results['test_metrics']['accuracy']:.4f}")
        
        # 测试预测功能
        test_data = create_sample_data(n_samples=100, n_features=30)
        test_features = test_data.drop(columns=['is_explosive'])
        
        predictions = model.predict(test_features)
        logger.info(f"✓ 预测功能测试成功: 预测了 {len(predictions)} 个样本")
        logger.info(f"  预测概率范围: [{predictions.min():.4f}, {predictions.max():.4f}]")
        
        # 测试爆发股筛选
        top_stocks = model.predict_explosive_stocks(test_features, top_n=10)
        logger.info(f"✓ 爆发股筛选成功: 筛选出 {len(top_stocks)} 只股票")
        
        if top_stocks:
            logger.info("Top 5 爆发潜力股票:")
            for i, (symbol, score) in enumerate(top_stocks[:5]):
                logger.info(f"  {i+1}. {symbol}: {score:.4f}")
        
        # 测试特征重要性
        importance = model._get_feature_importance()
        if importance:
            top_features = list(importance.items())[:5]
            logger.info("Top 5 重要特征:")
            for feature, score in top_features:
                logger.info(f"  {feature}: {score:.2f}")
        
        # 测试模型信息
        model_info = model.get_model_info()
        logger.info("✓ 模型信息获取成功")
        logger.info(f"  特征数量: {model_info['feature_count']}")
        logger.info(f"  设备: {model_info['device']}")
        logger.info(f"  有LightGBM模型: {model_info['has_lgb_model']}")
        logger.info(f"  有LSTM模型: {model_info['has_lstm_model']}")
        
        return True
        
    except Exception as e:
        logger.error(f"模型基础功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_training_framework():
    """测试训练框架"""
    logger.info("=" * 60)
    logger.info("测试训练框架")
    logger.info("=" * 60)
    
    try:
        from training_framework import ModelTrainingFramework, TrainingConfig
        from explosive_model import ModelConfig
        
        # 创建训练配置
        training_config = TrainingConfig(
            time_series_cv_splits=3,
            max_training_samples=500
        )
        
        # 创建训练框架
        framework = ModelTrainingFramework(training_config)
        logger.info("✓ 训练框架创建成功")
        
        # 创建测试数据
        data = create_sample_data(n_samples=500, n_features=20)
        features = data.drop(columns=['is_explosive'])
        target = data['is_explosive']
        
        # 准备训练数据
        prepared_features, prepared_target = framework.prepare_training_data(
            data, target_column='is_explosive'
        )
        logger.info(f"✓ 训练数据准备成功: {len(prepared_features)} 样本")
        
        # 创建模型配置
        model_config = ModelConfig(
            lstm_hidden_size=32,
            lstm_num_layers=1,
            sequence_length=5,
            test_size=0.2
        )
        
        # 执行时间序列验证
        cv_results = framework.time_series_validation(
            prepared_features, prepared_target, model_config
        )
        logger.info("✓ 时间序列交叉验证成功")
        logger.info(f"  平均准确率: {cv_results['avg_metrics'].get('accuracy_mean', 0):.4f}")
        logger.info(f"  平均F1得分: {cv_results['avg_metrics'].get('f1_score_mean', 0):.4f}")
        
        return True
        
    except Exception as e:
        logger.error(f"训练框架测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_hyperparameter_optimizer():
    """测试超参数优化器"""
    logger.info("=" * 60)
    logger.info("测试超参数优化器")
    logger.info("=" * 60)
    
    try:
        from hyperparameter_optimizer import HyperparameterOptimizer, OptimizationConfig
        
        # 创建优化配置
        opt_config = OptimizationConfig(
            strategy="random_search",
            max_iterations=5,  # 减少迭代次数以加快测试
            cv_folds=2
        )
        
        # 创建优化器
        optimizer = HyperparameterOptimizer(opt_config)
        logger.info("✓ 超参数优化器创建成功")
        
        # 创建测试数据
        data = create_sample_data(n_samples=200, n_features=15)
        features = data.drop(columns=['is_explosive'])
        target = data['is_explosive']
        
        # 定义搜索空间
        search_space = optimizer.define_search_space()
        logger.info(f"✓ 搜索空间定义成功: {len(search_space)} 个参数组")
        
        # 执行随机搜索优化
        optimization_results = optimizer.random_search_optimization(features, target)
        logger.info("✓ 随机搜索优化成功")
        logger.info(f"  最佳得分: {optimization_results['best_score']:.4f}")
        logger.info(f"  总迭代次数: {optimization_results['total_iterations']}")
        
        # 获取最佳模型配置
        best_config = optimizer.get_best_model_config()
        if best_config:
            logger.info("✓ 最佳模型配置获取成功")
            logger.info(f"  LSTM隐藏层大小: {best_config.lstm_hidden_size}")
            logger.info(f"  序列长度: {best_config.sequence_length}")
        
        # 获取优化摘要
        summary = optimizer.get_optimization_summary()
        logger.info("✓ 优化摘要生成成功")
        
        return True
        
    except Exception as e:
        logger.error(f"超参数优化器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance_monitor():
    """测试性能监控器"""
    logger.info("=" * 60)
    logger.info("测试性能监控器")
    logger.info("=" * 60)
    
    try:
        from performance_monitor import PerformanceMonitor, AlertConfig, PerformanceMetrics
        from explosive_model import ExplosiveStockModel, ModelConfig
        
        # 创建并训练一个简单模型
        model_config = ModelConfig(
            lstm_hidden_size=32,
            lstm_num_layers=1,
            sequence_length=5
        )
        model = ExplosiveStockModel(model_config)
        
        # 训练模型
        data = create_sample_data(n_samples=200, n_features=10)
        features = data.drop(columns=['is_explosive'])
        target = data['is_explosive']
        model.train(features, target)
        
        # 创建性能监控器
        alert_config = AlertConfig(
            min_accuracy=0.4,  # 降低阈值以便测试
            min_hit_rate=0.2
        )
        monitor = PerformanceMonitor(model, alert_config)
        logger.info("✓ 性能监控器创建成功")
        
        # 创建预测和实际结果数据
        test_data = create_sample_data(n_samples=100, n_features=10)
        test_features = test_data.drop(columns=['is_explosive'])
        predictions = model.predict(test_features)
        
        # 创建预测DataFrame
        predictions_df = pd.DataFrame({
            'symbol': test_data['symbol'],
            'date': test_data['date'],
            'prediction_score': predictions
        })
        
        # 创建实际结果DataFrame
        actual_results_df = pd.DataFrame({
            'symbol': test_data['symbol'],
            'date': test_data['date'],
            'actual_explosive': test_data['is_explosive']
        })
        
        # 评估日度性能
        performance = monitor.evaluate_daily_performance(predictions_df, actual_results_df)
        logger.info("✓ 日度性能评估成功")
        logger.info(f"  准确率: {performance.accuracy:.4f}")
        logger.info(f"  命中率: {performance.hit_rate:.4f}")
        logger.info(f"  F1得分: {performance.f1_score:.4f}")
        
        # 记录预测结果
        monitor.record_predictions(predictions_df)
        logger.info("✓ 预测结果记录成功")
        
        # 生成性能报告
        report = monitor.generate_performance_report(days=1)
        logger.info("✓ 性能报告生成成功")
        
        # 获取模型健康度评分
        health_score = monitor.get_model_health_score()
        logger.info(f"✓ 模型健康度评分: {health_score:.4f}")
        
        # 获取活跃告警
        active_alerts = monitor.get_active_alerts()
        logger.info(f"✓ 活跃告警数量: {len(active_alerts)}")
        
        return True
        
    except Exception as e:
        logger.error(f"性能监控器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    logger.info("开始爆发股识别模型完整测试")
    
    tests = [
        ("模型基础功能", test_model_basic_functionality),
        ("训练框架", test_training_framework),
        ("超参数优化器", test_hyperparameter_optimizer),
        ("性能监控器", test_performance_monitor)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n开始测试: {test_name}")
        try:
            if test_func():
                logger.info(f"✓ {test_name} 测试通过")
                passed += 1
            else:
                logger.error(f"✗ {test_name} 测试失败")
        except Exception as e:
            logger.error(f"✗ {test_name} 测试异常: {e}")
    
    logger.info("=" * 60)
    logger.info(f"测试完成: {passed}/{total} 通过")
    logger.info("=" * 60)
    
    if passed == total:
        logger.info("🎉 所有测试通过！爆发股识别模型实现完成！")
        logger.info("任务 4.2 开发爆发股识别模型 - 完成")
        return True
    else:
        logger.error("❌ 部分测试失败！")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)