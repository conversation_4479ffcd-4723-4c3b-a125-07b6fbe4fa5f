"""
特征选择和降维算法模块

实现多种特征选择方法和降维技术
为爆发股识别模型提供最优特征子集
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union
from sklearn.feature_selection import (
    SelectKBest, SelectPercentile, RFE, RFECV,
    f_regression, mutual_info_regression, chi2
)
from sklearn.decomposition import PCA, FastICA, TruncatedSVD
from sklearn.manifold import TSNE
from sklearn.ensemble import RandomForestRegressor, ExtraTreesRegressor
from sklearn.linear_model import LassoCV, ElasticNetCV
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import cross_val_score
import logging
from dataclasses import dataclass
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)


@dataclass
class FeatureSelectionResult:
    """特征选择结果"""
    selected_features: List[str]
    feature_scores: Dict[str, float]
    selection_method: str
    n_features_before: int
    n_features_after: int
    selection_time: float
    cross_val_score: Optional[float] = None


@dataclass
class DimensionalityReductionResult:
    """降维结果"""
    transformed_data: pd.DataFrame
    explained_variance_ratio: Optional[np.ndarray] = None
    n_components: int = 0
    reduction_method: str = ""
    reduction_time: float = 0.0


class BaseFeatureSelector(ABC):
    """特征选择基类"""
    
    @abstractmethod
    def select_features(self, X: pd.DataFrame, y: pd.Series) -> FeatureSelectionResult:
        pass


class UnivariateFeatureSelector(BaseFeatureSelector):
    """单变量特征选择器"""
    
    def __init__(self, method: str = 'mutual_info', k: int = 100):
        self.method = method
        self.k = k
        self.selector = None
    
    def select_features(self, X: pd.DataFrame, y: pd.Series) -> FeatureSelectionResult:
        """执行单变量特征选择"""
        import time
        start_time = time.time()
        
        logger.info(f"开始单变量特征选择，方法: {self.method}, k={self.k}")
        
        # 选择评分函数
        if self.method == 'mutual_info':
            score_func = mutual_info_regression
        elif self.method == 'f_regression':
            score_func = f_regression
        else:
            raise ValueError(f"不支持的方法: {self.method}")
        
        # 创建选择器
        self.selector = SelectKBest(score_func=score_func, k=min(self.k, X.shape[1]))
        
        # 执行特征选择
        X_selected = self.selector.fit_transform(X, y)
        selected_features = X.columns[self.selector.get_support()].tolist()
        
        # 获取特征评分
        feature_scores = dict(zip(X.columns, self.selector.scores_))
        selected_scores = {feat: feature_scores[feat] for feat in selected_features}
        
        selection_time = time.time() - start_time
        
        logger.info(f"单变量特征选择完成，选择了 {len(selected_features)} 个特征")
        
        return FeatureSelectionResult(
            selected_features=selected_features,
            feature_scores=selected_scores,
            selection_method=f"univariate_{self.method}",
            n_features_before=X.shape[1],
            n_features_after=len(selected_features),
            selection_time=selection_time
        )


class TreeBasedFeatureSelector(BaseFeatureSelector):
    """基于树模型的特征选择器"""
    
    def __init__(self, model_type: str = 'random_forest', n_estimators: int = 100,
                 importance_threshold: float = 0.001):
        self.model_type = model_type
        self.n_estimators = n_estimators
        self.importance_threshold = importance_threshold
        self.model = None
    
    def select_features(self, X: pd.DataFrame, y: pd.Series) -> FeatureSelectionResult:
        """基于树模型重要性的特征选择"""
        import time
        start_time = time.time()
        
        logger.info(f"开始树模型特征选择，模型: {self.model_type}")
        
        # 选择模型
        if self.model_type == 'random_forest':
            self.model = RandomForestRegressor(
                n_estimators=self.n_estimators,
                random_state=42,
                n_jobs=-1
            )
        elif self.model_type == 'extra_trees':
            self.model = ExtraTreesRegressor(
                n_estimators=self.n_estimators,
                random_state=42,
                n_jobs=-1
            )
        else:
            raise ValueError(f"不支持的模型类型: {self.model_type}")
        
        # 训练模型
        self.model.fit(X, y)
        
        # 获取特征重要性
        importances = self.model.feature_importances_
        feature_scores = dict(zip(X.columns, importances))
        
        # 选择重要性高于阈值的特征
        selected_features = [
            feat for feat, importance in feature_scores.items()
            if importance >= self.importance_threshold
        ]
        
        # 如果选择的特征太少，选择top-k个
        if len(selected_features) < 10:
            sorted_features = sorted(feature_scores.items(), key=lambda x: x[1], reverse=True)
            selected_features = [feat for feat, _ in sorted_features[:50]]
        
        selected_scores = {feat: feature_scores[feat] for feat in selected_features}
        
        # 计算交叉验证分数
        cv_score = np.mean(cross_val_score(self.model, X[selected_features], y, cv=5))
        
        selection_time = time.time() - start_time
        
        logger.info(f"树模型特征选择完成，选择了 {len(selected_features)} 个特征")
        
        return FeatureSelectionResult(
            selected_features=selected_features,
            feature_scores=selected_scores,
            selection_method=f"tree_{self.model_type}",
            n_features_before=X.shape[1],
            n_features_after=len(selected_features),
            selection_time=selection_time,
            cross_val_score=cv_score
        )


class RegularizationFeatureSelector(BaseFeatureSelector):
    """基于正则化的特征选择器"""
    
    def __init__(self, method: str = 'lasso', alpha: float = None):
        self.method = method
        self.alpha = alpha
        self.model = None
    
    def select_features(self, X: pd.DataFrame, y: pd.Series) -> FeatureSelectionResult:
        """基于正则化的特征选择"""
        import time
        start_time = time.time()
        
        logger.info(f"开始正则化特征选择，方法: {self.method}")
        
        # 标准化特征
        scaler = StandardScaler()
        X_scaled = pd.DataFrame(
            scaler.fit_transform(X),
            columns=X.columns,
            index=X.index
        )
        
        # 选择模型
        if self.method == 'lasso':
            if self.alpha:
                from sklearn.linear_model import Lasso
                self.model = Lasso(alpha=self.alpha)
            else:
                self.model = LassoCV(cv=5, random_state=42)
        elif self.method == 'elastic_net':
            if self.alpha:
                from sklearn.linear_model import ElasticNet
                self.model = ElasticNet(alpha=self.alpha)
            else:
                self.model = ElasticNetCV(cv=5, random_state=42)
        else:
            raise ValueError(f"不支持的方法: {self.method}")
        
        # 训练模型
        self.model.fit(X_scaled, y)
        
        # 获取特征系数
        coefficients = self.model.coef_
        feature_scores = dict(zip(X.columns, np.abs(coefficients)))
        
        # 选择系数非零的特征
        selected_features = [
            feat for feat, coef in zip(X.columns, coefficients)
            if abs(coef) > 1e-6
        ]
        
        selected_scores = {feat: feature_scores[feat] for feat in selected_features}
        
        # 计算交叉验证分数
        cv_score = np.mean(cross_val_score(self.model, X_scaled[selected_features], y, cv=5))
        
        selection_time = time.time() - start_time
        
        logger.info(f"正则化特征选择完成，选择了 {len(selected_features)} 个特征")
        
        return FeatureSelectionResult(
            selected_features=selected_features,
            feature_scores=selected_scores,
            selection_method=f"regularization_{self.method}",
            n_features_before=X.shape[1],
            n_features_after=len(selected_features),
            selection_time=selection_time,
            cross_val_score=cv_score
        )


class RecursiveFeatureSelector(BaseFeatureSelector):
    """递归特征消除选择器"""
    
    def __init__(self, estimator=None, n_features_to_select: int = 50, step: int = 1):
        self.estimator = estimator or RandomForestRegressor(n_estimators=50, random_state=42)
        self.n_features_to_select = n_features_to_select
        self.step = step
        self.selector = None
    
    def select_features(self, X: pd.DataFrame, y: pd.Series) -> FeatureSelectionResult:
        """递归特征消除"""
        import time
        start_time = time.time()
        
        logger.info(f"开始递归特征消除，目标特征数: {self.n_features_to_select}")
        
        # 创建RFE选择器
        self.selector = RFE(
            estimator=self.estimator,
            n_features_to_select=min(self.n_features_to_select, X.shape[1]),
            step=self.step
        )
        
        # 执行特征选择
        self.selector.fit(X, y)
        selected_features = X.columns[self.selector.support_].tolist()
        
        # 获取特征排名（转换为重要性分数）
        rankings = self.selector.ranking_
        max_rank = max(rankings)
        feature_scores = {
            feat: (max_rank - rank + 1) / max_rank
            for feat, rank in zip(X.columns, rankings)
        }
        
        selected_scores = {feat: feature_scores[feat] for feat in selected_features}
        
        # 计算交叉验证分数
        cv_score = np.mean(cross_val_score(self.estimator, X[selected_features], y, cv=5))
        
        selection_time = time.time() - start_time
        
        logger.info(f"递归特征消除完成，选择了 {len(selected_features)} 个特征")
        
        return FeatureSelectionResult(
            selected_features=selected_features,
            feature_scores=selected_scores,
            selection_method="recursive_elimination",
            n_features_before=X.shape[1],
            n_features_after=len(selected_features),
            selection_time=selection_time,
            cross_val_score=cv_score
        )


class HybridFeatureSelector(BaseFeatureSelector):
    """混合特征选择器"""
    
    def __init__(self, methods: List[str] = None, voting: str = 'union'):
        self.methods = methods or ['mutual_info', 'random_forest', 'lasso']
        self.voting = voting  # 'union', 'intersection', 'majority'
        self.selectors = {}
        self.results = {}
    
    def select_features(self, X: pd.DataFrame, y: pd.Series) -> FeatureSelectionResult:
        """混合特征选择"""
        import time
        start_time = time.time()
        
        logger.info(f"开始混合特征选择，方法: {self.methods}, 投票: {self.voting}")
        
        # 执行各种特征选择方法
        all_selected_features = []
        all_feature_scores = {}
        
        for method in self.methods:
            try:
                if method == 'mutual_info':
                    selector = UnivariateFeatureSelector(method='mutual_info', k=100)
                elif method == 'f_regression':
                    selector = UnivariateFeatureSelector(method='f_regression', k=100)
                elif method == 'random_forest':
                    selector = TreeBasedFeatureSelector(model_type='random_forest')
                elif method == 'extra_trees':
                    selector = TreeBasedFeatureSelector(model_type='extra_trees')
                elif method == 'lasso':
                    selector = RegularizationFeatureSelector(method='lasso')
                elif method == 'elastic_net':
                    selector = RegularizationFeatureSelector(method='elastic_net')
                elif method == 'rfe':
                    selector = RecursiveFeatureSelector()
                else:
                    logger.warning(f"未知的特征选择方法: {method}")
                    continue
                
                result = selector.select_features(X, y)
                self.selectors[method] = selector
                self.results[method] = result
                
                all_selected_features.append(set(result.selected_features))
                
                # 合并特征分数（标准化后）
                max_score = max(result.feature_scores.values()) if result.feature_scores else 1.0
                normalized_scores = {
                    feat: score / max_score
                    for feat, score in result.feature_scores.items()
                }
                
                for feat, score in normalized_scores.items():
                    if feat not in all_feature_scores:
                        all_feature_scores[feat] = []
                    all_feature_scores[feat].append(score)
                
            except Exception as e:
                logger.error(f"特征选择方法 {method} 执行失败: {e}")
                continue
        
        # 根据投票策略选择最终特征
        if self.voting == 'union':
            # 并集：任何方法选择的特征都保留
            final_features = set()
            for feature_set in all_selected_features:
                final_features.update(feature_set)
        elif self.voting == 'intersection':
            # 交集：所有方法都选择的特征
            final_features = all_selected_features[0]
            for feature_set in all_selected_features[1:]:
                final_features = final_features.intersection(feature_set)
        elif self.voting == 'majority':
            # 多数投票：超过一半方法选择的特征
            feature_votes = {}
            for feature_set in all_selected_features:
                for feature in feature_set:
                    feature_votes[feature] = feature_votes.get(feature, 0) + 1
            
            threshold = len(all_selected_features) / 2
            final_features = {
                feat for feat, votes in feature_votes.items()
                if votes > threshold
            }
        else:
            raise ValueError(f"不支持的投票策略: {self.voting}")
        
        final_features = list(final_features)
        
        # 计算最终特征分数（平均值）
        final_scores = {}
        for feat in final_features:
            if feat in all_feature_scores:
                final_scores[feat] = np.mean(all_feature_scores[feat])
            else:
                final_scores[feat] = 0.0
        
        selection_time = time.time() - start_time
        
        logger.info(f"混合特征选择完成，选择了 {len(final_features)} 个特征")
        
        return FeatureSelectionResult(
            selected_features=final_features,
            feature_scores=final_scores,
            selection_method=f"hybrid_{self.voting}",
            n_features_before=X.shape[1],
            n_features_after=len(final_features),
            selection_time=selection_time
        )


class DimensionalityReducer:
    """降维器"""
    
    def __init__(self, method: str = 'pca', n_components: Union[int, float] = 0.95):
        self.method = method
        self.n_components = n_components
        self.reducer = None
    
    def fit_transform(self, X: pd.DataFrame) -> DimensionalityReductionResult:
        """执行降维"""
        import time
        start_time = time.time()
        
        logger.info(f"开始降维，方法: {self.method}, 组件数: {self.n_components}")
        
        # 标准化数据
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        # 选择降维方法
        if self.method == 'pca':
            self.reducer = PCA(n_components=self.n_components, random_state=42)
        elif self.method == 'ica':
            n_comp = int(self.n_components) if isinstance(self.n_components, int) else min(50, X.shape[1])
            self.reducer = FastICA(n_components=n_comp, random_state=42)
        elif self.method == 'svd':
            n_comp = int(self.n_components) if isinstance(self.n_components, int) else min(50, X.shape[1])
            self.reducer = TruncatedSVD(n_components=n_comp, random_state=42)
        elif self.method == 'tsne':
            n_comp = min(2, X.shape[1])  # t-SNE通常用于2D/3D可视化
            self.reducer = TSNE(n_components=n_comp, random_state=42)
        else:
            raise ValueError(f"不支持的降维方法: {self.method}")
        
        # 执行降维
        X_reduced = self.reducer.fit_transform(X_scaled)
        
        # 创建结果DataFrame
        if self.method == 'pca':
            columns = [f'PC_{i+1}' for i in range(X_reduced.shape[1])]
        elif self.method == 'ica':
            columns = [f'IC_{i+1}' for i in range(X_reduced.shape[1])]
        elif self.method == 'svd':
            columns = [f'SVD_{i+1}' for i in range(X_reduced.shape[1])]
        elif self.method == 'tsne':
            columns = [f'TSNE_{i+1}' for i in range(X_reduced.shape[1])]
        else:
            columns = [f'Comp_{i+1}' for i in range(X_reduced.shape[1])]
        
        transformed_data = pd.DataFrame(
            X_reduced,
            columns=columns,
            index=X.index
        )
        
        # 获取解释方差比例（如果适用）
        explained_variance_ratio = None
        if hasattr(self.reducer, 'explained_variance_ratio_'):
            explained_variance_ratio = self.reducer.explained_variance_ratio_
        
        reduction_time = time.time() - start_time
        
        logger.info(f"降维完成，从 {X.shape[1]} 维降到 {X_reduced.shape[1]} 维")
        
        return DimensionalityReductionResult(
            transformed_data=transformed_data,
            explained_variance_ratio=explained_variance_ratio,
            n_components=X_reduced.shape[1],
            reduction_method=self.method,
            reduction_time=reduction_time
        )
    
    def transform(self, X: pd.DataFrame) -> pd.DataFrame:
        """使用已训练的降维器转换新数据"""
        if self.reducer is None:
            raise ValueError("降维器尚未训练，请先调用fit_transform")
        
        # 标准化数据
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        # 执行降维
        X_reduced = self.reducer.transform(X_scaled)
        
        # 创建结果DataFrame
        if self.method == 'pca':
            columns = [f'PC_{i+1}' for i in range(X_reduced.shape[1])]
        elif self.method == 'ica':
            columns = [f'IC_{i+1}' for i in range(X_reduced.shape[1])]
        elif self.method == 'svd':
            columns = [f'SVD_{i+1}' for i in range(X_reduced.shape[1])]
        else:
            columns = [f'Comp_{i+1}' for i in range(X_reduced.shape[1])]
        
        return pd.DataFrame(
            X_reduced,
            columns=columns,
            index=X.index
        )


class AdvancedFeatureSelector:
    """高级特征选择器 - 集成多种方法"""
    
    def __init__(self):
        self.selection_results = {}
        self.best_method = None
        self.best_score = -np.inf
    
    def auto_select_features(self, X: pd.DataFrame, y: pd.Series,
                           methods: List[str] = None,
                           cv_folds: int = 5) -> FeatureSelectionResult:
        """自动选择最佳特征选择方法"""
        if methods is None:
            methods = ['mutual_info', 'random_forest', 'lasso', 'rfe']
        
        logger.info(f"开始自动特征选择，测试方法: {methods}")
        
        best_result = None
        
        for method in methods:
            try:
                # 创建选择器
                if method == 'mutual_info':
                    selector = UnivariateFeatureSelector(method='mutual_info', k=100)
                elif method == 'random_forest':
                    selector = TreeBasedFeatureSelector(model_type='random_forest')
                elif method == 'lasso':
                    selector = RegularizationFeatureSelector(method='lasso')
                elif method == 'rfe':
                    selector = RecursiveFeatureSelector()
                elif method == 'hybrid':
                    selector = HybridFeatureSelector()
                else:
                    continue
                
                # 执行特征选择
                result = selector.select_features(X, y)
                self.selection_results[method] = result
                
                # 评估性能（使用交叉验证）
                if result.cross_val_score is not None:
                    score = result.cross_val_score
                else:
                    # 使用随机森林评估特征质量
                    rf = RandomForestRegressor(n_estimators=50, random_state=42)
                    scores = cross_val_score(rf, X[result.selected_features], y, cv=cv_folds)
                    score = np.mean(scores)
                
                logger.info(f"方法 {method}: 选择 {result.n_features_after} 个特征, CV分数: {score:.4f}")
                
                # 更新最佳方法
                if score > self.best_score:
                    self.best_score = score
                    self.best_method = method
                    best_result = result
                
            except Exception as e:
                logger.error(f"特征选择方法 {method} 失败: {e}")
                continue
        
        if best_result is None:
            raise ValueError("所有特征选择方法都失败了")
        
        logger.info(f"最佳特征选择方法: {self.best_method}, CV分数: {self.best_score:.4f}")
        return best_result
    
    def get_selection_comparison(self) -> pd.DataFrame:
        """获取特征选择方法比较"""
        comparison_data = []
        
        for method, result in self.selection_results.items():
            comparison_data.append({
                'method': method,
                'n_features': result.n_features_after,
                'selection_time': result.selection_time,
                'cv_score': result.cross_val_score or 0.0
            })
        
        return pd.DataFrame(comparison_data)