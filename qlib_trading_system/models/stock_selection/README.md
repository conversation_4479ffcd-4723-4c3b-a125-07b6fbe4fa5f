# 股票筛选AI模型 - 特征工程管道

## 概述

本模块实现了完整的特征工程管道，为爆发股识别模型提供高质量的特征数据。系统采用六维度特征分析框架，集成了特征提取、预处理、选择、重要性分析和监控等核心功能。

## 核心组件

### 1. 特征工程管道 (FeaturePipeline)

**文件**: `feature_pipeline.py`

**主要功能**:
- 六维度特征提取（基本面、估值、技术面、情绪、风险、大盘）
- 数据预处理（缺失值处理、异常值处理、标准化）
- 特征选择和降维
- 性能指标计算
- 缓存管理

**核心类**:
- `FeaturePipeline`: 主管道类
- `FeaturePipelineConfig`: 配置管理
- `PipelineMetrics`: 性能指标

### 2. 特征选择器 (FeatureSelector)

**文件**: `feature_selector.py`

**主要功能**:
- 多种特征选择方法（单变量、树模型、正则化、递归消除）
- 混合特征选择策略
- 降维算法（PCA、ICA、SVD、t-SNE）
- 自动方法选择和比较

**核心类**:
- `UnivariateFeatureSelector`: 单变量特征选择
- `TreeBasedFeatureSelector`: 基于树模型的选择
- `RegularizationFeatureSelector`: 正则化特征选择
- `HybridFeatureSelector`: 混合选择策略
- `DimensionalityReducer`: 降维器
- `AdvancedFeatureSelector`: 高级特征选择器

### 3. 特征重要性分析器 (FeatureImportanceAnalyzer)

**文件**: `feature_importance_analyzer.py`

**主要功能**:
- 多种重要性分析方法（树模型、排列重要性、SHAP、相关性）
- 综合重要性分析
- 类别汇总和趋势分析
- 可视化和报告生成

**核心类**:
- `FeatureImportanceAnalyzer`: 主分析器
- `ImportanceResult`: 重要性结果
- `ImportanceAnalysisReport`: 分析报告

### 4. 特征监控系统 (FeatureMonitor)

**文件**: `feature_monitor.py`

**主要功能**:
- 特征质量监控
- 特征漂移检测
- 特征更新管理
- 监控报告和警告

**核心类**:
- `FeatureMonitoringSystem`: 监控系统主类
- `FeatureQualityMonitor`: 质量监控器
- `FeatureDriftDetector`: 漂移检测器
- `FeatureUpdateManager`: 更新管理器

## 特征体系

### 六维度特征框架

1. **基本面分析特征** (60个)
   - 业绩爆发指标: 业绩预增幅度、ROE加速度、净利润环比增速
   - 估值修复机会: PEG比率、相对估值折价、历史估值分位数
   - 成长性突破: 营收增长拐点、毛利率改善、现金流转正
   - 财务质量跃升: 资产负债率下降、应收账款周转改善

2. **估值分析特征** (40个)
   - 动态估值模型: DCF估值上修、PB-ROE模型、EV/EBITDA合理性
   - 相对估值优势: 行业估值分位数、同业对比折价率
   - 估值催化剂: 分拆上市预期、资产注入可能性、重估触发因子

3. **技术分析特征** (80个)
   - 爆发力技术指标: 价格加速度、成交量爆发系数、动量突破强度
   - 主力行为识别: 龙虎榜机构买入强度、北向资金持仓变化率、大宗交易溢价率
   - 突破形态确认: 多重阻力位突破、放量突破验证、技术形态完成度
   - 量价关系分析: 价量背离修复、异常成交量识别、资金流向集中度

4. **情绪分析特征** (50个)
   - 市场情绪指标: 恐慌指数VIX、投资者情绪指数、市场热度分布
   - 舆论发酵程度: 新闻热度爆发、社交媒体提及频率、搜索指数激增
   - 资金情绪表现: 换手率异常、成交额爆发、游资活跃度
   - 板块轮动信号: 概念股轮动强度、热点持续性、龙头效应强度

5. **风险管理评估** (30个)
   - 流动性风险: 日均成交额、买卖价差、冲击成本
   - 基本面风险: 财务造假概率、ST风险评分、退市风险指标
   - 技术面风险: 超买超卖程度、支撑阻力有效性、趋势稳定性
   - 系统性风险: Beta系数、相关性分析、行业集中度风险

6. **大盘走势预测** (40个)
   - 宏观经济指标: GDP增速、CPI走势、货币政策方向
   - 市场技术指标: 上证指数技术面、成交量变化、市场宽度指标
   - 资金流向分析: 北向资金净流入、融资融券余额、新发基金规模
   - 政策影响因子: 政策预期指数、监管态度、改革红利释放

## 使用示例

### 基本使用

```python
from qlib_trading_system.models.stock_selection import FeaturePipeline, FeaturePipelineConfig

# 创建配置
config = FeaturePipelineConfig(
    feature_selection_method='mutual_info',
    max_features=200,
    scaler_type='robust',
    enable_monitoring=True
)

# 创建管道
pipeline = FeaturePipeline(config)

# 训练和转换
features_df, pipeline_info = pipeline.fit_transform(stock_data_dict, target_data)

# 使用训练好的管道转换新数据
new_features = pipeline.transform(new_stock_data_dict)
```

### 特征选择

```python
from qlib_trading_system.models.stock_selection import AdvancedFeatureSelector

# 自动特征选择
selector = AdvancedFeatureSelector()
result = selector.auto_select_features(X, y)

print(f"最佳方法: {selector.best_method}")
print(f"选择特征数: {result.n_features_after}")
```

### 重要性分析

```python
from qlib_trading_system.models.stock_selection import FeatureImportanceAnalyzer

# 综合重要性分析
analyzer = FeatureImportanceAnalyzer()
report = analyzer.comprehensive_importance_analysis(X, y)

# 显示top特征
for result in report.top_features[:10]:
    print(f"{result.feature_name}: {result.importance_score:.4f}")
```

### 特征监控

```python
from qlib_trading_system.models.stock_selection import FeatureMonitoringSystem

# 创建监控系统
monitoring_system = FeatureMonitoringSystem()

# 完整监控周期
results = monitoring_system.full_monitoring_cycle(X, y)

# 生成监控报告
report = monitoring_system.generate_monitoring_report(results)
print(report)
```

## 配置选项

### FeaturePipelineConfig

- `feature_selection_method`: 特征选择方法 ('mutual_info', 'f_regression', 'random_forest')
- `max_features`: 最大特征数量
- `scaler_type`: 标准化方法 ('standard', 'robust', 'minmax')
- `handle_missing`: 缺失值处理 ('drop', 'fill', 'interpolate')
- `enable_pca`: 是否启用PCA降维
- `enable_monitoring`: 是否启用特征监控
- `enable_cache`: 是否启用缓存

## 性能指标

### PipelineMetrics

- `processing_time`: 处理时间
- `feature_count_before/after`: 处理前后特征数量
- `missing_value_ratio`: 缺失值比例
- `outlier_ratio`: 异常值比例
- `feature_correlation_max`: 最大特征相关性
- `data_quality_score`: 数据质量分数

## 监控功能

### 质量监控
- 缺失值比例监控
- 异常值检测
- 特征方差分析
- 与目标变量相关性监控

### 漂移检测
- 均值漂移检测
- 方差漂移检测
- 分布漂移检测
- 统计显著性测试

### 更新管理
- 特征添加/删除记录
- 更新影响评估
- 历史变更追踪
- 回滚机制支持

## 扩展性

系统设计具有良好的扩展性：

1. **新特征类型**: 可以轻松添加新的特征分析器
2. **新选择方法**: 支持插件式的特征选择算法
3. **新监控指标**: 可以扩展质量和漂移检测指标
4. **新数据源**: 支持多种数据源适配

## 测试

运行测试：

```bash
python qlib_trading_system/models/stock_selection/standalone_test.py
```

测试覆盖：
- 配置和数据结构验证
- 特征重要性分析
- 特征监控机制
- 特征选择算法
- 管道工作流程
- 集成点验证

## 依赖

核心依赖：
- pandas: 数据处理
- numpy: 数值计算
- scikit-learn: 机器学习算法（可选）
- matplotlib/seaborn: 可视化（可选）

## 注意事项

1. **内存使用**: 大量特征可能消耗较多内存，建议启用缓存
2. **计算时间**: 综合分析可能较耗时，可以调整分析方法
3. **数据质量**: 输入数据质量直接影响特征质量
4. **参数调优**: 建议根据具体数据调整配置参数

## 后续开发

- [ ] 增加更多特征选择算法
- [ ] 优化大规模数据处理性能
- [ ] 增加实时特征更新机制
- [ ] 完善可视化功能
- [ ] 增加A/B测试框架