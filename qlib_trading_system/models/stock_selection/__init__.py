"""
股票筛选AI模型模块

包含特征工程管道、特征选择、重要性分析和监控等组件
为爆发股识别提供完整的特征处理解决方案
"""

from .feature_pipeline import FeaturePipeline, FeaturePipelineConfig, PipelineMetrics
from .feature_selector import (
    UnivariateFeatureSelector, TreeBasedFeatureSelector, 
    RegularizationFeatureSelector, RecursiveFeatureSelector,
    HybridFeatureSelector, DimensionalityReducer, AdvancedFeatureSelector
)
from .feature_importance_analyzer import (
    FeatureImportanceAnalyzer, ImportanceResult, ImportanceAnalysisReport
)
from .feature_monitor import (
    FeatureMonitoringSystem, FeatureQualityMonitor, 
    FeatureDriftDetector, FeatureUpdateManager
)

__all__ = [
    # 特征管道
    'FeaturePipeline',
    'FeaturePipelineConfig', 
    'PipelineMetrics',
    
    # 特征选择
    'UnivariateFeatureSelector',
    'TreeBasedFeatureSelector',
    'RegularizationFeatureSelector', 
    'RecursiveFeatureSelector',
    'HybridFeatureSelector',
    'DimensionalityReducer',
    'AdvancedFeatureSelector',
    
    # 重要性分析
    'FeatureImportanceAnalyzer',
    'ImportanceResult',
    'ImportanceAnalysisReport',
    
    # 特征监控
    'FeatureMonitoringSystem',
    'FeatureQualityMonitor',
    'FeatureDriftDetector', 
    'FeatureUpdateManager'
]