"""
特征工程管道 - 股票筛选AI模型核心组件

实现特征提取、预处理、选择和降维的完整管道
为爆发股识别模型提供高质量的特征数据
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union, Any
from datetime import datetime, timedelta
import logging
from dataclasses import dataclass, asdict
from sklearn.preprocessing import StandardScaler, RobustScaler, MinMaxScaler
from sklearn.feature_selection import SelectKBest, f_regression, mutual_info_regression
from sklearn.decomposition import PCA
from sklearn.ensemble import RandomForestRegressor
import joblib
import os
import warnings
warnings.filterwarnings('ignore')

# 导入六维度特征分析器
from ..features.feature_engine import FeatureEngine, ComprehensiveFeatures

logger = logging.getLogger(__name__)


@dataclass
class FeaturePipelineConfig:
    """特征管道配置"""
    # 特征选择配置
    feature_selection_method: str = 'mutual_info'  # 'mutual_info', 'f_regression', 'random_forest'
    max_features: int = 200  # 最大特征数量
    feature_importance_threshold: float = 0.001  # 特征重要性阈值
    
    # 数据预处理配置
    scaler_type: str = 'robust'  # 'standard', 'robust', 'minmax'
    handle_missing: str = 'interpolate'  # 'drop', 'fill', 'interpolate'
    outlier_method: str = 'iqr'  # 'iqr', 'zscore', 'isolation'
    
    # 降维配置
    enable_pca: bool = False
    pca_components: float = 0.95  # 保留95%方差
    
    # 特征监控配置
    enable_monitoring: bool = True
    drift_threshold: float = 0.1  # 特征漂移阈值
    update_frequency: str = 'daily'  # 'daily', 'weekly', 'monthly'
    
    # 缓存配置
    enable_cache: bool = True
    cache_dir: str = 'cache/features'


@dataclass
class FeatureImportance:
    """特征重要性结果"""
    feature_name: str
    importance_score: float
    rank: int
    category: str  # 'fundamental', 'technical', 'sentiment', etc.
    description: str


@dataclass
class PipelineMetrics:
    """管道性能指标"""
    processing_time: float
    feature_count_before: int
    feature_count_after: int
    missing_value_ratio: float
    outlier_ratio: float
    feature_correlation_max: float
    data_quality_score: float


class FeaturePipeline:
    """特征工程管道主类"""
    
    def __init__(self, config: FeaturePipelineConfig = None):
        self.config = config or FeaturePipelineConfig()
        self.feature_engine = FeatureEngine()
        
        # 初始化组件
        self.scaler = None
        self.feature_selector = None
        self.pca_transformer = None
        self.feature_importance_analyzer = None
        
        # 特征监控
        self.feature_stats_history = []
        self.drift_detector = FeatureDriftDetector()
        
        # 缓存管理
        self.cache_manager = FeatureCacheManager(self.config.cache_dir)
        
        # 创建缓存目录
        os.makedirs(self.config.cache_dir, exist_ok=True)
        
        logger.info("特征工程管道初始化完成")
    
    def fit_transform(self, 
                     stock_data_dict: Dict[str, Dict],
                     target_data: Dict[str, float] = None) -> Tuple[pd.DataFrame, Dict]:
        """训练并转换特征数据"""
        start_time = datetime.now()
        
        try:
            logger.info(f"开始特征工程管道处理，股票数量: {len(stock_data_dict)}")
            
            # 1. 特征提取
            raw_features = self._extract_raw_features(stock_data_dict)
            
            # 2. 数据预处理
            processed_features = self._preprocess_features(raw_features)
            
            # 3. 特征选择
            if target_data:
                selected_features = self._select_features(processed_features, target_data)
            else:
                selected_features = processed_features
            
            # 4. 降维处理
            if self.config.enable_pca:
                final_features = self._apply_pca(selected_features)
            else:
                final_features = selected_features
            
            # 5. 特征重要性分析
            if target_data:
                importance_results = self._analyze_feature_importance(final_features, target_data)
            else:
                importance_results = {}
            
            # 6. 计算管道指标
            processing_time = (datetime.now() - start_time).total_seconds()
            metrics = self._calculate_pipeline_metrics(
                raw_features, final_features, processing_time
            )
            
            # 7. 特征监控
            if self.config.enable_monitoring:
                self._update_feature_monitoring(final_features)
            
            # 8. 缓存结果
            if self.config.enable_cache:
                self._cache_pipeline_results(final_features, importance_results, metrics)
            
            logger.info(f"特征工程管道处理完成，最终特征数量: {len(final_features.columns)}")
            
            return final_features, {
                'importance': importance_results,
                'metrics': metrics,
                'feature_names': list(final_features.columns)
            }
            
        except Exception as e:
            logger.error(f"特征工程管道处理失败: {e}")
            raise
    
    def transform(self, stock_data_dict: Dict[str, Dict]) -> pd.DataFrame:
        """使用已训练的管道转换新数据"""
        try:
            logger.info(f"使用已训练管道转换数据，股票数量: {len(stock_data_dict)}")
            
            # 1. 特征提取
            raw_features = self._extract_raw_features(stock_data_dict)
            
            # 2. 应用已训练的预处理器
            if self.scaler:
                processed_features = pd.DataFrame(
                    self.scaler.transform(raw_features),
                    columns=raw_features.columns,
                    index=raw_features.index
                )
            else:
                processed_features = raw_features
            
            # 3. 应用特征选择
            if self.feature_selector:
                selected_features = pd.DataFrame(
                    self.feature_selector.transform(processed_features),
                    columns=self.feature_selector.get_feature_names_out(),
                    index=processed_features.index
                )
            else:
                selected_features = processed_features
            
            # 4. 应用PCA
            if self.pca_transformer:
                final_features = pd.DataFrame(
                    self.pca_transformer.transform(selected_features),
                    columns=[f'PC_{i+1}' for i in range(self.pca_transformer.n_components_)],
                    index=selected_features.index
                )
            else:
                final_features = selected_features
            
            logger.info(f"数据转换完成，特征数量: {len(final_features.columns)}")
            return final_features
            
        except Exception as e:
            logger.error(f"数据转换失败: {e}")
            raise
    
    def _extract_raw_features(self, stock_data_dict: Dict[str, Dict]) -> pd.DataFrame:
        """提取原始特征"""
        logger.info("开始提取原始特征")
        
        # 使用六维度特征引擎批量提取特征
        comprehensive_features = self.feature_engine.batch_feature_extraction(stock_data_dict)
        
        # 将特征转换为DataFrame格式
        feature_rows = []
        for symbol, features in comprehensive_features.items():
            feature_row = self._flatten_comprehensive_features(symbol, features)
            feature_rows.append(feature_row)
        
        raw_features_df = pd.DataFrame(feature_rows)
        raw_features_df.set_index('symbol', inplace=True)
        
        logger.info(f"原始特征提取完成，特征数量: {len(raw_features_df.columns)}")
        return raw_features_df
    
    def _flatten_comprehensive_features(self, symbol: str, features: ComprehensiveFeatures) -> Dict:
        """将综合特征展平为字典格式"""
        flattened = {'symbol': symbol}
        
        # 基础评分
        flattened.update({
            'fundamental_score': features.fundamental_score,
            'valuation_score': features.valuation_score,
            'technical_score': features.technical_score,
            'sentiment_score': features.sentiment_score,
            'risk_score': features.risk_score,
            'market_score': features.market_score,
            'overall_score': features.overall_score,
            'explosive_potential': features.explosive_potential,
            'confidence_level': features.confidence_level
        })
        
        # 展平各维度详细特征
        for category, feature_dict in [
            ('fundamental', features.fundamental_features),
            ('valuation', features.valuation_features),
            ('technical', features.technical_features),
            ('sentiment', features.sentiment_features),
            ('risk', features.risk_features),
            ('market', features.market_features)
        ]:
            if isinstance(feature_dict, dict):
                for key, value in feature_dict.items():
                    if isinstance(value, (int, float, np.number)):
                        flattened[f'{category}_{key}'] = float(value)
                    elif isinstance(value, dict):
                        # 进一步展平嵌套字典
                        for sub_key, sub_value in value.items():
                            if isinstance(sub_value, (int, float, np.number)):
                                flattened[f'{category}_{key}_{sub_key}'] = float(sub_value)
        
        return flattened
    
    def _preprocess_features(self, features_df: pd.DataFrame) -> pd.DataFrame:
        """数据预处理"""
        logger.info("开始数据预处理")
        
        processed_df = features_df.copy()
        
        # 1. 处理缺失值
        processed_df = self._handle_missing_values(processed_df)
        
        # 2. 处理异常值
        processed_df = self._handle_outliers(processed_df)
        
        # 3. 数据标准化
        processed_df = self._scale_features(processed_df)
        
        logger.info("数据预处理完成")
        return processed_df
    
    def _handle_missing_values(self, df: pd.DataFrame) -> pd.DataFrame:
        """处理缺失值"""
        if self.config.handle_missing == 'drop':
            # 删除包含缺失值的行
            return df.dropna()
        elif self.config.handle_missing == 'fill':
            # 用中位数填充数值型特征
            numeric_columns = df.select_dtypes(include=[np.number]).columns
            df[numeric_columns] = df[numeric_columns].fillna(df[numeric_columns].median())
            return df
        elif self.config.handle_missing == 'interpolate':
            # 线性插值
            numeric_columns = df.select_dtypes(include=[np.number]).columns
            df[numeric_columns] = df[numeric_columns].interpolate(method='linear')
            # 对于仍然存在的缺失值，用中位数填充
            df[numeric_columns] = df[numeric_columns].fillna(df[numeric_columns].median())
            return df
        else:
            return df
    
    def _handle_outliers(self, df: pd.DataFrame) -> pd.DataFrame:
        """处理异常值"""
        if self.config.outlier_method == 'iqr':
            # 使用IQR方法处理异常值
            numeric_columns = df.select_dtypes(include=[np.number]).columns
            for col in numeric_columns:
                Q1 = df[col].quantile(0.25)
                Q3 = df[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                df[col] = df[col].clip(lower_bound, upper_bound)
        elif self.config.outlier_method == 'zscore':
            # 使用Z-score方法
            numeric_columns = df.select_dtypes(include=[np.number]).columns
            for col in numeric_columns:
                z_scores = np.abs((df[col] - df[col].mean()) / df[col].std())
                df[col] = df[col].where(z_scores < 3, df[col].median())
        
        return df
    
    def _scale_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """特征标准化"""
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        
        if self.config.scaler_type == 'standard':
            self.scaler = StandardScaler()
        elif self.config.scaler_type == 'robust':
            self.scaler = RobustScaler()
        elif self.config.scaler_type == 'minmax':
            self.scaler = MinMaxScaler()
        else:
            return df
        
        # 训练并转换数据
        scaled_data = self.scaler.fit_transform(df[numeric_columns])
        
        # 创建新的DataFrame
        scaled_df = df.copy()
        scaled_df[numeric_columns] = scaled_data
        
        return scaled_df
    
    def _select_features(self, features_df: pd.DataFrame, target_data: Dict[str, float]) -> pd.DataFrame:
        """特征选择"""
        logger.info("开始特征选择")
        
        # 准备目标变量
        target_series = pd.Series(target_data, name='target')
        aligned_features = features_df.loc[target_series.index]
        
        numeric_columns = aligned_features.select_dtypes(include=[np.number]).columns
        X = aligned_features[numeric_columns]
        y = target_series
        
        if self.config.feature_selection_method == 'mutual_info':
            self.feature_selector = SelectKBest(
                score_func=mutual_info_regression,
                k=min(self.config.max_features, len(numeric_columns))
            )
        elif self.config.feature_selection_method == 'f_regression':
            self.feature_selector = SelectKBest(
                score_func=f_regression,
                k=min(self.config.max_features, len(numeric_columns))
            )
        elif self.config.feature_selection_method == 'random_forest':
            # 使用随机森林进行特征选择
            rf = RandomForestRegressor(n_estimators=100, random_state=42)
            rf.fit(X, y)
            
            # 获取特征重要性
            importances = rf.feature_importances_
            indices = np.argsort(importances)[::-1]
            
            # 选择重要性最高的特征
            selected_indices = indices[:min(self.config.max_features, len(indices))]
            selected_features = X.iloc[:, selected_indices]
            
            logger.info(f"特征选择完成，选择了 {len(selected_features.columns)} 个特征")
            return selected_features
        
        # 应用特征选择
        selected_data = self.feature_selector.fit_transform(X, y)
        selected_feature_names = X.columns[self.feature_selector.get_support()]
        
        selected_features = pd.DataFrame(
            selected_data,
            columns=selected_feature_names,
            index=X.index
        )
        
        logger.info(f"特征选择完成，选择了 {len(selected_features.columns)} 个特征")
        return selected_features
    
    def _apply_pca(self, features_df: pd.DataFrame) -> pd.DataFrame:
        """应用PCA降维"""
        logger.info("开始PCA降维")
        
        self.pca_transformer = PCA(n_components=self.config.pca_components)
        pca_data = self.pca_transformer.fit_transform(features_df)
        
        pca_df = pd.DataFrame(
            pca_data,
            columns=[f'PC_{i+1}' for i in range(pca_data.shape[1])],
            index=features_df.index
        )
        
        explained_variance_ratio = self.pca_transformer.explained_variance_ratio_.sum()
        logger.info(f"PCA降维完成，保留方差比例: {explained_variance_ratio:.3f}")
        
        return pca_df
    
    def _analyze_feature_importance(self, features_df: pd.DataFrame, 
                                  target_data: Dict[str, float]) -> List[FeatureImportance]:
        """分析特征重要性"""
        logger.info("开始特征重要性分析")
        
        # 准备数据
        target_series = pd.Series(target_data, name='target')
        aligned_features = features_df.loc[target_series.index]
        
        # 使用随机森林分析特征重要性
        rf = RandomForestRegressor(n_estimators=200, random_state=42)
        rf.fit(aligned_features, target_series)
        
        # 获取特征重要性
        importances = rf.feature_importances_
        feature_names = aligned_features.columns
        
        # 创建特征重要性对象列表
        importance_results = []
        for i, (name, importance) in enumerate(zip(feature_names, importances)):
            category = self._get_feature_category(name)
            importance_results.append(FeatureImportance(
                feature_name=name,
                importance_score=importance,
                rank=i + 1,
                category=category,
                description=self._get_feature_description(name)
            ))
        
        # 按重要性排序
        importance_results.sort(key=lambda x: x.importance_score, reverse=True)
        
        # 更新排名
        for i, result in enumerate(importance_results):
            result.rank = i + 1
        
        logger.info(f"特征重要性分析完成，分析了 {len(importance_results)} 个特征")
        return importance_results
    
    def _get_feature_category(self, feature_name: str) -> str:
        """获取特征类别"""
        if 'fundamental' in feature_name:
            return 'fundamental'
        elif 'valuation' in feature_name:
            return 'valuation'
        elif 'technical' in feature_name:
            return 'technical'
        elif 'sentiment' in feature_name:
            return 'sentiment'
        elif 'risk' in feature_name:
            return 'risk'
        elif 'market' in feature_name:
            return 'market'
        else:
            return 'other'
    
    def _get_feature_description(self, feature_name: str) -> str:
        """获取特征描述"""
        # 这里可以建立一个特征名称到描述的映射
        descriptions = {
            'fundamental_score': '基本面综合评分',
            'valuation_score': '估值综合评分',
            'technical_score': '技术面综合评分',
            'sentiment_score': '情绪综合评分',
            'risk_score': '风险综合评分',
            'market_score': '大盘综合评分',
            'overall_score': '整体综合评分',
            'explosive_potential': '爆发潜力评分'
        }
        return descriptions.get(feature_name, feature_name)
    
    def _calculate_pipeline_metrics(self, raw_features: pd.DataFrame,
                                  final_features: pd.DataFrame,
                                  processing_time: float) -> PipelineMetrics:
        """计算管道性能指标"""
        # 计算缺失值比例
        missing_ratio = raw_features.isnull().sum().sum() / (raw_features.shape[0] * raw_features.shape[1])
        
        # 计算异常值比例（简化计算）
        outlier_ratio = 0.05  # 假设值，实际应该根据异常值检测结果计算
        
        # 计算特征相关性最大值
        correlation_matrix = final_features.corr().abs()
        np.fill_diagonal(correlation_matrix.values, 0)
        max_correlation = correlation_matrix.max().max()
        
        # 计算数据质量评分
        data_quality_score = (1 - missing_ratio) * (1 - outlier_ratio) * (1 - max_correlation * 0.5)
        
        return PipelineMetrics(
            processing_time=processing_time,
            feature_count_before=len(raw_features.columns),
            feature_count_after=len(final_features.columns),
            missing_value_ratio=missing_ratio,
            outlier_ratio=outlier_ratio,
            feature_correlation_max=max_correlation,
            data_quality_score=data_quality_score
        )
    
    def _update_feature_monitoring(self, features_df: pd.DataFrame):
        """更新特征监控"""
        current_stats = {
            'timestamp': datetime.now(),
            'feature_count': len(features_df.columns),
            'sample_count': len(features_df),
            'feature_means': features_df.mean().to_dict(),
            'feature_stds': features_df.std().to_dict()
        }
        
        self.feature_stats_history.append(current_stats)
        
        # 检测特征漂移
        if len(self.feature_stats_history) > 1:
            drift_results = self.drift_detector.detect_drift(
                self.feature_stats_history[-2],
                current_stats,
                self.config.drift_threshold
            )
            
            if drift_results['has_drift']:
                logger.warning(f"检测到特征漂移: {drift_results['drifted_features']}")
    
    def _cache_pipeline_results(self, features_df: pd.DataFrame,
                              importance_results: List[FeatureImportance],
                              metrics: PipelineMetrics):
        """缓存管道结果"""
        if self.config.enable_cache:
            cache_data = {
                'features': features_df,
                'importance': importance_results,
                'metrics': metrics,
                'timestamp': datetime.now()
            }
            self.cache_manager.save_cache('pipeline_results', cache_data)
    
    def save_pipeline(self, filepath: str):
        """保存训练好的管道"""
        pipeline_data = {
            'config': asdict(self.config),
            'scaler': self.scaler,
            'feature_selector': self.feature_selector,
            'pca_transformer': self.pca_transformer,
            'feature_stats_history': self.feature_stats_history
        }
        
        joblib.dump(pipeline_data, filepath)
        logger.info(f"管道已保存到: {filepath}")
    
    def load_pipeline(self, filepath: str):
        """加载训练好的管道"""
        pipeline_data = joblib.load(filepath)
        
        self.config = FeaturePipelineConfig(**pipeline_data['config'])
        self.scaler = pipeline_data['scaler']
        self.feature_selector = pipeline_data['feature_selector']
        self.pca_transformer = pipeline_data['pca_transformer']
        self.feature_stats_history = pipeline_data.get('feature_stats_history', [])
        
        logger.info(f"管道已从 {filepath} 加载")
    
    def get_feature_importance_report(self, importance_results: List[FeatureImportance],
                                    top_n: int = 20) -> Dict:
        """生成特征重要性报告"""
        if not importance_results:
            return {}
        
        # 按类别分组
        category_importance = {}
        for result in importance_results:
            if result.category not in category_importance:
                category_importance[result.category] = []
            category_importance[result.category].append(result)
        
        # 生成报告
        report = {
            'top_features': [
                {
                    'name': result.feature_name,
                    'importance': result.importance_score,
                    'rank': result.rank,
                    'category': result.category,
                    'description': result.description
                }
                for result in importance_results[:top_n]
            ],
            'category_summary': {}
        }
        
        # 类别汇总
        for category, results in category_importance.items():
            total_importance = sum(r.importance_score for r in results)
            avg_importance = total_importance / len(results)
            report['category_summary'][category] = {
                'feature_count': len(results),
                'total_importance': total_importance,
                'average_importance': avg_importance,
                'top_feature': max(results, key=lambda x: x.importance_score).feature_name
            }
        
        return report


class FeatureDriftDetector:
    """特征漂移检测器"""
    
    def detect_drift(self, old_stats: Dict, new_stats: Dict, threshold: float) -> Dict:
        """检测特征漂移"""
        drifted_features = []
        
        old_means = old_stats.get('feature_means', {})
        new_means = new_stats.get('feature_means', {})
        
        for feature_name in old_means.keys():
            if feature_name in new_means:
                old_mean = old_means[feature_name]
                new_mean = new_means[feature_name]
                
                # 计算相对变化
                if old_mean != 0:
                    relative_change = abs(new_mean - old_mean) / abs(old_mean)
                    if relative_change > threshold:
                        drifted_features.append({
                            'feature': feature_name,
                            'old_mean': old_mean,
                            'new_mean': new_mean,
                            'relative_change': relative_change
                        })
        
        return {
            'has_drift': len(drifted_features) > 0,
            'drifted_features': drifted_features,
            'drift_count': len(drifted_features)
        }


class FeatureCacheManager:
    """特征缓存管理器"""
    
    def __init__(self, cache_dir: str):
        self.cache_dir = cache_dir
        os.makedirs(cache_dir, exist_ok=True)
    
    def save_cache(self, key: str, data: Any):
        """保存缓存"""
        cache_file = os.path.join(self.cache_dir, f"{key}.pkl")
        joblib.dump(data, cache_file)
    
    def load_cache(self, key: str) -> Any:
        """加载缓存"""
        cache_file = os.path.join(self.cache_dir, f"{key}.pkl")
        if os.path.exists(cache_file):
            return joblib.load(cache_file)
        return None
    
    def clear_cache(self):
        """清空缓存"""
        import shutil
        if os.path.exists(self.cache_dir):
            shutil.rmtree(self.cache_dir)
        os.makedirs(self.cache_dir, exist_ok=True)