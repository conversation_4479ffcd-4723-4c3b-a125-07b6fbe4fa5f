"""
多模型信号融合和权重分配系统
整合Transformer、CNN和技术分析的信号，生成最终交易决策
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, field
from enum import Enum
import logging
from collections import deque
import json
import pickle
from datetime import datetime, timedelta

# 条件导入AI模型，避免PyTorch依赖问题
try:
    from .transformer_model import TransformerPredictor, PredictionResult
    TRANSFORMER_AVAILABLE = True
except ImportError:
    TRANSFORMER_AVAILABLE = False
    TransformerPredictor = None
    PredictionResult = None

try:
    from .cnn_model import CNNPatternRecognizer, PatternResult
    CNN_AVAILABLE = True
except ImportError:
    CNN_AVAILABLE = False
    CNNPatternRecognizer = None
    PatternResult = None

# 技术分析器不依赖PyTorch
from .technical_analyzer import TechnicalAnalyzer, TechnicalSignal, SignalType

logger = logging.getLogger(__name__)


class FusionSignalType(Enum):
    """融合信号类型"""
    STRONG_BUY = 2
    BUY = 1
    HOLD = 0
    SELL = -1
    STRONG_SELL = -2


@dataclass
class ModelPerformance:
    """模型性能跟踪"""
    accuracy: float = 0.5  # 准确率
    precision: float = 0.5  # 精确率
    recall: float = 0.5  # 召回率
    sharpe_ratio: float = 0.0  # 夏普比率
    max_drawdown: float = 0.0  # 最大回撤
    recent_predictions: deque = field(default_factory=lambda: deque(maxlen=100))
    last_update: datetime = field(default_factory=datetime.now)


@dataclass
class FusedSignal:
    """融合后的交易信号"""
    signal_type: FusionSignalType
    strength: float  # 信号强度 0-1
    confidence: float  # 置信度 0-1
    expected_return: float  # 预期收益率
    risk_level: float  # 风险等级 0-1
    timeframe: int  # 建议持有时间（分钟）
    
    # 各模型贡献
    transformer_contribution: float
    cnn_contribution: float
    technical_contribution: float
    
    # 详细信息
    model_signals: Dict[str, Any]
    fusion_reason: str
    timestamp: pd.Timestamp
    
    # 执行建议
    entry_price: Optional[float] = None
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None


class AdaptiveWeightManager:
    """自适应权重管理器"""
    
    def __init__(self, initial_weights: Dict[str, float] = None):
        """
        初始化权重管理器
        
        Args:
            initial_weights: 初始权重配置
        """
        self.weights = initial_weights or {
            'transformer': 0.4,
            'cnn': 0.3,
            'technical': 0.3
        }
        
        # 性能跟踪
        self.model_performance = {
            'transformer': ModelPerformance(),
            'cnn': ModelPerformance(),
            'technical': ModelPerformance()
        }
        
        # 权重调整参数
        self.learning_rate = 0.01
        self.min_weight = 0.1
        self.max_weight = 0.7
        self.performance_window = 50  # 性能评估窗口
        
        # 市场环境因子
        self.market_regime = 'normal'  # normal, volatile, trending, ranging
        self.volatility_factor = 1.0
        
    def update_performance(self, model_name: str, prediction: Any, actual_result: float):
        """
        更新模型性能
        
        Args:
            model_name: 模型名称
            prediction: 模型预测
            actual_result: 实际结果
        """
        if model_name not in self.model_performance:
            return
        
        perf = self.model_performance[model_name]
        
        # 记录预测结果
        perf.recent_predictions.append({
            'prediction': prediction,
            'actual': actual_result,
            'timestamp': datetime.now()
        })
        
        # 计算准确率
        if len(perf.recent_predictions) >= 10:
            correct_predictions = 0
            total_predictions = len(perf.recent_predictions)
            
            for pred_record in perf.recent_predictions:
                pred_direction = self._get_prediction_direction(pred_record['prediction'])
                actual_direction = 1 if pred_record['actual'] > 0 else -1 if pred_record['actual'] < 0 else 0
                
                if pred_direction == actual_direction:
                    correct_predictions += 1
            
            perf.accuracy = correct_predictions / total_predictions
        
        # 计算收益相关指标
        returns = [pred['actual'] for pred in perf.recent_predictions]
        if len(returns) >= 20:
            returns_array = np.array(returns)
            perf.sharpe_ratio = np.mean(returns_array) / (np.std(returns_array) + 1e-8) * np.sqrt(252)
            
            # 计算最大回撤
            cumulative_returns = np.cumprod(1 + returns_array)
            running_max = np.maximum.accumulate(cumulative_returns)
            drawdown = (cumulative_returns - running_max) / running_max
            perf.max_drawdown = abs(np.min(drawdown))
        
        perf.last_update = datetime.now()
        
        # 自适应调整权重
        self._adjust_weights()
    
    def _get_prediction_direction(self, prediction: Any) -> int:
        """获取预测方向"""
        if hasattr(prediction, 'direction'):
            return prediction.direction
        elif hasattr(prediction, 'signal_type'):
            if prediction.signal_type == SignalType.BUY:
                return 1
            elif prediction.signal_type == SignalType.SELL:
                return -1
            else:
                return 0
        else:
            return 0
    
    def _adjust_weights(self):
        """自适应调整权重"""
        # 计算性能分数
        performance_scores = {}
        
        for model_name, perf in self.model_performance.items():
            # 综合性能分数
            accuracy_score = perf.accuracy
            sharpe_score = max(0, min(perf.sharpe_ratio / 2, 1))  # 归一化到0-1
            drawdown_score = max(0, 1 - perf.max_drawdown)
            
            # 时间衰减因子
            time_decay = self._calculate_time_decay(perf.last_update)
            
            performance_scores[model_name] = (
                accuracy_score * 0.4 + 
                sharpe_score * 0.4 + 
                drawdown_score * 0.2
            ) * time_decay
        
        # 根据性能调整权重
        total_score = sum(performance_scores.values())
        if total_score > 0:
            for model_name in self.weights:
                target_weight = performance_scores[model_name] / total_score
                current_weight = self.weights[model_name]
                
                # 渐进式调整
                new_weight = current_weight + self.learning_rate * (target_weight - current_weight)
                
                # 限制权重范围
                new_weight = max(self.min_weight, min(self.max_weight, new_weight))
                self.weights[model_name] = new_weight
        
        # 归一化权重
        total_weight = sum(self.weights.values())
        if total_weight > 0:
            for model_name in self.weights:
                self.weights[model_name] /= total_weight
        
        logger.debug(f"权重已调整: {self.weights}")
    
    def _calculate_time_decay(self, last_update: datetime) -> float:
        """计算时间衰减因子"""
        time_diff = datetime.now() - last_update
        hours_diff = time_diff.total_seconds() / 3600
        
        # 24小时内权重为1，之后逐渐衰减
        if hours_diff <= 24:
            return 1.0
        else:
            return max(0.5, np.exp(-(hours_diff - 24) / 48))
    
    def get_current_weights(self) -> Dict[str, float]:
        """获取当前权重"""
        return self.weights.copy()
    
    def adjust_for_market_regime(self, market_data: pd.DataFrame):
        """根据市场环境调整权重"""
        # 计算市场波动率
        returns = market_data['close'].pct_change().dropna()
        volatility = returns.std() * np.sqrt(252)  # 年化波动率
        
        # 判断市场环境
        if volatility > 0.3:
            self.market_regime = 'volatile'
            # 高波动环境下，增加技术分析权重
            self.weights['technical'] *= 1.2
            self.weights['transformer'] *= 0.9
        elif volatility < 0.15:
            self.market_regime = 'ranging'
            # 低波动环境下，增加CNN模式识别权重
            self.weights['cnn'] *= 1.2
            self.weights['technical'] *= 0.9
        else:
            self.market_regime = 'normal'
        
        # 归一化权重
        total_weight = sum(self.weights.values())
        for model_name in self.weights:
            self.weights[model_name] /= total_weight
        
        self.volatility_factor = volatility


class SignalFusionEngine:
    """信号融合引擎"""
    
    def __init__(self, config: Dict = None):
        """
        初始化信号融合引擎
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        
        # 初始化各个模型
        self.transformer_predictor = None
        self.cnn_recognizer = None
        self.technical_analyzer = TechnicalAnalyzer(self.config.get('technical_config', {}))
        
        # 权重管理器
        initial_weights = self.config.get('initial_weights', {
            'transformer': 0.4,
            'cnn': 0.3,
            'technical': 0.3
        })
        self.weight_manager = AdaptiveWeightManager(initial_weights)
        
        # 融合参数
        self.confidence_threshold = self.config.get('confidence_threshold', 0.6)
        self.signal_strength_threshold = self.config.get('signal_strength_threshold', 0.5)
        self.risk_tolerance = self.config.get('risk_tolerance', 0.7)
        
        # 信号历史
        self.signal_history = deque(maxlen=1000)
        
        # 市场状态监控
        self.market_monitor = MarketStateMonitor()
    
    def initialize_models(self, transformer_config: Dict = None, cnn_config: Dict = None):
        """
        初始化AI模型
        
        Args:
            transformer_config: Transformer配置
            cnn_config: CNN配置
        """
        try:
            if TRANSFORMER_AVAILABLE:
                from .transformer_model import create_transformer_predictor
                self.transformer_predictor = create_transformer_predictor(transformer_config)
                logger.info("Transformer模型初始化完成")
            else:
                logger.warning("Transformer模型不可用，跳过初始化")
                
            if CNN_AVAILABLE:
                from .cnn_model import create_cnn_recognizer
                self.cnn_recognizer = create_cnn_recognizer(cnn_config)
                logger.info("CNN模型初始化完成")
            else:
                logger.warning("CNN模型不可用，跳过初始化")
                
            logger.info("AI模型初始化完成")
        except Exception as e:
            logger.error(f"AI模型初始化失败: {e}")
            # 不抛出异常，允许系统继续运行
    
    def fuse_signals(self, data: pd.DataFrame, symbol: str = None) -> FusedSignal:
        """
        融合多模型信号
        
        Args:
            data: 市场数据
            symbol: 股票代码
            
        Returns:
            融合后的交易信号
        """
        # 更新市场状态
        self.market_monitor.update_market_state(data)
        self.weight_manager.adjust_for_market_regime(data)
        
        # 获取各模型信号
        model_signals = self._collect_model_signals(data)
        
        # 获取当前权重
        weights = self.weight_manager.get_current_weights()
        
        # 计算融合信号
        fused_signal = self._calculate_fused_signal(model_signals, weights)
        
        # 风险调整
        fused_signal = self._apply_risk_adjustment(fused_signal, data)
        
        # 记录信号历史
        self.signal_history.append({
            'timestamp': pd.Timestamp.now(),
            'symbol': symbol,
            'signal': fused_signal,
            'model_signals': model_signals,
            'weights': weights
        })
        
        return fused_signal
    
    def _collect_model_signals(self, data: pd.DataFrame) -> Dict[str, Any]:
        """收集各模型信号"""
        signals = {}
        
        # Transformer信号
        if self.transformer_predictor and self.transformer_predictor.is_trained:
            try:
                transformer_signal = self.transformer_predictor.predict(data)
                signals['transformer'] = transformer_signal
            except Exception as e:
                logger.warning(f"Transformer预测失败: {e}")
                signals['transformer'] = None
        
        # CNN信号
        if self.cnn_recognizer and self.cnn_recognizer.is_trained:
            try:
                cnn_signal = self.cnn_recognizer.predict_pattern(data)
                signals['cnn'] = cnn_signal
            except Exception as e:
                logger.warning(f"CNN预测失败: {e}")
                signals['cnn'] = None
        
        # 技术分析信号
        try:
            technical_signal = self.technical_analyzer.generate_signal(data)
            signals['technical'] = technical_signal
        except Exception as e:
            logger.warning(f"技术分析失败: {e}")
            signals['technical'] = None
        
        return signals
    
    def _calculate_fused_signal(self, model_signals: Dict[str, Any], weights: Dict[str, float]) -> FusedSignal:
        """计算融合信号"""
        # 初始化累积值
        total_strength = 0.0
        total_confidence = 0.0
        total_expected_return = 0.0
        signal_direction_votes = {'buy': 0, 'sell': 0, 'hold': 0}
        
        # 各模型贡献
        contributions = {'transformer': 0.0, 'cnn': 0.0, 'technical': 0.0}
        
        # 处理Transformer信号
        if model_signals.get('transformer'):
            transformer_signal = model_signals['transformer']
            weight = weights.get('transformer', 0)
            
            # 方向投票
            if transformer_signal.direction == 1:
                signal_direction_votes['buy'] += weight
            elif transformer_signal.direction == -1:
                signal_direction_votes['sell'] += weight
            else:
                signal_direction_votes['hold'] += weight
            
            # 累积强度和置信度
            total_strength += transformer_signal.strength * weight
            total_confidence += transformer_signal.confidence * weight
            total_expected_return += transformer_signal.expected_return * weight
            
            contributions['transformer'] = weight * transformer_signal.confidence
        
        # 处理CNN信号
        if model_signals.get('cnn'):
            cnn_signal = model_signals['cnn']
            weight = weights.get('cnn', 0)
            
            # 方向投票
            if cnn_signal.direction == 1:
                signal_direction_votes['buy'] += weight
            elif cnn_signal.direction == -1:
                signal_direction_votes['sell'] += weight
            else:
                signal_direction_votes['hold'] += weight
            
            # 累积强度和置信度
            total_strength += cnn_signal.strength * weight
            total_confidence += cnn_signal.confidence * weight
            
            # CNN主要提供模式识别，预期收益基于模式强度
            pattern_return = cnn_signal.strength * 0.02 * cnn_signal.direction
            total_expected_return += pattern_return * weight
            
            contributions['cnn'] = weight * cnn_signal.confidence
        
        # 处理技术分析信号
        if model_signals.get('technical'):
            technical_signal = model_signals['technical']
            weight = weights.get('technical', 0)
            
            # 方向投票
            if technical_signal.signal_type == SignalType.BUY:
                signal_direction_votes['buy'] += weight
            elif technical_signal.signal_type == SignalType.SELL:
                signal_direction_votes['sell'] += weight
            else:
                signal_direction_votes['hold'] += weight
            
            # 累积强度和置信度
            total_strength += technical_signal.strength * weight
            total_confidence += technical_signal.confidence * weight
            
            # 技术分析预期收益基于信号强度
            tech_return = technical_signal.strength * 0.015 * (1 if technical_signal.signal_type == SignalType.BUY else -1 if technical_signal.signal_type == SignalType.SELL else 0)
            total_expected_return += tech_return * weight
            
            contributions['technical'] = weight * technical_signal.confidence
        
        # 确定最终信号方向
        max_vote = max(signal_direction_votes.values())
        if signal_direction_votes['buy'] == max_vote and max_vote > 0.5:
            if total_strength > 0.8 and total_confidence > 0.8:
                final_signal_type = FusionSignalType.STRONG_BUY
            else:
                final_signal_type = FusionSignalType.BUY
        elif signal_direction_votes['sell'] == max_vote and max_vote > 0.5:
            if total_strength > 0.8 and total_confidence > 0.8:
                final_signal_type = FusionSignalType.STRONG_SELL
            else:
                final_signal_type = FusionSignalType.SELL
        else:
            final_signal_type = FusionSignalType.HOLD
        
        # 计算风险等级
        risk_level = self._calculate_risk_level(model_signals, total_confidence)
        
        # 建议持有时间（基于信号强度和类型）
        if final_signal_type in [FusionSignalType.STRONG_BUY, FusionSignalType.STRONG_SELL]:
            timeframe = min(60, max(15, int(total_strength * 60)))  # 15-60分钟
        elif final_signal_type in [FusionSignalType.BUY, FusionSignalType.SELL]:
            timeframe = min(30, max(5, int(total_strength * 30)))   # 5-30分钟
        else:
            timeframe = 0
        
        # 生成融合原因
        fusion_reason = self._generate_fusion_reason(model_signals, weights, signal_direction_votes)
        
        return FusedSignal(
            signal_type=final_signal_type,
            strength=min(total_strength, 1.0),
            confidence=min(total_confidence, 1.0),
            expected_return=total_expected_return,
            risk_level=risk_level,
            timeframe=timeframe,
            transformer_contribution=contributions['transformer'],
            cnn_contribution=contributions['cnn'],
            technical_contribution=contributions['technical'],
            model_signals=model_signals,
            fusion_reason=fusion_reason,
            timestamp=pd.Timestamp.now()
        )
    
    def _calculate_risk_level(self, model_signals: Dict[str, Any], confidence: float) -> float:
        """计算风险等级"""
        base_risk = 1 - confidence  # 基础风险与置信度成反比
        
        # 模型一致性风险
        directions = []
        if model_signals.get('transformer'):
            directions.append(model_signals['transformer'].direction)
        if model_signals.get('cnn'):
            directions.append(model_signals['cnn'].direction)
        if model_signals.get('technical'):
            if model_signals['technical'].signal_type == SignalType.BUY:
                directions.append(1)
            elif model_signals['technical'].signal_type == SignalType.SELL:
                directions.append(-1)
            else:
                directions.append(0)
        
        # 计算方向一致性
        if len(directions) > 1:
            direction_std = np.std(directions)
            consistency_risk = direction_std / 2  # 归一化到0-1
        else:
            consistency_risk = 0.5  # 单一模型风险较高
        
        # 市场环境风险
        market_risk = self.weight_manager.volatility_factor / 2  # 归一化
        
        # 综合风险
        total_risk = (base_risk * 0.4 + consistency_risk * 0.3 + market_risk * 0.3)
        
        return min(max(total_risk, 0.0), 1.0)
    
    def _generate_fusion_reason(self, model_signals: Dict[str, Any], weights: Dict[str, float], votes: Dict[str, float]) -> str:
        """生成融合原因说明"""
        reasons = []
        
        # 主导信号
        max_vote_type = max(votes, key=votes.get)
        max_vote_value = votes[max_vote_type]
        reasons.append(f"{max_vote_type.upper()}信号占优({max_vote_value:.2f})")
        
        # 各模型贡献
        model_contributions = []
        for model_name, weight in weights.items():
            if model_signals.get(model_name) and weight > 0.1:
                signal = model_signals[model_name]
                if hasattr(signal, 'confidence'):
                    conf = signal.confidence
                elif hasattr(signal, 'strength'):
                    conf = signal.strength
                else:
                    conf = 0.5
                
                model_contributions.append(f"{model_name}({weight:.2f}*{conf:.2f})")
        
        if model_contributions:
            reasons.append("模型贡献: " + ", ".join(model_contributions))
        
        # 市场环境
        reasons.append(f"市场环境: {self.weight_manager.market_regime}")
        
        return "; ".join(reasons)
    
    def _apply_risk_adjustment(self, signal: FusedSignal, data: pd.DataFrame) -> FusedSignal:
        """应用风险调整"""
        current_price = data['close'].iloc[-1]
        
        # 计算止损止盈位
        if signal.signal_type in [FusionSignalType.BUY, FusionSignalType.STRONG_BUY]:
            # 买入信号的止损止盈
            atr = self._calculate_atr(data)
            stop_loss_distance = atr * (2 - signal.confidence)  # 置信度越高，止损越紧
            take_profit_distance = atr * (1 + signal.strength) * 2  # 强度越高，止盈越远
            
            signal.entry_price = current_price
            signal.stop_loss = current_price - stop_loss_distance
            signal.take_profit = current_price + take_profit_distance
            
        elif signal.signal_type in [FusionSignalType.SELL, FusionSignalType.STRONG_SELL]:
            # 卖出信号的止损止盈
            atr = self._calculate_atr(data)
            stop_loss_distance = atr * (2 - signal.confidence)
            take_profit_distance = atr * (1 + signal.strength) * 2
            
            signal.entry_price = current_price
            signal.stop_loss = current_price + stop_loss_distance
            signal.take_profit = current_price - take_profit_distance
        
        # 风险调整信号强度
        if signal.risk_level > self.risk_tolerance:
            signal.strength *= (1 - (signal.risk_level - self.risk_tolerance))
            signal.confidence *= 0.8  # 降低置信度
        
        return signal
    
    def _calculate_atr(self, data: pd.DataFrame, period: int = 14) -> float:
        """计算平均真实波幅"""
        if len(data) < period + 1:
            return data['close'].std() * 0.02  # 简化计算
        
        high = data['high'].values
        low = data['low'].values
        close = data['close'].values
        
        tr_list = []
        for i in range(1, len(data)):
            tr = max(
                high[i] - low[i],
                abs(high[i] - close[i-1]),
                abs(low[i] - close[i-1])
            )
            tr_list.append(tr)
        
        if len(tr_list) >= period:
            return np.mean(tr_list[-period:])
        else:
            return np.mean(tr_list) if tr_list else data['close'].std() * 0.02
    
    def update_model_performance(self, symbol: str, actual_return: float, holding_period: int):
        """
        更新模型性能
        
        Args:
            symbol: 股票代码
            actual_return: 实际收益率
            holding_period: 持有期间（分钟）
        """
        # 查找对应的历史信号
        for record in reversed(self.signal_history):
            if (record['symbol'] == symbol and 
                (pd.Timestamp.now() - record['timestamp']).total_seconds() / 60 <= holding_period + 10):
                
                # 更新各模型性能
                model_signals = record['model_signals']
                
                if model_signals.get('transformer'):
                    self.weight_manager.update_performance('transformer', 
                                                         model_signals['transformer'], 
                                                         actual_return)
                
                if model_signals.get('cnn'):
                    self.weight_manager.update_performance('cnn', 
                                                         model_signals['cnn'], 
                                                         actual_return)
                
                if model_signals.get('technical'):
                    self.weight_manager.update_performance('technical', 
                                                         model_signals['technical'], 
                                                         actual_return)
                break
    
    def get_model_weights(self) -> Dict[str, float]:
        """获取当前模型权重"""
        return self.weight_manager.get_current_weights()
    
    def get_performance_summary(self) -> Dict[str, Dict]:
        """获取性能摘要"""
        summary = {}
        for model_name, perf in self.weight_manager.model_performance.items():
            summary[model_name] = {
                'accuracy': perf.accuracy,
                'sharpe_ratio': perf.sharpe_ratio,
                'max_drawdown': perf.max_drawdown,
                'recent_predictions_count': len(perf.recent_predictions),
                'last_update': perf.last_update.isoformat()
            }
        return summary
    
    def save_state(self, filepath: str):
        """保存融合引擎状态"""
        state = {
            'config': self.config,
            'weights': self.weight_manager.weights,
            'model_performance': {
                name: {
                    'accuracy': perf.accuracy,
                    'precision': perf.precision,
                    'recall': perf.recall,
                    'sharpe_ratio': perf.sharpe_ratio,
                    'max_drawdown': perf.max_drawdown,
                    'last_update': perf.last_update.isoformat()
                }
                for name, perf in self.weight_manager.model_performance.items()
            },
            'signal_history': list(self.signal_history)[-100:],  # 保存最近100条记录
            'market_regime': self.weight_manager.market_regime,
            'volatility_factor': self.weight_manager.volatility_factor
        }
        
        with open(filepath, 'wb') as f:
            pickle.dump(state, f)
        
        logger.info(f"融合引擎状态已保存到 {filepath}")
    
    def load_state(self, filepath: str):
        """加载融合引擎状态"""
        try:
            with open(filepath, 'rb') as f:
                state = pickle.load(f)
            
            self.config = state.get('config', {})
            self.weight_manager.weights = state.get('weights', self.weight_manager.weights)
            
            # 恢复模型性能
            for name, perf_data in state.get('model_performance', {}).items():
                if name in self.weight_manager.model_performance:
                    perf = self.weight_manager.model_performance[name]
                    perf.accuracy = perf_data.get('accuracy', 0.5)
                    perf.precision = perf_data.get('precision', 0.5)
                    perf.recall = perf_data.get('recall', 0.5)
                    perf.sharpe_ratio = perf_data.get('sharpe_ratio', 0.0)
                    perf.max_drawdown = perf_data.get('max_drawdown', 0.0)
                    perf.last_update = datetime.fromisoformat(perf_data.get('last_update', datetime.now().isoformat()))
            
            # 恢复信号历史
            signal_history = state.get('signal_history', [])
            self.signal_history = deque(signal_history, maxlen=1000)
            
            self.weight_manager.market_regime = state.get('market_regime', 'normal')
            self.weight_manager.volatility_factor = state.get('volatility_factor', 1.0)
            
            logger.info(f"融合引擎状态已从 {filepath} 加载")
            
        except Exception as e:
            logger.error(f"加载融合引擎状态失败: {e}")
            raise


class MarketStateMonitor:
    """市场状态监控器"""
    
    def __init__(self):
        self.current_state = {
            'trend': 'neutral',
            'volatility': 'normal',
            'volume': 'normal',
            'momentum': 'neutral'
        }
        
        self.state_history = deque(maxlen=100)
    
    def update_market_state(self, data: pd.DataFrame):
        """更新市场状态"""
        if len(data) < 20:
            return
        
        # 趋势分析
        recent_prices = data['close'].tail(20)
        trend_slope = np.polyfit(range(len(recent_prices)), recent_prices, 1)[0]
        
        if trend_slope > recent_prices.mean() * 0.001:
            trend = 'bullish'
        elif trend_slope < -recent_prices.mean() * 0.001:
            trend = 'bearish'
        else:
            trend = 'neutral'
        
        # 波动率分析
        returns = data['close'].pct_change().tail(20)
        volatility = returns.std()
        
        if volatility > 0.02:
            vol_state = 'high'
        elif volatility < 0.005:
            vol_state = 'low'
        else:
            vol_state = 'normal'
        
        # 成交量分析
        volume_ma = data['volume'].rolling(20).mean().iloc[-1]
        current_volume = data['volume'].iloc[-1]
        
        if current_volume > volume_ma * 1.5:
            volume_state = 'high'
        elif current_volume < volume_ma * 0.5:
            volume_state = 'low'
        else:
            volume_state = 'normal'
        
        # 动量分析
        momentum = data['close'].iloc[-1] / data['close'].iloc[-10] - 1
        
        if momentum > 0.02:
            momentum_state = 'strong_positive'
        elif momentum > 0.005:
            momentum_state = 'positive'
        elif momentum < -0.02:
            momentum_state = 'strong_negative'
        elif momentum < -0.005:
            momentum_state = 'negative'
        else:
            momentum_state = 'neutral'
        
        # 更新状态
        self.current_state = {
            'trend': trend,
            'volatility': vol_state,
            'volume': volume_state,
            'momentum': momentum_state
        }
        
        self.state_history.append({
            'timestamp': pd.Timestamp.now(),
            'state': self.current_state.copy()
        })


def create_signal_fusion_engine(config: Dict = None) -> SignalFusionEngine:
    """
    创建信号融合引擎
    
    Args:
        config: 配置参数
        
    Returns:
        SignalFusionEngine实例
    """
    return SignalFusionEngine(config)