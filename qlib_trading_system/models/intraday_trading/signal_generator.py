"""
多时间框架信号生成和决策系统
实现多时间框架信号生成、强度置信度评估、信号过滤确认和动态决策阈值调整
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, field
from enum import Enum
import logging
from collections import deque
import json
from datetime import datetime, timedelta

# 条件导入信号融合模块
try:
    from .signal_fusion import SignalFusionEngine, FusedSignal, FusionSignalType
    FUSION_AVAILABLE = True
except ImportError:
    FUSION_AVAILABLE = False
    SignalFusionEngine = None
    FusedSignal = None
    FusionSignalType = None

# 导入技术分析器
from .technical_analyzer import TechnicalAnalyzer, TechnicalSignal, SignalType

logger = logging.getLogger(__name__)


class TimeFrame(Enum):
    """时间框架枚举"""
    MINUTE_1 = "1min"
    MINUTE_5 = "5min"
    MINUTE_15 = "15min"
    MINUTE_30 = "30min"
    HOUR_1 = "1hour"


class SignalStrength(Enum):
    """信号强度等级"""
    VERY_WEAK = 1
    WEAK = 2
    MODERATE = 3
    STRONG = 4
    VERY_STRONG = 5


class ConfidenceLevel(Enum):
    """置信度等级"""
    VERY_LOW = 1
    LOW = 2
    MEDIUM = 3
    HIGH = 4
    VERY_HIGH = 5


@dataclass
class MultiTimeFrameSignal:
    """多时间框架信号"""
    symbol: str
    timestamp: pd.Timestamp
    
    # 各时间框架信号
    signals_1min: Optional[Any] = None
    signals_5min: Optional[Any] = None
    signals_15min: Optional[Any] = None
    signals_30min: Optional[Any] = None
    signals_1hour: Optional[Any] = None
    
    # 综合评估
    overall_direction: int = 0  # 1买入, -1卖出, 0持有
    overall_strength: float = 0.0  # 0-1
    overall_confidence: float = 0.0  # 0-1
    
    # 时间框架一致性
    timeframe_consensus: float = 0.0  # 0-1
    
    # 执行建议
    entry_timeframe: Optional[TimeFrame] = None
    exit_timeframe: Optional[TimeFrame] = None
    holding_period: int = 0  # 建议持有时间（分钟）
    
    # 详细信息
    analysis_details: Dict[str, Any] = field(default_factory=dict)
    
    # 风险评估
    risk_level: float = 0.0  # 0-1
    
    # 执行参数
    suggested_position_size: float = 0.0  # 建议仓位大小
    stop_loss_price: Optional[float] = None
    take_profit_price: Optional[float] = None


@dataclass
class SignalConfidence:
    """信号置信度评估"""
    base_confidence: float = 0.0  # 基础置信度
    timeframe_consistency: float = 0.0  # 时间框架一致性
    volume_confirmation: float = 0.0  # 成交量确认
    trend_alignment: float = 0.0  # 趋势一致性
    technical_strength: float = 0.0  # 技术指标强度
    
    # 综合置信度
    overall_confidence: float = 0.0
    
    # 置信度等级
    confidence_level: ConfidenceLevel = ConfidenceLevel.MEDIUM
    
    # 详细说明
    confidence_factors: Dict[str, float] = field(default_factory=dict)


@dataclass
class DynamicThreshold:
    """动态决策阈值"""
    buy_threshold: float = 0.6  # 买入阈值
    sell_threshold: float = 0.6  # 卖出阈值
    confidence_threshold: float = 0.5  # 置信度阈值
    
    # 市场环境调整因子
    volatility_factor: float = 1.0  # 波动率因子
    trend_factor: float = 1.0  # 趋势因子
    volume_factor: float = 1.0  # 成交量因子
    
    # 自适应参数
    adaptation_rate: float = 0.05  # 自适应速率
    performance_window: int = 50  # 性能评估窗口
    
    # 历史表现
    recent_accuracy: float = 0.5  # 近期准确率
    recent_returns: deque = field(default_factory=lambda: deque(maxlen=50))


class MultiTimeFrameSignalGenerator:
    """多时间框架信号生成器"""
    
    def __init__(self, config: Dict = None):
        """
        初始化多时间框架信号生成器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        
        # 时间框架配置
        self.timeframes = [
            TimeFrame.MINUTE_1,
            TimeFrame.MINUTE_5, 
            TimeFrame.MINUTE_15,
            TimeFrame.MINUTE_30,
            TimeFrame.HOUR_1
        ]
        
        # 时间框架权重
        self.timeframe_weights = self.config.get('timeframe_weights', {
            TimeFrame.MINUTE_1: 0.15,   # 入场时机
            TimeFrame.MINUTE_5: 0.25,   # 短期趋势
            TimeFrame.MINUTE_15: 0.30,  # 中期趋势
            TimeFrame.MINUTE_30: 0.20,  # 长期趋势
            TimeFrame.HOUR_1: 0.10      # 大方向
        })
        
        # 初始化信号融合引擎
        if FUSION_AVAILABLE:
            self.fusion_engine = SignalFusionEngine(self.config.get('fusion_config', {}))
        else:
            self.fusion_engine = None
            logger.warning("信号融合引擎不可用，将使用技术分析器")
        
        # 技术分析器
        self.technical_analyzer = TechnicalAnalyzer(self.config.get('technical_config', {}))
        
        # 动态阈值管理器
        self.threshold_manager = DynamicThresholdManager(self.config.get('threshold_config', {}))
        
        # 信号过滤器
        self.signal_filter = SignalFilter(self.config.get('filter_config', {}))
        
        # 置信度评估器
        self.confidence_evaluator = ConfidenceEvaluator(self.config.get('confidence_config', {}))
        
        # 信号历史记录
        self.signal_history = deque(maxlen=1000)
        
        # 性能统计
        self.performance_stats = {
            'total_signals': 0,
            'correct_predictions': 0,
            'accuracy': 0.0,
            'avg_return': 0.0,
            'sharpe_ratio': 0.0
        }
    
    def resample_data(self, data: pd.DataFrame, timeframe: TimeFrame) -> pd.DataFrame:
        """
        重采样数据到指定时间框架
        
        Args:
            data: 原始数据（1分钟级别）
            timeframe: 目标时间框架
            
        Returns:
            重采样后的数据
        """
        if timeframe == TimeFrame.MINUTE_1:
            return data
        
        # 设置时间索引
        if 'timestamp' in data.columns:
            data = data.set_index('timestamp')
        elif not isinstance(data.index, pd.DatetimeIndex):
            # 如果没有时间索引，创建一个
            data.index = pd.date_range(
                start=pd.Timestamp.now() - pd.Timedelta(minutes=len(data)-1),
                periods=len(data),
                freq='1min'
            )
        
        # 重采样规则
        resample_rules = {
            TimeFrame.MINUTE_5: '5min',
            TimeFrame.MINUTE_15: '15min', 
            TimeFrame.MINUTE_30: '30min',
            TimeFrame.HOUR_1: '1h'  # 修复FutureWarning
        }
        
        rule = resample_rules.get(timeframe, '5min')
        
        # 执行重采样
        resampled = data.resample(rule).agg({
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'close': 'last',
            'volume': 'sum'
        }).dropna()
        
        return resampled.reset_index()
    
    def generate_timeframe_signal(self, data: pd.DataFrame, timeframe: TimeFrame) -> Any:
        """
        为指定时间框架生成信号
        
        Args:
            data: 市场数据
            timeframe: 时间框架
            
        Returns:
            该时间框架的信号
        """
        # 重采样数据
        resampled_data = self.resample_data(data, timeframe)
        
        if len(resampled_data) < 20:  # 数据不足
            return None
        
        try:
            # 使用信号融合引擎生成信号
            if self.fusion_engine:
                signal = self.fusion_engine.fuse_signals(resampled_data)
            else:
                # 回退到技术分析
                signal = self.technical_analyzer.generate_signal(resampled_data)
            
            # 添加时间框架信息
            signal.timeframe = timeframe
            signal.data_points = len(resampled_data)
            
            return signal
            
        except Exception as e:
            logger.error(f"生成{timeframe.value}信号失败: {e}")
            return None
    
    def generate_multi_timeframe_signal(self, data: pd.DataFrame, symbol: str = None) -> MultiTimeFrameSignal:
        """
        生成多时间框架综合信号
        
        Args:
            data: 市场数据
            symbol: 股票代码
            
        Returns:
            多时间框架综合信号
        """
        # 生成各时间框架信号
        timeframe_signals = {}
        
        for timeframe in self.timeframes:
            signal = self.generate_timeframe_signal(data, timeframe)
            if signal:
                timeframe_signals[timeframe] = signal
        
        # 创建多时间框架信号对象
        mtf_signal = MultiTimeFrameSignal(
            symbol=symbol or 'UNKNOWN',
            timestamp=pd.Timestamp.now()
        )
        
        # 填充各时间框架信号
        mtf_signal.signals_1min = timeframe_signals.get(TimeFrame.MINUTE_1)
        mtf_signal.signals_5min = timeframe_signals.get(TimeFrame.MINUTE_5)
        mtf_signal.signals_15min = timeframe_signals.get(TimeFrame.MINUTE_15)
        mtf_signal.signals_30min = timeframe_signals.get(TimeFrame.MINUTE_30)
        mtf_signal.signals_1hour = timeframe_signals.get(TimeFrame.HOUR_1)
        
        # 计算综合信号
        self._calculate_overall_signal(mtf_signal, timeframe_signals)
        
        # 评估置信度
        confidence_assessment = self.confidence_evaluator.evaluate_confidence(
            timeframe_signals, data
        )
        mtf_signal.overall_confidence = confidence_assessment.overall_confidence
        
        # 计算时间框架一致性
        mtf_signal.timeframe_consensus = self._calculate_timeframe_consensus(timeframe_signals)
        
        # 应用信号过滤
        mtf_signal = self.signal_filter.filter_signal(mtf_signal, data)
        
        # 动态阈值决策
        mtf_signal = self.threshold_manager.apply_dynamic_threshold(mtf_signal, data)
        
        # 计算执行参数
        self._calculate_execution_parameters(mtf_signal, data)
        
        # 记录信号历史
        self.signal_history.append({
            'timestamp': mtf_signal.timestamp,
            'symbol': symbol,
            'signal': mtf_signal,
            'timeframe_signals': timeframe_signals
        })
        
        # 更新性能统计
        self.performance_stats['total_signals'] += 1
        
        return mtf_signal
    
    def _calculate_overall_signal(self, mtf_signal: MultiTimeFrameSignal, 
                                timeframe_signals: Dict[TimeFrame, Any]):
        """
        计算综合信号
        
        Args:
            mtf_signal: 多时间框架信号对象
            timeframe_signals: 各时间框架信号
        """
        total_strength = 0.0
        total_weight = 0.0
        direction_votes = {'buy': 0, 'sell': 0, 'hold': 0}
        
        for timeframe, signal in timeframe_signals.items():
            weight = self.timeframe_weights.get(timeframe, 0.1)
            
            # 获取信号方向和强度
            if hasattr(signal, 'signal_type'):
                # 技术分析信号
                if signal.signal_type == SignalType.BUY:
                    direction_votes['buy'] += weight
                    total_strength += signal.strength * weight
                elif signal.signal_type == SignalType.SELL:
                    direction_votes['sell'] += weight
                    total_strength += signal.strength * weight
                else:
                    direction_votes['hold'] += weight
            elif hasattr(signal, 'direction'):
                # 融合信号
                if signal.direction == 1:
                    direction_votes['buy'] += weight
                    total_strength += signal.strength * weight
                elif signal.direction == -1:
                    direction_votes['sell'] += weight
                    total_strength += signal.strength * weight
                else:
                    direction_votes['hold'] += weight
            
            total_weight += weight
        
        # 确定最终方向
        max_vote = max(direction_votes.values())
        if direction_votes['buy'] == max_vote and max_vote > 0.4:
            mtf_signal.overall_direction = 1
        elif direction_votes['sell'] == max_vote and max_vote > 0.4:
            mtf_signal.overall_direction = -1
        else:
            mtf_signal.overall_direction = 0
        
        # 计算综合强度
        if total_weight > 0:
            mtf_signal.overall_strength = min(total_strength / total_weight, 1.0)
        
        # 存储分析详情
        mtf_signal.analysis_details = {
            'direction_votes': direction_votes,
            'total_weight': total_weight,
            'timeframe_count': len(timeframe_signals)
        }
    
    def _calculate_timeframe_consensus(self, timeframe_signals: Dict[TimeFrame, Any]) -> float:
        """
        计算时间框架一致性
        
        Args:
            timeframe_signals: 各时间框架信号
            
        Returns:
            一致性分数 (0-1)
        """
        if len(timeframe_signals) < 2:
            return 0.5
        
        directions = []
        for signal in timeframe_signals.values():
            if hasattr(signal, 'signal_type'):
                if signal.signal_type == SignalType.BUY:
                    directions.append(1)
                elif signal.signal_type == SignalType.SELL:
                    directions.append(-1)
                else:
                    directions.append(0)
            elif hasattr(signal, 'direction'):
                directions.append(signal.direction)
            else:
                directions.append(0)
        
        # 计算方向一致性
        if len(directions) == 0:
            return 0.5
        
        # 统计各方向的数量
        buy_count = directions.count(1)
        sell_count = directions.count(-1)
        hold_count = directions.count(0)
        
        total_count = len(directions)
        max_count = max(buy_count, sell_count, hold_count)
        
        # 一致性 = 主导方向占比
        consensus = max_count / total_count
        
        return consensus
    
    def _calculate_execution_parameters(self, mtf_signal: MultiTimeFrameSignal, data: pd.DataFrame):
        """
        计算执行参数
        
        Args:
            mtf_signal: 多时间框架信号
            data: 市场数据
        """
        current_price = data['close'].iloc[-1]
        
        # 计算ATR用于止损止盈
        atr = self._calculate_atr(data)
        
        # 根据信号强度和置信度确定仓位大小
        base_position_size = 0.1  # 基础仓位10%
        strength_multiplier = mtf_signal.overall_strength
        confidence_multiplier = mtf_signal.overall_confidence
        
        mtf_signal.suggested_position_size = min(
            base_position_size * strength_multiplier * confidence_multiplier * 2,
            0.3  # 最大30%仓位
        )
        
        # 计算止损止盈位
        if mtf_signal.overall_direction == 1:  # 买入
            # 止损距离基于ATR和置信度
            stop_distance = atr * (2.5 - mtf_signal.overall_confidence)
            profit_distance = atr * (1 + mtf_signal.overall_strength) * 3
            
            mtf_signal.stop_loss_price = current_price - stop_distance
            mtf_signal.take_profit_price = current_price + profit_distance
            
        elif mtf_signal.overall_direction == -1:  # 卖出
            stop_distance = atr * (2.5 - mtf_signal.overall_confidence)
            profit_distance = atr * (1 + mtf_signal.overall_strength) * 3
            
            mtf_signal.stop_loss_price = current_price + stop_distance
            mtf_signal.take_profit_price = current_price - profit_distance
        
        # 确定建议持有时间
        if mtf_signal.overall_strength > 0.8:
            mtf_signal.holding_period = 60  # 强信号持有1小时
        elif mtf_signal.overall_strength > 0.6:
            mtf_signal.holding_period = 30  # 中等信号持有30分钟
        else:
            mtf_signal.holding_period = 15  # 弱信号持有15分钟
        
        # 确定入场和出场时间框架
        if mtf_signal.overall_direction != 0:
            # 入场使用最短时间框架
            mtf_signal.entry_timeframe = TimeFrame.MINUTE_1
            
            # 出场使用中等时间框架
            if mtf_signal.overall_strength > 0.7:
                mtf_signal.exit_timeframe = TimeFrame.MINUTE_15
            else:
                mtf_signal.exit_timeframe = TimeFrame.MINUTE_5
    
    def _calculate_atr(self, data: pd.DataFrame, period: int = 14) -> float:
        """计算平均真实波幅"""
        if len(data) < period + 1:
            return data['close'].std() * 0.02
        
        high = data['high'].values
        low = data['low'].values
        close = data['close'].values
        
        tr_list = []
        for i in range(1, len(data)):
            tr = max(
                high[i] - low[i],
                abs(high[i] - close[i-1]),
                abs(low[i] - close[i-1])
            )
            tr_list.append(tr)
        
        if len(tr_list) >= period:
            return np.mean(tr_list[-period:])
        else:
            return np.mean(tr_list) if tr_list else data['close'].std() * 0.02
    
    def update_performance(self, symbol: str, actual_return: float, holding_period: int):
        """
        更新性能统计
        
        Args:
            symbol: 股票代码
            actual_return: 实际收益率
            holding_period: 持有期间
        """
        # 查找对应的历史信号
        for record in reversed(self.signal_history):
            if (record['symbol'] == symbol and 
                (pd.Timestamp.now() - record['timestamp']).total_seconds() / 60 <= holding_period + 10):
                
                signal = record['signal']
                
                # 判断预测是否正确
                predicted_direction = signal.overall_direction
                actual_direction = 1 if actual_return > 0.01 else -1 if actual_return < -0.01 else 0
                
                if predicted_direction == actual_direction:
                    self.performance_stats['correct_predictions'] += 1
                
                # 更新准确率
                if self.performance_stats['total_signals'] > 0:
                    self.performance_stats['accuracy'] = (
                        self.performance_stats['correct_predictions'] / 
                        self.performance_stats['total_signals']
                    )
                
                # 更新平均收益
                returns = [r['actual_return'] for r in self.signal_history 
                          if 'actual_return' in r]
                if returns:
                    self.performance_stats['avg_return'] = np.mean(returns)
                    self.performance_stats['sharpe_ratio'] = (
                        np.mean(returns) / (np.std(returns) + 1e-8) * np.sqrt(252)
                    )
                
                # 记录实际收益
                record['actual_return'] = actual_return
                record['holding_period'] = holding_period
                
                # 更新融合引擎性能
                if self.fusion_engine:
                    self.fusion_engine.update_model_performance(symbol, actual_return, holding_period)
                
                # 更新动态阈值
                self.threshold_manager.update_performance(predicted_direction, actual_return)
                
                break
    
    def get_performance_summary(self) -> Dict:
        """获取性能摘要"""
        summary = {
            'signal_generator': self.performance_stats.copy(),
            'timeframe_weights': self.timeframe_weights,
            'recent_signals': len(self.signal_history)
        }
        
        if self.fusion_engine:
            summary['fusion_engine'] = self.fusion_engine.get_performance_summary()
        
        summary['threshold_manager'] = self.threshold_manager.get_performance_summary()
        
        return summary



class ConfidenceEvaluator:
    """置信度评估器"""
    
    def __init__(self, config: Dict = None):
        """
        初始化置信度评估器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        
        # 置信度权重配置
        self.confidence_weights = self.config.get('confidence_weights', {
            'timeframe_consistency': 0.3,  # 时间框架一致性
            'volume_confirmation': 0.2,    # 成交量确认
            'trend_alignment': 0.2,        # 趋势一致性
            'technical_strength': 0.2,     # 技术指标强度
            'market_condition': 0.1        # 市场环境
        })
        
        # 历史置信度表现
        self.confidence_history = deque(maxlen=200)
    
    def evaluate_confidence(self, timeframe_signals: Dict[TimeFrame, Any], 
                          data: pd.DataFrame) -> SignalConfidence:
        """
        评估信号置信度
        
        Args:
            timeframe_signals: 各时间框架信号
            data: 市场数据
            
        Returns:
            置信度评估结果
        """
        confidence = SignalConfidence()
        
        # 1. 时间框架一致性评估
        confidence.timeframe_consistency = self._evaluate_timeframe_consistency(timeframe_signals)
        
        # 2. 成交量确认评估
        confidence.volume_confirmation = self._evaluate_volume_confirmation(timeframe_signals, data)
        
        # 3. 趋势一致性评估
        confidence.trend_alignment = self._evaluate_trend_alignment(timeframe_signals, data)
        
        # 4. 技术指标强度评估
        confidence.technical_strength = self._evaluate_technical_strength(timeframe_signals)
        
        # 5. 市场环境评估
        market_condition_score = self._evaluate_market_condition(data)
        
        # 计算综合置信度
        weights = self.confidence_weights
        confidence.overall_confidence = (
            confidence.timeframe_consistency * weights['timeframe_consistency'] +
            confidence.volume_confirmation * weights['volume_confirmation'] +
            confidence.trend_alignment * weights['trend_alignment'] +
            confidence.technical_strength * weights['technical_strength'] +
            market_condition_score * weights['market_condition']
        )
        
        # 限制在0-1范围内
        confidence.overall_confidence = max(0.0, min(1.0, confidence.overall_confidence))
        
        # 确定置信度等级
        if confidence.overall_confidence >= 0.8:
            confidence.confidence_level = ConfidenceLevel.VERY_HIGH
        elif confidence.overall_confidence >= 0.65:
            confidence.confidence_level = ConfidenceLevel.HIGH
        elif confidence.overall_confidence >= 0.5:
            confidence.confidence_level = ConfidenceLevel.MEDIUM
        elif confidence.overall_confidence >= 0.35:
            confidence.confidence_level = ConfidenceLevel.LOW
        else:
            confidence.confidence_level = ConfidenceLevel.VERY_LOW
        
        # 记录置信度因子
        confidence.confidence_factors = {
            'timeframe_consistency': confidence.timeframe_consistency,
            'volume_confirmation': confidence.volume_confirmation,
            'trend_alignment': confidence.trend_alignment,
            'technical_strength': confidence.technical_strength,
            'market_condition': market_condition_score
        }
        
        # 记录历史
        self.confidence_history.append({
            'timestamp': pd.Timestamp.now(),
            'confidence': confidence.overall_confidence,
            'factors': confidence.confidence_factors
        })
        
        return confidence
    
    def _evaluate_timeframe_consistency(self, timeframe_signals: Dict[TimeFrame, Any]) -> float:
        """评估时间框架一致性"""
        if len(timeframe_signals) < 2:
            return 0.5
        
        directions = []
        strengths = []
        
        for signal in timeframe_signals.values():
            if hasattr(signal, 'signal_type'):
                # 技术分析信号
                if signal.signal_type == SignalType.BUY:
                    directions.append(1)
                elif signal.signal_type == SignalType.SELL:
                    directions.append(-1)
                else:
                    directions.append(0)
                strengths.append(signal.strength)
            elif hasattr(signal, 'direction'):
                # 融合信号
                directions.append(signal.direction)
                strengths.append(signal.strength)
        
        if not directions:
            return 0.5
        
        # 方向一致性
        direction_consistency = 0.0
        if len(set(directions)) == 1:  # 所有方向一致
            direction_consistency = 1.0
        elif len(set(directions)) == 2:  # 部分一致
            max_count = max(directions.count(1), directions.count(-1), directions.count(0))
            direction_consistency = max_count / len(directions)
        
        # 强度一致性
        strength_consistency = 0.0
        if strengths:
            strength_std = np.std(strengths)
            strength_mean = np.mean(strengths)
            if strength_mean > 0:
                strength_consistency = max(0, 1 - (strength_std / strength_mean))
        
        # 综合一致性
        consistency = (direction_consistency * 0.7 + strength_consistency * 0.3)
        return min(max(consistency, 0.0), 1.0)
    
    def _evaluate_volume_confirmation(self, timeframe_signals: Dict[TimeFrame, Any], 
                                    data: pd.DataFrame) -> float:
        """评估成交量确认"""
        if len(data) < 20:
            return 0.5
        
        # 计算成交量指标
        current_volume = data['volume'].iloc[-1]
        volume_ma = data['volume'].rolling(20).mean().iloc[-1]
        volume_ratio = current_volume / volume_ma if volume_ma > 0 else 1.0
        
        # 价格变化
        price_change = data['close'].pct_change().iloc[-1]
        
        # 成交量确认评分
        volume_score = 0.5  # 基础分数
        
        # 放量上涨/下跌确认
        if abs(price_change) > 0.01:  # 价格有明显变化
            if volume_ratio > 1.5:  # 放量
                volume_score = min(0.8 + (volume_ratio - 1.5) * 0.1, 1.0)
            elif volume_ratio > 1.2:  # 温和放量
                volume_score = 0.7
            elif volume_ratio < 0.8:  # 缩量
                volume_score = 0.3
        
        # 考虑信号方向与成交量的匹配度
        signal_directions = []
        for signal in timeframe_signals.values():
            if hasattr(signal, 'signal_type'):
                if signal.signal_type == SignalType.BUY:
                    signal_directions.append(1)
                elif signal.signal_type == SignalType.SELL:
                    signal_directions.append(-1)
            elif hasattr(signal, 'direction'):
                signal_directions.append(signal.direction)
        
        if signal_directions:
            dominant_direction = max(set(signal_directions), key=signal_directions.count)
            
            # 买入信号配合放量上涨
            if dominant_direction == 1 and price_change > 0 and volume_ratio > 1.2:
                volume_score *= 1.2
            # 卖出信号配合放量下跌
            elif dominant_direction == -1 and price_change < 0 and volume_ratio > 1.2:
                volume_score *= 1.2
            # 信号与成交量不匹配
            elif abs(dominant_direction) == 1 and volume_ratio < 0.8:
                volume_score *= 0.8
        
        return min(max(volume_score, 0.0), 1.0)
    
    def _evaluate_trend_alignment(self, timeframe_signals: Dict[TimeFrame, Any], 
                                data: pd.DataFrame) -> float:
        """评估趋势一致性"""
        if len(data) < 30:
            return 0.5
        
        # 计算不同周期的趋势
        short_trend = self._calculate_trend(data['close'].tail(10))
        medium_trend = self._calculate_trend(data['close'].tail(20))
        long_trend = self._calculate_trend(data['close'].tail(30))
        
        trends = [short_trend, medium_trend, long_trend]
        
        # 趋势一致性评分
        trend_directions = [1 if t > 0.001 else -1 if t < -0.001 else 0 for t in trends]
        
        if len(set(trend_directions)) == 1:  # 完全一致
            trend_consistency = 1.0
        elif len(set(trend_directions)) == 2:  # 部分一致
            max_count = max(trend_directions.count(1), trend_directions.count(-1), trend_directions.count(0))
            trend_consistency = max_count / len(trend_directions)
        else:  # 完全不一致
            trend_consistency = 0.3
        
        # 信号与趋势的一致性
        signal_directions = []
        for signal in timeframe_signals.values():
            if hasattr(signal, 'signal_type'):
                if signal.signal_type == SignalType.BUY:
                    signal_directions.append(1)
                elif signal.signal_type == SignalType.SELL:
                    signal_directions.append(-1)
            elif hasattr(signal, 'direction'):
                signal_directions.append(signal.direction)
        
        signal_trend_alignment = 0.5
        if signal_directions:
            dominant_signal = max(set(signal_directions), key=signal_directions.count)
            dominant_trend = max(set(trend_directions), key=trend_directions.count)
            
            if dominant_signal == dominant_trend:
                signal_trend_alignment = 0.8
            elif dominant_signal == -dominant_trend:
                signal_trend_alignment = 0.2
        
        # 综合评分
        alignment_score = (trend_consistency * 0.6 + signal_trend_alignment * 0.4)
        return min(max(alignment_score, 0.0), 1.0)
    
    def _calculate_trend(self, prices: pd.Series) -> float:
        """计算价格趋势斜率"""
        if len(prices) < 3:
            return 0.0
        
        x = np.arange(len(prices))
        slope, _ = np.polyfit(x, prices.values, 1)
        
        # 归一化斜率
        mean_price = prices.mean()
        if mean_price > 0:
            normalized_slope = slope / mean_price
        else:
            normalized_slope = 0.0
        
        return normalized_slope
    
    def _evaluate_technical_strength(self, timeframe_signals: Dict[TimeFrame, Any]) -> float:
        """评估技术指标强度"""
        if not timeframe_signals:
            return 0.5
        
        strengths = []
        confidences = []
        
        for signal in timeframe_signals.values():
            if hasattr(signal, 'strength'):
                strengths.append(signal.strength)
            if hasattr(signal, 'confidence'):
                confidences.append(signal.confidence)
        
        # 计算平均强度和置信度
        avg_strength = np.mean(strengths) if strengths else 0.5
        avg_confidence = np.mean(confidences) if confidences else 0.5
        
        # 综合技术强度
        technical_strength = (avg_strength * 0.6 + avg_confidence * 0.4)
        
        return min(max(technical_strength, 0.0), 1.0)
    
    def _evaluate_market_condition(self, data: pd.DataFrame) -> float:
        """评估市场环境"""
        if len(data) < 20:
            return 0.5
        
        # 计算市场波动率
        returns = data['close'].pct_change().dropna()
        volatility = returns.std()
        
        # 计算成交量稳定性
        volume_cv = data['volume'].std() / data['volume'].mean() if data['volume'].mean() > 0 else 1.0
        
        # 市场环境评分
        market_score = 0.5
        
        # 适度波动有利于信号有效性
        if 0.01 < volatility < 0.03:
            market_score += 0.2
        elif volatility > 0.05:  # 过度波动
            market_score -= 0.2
        
        # 成交量稳定性
        if volume_cv < 1.0:  # 成交量相对稳定
            market_score += 0.1
        elif volume_cv > 2.0:  # 成交量过于不稳定
            market_score -= 0.1
        
        return min(max(market_score, 0.0), 1.0)


class SignalFilter:
    """信号过滤器"""
    
    def __init__(self, config: Dict = None):
        """
        初始化信号过滤器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        
        # 过滤阈值
        self.min_strength = self.config.get('min_strength', 0.3)
        self.min_confidence = self.config.get('min_confidence', 0.4)
        self.min_consensus = self.config.get('min_consensus', 0.5)
        
        # 风险过滤参数
        self.max_risk_level = self.config.get('max_risk_level', 0.8)
        self.min_volume_ratio = self.config.get('min_volume_ratio', 0.5)
        
        # 时间过滤参数
        self.min_holding_period = self.config.get('min_holding_period', 5)  # 最小持有5分钟
        self.max_holding_period = self.config.get('max_holding_period', 120)  # 最大持有2小时
        
        # 过滤统计
        self.filter_stats = {
            'total_signals': 0,
            'filtered_signals': 0,
            'filter_reasons': {}
        }
    
    def filter_signal(self, signal: MultiTimeFrameSignal, data: pd.DataFrame) -> MultiTimeFrameSignal:
        """
        过滤和确认信号
        
        Args:
            signal: 原始信号
            data: 市场数据
            
        Returns:
            过滤后的信号
        """
        self.filter_stats['total_signals'] += 1
        
        # 应用各种过滤条件
        filter_results = []
        
        # 1. 强度过滤
        if signal.overall_strength < self.min_strength:
            filter_results.append(f"信号强度不足: {signal.overall_strength:.3f} < {self.min_strength}")
        
        # 2. 置信度过滤
        if signal.overall_confidence < self.min_confidence:
            filter_results.append(f"置信度不足: {signal.overall_confidence:.3f} < {self.min_confidence}")
        
        # 3. 一致性过滤
        if signal.timeframe_consensus < self.min_consensus:
            filter_results.append(f"时间框架一致性不足: {signal.timeframe_consensus:.3f} < {self.min_consensus}")
        
        # 4. 风险过滤
        if signal.risk_level > self.max_risk_level:
            filter_results.append(f"风险等级过高: {signal.risk_level:.3f} > {self.max_risk_level}")
        
        # 5. 成交量过滤
        volume_filter_result = self._check_volume_filter(data)
        if volume_filter_result:
            filter_results.append(volume_filter_result)
        
        # 6. 市场环境过滤
        market_filter_result = self._check_market_filter(data)
        if market_filter_result:
            filter_results.append(market_filter_result)
        
        # 7. 时间过滤
        time_filter_result = self._check_time_filter(signal)
        if time_filter_result:
            filter_results.append(time_filter_result)
        
        # 如果有过滤条件不满足，将信号设为HOLD
        if filter_results:
            self.filter_stats['filtered_signals'] += 1
            
            # 记录过滤原因
            for reason in filter_results:
                if reason not in self.filter_stats['filter_reasons']:
                    self.filter_stats['filter_reasons'][reason] = 0
                self.filter_stats['filter_reasons'][reason] += 1
            
            # 修改信号为HOLD
            signal.overall_direction = 0
            signal.overall_strength = 0.0
            signal.suggested_position_size = 0.0
            
            # 添加过滤信息到分析详情
            signal.analysis_details['filter_results'] = filter_results
            signal.analysis_details['filtered'] = True
        else:
            signal.analysis_details['filtered'] = False
        
        return signal
    
    def _check_volume_filter(self, data: pd.DataFrame) -> Optional[str]:
        """检查成交量过滤条件"""
        if len(data) < 10:
            return None
        
        current_volume = data['volume'].iloc[-1]
        avg_volume = data['volume'].rolling(10).mean().iloc[-1]
        
        if avg_volume > 0:
            volume_ratio = current_volume / avg_volume
            if volume_ratio < self.min_volume_ratio:
                return f"成交量过低: {volume_ratio:.3f} < {self.min_volume_ratio}"
        
        return None
    
    def _check_market_filter(self, data: pd.DataFrame) -> Optional[str]:
        """检查市场环境过滤条件"""
        if len(data) < 20:
            return None
        
        # 检查是否在涨跌停板附近
        current_price = data['close'].iloc[-1]
        prev_close = data['close'].iloc[-2] if len(data) > 1 else current_price
        
        price_change_pct = (current_price - prev_close) / prev_close if prev_close > 0 else 0
        
        # A股涨跌停限制为10%（创业板20%，但这里简化处理）
        if abs(price_change_pct) > 0.095:  # 接近涨跌停
            return f"价格接近涨跌停: {price_change_pct:.3f}"
        
        # 检查异常波动
        recent_returns = data['close'].pct_change().tail(10).dropna()
        if len(recent_returns) > 0:
            volatility = recent_returns.std()
            if volatility > 0.05:  # 波动率过高
                return f"市场波动率过高: {volatility:.3f}"
        
        return None
    
    def _check_time_filter(self, signal: MultiTimeFrameSignal) -> Optional[str]:
        """检查时间过滤条件"""
        # 检查建议持有时间是否合理
        if signal.holding_period < self.min_holding_period:
            return f"持有时间过短: {signal.holding_period} < {self.min_holding_period}"
        
        if signal.holding_period > self.max_holding_period:
            return f"持有时间过长: {signal.holding_period} > {self.max_holding_period}"
        
        # 检查当前时间是否适合交易（避免收盘前交易）
        current_time = pd.Timestamp.now().time()
        
        # A股交易时间：9:30-11:30, 13:00-15:00
        # 避免在收盘前10分钟交易
        if (current_time >= pd.Timestamp('11:20').time() and current_time <= pd.Timestamp('11:30').time()) or \
           (current_time >= pd.Timestamp('14:50').time() and current_time <= pd.Timestamp('15:00').time()):
            return "接近收盘时间，避免交易"
        
        return None
    
    def get_filter_stats(self) -> Dict:
        """获取过滤统计信息"""
        stats = self.filter_stats.copy()
        if stats['total_signals'] > 0:
            stats['filter_rate'] = stats['filtered_signals'] / stats['total_signals']
        else:
            stats['filter_rate'] = 0.0
        
        return stats


class DynamicThresholdManager:
    """动态决策阈值管理器"""
    
    def __init__(self, config: Dict = None):
        """
        初始化动态阈值管理器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        
        # 初始阈值
        self.current_threshold = DynamicThreshold(
            buy_threshold=self.config.get('initial_buy_threshold', 0.6),
            sell_threshold=self.config.get('initial_sell_threshold', 0.6),
            confidence_threshold=self.config.get('initial_confidence_threshold', 0.5)
        )
        
        # 自适应参数
        self.adaptation_rate = self.config.get('adaptation_rate', 0.05)
        self.performance_window = self.config.get('performance_window', 50)
        
        # 性能跟踪
        self.performance_history = deque(maxlen=self.performance_window)
        
        # 市场环境监控
        self.market_volatility = 1.0
        self.market_trend = 0.0  # -1 to 1
        
        # 阈值调整历史
        self.threshold_history = deque(maxlen=100)
    
    def apply_dynamic_threshold(self, signal: MultiTimeFrameSignal, 
                              data: pd.DataFrame) -> MultiTimeFrameSignal:
        """
        应用动态阈值决策
        
        Args:
            signal: 原始信号
            data: 市场数据
            
        Returns:
            应用阈值后的信号
        """
        # 更新市场环境因子
        self._update_market_factors(data)
        
        # 获取当前阈值
        current_thresholds = self._calculate_current_thresholds()
        
        # 应用阈值决策
        original_direction = signal.overall_direction
        
        if signal.overall_direction == 1:  # 买入信号
            if (signal.overall_strength < current_thresholds['buy_threshold'] or 
                signal.overall_confidence < current_thresholds['confidence_threshold']):
                signal.overall_direction = 0  # 改为持有
                signal.suggested_position_size = 0.0
        
        elif signal.overall_direction == -1:  # 卖出信号
            if (signal.overall_strength < current_thresholds['sell_threshold'] or 
                signal.overall_confidence < current_thresholds['confidence_threshold']):
                signal.overall_direction = 0  # 改为持有
                signal.suggested_position_size = 0.0
        
        # 记录阈值应用结果
        signal.analysis_details['threshold_applied'] = {
            'original_direction': original_direction,
            'final_direction': signal.overall_direction,
            'thresholds_used': current_thresholds,
            'strength': signal.overall_strength,
            'confidence': signal.overall_confidence
        }
        
        return signal
    
    def _update_market_factors(self, data: pd.DataFrame):
        """更新市场环境因子"""
        if len(data) < 20:
            return
        
        # 计算市场波动率
        returns = data['close'].pct_change().tail(20).dropna()
        if len(returns) > 0:
            self.market_volatility = returns.std() * np.sqrt(252)  # 年化波动率
        
        # 计算市场趋势
        prices = data['close'].tail(20)
        if len(prices) >= 10:
            x = np.arange(len(prices))
            slope, _ = np.polyfit(x, prices.values, 1)
            self.market_trend = np.tanh(slope / prices.mean() * 100)  # 归一化到-1到1
        
        # 更新阈值的市场环境因子
        self.current_threshold.volatility_factor = min(max(self.market_volatility / 0.3, 0.5), 2.0)
        self.current_threshold.trend_factor = 1.0 + self.market_trend * 0.2
        
        # 成交量因子
        if len(data) >= 10:
            current_volume = data['volume'].iloc[-1]
            avg_volume = data['volume'].tail(10).mean()
            if avg_volume > 0:
                self.current_threshold.volume_factor = min(max(current_volume / avg_volume, 0.5), 2.0)
    
    def _calculate_current_thresholds(self) -> Dict[str, float]:
        """计算当前动态阈值"""
        base_buy = self.current_threshold.buy_threshold
        base_sell = self.current_threshold.sell_threshold
        base_confidence = self.current_threshold.confidence_threshold
        
        # 应用市场环境调整
        volatility_adj = 1.0 / self.current_threshold.volatility_factor  # 高波动降低阈值
        trend_adj = self.current_threshold.trend_factor
        volume_adj = 1.0 / self.current_threshold.volume_factor  # 高成交量降低阈值
        
        # 应用性能调整
        performance_adj = self._calculate_performance_adjustment()
        
        # 计算调整后的阈值
        adjusted_buy = base_buy * volatility_adj * trend_adj * volume_adj * performance_adj
        adjusted_sell = base_sell * volatility_adj * (2 - trend_adj) * volume_adj * performance_adj
        adjusted_confidence = base_confidence * performance_adj
        
        # 限制阈值范围
        adjusted_buy = min(max(adjusted_buy, 0.3), 0.9)
        adjusted_sell = min(max(adjusted_sell, 0.3), 0.9)
        adjusted_confidence = min(max(adjusted_confidence, 0.2), 0.8)
        
        thresholds = {
            'buy_threshold': adjusted_buy,
            'sell_threshold': adjusted_sell,
            'confidence_threshold': adjusted_confidence
        }
        
        # 记录阈值历史
        self.threshold_history.append({
            'timestamp': pd.Timestamp.now(),
            'thresholds': thresholds,
            'market_factors': {
                'volatility': self.market_volatility,
                'trend': self.market_trend,
                'volume_factor': self.current_threshold.volume_factor
            }
        })
        
        return thresholds
    
    def _calculate_performance_adjustment(self) -> float:
        """计算基于历史表现的调整因子"""
        if len(self.performance_history) < 10:
            return 1.0
        
        # 计算近期准确率
        recent_performance = list(self.performance_history)[-20:]  # 最近20次
        correct_predictions = sum(1 for p in recent_performance if p['correct'])
        accuracy = correct_predictions / len(recent_performance)
        
        # 计算平均收益
        returns = [p['return'] for p in recent_performance]
        avg_return = np.mean(returns)
        
        # 性能调整因子
        if accuracy > 0.6 and avg_return > 0.01:
            # 表现好，降低阈值（更容易触发信号）
            performance_adj = 0.9
        elif accuracy < 0.4 or avg_return < -0.01:
            # 表现差，提高阈值（更难触发信号）
            performance_adj = 1.1
        else:
            # 表现一般，保持当前阈值
            performance_adj = 1.0
        
        return performance_adj
    
    def update_performance(self, predicted_direction: int, actual_return: float):
        """
        更新性能记录
        
        Args:
            predicted_direction: 预测方向
            actual_return: 实际收益率
        """
        # 判断预测是否正确
        actual_direction = 1 if actual_return > 0.01 else -1 if actual_return < -0.01 else 0
        correct = (predicted_direction == actual_direction)
        
        # 记录性能
        performance_record = {
            'timestamp': pd.Timestamp.now(),
            'predicted_direction': predicted_direction,
            'actual_return': actual_return,
            'correct': correct
        }
        
        self.performance_history.append(performance_record)
        
        # 更新当前阈值的性能统计
        if len(self.performance_history) >= 10:
            recent_performance = list(self.performance_history)[-10:]
            correct_count = sum(1 for p in recent_performance if p['correct'])
            self.current_threshold.recent_accuracy = correct_count / len(recent_performance)
            
            # 更新收益记录
            recent_returns = [p['actual_return'] for p in recent_performance]
            self.current_threshold.recent_returns.extend(recent_returns)
        
        # 自适应调整基础阈值
        self._adaptive_threshold_adjustment(correct, actual_return)
    
    def _adaptive_threshold_adjustment(self, correct: bool, actual_return: float):
        """自适应调整基础阈值"""
        adjustment = self.adaptation_rate
        
        if correct and actual_return > 0.02:
            # 预测正确且收益较好，略微降低阈值
            self.current_threshold.buy_threshold *= (1 - adjustment * 0.5)
            self.current_threshold.sell_threshold *= (1 - adjustment * 0.5)
        elif not correct or actual_return < -0.02:
            # 预测错误或亏损较大，提高阈值
            self.current_threshold.buy_threshold *= (1 + adjustment)
            self.current_threshold.sell_threshold *= (1 + adjustment)
        
        # 限制阈值范围
        self.current_threshold.buy_threshold = min(max(self.current_threshold.buy_threshold, 0.3), 0.9)
        self.current_threshold.sell_threshold = min(max(self.current_threshold.sell_threshold, 0.3), 0.9)
    
    def get_current_thresholds(self) -> Dict[str, float]:
        """获取当前阈值"""
        return self._calculate_current_thresholds()
    
    def get_performance_summary(self) -> Dict:
        """获取性能摘要"""
        summary = {
            'current_thresholds': {
                'buy_threshold': self.current_threshold.buy_threshold,
                'sell_threshold': self.current_threshold.sell_threshold,
                'confidence_threshold': self.current_threshold.confidence_threshold
            },
            'market_factors': {
                'volatility': self.market_volatility,
                'trend': self.market_trend,
                'volatility_factor': self.current_threshold.volatility_factor,
                'trend_factor': self.current_threshold.trend_factor,
                'volume_factor': self.current_threshold.volume_factor
            },
            'performance': {
                'recent_accuracy': self.current_threshold.recent_accuracy,
                'performance_records': len(self.performance_history)
            }
        }
        
        if len(self.performance_history) > 0:
            recent_returns = [p['actual_return'] for p in self.performance_history]
            summary['performance']['avg_return'] = np.mean(recent_returns)
            summary['performance']['return_std'] = np.std(recent_returns)
        
        return summary


def create_multi_timeframe_signal_generator(config: Dict = None) -> MultiTimeFrameSignalGenerator:
    """
    创建多时间框架信号生成器
    
    Args:
        config: 配置参数
        
    Returns:
        MultiTimeFrameSignalGenerator实例
    """
    return MultiTimeFrameSignalGenerator(config)