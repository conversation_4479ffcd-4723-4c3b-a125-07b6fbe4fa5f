#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
信号生成和决策系统测试
测试多时间框架信号生成、置信度评估、信号过滤和动态阈值调整功能
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_signal_generator.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

def create_test_data(length: int = 100) -> pd.DataFrame:
    """
    创建测试用的市场数据
    
    Args:
        length: 数据长度
        
    Returns:
        测试数据DataFrame
    """
    # 生成时间序列
    timestamps = pd.date_range(
        start=datetime.now() - timedelta(minutes=length-1),
        periods=length,
        freq='1min'
    )
    
    # 生成价格数据（随机游走 + 趋势）
    np.random.seed(42)
    base_price = 100.0
    trend = 0.001  # 轻微上涨趋势
    volatility = 0.02
    
    prices = [base_price]
    for i in range(1, length):
        change = np.random.normal(trend, volatility)
        new_price = prices[-1] * (1 + change)
        prices.append(new_price)
    
    # 生成OHLCV数据
    data = []
    for i, (timestamp, close) in enumerate(zip(timestamps, prices)):
        # 生成开高低价
        open_price = prices[i-1] if i > 0 else close
        high = max(open_price, close) * (1 + np.random.uniform(0, 0.01))
        low = min(open_price, close) * (1 - np.random.uniform(0, 0.01))
        
        # 生成成交量（与价格变化相关）
        price_change = abs(close - open_price) / open_price if open_price > 0 else 0
        base_volume = 1000000
        volume = int(base_volume * (1 + price_change * 5 + np.random.uniform(-0.3, 0.3)))
        
        data.append({
            'timestamp': timestamp,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
    
    return pd.DataFrame(data)

def test_multi_timeframe_signal_generator():
    """测试多时间框架信号生成器"""
    logger.info("开始测试多时间框架信号生成器...")
    
    try:
        from qlib_trading_system.models.intraday_trading.signal_generator import (
            create_multi_timeframe_signal_generator,
            TimeFrame,
            SignalStrength,
            ConfidenceLevel
        )
        
        # 创建测试数据
        test_data = create_test_data(200)
        logger.info(f"创建测试数据: {len(test_data)} 条记录")
        
        # 配置参数
        config = {
            'timeframe_weights': {
                TimeFrame.MINUTE_1: 0.15,
                TimeFrame.MINUTE_5: 0.25,
                TimeFrame.MINUTE_15: 0.30,
                TimeFrame.MINUTE_30: 0.20,
                TimeFrame.HOUR_1: 0.10
            },
            'technical_config': {
                'ma_periods': [5, 10, 20],
                'rsi_period': 14,
                'macd_params': (12, 26, 9)
            },
            'confidence_config': {
                'confidence_weights': {
                    'timeframe_consistency': 0.3,
                    'volume_confirmation': 0.2,
                    'trend_alignment': 0.2,
                    'technical_strength': 0.2,
                    'market_condition': 0.1
                }
            },
            'filter_config': {
                'min_strength': 0.3,
                'min_confidence': 0.4,
                'min_consensus': 0.5
            },
            'threshold_config': {
                'initial_buy_threshold': 0.6,
                'initial_sell_threshold': 0.6,
                'initial_confidence_threshold': 0.5
            }
        }
        
        # 创建信号生成器
        signal_generator = create_multi_timeframe_signal_generator(config)
        logger.info("信号生成器创建成功")
        
        # 生成信号
        symbol = "000001.SZ"
        mtf_signal = signal_generator.generate_multi_timeframe_signal(test_data, symbol)
        
        # 验证信号结果
        logger.info("=== 多时间框架信号结果 ===")
        logger.info(f"股票代码: {mtf_signal.symbol}")
        logger.info(f"生成时间: {mtf_signal.timestamp}")
        logger.info(f"综合方向: {mtf_signal.overall_direction} (1=买入, -1=卖出, 0=持有)")
        logger.info(f"综合强度: {mtf_signal.overall_strength:.3f}")
        logger.info(f"综合置信度: {mtf_signal.overall_confidence:.3f}")
        logger.info(f"时间框架一致性: {mtf_signal.timeframe_consensus:.3f}")
        logger.info(f"风险等级: {mtf_signal.risk_level:.3f}")
        logger.info(f"建议仓位大小: {mtf_signal.suggested_position_size:.3f}")
        logger.info(f"建议持有时间: {mtf_signal.holding_period} 分钟")
        
        if mtf_signal.stop_loss_price:
            logger.info(f"止损价格: {mtf_signal.stop_loss_price:.2f}")
        if mtf_signal.take_profit_price:
            logger.info(f"止盈价格: {mtf_signal.take_profit_price:.2f}")
        
        # 检查各时间框架信号
        logger.info("\n=== 各时间框架信号详情 ===")
        timeframe_signals = {
            '1分钟': mtf_signal.signals_1min,
            '5分钟': mtf_signal.signals_5min,
            '15分钟': mtf_signal.signals_15min,
            '30分钟': mtf_signal.signals_30min,
            '1小时': mtf_signal.signals_1hour
        }
        
        for tf_name, signal in timeframe_signals.items():
            if signal:
                if hasattr(signal, 'signal_type'):
                    logger.info(f"{tf_name}: {signal.signal_type.name}, 强度={signal.strength:.3f}, 置信度={signal.confidence:.3f}")
                elif hasattr(signal, 'direction'):
                    direction_name = '买入' if signal.direction == 1 else '卖出' if signal.direction == -1 else '持有'
                    logger.info(f"{tf_name}: {direction_name}, 强度={signal.strength:.3f}, 置信度={signal.confidence:.3f}")
            else:
                logger.info(f"{tf_name}: 无信号")
        
        # 检查分析详情
        if mtf_signal.analysis_details:
            logger.info("\n=== 分析详情 ===")
            for key, value in mtf_signal.analysis_details.items():
                logger.info(f"{key}: {value}")
        
        # 测试性能更新
        logger.info("\n=== 测试性能更新 ===")
        # 模拟实际收益
        actual_return = np.random.normal(0.01, 0.02)  # 模拟1%收益，2%波动
        holding_period = 30  # 持有30分钟
        
        signal_generator.update_performance(symbol, actual_return, holding_period)
        logger.info(f"更新性能: 实际收益={actual_return:.3f}, 持有时间={holding_period}分钟")
        
        # 获取性能摘要
        performance_summary = signal_generator.get_performance_summary()
        logger.info("\n=== 性能摘要 ===")
        for key, value in performance_summary.items():
            logger.info(f"{key}: {value}")
        
        logger.info("多时间框架信号生成器测试完成 ✓")
        return True
        
    except Exception as e:
        logger.error(f"多时间框架信号生成器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_confidence_evaluator():
    """测试置信度评估器"""
    logger.info("\n开始测试置信度评估器...")
    
    try:
        from qlib_trading_system.models.intraday_trading.signal_generator import (
            ConfidenceEvaluator,
            TimeFrame
        )
        from qlib_trading_system.models.intraday_trading.technical_analyzer import (
            TechnicalAnalyzer,
            SignalType
        )
        
        # 创建测试数据
        test_data = create_test_data(100)
        
        # 创建置信度评估器
        evaluator = ConfidenceEvaluator()
        
        # 创建模拟的时间框架信号
        technical_analyzer = TechnicalAnalyzer()
        
        # 生成不同时间框架的信号
        timeframe_signals = {}
        
        # 1分钟信号
        signal_1min = technical_analyzer.generate_signal(test_data.tail(20))
        timeframe_signals[TimeFrame.MINUTE_1] = signal_1min
        
        # 5分钟信号（重采样数据）
        data_5min = test_data.iloc[::5].reset_index(drop=True)  # 简化重采样
        signal_5min = technical_analyzer.generate_signal(data_5min.tail(20))
        timeframe_signals[TimeFrame.MINUTE_5] = signal_5min
        
        # 评估置信度
        confidence_assessment = evaluator.evaluate_confidence(timeframe_signals, test_data)
        
        logger.info("=== 置信度评估结果 ===")
        logger.info(f"时间框架一致性: {confidence_assessment.timeframe_consistency:.3f}")
        logger.info(f"成交量确认: {confidence_assessment.volume_confirmation:.3f}")
        logger.info(f"趋势一致性: {confidence_assessment.trend_alignment:.3f}")
        logger.info(f"技术指标强度: {confidence_assessment.technical_strength:.3f}")
        logger.info(f"综合置信度: {confidence_assessment.overall_confidence:.3f}")
        logger.info(f"置信度等级: {confidence_assessment.confidence_level.name}")
        
        logger.info("置信度评估器测试完成 ✓")
        return True
        
    except Exception as e:
        logger.error(f"置信度评估器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_signal_filter():
    """测试信号过滤器"""
    logger.info("\n开始测试信号过滤器...")
    
    try:
        from qlib_trading_system.models.intraday_trading.signal_generator import (
            SignalFilter,
            MultiTimeFrameSignal
        )
        
        # 创建测试数据
        test_data = create_test_data(50)
        
        # 创建信号过滤器
        filter_config = {
            'min_strength': 0.5,
            'min_confidence': 0.6,
            'min_consensus': 0.7,
            'max_risk_level': 0.8
        }
        signal_filter = SignalFilter(filter_config)
        
        # 创建测试信号
        test_signal = MultiTimeFrameSignal(
            symbol="000001.SZ",
            timestamp=pd.Timestamp.now()
        )
        
        # 测试不同强度的信号
        test_cases = [
            {"strength": 0.8, "confidence": 0.8, "consensus": 0.8, "risk": 0.3, "expected": "通过"},
            {"strength": 0.3, "confidence": 0.8, "consensus": 0.8, "risk": 0.3, "expected": "强度不足"},
            {"strength": 0.8, "confidence": 0.3, "consensus": 0.8, "risk": 0.3, "expected": "置信度不足"},
            {"strength": 0.8, "confidence": 0.8, "consensus": 0.3, "risk": 0.3, "expected": "一致性不足"},
            {"strength": 0.8, "confidence": 0.8, "consensus": 0.8, "risk": 0.9, "expected": "风险过高"}
        ]
        
        logger.info("=== 信号过滤测试 ===")
        for i, case in enumerate(test_cases):
            # 设置信号参数
            test_signal.overall_direction = 1  # 买入信号
            test_signal.overall_strength = case["strength"]
            test_signal.overall_confidence = case["confidence"]
            test_signal.timeframe_consensus = case["consensus"]
            test_signal.risk_level = case["risk"]
            test_signal.suggested_position_size = 0.2
            
            # 应用过滤
            filtered_signal = signal_filter.filter_signal(test_signal, test_data)
            
            # 检查结果
            is_filtered = filtered_signal.overall_direction == 0
            result = "被过滤" if is_filtered else "通过"
            
            logger.info(f"测试案例 {i+1}: 强度={case['strength']}, 置信度={case['confidence']}, "
                       f"一致性={case['consensus']}, 风险={case['risk']} -> {result}")
            
            if 'filter_results' in filtered_signal.analysis_details:
                for reason in filtered_signal.analysis_details['filter_results']:
                    logger.info(f"  过滤原因: {reason}")
        
        # 获取过滤统计
        filter_stats = signal_filter.get_filter_stats()
        logger.info(f"\n过滤统计: {filter_stats}")
        
        logger.info("信号过滤器测试完成 ✓")
        return True
        
    except Exception as e:
        logger.error(f"信号过滤器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dynamic_threshold_manager():
    """测试动态阈值管理器"""
    logger.info("\n开始测试动态阈值管理器...")
    
    try:
        from qlib_trading_system.models.intraday_trading.signal_generator import (
            DynamicThresholdManager,
            MultiTimeFrameSignal
        )
        
        # 创建测试数据
        test_data = create_test_data(100)
        
        # 创建动态阈值管理器
        threshold_config = {
            'initial_buy_threshold': 0.6,
            'initial_sell_threshold': 0.6,
            'initial_confidence_threshold': 0.5,
            'adaptation_rate': 0.1
        }
        threshold_manager = DynamicThresholdManager(threshold_config)
        
        # 创建测试信号
        test_signal = MultiTimeFrameSignal(
            symbol="000001.SZ",
            timestamp=pd.Timestamp.now()
        )
        test_signal.overall_direction = 1
        test_signal.overall_strength = 0.7
        test_signal.overall_confidence = 0.6
        test_signal.suggested_position_size = 0.2
        
        # 应用动态阈值
        logger.info("=== 动态阈值测试 ===")
        
        # 获取初始阈值
        initial_thresholds = threshold_manager.get_current_thresholds()
        logger.info(f"初始阈值: {initial_thresholds}")
        
        # 应用阈值决策
        adjusted_signal = threshold_manager.apply_dynamic_threshold(test_signal, test_data)
        
        logger.info(f"原始信号方向: {test_signal.overall_direction}")
        logger.info(f"调整后信号方向: {adjusted_signal.overall_direction}")
        logger.info(f"信号强度: {adjusted_signal.overall_strength:.3f}")
        logger.info(f"信号置信度: {adjusted_signal.overall_confidence:.3f}")
        
        if 'threshold_applied' in adjusted_signal.analysis_details:
            threshold_info = adjusted_signal.analysis_details['threshold_applied']
            logger.info(f"使用的阈值: {threshold_info['thresholds_used']}")
        
        # 模拟性能更新
        logger.info("\n=== 性能更新测试 ===")
        for i in range(10):
            # 模拟预测和实际结果
            predicted_direction = np.random.choice([-1, 0, 1])
            actual_return = np.random.normal(0.005, 0.02)
            
            threshold_manager.update_performance(predicted_direction, actual_return)
            
            if i % 3 == 0:  # 每3次显示一次阈值变化
                current_thresholds = threshold_manager.get_current_thresholds()
                logger.info(f"更新 {i+1}: 预测={predicted_direction}, 收益={actual_return:.3f}, "
                           f"买入阈值={current_thresholds['buy_threshold']:.3f}")
        
        # 获取性能摘要
        performance_summary = threshold_manager.get_performance_summary()
        logger.info(f"\n性能摘要: {performance_summary}")
        
        logger.info("动态阈值管理器测试完成 ✓")
        return True
        
    except Exception as e:
        logger.error(f"动态阈值管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integration():
    """集成测试"""
    logger.info("\n开始集成测试...")
    
    try:
        from qlib_trading_system.models.intraday_trading.signal_generator import (
            create_multi_timeframe_signal_generator
        )
        
        # 创建更长的测试数据
        test_data = create_test_data(300)
        
        # 创建信号生成器
        config = {
            'timeframe_weights': {
                'MINUTE_1': 0.15,
                'MINUTE_5': 0.25,
                'MINUTE_15': 0.30,
                'MINUTE_30': 0.20,
                'HOUR_1': 0.10
            }
        }
        
        signal_generator = create_multi_timeframe_signal_generator(config)
        
        # 模拟连续交易
        logger.info("=== 连续交易模拟 ===")
        symbols = ["000001.SZ", "000002.SZ", "600000.SH"]
        
        for i, symbol in enumerate(symbols):
            # 使用不同的数据段
            start_idx = i * 50
            end_idx = start_idx + 100
            data_segment = test_data.iloc[start_idx:end_idx].reset_index(drop=True)
            
            # 生成信号
            signal = signal_generator.generate_multi_timeframe_signal(data_segment, symbol)
            
            logger.info(f"\n股票 {symbol}:")
            logger.info(f"  信号方向: {signal.overall_direction}")
            logger.info(f"  信号强度: {signal.overall_strength:.3f}")
            logger.info(f"  置信度: {signal.overall_confidence:.3f}")
            logger.info(f"  建议仓位: {signal.suggested_position_size:.3f}")
            
            # 模拟交易结果
            if signal.overall_direction != 0:
                # 模拟持有一段时间后的收益
                simulated_return = np.random.normal(
                    0.01 * signal.overall_direction * signal.overall_strength,
                    0.02
                )
                
                # 更新性能
                signal_generator.update_performance(symbol, simulated_return, signal.holding_period)
                
                logger.info(f"  模拟收益: {simulated_return:.3f}")
        
        # 获取最终性能摘要
        final_performance = signal_generator.get_performance_summary()
        logger.info(f"\n最终性能摘要:")
        for key, value in final_performance.items():
            logger.info(f"  {key}: {value}")
        
        logger.info("集成测试完成 ✓")
        return True
        
    except Exception as e:
        logger.error(f"集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    logger.info("开始信号生成和决策系统测试")
    logger.info("=" * 60)
    
    test_results = []
    
    # 运行各项测试
    tests = [
        ("多时间框架信号生成器", test_multi_timeframe_signal_generator),
        ("置信度评估器", test_confidence_evaluator),
        ("信号过滤器", test_signal_filter),
        ("动态阈值管理器", test_dynamic_threshold_manager),
        ("集成测试", test_integration)
    ]
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            test_results.append((test_name, result))
            if result:
                logger.info(f"{test_name} 测试通过 ✓")
            else:
                logger.error(f"{test_name} 测试失败 ✗")
        except Exception as e:
            logger.error(f"{test_name} 测试异常: {e}")
            test_results.append((test_name, False))
    
    # 汇总测试结果
    logger.info("\n" + "="*60)
    logger.info("测试结果汇总:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "通过 ✓" if result else "失败 ✗"
        logger.info(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！信号生成和决策系统实现正确。")
        return True
    else:
        logger.error(f"❌ {total - passed} 项测试失败，请检查实现。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)