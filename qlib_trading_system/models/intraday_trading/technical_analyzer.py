"""
传统技术分析信号生成器
基于经典技术指标生成交易信号
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import logging

# 纯Python实现的技术指标函数
def sma(data: np.ndarray, period: int) -> np.ndarray:
    """简单移动平均"""
    return pd.Series(data).rolling(window=period).mean().values

def ema(data: np.ndarray, period: int) -> np.ndarray:
    """指数移动平均"""
    return pd.Series(data).ewm(span=period).mean().values

def rsi(data: np.ndarray, period: int = 14) -> np.ndarray:
    """相对强弱指标"""
    delta = pd.Series(data).diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    return (100 - (100 / (1 + rs))).values

def macd(data: np.ndarray, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """MACD指标"""
    ema_fast = ema(data, fast)
    ema_slow = ema(data, slow)
    macd_line = ema_fast - ema_slow
    signal_line = ema(macd_line, signal)
    histogram = macd_line - signal_line
    return macd_line, signal_line, histogram

def bollinger_bands(data: np.ndarray, period: int = 20, std_dev: float = 2) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """布林带"""
    series = pd.Series(data)
    middle = series.rolling(window=period).mean()
    std = series.rolling(window=period).std()
    upper = middle + (std * std_dev)
    lower = middle - (std * std_dev)
    return upper.values, middle.values, lower.values

def stochastic(high: np.ndarray, low: np.ndarray, close: np.ndarray, k_period: int = 14, d_period: int = 3) -> Tuple[np.ndarray, np.ndarray]:
    """随机指标KD"""
    high_series = pd.Series(high)
    low_series = pd.Series(low)
    close_series = pd.Series(close)
    
    lowest_low = low_series.rolling(window=k_period).min()
    highest_high = high_series.rolling(window=k_period).max()
    
    k = 100 * ((close_series - lowest_low) / (highest_high - lowest_low))
    d = k.rolling(window=d_period).mean()
    
    return k.values, d.values

def williams_r(high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 14) -> np.ndarray:
    """威廉指标"""
    high_series = pd.Series(high)
    low_series = pd.Series(low)
    close_series = pd.Series(close)
    
    highest_high = high_series.rolling(window=period).max()
    lowest_low = low_series.rolling(window=period).min()
    
    wr = -100 * ((highest_high - close_series) / (highest_high - lowest_low))
    return wr.values

def cci(high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 14) -> np.ndarray:
    """商品通道指标"""
    tp = (high + low + close) / 3  # 典型价格
    tp_series = pd.Series(tp)
    
    sma_tp = tp_series.rolling(window=period).mean()
    mad = tp_series.rolling(window=period).apply(lambda x: np.mean(np.abs(x - x.mean())))
    
    cci_values = (tp_series - sma_tp) / (0.015 * mad)
    return cci_values.values

def atr(high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 14) -> np.ndarray:
    """平均真实波幅"""
    high_series = pd.Series(high)
    low_series = pd.Series(low)
    close_series = pd.Series(close)
    
    tr1 = high_series - low_series
    tr2 = abs(high_series - close_series.shift(1))
    tr3 = abs(low_series - close_series.shift(1))
    
    tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    return tr.rolling(window=period).mean().values

logger = logging.getLogger(__name__)


class SignalType(Enum):
    """信号类型枚举"""
    BUY = 1
    SELL = -1
    HOLD = 0


@dataclass
class TechnicalSignal:
    """技术分析信号"""
    signal_type: SignalType
    strength: float  # 信号强度 0-1
    confidence: float  # 置信度 0-1
    indicators: Dict[str, float]  # 相关指标值
    reason: str  # 信号原因
    timestamp: pd.Timestamp


class TechnicalAnalyzer:
    """传统技术分析器"""
    
    def __init__(self, config: Dict = None):
        """
        初始化技术分析器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        
        # 指标参数配置
        self.ma_periods = self.config.get('ma_periods', [5, 10, 20, 60])
        self.ema_periods = self.config.get('ema_periods', [12, 26])
        self.rsi_period = self.config.get('rsi_period', 14)
        self.macd_params = self.config.get('macd_params', (12, 26, 9))
        self.bb_period = self.config.get('bb_period', 20)
        self.bb_std = self.config.get('bb_std', 2)
        self.kdj_params = self.config.get('kdj_params', (9, 3, 3))
        
        # 信号阈值配置
        self.rsi_overbought = self.config.get('rsi_overbought', 70)
        self.rsi_oversold = self.config.get('rsi_oversold', 30)
        self.volume_threshold = self.config.get('volume_threshold', 1.5)  # 成交量放大倍数
        
        # 权重配置
        self.indicator_weights = self.config.get('indicator_weights', {
            'ma_cross': 0.25,
            'macd': 0.20,
            'rsi': 0.15,
            'bollinger': 0.15,
            'kdj': 0.10,
            'volume': 0.10,
            'pattern': 0.05
        })
    
    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算所有技术指标
        
        Args:
            data: 包含OHLCV数据的DataFrame
            
        Returns:
            包含所有指标的DataFrame
        """
        df = data.copy()
        
        # 基础数据
        high = df['high'].values
        low = df['low'].values
        close = df['close'].values
        volume = df['volume'].values
        
        # 移动平均线
        for period in self.ma_periods:
            df[f'ma_{period}'] = sma(close, period)
            df[f'ema_{period}'] = ema(close, period)
        
        # MACD
        macd_line, macd_signal_line, macd_hist = macd(
            close, 
            fast=self.macd_params[0],
            slow=self.macd_params[1], 
            signal=self.macd_params[2]
        )
        df['macd'] = macd_line
        df['macd_signal'] = macd_signal_line
        df['macd_hist'] = macd_hist
        
        # RSI
        df['rsi'] = rsi(close, self.rsi_period)
        
        # 布林带
        bb_upper, bb_middle, bb_lower = bollinger_bands(
            close, 
            period=self.bb_period, 
            std_dev=self.bb_std
        )
        df['bb_upper'] = bb_upper
        df['bb_middle'] = bb_middle
        df['bb_lower'] = bb_lower
        df['bb_width'] = (bb_upper - bb_lower) / bb_middle
        df['bb_position'] = (close - bb_lower) / (bb_upper - bb_lower)
        
        # KDJ指标
        k, d = stochastic(
            high, low, close,
            k_period=self.kdj_params[0],
            d_period=self.kdj_params[1]
        )
        df['k'] = k
        df['d'] = d
        df['j'] = 3 * k - 2 * d
        
        # 威廉指标
        df['williams_r'] = williams_r(high, low, close, period=14)
        
        # CCI指标
        df['cci'] = cci(high, low, close, period=14)
        
        # 成交量指标
        df['volume_ma'] = sma(volume, 20)
        df['volume_ratio'] = volume / df['volume_ma']
        
        # OBV指标
        df['obv'] = self._calculate_obv(close, volume)
        
        # ATR指标
        df['atr'] = atr(high, low, close, period=14)
        
        # 价格变化率
        df['roc'] = pd.Series(close).pct_change(10).values
        
        # 动量指标
        df['momentum'] = pd.Series(close).diff(10).values
        
        return df
    
    def _calculate_obv(self, close: np.ndarray, volume: np.ndarray) -> np.ndarray:
        """计算OBV指标"""
        close_series = pd.Series(close)
        volume_series = pd.Series(volume)
        
        price_change = close_series.diff()
        obv = np.zeros(len(close))
        
        for i in range(1, len(close)):
            if price_change.iloc[i] > 0:
                obv[i] = obv[i-1] + volume_series.iloc[i]
            elif price_change.iloc[i] < 0:
                obv[i] = obv[i-1] - volume_series.iloc[i]
            else:
                obv[i] = obv[i-1]
        
        return obv
    
    def analyze_ma_signals(self, data: pd.DataFrame) -> Dict:
        """
        分析移动平均线信号
        
        Args:
            data: 包含MA指标的数据
            
        Returns:
            MA信号分析结果
        """
        signals = {}
        current_price = data['close'].iloc[-1]
        
        # 短期均线与长期均线交叉
        ma5 = data['ma_5'].iloc[-1] if 'ma_5' in data.columns else current_price
        ma10 = data['ma_10'].iloc[-1] if 'ma_10' in data.columns else current_price
        ma20 = data['ma_20'].iloc[-1] if 'ma_20' in data.columns else current_price
        ma60 = data['ma_60'].iloc[-1] if 'ma_60' in data.columns else current_price
        
        # 金叉死叉判断
        ma5_prev = data['ma_5'].iloc[-2] if len(data) > 1 and 'ma_5' in data.columns else ma5
        ma10_prev = data['ma_10'].iloc[-2] if len(data) > 1 and 'ma_10' in data.columns else ma10
        
        # 5日线与10日线交叉
        if ma5 > ma10 and ma5_prev <= ma10_prev:
            signals['ma_cross_5_10'] = {'type': 'golden_cross', 'strength': 0.6}
        elif ma5 < ma10 and ma5_prev >= ma10_prev:
            signals['ma_cross_5_10'] = {'type': 'death_cross', 'strength': 0.6}
        
        # 均线排列
        if ma5 > ma10 > ma20 > ma60:
            signals['ma_alignment'] = {'type': 'bullish', 'strength': 0.8}
        elif ma5 < ma10 < ma20 < ma60:
            signals['ma_alignment'] = {'type': 'bearish', 'strength': 0.8}
        
        # 价格与均线关系
        if current_price > ma5 > ma10:
            signals['price_ma_position'] = {'type': 'above_ma', 'strength': 0.5}
        elif current_price < ma5 < ma10:
            signals['price_ma_position'] = {'type': 'below_ma', 'strength': 0.5}
        
        return signals
    
    def analyze_macd_signals(self, data: pd.DataFrame) -> Dict:
        """
        分析MACD信号
        
        Args:
            data: 包含MACD指标的数据
            
        Returns:
            MACD信号分析结果
        """
        signals = {}
        
        macd = data['macd'].iloc[-1]
        macd_signal = data['macd_signal'].iloc[-1]
        macd_hist = data['macd_hist'].iloc[-1]
        
        macd_prev = data['macd'].iloc[-2] if len(data) > 1 else macd
        macd_signal_prev = data['macd_signal'].iloc[-2] if len(data) > 1 else macd_signal
        macd_hist_prev = data['macd_hist'].iloc[-2] if len(data) > 1 else macd_hist
        
        # MACD金叉死叉
        if macd > macd_signal and macd_prev <= macd_signal_prev:
            signals['macd_cross'] = {'type': 'golden_cross', 'strength': 0.7}
        elif macd < macd_signal and macd_prev >= macd_signal_prev:
            signals['macd_cross'] = {'type': 'death_cross', 'strength': 0.7}
        
        # MACD柱状图变化
        if macd_hist > 0 and macd_hist > macd_hist_prev:
            signals['macd_histogram'] = {'type': 'increasing', 'strength': 0.5}
        elif macd_hist < 0 and macd_hist < macd_hist_prev:
            signals['macd_histogram'] = {'type': 'decreasing', 'strength': 0.5}
        
        # MACD零轴突破
        if macd > 0 and macd_prev <= 0:
            signals['macd_zero_cross'] = {'type': 'bullish', 'strength': 0.6}
        elif macd < 0 and macd_prev >= 0:
            signals['macd_zero_cross'] = {'type': 'bearish', 'strength': 0.6}
        
        return signals
    
    def analyze_rsi_signals(self, data: pd.DataFrame) -> Dict:
        """
        分析RSI信号
        
        Args:
            data: 包含RSI指标的数据
            
        Returns:
            RSI信号分析结果
        """
        signals = {}
        
        rsi = data['rsi'].iloc[-1]
        rsi_prev = data['rsi'].iloc[-2] if len(data) > 1 else rsi
        
        # RSI超买超卖
        if rsi > self.rsi_overbought:
            signals['rsi_level'] = {'type': 'overbought', 'strength': min((rsi - 70) / 20, 1.0)}
        elif rsi < self.rsi_oversold:
            signals['rsi_level'] = {'type': 'oversold', 'strength': min((30 - rsi) / 20, 1.0)}
        
        # RSI背离（简化版）
        price_change = data['close'].iloc[-1] - data['close'].iloc[-5]
        rsi_change = rsi - data['rsi'].iloc[-5]
        
        if price_change > 0 and rsi_change < 0:
            signals['rsi_divergence'] = {'type': 'bearish', 'strength': 0.6}
        elif price_change < 0 and rsi_change > 0:
            signals['rsi_divergence'] = {'type': 'bullish', 'strength': 0.6}
        
        return signals
    
    def analyze_bollinger_signals(self, data: pd.DataFrame) -> Dict:
        """
        分析布林带信号
        
        Args:
            data: 包含布林带指标的数据
            
        Returns:
            布林带信号分析结果
        """
        signals = {}
        
        current_price = data['close'].iloc[-1]
        bb_upper = data['bb_upper'].iloc[-1]
        bb_lower = data['bb_lower'].iloc[-1]
        bb_middle = data['bb_middle'].iloc[-1]
        bb_position = data['bb_position'].iloc[-1]
        bb_width = data['bb_width'].iloc[-1]
        
        # 布林带位置
        if bb_position > 0.8:
            signals['bb_position'] = {'type': 'upper_band', 'strength': min((bb_position - 0.8) / 0.2, 1.0)}
        elif bb_position < 0.2:
            signals['bb_position'] = {'type': 'lower_band', 'strength': min((0.2 - bb_position) / 0.2, 1.0)}
        
        # 布林带收缩扩张
        bb_width_ma = data['bb_width'].rolling(10).mean().iloc[-1]
        if bb_width < bb_width_ma * 0.8:
            signals['bb_squeeze'] = {'type': 'squeeze', 'strength': 0.6}
        elif bb_width > bb_width_ma * 1.2:
            signals['bb_expansion'] = {'type': 'expansion', 'strength': 0.6}
        
        # 布林带突破
        prev_price = data['close'].iloc[-2] if len(data) > 1 else current_price
        prev_bb_upper = data['bb_upper'].iloc[-2] if len(data) > 1 else bb_upper
        prev_bb_lower = data['bb_lower'].iloc[-2] if len(data) > 1 else bb_lower
        
        if current_price > bb_upper and prev_price <= prev_bb_upper:
            signals['bb_breakout'] = {'type': 'upper_breakout', 'strength': 0.7}
        elif current_price < bb_lower and prev_price >= prev_bb_lower:
            signals['bb_breakout'] = {'type': 'lower_breakout', 'strength': 0.7}
        
        return signals
    
    def analyze_kdj_signals(self, data: pd.DataFrame) -> Dict:
        """
        分析KDJ信号
        
        Args:
            data: 包含KDJ指标的数据
            
        Returns:
            KDJ信号分析结果
        """
        signals = {}
        
        k = data['k'].iloc[-1]
        d = data['d'].iloc[-1]
        j = data['j'].iloc[-1]
        
        k_prev = data['k'].iloc[-2] if len(data) > 1 else k
        d_prev = data['d'].iloc[-2] if len(data) > 1 else d
        
        # KDJ金叉死叉
        if k > d and k_prev <= d_prev:
            signals['kdj_cross'] = {'type': 'golden_cross', 'strength': 0.6}
        elif k < d and k_prev >= d_prev:
            signals['kdj_cross'] = {'type': 'death_cross', 'strength': 0.6}
        
        # KDJ超买超卖
        if k > 80 and d > 80:
            signals['kdj_level'] = {'type': 'overbought', 'strength': min((k - 80) / 20, 1.0)}
        elif k < 20 and d < 20:
            signals['kdj_level'] = {'type': 'oversold', 'strength': min((20 - k) / 20, 1.0)}
        
        # J值极值
        if j > 100:
            signals['j_extreme'] = {'type': 'overbought', 'strength': min((j - 100) / 50, 1.0)}
        elif j < 0:
            signals['j_extreme'] = {'type': 'oversold', 'strength': min(abs(j) / 50, 1.0)}
        
        return signals
    
    def analyze_volume_signals(self, data: pd.DataFrame) -> Dict:
        """
        分析成交量信号
        
        Args:
            data: 包含成交量指标的数据
            
        Returns:
            成交量信号分析结果
        """
        signals = {}
        
        volume_ratio = data['volume_ratio'].iloc[-1]
        price_change = data['close'].pct_change().iloc[-1]
        
        # 放量上涨/下跌
        if volume_ratio > self.volume_threshold:
            if price_change > 0.01:
                signals['volume_price'] = {'type': 'volume_up', 'strength': min(volume_ratio / 3, 1.0)}
            elif price_change < -0.01:
                signals['volume_price'] = {'type': 'volume_down', 'strength': min(volume_ratio / 3, 1.0)}
        
        # 缩量整理
        elif volume_ratio < 0.5:
            signals['volume_shrink'] = {'type': 'consolidation', 'strength': 0.3}
        
        # OBV趋势
        obv_change = data['obv'].pct_change(5).iloc[-1]
        if abs(obv_change) > 0.05:
            if obv_change > 0:
                signals['obv_trend'] = {'type': 'accumulation', 'strength': min(obv_change * 10, 1.0)}
            else:
                signals['obv_trend'] = {'type': 'distribution', 'strength': min(abs(obv_change) * 10, 1.0)}
        
        return signals
    
    def analyze_pattern_signals(self, data: pd.DataFrame) -> Dict:
        """
        分析价格形态信号
        
        Args:
            data: 价格数据
            
        Returns:
            形态信号分析结果
        """
        signals = {}
        
        if len(data) < 20:
            return signals
        
        prices = data['close'].tail(20).values
        
        # 支撑阻力位突破
        recent_high = np.max(prices[-10:])
        recent_low = np.min(prices[-10:])
        current_price = prices[-1]
        
        if current_price > recent_high * 1.005:  # 突破阻力位
            signals['support_resistance'] = {'type': 'resistance_break', 'strength': 0.6}
        elif current_price < recent_low * 0.995:  # 跌破支撑位
            signals['support_resistance'] = {'type': 'support_break', 'strength': 0.6}
        
        # 价格趋势
        short_trend = np.polyfit(range(5), prices[-5:], 1)[0]
        long_trend = np.polyfit(range(10), prices[-10:], 1)[0]
        
        if short_trend > 0 and long_trend > 0:
            signals['trend'] = {'type': 'uptrend', 'strength': min(abs(short_trend) * 1000, 1.0)}
        elif short_trend < 0 and long_trend < 0:
            signals['trend'] = {'type': 'downtrend', 'strength': min(abs(short_trend) * 1000, 1.0)}
        
        return signals
    
    def generate_signal(self, data: pd.DataFrame) -> TechnicalSignal:
        """
        生成综合技术分析信号
        
        Args:
            data: 包含OHLCV数据的DataFrame
            
        Returns:
            综合技术信号
        """
        # 计算所有技术指标
        data_with_indicators = self.calculate_indicators(data)
        
        # 分析各类信号
        ma_signals = self.analyze_ma_signals(data_with_indicators)
        macd_signals = self.analyze_macd_signals(data_with_indicators)
        rsi_signals = self.analyze_rsi_signals(data_with_indicators)
        bb_signals = self.analyze_bollinger_signals(data_with_indicators)
        kdj_signals = self.analyze_kdj_signals(data_with_indicators)
        volume_signals = self.analyze_volume_signals(data_with_indicators)
        pattern_signals = self.analyze_pattern_signals(data_with_indicators)
        
        # 收集所有信号
        all_signals = {
            'ma': ma_signals,
            'macd': macd_signals,
            'rsi': rsi_signals,
            'bollinger': bb_signals,
            'kdj': kdj_signals,
            'volume': volume_signals,
            'pattern': pattern_signals
        }
        
        # 计算综合信号
        bullish_score = 0.0
        bearish_score = 0.0
        total_weight = 0.0
        signal_details = {}
        
        for category, signals in all_signals.items():
            category_weight = self.indicator_weights.get(category, 0.1)
            
            for signal_name, signal_info in signals.items():
                signal_type = signal_info['type']
                strength = signal_info['strength']
                
                # 根据信号类型累加分数
                if signal_type in ['golden_cross', 'bullish', 'above_ma', 'increasing', 
                                 'oversold', 'lower_band', 'volume_up', 'accumulation',
                                 'resistance_break', 'uptrend']:
                    bullish_score += strength * category_weight
                elif signal_type in ['death_cross', 'bearish', 'below_ma', 'decreasing',
                                   'overbought', 'upper_band', 'volume_down', 'distribution',
                                   'support_break', 'downtrend']:
                    bearish_score += strength * category_weight
                
                total_weight += category_weight
                signal_details[f"{category}_{signal_name}"] = signal_info
        
        # 归一化分数
        if total_weight > 0:
            bullish_score /= total_weight
            bearish_score /= total_weight
        
        # 确定最终信号
        net_score = bullish_score - bearish_score
        
        if net_score > 0.3:
            signal_type = SignalType.BUY
            strength = min(net_score, 1.0)
            confidence = min(bullish_score * 2, 1.0)
            reason = f"多头信号占优，净分数: {net_score:.3f}"
        elif net_score < -0.3:
            signal_type = SignalType.SELL
            strength = min(abs(net_score), 1.0)
            confidence = min(bearish_score * 2, 1.0)
            reason = f"空头信号占优，净分数: {net_score:.3f}"
        else:
            signal_type = SignalType.HOLD
            strength = 0.0
            confidence = 0.5
            reason = f"信号不明确，净分数: {net_score:.3f}"
        
        # 获取当前指标值
        current_indicators = {}
        if len(data_with_indicators) > 0:
            last_row = data_with_indicators.iloc[-1]
            current_indicators = {
                'rsi': last_row.get('rsi', 0),
                'macd': last_row.get('macd', 0),
                'bb_position': last_row.get('bb_position', 0),
                'k': last_row.get('k', 0),
                'd': last_row.get('d', 0),
                'volume_ratio': last_row.get('volume_ratio', 1)
            }
        
        return TechnicalSignal(
            signal_type=signal_type,
            strength=strength,
            confidence=confidence,
            indicators=current_indicators,
            reason=reason,
            timestamp=pd.Timestamp.now()
        )
    
    def get_support_resistance_levels(self, data: pd.DataFrame, window: int = 20) -> Dict:
        """
        计算支撑阻力位
        
        Args:
            data: 价格数据
            window: 计算窗口
            
        Returns:
            支撑阻力位信息
        """
        if len(data) < window:
            return {'support': None, 'resistance': None}
        
        recent_data = data.tail(window)
        
        # 计算支撑阻力位
        support_level = recent_data['low'].min()
        resistance_level = recent_data['high'].max()
        
        # 计算强度（基于触及次数）
        support_touches = sum(abs(recent_data['low'] - support_level) < support_level * 0.01)
        resistance_touches = sum(abs(recent_data['high'] - resistance_level) < resistance_level * 0.01)
        
        return {
            'support': {
                'level': support_level,
                'strength': min(support_touches / 5, 1.0)
            },
            'resistance': {
                'level': resistance_level,
                'strength': min(resistance_touches / 5, 1.0)
            }
        }
    
    def calculate_trend_strength(self, data: pd.DataFrame, period: int = 20) -> Dict:
        """
        计算趋势强度
        
        Args:
            data: 价格数据
            period: 计算周期
            
        Returns:
            趋势强度信息
        """
        if len(data) < period:
            return {'trend': 'unknown', 'strength': 0}
        
        prices = data['close'].tail(period).values
        
        # 线性回归计算趋势
        x = np.arange(len(prices))
        slope, intercept = np.polyfit(x, prices, 1)
        
        # 计算R²
        y_pred = slope * x + intercept
        ss_res = np.sum((prices - y_pred) ** 2)
        ss_tot = np.sum((prices - np.mean(prices)) ** 2)
        r_squared = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0
        
        # 确定趋势方向和强度
        if slope > 0:
            trend = 'uptrend'
        elif slope < 0:
            trend = 'downtrend'
        else:
            trend = 'sideways'
        
        strength = min(abs(slope) / np.mean(prices) * 100 * r_squared, 1.0)
        
        return {
            'trend': trend,
            'strength': strength,
            'slope': slope,
            'r_squared': r_squared
        }


def create_technical_analyzer(config: Dict = None) -> TechnicalAnalyzer:
    """
    创建技术分析器
    
    Args:
        config: 配置参数
        
    Returns:
        TechnicalAnalyzer实例
    """
    return TechnicalAnalyzer(config)