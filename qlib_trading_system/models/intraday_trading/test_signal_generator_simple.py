#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
信号生成和决策系统简化测试
不依赖PyTorch，仅测试核心功能
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_signal_generator_simple.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

def create_test_data(length: int = 100) -> pd.DataFrame:
    """创建测试用的市场数据"""
    timestamps = pd.date_range(
        start=datetime.now() - timedelta(minutes=length-1),
        periods=length,
        freq='1min'
    )
    
    np.random.seed(42)
    base_price = 100.0
    trend = 0.001
    volatility = 0.02
    
    prices = [base_price]
    for i in range(1, length):
        change = np.random.normal(trend, volatility)
        new_price = prices[-1] * (1 + change)
        prices.append(new_price)
    
    data = []
    for i, (timestamp, close) in enumerate(zip(timestamps, prices)):
        open_price = prices[i-1] if i > 0 else close
        high = max(open_price, close) * (1 + np.random.uniform(0, 0.01))
        low = min(open_price, close) * (1 - np.random.uniform(0, 0.01))
        
        price_change = abs(close - open_price) / open_price if open_price > 0 else 0
        base_volume = 1000000
        volume = int(base_volume * (1 + price_change * 5 + np.random.uniform(-0.3, 0.3)))
        
        data.append({
            'timestamp': timestamp,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
    
    return pd.DataFrame(data)

def test_technical_analyzer():
    """测试技术分析器（不依赖PyTorch）"""
    logger.info("开始测试技术分析器...")
    
    try:
        from qlib_trading_system.models.intraday_trading.technical_analyzer import (
            TechnicalAnalyzer,
            SignalType
        )
        
        # 创建测试数据
        test_data = create_test_data(100)
        logger.info(f"创建测试数据: {len(test_data)} 条记录")
        
        # 创建技术分析器
        config = {
            'ma_periods': [5, 10, 20],
            'rsi_period': 14,
            'macd_params': (12, 26, 9)
        }
        analyzer = TechnicalAnalyzer(config)
        
        # 生成信号
        signal = analyzer.generate_signal(test_data)
        
        logger.info("=== 技术分析信号结果 ===")
        logger.info(f"信号类型: {signal.signal_type.name}")
        logger.info(f"信号强度: {signal.strength:.3f}")
        logger.info(f"置信度: {signal.confidence:.3f}")
        logger.info(f"生成原因: {signal.reason}")
        logger.info(f"时间戳: {signal.timestamp}")
        
        # 检查指标值
        logger.info("\n=== 技术指标值 ===")
        for indicator, value in signal.indicators.items():
            logger.info(f"{indicator}: {value:.3f}")
        
        # 测试支撑阻力位计算
        sr_levels = analyzer.get_support_resistance_levels(test_data)
        logger.info(f"\n=== 支撑阻力位 ===")
        if sr_levels['support']:
            logger.info(f"支撑位: {sr_levels['support']['level']:.2f}, 强度: {sr_levels['support']['strength']:.3f}")
        if sr_levels['resistance']:
            logger.info(f"阻力位: {sr_levels['resistance']['level']:.2f}, 强度: {sr_levels['resistance']['strength']:.3f}")
        
        # 测试趋势强度计算
        trend_info = analyzer.calculate_trend_strength(test_data)
        logger.info(f"\n=== 趋势分析 ===")
        logger.info(f"趋势方向: {trend_info['trend']}")
        logger.info(f"趋势强度: {trend_info['strength']:.3f}")
        logger.info(f"R²: {trend_info['r_squared']:.3f}")
        
        logger.info("技术分析器测试完成 ✓")
        return True
        
    except Exception as e:
        logger.error(f"技术分析器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_timeframe_resampling():
    """测试时间框架重采样功能"""
    logger.info("\n开始测试时间框架重采样...")
    
    try:
        from qlib_trading_system.models.intraday_trading.signal_generator import (
            TimeFrame
        )
        
        # 创建测试数据
        test_data = create_test_data(100)
        
        # 模拟重采样功能
        def resample_data(data: pd.DataFrame, timeframe: TimeFrame) -> pd.DataFrame:
            """简化的重采样函数"""
            if timeframe == TimeFrame.MINUTE_1:
                return data
            
            # 设置时间索引
            if 'timestamp' in data.columns:
                data = data.set_index('timestamp')
            elif not isinstance(data.index, pd.DatetimeIndex):
                data.index = pd.date_range(
                    start=pd.Timestamp.now() - pd.Timedelta(minutes=len(data)-1),
                    periods=len(data),
                    freq='1min'
                )
            
            # 重采样规则
            resample_rules = {
                TimeFrame.MINUTE_5: '5min',
                TimeFrame.MINUTE_15: '15min', 
                TimeFrame.MINUTE_30: '30min',
                TimeFrame.HOUR_1: '1H'
            }
            
            rule = resample_rules.get(timeframe, '5min')
            
            # 执行重采样
            resampled = data.resample(rule).agg({
                'open': 'first',
                'high': 'max',
                'low': 'min',
                'close': 'last',
                'volume': 'sum'
            }).dropna()
            
            return resampled.reset_index()
        
        # 测试不同时间框架
        timeframes = [
            TimeFrame.MINUTE_1,
            TimeFrame.MINUTE_5,
            TimeFrame.MINUTE_15,
            TimeFrame.MINUTE_30,
            TimeFrame.HOUR_1
        ]
        
        logger.info("=== 时间框架重采样测试 ===")
        for tf in timeframes:
            resampled = resample_data(test_data, tf)
            logger.info(f"{tf.value}: 原始{len(test_data)}条 -> 重采样{len(resampled)}条")
            
            if len(resampled) > 0:
                logger.info(f"  最新价格: {resampled['close'].iloc[-1]:.2f}")
                logger.info(f"  最新成交量: {resampled['volume'].iloc[-1]:,}")
        
        logger.info("时间框架重采样测试完成 ✓")
        return True
        
    except Exception as e:
        logger.error(f"时间框架重采样测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_confidence_calculation():
    """测试置信度计算逻辑"""
    logger.info("\n开始测试置信度计算...")
    
    try:
        # 模拟置信度计算
        def calculate_timeframe_consistency(signals):
            """计算时间框架一致性"""
            if len(signals) < 2:
                return 0.5
            
            directions = []
            for signal in signals:
                if hasattr(signal, 'signal_type'):
                    if signal.signal_type.name == 'BUY':
                        directions.append(1)
                    elif signal.signal_type.name == 'SELL':
                        directions.append(-1)
                    else:
                        directions.append(0)
            
            if not directions:
                return 0.5
            
            # 统计各方向的数量
            buy_count = directions.count(1)
            sell_count = directions.count(-1)
            hold_count = directions.count(0)
            
            total_count = len(directions)
            max_count = max(buy_count, sell_count, hold_count)
            
            return max_count / total_count
        
        def calculate_volume_confirmation(data):
            """计算成交量确认"""
            if len(data) < 20:
                return 0.5
            
            current_volume = data['volume'].iloc[-1]
            volume_ma = data['volume'].rolling(20).mean().iloc[-1]
            volume_ratio = current_volume / volume_ma if volume_ma > 0 else 1.0
            
            price_change = data['close'].pct_change().iloc[-1]
            
            volume_score = 0.5
            if abs(price_change) > 0.01:
                if volume_ratio > 1.5:
                    volume_score = min(0.8 + (volume_ratio - 1.5) * 0.1, 1.0)
                elif volume_ratio > 1.2:
                    volume_score = 0.7
                elif volume_ratio < 0.8:
                    volume_score = 0.3
            
            return min(max(volume_score, 0.0), 1.0)
        
        # 创建测试数据
        test_data = create_test_data(50)
        
        # 创建模拟信号
        from qlib_trading_system.models.intraday_trading.technical_analyzer import (
            TechnicalAnalyzer
        )
        
        analyzer = TechnicalAnalyzer()
        
        # 生成多个信号模拟不同时间框架
        signals = []
        for i in range(3):
            data_segment = test_data.iloc[i*10:(i+1)*20].reset_index(drop=True)
            if len(data_segment) >= 20:
                signal = analyzer.generate_signal(data_segment)
                signals.append(signal)
        
        # 计算置信度指标
        logger.info("=== 置信度计算测试 ===")
        
        timeframe_consistency = calculate_timeframe_consistency(signals)
        logger.info(f"时间框架一致性: {timeframe_consistency:.3f}")
        
        volume_confirmation = calculate_volume_confirmation(test_data)
        logger.info(f"成交量确认: {volume_confirmation:.3f}")
        
        # 综合置信度
        weights = {
            'timeframe_consistency': 0.4,
            'volume_confirmation': 0.3,
            'technical_strength': 0.3
        }
        
        technical_strength = np.mean([s.strength for s in signals]) if signals else 0.5
        
        overall_confidence = (
            timeframe_consistency * weights['timeframe_consistency'] +
            volume_confirmation * weights['volume_confirmation'] +
            technical_strength * weights['technical_strength']
        )
        
        logger.info(f"技术指标强度: {technical_strength:.3f}")
        logger.info(f"综合置信度: {overall_confidence:.3f}")
        
        # 置信度等级
        if overall_confidence >= 0.8:
            confidence_level = "非常高"
        elif overall_confidence >= 0.65:
            confidence_level = "高"
        elif overall_confidence >= 0.5:
            confidence_level = "中等"
        elif overall_confidence >= 0.35:
            confidence_level = "低"
        else:
            confidence_level = "非常低"
        
        logger.info(f"置信度等级: {confidence_level}")
        
        logger.info("置信度计算测试完成 ✓")
        return True
        
    except Exception as e:
        logger.error(f"置信度计算测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_signal_filtering():
    """测试信号过滤逻辑"""
    logger.info("\n开始测试信号过滤...")
    
    try:
        # 模拟信号过滤
        def filter_signal(signal_strength, confidence, consensus, risk_level, 
                         min_strength=0.5, min_confidence=0.6, min_consensus=0.7, max_risk=0.8):
            """信号过滤函数"""
            filter_results = []
            
            if signal_strength < min_strength:
                filter_results.append(f"信号强度不足: {signal_strength:.3f} < {min_strength}")
            
            if confidence < min_confidence:
                filter_results.append(f"置信度不足: {confidence:.3f} < {min_confidence}")
            
            if consensus < min_consensus:
                filter_results.append(f"一致性不足: {consensus:.3f} < {min_consensus}")
            
            if risk_level > max_risk:
                filter_results.append(f"风险过高: {risk_level:.3f} > {max_risk}")
            
            return len(filter_results) == 0, filter_results
        
        # 测试不同的信号参数
        test_cases = [
            {"strength": 0.8, "confidence": 0.8, "consensus": 0.8, "risk": 0.3, "expected": True},
            {"strength": 0.3, "confidence": 0.8, "consensus": 0.8, "risk": 0.3, "expected": False},
            {"strength": 0.8, "confidence": 0.3, "consensus": 0.8, "risk": 0.3, "expected": False},
            {"strength": 0.8, "confidence": 0.8, "consensus": 0.3, "risk": 0.3, "expected": False},
            {"strength": 0.8, "confidence": 0.8, "consensus": 0.8, "risk": 0.9, "expected": False}
        ]
        
        logger.info("=== 信号过滤测试 ===")
        passed_count = 0
        
        for i, case in enumerate(test_cases):
            passed, reasons = filter_signal(
                case["strength"], case["confidence"], 
                case["consensus"], case["risk"]
            )
            
            result = "通过" if passed else "被过滤"
            expected = "通过" if case["expected"] else "被过滤"
            
            logger.info(f"测试案例 {i+1}: 强度={case['strength']}, 置信度={case['confidence']}, "
                       f"一致性={case['consensus']}, 风险={case['risk']} -> {result}")
            
            if not passed:
                for reason in reasons:
                    logger.info(f"  过滤原因: {reason}")
            
            if passed == case["expected"]:
                passed_count += 1
            else:
                logger.warning(f"  预期: {expected}, 实际: {result}")
        
        logger.info(f"\n过滤测试结果: {passed_count}/{len(test_cases)} 通过")
        
        logger.info("信号过滤测试完成 ✓")
        return passed_count == len(test_cases)
        
    except Exception as e:
        logger.error(f"信号过滤测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dynamic_threshold():
    """测试动态阈值调整"""
    logger.info("\n开始测试动态阈值调整...")
    
    try:
        # 模拟动态阈值管理
        class SimpleDynamicThreshold:
            def __init__(self):
                self.buy_threshold = 0.6
                self.sell_threshold = 0.6
                self.confidence_threshold = 0.5
                self.performance_history = []
                self.adaptation_rate = 0.05
            
            def update_performance(self, predicted_direction, actual_return):
                """更新性能记录"""
                actual_direction = 1 if actual_return > 0.01 else -1 if actual_return < -0.01 else 0
                correct = (predicted_direction == actual_direction)
                
                self.performance_history.append({
                    'predicted': predicted_direction,
                    'actual_return': actual_return,
                    'correct': correct
                })
                
                # 保持最近50条记录
                if len(self.performance_history) > 50:
                    self.performance_history = self.performance_history[-50:]
                
                # 自适应调整
                self._adaptive_adjustment(correct, actual_return)
            
            def _adaptive_adjustment(self, correct, actual_return):
                """自适应调整阈值"""
                adjustment = self.adaptation_rate
                
                if correct and actual_return > 0.02:
                    # 预测正确且收益好，降低阈值
                    self.buy_threshold *= (1 - adjustment * 0.5)
                    self.sell_threshold *= (1 - adjustment * 0.5)
                elif not correct or actual_return < -0.02:
                    # 预测错误或亏损大，提高阈值
                    self.buy_threshold *= (1 + adjustment)
                    self.sell_threshold *= (1 + adjustment)
                
                # 限制范围
                self.buy_threshold = min(max(self.buy_threshold, 0.3), 0.9)
                self.sell_threshold = min(max(self.sell_threshold, 0.3), 0.9)
            
            def get_accuracy(self):
                """获取准确率"""
                if len(self.performance_history) < 5:
                    return 0.5
                
                correct_count = sum(1 for p in self.performance_history if p['correct'])
                return correct_count / len(self.performance_history)
            
            def get_avg_return(self):
                """获取平均收益"""
                if not self.performance_history:
                    return 0.0
                
                returns = [p['actual_return'] for p in self.performance_history]
                return np.mean(returns)
        
        # 创建动态阈值管理器
        threshold_manager = SimpleDynamicThreshold()
        
        logger.info("=== 动态阈值调整测试 ===")
        logger.info(f"初始买入阈值: {threshold_manager.buy_threshold:.3f}")
        logger.info(f"初始卖出阈值: {threshold_manager.sell_threshold:.3f}")
        
        # 模拟交易序列
        np.random.seed(123)
        for i in range(20):
            # 随机预测方向
            predicted_direction = np.random.choice([-1, 0, 1])
            
            # 模拟实际收益（有一定的预测准确性）
            if predicted_direction == 1:
                actual_return = np.random.normal(0.015, 0.02)  # 买入信号略微正收益
            elif predicted_direction == -1:
                actual_return = np.random.normal(-0.01, 0.02)  # 卖出信号略微负收益
            else:
                actual_return = np.random.normal(0.005, 0.015)  # 持有信号小幅波动
            
            # 更新性能
            threshold_manager.update_performance(predicted_direction, actual_return)
            
            # 每5次显示一次状态
            if (i + 1) % 5 == 0:
                accuracy = threshold_manager.get_accuracy()
                avg_return = threshold_manager.get_avg_return()
                
                logger.info(f"第{i+1}次更新:")
                logger.info(f"  预测方向: {predicted_direction}, 实际收益: {actual_return:.3f}")
                logger.info(f"  当前准确率: {accuracy:.3f}")
                logger.info(f"  平均收益: {avg_return:.3f}")
                logger.info(f"  买入阈值: {threshold_manager.buy_threshold:.3f}")
                logger.info(f"  卖出阈值: {threshold_manager.sell_threshold:.3f}")
        
        # 最终统计
        final_accuracy = threshold_manager.get_accuracy()
        final_avg_return = threshold_manager.get_avg_return()
        
        logger.info(f"\n最终统计:")
        logger.info(f"  最终准确率: {final_accuracy:.3f}")
        logger.info(f"  最终平均收益: {final_avg_return:.3f}")
        logger.info(f"  最终买入阈值: {threshold_manager.buy_threshold:.3f}")
        logger.info(f"  最终卖出阈值: {threshold_manager.sell_threshold:.3f}")
        
        logger.info("动态阈值调整测试完成 ✓")
        return True
        
    except Exception as e:
        logger.error(f"动态阈值调整测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    logger.info("开始信号生成和决策系统简化测试")
    logger.info("=" * 60)
    
    test_results = []
    
    # 运行各项测试
    tests = [
        ("技术分析器", test_technical_analyzer),
        ("时间框架重采样", test_timeframe_resampling),
        ("置信度计算", test_confidence_calculation),
        ("信号过滤", test_signal_filtering),
        ("动态阈值调整", test_dynamic_threshold)
    ]
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            test_results.append((test_name, result))
            if result:
                logger.info(f"{test_name} 测试通过 ✓")
            else:
                logger.error(f"{test_name} 测试失败 ✗")
        except Exception as e:
            logger.error(f"{test_name} 测试异常: {e}")
            test_results.append((test_name, False))
    
    # 汇总测试结果
    logger.info("\n" + "="*60)
    logger.info("测试结果汇总:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "通过 ✓" if result else "失败 ✗"
        logger.info(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！信号生成和决策系统核心功能实现正确。")
        return True
    else:
        logger.error(f"❌ {total - passed} 项测试失败，请检查实现。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)