"""
Transformer时序预测模型
用于预测股票价格的时序变化趋势
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
import math
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)


@dataclass
class TransformerConfig:
    """Transformer模型配置"""
    d_model: int = 128  # 模型维度
    nhead: int = 8      # 注意力头数
    num_layers: int = 6  # Transformer层数
    dim_feedforward: int = 512  # 前馈网络维度
    dropout: float = 0.1  # Dropout率
    seq_length: int = 60  # 序列长度（60个时间点）
    feature_dim: int = 20  # 输入特征维度
    output_dim: int = 3   # 输出维度（涨跌幅预测）
    max_position_encoding: int = 1000  # 最大位置编码


class PositionalEncoding(nn.Module):
    """位置编码模块"""
    
    def __init__(self, d_model: int, max_len: int = 1000):
        super().__init__()
        self.dropout = nn.Dropout(p=0.1)
        
        # 创建位置编码矩阵
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        
        # 计算位置编码
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-math.log(10000.0) / d_model))
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """前向传播"""
        x = x + self.pe[:x.size(0), :]
        return self.dropout(x)


class MultiHeadAttention(nn.Module):
    """多头注意力机制"""
    
    def __init__(self, d_model: int, nhead: int, dropout: float = 0.1):
        super().__init__()
        self.d_model = d_model
        self.nhead = nhead
        self.d_k = d_model // nhead
        
        self.w_q = nn.Linear(d_model, d_model)
        self.w_k = nn.Linear(d_model, d_model)
        self.w_v = nn.Linear(d_model, d_model)
        self.w_o = nn.Linear(d_model, d_model)
        
        self.dropout = nn.Dropout(dropout)
        self.layer_norm = nn.LayerNorm(d_model)
        
    def forward(self, query: torch.Tensor, key: torch.Tensor, 
                value: torch.Tensor, mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """前向传播"""
        batch_size, seq_len = query.size(0), query.size(1)
        residual = query
        
        # 线性变换
        Q = self.w_q(query).view(batch_size, seq_len, self.nhead, self.d_k).transpose(1, 2)
        K = self.w_k(key).view(batch_size, seq_len, self.nhead, self.d_k).transpose(1, 2)
        V = self.w_v(value).view(batch_size, seq_len, self.nhead, self.d_k).transpose(1, 2)
        
        # 计算注意力分数
        scores = torch.matmul(Q, K.transpose(-2, -1)) / math.sqrt(self.d_k)
        
        if mask is not None:
            scores = scores.masked_fill(mask == 0, -1e9)
        
        attention_weights = F.softmax(scores, dim=-1)
        attention_weights = self.dropout(attention_weights)
        
        # 应用注意力权重
        context = torch.matmul(attention_weights, V)
        context = context.transpose(1, 2).contiguous().view(
            batch_size, seq_len, self.d_model)
        
        # 输出投影
        output = self.w_o(context)
        
        # 残差连接和层归一化
        output = self.layer_norm(output + residual)
        
        return output


class TransformerBlock(nn.Module):
    """Transformer块"""
    
    def __init__(self, config: TransformerConfig):
        super().__init__()
        self.attention = MultiHeadAttention(
            config.d_model, config.nhead, config.dropout)
        
        # 前馈网络
        self.feed_forward = nn.Sequential(
            nn.Linear(config.d_model, config.dim_feedforward),
            nn.ReLU(),
            nn.Dropout(config.dropout),
            nn.Linear(config.dim_feedforward, config.d_model),
            nn.Dropout(config.dropout)
        )
        
        self.layer_norm = nn.LayerNorm(config.d_model)
        
    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """前向传播"""
        # 自注意力
        attn_output = self.attention(x, x, x, mask)
        
        # 前馈网络
        residual = attn_output
        ff_output = self.feed_forward(attn_output)
        output = self.layer_norm(ff_output + residual)
        
        return output


class TransformerTimeSeriesModel(nn.Module):
    """Transformer时序预测模型"""
    
    def __init__(self, config: TransformerConfig):
        super().__init__()
        self.config = config
        
        # 输入投影层
        self.input_projection = nn.Linear(config.feature_dim, config.d_model)
        
        # 位置编码
        self.pos_encoding = PositionalEncoding(config.d_model, config.max_position_encoding)
        
        # Transformer层
        self.transformer_blocks = nn.ModuleList([
            TransformerBlock(config) for _ in range(config.num_layers)
        ])
        
        # 输出层
        self.output_projection = nn.Sequential(
            nn.Linear(config.d_model, config.d_model // 2),
            nn.ReLU(),
            nn.Dropout(config.dropout),
            nn.Linear(config.d_model // 2, config.output_dim)
        )
        
        # 初始化权重
        self._init_weights()
        
    def _init_weights(self):
        """初始化模型权重"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
    
    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 输入张量 [batch_size, seq_len, feature_dim]
            mask: 注意力掩码 [batch_size, seq_len, seq_len]
            
        Returns:
            预测结果 [batch_size, output_dim]
        """
        # 输入投影
        x = self.input_projection(x)  # [batch_size, seq_len, d_model]
        
        # 位置编码
        x = x.transpose(0, 1)  # [seq_len, batch_size, d_model]
        x = self.pos_encoding(x)
        x = x.transpose(0, 1)  # [batch_size, seq_len, d_model]
        
        # Transformer层
        for transformer_block in self.transformer_blocks:
            x = transformer_block(x, mask)
        
        # 全局平均池化
        x = torch.mean(x, dim=1)  # [batch_size, d_model]
        
        # 输出投影
        output = self.output_projection(x)  # [batch_size, output_dim]
        
        return output


@dataclass
class PredictionResult:
    """预测结果"""
    direction: int  # 方向：1上涨，-1下跌，0震荡
    strength: float  # 强度：0-1
    confidence: float  # 置信度：0-1
    expected_return: float  # 预期收益率
    timeframe: int  # 预测时间框架（分钟）
    timestamp: pd.Timestamp  # 预测时间戳


class TransformerPredictor:
    """Transformer预测器"""
    
    def __init__(self, config: TransformerConfig, device: str = 'cpu'):
        self.config = config
        self.device = torch.device(device)
        self.model = TransformerTimeSeriesModel(config).to(self.device)
        self.is_trained = False
        
        # 数据预处理参数
        self.feature_scaler = None
        self.target_scaler = None
        
    def prepare_data(self, data: pd.DataFrame, target_col: str = 'return_1min') -> Tuple[torch.Tensor, torch.Tensor]:
        """
        准备训练数据
        
        Args:
            data: 包含特征和目标的数据框
            target_col: 目标列名
            
        Returns:
            特征张量和目标张量
        """
        # 提取特征列
        feature_cols = [col for col in data.columns if col != target_col and not col.startswith('target_')]
        
        # 创建序列数据
        sequences = []
        targets = []
        
        for i in range(self.config.seq_length, len(data)):
            # 获取序列特征
            seq_features = data[feature_cols].iloc[i-self.config.seq_length:i].values
            sequences.append(seq_features)
            
            # 获取目标值（未来1分钟、5分钟、15分钟收益率）
            target_values = [
                data[target_col].iloc[i],
                data.get('return_5min', data[target_col]).iloc[i] if 'return_5min' in data.columns else data[target_col].iloc[i],
                data.get('return_15min', data[target_col]).iloc[i] if 'return_15min' in data.columns else data[target_col].iloc[i]
            ]
            targets.append(target_values)
        
        # 转换为张量
        X = torch.FloatTensor(np.array(sequences))
        y = torch.FloatTensor(np.array(targets))
        
        return X, y
    
    def train(self, train_data: pd.DataFrame, val_data: pd.DataFrame = None, 
              epochs: int = 100, batch_size: int = 32, learning_rate: float = 0.001):
        """
        训练模型
        
        Args:
            train_data: 训练数据
            val_data: 验证数据
            epochs: 训练轮数
            batch_size: 批次大小
            learning_rate: 学习率
        """
        logger.info("开始训练Transformer时序预测模型")
        
        # 准备数据
        X_train, y_train = self.prepare_data(train_data)
        
        if val_data is not None:
            X_val, y_val = self.prepare_data(val_data)
        
        # 创建数据加载器
        train_dataset = torch.utils.data.TensorDataset(X_train, y_train)
        train_loader = torch.utils.data.DataLoader(
            train_dataset, batch_size=batch_size, shuffle=True)
        
        # 优化器和损失函数
        optimizer = torch.optim.Adam(self.model.parameters(), lr=learning_rate)
        criterion = nn.MSELoss()
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, mode='min', factor=0.5, patience=10)
        
        # 训练循环
        best_val_loss = float('inf')
        patience_counter = 0
        
        for epoch in range(epochs):
            # 训练阶段
            self.model.train()
            train_loss = 0.0
            
            for batch_X, batch_y in train_loader:
                batch_X = batch_X.to(self.device)
                batch_y = batch_y.to(self.device)
                
                optimizer.zero_grad()
                outputs = self.model(batch_X)
                loss = criterion(outputs, batch_y)
                loss.backward()
                
                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                
                optimizer.step()
                train_loss += loss.item()
            
            avg_train_loss = train_loss / len(train_loader)
            
            # 验证阶段
            if val_data is not None:
                self.model.eval()
                val_loss = 0.0
                
                with torch.no_grad():
                    val_dataset = torch.utils.data.TensorDataset(X_val, y_val)
                    val_loader = torch.utils.data.DataLoader(
                        val_dataset, batch_size=batch_size, shuffle=False)
                    
                    for batch_X, batch_y in val_loader:
                        batch_X = batch_X.to(self.device)
                        batch_y = batch_y.to(self.device)
                        
                        outputs = self.model(batch_X)
                        loss = criterion(outputs, batch_y)
                        val_loss += loss.item()
                
                avg_val_loss = val_loss / len(val_loader)
                scheduler.step(avg_val_loss)
                
                # 早停检查
                if avg_val_loss < best_val_loss:
                    best_val_loss = avg_val_loss
                    patience_counter = 0
                    # 保存最佳模型
                    torch.save(self.model.state_dict(), 'best_transformer_model.pth')
                else:
                    patience_counter += 1
                
                if epoch % 10 == 0:
                    logger.info(f"Epoch {epoch}: Train Loss: {avg_train_loss:.6f}, Val Loss: {avg_val_loss:.6f}")
                
                # 早停
                if patience_counter >= 20:
                    logger.info(f"早停于第 {epoch} 轮")
                    break
            else:
                if epoch % 10 == 0:
                    logger.info(f"Epoch {epoch}: Train Loss: {avg_train_loss:.6f}")
        
        # 加载最佳模型
        if val_data is not None:
            self.model.load_state_dict(torch.load('best_transformer_model.pth'))
        
        self.is_trained = True
        logger.info("Transformer模型训练完成")
    
    def predict(self, data: pd.DataFrame, timeframe: int = 1) -> PredictionResult:
        """
        进行预测
        
        Args:
            data: 输入数据（最近seq_length个时间点）
            timeframe: 预测时间框架（分钟）
            
        Returns:
            预测结果
        """
        if not self.is_trained:
            raise ValueError("模型尚未训练，请先调用train方法")
        
        self.model.eval()
        
        # 准备输入数据
        feature_cols = [col for col in data.columns if not col.startswith('target_') and not col.startswith('return_')]
        
        if len(data) < self.config.seq_length:
            raise ValueError(f"输入数据长度不足，需要至少 {self.config.seq_length} 个时间点")
        
        # 获取最近的序列数据
        seq_data = data[feature_cols].tail(self.config.seq_length).values
        X = torch.FloatTensor(seq_data).unsqueeze(0).to(self.device)  # [1, seq_len, feature_dim]
        
        with torch.no_grad():
            predictions = self.model(X)  # [1, 3]
            predictions = predictions.cpu().numpy()[0]
        
        # 根据时间框架选择预测结果
        if timeframe <= 1:
            pred_return = predictions[0]  # 1分钟预测
        elif timeframe <= 5:
            pred_return = predictions[1]  # 5分钟预测
        else:
            pred_return = predictions[2]  # 15分钟预测
        
        # 计算方向和强度
        direction = 1 if pred_return > 0.002 else -1 if pred_return < -0.002 else 0
        strength = min(abs(pred_return) * 100, 1.0)  # 转换为0-1范围
        confidence = min(strength * 2, 1.0)  # 基于强度计算置信度
        
        return PredictionResult(
            direction=direction,
            strength=strength,
            confidence=confidence,
            expected_return=pred_return,
            timeframe=timeframe,
            timestamp=pd.Timestamp.now()
        )
    
    def save_model(self, filepath: str):
        """保存模型"""
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'config': self.config,
            'is_trained': self.is_trained
        }, filepath)
        logger.info(f"模型已保存到 {filepath}")
    
    def load_model(self, filepath: str):
        """加载模型"""
        checkpoint = torch.load(filepath, map_location=self.device)
        self.config = checkpoint['config']
        self.model = TransformerTimeSeriesModel(self.config).to(self.device)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.is_trained = checkpoint['is_trained']
        logger.info(f"模型已从 {filepath} 加载")


def create_transformer_predictor(config: Dict = None) -> TransformerPredictor:
    """
    创建Transformer预测器
    
    Args:
        config: 配置字典
        
    Returns:
        TransformerPredictor实例
    """
    if config is None:
        config = {}
    
    transformer_config = TransformerConfig(
        d_model=config.get('d_model', 128),
        nhead=config.get('nhead', 8),
        num_layers=config.get('num_layers', 6),
        dim_feedforward=config.get('dim_feedforward', 512),
        dropout=config.get('dropout', 0.1),
        seq_length=config.get('seq_length', 60),
        feature_dim=config.get('feature_dim', 20),
        output_dim=config.get('output_dim', 3)
    )
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    return TransformerPredictor(transformer_config, device)