#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
技术分析器独立测试
不依赖其他模块，仅测试技术分析功能
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_technical_only.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

def create_test_data(length: int = 100) -> pd.DataFrame:
    """创建测试用的市场数据"""
    timestamps = pd.date_range(
        start=datetime.now() - timedelta(minutes=length-1),
        periods=length,
        freq='1min'
    )
    
    np.random.seed(42)
    base_price = 100.0
    trend = 0.001
    volatility = 0.02
    
    prices = [base_price]
    for i in range(1, length):
        change = np.random.normal(trend, volatility)
        new_price = prices[-1] * (1 + change)
        prices.append(new_price)
    
    data = []
    for i, (timestamp, close) in enumerate(zip(timestamps, prices)):
        open_price = prices[i-1] if i > 0 else close
        high = max(open_price, close) * (1 + np.random.uniform(0, 0.01))
        low = min(open_price, close) * (1 - np.random.uniform(0, 0.01))
        
        price_change = abs(close - open_price) / open_price if open_price > 0 else 0
        base_volume = 1000000
        volume = int(base_volume * (1 + price_change * 5 + np.random.uniform(-0.3, 0.3)))
        
        data.append({
            'timestamp': timestamp,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
    
    return pd.DataFrame(data)

def test_technical_analyzer():
    """测试技术分析器"""
    logger.info("开始测试技术分析器...")
    
    try:
        # 直接导入技术分析器模块
        sys.path.append(os.path.dirname(__file__))
        from technical_analyzer import (
            TechnicalAnalyzer,
            SignalType
        )
        
        # 创建测试数据
        test_data = create_test_data(100)
        logger.info(f"创建测试数据: {len(test_data)} 条记录")
        
        # 创建技术分析器
        config = {
            'ma_periods': [5, 10, 20],
            'rsi_period': 14,
            'macd_params': (12, 26, 9)
        }
        analyzer = TechnicalAnalyzer(config)
        
        # 生成信号
        signal = analyzer.generate_signal(test_data)
        
        logger.info("=== 技术分析信号结果 ===")
        logger.info(f"信号类型: {signal.signal_type.name}")
        logger.info(f"信号强度: {signal.strength:.3f}")
        logger.info(f"置信度: {signal.confidence:.3f}")
        logger.info(f"生成原因: {signal.reason}")
        logger.info(f"时间戳: {signal.timestamp}")
        
        # 检查指标值
        logger.info("\n=== 技术指标值 ===")
        for indicator, value in signal.indicators.items():
            logger.info(f"{indicator}: {value:.3f}")
        
        # 测试支撑阻力位计算
        sr_levels = analyzer.get_support_resistance_levels(test_data)
        logger.info(f"\n=== 支撑阻力位 ===")
        if sr_levels['support']:
            logger.info(f"支撑位: {sr_levels['support']['level']:.2f}, 强度: {sr_levels['support']['strength']:.3f}")
        if sr_levels['resistance']:
            logger.info(f"阻力位: {sr_levels['resistance']['level']:.2f}, 强度: {sr_levels['resistance']['strength']:.3f}")
        
        # 测试趋势强度计算
        trend_info = analyzer.calculate_trend_strength(test_data)
        logger.info(f"\n=== 趋势分析 ===")
        logger.info(f"趋势方向: {trend_info['trend']}")
        logger.info(f"趋势强度: {trend_info['strength']:.3f}")
        logger.info(f"R²: {trend_info['r_squared']:.3f}")
        
        logger.info("技术分析器测试完成 ✓")
        return True
        
    except Exception as e:
        logger.error(f"技术分析器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_signal_generation_logic():
    """测试信号生成逻辑"""
    logger.info("\n开始测试信号生成逻辑...")
    
    try:
        # 直接导入技术分析器模块
        from technical_analyzer import TechnicalAnalyzer
        
        # 创建不同类型的测试数据
        test_cases = [
            ("上涨趋势", create_uptrend_data()),
            ("下跌趋势", create_downtrend_data()),
            ("震荡行情", create_sideways_data())
        ]
        
        analyzer = TechnicalAnalyzer()
        
        logger.info("=== 不同市场环境下的信号生成 ===")
        
        for case_name, data in test_cases:
            signal = analyzer.generate_signal(data)
            
            logger.info(f"\n{case_name}:")
            logger.info(f"  信号类型: {signal.signal_type.name}")
            logger.info(f"  信号强度: {signal.strength:.3f}")
            logger.info(f"  置信度: {signal.confidence:.3f}")
            logger.info(f"  RSI: {signal.indicators.get('rsi', 0):.1f}")
            logger.info(f"  MACD: {signal.indicators.get('macd', 0):.4f}")
        
        logger.info("信号生成逻辑测试完成 ✓")
        return True
        
    except Exception as e:
        logger.error(f"信号生成逻辑测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_uptrend_data(length: int = 50) -> pd.DataFrame:
    """创建上涨趋势数据"""
    timestamps = pd.date_range(
        start=datetime.now() - timedelta(minutes=length-1),
        periods=length,
        freq='1min'
    )
    
    base_price = 100.0
    trend = 0.003  # 强上涨趋势
    volatility = 0.015
    
    prices = [base_price]
    for i in range(1, length):
        change = np.random.normal(trend, volatility)
        new_price = prices[-1] * (1 + change)
        prices.append(new_price)
    
    data = []
    for i, (timestamp, close) in enumerate(zip(timestamps, prices)):
        open_price = prices[i-1] if i > 0 else close
        high = max(open_price, close) * (1 + np.random.uniform(0, 0.01))
        low = min(open_price, close) * (1 - np.random.uniform(0, 0.005))
        
        # 上涨时成交量放大
        base_volume = 1000000
        volume_multiplier = 1.5 if close > open_price else 0.8
        volume = int(base_volume * volume_multiplier * (1 + np.random.uniform(-0.2, 0.2)))
        
        data.append({
            'timestamp': timestamp,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
    
    return pd.DataFrame(data)

def create_downtrend_data(length: int = 50) -> pd.DataFrame:
    """创建下跌趋势数据"""
    timestamps = pd.date_range(
        start=datetime.now() - timedelta(minutes=length-1),
        periods=length,
        freq='1min'
    )
    
    base_price = 100.0
    trend = -0.002  # 下跌趋势
    volatility = 0.02
    
    prices = [base_price]
    for i in range(1, length):
        change = np.random.normal(trend, volatility)
        new_price = prices[-1] * (1 + change)
        prices.append(new_price)
    
    data = []
    for i, (timestamp, close) in enumerate(zip(timestamps, prices)):
        open_price = prices[i-1] if i > 0 else close
        high = max(open_price, close) * (1 + np.random.uniform(0, 0.005))
        low = min(open_price, close) * (1 - np.random.uniform(0, 0.01))
        
        # 下跌时成交量放大
        base_volume = 1000000
        volume_multiplier = 1.3 if close < open_price else 0.7
        volume = int(base_volume * volume_multiplier * (1 + np.random.uniform(-0.2, 0.2)))
        
        data.append({
            'timestamp': timestamp,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
    
    return pd.DataFrame(data)

def create_sideways_data(length: int = 50) -> pd.DataFrame:
    """创建震荡行情数据"""
    timestamps = pd.date_range(
        start=datetime.now() - timedelta(minutes=length-1),
        periods=length,
        freq='1min'
    )
    
    base_price = 100.0
    trend = 0.0001  # 几乎无趋势
    volatility = 0.015
    
    prices = [base_price]
    for i in range(1, length):
        # 添加周期性波动
        cycle_factor = np.sin(i * 2 * np.pi / 20) * 0.005
        change = np.random.normal(trend + cycle_factor, volatility)
        new_price = prices[-1] * (1 + change)
        prices.append(new_price)
    
    data = []
    for i, (timestamp, close) in enumerate(zip(timestamps, prices)):
        open_price = prices[i-1] if i > 0 else close
        high = max(open_price, close) * (1 + np.random.uniform(0, 0.008))
        low = min(open_price, close) * (1 - np.random.uniform(0, 0.008))
        
        # 震荡时成交量相对稳定
        base_volume = 1000000
        volume = int(base_volume * (1 + np.random.uniform(-0.3, 0.3)))
        
        data.append({
            'timestamp': timestamp,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
    
    return pd.DataFrame(data)

def test_indicator_calculations():
    """测试技术指标计算"""
    logger.info("\n开始测试技术指标计算...")
    
    try:
        from technical_analyzer import TechnicalAnalyzer
        
        # 创建测试数据
        test_data = create_test_data(100)
        
        analyzer = TechnicalAnalyzer()
        
        # 计算所有指标
        data_with_indicators = analyzer.calculate_indicators(test_data)
        
        logger.info("=== 技术指标计算测试 ===")
        
        # 检查关键指标
        indicators_to_check = [
            'ma_5', 'ma_10', 'ma_20',
            'ema_12', 'ema_26',
            'rsi', 'macd', 'macd_signal', 'macd_hist',
            'bb_upper', 'bb_middle', 'bb_lower',
            'k', 'd', 'j',
            'volume_ratio', 'atr'
        ]
        
        latest_data = data_with_indicators.iloc[-1]
        
        for indicator in indicators_to_check:
            if indicator in latest_data:
                value = latest_data[indicator]
                if pd.notna(value):
                    logger.info(f"{indicator}: {value:.4f}")
                else:
                    logger.warning(f"{indicator}: NaN")
            else:
                logger.warning(f"{indicator}: 未计算")
        
        # 验证指标的合理性
        logger.info("\n=== 指标合理性检查 ===")
        
        # RSI应该在0-100之间
        rsi_value = latest_data.get('rsi', 50)
        if 0 <= rsi_value <= 100:
            logger.info(f"RSI值合理: {rsi_value:.2f}")
        else:
            logger.warning(f"RSI值异常: {rsi_value:.2f}")
        
        # 布林带上轨应该大于下轨
        bb_upper = latest_data.get('bb_upper', 0)
        bb_lower = latest_data.get('bb_lower', 0)
        if bb_upper > bb_lower:
            logger.info(f"布林带合理: 上轨{bb_upper:.2f} > 下轨{bb_lower:.2f}")
        else:
            logger.warning(f"布林带异常: 上轨{bb_upper:.2f} <= 下轨{bb_lower:.2f}")
        
        # 成交量比率应该为正数
        volume_ratio = latest_data.get('volume_ratio', 1)
        if volume_ratio > 0:
            logger.info(f"成交量比率合理: {volume_ratio:.2f}")
        else:
            logger.warning(f"成交量比率异常: {volume_ratio:.2f}")
        
        logger.info("技术指标计算测试完成 ✓")
        return True
        
    except Exception as e:
        logger.error(f"技术指标计算测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    logger.info("开始技术分析器独立测试")
    logger.info("=" * 60)
    
    test_results = []
    
    # 运行各项测试
    tests = [
        ("技术分析器基础功能", test_technical_analyzer),
        ("信号生成逻辑", test_signal_generation_logic),
        ("技术指标计算", test_indicator_calculations)
    ]
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            test_results.append((test_name, result))
            if result:
                logger.info(f"{test_name} 测试通过 ✓")
            else:
                logger.error(f"{test_name} 测试失败 ✗")
        except Exception as e:
            logger.error(f"{test_name} 测试异常: {e}")
            test_results.append((test_name, False))
    
    # 汇总测试结果
    logger.info("\n" + "="*60)
    logger.info("测试结果汇总:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "通过 ✓" if result else "失败 ✗"
        logger.info(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！技术分析器实现正确。")
        return True
    else:
        logger.error(f"❌ {total - passed} 项测试失败，请检查实现。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)