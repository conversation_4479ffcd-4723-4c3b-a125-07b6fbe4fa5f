"""
CNN价格模式识别模型
用于识别股票价格的技术形态和模式
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import logging
from sklearn.preprocessing import MinMaxScaler

logger = logging.getLogger(__name__)


@dataclass
class CNNConfig:
    """CNN模型配置"""
    input_channels: int = 5  # 输入通道数（OHLCV）
    sequence_length: int = 60  # 序列长度
    conv_layers: List[int] = None  # 卷积层通道数
    kernel_sizes: List[int] = None  # 卷积核大小
    pool_sizes: List[int] = None  # 池化大小
    fc_layers: List[int] = None  # 全连接层大小
    dropout: float = 0.3  # Dropout率
    output_dim: int = 4  # 输出维度（模式类型数量）
    
    def __post_init__(self):
        if self.conv_layers is None:
            self.conv_layers = [32, 64, 128, 256]
        if self.kernel_sizes is None:
            self.kernel_sizes = [3, 3, 3, 3]
        if self.pool_sizes is None:
            self.pool_sizes = [2, 2, 2, 2]
        if self.fc_layers is None:
            self.fc_layers = [512, 256, 128]


class PricePatternCNN(nn.Module):
    """价格模式识别CNN模型"""
    
    def __init__(self, config: CNNConfig):
        super().__init__()
        self.config = config
        
        # 构建卷积层
        self.conv_layers = nn.ModuleList()
        self.pool_layers = nn.ModuleList()
        self.batch_norms = nn.ModuleList()
        
        in_channels = config.input_channels
        current_length = config.sequence_length
        
        for i, (out_channels, kernel_size, pool_size) in enumerate(
            zip(config.conv_layers, config.kernel_sizes, config.pool_sizes)):
            
            # 卷积层
            conv = nn.Conv1d(
                in_channels=in_channels,
                out_channels=out_channels,
                kernel_size=kernel_size,
                padding=kernel_size//2
            )
            self.conv_layers.append(conv)
            
            # 批归一化
            self.batch_norms.append(nn.BatchNorm1d(out_channels))
            
            # 池化层
            pool = nn.MaxPool1d(kernel_size=pool_size, stride=pool_size)
            self.pool_layers.append(pool)
            
            in_channels = out_channels
            current_length = current_length // pool_size
        
        # 计算展平后的特征维度
        self.flattened_size = config.conv_layers[-1] * current_length
        
        # 构建全连接层
        self.fc_layers = nn.ModuleList()
        fc_input_size = self.flattened_size
        
        for fc_size in config.fc_layers:
            self.fc_layers.append(nn.Linear(fc_input_size, fc_size))
            fc_input_size = fc_size
        
        # 输出层
        self.output_layer = nn.Linear(config.fc_layers[-1], config.output_dim)
        
        # Dropout
        self.dropout = nn.Dropout(config.dropout)
        
        # 初始化权重
        self._init_weights()
    
    def _init_weights(self):
        """初始化模型权重"""
        for module in self.modules():
            if isinstance(module, nn.Conv1d):
                nn.init.kaiming_normal_(module.weight, mode='fan_out', nonlinearity='relu')
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
            elif isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
            elif isinstance(module, nn.BatchNorm1d):
                nn.init.ones_(module.weight)
                nn.init.zeros_(module.bias)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 输入张量 [batch_size, channels, sequence_length]
            
        Returns:
            模式识别结果 [batch_size, output_dim]
        """
        # 卷积层
        for conv, bn, pool in zip(self.conv_layers, self.batch_norms, self.pool_layers):
            x = conv(x)
            x = bn(x)
            x = F.relu(x)
            x = pool(x)
            x = self.dropout(x)
        
        # 展平
        x = x.view(x.size(0), -1)
        
        # 全连接层
        for fc in self.fc_layers:
            x = fc(x)
            x = F.relu(x)
            x = self.dropout(x)
        
        # 输出层
        output = self.output_layer(x)
        
        return output


@dataclass
class PatternResult:
    """模式识别结果"""
    pattern_type: str  # 模式类型
    confidence: float  # 置信度
    strength: float  # 强度
    direction: int  # 方向：1上涨，-1下跌，0震荡
    support_level: float  # 支撑位
    resistance_level: float  # 阻力位
    timestamp: pd.Timestamp  # 识别时间


class CNNPatternRecognizer:
    """CNN模式识别器"""
    
    def __init__(self, config: CNNConfig, device: str = 'cpu'):
        self.config = config
        self.device = torch.device(device)
        self.model = PricePatternCNN(config).to(self.device)
        self.is_trained = False
        
        # 数据预处理器
        self.scaler = MinMaxScaler()
        
        # 模式类型映射
        self.pattern_types = {
            0: "突破形态",
            1: "反转形态", 
            2: "整理形态",
            3: "趋势延续"
        }
        
        # 技术形态识别器
        self.pattern_detectors = {
            'head_shoulders': self._detect_head_shoulders,
            'double_top': self._detect_double_top,
            'double_bottom': self._detect_double_bottom,
            'triangle': self._detect_triangle,
            'flag': self._detect_flag,
            'wedge': self._detect_wedge
        }
    
    def prepare_data(self, data: pd.DataFrame) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        准备训练数据
        
        Args:
            data: 包含OHLCV数据的DataFrame
            
        Returns:
            特征张量和标签张量
        """
        # 提取OHLCV数据
        ohlcv_cols = ['open', 'high', 'low', 'close', 'volume']
        
        # 创建序列数据
        sequences = []
        labels = []
        
        for i in range(self.config.sequence_length, len(data)):
            # 获取序列数据
            seq_data = data[ohlcv_cols].iloc[i-self.config.sequence_length:i].values
            
            # 归一化
            seq_data_normalized = self.scaler.fit_transform(seq_data)
            
            # 转置为 [channels, sequence_length] 格式
            seq_data_transposed = seq_data_normalized.T
            sequences.append(seq_data_transposed)
            
            # 生成标签（基于未来价格变化识别模式）
            future_prices = data['close'].iloc[i:i+10].values  # 未来10个时间点
            current_price = data['close'].iloc[i-1]
            
            label = self._generate_pattern_label(current_price, future_prices)
            labels.append(label)
        
        # 转换为张量
        X = torch.FloatTensor(np.array(sequences))
        y = torch.LongTensor(np.array(labels))
        
        return X, y
    
    def _generate_pattern_label(self, current_price: float, future_prices: np.ndarray) -> int:
        """
        根据未来价格变化生成模式标签
        
        Args:
            current_price: 当前价格
            future_prices: 未来价格序列
            
        Returns:
            模式标签
        """
        if len(future_prices) == 0:
            return 2  # 整理形态
        
        max_price = np.max(future_prices)
        min_price = np.min(future_prices)
        final_price = future_prices[-1]
        
        # 计算价格变化幅度
        upward_change = (max_price - current_price) / current_price
        downward_change = (current_price - min_price) / current_price
        final_change = (final_price - current_price) / current_price
        
        # 判断模式类型
        if upward_change > 0.03 and final_change > 0.01:  # 突破向上
            return 0  # 突破形态
        elif downward_change > 0.03 and final_change < -0.01:  # 突破向下
            return 0  # 突破形态
        elif abs(final_change) > 0.02:  # 明显反转
            return 1  # 反转形态
        elif max(upward_change, downward_change) < 0.015:  # 小幅波动
            return 2  # 整理形态
        else:
            return 3  # 趋势延续
    
    def train(self, train_data: pd.DataFrame, val_data: pd.DataFrame = None,
              epochs: int = 100, batch_size: int = 32, learning_rate: float = 0.001):
        """
        训练模型
        
        Args:
            train_data: 训练数据
            val_data: 验证数据
            epochs: 训练轮数
            batch_size: 批次大小
            learning_rate: 学习率
        """
        logger.info("开始训练CNN价格模式识别模型")
        
        # 准备数据
        X_train, y_train = self.prepare_data(train_data)
        
        if val_data is not None:
            X_val, y_val = self.prepare_data(val_data)
        
        # 创建数据加载器
        train_dataset = torch.utils.data.TensorDataset(X_train, y_train)
        train_loader = torch.utils.data.DataLoader(
            train_dataset, batch_size=batch_size, shuffle=True)
        
        # 优化器和损失函数
        optimizer = torch.optim.Adam(self.model.parameters(), lr=learning_rate, weight_decay=1e-5)
        criterion = nn.CrossEntropyLoss()
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, mode='min', factor=0.5, patience=10)
        
        # 训练循环
        best_val_acc = 0.0
        patience_counter = 0
        
        for epoch in range(epochs):
            # 训练阶段
            self.model.train()
            train_loss = 0.0
            train_correct = 0
            train_total = 0
            
            for batch_X, batch_y in train_loader:
                batch_X = batch_X.to(self.device)
                batch_y = batch_y.to(self.device)
                
                optimizer.zero_grad()
                outputs = self.model(batch_X)
                loss = criterion(outputs, batch_y)
                loss.backward()
                
                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                
                optimizer.step()
                
                train_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                train_total += batch_y.size(0)
                train_correct += (predicted == batch_y).sum().item()
            
            train_accuracy = 100 * train_correct / train_total
            avg_train_loss = train_loss / len(train_loader)
            
            # 验证阶段
            if val_data is not None:
                self.model.eval()
                val_loss = 0.0
                val_correct = 0
                val_total = 0
                
                with torch.no_grad():
                    val_dataset = torch.utils.data.TensorDataset(X_val, y_val)
                    val_loader = torch.utils.data.DataLoader(
                        val_dataset, batch_size=batch_size, shuffle=False)
                    
                    for batch_X, batch_y in val_loader:
                        batch_X = batch_X.to(self.device)
                        batch_y = batch_y.to(self.device)
                        
                        outputs = self.model(batch_X)
                        loss = criterion(outputs, batch_y)
                        
                        val_loss += loss.item()
                        _, predicted = torch.max(outputs.data, 1)
                        val_total += batch_y.size(0)
                        val_correct += (predicted == batch_y).sum().item()
                
                val_accuracy = 100 * val_correct / val_total
                avg_val_loss = val_loss / len(val_loader)
                scheduler.step(avg_val_loss)
                
                # 早停检查
                if val_accuracy > best_val_acc:
                    best_val_acc = val_accuracy
                    patience_counter = 0
                    # 保存最佳模型
                    torch.save(self.model.state_dict(), 'best_cnn_model.pth')
                else:
                    patience_counter += 1
                
                if epoch % 10 == 0:
                    logger.info(f"Epoch {epoch}: Train Loss: {avg_train_loss:.4f}, "
                              f"Train Acc: {train_accuracy:.2f}%, Val Loss: {avg_val_loss:.4f}, "
                              f"Val Acc: {val_accuracy:.2f}%")
                
                # 早停
                if patience_counter >= 20:
                    logger.info(f"早停于第 {epoch} 轮")
                    break
            else:
                if epoch % 10 == 0:
                    logger.info(f"Epoch {epoch}: Train Loss: {avg_train_loss:.4f}, "
                              f"Train Acc: {train_accuracy:.2f}%")
        
        # 加载最佳模型
        if val_data is not None:
            self.model.load_state_dict(torch.load('best_cnn_model.pth'))
        
        self.is_trained = True
        logger.info("CNN模型训练完成")
    
    def predict_pattern(self, data: pd.DataFrame) -> PatternResult:
        """
        识别价格模式
        
        Args:
            data: 输入数据（最近sequence_length个时间点）
            
        Returns:
            模式识别结果
        """
        if not self.is_trained:
            raise ValueError("模型尚未训练，请先调用train方法")
        
        self.model.eval()
        
        # 准备输入数据
        ohlcv_cols = ['open', 'high', 'low', 'close', 'volume']
        
        if len(data) < self.config.sequence_length:
            raise ValueError(f"输入数据长度不足，需要至少 {self.config.sequence_length} 个时间点")
        
        # 获取最近的序列数据
        seq_data = data[ohlcv_cols].tail(self.config.sequence_length).values
        seq_data_normalized = self.scaler.fit_transform(seq_data)
        seq_data_transposed = seq_data_normalized.T
        
        X = torch.FloatTensor(seq_data_transposed).unsqueeze(0).to(self.device)
        
        with torch.no_grad():
            outputs = self.model(X)
            probabilities = F.softmax(outputs, dim=1)
            predicted_class = torch.argmax(probabilities, dim=1).item()
            confidence = probabilities[0][predicted_class].item()
        
        # 获取模式类型
        pattern_type = self.pattern_types[predicted_class]
        
        # 计算支撑阻力位
        recent_prices = data['close'].tail(20).values
        support_level = np.min(recent_prices)
        resistance_level = np.max(recent_prices)
        
        # 确定方向
        if predicted_class == 0:  # 突破形态
            current_price = data['close'].iloc[-1]
            if current_price > (support_level + resistance_level) / 2:
                direction = 1  # 向上突破
            else:
                direction = -1  # 向下突破
        elif predicted_class == 1:  # 反转形态
            # 基于最近趋势判断反转方向
            recent_trend = data['close'].tail(5).pct_change().mean()
            direction = -1 if recent_trend > 0 else 1
        else:
            direction = 0  # 震荡或延续
        
        return PatternResult(
            pattern_type=pattern_type,
            confidence=confidence,
            strength=confidence,  # 使用置信度作为强度
            direction=direction,
            support_level=support_level,
            resistance_level=resistance_level,
            timestamp=pd.Timestamp.now()
        )
    
    def _detect_head_shoulders(self, prices: np.ndarray) -> Dict:
        """检测头肩顶/头肩底形态"""
        # 简化的头肩形态检测逻辑
        if len(prices) < 20:
            return {'detected': False}
        
        # 寻找局部极值点
        peaks = []
        valleys = []
        
        for i in range(1, len(prices) - 1):
            if prices[i] > prices[i-1] and prices[i] > prices[i+1]:
                peaks.append((i, prices[i]))
            elif prices[i] < prices[i-1] and prices[i] < prices[i+1]:
                valleys.append((i, prices[i]))
        
        # 头肩顶检测
        if len(peaks) >= 3:
            # 检查是否有明显的头部（中间峰值最高）
            sorted_peaks = sorted(peaks, key=lambda x: x[1], reverse=True)
            head = sorted_peaks[0]
            
            # 寻找左右肩膀
            left_shoulder = None
            right_shoulder = None
            
            for peak in peaks:
                if peak[0] < head[0] and (left_shoulder is None or peak[0] > left_shoulder[0]):
                    left_shoulder = peak
                elif peak[0] > head[0] and (right_shoulder is None or peak[0] < right_shoulder[0]):
                    right_shoulder = peak
            
            if left_shoulder and right_shoulder:
                # 检查肩膀高度是否相近
                shoulder_diff = abs(left_shoulder[1] - right_shoulder[1]) / max(left_shoulder[1], right_shoulder[1])
                if shoulder_diff < 0.05:  # 5%以内的差异
                    return {
                        'detected': True,
                        'type': 'head_shoulders_top',
                        'confidence': 1 - shoulder_diff,
                        'neckline': (left_shoulder[1] + right_shoulder[1]) / 2
                    }
        
        return {'detected': False}
    
    def _detect_double_top(self, prices: np.ndarray) -> Dict:
        """检测双顶形态"""
        # 简化的双顶检测逻辑
        if len(prices) < 15:
            return {'detected': False}
        
        # 寻找两个相近的高点
        peaks = []
        for i in range(2, len(prices) - 2):
            if (prices[i] > prices[i-1] and prices[i] > prices[i+1] and
                prices[i] > prices[i-2] and prices[i] > prices[i+2]):
                peaks.append((i, prices[i]))
        
        if len(peaks) >= 2:
            # 检查最高的两个峰值
            sorted_peaks = sorted(peaks, key=lambda x: x[1], reverse=True)[:2]
            peak1, peak2 = sorted_peaks
            
            # 检查高度是否相近
            height_diff = abs(peak1[1] - peak2[1]) / max(peak1[1], peak2[1])
            if height_diff < 0.03:  # 3%以内的差异
                return {
                    'detected': True,
                    'type': 'double_top',
                    'confidence': 1 - height_diff,
                    'resistance': max(peak1[1], peak2[1])
                }
        
        return {'detected': False}
    
    def _detect_double_bottom(self, prices: np.ndarray) -> Dict:
        """检测双底形态"""
        # 类似双顶的逻辑，但寻找低点
        if len(prices) < 15:
            return {'detected': False}
        
        valleys = []
        for i in range(2, len(prices) - 2):
            if (prices[i] < prices[i-1] and prices[i] < prices[i+1] and
                prices[i] < prices[i-2] and prices[i] < prices[i+2]):
                valleys.append((i, prices[i]))
        
        if len(valleys) >= 2:
            sorted_valleys = sorted(valleys, key=lambda x: x[1])[:2]
            valley1, valley2 = sorted_valleys
            
            height_diff = abs(valley1[1] - valley2[1]) / max(valley1[1], valley2[1])
            if height_diff < 0.03:
                return {
                    'detected': True,
                    'type': 'double_bottom',
                    'confidence': 1 - height_diff,
                    'support': min(valley1[1], valley2[1])
                }
        
        return {'detected': False}
    
    def _detect_triangle(self, prices: np.ndarray) -> Dict:
        """检测三角形整理形态"""
        # 简化的三角形检测
        if len(prices) < 20:
            return {'detected': False}
        
        # 计算趋势线
        highs = []
        lows = []
        
        for i in range(1, len(prices) - 1):
            if prices[i] > prices[i-1] and prices[i] > prices[i+1]:
                highs.append((i, prices[i]))
            elif prices[i] < prices[i-1] and prices[i] < prices[i+1]:
                lows.append((i, prices[i]))
        
        if len(highs) >= 2 and len(lows) >= 2:
            # 检查高点是否呈下降趋势，低点是否呈上升趋势
            high_trend = (highs[-1][1] - highs[0][1]) / (highs[-1][0] - highs[0][0])
            low_trend = (lows[-1][1] - lows[0][1]) / (lows[-1][0] - lows[0][0])
            
            if high_trend < 0 and low_trend > 0:  # 收敛三角形
                return {
                    'detected': True,
                    'type': 'converging_triangle',
                    'confidence': 0.7
                }
        
        return {'detected': False}
    
    def _detect_flag(self, prices: np.ndarray) -> Dict:
        """检测旗形整理形态"""
        # 简化的旗形检测
        if len(prices) < 15:
            return {'detected': False}
        
        # 检查是否有明显的趋势后的横盘整理
        first_half = prices[:len(prices)//2]
        second_half = prices[len(prices)//2:]
        
        first_trend = (first_half[-1] - first_half[0]) / len(first_half)
        second_volatility = np.std(second_half)
        
        if abs(first_trend) > 0.01 and second_volatility < 0.005:
            return {
                'detected': True,
                'type': 'flag',
                'confidence': 0.6
            }
        
        return {'detected': False}
    
    def _detect_wedge(self, prices: np.ndarray) -> Dict:
        """检测楔形形态"""
        # 简化的楔形检测
        if len(prices) < 20:
            return {'detected': False}
        
        # 类似三角形，但两条趋势线都是同向的
        highs = []
        lows = []
        
        for i in range(1, len(prices) - 1):
            if prices[i] > prices[i-1] and prices[i] > prices[i+1]:
                highs.append((i, prices[i]))
            elif prices[i] < prices[i-1] and prices[i] < prices[i+1]:
                lows.append((i, prices[i]))
        
        if len(highs) >= 2 and len(lows) >= 2:
            high_trend = (highs[-1][1] - highs[0][1]) / (highs[-1][0] - highs[0][0])
            low_trend = (lows[-1][1] - lows[0][1]) / (lows[-1][0] - lows[0][0])
            
            # 上升楔形或下降楔形
            if (high_trend > 0 and low_trend > 0 and high_trend < low_trend) or \
               (high_trend < 0 and low_trend < 0 and high_trend > low_trend):
                return {
                    'detected': True,
                    'type': 'wedge',
                    'confidence': 0.6
                }
        
        return {'detected': False}
    
    def save_model(self, filepath: str):
        """保存模型"""
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'config': self.config,
            'is_trained': self.is_trained,
            'scaler': self.scaler
        }, filepath)
        logger.info(f"CNN模型已保存到 {filepath}")
    
    def load_model(self, filepath: str):
        """加载模型"""
        checkpoint = torch.load(filepath, map_location=self.device)
        self.config = checkpoint['config']
        self.model = PricePatternCNN(self.config).to(self.device)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.is_trained = checkpoint['is_trained']
        self.scaler = checkpoint['scaler']
        logger.info(f"CNN模型已从 {filepath} 加载")


def create_cnn_recognizer(config: Dict = None) -> CNNPatternRecognizer:
    """
    创建CNN模式识别器
    
    Args:
        config: 配置字典
        
    Returns:
        CNNPatternRecognizer实例
    """
    if config is None:
        config = {}
    
    cnn_config = CNNConfig(
        input_channels=config.get('input_channels', 5),
        sequence_length=config.get('sequence_length', 60),
        conv_layers=config.get('conv_layers', [32, 64, 128, 256]),
        kernel_sizes=config.get('kernel_sizes', [3, 3, 3, 3]),
        pool_sizes=config.get('pool_sizes', [2, 2, 2, 2]),
        fc_layers=config.get('fc_layers', [512, 256, 128]),
        dropout=config.get('dropout', 0.3),
        output_dim=config.get('output_dim', 4)
    )
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    return CNNPatternRecognizer(cnn_config, device)