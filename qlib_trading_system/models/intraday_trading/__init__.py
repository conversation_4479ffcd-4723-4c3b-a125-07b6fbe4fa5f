"""
日内交易AI模型模块
包含Transformer时序预测、CNN模式识别、技术分析和信号融合功能
"""

# 条件导入核心类和函数
try:
    from .transformer_model import (
        TransformerPredictor,
        TransformerConfig,
        PredictionResult,
        create_transformer_predictor
    )
    TRANSFORMER_AVAILABLE = True
except ImportError as e:
    TRANSFORMER_AVAILABLE = False
    # 创建占位符类
    class TransformerPredictor: pass
    class TransformerConfig: pass
    class PredictionResult: pass
    def create_transformer_predictor(*args, **kwargs):
        raise ImportError(f"Transformer模型不可用: {e}")

try:
    from .cnn_model import (
        CNNPatternRecognizer,
        CNNConfig,
        PatternResult,
        create_cnn_recognizer
    )
    CNN_AVAILABLE = True
except ImportError as e:
    CNN_AVAILABLE = False
    # 创建占位符类
    class CNNPatternRecognizer: pass
    class CNNConfig: pass
    class PatternResult: pass
    def create_cnn_recognizer(*args, **kwargs):
        raise ImportError(f"CNN模型不可用: {e}")

# 技术分析器不依赖PyTorch，应该总是可用
try:
    from .technical_analyzer import (
        TechnicalAnalyzer,
        TechnicalSignal,
        SignalType,
        create_technical_analyzer
    )
    TECHNICAL_AVAILABLE = True
except ImportError as e:
    TECHNICAL_AVAILABLE = False
    # 创建占位符类
    class TechnicalAnalyzer: pass
    class TechnicalSignal: pass
    class SignalType: pass
    def create_technical_analyzer(*args, **kwargs):
        raise ImportError(f"技术分析器不可用: {e}")

# 信号融合模块，使用条件导入AI模型
try:
    from .signal_fusion import (
        SignalFusionEngine,
        FusedSignal,
        FusionSignalType,
        AdaptiveWeightManager,
        MarketStateMonitor,
        create_signal_fusion_engine
    )
    SIGNAL_FUSION_AVAILABLE = True
except ImportError as e:
    SIGNAL_FUSION_AVAILABLE = False
    # 创建占位符类
    class SignalFusionEngine: pass
    class FusedSignal: pass
    class FusionSignalType: pass
    class AdaptiveWeightManager: pass
    class MarketStateMonitor: pass
    def create_signal_fusion_engine(*args, **kwargs):
        raise ImportError(f"信号融合模块不可用: {e}")

# 版本信息
__version__ = "1.0.0"

# 模块说明
__doc__ = """
日内交易AI模型模块

主要组件:
1. TransformerPredictor - Transformer时序预测模型
2. CNNPatternRecognizer - CNN价格模式识别模型  
3. TechnicalAnalyzer - 传统技术分析信号生成器
4. SignalFusionEngine - 多模型信号融合引擎

使用示例:
```python
from qlib_trading_system.models.intraday_trading import (
    create_transformer_predictor,
    create_cnn_recognizer,
    create_technical_analyzer,
    create_signal_fusion_engine
)

# 创建各个模型
transformer = create_transformer_predictor()
cnn = create_cnn_recognizer()
technical = create_technical_analyzer()

# 创建融合引擎
fusion_engine = create_signal_fusion_engine({
    'initial_weights': {
        'transformer': 0.4,
        'cnn': 0.3,
        'technical': 0.3
    }
})

# 初始化AI模型
fusion_engine.initialize_models()

# 生成融合信号
signal = fusion_engine.fuse_signals(market_data, symbol='000001')
```
"""

# 导出的公共接口
__all__ = [
    # Transformer相关
    'TransformerPredictor',
    'TransformerConfig', 
    'PredictionResult',
    'create_transformer_predictor',
    
    # CNN相关
    'CNNPatternRecognizer',
    'CNNConfig',
    'PatternResult', 
    'create_cnn_recognizer',
    
    # 技术分析相关
    'TechnicalAnalyzer',
    'TechnicalSignal',
    'SignalType',
    'create_technical_analyzer',
    
    # 信号融合相关
    'SignalFusionEngine',
    'FusedSignal',
    'FusionSignalType',
    'AdaptiveWeightManager',
    'MarketStateMonitor',
    'create_signal_fusion_engine'
]