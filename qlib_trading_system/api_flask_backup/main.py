"""
API主应用入口
API Main Application Entry

创建和配置Flask API应用
"""

import os
import logging
from flask import Flask, jsonify, request
from flask_cors import CORS
from werkzeug.exceptions import HTTPException

from .auth import AuthManager, require_auth, require_api_key, require_permission, Permission
from .monitoring import APIMonitor, monitor_api
from .documentation import APIDocGenerator
from .routes import create_api_routes

logger = logging.getLogger(__name__)


def create_app(config=None):
    """
    创建Flask应用
    
    Args:
        config: 配置字典
        
    Returns:
        Flask应用实例
    """
    app = Flask(__name__)
    
    # 默认配置
    default_config = {
        'SECRET_KEY': os.environ.get('SECRET_KEY', 'dev-secret-key-change-in-production'),
        'JWT_SECRET_KEY': os.environ.get('JWT_SECRET_KEY', 'jwt-secret-key-change-in-production'),
        'API_TITLE': 'Qlib Trading System API',
        'API_VERSION': '1.0.0',
        'API_DESCRIPTION': '量化交易系统RESTful API接口',
        'CORS_ORIGINS': ['http://localhost:3000', 'http://localhost:8080'],
        'RATE_LIMIT_REQUESTS': 100,
        'RATE_LIMIT_WINDOW': 60,
        'DEBUG': os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'
    }
    
    # 合并配置
    if config:
        default_config.update(config)
    
    app.config.update(default_config)
    
    # 配置CORS
    CORS(app, origins=app.config['CORS_ORIGINS'])
    
    # 初始化组件
    auth_manager = AuthManager(
        secret_key=app.config['JWT_SECRET_KEY'],
        token_expiry_hours=24
    )
    
    api_monitor = APIMonitor(max_history=10000)
    
    doc_generator = APIDocGenerator(app)
    
    # 存储组件到应用上下文
    app.auth_manager = auth_manager
    app.api_monitor = api_monitor
    app.doc_generator = doc_generator
    
    # 注册路由
    create_api_routes(app, auth_manager, api_monitor, doc_generator)
    
    # 注册错误处理器
    register_error_handlers(app)
    
    # 注册中间件
    register_middleware(app, api_monitor)
    
    logger.info("Flask API应用创建完成")
    return app


def register_error_handlers(app: Flask):
    """注册错误处理器"""
    
    @app.errorhandler(400)
    def bad_request(error):
        return jsonify({
            'error': 'Bad Request',
            'message': '请求参数错误',
            'status_code': 400
        }), 400
    
    @app.errorhandler(401)
    def unauthorized(error):
        return jsonify({
            'error': 'Unauthorized',
            'message': '未授权访问',
            'status_code': 401
        }), 401
    
    @app.errorhandler(403)
    def forbidden(error):
        return jsonify({
            'error': 'Forbidden',
            'message': '权限不足',
            'status_code': 403
        }), 403
    
    @app.errorhandler(404)
    def not_found(error):
        return jsonify({
            'error': 'Not Found',
            'message': '资源不存在',
            'status_code': 404
        }), 404
    
    @app.errorhandler(405)
    def method_not_allowed(error):
        return jsonify({
            'error': 'Method Not Allowed',
            'message': '请求方法不允许',
            'status_code': 405
        }), 405
    
    @app.errorhandler(429)
    def rate_limit_exceeded(error):
        return jsonify({
            'error': 'Rate Limit Exceeded',
            'message': '请求频率过高，请稍后再试',
            'status_code': 429
        }), 429
    
    @app.errorhandler(500)
    def internal_server_error(error):
        logger.error(f"内部服务器错误: {error}")
        return jsonify({
            'error': 'Internal Server Error',
            'message': '服务器内部错误',
            'status_code': 500
        }), 500
    
    @app.errorhandler(HTTPException)
    def handle_http_exception(error):
        return jsonify({
            'error': error.name,
            'message': error.description,
            'status_code': error.code
        }), error.code
    
    @app.errorhandler(Exception)
    def handle_general_exception(error):
        logger.error(f"未处理的异常: {error}", exc_info=True)
        return jsonify({
            'error': 'Internal Server Error',
            'message': '服务器内部错误',
            'status_code': 500
        }), 500


def register_middleware(app: Flask, api_monitor: APIMonitor):
    """注册中间件"""
    
    @app.before_request
    def before_request():
        """请求前处理"""
        # 记录请求开始
        endpoint = request.endpoint or request.path
        method = request.method
        user_id = getattr(request, 'current_user', None)
        user_id = user_id.user_id if user_id else None
        
        api_monitor.start_request(endpoint, method, user_id)
    
    @app.after_request
    def after_request(response):
        """请求后处理"""
        # 记录请求结束
        if hasattr(request, 'api_request_id'):
            api_monitor.end_request(
                request.api_request_id,
                response.status_code,
                len(response.get_data()) if hasattr(response, 'get_data') else 0
            )
        
        # 添加CORS头
        response.headers.add('Access-Control-Allow-Origin', '*')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization,X-API-Key')
        response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,PATCH,OPTIONS')
        
        return response
    
    @app.teardown_appcontext
    def teardown_appcontext(error):
        """应用上下文清理"""
        if error:
            logger.error(f"应用上下文错误: {error}")


def create_health_check_routes(app: Flask):
    """创建健康检查路由"""
    
    @app.route('/health', methods=['GET'])
    def health_check():
        """健康检查"""
        return jsonify({
            'status': 'healthy',
            'timestamp': '2025-07-31T14:36:36.867283',
            'version': app.config.get('API_VERSION', '1.0.0'),
            'service': 'qlib-trading-system-api'
        })
    
    @app.route('/health/detailed', methods=['GET'])
    @require_auth(app.auth_manager)
    @require_permission(Permission.SYSTEM_ADMIN)
    def detailed_health_check():
        """详细健康检查"""
        try:
            # 检查各个组件状态
            auth_status = 'healthy' if app.auth_manager else 'unhealthy'
            monitor_status = 'healthy' if app.api_monitor else 'unhealthy'
            doc_status = 'healthy' if app.doc_generator else 'unhealthy'
            
            # 获取系统指标
            metrics = app.api_monitor.get_metrics(time_range_minutes=5)
            
            return jsonify({
                'status': 'healthy',
                'timestamp': '2025-07-31T14:36:36.867283',
                'version': app.config.get('API_VERSION', '1.0.0'),
                'components': {
                    'auth_manager': auth_status,
                    'api_monitor': monitor_status,
                    'doc_generator': doc_status
                },
                'metrics': metrics.to_dict(),
                'system_info': {
                    'total_users': len(app.auth_manager.users),
                    'active_endpoints': len(app.api_monitor.endpoint_stats)
                }
            })
            
        except Exception as e:
            logger.error(f"详细健康检查失败: {e}")
            return jsonify({
                'status': 'unhealthy',
                'error': str(e),
                'timestamp': '2025-07-31T14:36:36.867283'
            }), 500


def create_info_routes(app: Flask):
    """创建信息路由"""
    
    @app.route('/info', methods=['GET'])
    def api_info():
        """API信息"""
        return jsonify({
            'title': app.config.get('API_TITLE', 'Qlib Trading System API'),
            'version': app.config.get('API_VERSION', '1.0.0'),
            'description': app.config.get('API_DESCRIPTION', '量化交易系统RESTful API接口'),
            'documentation_url': '/docs/',
            'openapi_spec_url': '/api/v1/openapi.json',
            'postman_collection_url': '/api/v1/postman.json',
            'endpoints': {
                'auth': '/api/v1/auth',
                'config': '/api/v1/config',
                'trading': '/api/v1/trading',
                'monitoring': '/api/v1/monitoring',
                'data': '/api/v1/data'
            },
            'authentication': {
                'jwt': {
                    'header': 'Authorization',
                    'format': 'Bearer <token>'
                },
                'api_key': {
                    'header': 'X-API-Key',
                    'format': '<api_key>'
                }
            }
        })


def run_app(app: Flask, host='0.0.0.0', port=5000, debug=False):
    """
    运行Flask应用
    
    Args:
        app: Flask应用实例
        host: 主机地址
        port: 端口号
        debug: 调试模式
    """
    logger.info(f"启动API服务器: http://{host}:{port}")
    
    try:
        app.run(host=host, port=port, debug=debug)
    except KeyboardInterrupt:
        logger.info("API服务器已停止")
    except Exception as e:
        logger.error(f"API服务器启动失败: {e}")
        raise


if __name__ == '__main__':
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 创建应用
    app = create_app()
    
    # 添加健康检查和信息路由
    create_health_check_routes(app)
    create_info_routes(app)
    
    # 运行应用
    run_app(app, debug=True)
