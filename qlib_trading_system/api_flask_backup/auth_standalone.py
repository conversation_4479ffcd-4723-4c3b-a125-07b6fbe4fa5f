"""
独立的API认证系统（不依赖Flask）
Standalone API Authentication System (Flask-independent)

用于测试的独立认证系统
"""

import jwt
import hashlib
import secrets
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class UserRole(Enum):
    """用户角色枚举"""
    ADMIN = "admin"
    TRADER = "trader"
    ANALYST = "analyst"
    VIEWER = "viewer"
    API_USER = "api_user"


class Permission(Enum):
    """权限枚举"""
    MANAGE_CONFIG = "manage_config"
    VIEW_CONFIG = "view_config"
    EXECUTE_TRADE = "execute_trade"
    VIEW_POSITIONS = "view_positions"
    MANAGE_ORDERS = "manage_orders"
    VIEW_MARKET_DATA = "view_market_data"
    EXPORT_DATA = "export_data"
    SYSTEM_ADMIN = "system_admin"
    VIEW_LOGS = "view_logs"
    MANAGE_USERS = "manage_users"
    API_ACCESS = "api_access"
    API_ADMIN = "api_admin"


@dataclass
class User:
    """用户信息"""
    user_id: str
    username: str
    email: str
    role: UserRole
    permissions: List[Permission]
    is_active: bool
    created_time: datetime
    last_login: Optional[datetime] = None
    api_key: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "user_id": self.user_id,
            "username": self.username,
            "email": self.email,
            "role": self.role.value,
            "permissions": [p.value for p in self.permissions],
            "is_active": self.is_active,
            "created_time": self.created_time.isoformat(),
            "last_login": self.last_login.isoformat() if self.last_login else None,
            "has_api_key": bool(self.api_key)
        }


class AuthManager:
    """认证管理器"""
    
    def __init__(self, secret_key: str, token_expiry_hours: int = 24):
        """初始化认证管理器"""
        self.secret_key = secret_key
        self.token_expiry_hours = token_expiry_hours
        
        # 用户存储
        self.users: Dict[str, User] = {}
        self.username_to_id: Dict[str, str] = {}
        self.api_keys: Dict[str, str] = {}
        self.user_passwords: Dict[str, str] = {}
        
        # 角色权限映射
        self.role_permissions = self._init_role_permissions()
        
        # 创建默认管理员用户
        self._create_default_admin()
        
        logger.info("API认证管理器初始化完成")
    
    def _init_role_permissions(self) -> Dict[UserRole, List[Permission]]:
        """初始化角色权限映射"""
        return {
            UserRole.ADMIN: [
                Permission.SYSTEM_ADMIN,
                Permission.MANAGE_CONFIG,
                Permission.VIEW_CONFIG,
                Permission.EXECUTE_TRADE,
                Permission.VIEW_POSITIONS,
                Permission.MANAGE_ORDERS,
                Permission.VIEW_MARKET_DATA,
                Permission.EXPORT_DATA,
                Permission.VIEW_LOGS,
                Permission.MANAGE_USERS,
                Permission.API_ACCESS,
                Permission.API_ADMIN
            ],
            UserRole.TRADER: [
                Permission.VIEW_CONFIG,
                Permission.EXECUTE_TRADE,
                Permission.VIEW_POSITIONS,
                Permission.MANAGE_ORDERS,
                Permission.VIEW_MARKET_DATA,
                Permission.API_ACCESS
            ],
            UserRole.ANALYST: [
                Permission.VIEW_CONFIG,
                Permission.VIEW_POSITIONS,
                Permission.VIEW_MARKET_DATA,
                Permission.EXPORT_DATA,
                Permission.API_ACCESS
            ],
            UserRole.VIEWER: [
                Permission.VIEW_CONFIG,
                Permission.VIEW_POSITIONS,
                Permission.VIEW_MARKET_DATA,
                Permission.API_ACCESS
            ],
            UserRole.API_USER: [
                Permission.API_ACCESS,
                Permission.VIEW_MARKET_DATA,
                Permission.VIEW_POSITIONS
            ]
        }
    
    def _create_default_admin(self):
        """创建默认管理员用户"""
        admin_user = User(
            user_id="admin_001",
            username="admin",
            email="<EMAIL>",
            role=UserRole.ADMIN,
            permissions=self.role_permissions[UserRole.ADMIN],
            is_active=True,
            created_time=datetime.now()
        )
        
        self.users[admin_user.user_id] = admin_user
        self.username_to_id[admin_user.username] = admin_user.user_id
        self._set_user_password(admin_user.user_id, "admin123")
        
        logger.info("默认管理员用户已创建")
    
    def _hash_password(self, password: str) -> str:
        """密码哈希"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def _set_user_password(self, user_id: str, password: str):
        """设置用户密码"""
        self.user_passwords[user_id] = self._hash_password(password)
    
    def _verify_password(self, user_id: str, password: str) -> bool:
        """验证密码"""
        stored_hash = self.user_passwords.get(user_id)
        if not stored_hash:
            return False
        return stored_hash == self._hash_password(password)
    
    def create_user(
        self,
        username: str,
        email: str,
        password: str,
        role: UserRole,
        created_by: str
    ) -> Optional[str]:
        """创建用户"""
        try:
            if username in self.username_to_id:
                raise ValueError(f"用户名已存在: {username}")
            
            user_id = f"{role.value}_{len(self.users) + 1:03d}"
            
            user = User(
                user_id=user_id,
                username=username,
                email=email,
                role=role,
                permissions=self.role_permissions.get(role, []),
                is_active=True,
                created_time=datetime.now()
            )
            
            self.users[user_id] = user
            self.username_to_id[username] = user_id
            self._set_user_password(user_id, password)
            
            logger.info(f"用户创建成功: {username} ({user_id})")
            return user_id
            
        except Exception as e:
            logger.error(f"创建用户失败: {e}")
            return None
    
    def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """用户认证"""
        try:
            user_id = self.username_to_id.get(username)
            if not user_id:
                return None
            
            user = self.users.get(user_id)
            if not user or not user.is_active:
                return None
            
            if not self._verify_password(user_id, password):
                return None
            
            user.last_login = datetime.now()
            logger.info(f"用户认证成功: {username}")
            return user
            
        except Exception as e:
            logger.error(f"用户认证失败: {e}")
            return None
    
    def generate_token(self, user: User) -> str:
        """生成JWT Token"""
        payload = {
            'user_id': user.user_id,
            'username': user.username,
            'role': user.role.value,
            'permissions': [p.value for p in user.permissions],
            'exp': datetime.utcnow() + timedelta(hours=self.token_expiry_hours),
            'iat': datetime.utcnow()
        }
        
        return jwt.encode(payload, self.secret_key, algorithm='HS256')
    
    def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """验证JWT Token"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=['HS256'])
            
            user_id = payload.get('user_id')
            user = self.users.get(user_id)
            if not user or not user.is_active:
                return None
            
            return payload
            
        except jwt.ExpiredSignatureError:
            logger.warning("Token已过期")
            return None
        except jwt.InvalidTokenError as e:
            logger.warning(f"Token无效: {e}")
            return None
    
    def generate_api_key(self, user_id: str) -> Optional[str]:
        """生成API密钥"""
        try:
            user = self.users.get(user_id)
            if not user:
                raise ValueError(f"用户不存在: {user_id}")
            
            api_key = f"ak_{secrets.token_urlsafe(32)}"
            user.api_key = api_key
            self.api_keys[api_key] = user_id
            
            logger.info(f"API密钥已生成: {user.username}")
            return api_key
            
        except Exception as e:
            logger.error(f"生成API密钥失败: {e}")
            return None
    
    def verify_api_key(self, api_key: str) -> Optional[User]:
        """验证API密钥"""
        try:
            user_id = self.api_keys.get(api_key)
            if not user_id:
                return None
            
            user = self.users.get(user_id)
            if not user or not user.is_active:
                return None
            
            return user
            
        except Exception as e:
            logger.error(f"API密钥验证失败: {e}")
            return None
    
    def has_permission(self, user: User, permission: Permission) -> bool:
        """检查用户权限"""
        return permission in user.permissions
    
    def get_user_by_id(self, user_id: str) -> Optional[User]:
        """根据ID获取用户"""
        return self.users.get(user_id)
    
    def list_users(self) -> List[User]:
        """列出所有用户"""
        return list(self.users.values())
