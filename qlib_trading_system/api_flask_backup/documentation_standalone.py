"""
独立的API文档生成系统（不依赖Flask）
Standalone API Documentation Generation System (Flask-independent)

用于测试的独立文档生成系统
"""

import json
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class HTTPMethod(Enum):
    """HTTP方法枚举"""
    GET = "GET"
    POST = "POST"
    PUT = "PUT"
    DELETE = "DELETE"
    PATCH = "PATCH"


@dataclass
class APIEndpoint:
    """API端点信息"""
    path: str
    method: HTTPMethod
    summary: str
    description: str
    parameters: List[Dict[str, Any]]
    request_body: Optional[Dict[str, Any]]
    responses: Dict[int, Dict[str, Any]]
    tags: List[str]
    requires_auth: bool
    permissions: List[str]
    
    def to_openapi_spec(self) -> Dict[str, Any]:
        """转换为OpenAPI规范"""
        spec = {
            "summary": self.summary,
            "description": self.description,
            "tags": self.tags,
            "parameters": self.parameters,
            "responses": self.responses
        }
        
        if self.request_body:
            spec["requestBody"] = self.request_body
        
        if self.requires_auth:
            spec["security"] = [{"bearerAuth": []}]
        
        return spec


class APIDocGenerator:
    """API文档生成器"""
    
    def __init__(self):
        """初始化API文档生成器"""
        self.endpoints: List[APIEndpoint] = []
        self.models: Dict[str, Dict[str, Any]] = {}
        
        logger.info("API文档生成器初始化完成")
    
    def register_endpoint(
        self,
        path: str,
        method: HTTPMethod,
        summary: str,
        description: str = "",
        parameters: List[Dict[str, Any]] = None,
        request_body: Dict[str, Any] = None,
        responses: Dict[int, Dict[str, Any]] = None,
        tags: List[str] = None,
        requires_auth: bool = True,
        permissions: List[str] = None
    ):
        """注册API端点"""
        endpoint = APIEndpoint(
            path=path,
            method=method,
            summary=summary,
            description=description,
            parameters=parameters or [],
            request_body=request_body,
            responses=responses or {200: {"description": "Success"}},
            tags=tags or ["default"],
            requires_auth=requires_auth,
            permissions=permissions or []
        )
        
        self.endpoints.append(endpoint)
        logger.info(f"注册API端点: {method.value} {path}")
    
    def register_model(self, name: str, model_spec: Dict[str, Any]):
        """注册数据模型"""
        self.models[name] = model_spec
        logger.info(f"注册数据模型: {name}")
    
    def generate_openapi_spec(self) -> Dict[str, Any]:
        """生成OpenAPI规范"""
        spec = {
            "openapi": "3.0.0",
            "info": {
                "title": "Qlib Trading System API",
                "version": "1.0.0",
                "description": "量化交易系统API接口文档"
            },
            "servers": [
                {
                    "url": "/api/v1",
                    "description": "API服务器"
                }
            ],
            "components": {
                "securitySchemes": {
                    "bearerAuth": {
                        "type": "http",
                        "scheme": "bearer",
                        "bearerFormat": "JWT"
                    },
                    "apiKey": {
                        "type": "apiKey",
                        "in": "header",
                        "name": "X-API-Key"
                    }
                },
                "schemas": self.models
            },
            "paths": {}
        }
        
        # 添加端点
        for endpoint in self.endpoints:
            if endpoint.path not in spec["paths"]:
                spec["paths"][endpoint.path] = {}
            
            spec["paths"][endpoint.path][endpoint.method.value.lower()] = endpoint.to_openapi_spec()
        
        return spec
    
    def generate_postman_collection(self) -> Dict[str, Any]:
        """生成Postman集合"""
        collection = {
            "info": {
                "name": "Qlib Trading System API",
                "description": "量化交易系统API接口集合",
                "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
            },
            "auth": {
                "type": "bearer",
                "bearer": [
                    {
                        "key": "token",
                        "value": "{{jwt_token}}",
                        "type": "string"
                    }
                ]
            },
            "variable": [
                {
                    "key": "base_url",
                    "value": "http://localhost:5000/api/v1",
                    "type": "string"
                },
                {
                    "key": "jwt_token",
                    "value": "",
                    "type": "string"
                },
                {
                    "key": "api_key",
                    "value": "",
                    "type": "string"
                }
            ],
            "item": []
        }
        
        # 按标签分组
        tag_groups = {}
        for endpoint in self.endpoints:
            for tag in endpoint.tags:
                if tag not in tag_groups:
                    tag_groups[tag] = []
                tag_groups[tag].append(endpoint)
        
        # 生成请求项
        for tag, endpoints in tag_groups.items():
            folder = {
                "name": tag.title(),
                "item": []
            }
            
            for endpoint in endpoints:
                request_item = {
                    "name": endpoint.summary,
                    "request": {
                        "method": endpoint.method.value,
                        "header": [],
                        "url": {
                            "raw": "{{base_url}}" + endpoint.path,
                            "host": ["{{base_url}}"],
                            "path": endpoint.path.strip('/').split('/')
                        }
                    },
                    "response": []
                }
                
                # 添加认证头
                if endpoint.requires_auth:
                    request_item["request"]["header"].append({
                        "key": "Authorization",
                        "value": "Bearer {{jwt_token}}",
                        "type": "text"
                    })
                
                # 添加请求体
                if endpoint.request_body:
                    request_item["request"]["body"] = {
                        "mode": "raw",
                        "raw": json.dumps(endpoint.request_body.get("example", {}), indent=2),
                        "options": {
                            "raw": {
                                "language": "json"
                            }
                        }
                    }
                    request_item["request"]["header"].append({
                        "key": "Content-Type",
                        "value": "application/json",
                        "type": "text"
                    })
                
                folder["item"].append(request_item)
            
            collection["item"].append(folder)
        
        return collection
    
    def generate_curl_examples(self) -> Dict[str, str]:
        """生成cURL示例"""
        examples = {}
        
        for endpoint in self.endpoints:
            curl_cmd = f"curl -X {endpoint.method.value}"
            
            # 添加认证头
            if endpoint.requires_auth:
                curl_cmd += " -H 'Authorization: Bearer YOUR_JWT_TOKEN'"
            
            # 添加Content-Type头
            if endpoint.request_body:
                curl_cmd += " -H 'Content-Type: application/json'"
            
            # 添加请求体
            if endpoint.request_body:
                example_data = endpoint.request_body.get("example", {})
                curl_cmd += f" -d '{json.dumps(example_data)}'"
            
            # 添加URL
            curl_cmd += f" 'http://localhost:5000/api/v1{endpoint.path}'"
            
            key = f"{endpoint.method.value} {endpoint.path}"
            examples[key] = curl_cmd
        
        return examples
    
    def create_test_client_code(self, language: str = "python") -> str:
        """生成测试客户端代码"""
        if language.lower() == "python":
            return self._generate_python_client()
        elif language.lower() == "javascript":
            return self._generate_javascript_client()
        else:
            raise ValueError(f"不支持的语言: {language}")
    
    def _generate_python_client(self) -> str:
        """生成Python客户端代码"""
        code = '''
import requests
import json

class TradingSystemAPIClient:
    """交易系统API客户端"""
    
    def __init__(self, base_url="http://localhost:5000/api/v1", token=None, api_key=None):
        self.base_url = base_url
        self.token = token
        self.api_key = api_key
        self.session = requests.Session()
        
        # 设置认证头
        if token:
            self.session.headers.update({"Authorization": f"Bearer {token}"})
        elif api_key:
            self.session.headers.update({"X-API-Key": api_key})
    
    def login(self, username, password):
        """用户登录"""
        response = self.session.post(
            f"{self.base_url}/auth/login",
            json={"username": username, "password": password}
        )
        if response.status_code == 200:
            data = response.json()
            self.token = data.get("token")
            self.session.headers.update({"Authorization": f"Bearer {self.token}"})
        return response
    
    def get(self, endpoint, params=None):
        """GET请求"""
        return self.session.get(f"{self.base_url}{endpoint}", params=params)
    
    def post(self, endpoint, data=None):
        """POST请求"""
        return self.session.post(f"{self.base_url}{endpoint}", json=data)
    
    def put(self, endpoint, data=None):
        """PUT请求"""
        return self.session.put(f"{self.base_url}{endpoint}", json=data)
    
    def delete(self, endpoint):
        """DELETE请求"""
        return self.session.delete(f"{self.base_url}{endpoint}")
'''
        return code.strip()
    
    def _generate_javascript_client(self) -> str:
        """生成JavaScript客户端代码"""
        code = '''
class TradingSystemAPIClient {
    constructor(baseUrl = 'http://localhost:5000/api/v1', token = null, apiKey = null) {
        this.baseUrl = baseUrl;
        this.token = token;
        this.apiKey = apiKey;
    }
    
    getHeaders() {
        const headers = {
            'Content-Type': 'application/json'
        };
        
        if (this.token) {
            headers['Authorization'] = `Bearer ${this.token}`;
        } else if (this.apiKey) {
            headers['X-API-Key'] = this.apiKey;
        }
        
        return headers;
    }
    
    async login(username, password) {
        const response = await fetch(`${this.baseUrl}/auth/login`, {
            method: 'POST',
            headers: this.getHeaders(),
            body: JSON.stringify({ username, password })
        });
        
        if (response.ok) {
            const data = await response.json();
            this.token = data.token;
        }
        
        return response;
    }
    
    async get(endpoint, params = null) {
        let url = `${this.baseUrl}${endpoint}`;
        if (params) {
            const searchParams = new URLSearchParams(params);
            url += `?${searchParams}`;
        }
        
        return fetch(url, {
            method: 'GET',
            headers: this.getHeaders()
        });
    }
    
    async post(endpoint, data = null) {
        return fetch(`${this.baseUrl}${endpoint}`, {
            method: 'POST',
            headers: this.getHeaders(),
            body: data ? JSON.stringify(data) : null
        });
    }
}
'''
        return code.strip()
    
    def export_documentation(self, format: str = "json") -> str:
        """导出文档"""
        if format.lower() == "json":
            return json.dumps(self.generate_openapi_spec(), indent=2, ensure_ascii=False)
        elif format.lower() == "postman":
            return json.dumps(self.generate_postman_collection(), indent=2, ensure_ascii=False)
        else:
            raise ValueError(f"不支持的格式: {format}")
