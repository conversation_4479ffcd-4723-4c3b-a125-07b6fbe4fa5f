/**
 * Qlib交易系统监控仪表板JavaScript
 * Dashboard JavaScript for Qlib Trading System
 */

class TradingDashboard {
    constructor() {
        this.socket = null;
        this.pnlChart = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.updateInterval = null;
        
        // 初始化
        this.init();
    }
    
    /**
     * 初始化仪表板
     */
    init() {
        console.log('初始化交易监控仪表板...');
        
        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.setupDashboard();
            });
        } else {
            this.setupDashboard();
        }
    }
    
    /**
     * 设置仪表板
     */
    setupDashboard() {
        this.initializeWebSocket();
        this.initializeCharts();
        this.loadInitialData();
        this.setupEventListeners();
        this.startPeriodicUpdates();
    }
    
    /**
     * 初始化WebSocket连接
     */
    initializeWebSocket() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws`;
        
        console.log(`连接WebSocket: ${wsUrl}`);
        
        this.socket = new WebSocket(wsUrl);
        
        this.socket.onopen = (event) => {
            console.log('WebSocket连接已建立');
            this.isConnected = true;
            this.reconnectAttempts = 0;
            this.updateConnectionStatus('connected');
        };
        
        this.socket.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                this.handleWebSocketMessage(data);
            } catch (error) {
                console.error('解析WebSocket消息失败:', error);
            }
        };
        
        this.socket.onclose = (event) => {
            console.log('WebSocket连接已关闭');
            this.isConnected = false;
            this.updateConnectionStatus('disconnected');
            this.attemptReconnect();
        };
        
        this.socket.onerror = (error) => {
            console.error('WebSocket错误:', error);
            this.updateConnectionStatus('error');
        };
    }
    
    /**
     * 处理WebSocket消息
     */
    handleWebSocketMessage(data) {
        switch (data.type) {
            case 'metrics_update':
                this.updateMetrics(data.data);
                if (data.anomalies && data.anomalies.length > 0) {
                    this.updateAnomalies(data.anomalies);
                }
                break;
            case 'alert':
                this.handleAlert(data.alert);
                break;
            case 'system_status':
                this.updateSystemStatus(data.status);
                break;
            default:
                console.log('未知的WebSocket消息类型:', data.type);
        }
    }
    
    /**
     * 尝试重连WebSocket
     */
    attemptReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            const delay = Math.min(3000 * Math.pow(2, this.reconnectAttempts), 30000);
            
            setTimeout(() => {
                this.reconnectAttempts++;
                console.log(`尝试重连WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
                this.initializeWebSocket();
            }, delay);
        } else {
            console.error('WebSocket重连失败，已达到最大重试次数');
            this.updateConnectionStatus('failed');
        }
    }
    
    /**
     * 更新连接状态显示
     */
    updateConnectionStatus(status) {
        const statusDot = document.getElementById('connectionStatus');
        const statusText = document.getElementById('connectionText');
        
        if (!statusDot || !statusText) return;
        
        statusDot.className = 'status-dot';
        
        switch (status) {
            case 'connected':
                statusDot.classList.add('connected');
                statusText.textContent = '已连接';
                break;
            case 'disconnected':
                statusDot.classList.add('warning');
                statusText.textContent = '连接断开';
                break;
            case 'error':
                statusDot.classList.add('error');
                statusText.textContent = '连接错误';
                break;
            case 'failed':
                statusDot.classList.add('error');
                statusText.textContent = '连接失败';
                break;
            default:
                statusText.textContent = '连接中...';
        }
    }
    
    /**
     * 初始化图表
     */
    initializeCharts() {
        const ctx = document.getElementById('pnlChart');
        if (!ctx) {
            console.error('找不到图表画布元素');
            return;
        }
        
        this.pnlChart = new Chart(ctx.getContext('2d'), {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: '总盈亏',
                    data: [],
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4,
                    pointRadius: 2,
                    pointHoverRadius: 5
                }, {
                    label: '日盈亏',
                    data: [],
                    borderColor: '#27ae60',
                    backgroundColor: 'rgba(39, 174, 96, 0.1)',
                    borderWidth: 2,
                    fill: false,
                    tension: 0.4,
                    pointRadius: 2,
                    pointHoverRadius: 5
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                scales: {
                    x: {
                        type: 'time',
                        time: {
                            unit: 'minute',
                            displayFormats: {
                                minute: 'HH:mm'
                            }
                        },
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    },
                    y: {
                        beginAtZero: false,
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        },
                        ticks: {
                            callback: function(value) {
                                return '¥' + value.toLocaleString('zh-CN');
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': ¥' + 
                                       context.parsed.y.toLocaleString('zh-CN', {
                                           minimumFractionDigits: 2,
                                           maximumFractionDigits: 2
                                       });
                            }
                        }
                    }
                },
                animation: {
                    duration: 750,
                    easing: 'easeInOutQuart'
                }
            }
        });
    }
    
    /**
     * 加载初始数据
     */
    async loadInitialData() {
        try {
            console.log('加载初始数据...');
            
            // 并行加载所有数据
            const [metricsResponse, historyResponse, alertsResponse, anomaliesResponse] = await Promise.all([
                fetch('/api/metrics/current'),
                fetch('/api/metrics/history?minutes=60'),
                fetch('/api/alerts/recent?minutes=60'),
                fetch('/api/anomalies/recent?minutes=60')
            ]);
            
            // 处理当前指标
            if (metricsResponse.ok) {
                const metrics = await metricsResponse.json();
                this.updateMetrics(metrics);
            }
            
            // 处理历史数据
            if (historyResponse.ok) {
                const history = await historyResponse.json();
                this.updateChart(history);
            }
            
            // 处理报警信息
            if (alertsResponse.ok) {
                const alerts = await alertsResponse.json();
                this.updateAlerts(alerts);
            }
            
            // 处理异常事件
            if (anomaliesResponse.ok) {
                const anomalies = await anomaliesResponse.json();
                this.updateAnomaliesDisplay(anomalies);
            }
            
            console.log('初始数据加载完成');
            
        } catch (error) {
            console.error('加载初始数据失败:', error);
            this.showError('加载数据失败，请刷新页面重试');
        }
    }
    
    /**
     * 更新指标显示
     */
    updateMetrics(metrics) {
        try {
            // 盈亏指标
            this.updateElement('totalPnl', this.formatCurrency(metrics.total_pnl), this.getValueClass(metrics.total_pnl));
            this.updateElement('dailyPnl', this.formatCurrency(metrics.daily_pnl), this.getValueClass(metrics.daily_pnl));
            this.updateElement('unrealizedPnl', this.formatCurrency(metrics.unrealized_pnl));
            
            // 仓位信息
            this.updateElement('positionValue', this.formatCurrency(metrics.total_position_value));
            this.updateElement('cashBalance', this.formatCurrency(metrics.cash_balance));
            this.updateElement('positionCount', metrics.position_count);
            this.updateElement('leverageRatio', metrics.leverage_ratio.toFixed(1) + 'x');
            
            // 交易统计
            this.updateElement('dailyTrades', metrics.daily_trades);
            this.updateElement('winRate', (metrics.win_rate * 100).toFixed(1) + '%');
            this.updateElement('avgProfit', this.formatCurrency(metrics.avg_profit_per_trade));
            
            // 风险指标
            const drawdownClass = metrics.max_drawdown > 0.05 ? 'negative' : 'neutral';
            this.updateElement('maxDrawdown', (metrics.max_drawdown * 100).toFixed(2) + '%', drawdownClass);
            this.updateElement('var1d', this.formatCurrency(metrics.var_1d));
            this.updateElement('volatility', (metrics.volatility * 100).toFixed(2) + '%');
            this.updateElement('sharpeRatio', metrics.sharpe_ratio.toFixed(2));
            
            // 系统性能
            this.updateSystemMetrics(metrics);
            
            // 更新最后更新时间
            this.updateLastUpdateTime();
            
        } catch (error) {
            console.error('更新指标显示失败:', error);
        }
    }
    
    /**
     * 更新系统性能指标
     */
    updateSystemMetrics(metrics) {
        this.updateProgressBar('cpuProgress', 'cpuUsage', metrics.cpu_usage, '%');
        this.updateProgressBar('memoryProgress', 'memoryUsage', metrics.memory_usage, '%');
        this.updateProgressBar('latencyProgress', 'networkLatency', metrics.network_latency, 'ms', 100);
    }
    
    /**
     * 更新进度条
     */
    updateProgressBar(progressId, textId, value, unit, maxValue = 100) {
        const progressBar = document.getElementById(progressId);
        const textElement = document.getElementById(textId);
        
        if (!progressBar || !textElement) return;
        
        const percentage = Math.min((value / maxValue) * 100, 100);
        progressBar.style.width = percentage + '%';
        
        // 根据值设置颜色
        progressBar.className = 'progress-fill';
        if (percentage > 80) {
            progressBar.classList.add('danger');
        } else if (percentage > 60) {
            progressBar.classList.add('warning');
        }
        
        textElement.textContent = value.toFixed(1) + unit;
    }
    
    /**
     * 更新图表
     */
    updateChart(history) {
        if (!this.pnlChart || !history || history.length === 0) return;
        
        try {
            const labels = history.map(item => new Date(item.timestamp));
            const totalPnlData = history.map(item => item.total_pnl);
            const dailyPnlData = history.map(item => item.daily_pnl);
            
            this.pnlChart.data.labels = labels;
            this.pnlChart.data.datasets[0].data = totalPnlData;
            this.pnlChart.data.datasets[1].data = dailyPnlData;
            this.pnlChart.update('none'); // 无动画更新以提高性能
            
        } catch (error) {
            console.error('更新图表失败:', error);
        }
    }
    
    /**
     * 更新报警信息
     */
    updateAlerts(alerts) {
        const container = document.getElementById('alertsContainer');
        if (!container) return;
        
        if (!alerts || alerts.length === 0) {
            container.innerHTML = '<div class="empty-state">暂无报警信息</div>';
            return;
        }
        
        const alertsHtml = alerts.map(alert => `
            <div class="alert-item ${alert.level.toLowerCase()}">
                <div class="alert-time">${this.formatTime(alert.timestamp)}</div>
                <div class="alert-message">
                    <strong>[${alert.level}] ${alert.category}</strong><br>
                    ${alert.message}
                </div>
            </div>
        `).join('');
        
        container.innerHTML = alertsHtml;
    }
    
    /**
     * 更新异常事件显示（实时）
     */
    updateAnomalies(anomalies) {
        if (!anomalies || anomalies.length === 0) return;
        
        const container = document.getElementById('anomaliesContainer');
        if (!container) return;
        
        const currentContent = container.innerHTML;
        
        // 添加新的异常事件到顶部
        const newAnomaliesHtml = anomalies.map(anomaly => `
            <div class="anomaly-item ${anomaly.severity.toLowerCase()}">
                <div class="real-time-indicator"></div>
                <div class="alert-time">${this.formatTime(new Date().toISOString())}</div>
                <div class="alert-message">
                    <strong>[${anomaly.severity}] ${anomaly.type}</strong><br>
                    ${anomaly.message}
                </div>
            </div>
        `).join('');
        
        container.innerHTML = newAnomaliesHtml + currentContent;
        
        // 限制显示的异常事件数量
        const items = container.querySelectorAll('.anomaly-item');
        if (items.length > 10) {
            for (let i = 10; i < items.length; i++) {
                items[i].remove();
            }
        }
    }
    
    /**
     * 更新异常事件显示（历史）
     */
    updateAnomaliesDisplay(anomalies) {
        const container = document.getElementById('anomaliesContainer');
        if (!container) return;
        
        if (!anomalies || anomalies.length === 0) {
            container.innerHTML = '<div class="empty-state">暂无异常事件</div>';
            return;
        }
        
        const anomaliesHtml = anomalies.map(anomaly => `
            <div class="anomaly-item ${anomaly.severity.toLowerCase()}">
                <div class="alert-time">${this.formatTime(anomaly.timestamp)}</div>
                <div class="alert-message">
                    <strong>[${anomaly.severity}] ${anomaly.type}</strong><br>
                    ${anomaly.message}
                </div>
            </div>
        `).join('');
        
        container.innerHTML = anomaliesHtml;
    }
    
    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 刷新按钮
        const refreshButton = document.getElementById('refreshButton');
        if (refreshButton) {
            refreshButton.addEventListener('click', () => {
                this.refreshData();
            });
        }
        
        // 键盘快捷键
        document.addEventListener('keydown', (event) => {
            if (event.ctrlKey && event.key === 'r') {
                event.preventDefault();
                this.refreshData();
            }
        });
        
        // 页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pauseUpdates();
            } else {
                this.resumeUpdates();
            }
        });
        
        // 窗口焦点变化
        window.addEventListener('focus', () => {
            this.resumeUpdates();
        });
        
        window.addEventListener('blur', () => {
            this.pauseUpdates();
        });
    }
    
    /**
     * 开始定期更新
     */
    startPeriodicUpdates() {
        // 如果WebSocket连接失败，使用定期轮询作为备用
        this.updateInterval = setInterval(() => {
            if (!this.isConnected) {
                this.loadInitialData();
            }
        }, 30000); // 30秒
    }
    
    /**
     * 暂停更新
     */
    pauseUpdates() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
    }
    
    /**
     * 恢复更新
     */
    resumeUpdates() {
        if (!this.updateInterval) {
            this.startPeriodicUpdates();
            this.loadInitialData(); // 立即刷新一次数据
        }
    }
    
    /**
     * 刷新数据
     */
    refreshData() {
        console.log('手动刷新数据...');
        this.loadInitialData();
        this.showSuccess('数据已刷新');
    }
    
    /**
     * 工具函数
     */
    updateElement(id, value, className = '') {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
            if (className) {
                element.className = element.className.replace(/\b(positive|negative|neutral)\b/g, '');
                element.classList.add(className);
            }
        }
    }
    
    formatCurrency(value) {
        return '¥' + value.toLocaleString('zh-CN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    }
    
    getValueClass(value) {
        if (value > 0) return 'positive';
        if (value < 0) return 'negative';
        return 'neutral';
    }
    
    formatTime(timestamp) {
        return new Date(timestamp).toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });
    }
    
    updateLastUpdateTime() {
        const element = document.getElementById('lastUpdateTime');
        if (element) {
            element.textContent = new Date().toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        }
    }
    
    /**
     * 显示消息
     */
    showMessage(message, type = 'info') {
        // 创建消息元素
        const messageEl = document.createElement('div');
        messageEl.className = `message message-${type}`;
        messageEl.textContent = message;
        
        // 添加样式
        messageEl.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 6px;
            color: white;
            font-weight: 500;
            z-index: 10000;
            animation: slideInRight 0.3s ease;
        `;
        
        // 根据类型设置背景色
        switch (type) {
            case 'success':
                messageEl.style.backgroundColor = '#27ae60';
                break;
            case 'error':
                messageEl.style.backgroundColor = '#e74c3c';
                break;
            case 'warning':
                messageEl.style.backgroundColor = '#f39c12';
                break;
            default:
                messageEl.style.backgroundColor = '#3498db';
        }
        
        document.body.appendChild(messageEl);
        
        // 3秒后自动移除
        setTimeout(() => {
            messageEl.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                if (messageEl.parentNode) {
                    messageEl.parentNode.removeChild(messageEl);
                }
            }, 300);
        }, 3000);
    }
    
    showSuccess(message) {
        this.showMessage(message, 'success');
    }
    
    showError(message) {
        this.showMessage(message, 'error');
    }
    
    showWarning(message) {
        this.showMessage(message, 'warning');
    }
    
    /**
     * 处理报警
     */
    handleAlert(alert) {
        console.log('收到报警:', alert);
        
        // 显示报警通知
        this.showMessage(`[${alert.level}] ${alert.message}`, 
                        alert.level.toLowerCase() === 'critical' ? 'error' : 'warning');
        
        // 更新报警列表
        this.loadInitialData();
    }
    
    /**
     * 更新系统状态
     */
    updateSystemStatus(status) {
        console.log('系统状态更新:', status);
        
        // 根据状态更新UI
        const statusIndicator = document.getElementById('systemStatus');
        if (statusIndicator) {
            statusIndicator.textContent = status.message || status.status;
            statusIndicator.className = `status-${status.status.toLowerCase()}`;
        }
    }
    
    /**
     * 销毁仪表板
     */
    destroy() {
        if (this.socket) {
            this.socket.close();
        }
        
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
        
        if (this.pnlChart) {
            this.pnlChart.destroy();
        }
        
        console.log('仪表板已销毁');
    }
}

// 全局实例
let dashboard = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    dashboard = new TradingDashboard();
});

// 页面卸载时清理
window.addEventListener('beforeunload', function() {
    if (dashboard) {
        dashboard.destroy();
    }
});

// 导出到全局作用域（用于调试）
window.TradingDashboard = TradingDashboard;
window.dashboard = dashboard;