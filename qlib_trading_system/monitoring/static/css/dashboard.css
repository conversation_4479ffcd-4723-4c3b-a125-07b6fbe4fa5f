/* 
Qlib交易系统监控仪表板样式
Dashboard Styles for Qlib Trading System
*/

:root {
    --primary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #17a2b8;
    --dark-color: #2c3e50;
    --light-color: #ecf0f1;
    --background-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --card-shadow: 0 4px 20px rgba(0,0,0,0.1);
    --border-radius: 12px;
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--background-gradient);
    color: #333;
    min-height: 100vh;
    line-height: 1.6;
}

/* 头部样式 */
.header {
    background: rgba(255, 255, 255, 0.95);
    padding: 1rem 2rem;
    box-shadow: var(--card-shadow);
    display: flex;
    justify-content: space-between;
    align-items: center;
    backdrop-filter: blur(10px);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header h1 {
    color: var(--dark-color);
    font-size: 1.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 20px;
    font-size: 0.9rem;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--success-color);
    animation: pulse 2s infinite;
}

.status-dot.warning {
    background: var(--warning-color);
}

.status-dot.error {
    background: var(--danger-color);
}

@keyframes pulse {
    0% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(1.1); }
    100% { opacity: 1; transform: scale(1); }
}

/* 容器和网格 */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

/* 卡片样式 */
.card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--card-shadow);
    transition: var(--transition);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--light-color);
}

.card-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--dark-color);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.card-icon {
    font-size: 1.5rem;
    opacity: 0.7;
}

/* 指标显示 */
.metric-value {
    font-size: 2rem;
    font-weight: bold;
    margin: 0.5rem 0;
    transition: var(--transition);
}

.metric-value.positive {
    color: var(--success-color);
}

.metric-value.negative {
    color: var(--danger-color);
}

.metric-value.neutral {
    color: var(--dark-color);
}

.metric-label {
    color: #7f8c8d;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.metric-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0.5rem 0;
    padding: 0.25rem 0;
}

/* 图表容器 */
.chart-container {
    position: relative;
    height: 300px;
    margin-top: 1rem;
    background: rgba(248, 249, 250, 0.5);
    border-radius: 8px;
    padding: 1rem;
}

/* 系统性能指标 */
.system-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
}

.system-metric {
    text-align: center;
    padding: 1rem;
    background: rgba(248, 249, 250, 0.8);
    border-radius: 8px;
    transition: var(--transition);
}

.system-metric:hover {
    background: rgba(248, 249, 250, 1);
    transform: scale(1.02);
}

.system-metric-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.system-metric-value {
    font-size: 1.2rem;
    font-weight: bold;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

/* 进度条 */
.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--light-color);
    border-radius: 4px;
    overflow: hidden;
    margin: 0.5rem 0;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--success-color), #2ecc71);
    transition: width 0.5s ease;
    border-radius: 4px;
}

.progress-fill.warning {
    background: linear-gradient(90deg, var(--warning-color), #e67e22);
}

.progress-fill.danger {
    background: linear-gradient(90deg, var(--danger-color), #c0392b);
}

/* 报警和异常事件 */
.alerts-container {
    max-height: 400px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: #bdc3c7 transparent;
}

.alerts-container::-webkit-scrollbar {
    width: 6px;
}

.alerts-container::-webkit-scrollbar-track {
    background: transparent;
}

.alerts-container::-webkit-scrollbar-thumb {
    background: #bdc3c7;
    border-radius: 3px;
}

.alert-item, .anomaly-item {
    display: flex;
    align-items: flex-start;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    border-radius: 8px;
    border-left: 4px solid;
    transition: var(--transition);
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.alert-item:hover, .anomaly-item:hover {
    transform: translateX(5px);
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.alert-item.info, .anomaly-item.low {
    background: linear-gradient(135deg, #e8f4fd, #d4edda);
    border-color: var(--info-color);
}

.alert-item.warning, .anomaly-item.medium {
    background: linear-gradient(135deg, #fef9e7, #fff3cd);
    border-color: var(--warning-color);
}

.alert-item.error, .anomaly-item.high {
    background: linear-gradient(135deg, #fdf2f2, #f8d7da);
    border-color: var(--danger-color);
}

.alert-item.critical {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    border-color: #dc3545;
}

.alert-time {
    font-size: 0.8rem;
    color: #6c757d;
    margin-right: 1rem;
    min-width: 80px;
    font-weight: 500;
}

.alert-message {
    flex: 1;
    font-size: 0.9rem;
    line-height: 1.4;
}

.alert-message strong {
    color: var(--dark-color);
    display: block;
    margin-bottom: 0.25rem;
}

/* 实时指示器 */
.real-time-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--success-color);
    margin-right: 0.5rem;
    animation: blink 1s infinite;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

/* 按钮样式 */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: var(--transition);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: #2980b9;
    transform: translateY(-1px);
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-success:hover {
    background: #219a52;
    transform: translateY(-1px);
}

.btn-warning {
    background: var(--warning-color);
    color: white;
}

.btn-warning:hover {
    background: #e67e22;
    transform: translateY(-1px);
}

/* 连接状态 */
.connection-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    background: rgba(255, 255, 255, 0.9);
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 全宽卡片 */
.full-width {
    grid-column: 1 / -1;
}

/* 空状态 */
.empty-state {
    text-align: center;
    color: #7f8c8d;
    padding: 2rem;
    font-style: italic;
}

.empty-state::before {
    content: "📊";
    display: block;
    font-size: 2rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .container {
        padding: 1.5rem;
    }
    
    .dashboard-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1rem;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .system-metrics {
        grid-template-columns: 1fr;
    }
    
    .header {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
    }
    
    .header-controls {
        width: 100%;
        justify-content: space-between;
    }
    
    .metric-value {
        font-size: 1.5rem;
    }
    
    .card {
        padding: 1rem;
    }
    
    .chart-container {
        height: 250px;
    }
}

@media (max-width: 480px) {
    .header h1 {
        font-size: 1.4rem;
    }
    
    .card-title {
        font-size: 1rem;
    }
    
    .metric-value {
        font-size: 1.3rem;
    }
    
    .system-metric {
        padding: 0.75rem;
    }
    
    .alert-item, .anomaly-item {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .alert-time {
        margin-right: 0;
        margin-bottom: 0.25rem;
        min-width: auto;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --background-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        --card-shadow: 0 4px 20px rgba(0,0,0,0.3);
    }
    
    body {
        color: #ecf0f1;
    }
    
    .card {
        background: rgba(52, 73, 94, 0.95);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .header {
        background: rgba(44, 62, 80, 0.95);
    }
    
    .header h1 {
        color: #ecf0f1;
    }
    
    .card-title {
        color: #ecf0f1;
    }
    
    .metric-value.neutral {
        color: #ecf0f1;
    }
}

/* 打印样式 */
@media print {
    body {
        background: white;
        color: black;
    }
    
    .header {
        background: white;
        box-shadow: none;
        border-bottom: 2px solid #333;
    }
    
    .card {
        background: white;
        box-shadow: none;
        border: 1px solid #ddd;
        break-inside: avoid;
        margin-bottom: 1rem;
    }
    
    .chart-container {
        height: 200px;
    }
    
    .btn {
        display: none;
    }
}