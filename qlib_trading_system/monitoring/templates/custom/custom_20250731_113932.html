
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ template_name }}</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #007acc; padding-bottom: 20px; }
        .header h1 { color: #007acc; margin: 0; font-size: 28px; }
        .section { margin: 30px 0; }
        .section h2 { color: #007acc; border-bottom: 2px solid #e9ecef; padding-bottom: 10px; }
        .metrics-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .metric-card { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #007acc; }
        .chart-container { margin: 20px 0; text-align: center; }
        .chart-container img { max-width: 100%; height: auto; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        .footer { text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #dee2e6; color: #6c757d; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{ template_name }}</h1>
            <p>生成时间: {{ generation_time }}</p>
        </div>
        
        <div class="section">
            <h2>📊 数据概览</h2>
            <div class="metrics-grid">
                {% for key, value in data.items() %}
                <div class="metric-card">
                    <h3>{{ key }}</h3>
                    <div class="metric-value">{{ value }}</div>
                </div>
                {% endfor %}
            </div>
        </div>
        
        {% if charts %}
        <div class="section">
            <h2>📈 图表分析</h2>
            {% for chart_name, chart_path in charts.items() %}
            <div class="chart-container">
                <h3>{{ chart_name }}</h3>
                <img src="{{ chart_path }}" alt="{{ chart_name }}">
            </div>
            {% endfor %}
        </div>
        {% endif %}
        
        <div class="footer">
            <p>本报告由Qlib交易系统自动生成 | 生成时间: {{ generation_time }}</p>
        </div>
    </div>
</body>
</html>
