<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Qlib交易系统实时监控仪表板</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/moment@2.29.4/moment.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-moment@1.0.1/dist/chartjs-adapter-moment.min.js"></script>
    <link rel="stylesheet" href="/static/css/dashboard.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 1.8rem;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #27ae60;
            animation: pulse 2s infinite;
        }

        .status-dot.warning {
            background: #f39c12;
        }

        .status-dot.error {
            background: #e74c3c;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #ecf0f1;
        }

        .card-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2c3e50;
        }

        .card-icon {
            font-size: 1.5rem;
        }

        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            margin: 0.5rem 0;
        }

        .metric-value.positive {
            color: #27ae60;
        }

        .metric-value.negative {
            color: #e74c3c;
        }

        .metric-value.neutral {
            color: #34495e;
        }

        .metric-label {
            color: #7f8c8d;
            font-size: 0.9rem;
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin-top: 1rem;
        }

        .alerts-container {
            max-height: 400px;
            overflow-y: auto;
        }

        .alert-item {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            border-radius: 8px;
            border-left: 4px solid;
        }

        .alert-item.info {
            background: #e8f4fd;
            border-color: #3498db;
        }

        .alert-item.warning {
            background: #fef9e7;
            border-color: #f39c12;
        }

        .alert-item.error {
            background: #fdf2f2;
            border-color: #e74c3c;
        }

        .alert-item.critical {
            background: #f8d7da;
            border-color: #dc3545;
        }

        .anomaly-item {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            border-radius: 8px;
            border-left: 4px solid;
        }

        .anomaly-item.high {
            background: #fdf2f2;
            border-color: #e74c3c;
        }

        .anomaly-item.medium {
            background: #fef9e7;
            border-color: #f39c12;
        }

        .anomaly-item.low {
            background: #e8f4fd;
            border-color: #3498db;
        }

        .real-time-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #27ae60;
            margin-right: 0.5rem;
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }

        .alert-time {
            font-size: 0.8rem;
            color: #7f8c8d;
            margin-right: 1rem;
            min-width: 80px;
        }

        .alert-message {
            flex: 1;
            font-size: 0.9rem;
        }

        .system-metrics {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
        }

        .system-metric {
            text-align: center;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #ecf0f1;
            border-radius: 4px;
            overflow: hidden;
            margin: 0.5rem 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #27ae60, #2ecc71);
            transition: width 0.3s ease;
        }

        .progress-fill.warning {
            background: linear-gradient(90deg, #f39c12, #e67e22);
        }

        .progress-fill.danger {
            background: linear-gradient(90deg, #e74c3c, #c0392b);
        }

        .refresh-button {
            background: #3498db;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background 0.3s ease;
        }

        .refresh-button:hover {
            background: #2980b9;
        }

        .connection-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .full-width {
            grid-column: 1 / -1;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .system-metrics {
                grid-template-columns: 1fr;
            }
            
            .header {
                padding: 1rem;
                flex-direction: column;
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Qlib交易系统实时监控</h1>
        <div class="header-controls">
            <div class="status-indicator">
                <div class="status-dot" id="connectionStatus"></div>
                <span id="connectionText">连接中...</span>
            </div>
            <button class="btn btn-primary" id="refreshButton">
                🔄 刷新数据
            </button>
            <div class="connection-status">
                <span>最后更新: </span>
                <span id="lastUpdateTime">--:--</span>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="dashboard-grid">
            <!-- 盈亏概览 -->
            <div class="card">
                <div class="card-header">
                    <span class="card-title">💰 盈亏概览</span>
                    <span class="card-icon">📊</span>
                </div>
                <div class="metric-value" id="totalPnl">¥0.00</div>
                <div class="metric-label">总盈亏</div>
                <div style="margin-top: 1rem;">
                    <div style="display: flex; justify-content: space-between;">
                        <span>今日盈亏:</span>
                        <span id="dailyPnl" class="metric-value" style="font-size: 1.2rem;">¥0.00</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-top: 0.5rem;">
                        <span>未实现盈亏:</span>
                        <span id="unrealizedPnl" style="font-size: 1rem;">¥0.00</span>
                    </div>
                </div>
            </div>

            <!-- 仓位信息 -->
            <div class="card">
                <div class="card-header">
                    <span class="card-title">📈 仓位信息</span>
                    <span class="card-icon">💼</span>
                </div>
                <div class="metric-value neutral" id="positionValue">¥0.00</div>
                <div class="metric-label">总仓位价值</div>
                <div style="margin-top: 1rem;">
                    <div style="display: flex; justify-content: space-between;">
                        <span>现金余额:</span>
                        <span id="cashBalance">¥0.00</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-top: 0.5rem;">
                        <span>持仓数量:</span>
                        <span id="positionCount">0</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-top: 0.5rem;">
                        <span>杠杆倍数:</span>
                        <span id="leverageRatio">1.0x</span>
                    </div>
                </div>
            </div>

            <!-- 交易统计 -->
            <div class="card">
                <div class="card-header">
                    <span class="card-title">📋 交易统计</span>
                    <span class="card-icon">📊</span>
                </div>
                <div class="metric-value neutral" id="dailyTrades">0</div>
                <div class="metric-label">今日交易次数</div>
                <div style="margin-top: 1rem;">
                    <div style="display: flex; justify-content: space-between;">
                        <span>胜率:</span>
                        <span id="winRate">0.0%</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-top: 0.5rem;">
                        <span>平均盈利:</span>
                        <span id="avgProfit">¥0.00</span>
                    </div>
                </div>
            </div>

            <!-- 风险指标 -->
            <div class="card">
                <div class="card-header">
                    <span class="card-title">⚠️ 风险指标</span>
                    <span class="card-icon">🛡️</span>
                </div>
                <div class="metric-value" id="maxDrawdown">0.00%</div>
                <div class="metric-label">最大回撤</div>
                <div style="margin-top: 1rem;">
                    <div style="display: flex; justify-content: space-between;">
                        <span>VaR(1日):</span>
                        <span id="var1d">¥0.00</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-top: 0.5rem;">
                        <span>波动率:</span>
                        <span id="volatility">0.00%</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-top: 0.5rem;">
                        <span>夏普比率:</span>
                        <span id="sharpeRatio">0.00</span>
                    </div>
                </div>
            </div>

            <!-- 系统性能 -->
            <div class="card">
                <div class="card-header">
                    <span class="card-title">💻 系统性能</span>
                    <span class="card-icon">⚡</span>
                </div>
                <div class="system-metrics">
                    <div class="system-metric">
                        <div>CPU</div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="cpuProgress"></div>
                        </div>
                        <div id="cpuUsage">0%</div>
                    </div>
                    <div class="system-metric">
                        <div>内存</div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="memoryProgress"></div>
                        </div>
                        <div id="memoryUsage">0%</div>
                    </div>
                    <div class="system-metric">
                        <div>网络延迟</div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="latencyProgress"></div>
                        </div>
                        <div id="networkLatency">0ms</div>
                    </div>
                </div>
            </div>

            <!-- 实时报警 -->
            <div class="card">
                <div class="card-header">
                    <span class="card-title">🚨 实时报警</span>
                    <span class="card-icon">🔔</span>
                </div>
                <div class="alerts-container" id="alertsContainer">
                    <div style="text-align: center; color: #7f8c8d; padding: 2rem;">
                        暂无报警信息
                    </div>
                </div>
            </div>

            <!-- 异常事件监控 -->
            <div class="card">
                <div class="card-header">
                    <span class="card-title">⚡ 异常事件</span>
                    <span class="card-icon">🔍</span>
                </div>
                <div class="alerts-container" id="anomaliesContainer">
                    <div style="text-align: center; color: #7f8c8d; padding: 2rem;">
                        暂无异常事件
                    </div>
                </div>
            </div>

            <!-- 盈亏趋势图 -->
            <div class="card full-width">
                <div class="card-header">
                    <span class="card-title">📈 盈亏趋势</span>
                    <span class="card-icon">📊</span>
                </div>
                <div class="chart-container">
                    <canvas id="pnlChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let socket = null;
        let pnlChart = null;
        let isConnected = false;
        let reconnectAttempts = 0;
        const maxReconnectAttempts = 5;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeWebSocket();
            initializeCharts();
            loadInitialData();
        });

        // WebSocket连接
        function initializeWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws`;
            
            socket = new WebSocket(wsUrl);
            
            socket.onopen = function(event) {
                console.log('WebSocket连接已建立');
                isConnected = true;
                reconnectAttempts = 0;
                updateConnectionStatus('connected');
            };
            
            socket.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    if (data.type === 'metrics_update') {
                        updateMetrics(data.data);
                        
                        // 处理异常事件
                        if (data.anomalies && data.anomalies.length > 0) {
                            updateAnomalies(data.anomalies);
                        }
                    }
                } catch (error) {
                    console.error('解析WebSocket消息失败:', error);
                }
            };
            
            socket.onclose = function(event) {
                console.log('WebSocket连接已关闭');
                isConnected = false;
                updateConnectionStatus('disconnected');
                
                // 自动重连
                if (reconnectAttempts < maxReconnectAttempts) {
                    setTimeout(() => {
                        reconnectAttempts++;
                        console.log(`尝试重连 (${reconnectAttempts}/${maxReconnectAttempts})`);
                        initializeWebSocket();
                    }, 3000 * reconnectAttempts);
                }
            };
            
            socket.onerror = function(error) {
                console.error('WebSocket错误:', error);
                updateConnectionStatus('error');
            };
        }

        // 更新连接状态
        function updateConnectionStatus(status) {
            const statusDot = document.getElementById('connectionStatus');
            const statusText = document.getElementById('connectionText');
            
            statusDot.className = 'status-dot';
            
            switch (status) {
                case 'connected':
                    statusDot.classList.add('connected');
                    statusText.textContent = '已连接';
                    break;
                case 'disconnected':
                    statusDot.classList.add('warning');
                    statusText.textContent = '连接断开';
                    break;
                case 'error':
                    statusDot.classList.add('error');
                    statusText.textContent = '连接错误';
                    break;
                default:
                    statusText.textContent = '连接中...';
            }
        }

        // 初始化图表
        function initializeCharts() {
            const ctx = document.getElementById('pnlChart').getContext('2d');
            pnlChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '总盈亏',
                        data: [],
                        borderColor: '#3498db',
                        backgroundColor: 'rgba(52, 152, 219, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4
                    }, {
                        label: '日盈亏',
                        data: [],
                        borderColor: '#27ae60',
                        backgroundColor: 'rgba(39, 174, 96, 0.1)',
                        borderWidth: 2,
                        fill: false,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            type: 'time',
                            time: {
                                unit: 'minute',
                                displayFormats: {
                                    minute: 'HH:mm'
                                }
                            }
                        },
                        y: {
                            beginAtZero: false
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    }
                }
            });
        }

        // 加载初始数据
        async function loadInitialData() {
            try {
                // 加载当前指标
                const metricsResponse = await fetch('/api/metrics/current');
                if (metricsResponse.ok) {
                    const metrics = await metricsResponse.json();
                    updateMetrics(metrics);
                }

                // 加载历史数据
                const historyResponse = await fetch('/api/metrics/history?minutes=60');
                if (historyResponse.ok) {
                    const history = await historyResponse.json();
                    updateChart(history);
                }

                // 加载报警信息
                const alertsResponse = await fetch('/api/alerts/recent?minutes=60');
                if (alertsResponse.ok) {
                    const alerts = await alertsResponse.json();
                    updateAlerts(alerts);
                }

                // 加载异常事件
                const anomaliesResponse = await fetch('/api/anomalies/recent?minutes=60');
                if (anomaliesResponse.ok) {
                    const anomalies = await anomaliesResponse.json();
                    updateAnomaliesDisplay(anomalies);
                }
            } catch (error) {
                console.error('加载初始数据失败:', error);
            }
        }

        // 更新指标显示
        function updateMetrics(metrics) {
            // 盈亏指标
            updateElement('totalPnl', formatCurrency(metrics.total_pnl), getValueClass(metrics.total_pnl));
            updateElement('dailyPnl', formatCurrency(metrics.daily_pnl), getValueClass(metrics.daily_pnl));
            updateElement('unrealizedPnl', formatCurrency(metrics.unrealized_pnl));
            
            // 仓位信息
            updateElement('positionValue', formatCurrency(metrics.total_position_value));
            updateElement('cashBalance', formatCurrency(metrics.cash_balance));
            updateElement('positionCount', metrics.position_count);
            updateElement('leverageRatio', metrics.leverage_ratio.toFixed(1) + 'x');
            
            // 交易统计
            updateElement('dailyTrades', metrics.daily_trades);
            updateElement('winRate', (metrics.win_rate * 100).toFixed(1) + '%');
            updateElement('avgProfit', formatCurrency(metrics.avg_profit_per_trade));
            
            // 风险指标
            const drawdownClass = metrics.max_drawdown > 0.05 ? 'negative' : 'neutral';
            updateElement('maxDrawdown', (metrics.max_drawdown * 100).toFixed(2) + '%', drawdownClass);
            updateElement('var1d', formatCurrency(metrics.var_1d));
            updateElement('volatility', (metrics.volatility * 100).toFixed(2) + '%');
            updateElement('sharpeRatio', metrics.sharpe_ratio.toFixed(2));
            
            // 系统性能
            updateSystemMetrics(metrics);
            
            // 更新最后更新时间
            updateLastUpdateTime();
        }

        // 更新系统性能指标
        function updateSystemMetrics(metrics) {
            updateProgressBar('cpuProgress', 'cpuUsage', metrics.cpu_usage, '%');
            updateProgressBar('memoryProgress', 'memoryUsage', metrics.memory_usage, '%');
            updateProgressBar('latencyProgress', 'networkLatency', metrics.network_latency, 'ms', 100);
        }

        // 更新进度条
        function updateProgressBar(progressId, textId, value, unit, maxValue = 100) {
            const progressBar = document.getElementById(progressId);
            const textElement = document.getElementById(textId);
            
            const percentage = Math.min((value / maxValue) * 100, 100);
            progressBar.style.width = percentage + '%';
            
            // 根据值设置颜色
            progressBar.className = 'progress-fill';
            if (percentage > 80) {
                progressBar.classList.add('danger');
            } else if (percentage > 60) {
                progressBar.classList.add('warning');
            }
            
            textElement.textContent = value.toFixed(1) + unit;
        }

        // 更新图表
        function updateChart(history) {
            if (!pnlChart || !history || history.length === 0) return;
            
            const labels = history.map(item => new Date(item.timestamp));
            const totalPnlData = history.map(item => item.total_pnl);
            const dailyPnlData = history.map(item => item.daily_pnl);
            
            pnlChart.data.labels = labels;
            pnlChart.data.datasets[0].data = totalPnlData;
            pnlChart.data.datasets[1].data = dailyPnlData;
            pnlChart.update();
        }

        // 更新报警信息
        function updateAlerts(alerts) {
            const container = document.getElementById('alertsContainer');
            
            if (!alerts || alerts.length === 0) {
                container.innerHTML = '<div style="text-align: center; color: #7f8c8d; padding: 2rem;">暂无报警信息</div>';
                return;
            }
            
            const alertsHtml = alerts.map(alert => `
                <div class="alert-item ${alert.level.toLowerCase()}">
                    <div class="alert-time">${formatTime(alert.timestamp)}</div>
                    <div class="alert-message">
                        <strong>[${alert.level}] ${alert.category}</strong><br>
                        ${alert.message}
                    </div>
                </div>
            `).join('');
            
            container.innerHTML = alertsHtml;
        }

        // 工具函数
        function updateElement(id, value, className = '') {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
                if (className) {
                    element.className = element.className.replace(/\b(positive|negative|neutral)\b/g, '');
                    element.classList.add(className);
                }
            }
        }

        function formatCurrency(value) {
            return '¥' + value.toLocaleString('zh-CN', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        }

        function getValueClass(value) {
            if (value > 0) return 'positive';
            if (value < 0) return 'negative';
            return 'neutral';
        }

        function formatTime(timestamp) {
            return new Date(timestamp).toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        function updateLastUpdateTime() {
            const element = document.getElementById('lastUpdateTime');
            if (element) {
                element.textContent = new Date().toLocaleTimeString('zh-CN', {
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });
            }
        }

        // 更新异常事件显示（实时）
        function updateAnomalies(anomalies) {
            if (!anomalies || anomalies.length === 0) return;
            
            const container = document.getElementById('anomaliesContainer');
            const currentContent = container.innerHTML;
            
            // 添加新的异常事件到顶部
            const newAnomaliesHtml = anomalies.map(anomaly => `
                <div class="anomaly-item ${anomaly.severity.toLowerCase()}">
                    <div class="real-time-indicator"></div>
                    <div class="alert-time">${formatTime(new Date().toISOString())}</div>
                    <div class="alert-message">
                        <strong>[${anomaly.severity}] ${anomaly.type}</strong><br>
                        ${anomaly.message}
                    </div>
                </div>
            `).join('');
            
            container.innerHTML = newAnomaliesHtml + currentContent;
            
            // 限制显示的异常事件数量
            const items = container.querySelectorAll('.anomaly-item');
            if (items.length > 10) {
                for (let i = 10; i < items.length; i++) {
                    items[i].remove();
                }
            }
        }

        // 更新异常事件显示（历史）
        function updateAnomaliesDisplay(anomalies) {
            const container = document.getElementById('anomaliesContainer');
            
            if (!anomalies || anomalies.length === 0) {
                container.innerHTML = '<div style="text-align: center; color: #7f8c8d; padding: 2rem;">暂无异常事件</div>';
                return;
            }
            
            const anomaliesHtml = anomalies.map(anomaly => `
                <div class="anomaly-item ${anomaly.severity.toLowerCase()}">
                    <div class="alert-time">${formatTime(anomaly.timestamp)}</div>
                    <div class="alert-message">
                        <strong>[${anomaly.severity}] ${anomaly.type}</strong><br>
                        ${anomaly.message}
                    </div>
                </div>
            `).join('');
            
            container.innerHTML = anomaliesHtml;
        }

        // 刷新数据
        function refreshData() {
            loadInitialData();
        }

        // 定期刷新数据（备用机制）
        setInterval(() => {
            if (!isConnected) {
                loadInitialData();
            }
        }, 30000); // 30秒
    </script>
</body>
</html>