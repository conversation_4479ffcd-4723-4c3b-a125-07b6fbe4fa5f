"""
实时监控仪表板 - 提供Web界面的实时交易监控
Real-time Dashboard - Provides web interface for real-time trading monitoring
"""

import json
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Request
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
import uvicorn
from .metrics_collector import MetricsCollector, TradingMetrics, AlertEvent
from ..utils.logging.logger import get_logger

class AnomalyDetector:
    """异常事件检测器"""
    
    def __init__(self):
        self.baseline_metrics = {}
        self.anomaly_threshold = 2.0  # 标准差倍数
        
    def detect_anomalies(self, current_metrics: TradingMetrics, history: list) -> list:
        """检测异常事件"""
        anomalies = []
        
        if len(history) < 10:  # 需要足够的历史数据
            return anomalies
        
        # 检测PnL异常波动
        pnl_values = [m.total_pnl for m in history[-20:]]
        pnl_mean = sum(pnl_values) / len(pnl_values)
        pnl_std = (sum((x - pnl_mean) ** 2 for x in pnl_values) / len(pnl_values)) ** 0.5
        
        if abs(current_metrics.total_pnl - pnl_mean) > self.anomaly_threshold * pnl_std:
            anomalies.append({
                'type': 'PNL_ANOMALY',
                'message': f'PnL异常波动: 当前{current_metrics.total_pnl:.2f}, 均值{pnl_mean:.2f}',
                'severity': 'HIGH' if abs(current_metrics.total_pnl - pnl_mean) > 3 * pnl_std else 'MEDIUM'
            })
        
        # 检测交易频率异常
        trade_counts = [m.daily_trades for m in history[-10:]]
        trade_mean = sum(trade_counts) / len(trade_counts)
        
        if current_metrics.daily_trades > trade_mean * 2:
            anomalies.append({
                'type': 'TRADING_FREQUENCY_ANOMALY',
                'message': f'交易频率异常: 当前{current_metrics.daily_trades}, 平均{trade_mean:.1f}',
                'severity': 'MEDIUM'
            })
        
        # 检测系统性能异常
        if current_metrics.cpu_usage > 90:
            anomalies.append({
                'type': 'SYSTEM_PERFORMANCE_ANOMALY',
                'message': f'CPU使用率过高: {current_metrics.cpu_usage:.1f}%',
                'severity': 'HIGH'
            })
        
        if current_metrics.memory_usage > 95:
            anomalies.append({
                'type': 'SYSTEM_PERFORMANCE_ANOMALY',
                'message': f'内存使用率过高: {current_metrics.memory_usage:.1f}%',
                'severity': 'CRITICAL'
            })
        
        return anomalies

logger = get_logger(__name__)

class WebSocketManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        self.active_connections: List[WebSocket] = []
    
    async def connect(self, websocket: WebSocket):
        """接受WebSocket连接"""
        await websocket.accept()
        self.active_connections.append(websocket)
        logger.info(f"WebSocket连接已建立，当前连接数: {len(self.active_connections)}")
    
    def disconnect(self, websocket: WebSocket):
        """断开WebSocket连接"""
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        logger.info(f"WebSocket连接已断开，当前连接数: {len(self.active_connections)}")
    
    async def send_personal_message(self, message: str, websocket: WebSocket):
        """发送个人消息"""
        try:
            await websocket.send_text(message)
        except Exception as e:
            logger.error(f"发送WebSocket消息失败: {e}")
            self.disconnect(websocket)
    
    async def broadcast(self, message: str):
        """广播消息给所有连接"""
        disconnected = []
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except Exception as e:
                logger.error(f"广播消息失败: {e}")
                disconnected.append(connection)
        
        # 清理断开的连接
        for conn in disconnected:
            self.disconnect(conn)

class RealTimeDashboard:
    """实时监控仪表板"""
    
    def __init__(self, metrics_collector: MetricsCollector, config: Dict = None):
        """
        初始化仪表板
        
        Args:
            metrics_collector: 指标收集器实例
            config: 配置参数
        """
        self.metrics_collector = metrics_collector
        self.config = config or {}
        
        # FastAPI应用
        self.app = FastAPI(title="Qlib交易系统监控仪表板", version="1.0.0")
        
        # WebSocket管理器
        self.websocket_manager = WebSocketManager()
        
        # 模板引擎
        self.templates = Jinja2Templates(directory="qlib_trading_system/monitoring/templates")
        
        # 静态文件
        self.app.mount("/static", StaticFiles(directory="qlib_trading_system/monitoring/static"), name="static")
        
        # 配置参数
        self.host = self.config.get('host', '0.0.0.0')
        self.port = self.config.get('port', 8080)
        self.update_interval = self.config.get('update_interval', 1.0)  # 秒
        
        # 设置路由
        self._setup_routes()
        
        # 订阅指标更新
        self.metrics_collector.subscribe(self._on_metrics_update)
        
        # 异常事件监控
        self.anomaly_detector = AnomalyDetector()
        self.event_history = []
        
        logger.info("实时监控仪表板初始化完成")
    
    def _setup_routes(self):
        """设置API路由"""
        
        @self.app.get("/", response_class=HTMLResponse)
        async def dashboard_home(request: Request):
            """仪表板主页"""
            return self.templates.TemplateResponse("dashboard.html", {"request": request})
        
        @self.app.get("/api/metrics/current")
        async def get_current_metrics():
            """获取当前指标"""
            try:
                metrics = self.metrics_collector.get_current_metrics()
                metrics_dict = metrics.to_dict()
                # 转换datetime为字符串
                if 'timestamp' in metrics_dict:
                    metrics_dict['timestamp'] = metrics_dict['timestamp'].isoformat()
                return JSONResponse(content=metrics_dict)
            except Exception as e:
                logger.error(f"获取当前指标失败: {e}")
                return JSONResponse(content={"error": str(e)}, status_code=500)
        
        @self.app.get("/api/metrics/history")
        async def get_metrics_history(minutes: int = 60):
            """获取历史指标"""
            try:
                history = self.metrics_collector.get_metrics_history(minutes)
                history_data = []
                for m in history:
                    m_dict = m.to_dict()
                    # 转换datetime为字符串
                    if 'timestamp' in m_dict:
                        m_dict['timestamp'] = m_dict['timestamp'].isoformat()
                    history_data.append(m_dict)
                return JSONResponse(content=history_data)
            except Exception as e:
                logger.error(f"获取历史指标失败: {e}")
                return JSONResponse(content={"error": str(e)}, status_code=500)
        
        @self.app.get("/api/alerts/recent")
        async def get_recent_alerts(minutes: int = 60):
            """获取最近报警"""
            try:
                alerts = self.metrics_collector.get_recent_alerts(minutes)
                return JSONResponse(content=[
                    {
                        'timestamp': a.timestamp.isoformat(),
                        'level': a.level,
                        'category': a.category,
                        'message': a.message,
                        'details': a.details,
                        'resolved': a.resolved
                    } for a in alerts
                ])
            except Exception as e:
                logger.error(f"获取报警信息失败: {e}")
                return JSONResponse(content={"error": str(e)}, status_code=500)
        
        @self.app.get("/api/summary")
        async def get_summary():
            """获取汇总信息"""
            try:
                summary = self.metrics_collector.get_summary_stats()
                return JSONResponse(content=summary)
            except Exception as e:
                logger.error(f"获取汇总信息失败: {e}")
                return JSONResponse(content={"error": str(e)}, status_code=500)
        
        @self.app.websocket("/ws")
        async def websocket_endpoint(websocket: WebSocket):
            """WebSocket端点"""
            await self.websocket_manager.connect(websocket)
            try:
                while True:
                    # 保持连接活跃
                    await websocket.receive_text()
            except WebSocketDisconnect:
                self.websocket_manager.disconnect(websocket)
        
        @self.app.get("/api/anomalies/recent")
        async def get_recent_anomalies(minutes: int = 60):
            """获取最近的异常事件"""
            try:
                cutoff_time = datetime.now() - timedelta(minutes=minutes)
                recent_anomalies = [
                    event for event in self.event_history 
                    if event.get('timestamp', datetime.min) >= cutoff_time
                ]
                return JSONResponse(content=recent_anomalies)
            except Exception as e:
                logger.error(f"获取异常事件失败: {e}")
                return JSONResponse(content={"error": str(e)}, status_code=500)
        
        @self.app.get("/api/system/status")
        async def get_system_status():
            """获取系统状态"""
            try:
                current_metrics = self.metrics_collector.get_current_metrics()
                recent_alerts = self.metrics_collector.get_recent_alerts(60)
                
                # 计算系统健康状态
                critical_alerts = len([a for a in recent_alerts if a.level == 'CRITICAL'])
                error_alerts = len([a for a in recent_alerts if a.level == 'ERROR'])
                
                if critical_alerts > 0:
                    status = 'CRITICAL'
                elif error_alerts > 0:
                    status = 'ERROR'
                elif current_metrics.cpu_usage > 80 or current_metrics.memory_usage > 85:
                    status = 'WARNING'
                else:
                    status = 'HEALTHY'
                
                return JSONResponse(content={
                    'status': status,
                    'timestamp': datetime.now().isoformat(),
                    'metrics': {
                        'cpu_usage': current_metrics.cpu_usage,
                        'memory_usage': current_metrics.memory_usage,
                        'network_latency': current_metrics.network_latency
                    },
                    'alerts': {
                        'critical': critical_alerts,
                        'error': error_alerts,
                        'total': len(recent_alerts)
                    }
                })
            except Exception as e:
                logger.error(f"获取系统状态失败: {e}")
                return JSONResponse(content={"error": str(e)}, status_code=500)
        
        @self.app.get("/health")
        async def health_check():
            """健康检查"""
            return {"status": "healthy", "timestamp": datetime.now().isoformat()}
    
    async def _on_metrics_update(self, metrics: TradingMetrics):
        """指标更新回调"""
        try:
            # 检测异常事件
            history = self.metrics_collector.get_metrics_history(60)
            anomalies = self.anomaly_detector.detect_anomalies(metrics, history)
            
            # 记录异常事件
            for anomaly in anomalies:
                anomaly['timestamp'] = datetime.now()
                self.event_history.append(anomaly)
                logger.warning(f"检测到异常事件: {anomaly['message']}")
            
            # 限制事件历史长度
            if len(self.event_history) > 1000:
                self.event_history = self.event_history[-500:]
            
            # 广播最新指标给所有WebSocket连接
            message = json.dumps({
                "type": "metrics_update",
                "data": metrics.to_dict(),
                "anomalies": anomalies
            })
            await self.websocket_manager.broadcast(message)
        except Exception as e:
            logger.error(f"广播指标更新失败: {e}")
    
    def start_server(self):
        """启动仪表板服务器"""
        try:
            logger.info(f"启动仪表板服务器: http://{self.host}:{self.port}")
            uvicorn.run(
                self.app,
                host=self.host,
                port=self.port,
                log_level="info"
            )
        except Exception as e:
            logger.error(f"启动仪表板服务器失败: {e}")
            raise
    
    async def start_server_async(self):
        """异步启动仪表板服务器"""
        try:
            config = uvicorn.Config(
                self.app,
                host=self.host,
                port=self.port,
                log_level="info"
            )
            server = uvicorn.Server(config)
            await server.serve()
        except Exception as e:
            logger.error(f"异步启动仪表板服务器失败: {e}")
            raise
    
    def stop_server(self):
        """停止仪表板服务器"""
        # 这里可以添加优雅关闭逻辑
        logger.info("仪表板服务器已停止")

class DashboardManager:
    """仪表板管理器 - 统一管理仪表板和指标收集"""
    
    def __init__(self, config: Dict = None):
        """
        初始化仪表板管理器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        
        # 创建指标收集器
        metrics_config = self.config.get('metrics', {})
        self.metrics_collector = MetricsCollector(metrics_config)
        
        # 创建仪表板
        dashboard_config = self.config.get('dashboard', {})
        self.dashboard = RealTimeDashboard(self.metrics_collector, dashboard_config)
        
        logger.info("仪表板管理器初始化完成")
    
    def start(self):
        """启动监控系统"""
        try:
            # 启动指标收集
            self.metrics_collector.start_collection()
            
            # 启动仪表板服务器
            self.dashboard.start_server()
            
        except Exception as e:
            logger.error(f"启动监控系统失败: {e}")
            self.stop()
            raise
    
    async def start_async(self):
        """异步启动监控系统"""
        try:
            # 启动指标收集
            self.metrics_collector.start_collection()
            
            # 异步启动仪表板服务器
            await self.dashboard.start_server_async()
            
        except Exception as e:
            logger.error(f"异步启动监控系统失败: {e}")
            await self.stop_async()
            raise
    
    def stop(self):
        """停止监控系统"""
        try:
            # 停止指标收集
            self.metrics_collector.stop_collection()
            
            # 停止仪表板服务器
            self.dashboard.stop_server()
            
            logger.info("监控系统已停止")
            
        except Exception as e:
            logger.error(f"停止监控系统失败: {e}")
    
    async def stop_async(self):
        """异步停止监控系统"""
        try:
            # 停止指标收集
            self.metrics_collector.stop_collection()
            
            # 停止仪表板服务器
            self.dashboard.stop_server()
            
            logger.info("监控系统已停止")
            
        except Exception as e:
            logger.error(f"异步停止监控系统失败: {e}")
    
    def get_metrics_collector(self) -> MetricsCollector:
        """获取指标收集器"""
        return self.metrics_collector
    
    def get_dashboard(self) -> RealTimeDashboard:
        """获取仪表板"""
        return self.dashboard