"""
报警管理器 - 处理异常事件监控和报警通知
Alert Manager - Handles exception event monitoring and alert notifications
"""

import smtplib
import requests
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from .metrics_collector import AlertEvent, MetricsCollector
from ..utils.logging.logger import get_logger

logger = get_logger(__name__)

@dataclass
class NotificationChannel:
    """通知渠道配置"""
    name: str
    type: str  # email, webhook, sms, wechat, dingtalk
    config: Dict[str, Any]
    enabled: bool = True
    min_level: str = 'INFO'  # 最小报警级别

class AlertManager:
    """报警管理器"""
    
    def __init__(self, config: Dict = None):
        """
        初始化报警管理器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        
        # 通知渠道
        self.notification_channels: List[NotificationChannel] = []
        self._setup_notification_channels()
        
        # 报警规则
        self.alert_rules = self.config.get('alert_rules', {})
        
        # 报警抑制配置（防止报警风暴）
        self.suppression_config = self.config.get('suppression', {
            'same_alert_interval': 300,  # 相同报警间隔5分钟
            'max_alerts_per_hour': 20,   # 每小时最大报警数
        })
        
        # 报警历史（用于抑制重复报警）
        self.alert_history: List[AlertEvent] = []
        self.last_alert_times: Dict[str, datetime] = {}
        
        # 自定义处理器
        self.custom_handlers: Dict[str, Callable] = {}
        
        logger.info("报警管理器初始化完成")
    
    def _setup_notification_channels(self):
        """设置通知渠道"""
        channels_config = self.config.get('notification_channels', [])
        
        for channel_config in channels_config:
            channel = NotificationChannel(
                name=channel_config.get('name'),
                type=channel_config.get('type'),
                config=channel_config.get('config', {}),
                enabled=channel_config.get('enabled', True),
                min_level=channel_config.get('min_level', 'INFO')
            )
            self.notification_channels.append(channel)
            logger.info(f"添加通知渠道: {channel.name} ({channel.type})")
    
    def add_notification_channel(self, channel: NotificationChannel):
        """添加通知渠道"""
        self.notification_channels.append(channel)
        logger.info(f"添加通知渠道: {channel.name} ({channel.type})")
    
    def remove_notification_channel(self, channel_name: str):
        """移除通知渠道"""
        self.notification_channels = [
            ch for ch in self.notification_channels 
            if ch.name != channel_name
        ]
        logger.info(f"移除通知渠道: {channel_name}")
    
    def register_custom_handler(self, alert_type: str, handler: Callable):
        """注册自定义报警处理器"""
        self.custom_handlers[alert_type] = handler
        logger.info(f"注册自定义处理器: {alert_type}")
    
    def process_alert(self, alert: AlertEvent):
        """处理报警事件"""
        try:
            # 检查是否需要抑制报警
            if self._should_suppress_alert(alert):
                logger.debug(f"报警被抑制: {alert.message}")
                return
            
            # 记录报警历史
            self.alert_history.append(alert)
            self._update_alert_times(alert)
            
            # 执行自定义处理器
            if alert.category in self.custom_handlers:
                try:
                    self.custom_handlers[alert.category](alert)
                except Exception as e:
                    logger.error(f"执行自定义处理器失败: {e}")
            
            # 发送通知
            self._send_notifications(alert)
            
            logger.info(f"处理报警事件: [{alert.level}] {alert.category} - {alert.message}")
            
        except Exception as e:
            logger.error(f"处理报警事件失败: {e}")
    
    def _should_suppress_alert(self, alert: AlertEvent) -> bool:
        """检查是否应该抑制报警"""
        try:
            # 检查相同报警的时间间隔
            alert_key = f"{alert.category}_{alert.message}"
            if alert_key in self.last_alert_times:
                last_time = self.last_alert_times[alert_key]
                interval = (alert.timestamp - last_time).total_seconds()
                if interval < self.suppression_config['same_alert_interval']:
                    return True
            
            # 检查每小时报警数量限制
            hour_ago = alert.timestamp - timedelta(hours=1)
            recent_alerts = [
                a for a in self.alert_history 
                if a.timestamp >= hour_ago
            ]
            if len(recent_alerts) >= self.suppression_config['max_alerts_per_hour']:
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"检查报警抑制失败: {e}")
            return False
    
    def _update_alert_times(self, alert: AlertEvent):
        """更新报警时间记录"""
        alert_key = f"{alert.category}_{alert.message}"
        self.last_alert_times[alert_key] = alert.timestamp
        
        # 清理过期的记录
        cutoff_time = alert.timestamp - timedelta(hours=24)
        self.last_alert_times = {
            k: v for k, v in self.last_alert_times.items()
            if v >= cutoff_time
        }
    
    def _send_notifications(self, alert: AlertEvent):
        """发送通知"""
        for channel in self.notification_channels:
            if not channel.enabled:
                continue
            
            # 检查报警级别
            if not self._should_send_to_channel(alert, channel):
                continue
            
            try:
                if channel.type == 'email':
                    self._send_email_notification(alert, channel)
                elif channel.type == 'webhook':
                    self._send_webhook_notification(alert, channel)
                elif channel.type == 'dingtalk':
                    self._send_dingtalk_notification(alert, channel)
                elif channel.type == 'wechat':
                    self._send_wechat_notification(alert, channel)
                else:
                    logger.warning(f"不支持的通知渠道类型: {channel.type}")
                    
            except Exception as e:
                logger.error(f"发送通知失败 ({channel.name}): {e}")
    
    def _should_send_to_channel(self, alert: AlertEvent, channel: NotificationChannel) -> bool:
        """检查是否应该发送到指定渠道"""
        level_priority = {
            'DEBUG': 0,
            'INFO': 1,
            'WARNING': 2,
            'ERROR': 3,
            'CRITICAL': 4
        }
        
        alert_priority = level_priority.get(alert.level, 1)
        channel_priority = level_priority.get(channel.min_level, 1)
        
        return alert_priority >= channel_priority
    
    def _send_email_notification(self, alert: AlertEvent, channel: NotificationChannel):
        """发送邮件通知"""
        try:
            config = channel.config
            
            # 创建邮件
            msg = MIMEMultipart()
            msg['From'] = config['from_email']
            msg['To'] = ', '.join(config['to_emails'])
            msg['Subject'] = f"[{alert.level}] Qlib交易系统报警 - {alert.category}"
            
            # 邮件内容
            body = f"""
报警时间: {alert.timestamp.strftime('%Y-%m-%d %H:%M:%S')}
报警级别: {alert.level}
报警类别: {alert.category}
报警消息: {alert.message}

详细信息:
{json.dumps(alert.details, indent=2, ensure_ascii=False)}

请及时处理相关问题。

---
Qlib交易系统监控
            """
            
            msg.attach(MIMEText(body, 'plain', 'utf-8'))
            
            # 发送邮件
            server = smtplib.SMTP(config['smtp_server'], config['smtp_port'])
            if config.get('use_tls', True):
                server.starttls()
            if config.get('username') and config.get('password'):
                server.login(config['username'], config['password'])
            
            server.send_message(msg)
            server.quit()
            
            logger.info(f"邮件通知发送成功: {channel.name}")
            
        except Exception as e:
            logger.error(f"发送邮件通知失败: {e}")
    
    def _send_webhook_notification(self, alert: AlertEvent, channel: NotificationChannel):
        """发送Webhook通知"""
        try:
            config = channel.config
            
            payload = {
                'timestamp': alert.timestamp.isoformat(),
                'level': alert.level,
                'category': alert.category,
                'message': alert.message,
                'details': alert.details,
                'source': 'qlib_trading_system'
            }
            
            headers = config.get('headers', {'Content-Type': 'application/json'})
            timeout = config.get('timeout', 10)
            
            response = requests.post(
                config['url'],
                json=payload,
                headers=headers,
                timeout=timeout
            )
            
            response.raise_for_status()
            logger.info(f"Webhook通知发送成功: {channel.name}")
            
        except Exception as e:
            logger.error(f"发送Webhook通知失败: {e}")
    
    def _send_dingtalk_notification(self, alert: AlertEvent, channel: NotificationChannel):
        """发送钉钉通知"""
        try:
            config = channel.config
            
            # 钉钉消息格式
            message = f"""
## 🚨 Qlib交易系统报警
            
**报警时间:** {alert.timestamp.strftime('%Y-%m-%d %H:%M:%S')}
            
**报警级别:** {alert.level}
            
**报警类别:** {alert.category}
            
**报警消息:** {alert.message}
            
**详细信息:** {json.dumps(alert.details, ensure_ascii=False)}
            """
            
            payload = {
                "msgtype": "markdown",
                "markdown": {
                    "title": f"[{alert.level}] 交易系统报警",
                    "text": message
                }
            }
            
            # 添加@所有人或特定人员
            if config.get('at_all', False):
                payload["at"] = {"isAtAll": True}
            elif config.get('at_mobiles'):
                payload["at"] = {"atMobiles": config['at_mobiles']}
            
            response = requests.post(
                config['webhook_url'],
                json=payload,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            response.raise_for_status()
            logger.info(f"钉钉通知发送成功: {channel.name}")
            
        except Exception as e:
            logger.error(f"发送钉钉通知失败: {e}")
    
    def _send_wechat_notification(self, alert: AlertEvent, channel: NotificationChannel):
        """发送微信通知"""
        try:
            config = channel.config
            
            # 企业微信机器人消息格式
            message = f"""[{alert.level}] Qlib交易系统报警
            
时间: {alert.timestamp.strftime('%Y-%m-%d %H:%M:%S')}
类别: {alert.category}
消息: {alert.message}
            
详情: {json.dumps(alert.details, ensure_ascii=False)}"""
            
            payload = {
                "msgtype": "text",
                "text": {
                    "content": message
                }
            }
            
            # 添加@特定人员
            if config.get('mentioned_list'):
                payload["text"]["mentioned_list"] = config['mentioned_list']
            
            response = requests.post(
                config['webhook_url'],
                json=payload,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            response.raise_for_status()
            logger.info(f"微信通知发送成功: {channel.name}")
            
        except Exception as e:
            logger.error(f"发送微信通知失败: {e}")
    
    def get_alert_statistics(self, hours: int = 24) -> Dict:
        """获取报警统计信息"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            recent_alerts = [
                a for a in self.alert_history 
                if a.timestamp >= cutoff_time
            ]
            
            # 按级别统计
            level_counts = {}
            for alert in recent_alerts:
                level_counts[alert.level] = level_counts.get(alert.level, 0) + 1
            
            # 按类别统计
            category_counts = {}
            for alert in recent_alerts:
                category_counts[alert.category] = category_counts.get(alert.category, 0) + 1
            
            return {
                'total_alerts': len(recent_alerts),
                'level_distribution': level_counts,
                'category_distribution': category_counts,
                'time_range_hours': hours,
                'active_channels': len([ch for ch in self.notification_channels if ch.enabled])
            }
            
        except Exception as e:
            logger.error(f"获取报警统计失败: {e}")
            return {}
    
    def test_notification_channels(self):
        """测试所有通知渠道"""
        test_alert = AlertEvent(
            timestamp=datetime.now(),
            level='INFO',
            category='SYSTEM',
            message='通知渠道测试消息',
            details={'test': True}
        )
        
        for channel in self.notification_channels:
            if channel.enabled:
                try:
                    logger.info(f"测试通知渠道: {channel.name}")
                    self._send_notifications(test_alert)
                except Exception as e:
                    logger.error(f"测试通知渠道失败 ({channel.name}): {e}")
    
    def cleanup_old_alerts(self, days: int = 7):
        """清理旧的报警记录"""
        cutoff_time = datetime.now() - timedelta(days=days)
        old_count = len(self.alert_history)
        
        self.alert_history = [
            a for a in self.alert_history 
            if a.timestamp >= cutoff_time
        ]
        
        new_count = len(self.alert_history)
        logger.info(f"清理旧报警记录: {old_count - new_count} 条记录被清理")