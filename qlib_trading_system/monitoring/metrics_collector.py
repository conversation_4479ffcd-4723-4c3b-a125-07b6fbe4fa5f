"""
指标收集器 - 收集和计算实时交易指标
Metrics Collector - Collects and calculates real-time trading metrics
"""

import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from collections import deque, defaultdict
import pandas as pd
import numpy as np
from ..utils.logging.logger import get_logger

logger = get_logger(__name__)

@dataclass
class TradingMetrics:
    """交易指标数据结构"""
    timestamp: datetime
    # 基础指标
    total_pnl: float = 0.0
    daily_pnl: float = 0.0
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0
    
    # 仓位指标
    total_position_value: float = 0.0
    cash_balance: float = 0.0
    position_count: int = 0
    leverage_ratio: float = 0.0
    
    # 交易指标
    daily_trades: int = 0
    win_rate: float = 0.0
    avg_profit_per_trade: float = 0.0
    max_drawdown: float = 0.0
    
    # 风险指标
    var_1d: float = 0.0  # 1日风险价值
    volatility: float = 0.0
    sharpe_ratio: float = 0.0
    
    # 系统指标
    cpu_usage: float = 0.0
    memory_usage: float = 0.0
    network_latency: float = 0.0
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return asdict(self)

@dataclass
class AlertEvent:
    """报警事件数据结构"""
    timestamp: datetime
    level: str  # INFO, WARNING, ERROR, CRITICAL
    category: str  # TRADING, RISK, SYSTEM, NETWORK
    message: str
    details: Dict[str, Any]
    resolved: bool = False

class MetricsCollector:
    """实时指标收集器"""
    
    def __init__(self, config: Dict = None):
        """
        初始化指标收集器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        self.is_running = False
        self.collection_thread = None
        
        # 数据存储
        self.metrics_history = deque(maxlen=1000)  # 保存最近1000个数据点
        self.current_metrics = TradingMetrics(timestamp=datetime.now())
        self.alerts = deque(maxlen=500)  # 保存最近500个报警
        
        # 计算缓存
        self.pnl_history = deque(maxlen=100)  # 用于计算波动率等指标
        self.trade_history = deque(maxlen=1000)
        
        # 订阅者列表（观察者模式）
        self.subscribers = []
        
        # 配置参数
        self.collection_interval = self.config.get('collection_interval', 1.0)  # 秒
        self.alert_thresholds = self.config.get('alert_thresholds', {
            'max_drawdown': 0.05,  # 5%最大回撤警告
            'daily_loss': 0.03,    # 3%日亏损警告
            'position_concentration': 0.8,  # 80%仓位集中度警告
            'system_cpu': 80.0,    # 80% CPU使用率警告
            'system_memory': 85.0, # 85%内存使用率警告
            'network_latency': 100.0  # 100ms网络延迟警告
        })
        
        logger.info("指标收集器初始化完成")
    
    def start_collection(self):
        """开始指标收集"""
        if self.is_running:
            logger.warning("指标收集器已在运行中")
            return
        
        self.is_running = True
        self.collection_thread = threading.Thread(target=self._collection_loop, daemon=True)
        self.collection_thread.start()
        logger.info("指标收集器已启动")
    
    def stop_collection(self):
        """停止指标收集"""
        self.is_running = False
        if self.collection_thread:
            self.collection_thread.join(timeout=5.0)
        logger.info("指标收集器已停止")
    
    def _collection_loop(self):
        """指标收集主循环"""
        while self.is_running:
            try:
                # 收集指标
                self._collect_metrics()
                
                # 检查报警条件
                self._check_alerts()
                
                # 通知订阅者
                self._notify_subscribers()
                
                time.sleep(self.collection_interval)
                
            except Exception as e:
                logger.error(f"指标收集过程中发生错误: {e}")
                time.sleep(1.0)  # 错误时短暂休息
    
    def _collect_metrics(self):
        """收集当前指标"""
        try:
            current_time = datetime.now()
            
            # 收集基础交易指标
            trading_metrics = self._collect_trading_metrics()
            
            # 收集系统指标
            system_metrics = self._collect_system_metrics()
            
            # 计算衍生指标
            derived_metrics = self._calculate_derived_metrics()
            
            # 合并所有指标
            self.current_metrics = TradingMetrics(
                timestamp=current_time,
                **trading_metrics,
                **system_metrics,
                **derived_metrics
            )
            
            # 保存到历史记录
            self.metrics_history.append(self.current_metrics)
            
            # 更新PnL历史用于计算
            self.pnl_history.append(self.current_metrics.total_pnl)
            
        except Exception as e:
            logger.error(f"收集指标时发生错误: {e}")
    
    def _collect_trading_metrics(self) -> Dict:
        """收集交易相关指标"""
        # 这里应该从实际的交易系统获取数据
        # 目前使用模拟数据进行演示
        
        # 模拟数据 - 实际实现时需要从交易系统获取
        return {
            'total_pnl': np.random.normal(1000, 100),
            'daily_pnl': np.random.normal(50, 20),
            'unrealized_pnl': np.random.normal(200, 50),
            'realized_pnl': np.random.normal(800, 80),
            'total_position_value': np.random.normal(50000, 5000),
            'cash_balance': np.random.normal(10000, 1000),
            'position_count': np.random.randint(1, 5),
            'leverage_ratio': np.random.uniform(1.0, 2.0),
            'daily_trades': np.random.randint(5, 20)
        }
    
    def _collect_system_metrics(self) -> Dict:
        """收集系统性能指标"""
        try:
            import psutil
            
            # CPU使用率
            cpu_usage = psutil.cpu_percent(interval=0.1)
            
            # 内存使用率
            memory = psutil.virtual_memory()
            memory_usage = memory.percent
            
            # 网络延迟（模拟）
            network_latency = np.random.uniform(10, 50)
            
            return {
                'cpu_usage': cpu_usage,
                'memory_usage': memory_usage,
                'network_latency': network_latency
            }
            
        except ImportError:
            logger.warning("psutil未安装，使用模拟系统指标")
            return {
                'cpu_usage': np.random.uniform(20, 60),
                'memory_usage': np.random.uniform(40, 70),
                'network_latency': np.random.uniform(10, 50)
            }
        except Exception as e:
            logger.error(f"收集系统指标时发生错误: {e}")
            return {
                'cpu_usage': 0.0,
                'memory_usage': 0.0,
                'network_latency': 0.0
            }
    
    def _calculate_derived_metrics(self) -> Dict:
        """计算衍生指标"""
        try:
            # 计算胜率
            win_rate = self._calculate_win_rate()
            
            # 计算平均每笔交易盈利
            avg_profit_per_trade = self._calculate_avg_profit_per_trade()
            
            # 计算最大回撤
            max_drawdown = self._calculate_max_drawdown()
            
            # 计算波动率
            volatility = self._calculate_volatility()
            
            # 计算夏普比率
            sharpe_ratio = self._calculate_sharpe_ratio()
            
            # 计算VaR
            var_1d = self._calculate_var()
            
            return {
                'win_rate': win_rate,
                'avg_profit_per_trade': avg_profit_per_trade,
                'max_drawdown': max_drawdown,
                'volatility': volatility,
                'sharpe_ratio': sharpe_ratio,
                'var_1d': var_1d
            }
            
        except Exception as e:
            logger.error(f"计算衍生指标时发生错误: {e}")
            return {
                'win_rate': 0.0,
                'avg_profit_per_trade': 0.0,
                'max_drawdown': 0.0,
                'volatility': 0.0,
                'sharpe_ratio': 0.0,
                'var_1d': 0.0
            }
    
    def _calculate_win_rate(self) -> float:
        """计算胜率"""
        if len(self.trade_history) == 0:
            return 0.0
        
        # 模拟计算 - 实际实现时需要从交易记录计算
        return np.random.uniform(0.4, 0.7)
    
    def _calculate_avg_profit_per_trade(self) -> float:
        """计算平均每笔交易盈利"""
        if len(self.trade_history) == 0:
            return 0.0
        
        # 模拟计算
        return np.random.normal(10, 5)
    
    def _calculate_max_drawdown(self) -> float:
        """计算最大回撤"""
        if len(self.pnl_history) < 2:
            return 0.0
        
        pnl_array = np.array(list(self.pnl_history))
        peak = np.maximum.accumulate(pnl_array)
        drawdown = (peak - pnl_array) / peak
        return float(np.max(drawdown))
    
    def _calculate_volatility(self) -> float:
        """计算波动率"""
        if len(self.pnl_history) < 2:
            return 0.0
        
        pnl_array = np.array(list(self.pnl_history))
        returns = np.diff(pnl_array) / pnl_array[:-1]
        return float(np.std(returns) * np.sqrt(252))  # 年化波动率
    
    def _calculate_sharpe_ratio(self) -> float:
        """计算夏普比率"""
        if len(self.pnl_history) < 2:
            return 0.0
        
        pnl_array = np.array(list(self.pnl_history))
        returns = np.diff(pnl_array) / pnl_array[:-1]
        
        if np.std(returns) == 0:
            return 0.0
        
        # 假设无风险利率为3%
        risk_free_rate = 0.03 / 252  # 日无风险利率
        excess_returns = returns - risk_free_rate
        
        return float(np.mean(excess_returns) / np.std(returns) * np.sqrt(252))
    
    def _calculate_var(self) -> float:
        """计算1日风险价值（VaR）"""
        if len(self.pnl_history) < 2:
            return 0.0
        
        pnl_array = np.array(list(self.pnl_history))
        returns = np.diff(pnl_array) / pnl_array[:-1]
        
        # 95%置信度的VaR
        var_95 = np.percentile(returns, 5)
        return float(abs(var_95 * self.current_metrics.total_position_value))
    
    def _check_alerts(self):
        """检查报警条件"""
        try:
            current_time = datetime.now()
            
            # 检查最大回撤报警
            if self.current_metrics.max_drawdown > self.alert_thresholds['max_drawdown']:
                self._create_alert(
                    level='WARNING',
                    category='RISK',
                    message=f'最大回撤超过阈值: {self.current_metrics.max_drawdown:.2%}',
                    details={'threshold': self.alert_thresholds['max_drawdown'], 
                            'current': self.current_metrics.max_drawdown}
                )
            
            # 检查日亏损报警
            if self.current_metrics.daily_pnl < -abs(self.current_metrics.total_position_value * self.alert_thresholds['daily_loss']):
                self._create_alert(
                    level='ERROR',
                    category='TRADING',
                    message=f'日亏损超过阈值: {self.current_metrics.daily_pnl:.2f}',
                    details={'threshold': self.alert_thresholds['daily_loss'],
                            'current_pnl': self.current_metrics.daily_pnl}
                )
            
            # 检查系统性能报警
            if self.current_metrics.cpu_usage > self.alert_thresholds['system_cpu']:
                self._create_alert(
                    level='WARNING',
                    category='SYSTEM',
                    message=f'CPU使用率过高: {self.current_metrics.cpu_usage:.1f}%',
                    details={'threshold': self.alert_thresholds['system_cpu'],
                            'current': self.current_metrics.cpu_usage}
                )
            
            if self.current_metrics.memory_usage > self.alert_thresholds['system_memory']:
                self._create_alert(
                    level='WARNING',
                    category='SYSTEM',
                    message=f'内存使用率过高: {self.current_metrics.memory_usage:.1f}%',
                    details={'threshold': self.alert_thresholds['system_memory'],
                            'current': self.current_metrics.memory_usage}
                )
            
            # 检查网络延迟报警
            if self.current_metrics.network_latency > self.alert_thresholds['network_latency']:
                self._create_alert(
                    level='WARNING',
                    category='NETWORK',
                    message=f'网络延迟过高: {self.current_metrics.network_latency:.1f}ms',
                    details={'threshold': self.alert_thresholds['network_latency'],
                            'current': self.current_metrics.network_latency}
                )
                
        except Exception as e:
            logger.error(f"检查报警条件时发生错误: {e}")
    
    def _create_alert(self, level: str, category: str, message: str, details: Dict):
        """创建报警事件"""
        alert = AlertEvent(
            timestamp=datetime.now(),
            level=level,
            category=category,
            message=message,
            details=details
        )
        
        self.alerts.append(alert)
        logger.warning(f"报警事件: [{level}] {category} - {message}")
    
    def subscribe(self, callback):
        """订阅指标更新"""
        self.subscribers.append(callback)
    
    def unsubscribe(self, callback):
        """取消订阅"""
        if callback in self.subscribers:
            self.subscribers.remove(callback)
    
    def _notify_subscribers(self):
        """通知所有订阅者"""
        for callback in self.subscribers:
            try:
                import asyncio
                import inspect
                if inspect.iscoroutinefunction(callback):
                    # 对于异步回调，需要在事件循环中运行
                    try:
                        loop = asyncio.get_event_loop()
                        if loop.is_running():
                            # 如果事件循环正在运行，创建任务
                            loop.create_task(callback(self.current_metrics))
                        else:
                            # 如果事件循环未运行，直接运行
                            loop.run_until_complete(callback(self.current_metrics))
                    except RuntimeError:
                        # 如果没有事件循环，跳过异步回调
                        logger.warning("跳过异步回调，因为没有运行的事件循环")
                else:
                    callback(self.current_metrics)
            except Exception as e:
                logger.error(f"通知订阅者时发生错误: {e}")
    
    def get_current_metrics(self) -> TradingMetrics:
        """获取当前指标"""
        return self.current_metrics
    
    def get_metrics_history(self, minutes: int = 60) -> List[TradingMetrics]:
        """获取历史指标"""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        return [m for m in self.metrics_history if m.timestamp >= cutoff_time]
    
    def get_recent_alerts(self, minutes: int = 60) -> List[AlertEvent]:
        """获取最近的报警事件"""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        return [a for a in self.alerts if a.timestamp >= cutoff_time]
    
    def get_summary_stats(self) -> Dict:
        """获取汇总统计信息"""
        if not self.metrics_history:
            return {}
        
        recent_metrics = list(self.metrics_history)[-60:]  # 最近60个数据点
        
        return {
            'avg_pnl': np.mean([m.total_pnl for m in recent_metrics]),
            'max_pnl': np.max([m.total_pnl for m in recent_metrics]),
            'min_pnl': np.min([m.total_pnl for m in recent_metrics]),
            'avg_position_value': np.mean([m.total_position_value for m in recent_metrics]),
            'total_trades_today': sum([m.daily_trades for m in recent_metrics]),
            'alert_count': len([a for a in self.alerts if a.timestamp.date() == datetime.now().date()])
        }