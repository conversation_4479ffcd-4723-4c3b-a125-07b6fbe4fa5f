# -*- coding: utf-8 -*-
"""
增强版报告生成系统 - 实现自定义报告模板和高级导出功能
Enhanced Report Generation System - Implements custom report templates and advanced export features
"""

import os
import json
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta, date
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
import logging
from jinja2 import Template, Environment, FileSystemLoader, select_autoescape
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.chart import LineChart, BarChart, Reference
from openpyxl.utils.dataframe import dataframe_to_rows
import warnings
import base64
from io import BytesIO
import zipfile
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

logger = logging.getLogger(__name__)

# 可选依赖包
try:
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib import colors
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False
    logger.warning("ReportLab未安装，PDF生成功能将不可用")

try:
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.subplots import make_subplots
    import plotly.offline as pyo
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False
    logger.warning("Plotly未安装，交互式图表功能将不可用")


@dataclass
class CustomReportTemplate:
    """自定义报告模板配置"""
    template_id: str
    template_name: str
    template_type: str  # 'daily', 'weekly', 'monthly', 'strategy', 'custom'
    sections: List[str]
    charts: List[str]
    export_formats: List[str]
    template_content: str
    variables: Dict[str, Any]
    created_time: str
    updated_time: str


@dataclass
class ReportExportConfig:
    """报告导出配置"""
    export_id: str
    export_name: str
    export_format: str  # 'html', 'pdf', 'excel', 'json', 'csv', 'zip'
    include_charts: bool
    include_raw_data: bool
    custom_styling: Dict[str, Any]
    compression_level: int
    password_protected: bool
    password: Optional[str]


class EnhancedReportGenerator:
    """增强版报告生成器"""
    
    def __init__(self, config: Dict = None):
        """
        初始化增强版报告生成器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        
        # 报告配置
        self.report_dir = self.config.get('report_dir', 'reports')
        self.template_dir = self.config.get('template_dir', 'qlib_trading_system/monitoring/templates')
        self.enable_charts = self.config.get('enable_charts', True)
        self.chart_format = self.config.get('chart_format', 'png')
        self.enable_interactive_charts = self.config.get('enable_interactive_charts', True)
        
        # 创建报告目录结构
        self._create_directory_structure()
        
        # 初始化模板引擎
        self.template_env = Environment(
            loader=FileSystemLoader([self.template_dir, f"{self.template_dir}/custom"]),
            autoescape=select_autoescape(['html', 'xml'])
        )
        
        # 自定义模板存储
        self.custom_templates = {}
        self.export_configs = {}
        
        # 数据缓存
        self.data_cache = {
            'daily': {},
            'weekly': {},
            'monthly': {},
            'strategy': {}
        }
        
        # 加载现有的自定义模板
        self._load_custom_templates()
        
        logger.info("增强版报告生成器初始化完成")
    
    def _create_directory_structure(self):
        """创建报告目录结构"""
        directories = [
            self.report_dir,
            f"{self.report_dir}/daily",
            f"{self.report_dir}/weekly", 
            f"{self.report_dir}/monthly",
            f"{self.report_dir}/strategy",
            f"{self.report_dir}/custom",
            f"{self.report_dir}/charts",
            f"{self.report_dir}/exports",
            f"{self.report_dir}/templates",
            f"{self.report_dir}/archives",
            f"{self.template_dir}/custom"
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    def _load_custom_templates(self):
        """加载自定义模板"""
        try:
            template_config_path = Path(f"{self.report_dir}/templates/custom_templates.json")
            if template_config_path.exists():
                with open(template_config_path, 'r', encoding='utf-8') as f:
                    templates_data = json.load(f)
                
                for template_data in templates_data:
                    template = CustomReportTemplate(**template_data)
                    self.custom_templates[template.template_id] = template
                
                logger.info(f"加载了 {len(self.custom_templates)} 个自定义模板")
        except Exception as e:
            logger.error(f"加载自定义模板时发生错误: {e}")
    
    def create_custom_template(self, 
                             template_name: str,
                             template_type: str,
                             sections: List[str],
                             charts: List[str],
                             template_content: str,
                             variables: Dict[str, Any] = None) -> str:
        """
        创建自定义报告模板
        
        Args:
            template_name: 模板名称
            template_type: 模板类型
            sections: 报告章节
            charts: 图表列表
            template_content: 模板内容
            variables: 模板变量
            
        Returns:
            str: 模板ID
        """
        try:
            template_id = f"custom_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            current_time = datetime.now().isoformat()
            
            custom_template = CustomReportTemplate(
                template_id=template_id,
                template_name=template_name,
                template_type=template_type,
                sections=sections,
                charts=charts,
                export_formats=['html', 'pdf', 'excel'],
                template_content=template_content,
                variables=variables or {},
                created_time=current_time,
                updated_time=current_time
            )
            
            # 保存模板
            self.custom_templates[template_id] = custom_template
            
            # 保存模板文件
            template_file_path = Path(f"{self.template_dir}/custom/{template_id}.html")
            with open(template_file_path, 'w', encoding='utf-8') as f:
                f.write(template_content)
            
            # 更新模板配置文件
            self._save_custom_templates()
            
            logger.info(f"自定义模板创建成功: {template_id}")
            return template_id
            
        except Exception as e:
            logger.error(f"创建自定义模板时发生错误: {e}")
            raise
    
    def update_custom_template(self, 
                             template_id: str,
                             template_name: Optional[str] = None,
                             sections: Optional[List[str]] = None,
                             charts: Optional[List[str]] = None,
                             template_content: Optional[str] = None,
                             variables: Optional[Dict[str, Any]] = None) -> bool:
        """
        更新自定义报告模板
        
        Args:
            template_id: 模板ID
            template_name: 模板名称
            sections: 报告章节
            charts: 图表列表
            template_content: 模板内容
            variables: 模板变量
            
        Returns:
            bool: 更新是否成功
        """
        try:
            if template_id not in self.custom_templates:
                logger.error(f"模板不存在: {template_id}")
                return False
            
            template = self.custom_templates[template_id]
            
            # 更新模板属性
            if template_name is not None:
                template.template_name = template_name
            if sections is not None:
                template.sections = sections
            if charts is not None:
                template.charts = charts
            if template_content is not None:
                template.template_content = template_content
                # 更新模板文件
                template_file_path = Path(f"{self.template_dir}/custom/{template_id}.html")
                with open(template_file_path, 'w', encoding='utf-8') as f:
                    f.write(template_content)
            if variables is not None:
                template.variables.update(variables)
            
            template.updated_time = datetime.now().isoformat()
            
            # 保存更新
            self._save_custom_templates()
            
            logger.info(f"自定义模板更新成功: {template_id}")
            return True
            
        except Exception as e:
            logger.error(f"更新自定义模板时发生错误: {e}")
            return False
    
    def delete_custom_template(self, template_id: str) -> bool:
        """
        删除自定义报告模板
        
        Args:
            template_id: 模板ID
            
        Returns:
            bool: 删除是否成功
        """
        try:
            if template_id not in self.custom_templates:
                logger.error(f"模板不存在: {template_id}")
                return False
            
            # 删除模板文件
            template_file_path = Path(f"{self.template_dir}/custom/{template_id}.html")
            if template_file_path.exists():
                template_file_path.unlink()
            
            # 从内存中删除
            del self.custom_templates[template_id]
            
            # 更新配置文件
            self._save_custom_templates()
            
            logger.info(f"自定义模板删除成功: {template_id}")
            return True
            
        except Exception as e:
            logger.error(f"删除自定义模板时发生错误: {e}")
            return False
    
    def list_custom_templates(self) -> List[Dict[str, Any]]:
        """
        列出所有自定义模板
        
        Returns:
            List[Dict]: 模板列表
        """
        templates_list = []
        for template_id, template in self.custom_templates.items():
            templates_list.append({
                'template_id': template.template_id,
                'template_name': template.template_name,
                'template_type': template.template_type,
                'sections_count': len(template.sections),
                'charts_count': len(template.charts),
                'created_time': template.created_time,
                'updated_time': template.updated_time
            })
        
        return templates_list
    
    def generate_custom_report(self,
                             template_id: str,
                             data: Dict[str, Any],
                             output_filename: Optional[str] = None,
                             export_formats: List[str] = ['html']) -> Dict[str, str]:
        """
        使用自定义模板生成报告
        
        Args:
            template_id: 模板ID
            data: 报告数据
            output_filename: 输出文件名
            export_formats: 导出格式列表
            
        Returns:
            Dict[str, str]: 生成的报告文件路径
        """
        try:
            if template_id not in self.custom_templates:
                raise ValueError(f"模板不存在: {template_id}")
            
            template = self.custom_templates[template_id]
            
            # 准备模板变量
            template_vars = {
                'data': data,
                'generation_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'template_name': template.template_name,
                **template.variables
            }
            
            # 生成图表（如果需要）
            chart_paths = {}
            if self.enable_charts and template.charts:
                chart_paths = self._generate_custom_charts(data, template.charts, template_id)
                template_vars['charts'] = chart_paths
            
            # 生成报告文件
            report_paths = {}
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            base_filename = output_filename or f"custom_report_{template_id}_{timestamp}"
            
            for format_type in export_formats:
                if format_type == 'html':
                    report_paths['html'] = self._generate_custom_html_report(
                        template, template_vars, base_filename
                    )
                elif format_type == 'pdf':
                    report_paths['pdf'] = self._generate_custom_pdf_report(
                        template, template_vars, base_filename
                    )
                elif format_type == 'excel':
                    report_paths['excel'] = self._generate_custom_excel_report(
                        template, data, base_filename
                    )
                elif format_type == 'json':
                    report_paths['json'] = self._generate_custom_json_report(
                        template, data, base_filename
                    )
            
            logger.info(f"自定义报告生成完成: {template_id}, 格式: {list(report_paths.keys())}")
            return report_paths
            
        except Exception as e:
            logger.error(f"生成自定义报告时发生错误: {e}")
            raise
    
    def create_export_config(self,
                           export_name: str,
                           export_format: str,
                           include_charts: bool = True,
                           include_raw_data: bool = False,
                           custom_styling: Dict[str, Any] = None,
                           compression_level: int = 6,
                           password_protected: bool = False,
                           password: Optional[str] = None) -> str:
        """
        创建导出配置
        
        Args:
            export_name: 导出配置名称
            export_format: 导出格式
            include_charts: 是否包含图表
            include_raw_data: 是否包含原始数据
            custom_styling: 自定义样式
            compression_level: 压缩级别 (0-9)
            password_protected: 是否密码保护
            password: 密码
            
        Returns:
            str: 导出配置ID
        """
        try:
            export_id = f"export_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            export_config = ReportExportConfig(
                export_id=export_id,
                export_name=export_name,
                export_format=export_format,
                include_charts=include_charts,
                include_raw_data=include_raw_data,
                custom_styling=custom_styling or {},
                compression_level=compression_level,
                password_protected=password_protected,
                password=password
            )
            
            self.export_configs[export_id] = export_config
            
            # 保存导出配置
            self._save_export_configs()
            
            logger.info(f"导出配置创建成功: {export_id}")
            return export_id
            
        except Exception as e:
            logger.error(f"创建导出配置时发生错误: {e}")
            raise
    
    def export_reports_batch(self,
                           report_paths: List[str],
                           export_config_id: str,
                           output_path: Optional[str] = None) -> str:
        """
        批量导出报告
        
        Args:
            report_paths: 报告文件路径列表
            export_config_id: 导出配置ID
            output_path: 输出路径
            
        Returns:
            str: 导出文件路径
        """
        try:
            if export_config_id not in self.export_configs:
                raise ValueError(f"导出配置不存在: {export_config_id}")
            
            export_config = self.export_configs[export_config_id]
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            if output_path is None:
                output_path = f"{self.report_dir}/exports/batch_export_{timestamp}"
            
            if export_config.export_format == 'zip':
                return self._create_zip_export(report_paths, export_config, output_path)
            elif export_config.export_format == 'pdf':
                return self._create_merged_pdf_export(report_paths, export_config, output_path)
            elif export_config.export_format == 'excel':
                return self._create_merged_excel_export(report_paths, export_config, output_path)
            else:
                raise ValueError(f"不支持的批量导出格式: {export_config.export_format}")
            
        except Exception as e:
            logger.error(f"批量导出报告时发生错误: {e}")
            raise
    
    def generate_report_summary(self,
                              start_date: Union[str, date],
                              end_date: Union[str, date],
                              report_types: List[str] = ['daily', 'weekly', 'monthly']) -> Dict[str, Any]:
        """
        生成报告汇总
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            report_types: 报告类型列表
            
        Returns:
            Dict[str, Any]: 报告汇总数据
        """
        try:
            if isinstance(start_date, str):
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            if isinstance(end_date, str):
                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
            
            summary = {
                'period': {
                    'start_date': start_date.strftime('%Y-%m-%d'),
                    'end_date': end_date.strftime('%Y-%m-%d'),
                    'total_days': (end_date - start_date).days + 1
                },
                'reports_generated': {},
                'performance_summary': {},
                'key_metrics': {},
                'generation_time': datetime.now().isoformat()
            }
            
            # 统计各类型报告数量
            for report_type in report_types:
                cache_data = self.data_cache.get(report_type, {})
                
                # 筛选日期范围内的报告
                reports_in_range = []
                for date_key, data in cache_data.items():
                    try:
                        if report_type == 'daily':
                            report_date = datetime.strptime(date_key, '%Y-%m-%d').date()
                            if start_date <= report_date <= end_date:
                                reports_in_range.append(data)
                        elif report_type == 'weekly':
                            week_start = datetime.strptime(date_key.split('_')[0], '%Y-%m-%d').date()
                            if start_date <= week_start <= end_date:
                                reports_in_range.append(data)
                        elif report_type == 'monthly':
                            month_date = datetime.strptime(f"{date_key}-01", '%Y-%m-%d').date()
                            if start_date <= month_date <= end_date:
                                reports_in_range.append(data)
                    except (ValueError, IndexError):
                        continue
                
                summary['reports_generated'][report_type] = len(reports_in_range)
                
                # 计算关键指标汇总
                if reports_in_range:
                    if report_type == 'daily':
                        summary['performance_summary'][report_type] = self._summarize_daily_performance(reports_in_range)
                    elif report_type == 'weekly':
                        summary['performance_summary'][report_type] = self._summarize_weekly_performance(reports_in_range)
                    elif report_type == 'monthly':
                        summary['performance_summary'][report_type] = self._summarize_monthly_performance(reports_in_range)
            
            # 计算整体关键指标
            summary['key_metrics'] = self._calculate_overall_metrics(summary['performance_summary'])
            
            logger.info(f"报告汇总生成完成: {start_date} 至 {end_date}")
            return summary
            
        except Exception as e:
            logger.error(f"生成报告汇总时发生错误: {e}")
            raise
    
    def _save_custom_templates(self):
        """保存自定义模板配置"""
        try:
            templates_data = []
            for template in self.custom_templates.values():
                templates_data.append(asdict(template))
            
            config_path = Path(f"{self.report_dir}/templates/custom_templates.json")
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(templates_data, f, ensure_ascii=False, indent=2, default=str)
                
        except Exception as e:
            logger.error(f"保存自定义模板配置时发生错误: {e}")
    
    def _save_export_configs(self):
        """保存导出配置"""
        try:
            configs_data = []
            for config in self.export_configs.values():
                configs_data.append(asdict(config))
            
            config_path = Path(f"{self.report_dir}/templates/export_configs.json")
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(configs_data, f, ensure_ascii=False, indent=2, default=str)
                
        except Exception as e:
            logger.error(f"保存导出配置时发生错误: {e}")
    
    def _generate_custom_charts(self, data: Dict[str, Any], chart_types: List[str], template_id: str) -> Dict[str, str]:
        """生成自定义图表"""
        chart_paths = {}
        
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            for chart_type in chart_types:
                if chart_type == 'performance_trend':
                    chart_path = self._create_performance_trend_chart(data, template_id, timestamp)
                elif chart_type == 'risk_analysis':
                    chart_path = self._create_risk_analysis_chart(data, template_id, timestamp)
                elif chart_type == 'trading_volume':
                    chart_path = self._create_trading_volume_chart(data, template_id, timestamp)
                elif chart_type == 'profit_distribution':
                    chart_path = self._create_profit_distribution_chart(data, template_id, timestamp)
                else:
                    continue
                
                if chart_path:
                    chart_paths[chart_type] = chart_path
            
            logger.info(f"自定义图表生成完成: {len(chart_paths)}个图表")
            
        except Exception as e:
            logger.error(f"生成自定义图表时发生错误: {e}")
        
        return chart_paths
    
    def _create_performance_trend_chart(self, data: Dict[str, Any], template_id: str, timestamp: str) -> str:
        """创建业绩趋势图表"""
        try:
            fig, ax = plt.subplots(figsize=(12, 6))
            
            # 模拟业绩数据
            dates = pd.date_range(start='2024-01-01', periods=30, freq='D')
            returns = np.cumsum(np.random.normal(0.001, 0.02, 30))
            
            ax.plot(dates, returns, 'b-', linewidth=2, label='累计收益率')
            ax.axhline(y=0, color='r', linestyle='--', alpha=0.7)
            ax.set_title('业绩趋势分析', fontsize=14, fontweight='bold')
            ax.set_xlabel('日期')
            ax.set_ylabel('累计收益率')
            ax.legend()
            ax.grid(True, alpha=0.3)
            
            plt.xticks(rotation=45)
            plt.tight_layout()
            
            chart_path = f"{self.report_dir}/charts/performance_trend_{template_id}_{timestamp}.png"
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            return chart_path
            
        except Exception as e:
            logger.error(f"创建业绩趋势图表时发生错误: {e}")
            return ""
    
    def _create_risk_analysis_chart(self, data: Dict[str, Any], template_id: str, timestamp: str) -> str:
        """创建风险分析图表"""
        try:
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
            
            # 风险指标雷达图
            categories = ['最大回撤', '波动率', 'VaR', '夏普比率', '胜率']
            values = np.random.uniform(0.3, 0.9, 5)
            
            angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
            values_plot = values.tolist()
            values_plot += values_plot[:1]
            angles += angles[:1]
            
            ax1 = plt.subplot(1, 2, 1, projection='polar')
            ax1.plot(angles, values_plot, 'o-', linewidth=2, color='red')
            ax1.fill(angles, values_plot, alpha=0.25, color='red')
            ax1.set_xticks(angles[:-1])
            ax1.set_xticklabels(categories)
            ax1.set_ylim(0, 1)
            ax1.set_title('风险指标分析', fontsize=12, fontweight='bold', pad=20)
            
            # 收益分布直方图
            ax2 = plt.subplot(1, 2, 2)
            returns = np.random.normal(0.001, 0.02, 1000)
            ax2.hist(returns, bins=50, alpha=0.7, color='blue', edgecolor='black')
            ax2.axvline(x=0, color='red', linestyle='--', alpha=0.7)
            ax2.set_title('收益分布分析', fontsize=12, fontweight='bold')
            ax2.set_xlabel('日收益率')
            ax2.set_ylabel('频次')
            ax2.grid(True, alpha=0.3)
            
            plt.tight_layout()
            
            chart_path = f"{self.report_dir}/charts/risk_analysis_{template_id}_{timestamp}.png"
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            return chart_path
            
        except Exception as e:
            logger.error(f"创建风险分析图表时发生错误: {e}")
            return ""
    
    def _create_trading_volume_chart(self, data: Dict[str, Any], template_id: str, timestamp: str) -> str:
        """创建交易量图表"""
        try:
            fig, ax = plt.subplots(figsize=(12, 6))
            
            # 模拟交易量数据
            dates = pd.date_range(start='2024-01-01', periods=30, freq='D')
            volumes = np.random.randint(1000, 10000, 30)
            
            bars = ax.bar(dates, volumes, alpha=0.7, color='green')
            ax.set_title('日交易量统计', fontsize=14, fontweight='bold')
            ax.set_xlabel('日期')
            ax.set_ylabel('交易量')
            ax.grid(True, alpha=0.3)
            
            # 添加平均线
            avg_volume = np.mean(volumes)
            ax.axhline(y=avg_volume, color='red', linestyle='--', alpha=0.7, label=f'平均交易量: {avg_volume:.0f}')
            ax.legend()
            
            plt.xticks(rotation=45)
            plt.tight_layout()
            
            chart_path = f"{self.report_dir}/charts/trading_volume_{template_id}_{timestamp}.png"
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            return chart_path
            
        except Exception as e:
            logger.error(f"创建交易量图表时发生错误: {e}")
            return ""
    
    def _create_profit_distribution_chart(self, data: Dict[str, Any], template_id: str, timestamp: str) -> str:
        """创建盈亏分布图表"""
        try:
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
            
            # 盈亏分布饼图
            labels = ['盈利交易', '亏损交易']
            sizes = [65, 35]  # 模拟数据
            colors = ['green', 'red']
            
            ax1.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
            ax1.set_title('盈亏交易分布', fontsize=12, fontweight='bold')
            
            # 盈亏金额分布
            profit_amounts = np.random.normal(1000, 2000, 100)
            loss_amounts = np.random.normal(-800, 1500, 100)
            all_amounts = np.concatenate([profit_amounts[profit_amounts > 0], loss_amounts[loss_amounts < 0]])
            
            ax2.hist([profit_amounts[profit_amounts > 0], loss_amounts[loss_amounts < 0]], 
                    bins=20, alpha=0.7, color=['green', 'red'], label=['盈利', '亏损'])
            ax2.axvline(x=0, color='black', linestyle='-', alpha=0.7)
            ax2.set_title('盈亏金额分布', fontsize=12, fontweight='bold')
            ax2.set_xlabel('盈亏金额 (元)')
            ax2.set_ylabel('频次')
            ax2.legend()
            ax2.grid(True, alpha=0.3)
            
            plt.tight_layout()
            
            chart_path = f"{self.report_dir}/charts/profit_distribution_{template_id}_{timestamp}.png"
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            return chart_path
            
        except Exception as e:
            logger.error(f"创建盈亏分布图表时发生错误: {e}")
            return ""
    
    def _generate_custom_html_report(self, template: CustomReportTemplate, template_vars: Dict[str, Any], base_filename: str) -> str:
        """生成自定义HTML报告"""
        try:
            # 渲染模板
            jinja_template = self.template_env.from_string(template.template_content)
            rendered_html = jinja_template.render(**template_vars)
            
            # 保存文件
            report_path = f"{self.report_dir}/custom/{base_filename}.html"
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(rendered_html)
            
            logger.info(f"自定义HTML报告生成完成: {report_path}")
            return report_path
            
        except Exception as e:
            logger.error(f"生成自定义HTML报告时发生错误: {e}")
            raise
    
    def _generate_custom_pdf_report(self, template: CustomReportTemplate, template_vars: Dict[str, Any], base_filename: str) -> str:
        """生成自定义PDF报告"""
        try:
            if not REPORTLAB_AVAILABLE:
                logger.warning("ReportLab未安装，无法生成PDF报告")
                return ""
            
            # 先生成HTML，然后转换为PDF（简化实现）
            html_content = self.template_env.from_string(template.template_content).render(**template_vars)
            
            # 创建PDF文档
            report_path = f"{self.report_dir}/custom/{base_filename}.pdf"
            doc = SimpleDocTemplate(report_path, pagesize=A4)
            
            # 获取样式
            styles = getSampleStyleSheet()
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=16,
                spaceAfter=30,
                alignment=1  # 居中
            )
            
            # 构建PDF内容
            story = []
            story.append(Paragraph(template.template_name, title_style))
            story.append(Spacer(1, 12))
            
            # 添加基本信息
            info_data = [
                ['生成时间', template_vars.get('generation_time', '')],
                ['模板类型', template.template_type],
                ['报告章节', ', '.join(template.sections)]
            ]
            
            info_table = Table(info_data, colWidths=[2*inch, 4*inch])
            info_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(info_table)
            story.append(Spacer(1, 12))
            
            # 添加数据表格（简化）
            if 'data' in template_vars and isinstance(template_vars['data'], dict):
                data_items = list(template_vars['data'].items())[:10]  # 限制显示前10项
                if data_items:
                    data_table_data = [['指标', '数值']]
                    for key, value in data_items:
                        data_table_data.append([str(key), str(value)])
                    
                    data_table = Table(data_table_data, colWidths=[3*inch, 3*inch])
                    data_table.setStyle(TableStyle([
                        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                        ('FONTSIZE', (0, 0), (-1, 0), 10),
                        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                        ('GRID', (0, 0), (-1, -1), 1, colors.black)
                    ]))
                    
                    story.append(data_table)
            
            # 构建PDF
            doc.build(story)
            
            logger.info(f"自定义PDF报告生成完成: {report_path}")
            return report_path
            
        except Exception as e:
            logger.error(f"生成自定义PDF报告时发生错误: {e}")
            raise
    
    def _generate_custom_excel_report(self, template: CustomReportTemplate, data: Dict[str, Any], base_filename: str) -> str:
        """生成自定义Excel报告"""
        try:
            wb = Workbook()
            ws = wb.active
            ws.title = template.template_name
            
            # 设置标题
            ws['A1'] = template.template_name
            ws['A1'].font = Font(size=16, bold=True)
            ws.merge_cells('A1:D1')
            
            # 添加基本信息
            row = 3
            ws[f'A{row}'] = "报告信息"
            ws[f'A{row}'].font = Font(bold=True)
            row += 1
            
            info_items = [
                ("模板类型", template.template_type),
                ("生成时间", datetime.now().strftime('%Y-%m-%d %H:%M:%S')),
                ("报告章节", ', '.join(template.sections)),
                ("图表数量", len(template.charts))
            ]
            
            for key, value in info_items:
                ws[f'A{row}'] = key
                ws[f'B{row}'] = value
                row += 1
            
            # 添加数据
            if data:
                row += 1
                ws[f'A{row}'] = "报告数据"
                ws[f'A{row}'].font = Font(bold=True)
                row += 1
                
                # 将数据转换为表格
                if isinstance(data, dict):
                    for key, value in data.items():
                        ws[f'A{row}'] = str(key)
                        ws[f'B{row}'] = str(value)
                        row += 1
            
            # 保存文件
            report_path = f"{self.report_dir}/custom/{base_filename}.xlsx"
            wb.save(report_path)
            
            logger.info(f"自定义Excel报告生成完成: {report_path}")
            return report_path
            
        except Exception as e:
            logger.error(f"生成自定义Excel报告时发生错误: {e}")
            raise
    
    def _generate_custom_json_report(self, template: CustomReportTemplate, data: Dict[str, Any], base_filename: str) -> str:
        """生成自定义JSON报告"""
        try:
            report_data = {
                "report_type": "custom",
                "template_id": template.template_id,
                "template_name": template.template_name,
                "template_type": template.template_type,
                "generation_time": datetime.now().isoformat(),
                "sections": template.sections,
                "charts": template.charts,
                "data": data
            }
            
            report_path = f"{self.report_dir}/custom/{base_filename}.json"
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2, default=str)
            
            logger.info(f"自定义JSON报告生成完成: {report_path}")
            return report_path
            
        except Exception as e:
            logger.error(f"生成自定义JSON报告时发生错误: {e}")
            raise
    
    def _create_zip_export(self, report_paths: List[str], export_config: ReportExportConfig, output_path: str) -> str:
        """创建ZIP导出"""
        try:
            zip_path = f"{output_path}.zip"
            
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED, compresslevel=export_config.compression_level) as zipf:
                for report_path in report_paths:
                    if os.path.exists(report_path):
                        # 使用相对路径作为ZIP内的文件名
                        arcname = os.path.basename(report_path)
                        zipf.write(report_path, arcname)
                
                # 如果包含原始数据，添加数据文件
                if export_config.include_raw_data:
                    data_summary = self.generate_report_summary(
                        start_date=datetime.now().date() - timedelta(days=30),
                        end_date=datetime.now().date()
                    )
                    
                    data_json = json.dumps(data_summary, ensure_ascii=False, indent=2, default=str)
                    zipf.writestr("raw_data_summary.json", data_json)
            
            # 如果需要密码保护
            if export_config.password_protected and export_config.password:
                # 注意: zipfile模块不支持密码保护，这里只是示例
                logger.warning("ZIP密码保护功能需要额外的库支持")
            
            logger.info(f"ZIP导出完成: {zip_path}")
            return zip_path
            
        except Exception as e:
            logger.error(f"创建ZIP导出时发生错误: {e}")
            raise
    
    def _create_merged_pdf_export(self, report_paths: List[str], export_config: ReportExportConfig, output_path: str) -> str:
        """创建合并PDF导出"""
        try:
            if not REPORTLAB_AVAILABLE:
                logger.warning("ReportLab未安装，无法创建合并PDF导出")
                return ""
            
            # 这里需要使用PyPDF2或类似库来合并PDF
            # 简化实现：创建一个包含所有报告信息的新PDF
            
            pdf_path = f"{output_path}.pdf"
            doc = SimpleDocTemplate(pdf_path, pagesize=A4)
            
            styles = getSampleStyleSheet()
            title_style = ParagraphStyle(
                'MergedTitle',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=30,
                alignment=1
            )
            
            story = []
            story.append(Paragraph("合并报告导出", title_style))
            story.append(Spacer(1, 12))
            
            # 添加报告列表
            report_data = [['序号', '报告文件', '文件大小']]
            for i, report_path in enumerate(report_paths, 1):
                if os.path.exists(report_path):
                    file_size = os.path.getsize(report_path)
                    report_data.append([str(i), os.path.basename(report_path), f"{file_size} bytes"])
            
            report_table = Table(report_data, colWidths=[1*inch, 3*inch, 2*inch])
            report_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(report_table)
            doc.build(story)
            
            logger.info(f"合并PDF导出完成: {pdf_path}")
            return pdf_path
            
        except Exception as e:
            logger.error(f"创建合并PDF导出时发生错误: {e}")
            raise
    
    def _create_merged_excel_export(self, report_paths: List[str], export_config: ReportExportConfig, output_path: str) -> str:
        """创建合并Excel导出"""
        try:
            excel_path = f"{output_path}.xlsx"
            wb = Workbook()
            
            # 删除默认工作表
            wb.remove(wb.active)
            
            # 为每个报告创建一个工作表
            for i, report_path in enumerate(report_paths):
                if os.path.exists(report_path) and report_path.endswith('.xlsx'):
                    try:
                        # 读取源Excel文件
                        source_wb = pd.ExcelFile(report_path)
                        
                        for sheet_name in source_wb.sheet_names:
                            df = pd.read_excel(report_path, sheet_name=sheet_name)
                            
                            # 创建新工作表
                            ws_name = f"Report_{i+1}_{sheet_name}"[:31]  # Excel工作表名称限制
                            ws = wb.create_sheet(title=ws_name)
                            
                            # 写入数据
                            for r in dataframe_to_rows(df, index=False, header=True):
                                ws.append(r)
                    
                    except Exception as e:
                        logger.warning(f"无法处理Excel文件 {report_path}: {e}")
                        continue
            
            # 如果没有成功添加任何工作表，创建一个汇总表
            if not wb.worksheets:
                ws = wb.create_sheet(title="报告汇总")
                ws['A1'] = "合并Excel导出"
                ws['A1'].font = Font(size=16, bold=True)
                
                ws['A3'] = "包含的报告文件:"
                ws['A3'].font = Font(bold=True)
                
                for i, report_path in enumerate(report_paths, 4):
                    ws[f'A{i}'] = os.path.basename(report_path)
            
            wb.save(excel_path)
            
            logger.info(f"合并Excel导出完成: {excel_path}")
            return excel_path
            
        except Exception as e:
            logger.error(f"创建合并Excel导出时发生错误: {e}")
            raise
    
    def _summarize_daily_performance(self, daily_reports: List[Any]) -> Dict[str, Any]:
        """汇总日度业绩"""
        if not daily_reports:
            return {}
        
        total_pnl = sum(r.daily_pnl for r in daily_reports)
        total_trades = sum(r.total_trades for r in daily_reports)
        avg_daily_pnl = total_pnl / len(daily_reports)
        
        return {
            'total_pnl': total_pnl,
            'avg_daily_pnl': avg_daily_pnl,
            'total_trades': total_trades,
            'avg_daily_trades': total_trades / len(daily_reports),
            'trading_days': len(daily_reports)
        }
    
    def _summarize_weekly_performance(self, weekly_reports: List[Any]) -> Dict[str, Any]:
        """汇总周度业绩"""
        if not weekly_reports:
            return {}
        
        total_return = sum(r.weekly_return for r in weekly_reports)
        avg_weekly_return = total_return / len(weekly_reports)
        
        return {
            'total_return': total_return,
            'avg_weekly_return': avg_weekly_return,
            'weeks_count': len(weekly_reports)
        }
    
    def _summarize_monthly_performance(self, monthly_reports: List[Any]) -> Dict[str, Any]:
        """汇总月度业绩"""
        if not monthly_reports:
            return {}
        
        total_return = sum(r.monthly_return for r in monthly_reports)
        avg_monthly_return = total_return / len(monthly_reports)
        
        return {
            'total_return': total_return,
            'avg_monthly_return': avg_monthly_return,
            'months_count': len(monthly_reports)
        }
    
    def _calculate_overall_metrics(self, performance_summary: Dict[str, Any]) -> Dict[str, Any]:
        """计算整体关键指标"""
        metrics = {}
        
        # 从各类型报告中提取关键指标
        if 'daily' in performance_summary:
            daily_data = performance_summary['daily']
            metrics['total_pnl'] = daily_data.get('total_pnl', 0)
            metrics['avg_daily_pnl'] = daily_data.get('avg_daily_pnl', 0)
            metrics['total_trades'] = daily_data.get('total_trades', 0)
        
        if 'weekly' in performance_summary:
            weekly_data = performance_summary['weekly']
            metrics['total_weekly_return'] = weekly_data.get('total_return', 0)
        
        if 'monthly' in performance_summary:
            monthly_data = performance_summary['monthly']
            metrics['total_monthly_return'] = monthly_data.get('total_return', 0)
        
        return metrics


# 默认自定义模板示例
DEFAULT_CUSTOM_TEMPLATE = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ template_name }}</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #007acc; padding-bottom: 20px; }
        .header h1 { color: #007acc; margin: 0; font-size: 28px; }
        .section { margin: 30px 0; }
        .section h2 { color: #007acc; border-bottom: 2px solid #e9ecef; padding-bottom: 10px; }
        .metrics-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .metric-card { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #007acc; }
        .chart-container { margin: 20px 0; text-align: center; }
        .chart-container img { max-width: 100%; height: auto; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        .footer { text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #dee2e6; color: #6c757d; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{ template_name }}</h1>
            <p>生成时间: {{ generation_time }}</p>
        </div>
        
        <div class="section">
            <h2>📊 数据概览</h2>
            <div class="metrics-grid">
                {% for key, value in data.items() %}
                <div class="metric-card">
                    <h3>{{ key }}</h3>
                    <div class="metric-value">{{ value }}</div>
                </div>
                {% endfor %}
            </div>
        </div>
        
        {% if charts %}
        <div class="section">
            <h2>📈 图表分析</h2>
            {% for chart_name, chart_path in charts.items() %}
            <div class="chart-container">
                <h3>{{ chart_name }}</h3>
                <img src="{{ chart_path }}" alt="{{ chart_name }}">
            </div>
            {% endfor %}
        </div>
        {% endif %}
        
        <div class="footer">
            <p>本报告由Qlib交易系统自动生成 | 生成时间: {{ generation_time }}</p>
        </div>
    </div>
</body>
</html>
"""