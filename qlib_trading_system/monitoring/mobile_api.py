"""
移动端监控API - 为移动应用提供监控数据接口
Mobile API - Provides monitoring data interface for mobile applications
"""

import jwt
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from fastapi import FastAPI, HTTPException, Depends, Header, Query
from fastapi.security import H<PERSON><PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from .metrics_collector import MetricsCollector, TradingMetrics, AlertEvent
from ..utils.logging.logger import get_logger

logger = get_logger(__name__)

# Pydantic模型定义
class LoginRequest(BaseModel):
    """登录请求模型"""
    username: str
    password: str
    device_id: Optional[str] = None

class LoginResponse(BaseModel):
    """登录响应模型"""
    access_token: str
    token_type: str = "bearer"
    expires_in: int
    user_info: Dict[str, Any]

class MetricsResponse(BaseModel):
    """指标响应模型"""
    timestamp: str
    data: Dict[str, Any]
    status: str = "success"

class AlertResponse(BaseModel):
    """报警响应模型"""
    alerts: List[Dict[str, Any]]
    total_count: int
    unresolved_count: int

class MobileAPI:
    """移动端API接口"""
    
    def __init__(self, metrics_collector: MetricsCollector, config: Dict = None):
        """
        初始化移动端API
        
        Args:
            metrics_collector: 指标收集器实例
            config: 配置参数
        """
        self.metrics_collector = metrics_collector
        self.config = config or {}
        
        # FastAPI应用
        self.app = FastAPI(
            title="Qlib交易系统移动端API",
            description="为移动应用提供实时交易监控数据",
            version="1.0.0"
        )
        
        # JWT配置
        self.jwt_secret = self.config.get('jwt_secret', 'your-secret-key-change-in-production')
        self.jwt_algorithm = self.config.get('jwt_algorithm', 'HS256')
        self.jwt_expiration = self.config.get('jwt_expiration', 3600)  # 1小时
        
        # 用户配置（实际应用中应该从数据库获取）
        self.users = self.config.get('users', {
            'test_admin': {
                'password_hash': self._hash_password('secret123'),
                'role': 'admin',
                'permissions': ['read', 'write']
            },
            'admin': {
                'password_hash': self._hash_password('admin123'),
                'role': 'admin',
                'permissions': ['read', 'write']
            },
            'trader': {
                'password_hash': self._hash_password('trader123'),
                'role': 'trader',
                'permissions': ['read']
            }
        })
        
        # 安全配置
        self.security = HTTPBearer()
        
        # 设置CORS
        self._setup_cors()
        
        # 设置路由
        self._setup_routes()
        
        logger.info("移动端API初始化完成")
    
    def _setup_cors(self):
        """设置跨域资源共享"""
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=self.config.get('allowed_origins', ["*"]),
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
    
    def _hash_password(self, password: str) -> str:
        """密码哈希"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def _verify_password(self, password: str, password_hash: str) -> bool:
        """验证密码"""
        return self._hash_password(password) == password_hash
    
    def _create_access_token(self, username: str, user_info: Dict) -> str:
        """创建访问令牌"""
        payload = {
            'username': username,
            'role': user_info['role'],
            'permissions': user_info['permissions'],
            'exp': datetime.utcnow() + timedelta(seconds=self.jwt_expiration),
            'iat': datetime.utcnow()
        }
        return jwt.encode(payload, self.jwt_secret, algorithm=self.jwt_algorithm)
    
    def _verify_token(self, token: str) -> Dict:
        """验证访问令牌"""
        try:
            payload = jwt.decode(token, self.jwt_secret, algorithms=[self.jwt_algorithm])
            return payload
        except jwt.ExpiredSignatureError:
            raise HTTPException(status_code=401, detail="Token已过期")
        except jwt.InvalidTokenError:
            raise HTTPException(status_code=401, detail="无效的Token")
    
    def _get_current_user(self, credentials: HTTPAuthorizationCredentials = Depends(HTTPBearer())):
        """获取当前用户"""
        token = credentials.credentials
        payload = self._verify_token(token)
        return payload
    
    def _setup_routes(self):
        """设置API路由"""
        
        @self.app.post("/api/mobile/auth/login", response_model=LoginResponse)
        async def login(request: LoginRequest):
            """用户登录"""
            try:
                username = request.username
                password = request.password
                
                # 验证用户
                if username not in self.users:
                    raise HTTPException(status_code=401, detail="用户名或密码错误")
                
                user_info = self.users[username]
                if not self._verify_password(password, user_info['password_hash']):
                    raise HTTPException(status_code=401, detail="用户名或密码错误")
                
                # 创建访问令牌
                access_token = self._create_access_token(username, user_info)
                
                logger.info(f"用户登录成功: {username}")
                
                return LoginResponse(
                    access_token=access_token,
                    expires_in=self.jwt_expiration,
                    user_info={
                        'username': username,
                        'role': user_info['role'],
                        'permissions': user_info['permissions']
                    }
                )
                
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"登录失败: {e}")
                raise HTTPException(status_code=500, detail="登录失败")
        
        @self.app.get("/api/mobile/metrics/current", response_model=MetricsResponse)
        async def get_current_metrics(current_user: Dict = Depends(self._get_current_user)):
            """获取当前指标"""
            try:
                if 'read' not in current_user['permissions']:
                    raise HTTPException(status_code=403, detail="权限不足")
                
                metrics = self.metrics_collector.get_current_metrics()
                
                # 为移动端优化数据格式
                mobile_data = {
                    'pnl': {
                        'total': round(metrics.total_pnl, 2),
                        'daily': round(metrics.daily_pnl, 2),
                        'unrealized': round(metrics.unrealized_pnl, 2),
                        'realized': round(metrics.realized_pnl, 2)
                    },
                    'position': {
                        'value': round(metrics.total_position_value, 2),
                        'cash': round(metrics.cash_balance, 2),
                        'count': metrics.position_count,
                        'leverage': round(metrics.leverage_ratio, 2)
                    },
                    'trading': {
                        'daily_trades': metrics.daily_trades,
                        'win_rate': round(metrics.win_rate * 100, 1),
                        'avg_profit': round(metrics.avg_profit_per_trade, 2)
                    },
                    'risk': {
                        'max_drawdown': round(metrics.max_drawdown * 100, 2),
                        'var_1d': round(metrics.var_1d, 2),
                        'volatility': round(metrics.volatility * 100, 2),
                        'sharpe_ratio': round(metrics.sharpe_ratio, 2)
                    },
                    'system': {
                        'cpu_usage': round(metrics.cpu_usage, 1),
                        'memory_usage': round(metrics.memory_usage, 1),
                        'network_latency': round(metrics.network_latency, 1)
                    }
                }
                
                return MetricsResponse(
                    timestamp=metrics.timestamp.isoformat(),
                    data=mobile_data
                )
                
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"获取当前指标失败: {e}")
                raise HTTPException(status_code=500, detail="获取指标失败")
        
        @self.app.get("/api/mobile/metrics/history")
        async def get_metrics_history(
            minutes: int = Query(60, ge=1, le=1440),
            current_user: Dict = Depends(self._get_current_user)
        ):
            """获取历史指标"""
            try:
                if 'read' not in current_user['permissions']:
                    raise HTTPException(status_code=403, detail="权限不足")
                
                history = self.metrics_collector.get_metrics_history(minutes)
                
                # 为移动端简化数据
                mobile_history = []
                for metrics in history:
                    mobile_history.append({
                        'timestamp': metrics.timestamp.isoformat(),
                        'total_pnl': round(metrics.total_pnl, 2),
                        'daily_pnl': round(metrics.daily_pnl, 2),
                        'position_value': round(metrics.total_position_value, 2),
                        'max_drawdown': round(metrics.max_drawdown * 100, 2)
                    })
                
                return {
                    'data': mobile_history,
                    'count': len(mobile_history),
                    'time_range_minutes': minutes
                }
                
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"获取历史指标失败: {e}")
                raise HTTPException(status_code=500, detail="获取历史指标失败")
        
        @self.app.get("/api/mobile/alerts", response_model=AlertResponse)
        async def get_alerts(
            minutes: int = Query(60, ge=1, le=1440),
            level: Optional[str] = Query(None, regex="^(INFO|WARNING|ERROR|CRITICAL)$"),
            current_user: Dict = Depends(self._get_current_user)
        ):
            """获取报警信息"""
            try:
                if 'read' not in current_user['permissions']:
                    raise HTTPException(status_code=403, detail="权限不足")
                
                alerts = self.metrics_collector.get_recent_alerts(minutes)
                
                # 按级别过滤
                if level:
                    alerts = [a for a in alerts if a.level == level]
                
                # 为移动端格式化数据
                mobile_alerts = []
                unresolved_count = 0
                
                for alert in alerts:
                    mobile_alert = {
                        'id': f"{alert.timestamp.timestamp()}_{alert.category}",
                        'timestamp': alert.timestamp.isoformat(),
                        'level': alert.level,
                        'category': alert.category,
                        'message': alert.message,
                        'resolved': alert.resolved,
                        'details': alert.details
                    }
                    mobile_alerts.append(mobile_alert)
                    
                    if not alert.resolved:
                        unresolved_count += 1
                
                return AlertResponse(
                    alerts=mobile_alerts,
                    total_count=len(mobile_alerts),
                    unresolved_count=unresolved_count
                )
                
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"获取报警信息失败: {e}")
                raise HTTPException(status_code=500, detail="获取报警信息失败")
        
        @self.app.get("/api/mobile/summary")
        async def get_summary(current_user: Dict = Depends(self._get_current_user)):
            """获取汇总信息"""
            try:
                if 'read' not in current_user['permissions']:
                    raise HTTPException(status_code=403, detail="权限不足")
                
                summary = self.metrics_collector.get_summary_stats()
                current_metrics = self.metrics_collector.get_current_metrics()
                recent_alerts = self.metrics_collector.get_recent_alerts(60)
                
                # 计算关键指标
                critical_alerts = len([a for a in recent_alerts if a.level == 'CRITICAL'])
                error_alerts = len([a for a in recent_alerts if a.level == 'ERROR'])
                
                mobile_summary = {
                    'status': 'healthy' if critical_alerts == 0 else 'warning' if error_alerts == 0 else 'critical',
                    'pnl_today': round(current_metrics.daily_pnl, 2),
                    'total_pnl': round(current_metrics.total_pnl, 2),
                    'position_count': current_metrics.position_count,
                    'alerts_count': len(recent_alerts),
                    'critical_alerts': critical_alerts,
                    'system_health': {
                        'cpu': round(current_metrics.cpu_usage, 1),
                        'memory': round(current_metrics.memory_usage, 1),
                        'network': round(current_metrics.network_latency, 1)
                    },
                    'last_update': current_metrics.timestamp.isoformat()
                }
                
                return mobile_summary
                
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"获取汇总信息失败: {e}")
                raise HTTPException(status_code=500, detail="获取汇总信息失败")
        
        @self.app.post("/api/mobile/alerts/{alert_id}/resolve")
        async def resolve_alert(
            alert_id: str,
            current_user: Dict = Depends(self._get_current_user)
        ):
            """标记报警为已解决"""
            try:
                if 'write' not in current_user['permissions']:
                    raise HTTPException(status_code=403, detail="权限不足")
                
                # 这里应该实现实际的报警解决逻辑
                # 目前只是返回成功响应
                logger.info(f"用户 {current_user['username']} 解决了报警: {alert_id}")
                
                return {"message": "报警已标记为解决", "alert_id": alert_id}
                
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"解决报警失败: {e}")
                raise HTTPException(status_code=500, detail="解决报警失败")
        
        @self.app.get("/api/mobile/health")
        async def health_check():
            """健康检查"""
            return {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "version": "1.0.0"
            }
        
        @self.app.get("/api/mobile/anomalies")
        async def get_anomalies(
            minutes: int = Query(60, ge=1, le=1440),
            current_user: Dict = Depends(self._get_current_user)
        ):
            """获取异常事件"""
            try:
                if 'read' not in current_user['permissions']:
                    raise HTTPException(status_code=403, detail="权限不足")
                
                # 这里应该从实际的异常检测系统获取数据
                # 目前返回模拟数据
                anomalies = [
                    {
                        'id': f'anomaly_{i}',
                        'timestamp': (datetime.now() - timedelta(minutes=i*5)).isoformat(),
                        'type': 'PNL_ANOMALY' if i % 2 == 0 else 'TRADING_FREQUENCY_ANOMALY',
                        'message': f'检测到异常事件 {i}',
                        'severity': 'HIGH' if i % 3 == 0 else 'MEDIUM'
                    }
                    for i in range(min(5, minutes // 10))
                ]
                
                return {
                    'anomalies': anomalies,
                    'total_count': len(anomalies),
                    'time_range_minutes': minutes
                }
                
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"获取异常事件失败: {e}")
                raise HTTPException(status_code=500, detail="获取异常事件失败")
        
        @self.app.get("/api/mobile/system/status")
        async def get_system_status(current_user: Dict = Depends(self._get_current_user)):
            """获取系统状态"""
            try:
                if 'read' not in current_user['permissions']:
                    raise HTTPException(status_code=403, detail="权限不足")
                
                current_metrics = self.metrics_collector.get_current_metrics()
                recent_alerts = self.metrics_collector.get_recent_alerts(60)
                
                # 计算系统健康状态
                critical_alerts = len([a for a in recent_alerts if a.level == 'CRITICAL'])
                error_alerts = len([a for a in recent_alerts if a.level == 'ERROR'])
                
                if critical_alerts > 0:
                    status = 'CRITICAL'
                    status_message = f'{critical_alerts}个严重报警'
                elif error_alerts > 0:
                    status = 'ERROR'
                    status_message = f'{error_alerts}个错误报警'
                elif current_metrics.cpu_usage > 80 or current_metrics.memory_usage > 85:
                    status = 'WARNING'
                    status_message = '系统资源使用率较高'
                else:
                    status = 'HEALTHY'
                    status_message = '系统运行正常'
                
                return {
                    'status': status,
                    'status_message': status_message,
                    'timestamp': datetime.now().isoformat(),
                    'system_metrics': {
                        'cpu_usage': round(current_metrics.cpu_usage, 1),
                        'memory_usage': round(current_metrics.memory_usage, 1),
                        'network_latency': round(current_metrics.network_latency, 1)
                    },
                    'trading_status': {
                        'total_pnl': round(current_metrics.total_pnl, 2),
                        'daily_pnl': round(current_metrics.daily_pnl, 2),
                        'position_count': current_metrics.position_count,
                        'daily_trades': current_metrics.daily_trades
                    },
                    'alerts_summary': {
                        'critical': critical_alerts,
                        'error': error_alerts,
                        'warning': len([a for a in recent_alerts if a.level == 'WARNING']),
                        'total': len(recent_alerts)
                    }
                }
                
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"获取系统状态失败: {e}")
                raise HTTPException(status_code=500, detail="获取系统状态失败")
        
        @self.app.get("/api/mobile/user/profile")
        async def get_user_profile(current_user: Dict = Depends(self._get_current_user)):
            """获取用户信息"""
            return {
                "username": current_user['username'],
                "role": current_user['role'],
                "permissions": current_user['permissions'],
                "login_time": datetime.now().isoformat()
            }

class MobileAPIManager:
    """移动端API管理器"""
    
    def __init__(self, metrics_collector: MetricsCollector, config: Dict = None):
        """
        初始化移动端API管理器
        
        Args:
            metrics_collector: 指标收集器实例
            config: 配置参数
        """
        self.config = config or {}
        self.mobile_api = MobileAPI(metrics_collector, config)
        
        # 服务器配置
        self.host = self.config.get('host', '0.0.0.0')
        self.port = self.config.get('port', 8081)
        
        logger.info("移动端API管理器初始化完成")
    
    def start_server(self):
        """启动移动端API服务器"""
        try:
            import uvicorn
            logger.info(f"启动移动端API服务器: http://{self.host}:{self.port}")
            uvicorn.run(
                self.mobile_api.app,
                host=self.host,
                port=self.port,
                log_level="info"
            )
        except Exception as e:
            logger.error(f"启动移动端API服务器失败: {e}")
            raise
    
    async def start_server_async(self):
        """异步启动移动端API服务器"""
        try:
            import uvicorn
            config = uvicorn.Config(
                self.mobile_api.app,
                host=self.host,
                port=self.port,
                log_level="info"
            )
            server = uvicorn.Server(config)
            await server.serve()
        except Exception as e:
            logger.error(f"异步启动移动端API服务器失败: {e}")
            raise
    
    def get_api_app(self):
        """获取FastAPI应用实例"""
        return self.mobile_api.app