"""
监控系统配置管理
Monitoring System Configuration Management
"""

from typing import Dict, List, Any
from dataclasses import dataclass, field
import os

try:
    from pydantic_settings import BaseSettings
    from pydantic import validator
except ImportError:
    try:
        from pydantic import BaseSettings, validator
    except ImportError:
        # 如果pydantic不可用，创建一个简单的替代类
        class BaseSettings:
            def __init__(self, **kwargs):
                for key, value in kwargs.items():
                    setattr(self, key, value)
            
            class Config:
                env_file = '.env'
                env_prefix = 'MONITORING_'
        
        def validator(*args, **kwargs):
            def decorator(func):
                return func
            return decorator

@dataclass
class MetricsConfig:
    """指标收集配置"""
    collection_interval: float = 1.0  # 收集间隔（秒）
    history_size: int = 1000  # 历史数据保存数量
    alert_thresholds: Dict[str, float] = field(default_factory=lambda: {
        'max_drawdown': 0.05,      # 5%最大回撤警告
        'daily_loss': 0.03,        # 3%日亏损警告
        'position_concentration': 0.8,  # 80%仓位集中度警告
        'system_cpu': 80.0,        # 80% CPU使用率警告
        'system_memory': 85.0,     # 85%内存使用率警告
        'network_latency': 100.0   # 100ms网络延迟警告
    })

@dataclass
class DashboardConfig:
    """仪表板配置"""
    host: str = '0.0.0.0'
    port: int = 8080
    update_interval: float = 1.0  # 更新间隔（秒）
    template_dir: str = 'qlib_trading_system/monitoring/templates'
    static_dir: str = 'qlib_trading_system/monitoring/static'

@dataclass
class MobileAPIConfig:
    """移动端API配置"""
    host: str = '0.0.0.0'
    port: int = 8081
    jwt_secret: str = 'your-secret-key-change-in-production'
    jwt_algorithm: str = 'HS256'
    jwt_expiration: int = 3600  # 1小时
    allowed_origins: List[str] = field(default_factory=lambda: ["*"])
    users: Dict[str, Dict] = field(default_factory=lambda: {
        'admin': {
            'password_hash': 'ef92b778bafe771e89245b89ecbc08a44a4e166c06659911881f383d4473e94f',  # admin123
            'role': 'admin',
            'permissions': ['read', 'write']
        },
        'trader': {
            'password_hash': '2bb80d537b1da3e38bd30361aa855686bde0eacd7162fef6a25fe97bf527a25b',  # trader123
            'role': 'trader',
            'permissions': ['read']
        }
    })

@dataclass
class NotificationChannelConfig:
    """通知渠道配置"""
    name: str
    type: str  # email, webhook, dingtalk, wechat
    config: Dict[str, Any]
    enabled: bool = True
    min_level: str = 'INFO'

@dataclass
class AlertManagerConfig:
    """报警管理器配置"""
    notification_channels: List[NotificationChannelConfig] = field(default_factory=list)
    suppression: Dict[str, Any] = field(default_factory=lambda: {
        'same_alert_interval': 300,  # 相同报警间隔5分钟
        'max_alerts_per_hour': 20,   # 每小时最大报警数
    })
    alert_rules: Dict[str, Any] = field(default_factory=dict)

class MonitoringSettings(BaseSettings):
    """监控系统设置"""
    
    # 基础配置
    debug: bool = False
    log_level: str = 'INFO'
    
    # 指标收集配置
    metrics_collection_interval: float = 1.0
    metrics_history_size: int = 1000
    
    # 仪表板配置
    dashboard_host: str = '0.0.0.0'
    dashboard_port: int = 8080
    
    # 移动端API配置
    mobile_api_host: str = '0.0.0.0'
    mobile_api_port: int = 8081
    mobile_api_jwt_secret: str = 'your-secret-key-change-in-production'
    
    # 报警阈值
    alert_max_drawdown: float = 0.05
    alert_daily_loss: float = 0.03
    alert_system_cpu: float = 80.0
    alert_system_memory: float = 85.0
    alert_network_latency: float = 100.0
    
    # 通知配置
    email_enabled: bool = False
    email_smtp_server: str = ''
    email_smtp_port: int = 587
    email_username: str = ''
    email_password: str = ''
    email_from: str = ''
    email_to: List[str] = []
    
    webhook_enabled: bool = False
    webhook_url: str = ''
    
    dingtalk_enabled: bool = False
    dingtalk_webhook_url: str = ''
    dingtalk_at_all: bool = False
    
    class Config:
        env_file = '.env'
        env_prefix = 'MONITORING_'
        extra = 'ignore'  # 忽略额外的环境变量
    
    @validator('email_to', pre=True)
    def parse_email_list(cls, v):
        if isinstance(v, str):
            return [email.strip() for email in v.split(',') if email.strip()]
        return v

def create_monitoring_config(settings: MonitoringSettings = None) -> Dict[str, Any]:
    """创建监控系统配置"""
    if settings is None:
        settings = MonitoringSettings()
    
    # 构建通知渠道配置
    notification_channels = []
    
    if settings.email_enabled and settings.email_smtp_server:
        notification_channels.append(NotificationChannelConfig(
            name='email',
            type='email',
            config={
                'smtp_server': settings.email_smtp_server,
                'smtp_port': settings.email_smtp_port,
                'username': settings.email_username,
                'password': settings.email_password,
                'from_email': settings.email_from,
                'to_emails': settings.email_to,
                'use_tls': True
            },
            enabled=True,
            min_level='WARNING'
        ))
    
    if settings.webhook_enabled and settings.webhook_url:
        notification_channels.append(NotificationChannelConfig(
            name='webhook',
            type='webhook',
            config={
                'url': settings.webhook_url,
                'headers': {'Content-Type': 'application/json'},
                'timeout': 10
            },
            enabled=True,
            min_level='WARNING'
        ))
    
    if settings.dingtalk_enabled and settings.dingtalk_webhook_url:
        notification_channels.append(NotificationChannelConfig(
            name='dingtalk',
            type='dingtalk',
            config={
                'webhook_url': settings.dingtalk_webhook_url,
                'at_all': settings.dingtalk_at_all
            },
            enabled=True,
            min_level='WARNING'
        ))
    
    return {
        'metrics': MetricsConfig(
            collection_interval=settings.metrics_collection_interval,
            history_size=settings.metrics_history_size,
            alert_thresholds={
                'max_drawdown': settings.alert_max_drawdown,
                'daily_loss': settings.alert_daily_loss,
                'system_cpu': settings.alert_system_cpu,
                'system_memory': settings.alert_system_memory,
                'network_latency': settings.alert_network_latency
            }
        ),
        'dashboard': DashboardConfig(
            host=settings.dashboard_host,
            port=settings.dashboard_port
        ),
        'mobile_api': MobileAPIConfig(
            host=settings.mobile_api_host,
            port=settings.mobile_api_port,
            jwt_secret=settings.mobile_api_jwt_secret
        ),
        'alert_manager': AlertManagerConfig(
            notification_channels=notification_channels
        )
    }

def load_monitoring_config() -> Dict[str, Any]:
    """加载监控系统配置"""
    try:
        settings = MonitoringSettings()
        return create_monitoring_config(settings)
    except Exception as e:
        # 如果加载配置失败，使用默认配置
        print(f"加载监控配置失败，使用默认配置: {e}")
        return create_default_config()

def create_default_config() -> Dict[str, Any]:
    """创建默认配置"""
    return {
        'metrics': MetricsConfig(),
        'dashboard': DashboardConfig(),
        'mobile_api': MobileAPIConfig(),
        'alert_manager': AlertManagerConfig()
    }

# 默认配置实例
DEFAULT_MONITORING_CONFIG = create_default_config()