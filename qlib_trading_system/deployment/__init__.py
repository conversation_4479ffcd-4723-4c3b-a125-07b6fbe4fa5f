# -*- coding: utf-8 -*-
"""
部署系统模块

提供微服务架构部署、容器化、Kubernetes集群管理等功能
"""

from .docker_manager import DockerManager, DockerConfig
from .kubernetes_manager import KubernetesManager, K8sConfig
from .service_discovery import ServiceDiscovery, ServiceRegistry
from .deployment_pipeline import DeploymentPipeline, PipelineConfig
from .load_balancer import LoadBalancer, LoadBalancerConfig

__all__ = [
    'DockerManager',
    'DockerConfig', 
    'KubernetesManager',
    'K8sConfig',
    'ServiceDiscovery',
    'ServiceRegistry',
    'DeploymentPipeline',
    'PipelineConfig',
    'LoadBalancer',
    'LoadBalancerConfig'
]

__version__ = "1.0.0"

__doc__ = """
部署系统模块

主要组件:
1. DockerManager - Docker容器管理
2. KubernetesManager - Kubernetes集群管理
3. ServiceDiscovery - 服务发现和注册
4. DeploymentPipeline - 自动化部署流水线
5. LoadBalancer - 负载均衡管理

使用示例:
```python
from qlib_trading_system.deployment import (
    DockerManager,
    KubernetesManager,
    DeploymentPipeline
)

# 创建Docker管理器
docker_manager = DockerManager()

# 构建容器镜像
docker_manager.build_image('qlib-trading-system', '.')

# 创建Kubernetes管理器
k8s_manager = KubernetesManager()

# 部署到Kubernetes
k8s_manager.deploy_application('qlib-trading-system')

# 创建部署流水线
pipeline = DeploymentPipeline()

# 执行自动化部署
pipeline.deploy('production', 'v1.0.0')
```
"""
