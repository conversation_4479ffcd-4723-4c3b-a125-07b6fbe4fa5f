# -*- coding: utf-8 -*-
"""
Kubernetes集群管理器

提供Kubernetes部署、服务管理、配置管理等功能
"""

import os
import json
import logging
import subprocess
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from pathlib import Path
import yaml

logger = logging.getLogger(__name__)


@dataclass
class K8sConfig:
    """Kubernetes配置"""
    # 集群配置
    namespace: str = "qlib-trading"
    cluster_name: str = "qlib-cluster"
    kubeconfig_path: str = "~/.kube/config"
    
    # 镜像配置
    registry_url: str = "localhost:5000"
    image_prefix: str = "qlib-trading"
    image_pull_policy: str = "Always"
    
    # 资源配置
    default_cpu_request: str = "100m"
    default_cpu_limit: str = "500m"
    default_memory_request: str = "256Mi"
    default_memory_limit: str = "1Gi"
    
    # 服务配置
    services: Dict[str, Dict[str, Any]] = field(default_factory=lambda: {
        'api': {
            'port': 8000,
            'replicas': 2,
            'service_type': 'ClusterIP',
            'health_check_path': '/health'
        },
        'data-processor': {
            'port': 8001,
            'replicas': 1,
            'service_type': 'ClusterIP',
            'health_check_path': '/health'
        },
        'model-server': {
            'port': 8002,
            'replicas': 1,
            'service_type': 'ClusterIP',
            'health_check_path': '/health'
        },
        'risk-monitor': {
            'port': 8003,
            'replicas': 1,
            'service_type': 'ClusterIP',
            'health_check_path': '/health'
        }
    })
    
    # 存储配置
    storage_class: str = "standard"
    persistent_volume_size: str = "10Gi"


class KubernetesManager:
    """Kubernetes集群管理器"""
    
    def __init__(self, config: K8sConfig = None):
        """初始化Kubernetes管理器"""
        self.config = config or K8sConfig()
        
        # 创建部署目录
        self.k8s_dir = Path("deployment/k8s")
        self.k8s_dir.mkdir(parents=True, exist_ok=True)
        
        # 验证kubectl连接
        self._verify_kubectl_connection()
        
        logger.info("Kubernetes管理器初始化完成")
    
    def _verify_kubectl_connection(self) -> bool:
        """验证kubectl连接"""
        try:
            result = subprocess.run(
                ["kubectl", "cluster-info"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                logger.info("kubectl连接验证成功")
                return True
            else:
                logger.warning(f"kubectl连接验证失败: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.warning("kubectl连接验证超时")
            return False
        except FileNotFoundError:
            logger.warning("kubectl命令未找到，请确保已安装kubectl")
            return False
        except Exception as e:
            logger.warning(f"kubectl连接验证出错: {e}")
            return False
    
    def create_namespace(self) -> bool:
        """创建命名空间"""
        namespace_config = {
            'apiVersion': 'v1',
            'kind': 'Namespace',
            'metadata': {
                'name': self.config.namespace,
                'labels': {
                    'app': 'qlib-trading-system',
                    'environment': 'production'
                }
            }
        }
        
        # 保存命名空间配置
        namespace_path = self.k8s_dir / "namespace.yaml"
        with open(namespace_path, 'w', encoding='utf-8') as f:
            yaml.dump(namespace_config, f, default_flow_style=False)
        
        # 应用配置
        return self._apply_config(namespace_path)
    
    def create_deployment(self, service_name: str, service_config: Dict[str, Any]) -> str:
        """创建服务部署配置"""
        deployment_config = {
            'apiVersion': 'apps/v1',
            'kind': 'Deployment',
            'metadata': {
                'name': f'{service_name}-deployment',
                'namespace': self.config.namespace,
                'labels': {
                    'app': service_name,
                    'component': 'qlib-trading-system'
                }
            },
            'spec': {
                'replicas': service_config.get('replicas', 1),
                'selector': {
                    'matchLabels': {
                        'app': service_name
                    }
                },
                'template': {
                    'metadata': {
                        'labels': {
                            'app': service_name,
                            'component': 'qlib-trading-system'
                        }
                    },
                    'spec': {
                        'containers': [{
                            'name': service_name,
                            'image': f"{self.config.registry_url}/{self.config.image_prefix}-{service_name}:latest",
                            'imagePullPolicy': self.config.image_pull_policy,
                            'ports': [{
                                'containerPort': service_config['port'],
                                'name': 'http'
                            }],
                            'env': [
                                {'name': 'SERVICE_NAME', 'value': service_name},
                                {'name': 'SERVICE_PORT', 'value': str(service_config['port'])},
                                {'name': 'ENVIRONMENT', 'value': 'production'},
                                {'name': 'LOG_LEVEL', 'value': 'INFO'}
                            ],
                            'resources': {
                                'requests': {
                                    'cpu': self.config.default_cpu_request,
                                    'memory': self.config.default_memory_request
                                },
                                'limits': {
                                    'cpu': self.config.default_cpu_limit,
                                    'memory': self.config.default_memory_limit
                                }
                            },
                            'livenessProbe': {
                                'httpGet': {
                                    'path': service_config.get('health_check_path', '/health'),
                                    'port': service_config['port']
                                },
                                'initialDelaySeconds': 30,
                                'periodSeconds': 10,
                                'timeoutSeconds': 5,
                                'failureThreshold': 3
                            },
                            'readinessProbe': {
                                'httpGet': {
                                    'path': service_config.get('health_check_path', '/health'),
                                    'port': service_config['port']
                                },
                                'initialDelaySeconds': 5,
                                'periodSeconds': 5,
                                'timeoutSeconds': 3,
                                'failureThreshold': 3
                            }
                        }],
                        'restartPolicy': 'Always'
                    }
                }
            }
        }
        
        # 保存部署配置
        deployment_path = self.k8s_dir / f"{service_name}-deployment.yaml"
        with open(deployment_path, 'w', encoding='utf-8') as f:
            yaml.dump(deployment_config, f, default_flow_style=False)
        
        logger.info(f"部署配置已创建: {deployment_path}")
        return str(deployment_path)
    
    def create_service(self, service_name: str, service_config: Dict[str, Any]) -> str:
        """创建服务配置"""
        service_k8s_config = {
            'apiVersion': 'v1',
            'kind': 'Service',
            'metadata': {
                'name': f'{service_name}-service',
                'namespace': self.config.namespace,
                'labels': {
                    'app': service_name,
                    'component': 'qlib-trading-system'
                }
            },
            'spec': {
                'selector': {
                    'app': service_name
                },
                'ports': [{
                    'port': service_config['port'],
                    'targetPort': service_config['port'],
                    'protocol': 'TCP',
                    'name': 'http'
                }],
                'type': service_config.get('service_type', 'ClusterIP')
            }
        }
        
        # 保存服务配置
        service_path = self.k8s_dir / f"{service_name}-service.yaml"
        with open(service_path, 'w', encoding='utf-8') as f:
            yaml.dump(service_k8s_config, f, default_flow_style=False)
        
        logger.info(f"服务配置已创建: {service_path}")
        return str(service_path)
    
    def create_configmap(self, config_data: Dict[str, Any]) -> str:
        """创建配置映射"""
        configmap_config = {
            'apiVersion': 'v1',
            'kind': 'ConfigMap',
            'metadata': {
                'name': 'qlib-trading-config',
                'namespace': self.config.namespace,
                'labels': {
                    'app': 'qlib-trading-system'
                }
            },
            'data': {}
        }
        
        # 转换配置数据
        for key, value in config_data.items():
            if isinstance(value, (dict, list)):
                configmap_config['data'][key] = json.dumps(value, ensure_ascii=False)
            else:
                configmap_config['data'][key] = str(value)
        
        # 保存配置映射
        configmap_path = self.k8s_dir / "configmap.yaml"
        with open(configmap_path, 'w', encoding='utf-8') as f:
            yaml.dump(configmap_config, f, default_flow_style=False)
        
        logger.info(f"配置映射已创建: {configmap_path}")
        return str(configmap_path)
    
    def create_secret(self, secret_data: Dict[str, str]) -> str:
        """创建密钥"""
        import base64
        
        secret_config = {
            'apiVersion': 'v1',
            'kind': 'Secret',
            'metadata': {
                'name': 'qlib-trading-secrets',
                'namespace': self.config.namespace,
                'labels': {
                    'app': 'qlib-trading-system'
                }
            },
            'type': 'Opaque',
            'data': {}
        }
        
        # 编码密钥数据
        for key, value in secret_data.items():
            encoded_value = base64.b64encode(value.encode('utf-8')).decode('utf-8')
            secret_config['data'][key] = encoded_value
        
        # 保存密钥配置
        secret_path = self.k8s_dir / "secrets.yaml"
        with open(secret_path, 'w', encoding='utf-8') as f:
            yaml.dump(secret_config, f, default_flow_style=False)
        
        logger.info(f"密钥配置已创建: {secret_path}")
        return str(secret_path)
    
    def _apply_config(self, config_path: str) -> bool:
        """应用Kubernetes配置"""
        try:
            result = subprocess.run(
                ["kubectl", "apply", "-f", str(config_path)],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0:
                logger.info(f"配置应用成功: {config_path}")
                return True
            else:
                logger.error(f"配置应用失败: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error(f"配置应用超时: {config_path}")
            return False
        except Exception as e:
            logger.error(f"配置应用出错: {e}")
            return False
    
    def deploy_application(self, tag: str = "latest") -> Dict[str, bool]:
        """部署整个应用"""
        results = {}
        
        # 创建命名空间
        results['namespace'] = self.create_namespace()
        
        # 部署各个服务
        for service_name, service_config in self.config.services.items():
            # 创建部署配置
            deployment_path = self.create_deployment(service_name, service_config)
            results[f'{service_name}_deployment'] = self._apply_config(deployment_path)
            
            # 创建服务配置
            service_path = self.create_service(service_name, service_config)
            results[f'{service_name}_service'] = self._apply_config(service_path)
        
        return results


def create_kubernetes_manager(config: K8sConfig = None) -> KubernetesManager:
    """创建Kubernetes管理器实例"""
    return KubernetesManager(config)


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    # 创建Kubernetes管理器
    k8s_manager = create_kubernetes_manager()
    
    # 创建所有配置文件
    for service_name, service_config in k8s_manager.config.services.items():
        k8s_manager.create_deployment(service_name, service_config)
        k8s_manager.create_service(service_name, service_config)
    
    print("Kubernetes配置文件创建完成")
