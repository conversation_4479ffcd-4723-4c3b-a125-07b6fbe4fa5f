# -*- coding: utf-8 -*-
"""
负载均衡器

提供多种负载均衡算法和健康检查功能
"""

import time
import logging
import threading
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import requests
from .service_discovery import ServiceInstance, ServiceDiscovery

logger = logging.getLogger(__name__)


class LoadBalanceStrategy(Enum):
    """负载均衡策略"""
    ROUND_ROBIN = "round_robin"
    WEIGHTED_ROUND_ROBIN = "weighted_round_robin"
    LEAST_CONNECTIONS = "least_connections"
    WEIGHTED_LEAST_CONNECTIONS = "weighted_least_connections"
    RANDOM = "random"
    WEIGHTED_RANDOM = "weighted_random"
    IP_HASH = "ip_hash"
    CONSISTENT_HASH = "consistent_hash"


@dataclass
class LoadBalancerConfig:
    """负载均衡器配置"""
    # 负载均衡策略
    strategy: LoadBalanceStrategy = LoadBalanceStrategy.ROUND_ROBIN
    
    # 健康检查配置
    health_check_enabled: bool = True
    health_check_interval: int = 30  # 秒
    health_check_timeout: int = 5    # 秒
    health_check_retries: int = 3
    
    # 连接跟踪配置
    track_connections: bool = True
    connection_timeout: int = 300    # 秒
    
    # 故障转移配置
    failover_enabled: bool = True
    max_failures: int = 3
    failure_window: int = 60         # 秒
    
    # 会话保持配置
    session_affinity: bool = False
    session_timeout: int = 1800      # 秒


@dataclass
class ConnectionInfo:
    """连接信息"""
    client_id: str
    instance_id: str
    start_time: datetime
    last_activity: datetime
    request_count: int = 0


class LoadBalancer:
    """负载均衡器"""
    
    def __init__(self, service_discovery: ServiceDiscovery, config: LoadBalancerConfig = None):
        """初始化负载均衡器"""
        self.service_discovery = service_discovery
        self.config = config or LoadBalancerConfig()
        
        # 轮询计数器
        self._round_robin_counters = {}
        
        # 连接跟踪
        self._connections = {}  # client_id -> ConnectionInfo
        self._instance_connections = {}  # instance_id -> count
        
        # 实例状态跟踪
        self._instance_failures = {}  # instance_id -> failure_count
        self._instance_last_failure = {}  # instance_id -> datetime
        
        # 会话保持
        self._session_map = {}  # session_id -> instance_id
        
        # 启动健康检查线程
        if self.config.health_check_enabled:
            self._start_health_check_thread()
        
        # 启动连接清理线程
        if self.config.track_connections:
            self._start_connection_cleanup_thread()
        
        logger.info("负载均衡器初始化完成")
    
    def select_instance(self, service_name: str, client_id: str = None, 
                       session_id: str = None) -> Optional[ServiceInstance]:
        """选择服务实例"""
        # 获取可用实例
        instances = self._get_healthy_instances(service_name)
        if not instances:
            logger.warning(f"没有可用的服务实例: {service_name}")
            return None
        
        # 会话保持
        if self.config.session_affinity and session_id:
            if session_id in self._session_map:
                instance_id = self._session_map[session_id]
                for instance in instances:
                    if instance.instance_id == instance_id:
                        return instance
        
        # 应用负载均衡策略
        selected_instance = self._apply_strategy(service_name, instances, client_id)
        
        # 记录连接信息
        if self.config.track_connections and client_id and selected_instance:
            self._track_connection(client_id, selected_instance.instance_id)
        
        # 记录会话映射
        if self.config.session_affinity and session_id and selected_instance:
            self._session_map[session_id] = selected_instance.instance_id
        
        return selected_instance
    
    def _get_healthy_instances(self, service_name: str) -> List[ServiceInstance]:
        """获取健康的服务实例"""
        all_instances = self.service_discovery._get_cached_instances(service_name)
        
        healthy_instances = []
        for instance in all_instances:
            # 检查故障状态
            if self._is_instance_failed(instance.instance_id):
                continue
            
            healthy_instances.append(instance)
        
        return healthy_instances
    
    def _apply_strategy(self, service_name: str, instances: List[ServiceInstance], 
                       client_id: str = None) -> ServiceInstance:
        """应用负载均衡策略"""
        strategy = self.config.strategy
        
        if strategy == LoadBalanceStrategy.ROUND_ROBIN:
            return self._round_robin_select(service_name, instances)
        elif strategy == LoadBalanceStrategy.WEIGHTED_ROUND_ROBIN:
            return self._weighted_round_robin_select(service_name, instances)
        elif strategy == LoadBalanceStrategy.LEAST_CONNECTIONS:
            return self._least_connections_select(instances)
        elif strategy == LoadBalanceStrategy.WEIGHTED_LEAST_CONNECTIONS:
            return self._weighted_least_connections_select(instances)
        elif strategy == LoadBalanceStrategy.RANDOM:
            return self._random_select(instances)
        elif strategy == LoadBalanceStrategy.WEIGHTED_RANDOM:
            return self._weighted_random_select(instances)
        elif strategy == LoadBalanceStrategy.IP_HASH:
            return self._ip_hash_select(instances, client_id)
        elif strategy == LoadBalanceStrategy.CONSISTENT_HASH:
            return self._consistent_hash_select(instances, client_id)
        else:
            return instances[0]  # 默认返回第一个
    
    def _round_robin_select(self, service_name: str, instances: List[ServiceInstance]) -> ServiceInstance:
        """轮询选择"""
        if service_name not in self._round_robin_counters:
            self._round_robin_counters[service_name] = 0
        
        index = self._round_robin_counters[service_name] % len(instances)
        self._round_robin_counters[service_name] += 1
        
        return instances[index]
    
    def _weighted_round_robin_select(self, service_name: str, instances: List[ServiceInstance]) -> ServiceInstance:
        """加权轮询选择"""
        if service_name not in self._round_robin_counters:
            self._round_robin_counters[service_name] = 0
        
        # 构建加权列表
        weighted_instances = []
        for instance in instances:
            weight = max(1, instance.weight)
            weighted_instances.extend([instance] * weight)
        
        index = self._round_robin_counters[service_name] % len(weighted_instances)
        self._round_robin_counters[service_name] += 1
        
        return weighted_instances[index]
    
    def _least_connections_select(self, instances: List[ServiceInstance]) -> ServiceInstance:
        """最少连接选择"""
        min_connections = float('inf')
        selected_instance = instances[0]
        
        for instance in instances:
            connections = self._instance_connections.get(instance.instance_id, 0)
            if connections < min_connections:
                min_connections = connections
                selected_instance = instance
        
        return selected_instance
    
    def _weighted_least_connections_select(self, instances: List[ServiceInstance]) -> ServiceInstance:
        """加权最少连接选择"""
        min_ratio = float('inf')
        selected_instance = instances[0]
        
        for instance in instances:
            connections = self._instance_connections.get(instance.instance_id, 0)
            weight = max(1, instance.weight)
            ratio = connections / weight
            
            if ratio < min_ratio:
                min_ratio = ratio
                selected_instance = instance
        
        return selected_instance
    
    def _random_select(self, instances: List[ServiceInstance]) -> ServiceInstance:
        """随机选择"""
        import random
        return random.choice(instances)
    
    def _weighted_random_select(self, instances: List[ServiceInstance]) -> ServiceInstance:
        """加权随机选择"""
        import random
        
        total_weight = sum(max(1, instance.weight) for instance in instances)
        random_weight = random.randint(1, total_weight)
        current_weight = 0
        
        for instance in instances:
            current_weight += max(1, instance.weight)
            if current_weight >= random_weight:
                return instance
        
        return instances[-1]
    
    def _ip_hash_select(self, instances: List[ServiceInstance], client_id: str) -> ServiceInstance:
        """IP哈希选择"""
        if not client_id:
            return instances[0]
        
        hash_value = hash(client_id)
        index = hash_value % len(instances)
        return instances[index]
    
    def _consistent_hash_select(self, instances: List[ServiceInstance], client_id: str) -> ServiceInstance:
        """一致性哈希选择"""
        if not client_id:
            return instances[0]
        
        # 简化的一致性哈希实现
        hash_ring = {}
        for i, instance in enumerate(instances):
            for j in range(instance.weight):
                key = hash(f"{instance.instance_id}:{j}")
                hash_ring[key] = instance
        
        if not hash_ring:
            return instances[0]
        
        client_hash = hash(client_id)
        sorted_keys = sorted(hash_ring.keys())
        
        # 找到第一个大于client_hash的key
        for key in sorted_keys:
            if key >= client_hash:
                return hash_ring[key]
        
        # 如果没找到，返回第一个
        return hash_ring[sorted_keys[0]]
    
    def _track_connection(self, client_id: str, instance_id: str):
        """跟踪连接"""
        now = datetime.now()
        
        # 更新连接信息
        if client_id in self._connections:
            conn_info = self._connections[client_id]
            conn_info.last_activity = now
            conn_info.request_count += 1
        else:
            self._connections[client_id] = ConnectionInfo(
                client_id=client_id,
                instance_id=instance_id,
                start_time=now,
                last_activity=now,
                request_count=1
            )
        
        # 更新实例连接计数
        self._instance_connections[instance_id] = self._instance_connections.get(instance_id, 0) + 1
    
    def _is_instance_failed(self, instance_id: str) -> bool:
        """检查实例是否失败"""
        if not self.config.failover_enabled:
            return False
        
        failure_count = self._instance_failures.get(instance_id, 0)
        if failure_count < self.config.max_failures:
            return False
        
        last_failure = self._instance_last_failure.get(instance_id)
        if last_failure:
            time_since_failure = (datetime.now() - last_failure).total_seconds()
            if time_since_failure > self.config.failure_window:
                # 重置失败计数
                self._instance_failures[instance_id] = 0
                return False
        
        return True
    
    def report_failure(self, instance_id: str):
        """报告实例失败"""
        if not self.config.failover_enabled:
            return
        
        self._instance_failures[instance_id] = self._instance_failures.get(instance_id, 0) + 1
        self._instance_last_failure[instance_id] = datetime.now()
        
        logger.warning(f"实例失败报告: {instance_id}, 失败次数: {self._instance_failures[instance_id]}")
    
    def _start_health_check_thread(self):
        """启动健康检查线程"""
        def health_check_worker():
            while True:
                try:
                    # 获取所有服务的所有实例
                    all_services = self.service_discovery.registry.get_all_services()
                    
                    for service_name, instances in all_services.items():
                        for instance in instances:
                            if self._perform_health_check(instance):
                                # 健康检查成功，重置失败计数
                                if instance.instance_id in self._instance_failures:
                                    self._instance_failures[instance.instance_id] = 0
                            else:
                                # 健康检查失败
                                self.report_failure(instance.instance_id)
                    
                    time.sleep(self.config.health_check_interval)
                    
                except Exception as e:
                    logger.error(f"健康检查线程出错: {e}")
                    time.sleep(self.config.health_check_interval)
        
        health_thread = threading.Thread(target=health_check_worker, daemon=True)
        health_thread.start()
    
    def _perform_health_check(self, instance: ServiceInstance) -> bool:
        """执行健康检查"""
        if not instance.health_check_url:
            return True
        
        for attempt in range(self.config.health_check_retries):
            try:
                response = requests.get(
                    instance.health_check_url,
                    timeout=self.config.health_check_timeout
                )
                if response.status_code == 200:
                    return True
            except Exception as e:
                logger.debug(f"健康检查失败 {instance.instance_id} (尝试 {attempt + 1}): {e}")
        
        return False
    
    def _start_connection_cleanup_thread(self):
        """启动连接清理线程"""
        def cleanup_worker():
            while True:
                try:
                    now = datetime.now()
                    expired_connections = []
                    
                    for client_id, conn_info in self._connections.items():
                        time_since_activity = (now - conn_info.last_activity).total_seconds()
                        if time_since_activity > self.config.connection_timeout:
                            expired_connections.append(client_id)
                    
                    # 清理过期连接
                    for client_id in expired_connections:
                        conn_info = self._connections.pop(client_id)
                        instance_id = conn_info.instance_id
                        if instance_id in self._instance_connections:
                            self._instance_connections[instance_id] = max(0, 
                                self._instance_connections[instance_id] - 1)
                    
                    # 清理过期会话
                    if self.config.session_affinity:
                        expired_sessions = []
                        for session_id in list(self._session_map.keys()):
                            # 简化的会话过期检查
                            expired_sessions.append(session_id)
                        
                        for session_id in expired_sessions[:len(expired_sessions)//2]:  # 清理一半
                            self._session_map.pop(session_id, None)
                    
                    time.sleep(60)  # 每分钟清理一次
                    
                except Exception as e:
                    logger.error(f"连接清理线程出错: {e}")
                    time.sleep(60)
        
        cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        cleanup_thread.start()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取负载均衡器统计信息"""
        return {
            'total_connections': len(self._connections),
            'instance_connections': dict(self._instance_connections),
            'instance_failures': dict(self._instance_failures),
            'active_sessions': len(self._session_map),
            'strategy': self.config.strategy.value,
            'health_check_enabled': self.config.health_check_enabled
        }


def create_load_balancer(service_discovery: ServiceDiscovery, 
                        config: LoadBalancerConfig = None) -> LoadBalancer:
    """创建负载均衡器实例"""
    return LoadBalancer(service_discovery, config)


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    from .service_discovery import create_service_registry, create_service_discovery
    
    # 创建服务发现
    registry = create_service_registry()
    discovery = create_service_discovery(registry)
    
    # 创建负载均衡器
    lb_config = LoadBalancerConfig(strategy=LoadBalanceStrategy.ROUND_ROBIN)
    load_balancer = create_load_balancer(discovery, lb_config)
    
    print("负载均衡器测试完成")
