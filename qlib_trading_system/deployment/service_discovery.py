# -*- coding: utf-8 -*-
"""
服务发现和注册系统

提供服务注册、发现、健康检查、负载均衡等功能
"""

import os
import json
import time
import logging
import threading
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import requests
import redis
from enum import Enum

logger = logging.getLogger(__name__)


class ServiceStatus(Enum):
    """服务状态枚举"""
    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"
    STARTING = "starting"
    STOPPING = "stopping"


@dataclass
class ServiceInstance:
    """服务实例"""
    service_name: str
    instance_id: str
    host: str
    port: int
    status: ServiceStatus = ServiceStatus.UNKNOWN
    metadata: Dict[str, Any] = field(default_factory=dict)
    last_heartbeat: Optional[datetime] = None
    health_check_url: Optional[str] = None
    weight: int = 1  # 负载均衡权重
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'service_name': self.service_name,
            'instance_id': self.instance_id,
            'host': self.host,
            'port': self.port,
            'status': self.status.value,
            'metadata': self.metadata,
            'last_heartbeat': self.last_heartbeat.isoformat() if self.last_heartbeat else None,
            'health_check_url': self.health_check_url,
            'weight': self.weight
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ServiceInstance':
        """从字典创建实例"""
        instance = cls(
            service_name=data['service_name'],
            instance_id=data['instance_id'],
            host=data['host'],
            port=data['port'],
            status=ServiceStatus(data.get('status', 'unknown')),
            metadata=data.get('metadata', {}),
            health_check_url=data.get('health_check_url'),
            weight=data.get('weight', 1)
        )
        
        if data.get('last_heartbeat'):
            instance.last_heartbeat = datetime.fromisoformat(data['last_heartbeat'])
        
        return instance


class ServiceRegistry:
    """服务注册中心"""
    
    def __init__(self, redis_host: str = "localhost", redis_port: int = 6379, 
                 redis_db: int = 0, redis_password: str = None):
        """初始化服务注册中心"""
        self.redis_client = redis.Redis(
            host=redis_host,
            port=redis_port,
            db=redis_db,
            password=redis_password,
            decode_responses=True
        )
        
        # 服务注册表键前缀
        self.service_prefix = "qlib:services"
        self.instance_prefix = "qlib:instances"
        
        # 心跳超时时间（秒）
        self.heartbeat_timeout = 30
        
        # 启动清理线程
        self._start_cleanup_thread()
        
        logger.info("服务注册中心初始化完成")
    
    def register_service(self, instance: ServiceInstance) -> bool:
        """注册服务实例"""
        try:
            # 设置心跳时间
            instance.last_heartbeat = datetime.now()
            instance.status = ServiceStatus.HEALTHY
            
            # 存储服务实例信息
            instance_key = f"{self.instance_prefix}:{instance.service_name}:{instance.instance_id}"
            self.redis_client.setex(
                instance_key,
                self.heartbeat_timeout * 2,  # TTL设置为心跳超时的2倍
                json.dumps(instance.to_dict())
            )
            
            # 添加到服务列表
            service_key = f"{self.service_prefix}:{instance.service_name}"
            self.redis_client.sadd(service_key, instance.instance_id)
            
            logger.info(f"服务实例注册成功: {instance.service_name}:{instance.instance_id}")
            return True
            
        except Exception as e:
            logger.error(f"服务实例注册失败: {e}")
            return False
    
    def unregister_service(self, service_name: str, instance_id: str) -> bool:
        """注销服务实例"""
        try:
            # 删除实例信息
            instance_key = f"{self.instance_prefix}:{service_name}:{instance_id}"
            self.redis_client.delete(instance_key)
            
            # 从服务列表中移除
            service_key = f"{self.service_prefix}:{service_name}"
            self.redis_client.srem(service_key, instance_id)
            
            logger.info(f"服务实例注销成功: {service_name}:{instance_id}")
            return True
            
        except Exception as e:
            logger.error(f"服务实例注销失败: {e}")
            return False
    
    def heartbeat(self, service_name: str, instance_id: str) -> bool:
        """发送心跳"""
        try:
            instance_key = f"{self.instance_prefix}:{service_name}:{instance_id}"
            instance_data = self.redis_client.get(instance_key)
            
            if instance_data:
                instance = ServiceInstance.from_dict(json.loads(instance_data))
                instance.last_heartbeat = datetime.now()
                instance.status = ServiceStatus.HEALTHY
                
                # 更新实例信息
                self.redis_client.setex(
                    instance_key,
                    self.heartbeat_timeout * 2,
                    json.dumps(instance.to_dict())
                )
                
                return True
            else:
                logger.warning(f"心跳失败，服务实例不存在: {service_name}:{instance_id}")
                return False
                
        except Exception as e:
            logger.error(f"心跳发送失败: {e}")
            return False
    
    def discover_services(self, service_name: str) -> List[ServiceInstance]:
        """发现服务实例"""
        try:
            service_key = f"{self.service_prefix}:{service_name}"
            instance_ids = self.redis_client.smembers(service_key)
            
            instances = []
            for instance_id in instance_ids:
                instance_key = f"{self.instance_prefix}:{service_name}:{instance_id}"
                instance_data = self.redis_client.get(instance_key)
                
                if instance_data:
                    instance = ServiceInstance.from_dict(json.loads(instance_data))
                    instances.append(instance)
            
            # 过滤健康的实例
            healthy_instances = [
                instance for instance in instances 
                if instance.status == ServiceStatus.HEALTHY
            ]
            
            return healthy_instances
            
        except Exception as e:
            logger.error(f"服务发现失败: {e}")
            return []
    
    def get_all_services(self) -> Dict[str, List[ServiceInstance]]:
        """获取所有服务"""
        try:
            services = {}
            
            # 获取所有服务键
            service_keys = self.redis_client.keys(f"{self.service_prefix}:*")
            
            for service_key in service_keys:
                service_name = service_key.split(':')[-1]
                instances = self.discover_services(service_name)
                if instances:
                    services[service_name] = instances
            
            return services
            
        except Exception as e:
            logger.error(f"获取所有服务失败: {e}")
            return {}
    
    def _start_cleanup_thread(self):
        """启动清理线程"""
        def cleanup_expired_instances():
            while True:
                try:
                    # 获取所有实例键
                    instance_keys = self.redis_client.keys(f"{self.instance_prefix}:*")
                    
                    for instance_key in instance_keys:
                        instance_data = self.redis_client.get(instance_key)
                        if instance_data:
                            instance = ServiceInstance.from_dict(json.loads(instance_data))
                            
                            # 检查心跳超时
                            if instance.last_heartbeat:
                                time_since_heartbeat = datetime.now() - instance.last_heartbeat
                                if time_since_heartbeat.total_seconds() > self.heartbeat_timeout:
                                    # 标记为不健康
                                    instance.status = ServiceStatus.UNHEALTHY
                                    self.redis_client.setex(
                                        instance_key,
                                        self.heartbeat_timeout,
                                        json.dumps(instance.to_dict())
                                    )
                                    
                                    logger.warning(f"服务实例心跳超时: {instance.service_name}:{instance.instance_id}")
                    
                    time.sleep(10)  # 每10秒检查一次
                    
                except Exception as e:
                    logger.error(f"清理过期实例失败: {e}")
                    time.sleep(10)
        
        cleanup_thread = threading.Thread(target=cleanup_expired_instances, daemon=True)
        cleanup_thread.start()


class ServiceDiscovery:
    """服务发现客户端"""
    
    def __init__(self, registry: ServiceRegistry):
        """初始化服务发现客户端"""
        self.registry = registry
        self.service_cache = {}
        self.cache_ttl = 30  # 缓存TTL（秒）
        self.last_cache_update = {}
        
        logger.info("服务发现客户端初始化完成")
    
    def get_service_instance(self, service_name: str, 
                           load_balance_strategy: str = "round_robin") -> Optional[ServiceInstance]:
        """获取服务实例（带负载均衡）"""
        instances = self._get_cached_instances(service_name)
        
        if not instances:
            return None
        
        # 应用负载均衡策略
        if load_balance_strategy == "round_robin":
            return self._round_robin_select(service_name, instances)
        elif load_balance_strategy == "weighted":
            return self._weighted_select(instances)
        elif load_balance_strategy == "random":
            import random
            return random.choice(instances)
        else:
            return instances[0]  # 默认返回第一个
    
    def _get_cached_instances(self, service_name: str) -> List[ServiceInstance]:
        """获取缓存的服务实例"""
        now = datetime.now()
        
        # 检查缓存是否过期
        if (service_name not in self.last_cache_update or 
            (now - self.last_cache_update[service_name]).total_seconds() > self.cache_ttl):
            
            # 更新缓存
            instances = self.registry.discover_services(service_name)
            self.service_cache[service_name] = instances
            self.last_cache_update[service_name] = now
        
        return self.service_cache.get(service_name, [])
    
    def _round_robin_select(self, service_name: str, instances: List[ServiceInstance]) -> ServiceInstance:
        """轮询选择"""
        if not hasattr(self, '_round_robin_counters'):
            self._round_robin_counters = {}
        
        if service_name not in self._round_robin_counters:
            self._round_robin_counters[service_name] = 0
        
        index = self._round_robin_counters[service_name] % len(instances)
        self._round_robin_counters[service_name] += 1
        
        return instances[index]
    
    def _weighted_select(self, instances: List[ServiceInstance]) -> ServiceInstance:
        """加权选择"""
        import random
        
        total_weight = sum(instance.weight for instance in instances)
        if total_weight == 0:
            return random.choice(instances)
        
        random_weight = random.randint(1, total_weight)
        current_weight = 0
        
        for instance in instances:
            current_weight += instance.weight
            if current_weight >= random_weight:
                return instance
        
        return instances[-1]  # 备用返回
    
    def health_check(self, instance: ServiceInstance) -> bool:
        """健康检查"""
        if not instance.health_check_url:
            return True  # 如果没有健康检查URL，默认健康
        
        try:
            response = requests.get(
                instance.health_check_url,
                timeout=5
            )
            return response.status_code == 200
            
        except Exception as e:
            logger.warning(f"健康检查失败 {instance.service_name}:{instance.instance_id}: {e}")
            return False


def create_service_registry(redis_host: str = "localhost", redis_port: int = 6379) -> ServiceRegistry:
    """创建服务注册中心实例"""
    return ServiceRegistry(redis_host, redis_port)


def create_service_discovery(registry: ServiceRegistry) -> ServiceDiscovery:
    """创建服务发现客户端实例"""
    return ServiceDiscovery(registry)


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    # 创建服务注册中心
    registry = create_service_registry()
    
    # 创建服务发现客户端
    discovery = create_service_discovery(registry)
    
    # 注册测试服务
    test_instance = ServiceInstance(
        service_name="api",
        instance_id="api-001",
        host="localhost",
        port=8000,
        health_check_url="http://localhost:8000/health"
    )
    
    registry.register_service(test_instance)
    
    # 发现服务
    instances = discovery.registry.discover_services("api")
    print(f"发现服务实例: {len(instances)}")
    
    print("服务发现系统测试完成")
