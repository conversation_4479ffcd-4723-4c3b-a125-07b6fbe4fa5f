# -*- coding: utf-8 -*-
"""
Docker容器管理器

提供Docker镜像构建、容器管理、多服务编排等功能
"""

import os
import json
import logging
import subprocess
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from pathlib import Path
import docker
from docker.errors import DockerException, BuildError, APIError

logger = logging.getLogger(__name__)


@dataclass
class DockerConfig:
    """Docker配置"""
    # 镜像配置
    registry_url: str = "localhost:5000"
    image_prefix: str = "qlib-trading"
    base_image: str = "python:3.11-slim"
    
    # 构建配置
    build_context: str = "."
    dockerfile_path: str = "Dockerfile"
    build_args: Dict[str, str] = field(default_factory=dict)
    
    # 容器配置
    container_memory: str = "2g"
    container_cpu: str = "1.0"
    network_mode: str = "bridge"
    
    # 服务配置
    services: Dict[str, Dict[str, Any]] = field(default_factory=lambda: {
        'api': {
            'port': 8000,
            'health_check': '/health',
            'replicas': 2
        },
        'data-processor': {
            'port': 8001,
            'health_check': '/health',
            'replicas': 1
        },
        'model-server': {
            'port': 8002,
            'health_check': '/health',
            'replicas': 1
        },
        'risk-monitor': {
            'port': 8003,
            'health_check': '/health',
            'replicas': 1
        }
    })


class DockerManager:
    """Docker容器管理器"""
    
    def __init__(self, config: DockerConfig = None):
        """初始化Docker管理器"""
        self.config = config or DockerConfig()
        
        try:
            self.client = docker.from_env()
            logger.info("Docker客户端连接成功")
        except DockerException as e:
            logger.error(f"Docker客户端连接失败: {e}")
            raise
        
        # 创建部署目录
        self.deployment_dir = Path("deployment")
        self.deployment_dir.mkdir(exist_ok=True)
        
        # 初始化服务状态
        self.service_status = {}
    
    def build_image(self, service_name: str, context_path: str = None, 
                   dockerfile: str = None, tag: str = "latest") -> bool:
        """构建Docker镜像"""
        try:
            context_path = context_path or self.config.build_context
            dockerfile = dockerfile or self.config.dockerfile_path
            
            # 构建镜像标签
            image_tag = f"{self.config.registry_url}/{self.config.image_prefix}-{service_name}:{tag}"
            
            logger.info(f"开始构建镜像: {image_tag}")
            
            # 构建镜像
            image, build_logs = self.client.images.build(
                path=context_path,
                dockerfile=dockerfile,
                tag=image_tag,
                buildargs=self.config.build_args,
                rm=True,
                forcerm=True
            )
            
            # 记录构建日志
            for log in build_logs:
                if 'stream' in log:
                    logger.debug(log['stream'].strip())
            
            logger.info(f"镜像构建成功: {image_tag}")
            return True
            
        except BuildError as e:
            logger.error(f"镜像构建失败: {e}")
            return False
        except Exception as e:
            logger.error(f"构建过程出错: {e}")
            return False
    
    def push_image(self, service_name: str, tag: str = "latest") -> bool:
        """推送镜像到仓库"""
        try:
            image_tag = f"{self.config.registry_url}/{self.config.image_prefix}-{service_name}:{tag}"
            
            logger.info(f"开始推送镜像: {image_tag}")
            
            # 推送镜像
            push_logs = self.client.images.push(
                repository=f"{self.config.registry_url}/{self.config.image_prefix}-{service_name}",
                tag=tag,
                stream=True,
                decode=True
            )
            
            # 记录推送日志
            for log in push_logs:
                if 'status' in log:
                    logger.debug(f"推送状态: {log['status']}")
            
            logger.info(f"镜像推送成功: {image_tag}")
            return True
            
        except APIError as e:
            logger.error(f"镜像推送失败: {e}")
            return False
        except Exception as e:
            logger.error(f"推送过程出错: {e}")
            return False
    
    def create_dockerfile(self, service_name: str, service_config: Dict[str, Any]) -> str:
        """创建服务的Dockerfile"""
        dockerfile_content = f"""# {service_name} 服务 Dockerfile
FROM {self.config.base_image}

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONPATH=/app
ENV SERVICE_NAME={service_name}
ENV SERVICE_PORT={service_config.get('port', 8000)}

# 安装系统依赖
RUN apt-get update && apt-get install -y \\
    gcc \\
    g++ \\
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY qlib_trading_system/ ./qlib_trading_system/
COPY config/ ./config/
COPY main.py .

# 创建非root用户
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

# 暴露端口
EXPOSE {service_config.get('port', 8000)}

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \\
    CMD python -c "import requests; requests.get('http://localhost:{service_config.get('port', 8000)}{service_config.get('health_check', '/health')}')"

# 启动命令
CMD ["python", "main.py", "--service", "{service_name}", "--port", "{service_config.get('port', 8000)}"]
"""
        
        # 保存Dockerfile
        dockerfile_path = self.deployment_dir / f"Dockerfile.{service_name}"
        with open(dockerfile_path, 'w', encoding='utf-8') as f:
            f.write(dockerfile_content)
        
        logger.info(f"Dockerfile已创建: {dockerfile_path}")
        return str(dockerfile_path)
    
    def create_docker_compose(self) -> str:
        """创建docker-compose.yml文件"""
        compose_config = {
            'version': '3.8',
            'services': {},
            'networks': {
                'qlib-network': {
                    'driver': 'bridge'
                }
            },
            'volumes': {
                'postgres_data': {},
                'redis_data': {},
                'models_data': {},
                'logs_data': {}
            }
        }

        # 添加应用服务
        for service_name, service_config in self.config.services.items():
            compose_config['services'][service_name] = {
                'build': {
                    'context': '.',
                    'dockerfile': f'deployment/Dockerfile.{service_name}'
                },
                'ports': [f"{service_config['port']}:{service_config['port']}"],
                'environment': [
                    f'SERVICE_NAME={service_name}',
                    f'SERVICE_PORT={service_config["port"]}',
                    'ENVIRONMENT=production',
                    'LOG_LEVEL=INFO'
                ],
                'volumes': [
                    'models_data:/app/models',
                    'logs_data:/app/logs'
                ],
                'networks': ['qlib-network'],
                'restart': 'unless-stopped',
                'deploy': {
                    'replicas': service_config.get('replicas', 1),
                    'resources': {
                        'limits': {
                            'memory': self.config.container_memory,
                            'cpus': self.config.container_cpu
                        }
                    }
                },
                'depends_on': ['postgres', 'redis']
            }

        # 添加数据库服务
        compose_config['services']['postgres'] = {
            'image': 'postgres:13',
            'environment': [
                'POSTGRES_DB=qlib_trading',
                'POSTGRES_USER=qlib_user',
                'POSTGRES_PASSWORD=qlib_password'
            ],
            'volumes': ['postgres_data:/var/lib/postgresql/data'],
            'networks': ['qlib-network'],
            'restart': 'unless-stopped'
        }

        compose_config['services']['redis'] = {
            'image': 'redis:6-alpine',
            'volumes': ['redis_data:/data'],
            'networks': ['qlib-network'],
            'restart': 'unless-stopped'
        }

        # 添加负载均衡器
        compose_config['services']['nginx'] = {
            'image': 'nginx:alpine',
            'ports': ['80:80', '443:443'],
            'volumes': [
                './deployment/nginx.conf:/etc/nginx/nginx.conf',
                './deployment/ssl:/etc/nginx/ssl'
            ],
            'networks': ['qlib-network'],
            'depends_on': list(self.config.services.keys()),
            'restart': 'unless-stopped'
        }

        # 保存docker-compose.yml
        compose_path = self.deployment_dir / "docker-compose.yml"
        with open(compose_path, 'w', encoding='utf-8') as f:
            try:
                import yaml
                yaml.dump(compose_config, f, default_flow_style=False, allow_unicode=True)
            except ImportError:
                # 如果没有yaml库，使用json格式
                json.dump(compose_config, f, indent=2, ensure_ascii=False)

        logger.info(f"docker-compose.yml已创建: {compose_path}")
        return str(compose_path)
    
    def build_all_services(self, tag: str = "latest") -> Dict[str, bool]:
        """构建所有服务的镜像"""
        results = {}
        
        # 为每个服务创建Dockerfile
        for service_name, service_config in self.config.services.items():
            dockerfile_path = self.create_dockerfile(service_name, service_config)
            
            # 构建镜像
            success = self.build_image(
                service_name=service_name,
                dockerfile=dockerfile_path,
                tag=tag
            )
            results[service_name] = success
        
        return results
    
    def get_service_status(self, service_name: str) -> Dict[str, Any]:
        """获取服务状态"""
        try:
            containers = self.client.containers.list(
                filters={'label': f'com.docker.compose.service={service_name}'}
            )
            
            status = {
                'service_name': service_name,
                'container_count': len(containers),
                'containers': []
            }
            
            for container in containers:
                container_info = {
                    'id': container.id[:12],
                    'name': container.name,
                    'status': container.status,
                    'created': container.attrs['Created'],
                    'ports': container.ports
                }
                status['containers'].append(container_info)
            
            return status
            
        except Exception as e:
            logger.error(f"获取服务状态失败: {e}")
            return {'service_name': service_name, 'error': str(e)}
    
    def cleanup_unused_resources(self) -> Dict[str, Any]:
        """清理未使用的Docker资源"""
        try:
            # 清理未使用的镜像
            images_pruned = self.client.images.prune()
            
            # 清理未使用的容器
            containers_pruned = self.client.containers.prune()
            
            # 清理未使用的网络
            networks_pruned = self.client.networks.prune()
            
            # 清理未使用的卷
            volumes_pruned = self.client.volumes.prune()
            
            cleanup_result = {
                'images_deleted': images_pruned.get('ImagesDeleted', []),
                'space_reclaimed': images_pruned.get('SpaceReclaimed', 0),
                'containers_deleted': containers_pruned.get('ContainersDeleted', []),
                'networks_deleted': networks_pruned.get('NetworksDeleted', []),
                'volumes_deleted': volumes_pruned.get('VolumesDeleted', [])
            }
            
            logger.info(f"Docker资源清理完成: {cleanup_result}")
            return cleanup_result
            
        except Exception as e:
            logger.error(f"Docker资源清理失败: {e}")
            return {'error': str(e)}


def create_docker_manager(config: DockerConfig = None) -> DockerManager:
    """创建Docker管理器实例"""
    return DockerManager(config)


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    # 创建Docker管理器
    docker_manager = create_docker_manager()
    
    # 创建所有服务的Dockerfile
    for service_name, service_config in docker_manager.config.services.items():
        docker_manager.create_dockerfile(service_name, service_config)
    
    # 创建docker-compose.yml
    docker_manager.create_docker_compose()
    
    print("Docker配置文件创建完成")
