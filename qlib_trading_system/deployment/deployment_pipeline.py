# -*- coding: utf-8 -*-
"""
自动化部署流水线

提供CI/CD流水线、自动化构建、部署、回滚等功能
"""

import os
import json
import logging
import subprocess
import time
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from pathlib import Path
import yaml

from .docker_manager import DockerManager, DockerConfig
from .kubernetes_manager import KubernetesManager, K8sConfig

logger = logging.getLogger(__name__)


class DeploymentStage(Enum):
    """部署阶段"""
    BUILD = "build"
    TEST = "test"
    DEPLOY = "deploy"
    VERIFY = "verify"
    ROLLBACK = "rollback"
    CLEANUP = "cleanup"


class DeploymentStatus(Enum):
    """部署状态"""
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    CANCELLED = "cancelled"
    ROLLED_BACK = "rolled_back"


@dataclass
class PipelineConfig:
    """流水线配置"""
    # 基础配置
    pipeline_name: str = "qlib-trading-deployment"
    environment: str = "production"
    version: str = "latest"
    
    # 构建配置
    build_enabled: bool = True
    build_timeout: int = 600  # 秒
    parallel_builds: bool = True
    
    # 测试配置
    test_enabled: bool = True
    test_timeout: int = 300   # 秒
    test_coverage_threshold: float = 0.8
    
    # 部署配置
    deployment_strategy: str = "rolling"  # rolling, blue_green, canary
    deployment_timeout: int = 900         # 秒
    health_check_timeout: int = 300       # 秒
    
    # 回滚配置
    auto_rollback_enabled: bool = True
    rollback_timeout: int = 300           # 秒
    
    # 通知配置
    notifications_enabled: bool = True
    notification_channels: List[str] = field(default_factory=lambda: ["email", "slack"])
    
    # 环境配置
    environments: Dict[str, Dict[str, Any]] = field(default_factory=lambda: {
        "development": {
            "replicas": 1,
            "resources": {"cpu": "100m", "memory": "256Mi"}
        },
        "staging": {
            "replicas": 2,
            "resources": {"cpu": "200m", "memory": "512Mi"}
        },
        "production": {
            "replicas": 3,
            "resources": {"cpu": "500m", "memory": "1Gi"}
        }
    })


@dataclass
class DeploymentRecord:
    """部署记录"""
    deployment_id: str
    pipeline_name: str
    environment: str
    version: str
    status: DeploymentStatus
    start_time: datetime
    end_time: Optional[datetime] = None
    stages: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    logs: List[str] = field(default_factory=list)
    rollback_version: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'deployment_id': self.deployment_id,
            'pipeline_name': self.pipeline_name,
            'environment': self.environment,
            'version': self.version,
            'status': self.status.value,
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'stages': self.stages,
            'logs': self.logs,
            'rollback_version': self.rollback_version
        }


class DeploymentPipeline:
    """自动化部署流水线"""
    
    def __init__(self, config: PipelineConfig = None, 
                 docker_manager: DockerManager = None,
                 k8s_manager: KubernetesManager = None):
        """初始化部署流水线"""
        self.config = config or PipelineConfig()
        self.docker_manager = docker_manager or DockerManager()
        self.k8s_manager = k8s_manager or KubernetesManager()
        
        # 创建部署目录
        self.deployment_dir = Path("deployment/pipeline")
        self.deployment_dir.mkdir(parents=True, exist_ok=True)
        
        # 部署记录
        self.deployment_records = {}
        
        # 回调函数
        self.stage_callbacks = {}
        
        logger.info("部署流水线初始化完成")
    
    def deploy(self, environment: str = None, version: str = None, 
               services: List[str] = None) -> str:
        """执行部署"""
        environment = environment or self.config.environment
        version = version or self.config.version
        services = services or list(self.docker_manager.config.services.keys())
        
        # 生成部署ID
        deployment_id = f"deploy-{int(time.time())}"
        
        # 创建部署记录
        record = DeploymentRecord(
            deployment_id=deployment_id,
            pipeline_name=self.config.pipeline_name,
            environment=environment,
            version=version,
            status=DeploymentStatus.PENDING,
            start_time=datetime.now()
        )
        
        self.deployment_records[deployment_id] = record
        
        try:
            logger.info(f"开始部署 {deployment_id}: {environment} - {version}")
            record.status = DeploymentStatus.RUNNING
            
            # 执行部署阶段
            stages = [
                (DeploymentStage.BUILD, self._build_stage),
                (DeploymentStage.TEST, self._test_stage),
                (DeploymentStage.DEPLOY, self._deploy_stage),
                (DeploymentStage.VERIFY, self._verify_stage)
            ]
            
            for stage, stage_func in stages:
                if not self._execute_stage(record, stage, stage_func, 
                                         environment, version, services):
                    # 阶段失败，执行回滚
                    if self.config.auto_rollback_enabled:
                        self._rollback_stage(record, environment, services)
                    record.status = DeploymentStatus.FAILED
                    record.end_time = datetime.now()
                    return deployment_id
            
            # 部署成功
            record.status = DeploymentStatus.SUCCESS
            record.end_time = datetime.now()
            
            logger.info(f"部署成功 {deployment_id}")
            
            # 发送通知
            if self.config.notifications_enabled:
                self._send_notification(record, "部署成功")
            
            return deployment_id
            
        except Exception as e:
            logger.error(f"部署失败 {deployment_id}: {e}")
            record.status = DeploymentStatus.FAILED
            record.end_time = datetime.now()
            record.logs.append(f"部署异常: {str(e)}")
            
            # 自动回滚
            if self.config.auto_rollback_enabled:
                self._rollback_stage(record, environment, services)
            
            return deployment_id
    
    def _execute_stage(self, record: DeploymentRecord, stage: DeploymentStage,
                      stage_func: Callable, environment: str, version: str,
                      services: List[str]) -> bool:
        """执行部署阶段"""
        stage_name = stage.value
        logger.info(f"执行阶段: {stage_name}")
        
        stage_start = datetime.now()
        record.stages[stage_name] = {
            'status': 'running',
            'start_time': stage_start.isoformat(),
            'logs': []
        }
        
        try:
            # 执行阶段函数
            success = stage_func(record, environment, version, services)
            
            stage_end = datetime.now()
            record.stages[stage_name].update({
                'status': 'success' if success else 'failed',
                'end_time': stage_end.isoformat(),
                'duration': (stage_end - stage_start).total_seconds()
            })
            
            # 执行回调
            if stage in self.stage_callbacks:
                self.stage_callbacks[stage](record, success)
            
            return success
            
        except Exception as e:
            stage_end = datetime.now()
            error_msg = f"阶段 {stage_name} 执行失败: {str(e)}"
            logger.error(error_msg)
            
            record.stages[stage_name].update({
                'status': 'failed',
                'end_time': stage_end.isoformat(),
                'duration': (stage_end - stage_start).total_seconds(),
                'error': error_msg
            })
            record.logs.append(error_msg)
            
            return False
    
    def _build_stage(self, record: DeploymentRecord, environment: str,
                    version: str, services: List[str]) -> bool:
        """构建阶段"""
        if not self.config.build_enabled:
            return True
        
        logger.info("开始构建阶段")
        
        try:
            # 并行构建或串行构建
            if self.config.parallel_builds:
                results = {}
                import concurrent.futures
                
                with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
                    future_to_service = {
                        executor.submit(self.docker_manager.build_image, service, tag=version): service
                        for service in services
                    }
                    
                    for future in concurrent.futures.as_completed(future_to_service):
                        service = future_to_service[future]
                        try:
                            results[service] = future.result(timeout=self.config.build_timeout)
                        except Exception as e:
                            logger.error(f"服务 {service} 构建失败: {e}")
                            results[service] = False
            else:
                results = {}
                for service in services:
                    results[service] = self.docker_manager.build_image(service, tag=version)
            
            # 检查构建结果
            failed_services = [service for service, success in results.items() if not success]
            if failed_services:
                record.logs.append(f"构建失败的服务: {failed_services}")
                return False
            
            # 推送镜像
            for service in services:
                if not self.docker_manager.push_image(service, tag=version):
                    record.logs.append(f"镜像推送失败: {service}")
                    return False
            
            record.logs.append(f"构建成功: {services}")
            return True
            
        except Exception as e:
            record.logs.append(f"构建阶段失败: {str(e)}")
            return False
    
    def _test_stage(self, record: DeploymentRecord, environment: str,
                   version: str, services: List[str]) -> bool:
        """测试阶段"""
        if not self.config.test_enabled:
            return True
        
        logger.info("开始测试阶段")
        
        try:
            # 运行单元测试
            test_result = subprocess.run(
                ["python", "-m", "pytest", "tests/", "--cov=qlib_trading_system", 
                 f"--cov-fail-under={int(self.config.test_coverage_threshold * 100)}"],
                capture_output=True,
                text=True,
                timeout=self.config.test_timeout
            )
            
            if test_result.returncode == 0:
                record.logs.append("单元测试通过")
                
                # 运行集成测试
                integration_result = subprocess.run(
                    ["python", "-m", "pytest", "tests/integration/"],
                    capture_output=True,
                    text=True,
                    timeout=self.config.test_timeout
                )
                
                if integration_result.returncode == 0:
                    record.logs.append("集成测试通过")
                    return True
                else:
                    record.logs.append(f"集成测试失败: {integration_result.stderr}")
                    return False
            else:
                record.logs.append(f"单元测试失败: {test_result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            record.logs.append("测试超时")
            return False
        except Exception as e:
            record.logs.append(f"测试阶段失败: {str(e)}")
            return False
    
    def _deploy_stage(self, record: DeploymentRecord, environment: str,
                     version: str, services: List[str]) -> bool:
        """部署阶段"""
        logger.info("开始部署阶段")
        
        try:
            # 根据部署策略执行部署
            strategy = self.config.deployment_strategy
            
            if strategy == "rolling":
                return self._rolling_deployment(record, environment, version, services)
            elif strategy == "blue_green":
                return self._blue_green_deployment(record, environment, version, services)
            elif strategy == "canary":
                return self._canary_deployment(record, environment, version, services)
            else:
                # 默认滚动部署
                return self._rolling_deployment(record, environment, version, services)
                
        except Exception as e:
            record.logs.append(f"部署阶段失败: {str(e)}")
            return False
    
    def _rolling_deployment(self, record: DeploymentRecord, environment: str,
                           version: str, services: List[str]) -> bool:
        """滚动部署"""
        logger.info("执行滚动部署")
        
        try:
            # 更新Kubernetes部署
            results = self.k8s_manager.deploy_application(tag=version)
            
            failed_deployments = [name for name, success in results.items() if not success]
            if failed_deployments:
                record.logs.append(f"滚动部署失败: {failed_deployments}")
                return False
            
            record.logs.append("滚动部署成功")
            return True
            
        except Exception as e:
            record.logs.append(f"滚动部署失败: {str(e)}")
            return False
    
    def _blue_green_deployment(self, record: DeploymentRecord, environment: str,
                              version: str, services: List[str]) -> bool:
        """蓝绿部署"""
        logger.info("执行蓝绿部署")
        
        # 蓝绿部署的简化实现
        try:
            # 部署到绿色环境
            green_results = self.k8s_manager.deploy_application(tag=version)
            
            if all(green_results.values()):
                # 切换流量到绿色环境
                record.logs.append("蓝绿部署成功，流量已切换")
                return True
            else:
                record.logs.append("蓝绿部署失败")
                return False
                
        except Exception as e:
            record.logs.append(f"蓝绿部署失败: {str(e)}")
            return False
    
    def _canary_deployment(self, record: DeploymentRecord, environment: str,
                          version: str, services: List[str]) -> bool:
        """金丝雀部署"""
        logger.info("执行金丝雀部署")
        
        # 金丝雀部署的简化实现
        try:
            # 部署少量实例
            canary_results = self.k8s_manager.deploy_application(tag=version)
            
            if all(canary_results.values()):
                # 监控金丝雀实例
                time.sleep(60)  # 等待1分钟观察
                
                # 如果金丝雀实例正常，继续部署
                record.logs.append("金丝雀部署成功")
                return True
            else:
                record.logs.append("金丝雀部署失败")
                return False
                
        except Exception as e:
            record.logs.append(f"金丝雀部署失败: {str(e)}")
            return False
    
    def _verify_stage(self, record: DeploymentRecord, environment: str,
                     version: str, services: List[str]) -> bool:
        """验证阶段"""
        logger.info("开始验证阶段")
        
        try:
            # 健康检查
            for service in services:
                service_config = self.docker_manager.config.services.get(service, {})
                health_check_url = f"http://localhost:{service_config.get('port', 8000)}/health"
                
                # 等待服务启动
                max_retries = 30
                for i in range(max_retries):
                    try:
                        import requests
                        response = requests.get(health_check_url, timeout=5)
                        if response.status_code == 200:
                            break
                    except:
                        pass
                    
                    time.sleep(10)
                else:
                    record.logs.append(f"服务 {service} 健康检查失败")
                    return False
            
            record.logs.append("所有服务验证通过")
            return True
            
        except Exception as e:
            record.logs.append(f"验证阶段失败: {str(e)}")
            return False
    
    def _rollback_stage(self, record: DeploymentRecord, environment: str, services: List[str]):
        """回滚阶段"""
        logger.info("开始回滚阶段")
        
        try:
            # 获取上一个成功的版本
            previous_version = self._get_previous_version(environment)
            if not previous_version:
                record.logs.append("没有找到可回滚的版本")
                return
            
            record.rollback_version = previous_version
            
            # 执行回滚
            rollback_results = self.k8s_manager.deploy_application(tag=previous_version)
            
            if all(rollback_results.values()):
                record.logs.append(f"回滚成功到版本: {previous_version}")
                record.status = DeploymentStatus.ROLLED_BACK
            else:
                record.logs.append("回滚失败")
                
        except Exception as e:
            record.logs.append(f"回滚失败: {str(e)}")
    
    def _get_previous_version(self, environment: str) -> Optional[str]:
        """获取上一个成功的版本"""
        # 查找最近一次成功的部署
        successful_deployments = [
            record for record in self.deployment_records.values()
            if (record.environment == environment and 
                record.status == DeploymentStatus.SUCCESS)
        ]
        
        if successful_deployments:
            # 按时间排序，返回最新的成功版本
            latest_success = max(successful_deployments, key=lambda x: x.start_time)
            return latest_success.version
        
        return None
    
    def _send_notification(self, record: DeploymentRecord, message: str):
        """发送通知"""
        try:
            notification_data = {
                'deployment_id': record.deployment_id,
                'environment': record.environment,
                'version': record.version,
                'status': record.status.value,
                'message': message,
                'timestamp': datetime.now().isoformat()
            }
            
            logger.info(f"发送通知: {message}")
            # 这里可以集成实际的通知服务（邮件、Slack等）
            
        except Exception as e:
            logger.error(f"发送通知失败: {e}")
    
    def get_deployment_status(self, deployment_id: str) -> Optional[DeploymentRecord]:
        """获取部署状态"""
        return self.deployment_records.get(deployment_id)
    
    def list_deployments(self, environment: str = None) -> List[DeploymentRecord]:
        """列出部署记录"""
        deployments = list(self.deployment_records.values())
        
        if environment:
            deployments = [d for d in deployments if d.environment == environment]
        
        # 按时间倒序排列
        deployments.sort(key=lambda x: x.start_time, reverse=True)
        
        return deployments


def create_deployment_pipeline(config: PipelineConfig = None) -> DeploymentPipeline:
    """创建部署流水线实例"""
    return DeploymentPipeline(config)


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    # 创建部署流水线
    pipeline_config = PipelineConfig(environment="staging")
    pipeline = create_deployment_pipeline(pipeline_config)
    
    # 执行部署
    deployment_id = pipeline.deploy("staging", "v1.0.0")
    
    # 获取部署状态
    status = pipeline.get_deployment_status(deployment_id)
    print(f"部署状态: {status.status.value if status else 'Not Found'}")
    
    print("部署流水线测试完成")
