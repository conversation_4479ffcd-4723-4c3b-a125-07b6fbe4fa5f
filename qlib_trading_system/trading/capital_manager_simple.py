#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资金管理系统 - Capital Management System (简化版)

实现小资金专用配置管理、全仓单股策略风险控制、资金使用效率优化算法和资金流水成本分析系统

Author: Qlib Trading System
Date: 2025-01-30
"""

import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from enum import Enum
import pandas as pd
import numpy as np

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CapitalMode(Enum):
    """资金模式枚举"""
    SMALL = "small"      # 小资金模式 (<50万)
    MEDIUM = "medium"    # 中等资金模式 (50-500万)
    LARGE = "large"      # 大资金模式 (>500万)


class PositionType(Enum):
    """持仓类型枚举"""
    BASE = "base"        # 底仓
    T_POSITION = "t"     # T仓
    CASH = "cash"        # 现金


class TransactionType(Enum):
    """交易类型枚举"""
    BUY_BASE = "buy_base"           # 买入底仓
    SELL_BASE = "sell_base"         # 卖出底仓
    BUY_T = "buy_t"                 # 买入T仓
    SELL_T = "sell_t"               # 卖出T仓
    DIVIDEND = "dividend"           # 分红
    TRANSFER_IN = "transfer_in"     # 转入资金
    TRANSFER_OUT = "transfer_out"   # 转出资金


@dataclass
class CapitalConfig:
    """资金配置类"""
    total_capital: float                    # 总资金
    mode: CapitalMode                      # 资金模式
    max_stocks: int                        # 最大持股数量
    base_position_ratio: float             # 底仓比例
    t_position_ratio: float                # 做T仓位比例
    cash_reserve_ratio: float              # 现金储备比例
    max_single_stock_ratio: float          # 单股最大仓位比例
    max_daily_loss_ratio: float            # 单日最大亏损比例
    max_drawdown_ratio: float              # 最大回撤比例
    leverage_ratio: float                  # 杠杆比例

@dataclass
class Transaction:
    """交易记录类"""
    transaction_id: str                    # 交易ID
    timestamp: datetime                    # 交易时间
    symbol: str                           # 股票代码
    transaction_type: TransactionType     # 交易类型
    quantity: int                         # 数量
    price: float                          # 价格
    amount: float                         # 金额
    commission: float                     # 手续费
    tax: float                           # 税费
    net_amount: float                    # 净金额
    balance_after: float                 # 交易后余额
    notes: str = ""                      # 备注


@dataclass
class Position:
    """持仓信息类"""
    symbol: str                          # 股票代码
    position_type: PositionType          # 持仓类型
    quantity: int                        # 持仓数量
    avg_cost: float                      # 平均成本
    current_price: float                 # 当前价格
    market_value: float                  # 市值
    unrealized_pnl: float               # 浮动盈亏
    unrealized_pnl_pct: float           # 浮动盈亏比例
    last_update: datetime               # 最后更新时间
    
    def update_price(self, new_price: float):
        """更新价格和相关计算"""
        self.current_price = new_price
        self.market_value = self.quantity * new_price
        self.unrealized_pnl = self.market_value - (self.quantity * self.avg_cost)
        self.unrealized_pnl_pct = self.unrealized_pnl / (self.quantity * self.avg_cost) if self.avg_cost > 0 else 0
        self.last_update = datetime.now()


class CapitalManager:
    """资金管理器主类"""
    
    def __init__(self, config: CapitalConfig):
        """
        初始化资金管理器
        
        Args:
            config: 资金配置
        """
        self.config = config
        self.total_capital = config.total_capital
        self.available_cash = config.total_capital  # 初始时所有资金都可用
        self.positions: Dict[str, Position] = {}
        self.transactions: List[Transaction] = []
        
        # 风险控制参数
        self.daily_loss = 0.0
        self.max_drawdown = 0.0
        self.peak_value = config.total_capital
        
        logger.info(f"资金管理器初始化完成 - 模式: {config.mode.value}, 总资金: {config.total_capital:,.2f}")
    
    def get_capital_allocation(self) -> Dict[str, float]:
        """
        获取资金分配情况
        
        Returns:
            资金分配字典
        """
        base_allocation = self.total_capital * self.config.base_position_ratio
        t_allocation = self.total_capital * self.config.t_position_ratio
        cash_allocation = self.total_capital * self.config.cash_reserve_ratio
        
        return {
            "total_capital": self.total_capital,
            "base_allocation": base_allocation,
            "t_allocation": t_allocation,
            "cash_allocation": cash_allocation,
            "available_cash": self.available_cash,
            "used_base": sum(pos.market_value for pos in self.positions.values() if pos.position_type == PositionType.BASE),
            "used_t": sum(pos.market_value for pos in self.positions.values() if pos.position_type == PositionType.T_POSITION)
        }
    
    def can_buy(self, symbol: str, quantity: int, price: float, position_type: PositionType) -> Tuple[bool, str]:
        """
        检查是否可以买入
        
        Args:
            symbol: 股票代码
            quantity: 数量
            price: 价格
            position_type: 持仓类型
            
        Returns:
            (是否可以买入, 原因)
        """
        required_amount = quantity * price
        commission = self._calculate_commission(required_amount, "buy")
        total_cost = required_amount + commission
        
        # 检查现金是否充足
        if total_cost > self.available_cash:
            return False, f"现金不足: 需要{total_cost:,.2f}, 可用{self.available_cash:,.2f}"
        
        return True, "可以买入"
    
    def execute_buy(self, symbol: str, quantity: int, price: float, position_type: PositionType, notes: str = "") -> bool:
        """
        执行买入操作
        
        Args:
            symbol: 股票代码
            quantity: 数量
            price: 价格
            position_type: 持仓类型
            notes: 备注
            
        Returns:
            是否执行成功
        """
        can_buy, reason = self.can_buy(symbol, quantity, price, position_type)
        if not can_buy:
            logger.warning(f"买入失败: {symbol} - {reason}")
            return False
        
        amount = quantity * price
        commission = self._calculate_commission(amount, "buy")
        tax = 0  # 买入不收印花税
        net_amount = amount + commission + tax
        
        # 更新现金
        self.available_cash -= net_amount
        
        # 更新持仓
        if symbol in self.positions:
            position = self.positions[symbol]
            total_cost = position.quantity * position.avg_cost + amount
            total_quantity = position.quantity + quantity
            position.avg_cost = total_cost / total_quantity
            position.quantity = total_quantity
            position.market_value = total_quantity * price
        else:
            self.positions[symbol] = Position(
                symbol=symbol,
                position_type=position_type,
                quantity=quantity,
                avg_cost=price,
                current_price=price,
                market_value=amount,
                unrealized_pnl=0,
                unrealized_pnl_pct=0,
                last_update=datetime.now()
            )
        
        # 记录交易
        transaction_type = TransactionType.BUY_BASE if position_type == PositionType.BASE else TransactionType.BUY_T
        transaction = Transaction(
            transaction_id=self._generate_transaction_id(),
            timestamp=datetime.now(),
            symbol=symbol,
            transaction_type=transaction_type,
            quantity=quantity,
            price=price,
            amount=amount,
            commission=commission,
            tax=tax,
            net_amount=net_amount,
            balance_after=self.available_cash,
            notes=notes
        )
        self.transactions.append(transaction)
        
        logger.info(f"买入成功: {symbol} {quantity}股 @{price:.2f} 类型:{position_type.value}")
        return True
    
    def execute_sell(self, symbol: str, quantity: int, price: float, position_type: PositionType, notes: str = "") -> bool:
        """
        执行卖出操作
        
        Args:
            symbol: 股票代码
            quantity: 数量
            price: 价格
            position_type: 持仓类型
            notes: 备注
            
        Returns:
            是否执行成功
        """
        # 检查是否有足够持仓
        if symbol not in self.positions:
            logger.warning(f"卖出失败: {symbol} - 没有持仓")
            return False
        
        position = self.positions[symbol]
        if position.quantity < quantity:
            logger.warning(f"卖出失败: {symbol} - 持仓数量不足")
            return False
        
        amount = quantity * price
        commission = self._calculate_commission(amount, "sell")
        tax = amount * 0.001  # 印花税0.1%
        net_amount = amount - commission - tax
        
        # 更新现金
        self.available_cash += net_amount
        
        # 更新持仓
        position.quantity -= quantity
        position.market_value = position.quantity * position.current_price
        
        # 如果全部卖出，删除持仓
        if position.quantity == 0:
            del self.positions[symbol]
        
        # 记录交易
        transaction_type = TransactionType.SELL_BASE if position_type == PositionType.BASE else TransactionType.SELL_T
        transaction = Transaction(
            transaction_id=self._generate_transaction_id(),
            timestamp=datetime.now(),
            symbol=symbol,
            transaction_type=transaction_type,
            quantity=quantity,
            price=price,
            amount=amount,
            commission=commission,
            tax=tax,
            net_amount=net_amount,
            balance_after=self.available_cash,
            notes=notes
        )
        self.transactions.append(transaction)
        
        logger.info(f"卖出成功: {symbol} {quantity}股 @{price:.2f} 类型:{position_type.value}")
        return True
    
    def update_positions_price(self, price_data: Dict[str, float]):
        """
        更新持仓价格
        
        Args:
            price_data: 价格数据字典 {symbol: price}
        """
        for symbol, position in self.positions.items():
            if symbol in price_data:
                position.update_price(price_data[symbol])
    
    def get_portfolio_summary(self) -> Dict:
        """
        获取投资组合摘要
        
        Returns:
            投资组合摘要字典
        """
        total_market_value = sum(pos.market_value for pos in self.positions.values())
        total_cost = sum(pos.quantity * pos.avg_cost for pos in self.positions.values())
        total_unrealized_pnl = sum(pos.unrealized_pnl for pos in self.positions.values())
        
        portfolio_value = total_market_value + self.available_cash
        total_return = (portfolio_value - self.total_capital) / self.total_capital
        
        return {
            "total_capital": self.total_capital,
            "available_cash": self.available_cash,
            "total_market_value": total_market_value,
            "portfolio_value": portfolio_value,
            "total_cost": total_cost,
            "total_unrealized_pnl": total_unrealized_pnl,
            "total_return": total_return,
            "total_return_pct": total_return * 100,
            "daily_loss": self.daily_loss,
            "max_drawdown": self.max_drawdown,
            "position_count": len(self.positions),
            "cash_ratio": self.available_cash / portfolio_value if portfolio_value > 0 else 0
        }
    
    def calculate_capital_efficiency(self) -> Dict:
        """
        计算资金使用效率
        
        Returns:
            资金效率指标字典
        """
        portfolio_summary = self.get_portfolio_summary()
        
        # 资金利用率
        capital_utilization = (portfolio_summary["total_market_value"] / self.total_capital) * 100
        
        # 现金闲置率
        cash_idle_rate = (self.available_cash / self.total_capital) * 100
        
        # 持仓集中度
        if self.positions:
            position_values = [pos.market_value for pos in self.positions.values()]
            max_position_value = max(position_values)
            concentration_ratio = (max_position_value / portfolio_summary["total_market_value"]) * 100
        else:
            concentration_ratio = 0
        
        # 换手率计算（基于最近30天交易）
        recent_transactions = [t for t in self.transactions 
                             if t.timestamp > datetime.now() - timedelta(days=30)]
        total_turnover = sum(t.amount for t in recent_transactions)
        turnover_rate = (total_turnover / self.total_capital) * 100 if self.total_capital > 0 else 0
        
        # T+0效率计算
        t_transactions = [t for t in recent_transactions 
                         if t.transaction_type in [TransactionType.BUY_T, TransactionType.SELL_T]]
        t_profit = sum(t.net_amount for t in t_transactions if t.transaction_type == TransactionType.SELL_T) - \
                  sum(t.net_amount for t in t_transactions if t.transaction_type == TransactionType.BUY_T)
        t_efficiency = (t_profit / self.total_capital) * 100 if self.total_capital > 0 else 0
        
        # 计算综合效率评分
        efficiency_score = self._calculate_efficiency_score(capital_utilization, cash_idle_rate, t_efficiency)
        
        return {
            "capital_utilization": capital_utilization,
            "cash_idle_rate": cash_idle_rate,
            "concentration_ratio": concentration_ratio,
            "turnover_rate": turnover_rate,
            "t_efficiency": t_efficiency,
            "efficiency_score": efficiency_score
        }
    
    def optimize_capital_allocation(self, target_stocks: List[str], stock_scores: Dict[str, float]) -> Dict:
        """
        优化资金分配
        
        Args:
            target_stocks: 目标股票列表
            stock_scores: 股票评分字典
            
        Returns:
            优化后的资金分配方案
        """
        if not target_stocks or not stock_scores:
            return {"error": "目标股票列表或评分为空"}
        
        # 根据资金模式确定分配策略
        if self.config.mode == CapitalMode.SMALL:
            return self._optimize_small_capital_allocation(target_stocks, stock_scores)
        else:
            return self._optimize_medium_capital_allocation(target_stocks, stock_scores)
    
    def _optimize_small_capital_allocation(self, target_stocks: List[str], stock_scores: Dict[str, float]) -> Dict:
        """小资金模式优化分配"""
        # 选择评分最高的股票全仓
        best_stock = max(target_stocks, key=lambda x: stock_scores.get(x, 0))
        
        base_allocation = self.total_capital * self.config.base_position_ratio
        t_allocation = self.total_capital * self.config.t_position_ratio
        
        return {
            "strategy": "全仓单股+做T",
            "allocations": {
                best_stock: {
                    "base_amount": base_allocation,
                    "t_amount": t_allocation,
                    "total_amount": base_allocation + t_allocation,
                    "score": stock_scores.get(best_stock, 0)
                }
            },
            "cash_reserve": self.total_capital * self.config.cash_reserve_ratio,
            "expected_efficiency": 95.0
        }
    
    def _optimize_medium_capital_allocation(self, target_stocks: List[str], stock_scores: Dict[str, float]) -> Dict:
        """中等资金模式优化分配"""
        # 选择评分前2-3只股票分配
        sorted_stocks = sorted(target_stocks, key=lambda x: stock_scores.get(x, 0), reverse=True)[:3]
        
        total_investable = self.total_capital * (self.config.base_position_ratio + self.config.t_position_ratio)
        
        # 按评分权重分配
        total_score = sum(stock_scores.get(stock, 0) for stock in sorted_stocks)
        allocations = {}
        
        for stock in sorted_stocks:
            score = stock_scores.get(stock, 0)
            weight = score / total_score if total_score > 0 else 1.0 / len(sorted_stocks)
            allocation = total_investable * weight
            
            allocations[stock] = {
                "base_amount": allocation * 0.7,
                "t_amount": allocation * 0.3,
                "total_amount": allocation,
                "weight": weight,
                "score": score
            }
        
        return {
            "strategy": "多股分散+做T",
            "allocations": allocations,
            "cash_reserve": self.total_capital * self.config.cash_reserve_ratio,
            "expected_efficiency": 85.0
        }
    
    def _calculate_commission(self, amount: float, side: str) -> float:
        """
        计算手续费
        
        Args:
            amount: 交易金额
            side: 买卖方向 ("buy" or "sell")
            
        Returns:
            手续费
        """
        # 佣金费率 0.03%，最低5元
        commission_rate = 0.0003
        commission = max(amount * commission_rate, 5.0)
        return commission
    
    def _generate_transaction_id(self) -> str:
        """生成交易ID"""
        return f"T{datetime.now().strftime('%Y%m%d%H%M%S')}{len(self.transactions):04d}"
    
    def _calculate_efficiency_score(self, utilization: float, idle_rate: float, t_efficiency: float) -> float:
        """
        计算综合效率评分
        
        Args:
            utilization: 资金利用率
            idle_rate: 现金闲置率
            t_efficiency: T+0效率
            
        Returns:
            效率评分 (0-100)
        """
        # 权重分配
        utilization_weight = 0.4
        idle_penalty_weight = 0.3
        t_efficiency_weight = 0.3
        
        # 利用率评分 (目标90-95%)
        utilization_score = 100 if 90 <= utilization <= 95 else max(0, 100 - abs(utilization - 92.5) * 2)
        
        # 闲置率惩罚 (目标<10%)
        idle_penalty = max(0, idle_rate - 10) * 5
        
        # T+0效率奖励
        t_bonus = min(t_efficiency * 10, 30)  # 最高30分奖励
        
        efficiency_score = (utilization_score * utilization_weight - 
                          idle_penalty * idle_penalty_weight + 
                          t_bonus * t_efficiency_weight)
        
        return max(0, min(100, efficiency_score))


# 测试代码
if __name__ == "__main__":
    # 创建测试配置
    config = CapitalConfig(
        total_capital=100000.0,
        mode=CapitalMode.SMALL,
        max_stocks=1,
        base_position_ratio=0.75,
        t_position_ratio=0.20,
        cash_reserve_ratio=0.05,
        max_single_stock_ratio=1.0,
        max_daily_loss_ratio=0.03,
        max_drawdown_ratio=0.15,
        leverage_ratio=1.0
    )
    
    # 创建资金管理器
    manager = CapitalManager(config)
    
    print("=== 资金管理系统测试 ===")
    
    # 测试资金分配
    allocation = manager.get_capital_allocation()
    print(f"资金分配: {allocation}")
    
    # 测试买入操作
    success = manager.execute_buy("000001.SZ", 1000, 10.0, PositionType.BASE, "测试买入")
    print(f"买入结果: {success}")
    
    # 测试投资组合摘要
    portfolio = manager.get_portfolio_summary()
    print(f"投资组合: {portfolio}")
    
    # 测试资金效率计算
    efficiency = manager.calculate_capital_efficiency()
    print(f"资金效率: {efficiency}")
    
    print("=== 测试完成 ===")