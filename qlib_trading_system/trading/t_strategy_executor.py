"""
T+0策略执行器
整合时机分析、成本优化和风险控制，实现完整的T+0交易策略
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
import logging
from datetime import datetime, timedelta
from collections import deque
import json
import threading
import time

from .t_plus_zero_engine import (
    TTimingAnalyzer, TCostOptimizer, TPosition, TSignal, TTradeRecord, 
    TPositionType, TSignalType
)
from .t_risk_controller import TRiskController, RiskAlert, RiskAction, RiskLevel
from ..utils.config.integration_adapter import get_module_config

logger = logging.getLogger(__name__)


class ExecutionStatus(Enum):
    """执行状态"""
    IDLE = "IDLE"                    # 空闲
    ANALYZING = "ANALYZING"          # 分析中
    EXECUTING = "EXECUTING"          # 执行中
    MONITORING = "MONITORING"        # 监控中
    EMERGENCY = "EMERGENCY"          # 紧急状态
    STOPPED = "STOPPED"              # 已停止


@dataclass
class ExecutionConfig:
    """执行配置"""
    # 基础参数
    symbol: str
    base_shares: int
    base_cost: float
    
    # 执行参数
    max_t_ratio: float = 0.25        # 最大T仓比例
    min_profit_threshold: float = 0.005  # 最小盈利阈值
    analysis_interval: int = 30      # 分析间隔（秒）
    
    # 风险参数
    stop_loss_pct: float = 0.015     # 止损比例
    max_holding_time: int = 120      # 最大持有时间（分钟）
    max_daily_trades: int = 20       # 日最大交易次数
    
    # 成本优化参数
    target_cost_reduction: float = 0.01  # 目标成本降低
    cost_optimization_enabled: bool = True
    
    # 回调函数
    on_signal_generated: Optional[Callable] = None
    on_trade_executed: Optional[Callable] = None
    on_risk_alert: Optional[Callable] = None


@dataclass
class ExecutionState:
    """执行状态"""
    status: ExecutionStatus = ExecutionStatus.IDLE
    base_position: Optional[TPosition] = None
    t_positions: List[TPosition] = field(default_factory=list)
    
    # 统计信息
    total_trades: int = 0
    successful_trades: int = 0
    total_profit: float = 0.0
    cost_reduction: float = 0.0
    
    # 当日统计
    daily_trades: int = 0
    daily_profit: float = 0.0
    
    # 最新信号
    latest_signal: Optional[TSignal] = None
    last_analysis_time: Optional[datetime] = None
    
    # 风险状态
    risk_alerts: List[RiskAlert] = field(default_factory=list)
    emergency_mode: bool = False


class TStrategyExecutor:
    """T+0策略执行器"""
    
    def __init__(self, config: ExecutionConfig):
        """
        初始化策略执行器

        Args:
            config: 执行配置
        """
        self.config = config
        self.state = ExecutionState()

        # 从新配置系统加载策略配置
        self._load_strategy_config()

        # 初始化组件
        self.timing_analyzer = TTimingAnalyzer(self._get_timing_config())
        self.cost_optimizer = TCostOptimizer(self._get_cost_config())
        self.risk_controller = TRiskController(self._get_risk_config())
        
        # 数据缓存
        self.market_data_cache = deque(maxlen=200)  # 缓存200个数据点
        self.execution_history = deque(maxlen=1000)
        
        # 线程控制
        self.running = False
        self.analysis_thread = None
        self.monitoring_thread = None
        self._lock = threading.Lock()
        
        # 初始化底仓
        self._initialize_base_position()
        
        logger.info(f"T+0策略执行器初始化完成: {config.symbol}")

    def _load_strategy_config(self):
        """从新配置系统加载策略配置"""
        try:
            # 获取T+0策略配置
            strategy_config = get_module_config('t_plus_zero')
            if strategy_config and 'parameters' in strategy_config:
                params = strategy_config['parameters']
                # 更新配置参数
                if 'max_t_ratio' in params:
                    self.config.max_t_ratio = params['max_t_ratio']
                if 'min_profit_threshold' in params:
                    self.config.min_profit_threshold = params['min_profit_threshold']
                if 'analysis_interval' in params:
                    self.config.analysis_interval = params['analysis_interval']
                if 'stop_loss_pct' in params:
                    self.config.stop_loss_pct = params['stop_loss_pct']
                if 'max_holding_time' in params:
                    self.config.max_holding_time = params['max_holding_time']
                if 'max_daily_trades' in params:
                    self.config.max_daily_trades = params['max_daily_trades']
                if 'target_cost_reduction' in params:
                    self.config.target_cost_reduction = params['target_cost_reduction']

                logger.info("已从新配置系统加载T+0策略配置")
            else:
                logger.warning("未找到T+0策略配置，使用默认配置")

        except Exception as e:
            logger.error(f"加载策略配置失败: {e}")

    def _get_timing_config(self) -> Dict:
        """获取时机分析配置"""
        return {
            'min_profit_threshold': self.config.min_profit_threshold,
            'max_holding_time': self.config.max_holding_time,
            'rsi_period': 14,
            'macd_fast': 12,
            'macd_slow': 26,
            'macd_signal': 9
        }
    
    def _get_cost_config(self) -> Dict:
        """获取成本优化配置"""
        return {
            'target_cost_reduction': self.config.target_cost_reduction,
            'max_t_ratio': self.config.max_t_ratio,
            'commission_rate': 0.0003,
            'stamp_tax_rate': 0.001
        }
    
    def _get_risk_config(self) -> Dict:
        """获取风险控制配置"""
        return {
            'risk_limits': {
                'max_position_ratio': self.config.max_t_ratio,
                'max_single_loss': 0.02,
                'max_daily_loss': 0.05,
                'stop_loss_pct': self.config.stop_loss_pct,
                'max_holding_time': self.config.max_holding_time
            }
        }
    
    def _initialize_base_position(self):
        """初始化底仓"""
        self.state.base_position = TPosition(
            symbol=self.config.symbol,
            position_type=TPositionType.BASE,
            shares=self.config.base_shares,
            avg_cost=self.config.base_cost,
            current_price=self.config.base_cost,
            unrealized_pnl=0.0,
            unrealized_pnl_pct=0.0,
            entry_time=datetime.now(),
            holding_minutes=0,
            base_shares=self.config.base_shares
        )
    
    def start(self):
        """启动策略执行"""
        if self.running:
            logger.warning("策略执行器已在运行")
            return
        
        self.running = True
        self.state.status = ExecutionStatus.ANALYZING
        
        # 启动分析线程
        self.analysis_thread = threading.Thread(target=self._analysis_loop, daemon=True)
        self.analysis_thread.start()
        
        # 启动监控线程
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitoring_thread.start()
        
        logger.info(f"T+0策略执行器已启动: {self.config.symbol}")
    
    def stop(self):
        """停止策略执行"""
        self.running = False
        self.state.status = ExecutionStatus.STOPPED
        
        # 等待线程结束
        if self.analysis_thread and self.analysis_thread.is_alive():
            self.analysis_thread.join(timeout=5)
        
        if self.monitoring_thread and self.monitoring_thread.is_alive():
            self.monitoring_thread.join(timeout=5)
        
        logger.info(f"T+0策略执行器已停止: {self.config.symbol}")
    
    def update_market_data(self, data: pd.DataFrame):
        """
        更新市场数据
        
        Args:
            data: 最新市场数据
        """
        with self._lock:
            # 更新数据缓存
            if len(data) > 0:
                latest_row = data.iloc[-1].copy()
                latest_row['timestamp'] = datetime.now()
                self.market_data_cache.append(latest_row)
            
            # 更新底仓价格
            if self.state.base_position and len(data) > 0:
                current_price = data['close'].iloc[-1]
                self.state.base_position.update_price(current_price)
            
            # 更新T仓价格
            for t_position in self.state.t_positions:
                if len(data) > 0:
                    current_price = data['close'].iloc[-1]
                    t_position.update_price(current_price)
    
    def _analysis_loop(self):
        """分析循环"""
        while self.running:
            try:
                if self.state.status == ExecutionStatus.EMERGENCY:
                    time.sleep(60)  # 紧急状态下暂停分析
                    continue
                
                # 检查是否有足够数据
                if len(self.market_data_cache) < 30:
                    time.sleep(self.config.analysis_interval)
                    continue
                
                # 构建分析数据
                analysis_data = self._build_analysis_data()
                
                if analysis_data is not None and len(analysis_data) >= 30:
                    # 执行分析
                    self._perform_analysis(analysis_data)
                
                # 更新分析时间
                self.state.last_analysis_time = datetime.now()
                
                time.sleep(self.config.analysis_interval)
                
            except Exception as e:
                logger.error(f"分析循环异常: {e}")
                time.sleep(60)  # 异常时延长等待时间
    
    def _monitoring_loop(self):
        """监控循环"""
        while self.running:
            try:
                if self.state.status == ExecutionStatus.EMERGENCY:
                    time.sleep(30)
                    continue
                
                # 监控持仓风险
                self._monitor_positions()
                
                # 检查日度限制
                self._check_daily_limits()
                
                time.sleep(10)  # 每10秒监控一次
                
            except Exception as e:
                logger.error(f"监控循环异常: {e}")
                time.sleep(30)
    
    def _build_analysis_data(self) -> Optional[pd.DataFrame]:
        """构建分析数据"""
        if len(self.market_data_cache) < 30:
            return None
        
        # 转换为DataFrame
        data_list = []
        for item in list(self.market_data_cache)[-100:]:  # 使用最近100个数据点
            if isinstance(item, pd.Series):
                data_list.append(item.to_dict())
            else:
                data_list.append(item)
        
        if not data_list:
            return None
        
        try:
            df = pd.DataFrame(data_list)
            
            # 确保必要的列存在
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in required_columns:
                if col not in df.columns:
                    logger.warning(f"缺少必要列: {col}")
                    return None
            
            # 按时间排序
            if 'timestamp' in df.columns:
                df = df.sort_values('timestamp')
            
            return df
            
        except Exception as e:
            logger.error(f"构建分析数据失败: {e}")
            return None
    
    def _perform_analysis(self, data: pd.DataFrame):
        """执行分析"""
        try:
            with self._lock:
                if not self.state.base_position:
                    return
                
                self.state.status = ExecutionStatus.ANALYZING
                
                # 时机分析
                timing_signal = self.timing_analyzer.analyze_t_timing(data, self.state.base_position)
                
                # 成本优化分析
                if self.config.cost_optimization_enabled:
                    current_price = data['close'].iloc[-1]
                    optimization_result = self.cost_optimizer.optimize_t_strategy(
                        self.state.base_position, current_price, data
                    )
                    
                    # 结合优化建议调整信号
                    timing_signal = self._adjust_signal_with_optimization(timing_signal, optimization_result)
                
                # 风险检查
                allow_trade, risk_alerts = self.risk_controller.check_pre_trade_risk(
                    timing_signal, self.state.base_position, data
                )
                
                # 更新状态
                self.state.latest_signal = timing_signal
                self.state.risk_alerts = risk_alerts
                
                # 处理风险警报
                self._handle_risk_alerts(risk_alerts)
                
                # 执行交易决策
                if allow_trade and timing_signal.strength > 0.6:
                    self._execute_signal(timing_signal, data)
                
                # 回调通知
                if self.config.on_signal_generated:
                    self.config.on_signal_generated(timing_signal, allow_trade, risk_alerts)
                
        except Exception as e:
            logger.error(f"执行分析失败: {e}")
            self.state.status = ExecutionStatus.IDLE
    
    def _adjust_signal_with_optimization(self, signal: TSignal, optimization: Dict) -> TSignal:
        """结合优化建议调整信号"""
        if optimization['recommended_action'] == 'HOLD':
            # 优化建议持有，降低信号强度
            signal.strength *= 0.5
            signal.confidence *= 0.8
            signal.reason += "; 优化建议持有"
        
        elif optimization['recommended_action'] in ['BUY_T', 'SELL_T']:
            # 优化建议与信号一致，增强信号
            if ((optimization['recommended_action'] == 'BUY_T' and signal.signal_type == TSignalType.BUY_T) or
                (optimization['recommended_action'] == 'SELL_T' and signal.signal_type == TSignalType.SELL_T)):
                
                signal.strength = min(signal.strength * 1.2, 1.0)
                signal.confidence = min(signal.confidence * 1.1, 1.0)
                signal.suggested_shares = optimization.get('optimal_shares', signal.suggested_shares)
                signal.reason += f"; 优化支持({optimization['confidence']:.2f})"
        
        return signal
    
    def _execute_signal(self, signal: TSignal, data: pd.DataFrame):
        """执行交易信号"""
        try:
            self.state.status = ExecutionStatus.EXECUTING
            
            # 检查日度交易限制
            if self.state.daily_trades >= self.config.max_daily_trades:
                logger.warning("已达到日度交易次数限制")
                return
            
            # 模拟交易执行（实际实现中需要连接券商接口）
            trade_result = self._simulate_trade_execution(signal, data)
            
            if trade_result['success']:
                # 更新持仓
                self._update_positions_after_trade(signal, trade_result)
                
                # 记录交易
                trade_record = TTradeRecord(
                    symbol=signal.symbol,
                    trade_type=signal.signal_type.value,
                    shares=signal.suggested_shares,
                    price=signal.entry_price,
                    timestamp=datetime.now(),
                    cost=trade_result['cost'],
                    base_cost_before=self.state.base_position.avg_cost,
                    base_cost_after=self.state.base_position.avg_cost,  # 实际中需要重新计算
                    profit=0.0,  # 开仓时利润为0
                    success=True
                )
                
                # 更新统计
                self.state.total_trades += 1
                self.state.daily_trades += 1
                
                # 记录历史
                self.execution_history.append({
                    'timestamp': datetime.now(),
                    'signal': signal,
                    'trade_result': trade_result,
                    'trade_record': trade_record
                })
                
                # 更新风险控制器
                self.risk_controller.update_trade_result(trade_record)
                
                # 回调通知
                if self.config.on_trade_executed:
                    self.config.on_trade_executed(trade_record)
                
                logger.info(f"交易执行成功: {signal.signal_type.value} {signal.suggested_shares}股 @{signal.entry_price:.2f}")
            
            else:
                logger.warning(f"交易执行失败: {trade_result['error']}")
            
        except Exception as e:
            logger.error(f"执行交易信号失败: {e}")
        
        finally:
            self.state.status = ExecutionStatus.MONITORING
    
    def _simulate_trade_execution(self, signal: TSignal, data: pd.DataFrame) -> Dict:
        """模拟交易执行"""
        # 这里是模拟实现，实际中需要连接券商接口
        current_price = data['close'].iloc[-1]
        
        # 计算交易成本
        trade_value = signal.suggested_shares * current_price
        commission = max(trade_value * 0.0003, 5.0)  # 佣金
        stamp_tax = trade_value * 0.001 if signal.signal_type == TSignalType.SELL_T else 0  # 印花税
        total_cost = commission + stamp_tax
        
        # 模拟成功执行
        return {
            'success': True,
            'executed_price': current_price,
            'executed_shares': signal.suggested_shares,
            'cost': total_cost,
            'order_id': f"T_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            'execution_time': datetime.now()
        }
    
    def _update_positions_after_trade(self, signal: TSignal, trade_result: Dict):
        """交易后更新持仓"""
        if signal.signal_type == TSignalType.BUY_T:
            # 买入T仓
            t_position = TPosition(
                symbol=signal.symbol,
                position_type=TPositionType.T_LONG,
                shares=trade_result['executed_shares'],
                avg_cost=trade_result['executed_price'],
                current_price=trade_result['executed_price'],
                unrealized_pnl=0.0,
                unrealized_pnl_pct=0.0,
                entry_time=datetime.now(),
                holding_minutes=0,
                base_shares=self.state.base_position.shares,
                expected_profit=signal.expected_return * trade_result['executed_shares'] * trade_result['executed_price']
            )
            self.state.t_positions.append(t_position)
            
        elif signal.signal_type == TSignalType.SELL_T:
            # 卖出T仓（通过减少底仓实现）
            t_position = TPosition(
                symbol=signal.symbol,
                position_type=TPositionType.T_SHORT,
                shares=trade_result['executed_shares'],
                avg_cost=trade_result['executed_price'],
                current_price=trade_result['executed_price'],
                unrealized_pnl=0.0,
                unrealized_pnl_pct=0.0,
                entry_time=datetime.now(),
                holding_minutes=0,
                base_shares=self.state.base_position.shares,
                expected_profit=signal.expected_return * trade_result['executed_shares'] * trade_result['executed_price']
            )
            self.state.t_positions.append(t_position)
    
    def _monitor_positions(self):
        """监控持仓"""
        if not self.state.base_position:
            return
        
        # 构建持仓字典
        positions = {self.config.symbol: self.state.base_position}
        for t_pos in self.state.t_positions:
            positions[f"{self.config.symbol}_T_{t_pos.entry_time.strftime('%H%M%S')}"] = t_pos
        
        # 构建市场数据
        market_data = {}
        if len(self.market_data_cache) > 0:
            analysis_data = self._build_analysis_data()
            if analysis_data is not None:
                market_data[self.config.symbol] = analysis_data
        
        # 监控风险
        if market_data:
            risk_alerts = self.risk_controller.monitor_position_risk(positions, market_data)
            
            if risk_alerts:
                self.state.risk_alerts.extend(risk_alerts)
                self._handle_risk_alerts(risk_alerts)
    
    def _handle_risk_alerts(self, alerts: List[RiskAlert]):
        """处理风险警报"""
        for alert in alerts:
            logger.warning(f"风险警报: {alert.message}")
            
            # 根据建议动作处理
            if alert.suggested_action == RiskAction.STOP:
                self.state.status = ExecutionStatus.EMERGENCY
                logger.critical("交易已暂停")
            
            elif alert.suggested_action == RiskAction.LIQUIDATE:
                self._emergency_liquidate(alert)
            
            elif alert.risk_level == RiskLevel.CRITICAL:
                self.risk_controller.activate_emergency_mode(alert.message)
                self.state.emergency_mode = True
            
            # 回调通知
            if self.config.on_risk_alert:
                self.config.on_risk_alert(alert)
    
    def _emergency_liquidate(self, alert: RiskAlert):
        """紧急平仓"""
        logger.critical(f"执行紧急平仓: {alert.message}")
        
        # 平仓所有T仓位
        for t_position in self.state.t_positions[:]:  # 使用切片复制避免修改列表时的问题
            try:
                self._close_t_position(t_position, "紧急平仓")
            except Exception as e:
                logger.error(f"紧急平仓失败: {e}")
    
    def _close_t_position(self, position: TPosition, reason: str):
        """平仓T仓位"""
        logger.info(f"平仓T仓位: {position.position_type.value} {position.shares}股, 原因: {reason}")
        
        # 计算盈亏
        if position.position_type == TPositionType.T_LONG:
            profit = (position.current_price - position.avg_cost) * position.shares
        else:  # T_SHORT
            profit = (position.avg_cost - position.current_price) * position.shares
        
        # 扣除交易成本
        trade_value = position.shares * position.current_price
        cost = max(trade_value * 0.0003, 5.0) + trade_value * 0.001  # 佣金+印花税
        profit -= cost
        
        # 更新统计
        self.state.total_profit += profit
        self.state.daily_profit += profit
        
        if profit > 0:
            self.state.successful_trades += 1
        
        # 记录交易
        trade_record = TTradeRecord(
            symbol=position.symbol,
            trade_type="CLOSE_T",
            shares=position.shares,
            price=position.current_price,
            timestamp=datetime.now(),
            cost=cost,
            base_cost_before=self.state.base_position.avg_cost,
            base_cost_after=self.state.base_position.avg_cost,
            profit=profit,
            success=True
        )
        
        # 更新成本优化器
        self.cost_optimizer.record_trade(trade_record)
        
        # 从持仓列表中移除
        if position in self.state.t_positions:
            self.state.t_positions.remove(position)
    
    def _check_daily_limits(self):
        """检查日度限制"""
        now = datetime.now()
        
        # 检查是否需要重置日度统计
        if hasattr(self, '_last_reset_date'):
            if self._last_reset_date.date() != now.date():
                self._reset_daily_stats()
        else:
            self._last_reset_date = now
    
    def _reset_daily_stats(self):
        """重置日度统计"""
        self.state.daily_trades = 0
        self.state.daily_profit = 0.0
        self.risk_controller.reset_daily_stats()
        self._last_reset_date = datetime.now()
        logger.info("日度统计已重置")
    
    def get_status(self) -> Dict[str, Any]:
        """获取执行状态"""
        return {
            'status': self.state.status.value,
            'symbol': self.config.symbol,
            'base_position': {
                'shares': self.state.base_position.shares if self.state.base_position else 0,
                'avg_cost': self.state.base_position.avg_cost if self.state.base_position else 0,
                'current_price': self.state.base_position.current_price if self.state.base_position else 0,
                'unrealized_pnl': self.state.base_position.unrealized_pnl if self.state.base_position else 0,
                'unrealized_pnl_pct': self.state.base_position.unrealized_pnl_pct if self.state.base_position else 0
            },
            't_positions': len(self.state.t_positions),
            'statistics': {
                'total_trades': self.state.total_trades,
                'successful_trades': self.state.successful_trades,
                'success_rate': self.state.successful_trades / max(self.state.total_trades, 1),
                'total_profit': self.state.total_profit,
                'daily_trades': self.state.daily_trades,
                'daily_profit': self.state.daily_profit
            },
            'latest_signal': {
                'signal_type': self.state.latest_signal.signal_type.value if self.state.latest_signal else None,
                'strength': self.state.latest_signal.strength if self.state.latest_signal else 0,
                'confidence': self.state.latest_signal.confidence if self.state.latest_signal else 0,
                'timestamp': self.state.latest_signal.timestamp.isoformat() if self.state.latest_signal else None
            },
            'risk_status': {
                'emergency_mode': self.state.emergency_mode,
                'active_alerts': len(self.state.risk_alerts),
                'risk_summary': self.risk_controller.get_risk_summary()
            },
            'last_analysis_time': self.state.last_analysis_time.isoformat() if self.state.last_analysis_time else None
        }
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        # 计算成本降低效果
        cost_effect = self.cost_optimizer.calculate_cost_reduction_effect(
            self.state.base_position,
            [record for record in self.execution_history if hasattr(record, 'trade_record')]
        )
        
        return {
            'symbol': self.config.symbol,
            'period': {
                'start_time': self.execution_history[0]['timestamp'].isoformat() if self.execution_history else None,
                'end_time': datetime.now().isoformat()
            },
            'trading_performance': {
                'total_trades': self.state.total_trades,
                'successful_trades': self.state.successful_trades,
                'success_rate': self.state.successful_trades / max(self.state.total_trades, 1),
                'total_profit': self.state.total_profit,
                'average_profit_per_trade': self.state.total_profit / max(self.state.total_trades, 1)
            },
            'cost_optimization': cost_effect,
            'risk_metrics': self.risk_controller.get_risk_summary(),
            'position_status': {
                'base_position_value': (self.state.base_position.shares * self.state.base_position.current_price 
                                      if self.state.base_position else 0),
                't_positions_count': len(self.state.t_positions),
                'total_unrealized_pnl': sum(pos.unrealized_pnl for pos in self.state.t_positions)
            }
        }