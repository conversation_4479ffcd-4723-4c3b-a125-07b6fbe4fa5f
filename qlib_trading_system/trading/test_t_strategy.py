"""
T+0策略执行引擎集成测试
测试完整的T+0交易策略功能
"""

import pandas as pd
import numpy as np
import unittest
from datetime import datetime, timedelta
import time
import logging
from typing import Dict, List

from .t_plus_zero_engine import TTimingAnalyzer, TCostOptimizer, TPosition, TPositionType
from .t_risk_controller import TRiskController, RiskLevel
from .t_strategy_executor import TStrategyExecutor, ExecutionConfig, ExecutionStatus

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestTStrategyIntegration(unittest.TestCase):
    """T+0策略集成测试"""
    
    def setUp(self):
        """测试初始化"""
        self.symbol = "000001.SZ"
        self.base_shares = 1000
        self.base_cost = 10.0
        
        # 创建执行配置
        self.config = ExecutionConfig(
            symbol=self.symbol,
            base_shares=self.base_shares,
            base_cost=self.base_cost,
            max_t_ratio=0.25,
            min_profit_threshold=0.005,
            analysis_interval=1,  # 测试时使用1秒间隔
            stop_loss_pct=0.015,
            max_holding_time=60,  # 测试时使用60分钟
            max_daily_trades=10
        )
        
        # 创建策略执行器
        self.executor = TStrategyExecutor(self.config)
        
        # 创建测试数据
        self.test_data = self._create_test_data()
        
        logger.info("测试初始化完成")
    
    def tearDown(self):
        """测试清理"""
        if hasattr(self, 'executor'):
            self.executor.stop()
        logger.info("测试清理完成")
    
    def _create_test_data(self) -> pd.DataFrame:
        """创建测试数据"""
        # 生成100个数据点的模拟行情数据
        np.random.seed(42)  # 固定随机种子确保测试可重复
        
        dates = pd.date_range(start='2024-01-01 09:30:00', periods=100, freq='1min')
        
        # 基础价格走势
        base_price = 10.0
        price_changes = np.random.normal(0, 0.01, 100)  # 1%的标准差
        prices = [base_price]
        
        for change in price_changes[1:]:
            new_price = prices[-1] * (1 + change)
            prices.append(max(new_price, 0.1))  # 确保价格为正
        
        # 生成OHLCV数据
        data = []
        for i, (date, price) in enumerate(zip(dates, prices)):
            # 生成高低开收
            volatility = 0.005  # 0.5%的日内波动
            high = price * (1 + np.random.uniform(0, volatility))
            low = price * (1 - np.random.uniform(0, volatility))
            
            if i == 0:
                open_price = price
            else:
                open_price = prices[i-1]
            
            close_price = price
            
            # 确保OHLC逻辑正确
            high = max(high, open_price, close_price)
            low = min(low, open_price, close_price)
            
            # 生成成交量
            volume = np.random.randint(100000, 1000000)
            
            data.append({
                'timestamp': date,
                'open': round(open_price, 2),
                'high': round(high, 2),
                'low': round(low, 2),
                'close': round(close_price, 2),
                'volume': volume
            })
        
        df = pd.DataFrame(data)
        logger.info(f"创建测试数据: {len(df)}行")
        return df
    
    def test_timing_analyzer(self):
        """测试时机分析器"""
        logger.info("开始测试时机分析器")
        
        # 创建时机分析器
        analyzer = TTimingAnalyzer()
        
        # 创建底仓
        base_position = TPosition(
            symbol=self.symbol,
            position_type=TPositionType.BASE,
            shares=self.base_shares,
            avg_cost=self.base_cost,
            current_price=self.base_cost,
            unrealized_pnl=0.0,
            unrealized_pnl_pct=0.0,
            entry_time=datetime.now(),
            holding_minutes=0
        )
        
        # 测试分析功能
        signal = analyzer.analyze_t_timing(self.test_data, base_position)
        
        # 验证信号
        self.assertIsNotNone(signal)
        self.assertEqual(signal.symbol, self.symbol)
        self.assertIn(signal.signal_type.value, ['BUY_T', 'SELL_T', 'HOLD_T'])
        self.assertGreaterEqual(signal.strength, 0.0)
        self.assertLessEqual(signal.strength, 1.0)
        self.assertGreaterEqual(signal.confidence, 0.0)
        self.assertLessEqual(signal.confidence, 1.0)
        
        logger.info(f"时机分析结果: {signal.signal_type.value}, 强度: {signal.strength:.3f}, 置信度: {signal.confidence:.3f}")
        logger.info("时机分析器测试通过")
    
    def test_cost_optimizer(self):
        """测试成本优化器"""
        logger.info("开始测试成本优化器")
        
        # 创建成本优化器
        optimizer = TCostOptimizer()
        
        # 创建底仓
        base_position = TPosition(
            symbol=self.symbol,
            position_type=TPositionType.BASE,
            shares=self.base_shares,
            avg_cost=self.base_cost,
            current_price=self.test_data['close'].iloc[-1],
            unrealized_pnl=0.0,
            unrealized_pnl_pct=0.0,
            entry_time=datetime.now(),
            holding_minutes=0
        )
        
        # 测试优化功能
        current_price = self.test_data['close'].iloc[-1]
        optimization_result = optimizer.optimize_t_strategy(base_position, current_price, self.test_data)
        
        # 验证结果
        self.assertIsInstance(optimization_result, dict)
        self.assertIn('recommended_action', optimization_result)
        self.assertIn('optimal_shares', optimization_result)
        self.assertIn('expected_cost_reduction', optimization_result)
        self.assertIn('confidence', optimization_result)
        
        logger.info(f"成本优化结果: {optimization_result['recommended_action']}, 预期成本降低: {optimization_result['expected_cost_reduction']:.4f}")
        logger.info("成本优化器测试通过")
    
    def test_risk_controller(self):
        """测试风险控制器"""
        logger.info("开始测试风险控制器")
        
        # 创建风险控制器
        risk_controller = TRiskController()
        
        # 创建底仓
        base_position = TPosition(
            symbol=self.symbol,
            position_type=TPositionType.BASE,
            shares=self.base_shares,
            avg_cost=self.base_cost,
            current_price=self.test_data['close'].iloc[-1],
            unrealized_pnl=0.0,
            unrealized_pnl_pct=0.0,
            entry_time=datetime.now(),
            holding_minutes=0
        )
        
        # 创建测试信号
        from .t_plus_zero_engine import TSignal, TSignalType
        test_signal = TSignal(
            symbol=self.symbol,
            signal_type=TSignalType.BUY_T,
            strength=0.8,
            confidence=0.7,
            expected_return=0.01,
            suggested_shares=200,
            entry_price=self.test_data['close'].iloc[-1]
        )
        
        # 测试风险检查
        allow_trade, alerts = risk_controller.check_pre_trade_risk(test_signal, base_position, self.test_data)
        
        # 验证结果
        self.assertIsInstance(allow_trade, bool)
        self.assertIsInstance(alerts, list)
        
        logger.info(f"风险检查结果: 允许交易={allow_trade}, 警报数量={len(alerts)}")
        
        # 测试持仓监控
        positions = {self.symbol: base_position}
        market_data = {self.symbol: self.test_data}
        monitor_alerts = risk_controller.monitor_position_risk(positions, market_data)
        
        self.assertIsInstance(monitor_alerts, list)
        
        logger.info(f"持仓监控结果: 警报数量={len(monitor_alerts)}")
        logger.info("风险控制器测试通过")
    
    def test_strategy_executor_initialization(self):
        """测试策略执行器初始化"""
        logger.info("开始测试策略执行器初始化")
        
        # 验证初始化状态
        self.assertEqual(self.executor.config.symbol, self.symbol)
        self.assertEqual(self.executor.config.base_shares, self.base_shares)
        self.assertEqual(self.executor.config.base_cost, self.base_cost)
        
        # 验证底仓初始化
        self.assertIsNotNone(self.executor.state.base_position)
        self.assertEqual(self.executor.state.base_position.symbol, self.symbol)
        self.assertEqual(self.executor.state.base_position.shares, self.base_shares)
        self.assertEqual(self.executor.state.base_position.avg_cost, self.base_cost)
        
        # 验证组件初始化
        self.assertIsNotNone(self.executor.timing_analyzer)
        self.assertIsNotNone(self.executor.cost_optimizer)
        self.assertIsNotNone(self.executor.risk_controller)
        
        logger.info("策略执行器初始化测试通过")
    
    def test_market_data_update(self):
        """测试市场数据更新"""
        logger.info("开始测试市场数据更新")
        
        # 更新市场数据
        self.executor.update_market_data(self.test_data)
        
        # 验证数据缓存
        self.assertGreater(len(self.executor.market_data_cache), 0)
        
        # 验证底仓价格更新
        latest_price = self.test_data['close'].iloc[-1]
        self.assertEqual(self.executor.state.base_position.current_price, latest_price)
        
        logger.info(f"市场数据更新成功, 最新价格: {latest_price}")
        logger.info("市场数据更新测试通过")
    
    def test_analysis_data_building(self):
        """测试分析数据构建"""
        logger.info("开始测试分析数据构建")
        
        # 先更新市场数据
        self.executor.update_market_data(self.test_data)
        
        # 构建分析数据
        analysis_data = self.executor._build_analysis_data()
        
        # 验证分析数据
        self.assertIsNotNone(analysis_data)
        self.assertIsInstance(analysis_data, pd.DataFrame)
        self.assertGreater(len(analysis_data), 0)
        
        # 验证必要列存在
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in required_columns:
            self.assertIn(col, analysis_data.columns)
        
        logger.info(f"分析数据构建成功, 数据行数: {len(analysis_data)}")
        logger.info("分析数据构建测试通过")
    
    def test_signal_execution_simulation(self):
        """测试信号执行模拟"""
        logger.info("开始测试信号执行模拟")
        
        # 创建测试信号
        from .t_plus_zero_engine import TSignal, TSignalType
        test_signal = TSignal(
            symbol=self.symbol,
            signal_type=TSignalType.BUY_T,
            strength=0.8,
            confidence=0.7,
            expected_return=0.01,
            suggested_shares=200,
            entry_price=self.test_data['close'].iloc[-1]
        )
        
        # 模拟交易执行
        trade_result = self.executor._simulate_trade_execution(test_signal, self.test_data)
        
        # 验证交易结果
        self.assertIsInstance(trade_result, dict)
        self.assertTrue(trade_result['success'])
        self.assertIn('executed_price', trade_result)
        self.assertIn('executed_shares', trade_result)
        self.assertIn('cost', trade_result)
        self.assertIn('order_id', trade_result)
        
        logger.info(f"交易执行模拟成功: {trade_result['executed_shares']}股 @{trade_result['executed_price']:.2f}")
        logger.info("信号执行模拟测试通过")
    
    def test_position_update_after_trade(self):
        """测试交易后持仓更新"""
        logger.info("开始测试交易后持仓更新")
        
        # 记录初始T仓位数量
        initial_t_positions = len(self.executor.state.t_positions)
        
        # 创建买入T仓信号
        from .t_plus_zero_engine import TSignal, TSignalType
        buy_signal = TSignal(
            symbol=self.symbol,
            signal_type=TSignalType.BUY_T,
            strength=0.8,
            confidence=0.7,
            expected_return=0.01,
            suggested_shares=200,
            entry_price=self.test_data['close'].iloc[-1]
        )
        
        # 模拟交易结果
        trade_result = {
            'success': True,
            'executed_price': buy_signal.entry_price,
            'executed_shares': buy_signal.suggested_shares,
            'cost': 10.0,
            'order_id': 'TEST_001',
            'execution_time': datetime.now()
        }
        
        # 更新持仓
        self.executor._update_positions_after_trade(buy_signal, trade_result)
        
        # 验证T仓位增加
        self.assertEqual(len(self.executor.state.t_positions), initial_t_positions + 1)
        
        # 验证新增T仓位属性
        new_t_position = self.executor.state.t_positions[-1]
        self.assertEqual(new_t_position.symbol, self.symbol)
        self.assertEqual(new_t_position.position_type, TPositionType.T_LONG)
        self.assertEqual(new_t_position.shares, buy_signal.suggested_shares)
        self.assertEqual(new_t_position.avg_cost, buy_signal.entry_price)
        
        logger.info(f"T仓位更新成功: {new_t_position.position_type.value} {new_t_position.shares}股")
        logger.info("交易后持仓更新测试通过")
    
    def test_status_reporting(self):
        """测试状态报告"""
        logger.info("开始测试状态报告")
        
        # 获取状态
        status = self.executor.get_status()
        
        # 验证状态结构
        self.assertIsInstance(status, dict)
        self.assertIn('status', status)
        self.assertIn('symbol', status)
        self.assertIn('base_position', status)
        self.assertIn('statistics', status)
        self.assertIn('risk_status', status)
        
        # 验证基本信息
        self.assertEqual(status['symbol'], self.symbol)
        self.assertEqual(status['base_position']['shares'], self.base_shares)
        self.assertEqual(status['base_position']['avg_cost'], self.base_cost)
        
        logger.info(f"状态报告: {status['status']}, T仓位数: {status['t_positions']}")
        
        # 获取性能报告
        performance = self.executor.get_performance_report()
        
        # 验证性能报告结构
        self.assertIsInstance(performance, dict)
        self.assertIn('symbol', performance)
        self.assertIn('trading_performance', performance)
        self.assertIn('cost_optimization', performance)
        self.assertIn('risk_metrics', performance)
        
        logger.info(f"性能报告: 总交易{performance['trading_performance']['total_trades']}笔")
        logger.info("状态报告测试通过")
    
    def test_full_workflow_simulation(self):
        """测试完整工作流程模拟"""
        logger.info("开始测试完整工作流程模拟")
        
        # 记录回调调用
        signal_callbacks = []
        trade_callbacks = []
        risk_callbacks = []
        
        def on_signal_generated(signal, allow_trade, alerts):
            signal_callbacks.append((signal, allow_trade, alerts))
        
        def on_trade_executed(trade_record):
            trade_callbacks.append(trade_record)
        
        def on_risk_alert(alert):
            risk_callbacks.append(alert)
        
        # 设置回调
        self.executor.config.on_signal_generated = on_signal_generated
        self.executor.config.on_trade_executed = on_trade_executed
        self.executor.config.on_risk_alert = on_risk_alert
        
        # 分批更新市场数据并执行分析
        batch_size = 20
        for i in range(0, len(self.test_data), batch_size):
            batch_data = self.test_data.iloc[i:i+batch_size]
            
            # 更新市场数据
            self.executor.update_market_data(batch_data)
            
            # 如果有足够数据，执行一次分析
            if len(self.executor.market_data_cache) >= 30:
                analysis_data = self.executor._build_analysis_data()
                if analysis_data is not None:
                    self.executor._perform_analysis(analysis_data)
            
            # 短暂等待
            time.sleep(0.1)
        
        # 验证工作流程执行
        logger.info(f"信号生成回调: {len(signal_callbacks)}次")
        logger.info(f"交易执行回调: {len(trade_callbacks)}次")
        logger.info(f"风险警报回调: {len(risk_callbacks)}次")
        
        # 获取最终状态
        final_status = self.executor.get_status()
        logger.info(f"最终状态: {final_status['status']}")
        logger.info(f"总交易次数: {final_status['statistics']['total_trades']}")
        logger.info(f"成功率: {final_status['statistics']['success_rate']:.2%}")
        
        logger.info("完整工作流程模拟测试通过")


def create_sample_data_for_manual_test():
    """创建用于手动测试的样本数据"""
    logger.info("创建手动测试样本数据")
    
    # 创建更真实的股票数据
    np.random.seed(123)
    
    # 模拟一天的分钟级数据
    start_time = datetime.now().replace(hour=9, minute=30, second=0, microsecond=0)
    periods = 240  # 4小时 * 60分钟
    
    dates = pd.date_range(start=start_time, periods=periods, freq='1min')
    
    # 创建有趋势的价格数据
    base_price = 15.0
    trend = 0.0001  # 轻微上涨趋势
    volatility = 0.008  # 0.8%波动率
    
    prices = [base_price]
    for i in range(1, periods):
        # 添加趋势和随机波动
        change = trend + np.random.normal(0, volatility)
        new_price = prices[-1] * (1 + change)
        prices.append(max(new_price, 1.0))  # 确保价格为正
    
    # 生成完整的OHLCV数据
    data = []
    for i, (date, close_price) in enumerate(zip(dates, prices)):
        if i == 0:
            open_price = close_price
        else:
            open_price = prices[i-1]
        
        # 生成日内高低点
        intraday_range = close_price * 0.003  # 0.3%的日内波动
        high = max(open_price, close_price) + np.random.uniform(0, intraday_range)
        low = min(open_price, close_price) - np.random.uniform(0, intraday_range)
        
        # 生成成交量（模拟开盘和收盘时成交量较大）
        hour = date.hour
        if hour in [9, 14]:  # 开盘和收盘前
            volume_base = 800000
        elif hour in [11, 13]:  # 午休前后
            volume_base = 300000
        else:
            volume_base = 500000
        
        volume = int(volume_base * (1 + np.random.uniform(-0.3, 0.5)))
        
        data.append({
            'timestamp': date,
            'open': round(open_price, 2),
            'high': round(high, 2),
            'low': round(low, 2),
            'close': round(close_price, 2),
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    logger.info(f"创建样本数据: {len(df)}行, 价格范围: {df['close'].min():.2f} - {df['close'].max():.2f}")
    
    return df


def run_manual_test():
    """运行手动测试"""
    logger.info("开始手动测试T+0策略执行引擎")
    
    # 创建测试数据
    test_data = create_sample_data_for_manual_test()
    
    # 创建执行配置
    config = ExecutionConfig(
        symbol="000001.SZ",
        base_shares=2000,
        base_cost=15.0,
        max_t_ratio=0.2,
        min_profit_threshold=0.003,
        analysis_interval=2,
        stop_loss_pct=0.012,
        max_holding_time=90,
        max_daily_trades=15
    )
    
    # 创建策略执行器
    executor = TStrategyExecutor(config)
    
    # 设置回调函数
    def on_signal_generated(signal, allow_trade, alerts):
        logger.info(f"信号生成: {signal.signal_type.value}, 强度: {signal.strength:.3f}, 允许交易: {allow_trade}")
        if alerts:
            logger.warning(f"风险警报: {len(alerts)}个")
    
    def on_trade_executed(trade_record):
        logger.info(f"交易执行: {trade_record.trade_type} {trade_record.shares}股 @{trade_record.price:.2f}")
    
    def on_risk_alert(alert):
        logger.warning(f"风险警报: {alert.alert_type} - {alert.message}")
    
    config.on_signal_generated = on_signal_generated
    config.on_trade_executed = on_trade_executed
    config.on_risk_alert = on_risk_alert
    
    try:
        # 启动执行器
        executor.start()
        logger.info("策略执行器已启动")
        
        # 模拟实时数据流
        batch_size = 10
        for i in range(0, len(test_data), batch_size):
            batch_data = test_data.iloc[i:i+batch_size]
            
            # 更新市场数据
            executor.update_market_data(batch_data)
            
            # 显示当前状态
            if i % 50 == 0:  # 每50个数据点显示一次状态
                status = executor.get_status()
                logger.info(f"进度: {i}/{len(test_data)}, 状态: {status['status']}, "
                          f"交易: {status['statistics']['total_trades']}笔, "
                          f"盈利: {status['statistics']['total_profit']:.2f}")
            
            # 模拟实时延迟
            time.sleep(0.1)
        
        # 等待处理完成
        time.sleep(5)
        
        # 获取最终报告
        final_status = executor.get_status()
        performance_report = executor.get_performance_report()
        
        logger.info("=== 最终测试结果 ===")
        logger.info(f"执行状态: {final_status['status']}")
        logger.info(f"总交易次数: {final_status['statistics']['total_trades']}")
        logger.info(f"成功交易: {final_status['statistics']['successful_trades']}")
        logger.info(f"成功率: {final_status['statistics']['success_rate']:.2%}")
        logger.info(f"总盈利: {final_status['statistics']['total_profit']:.2f}")
        logger.info(f"T仓位数: {final_status['t_positions']}")
        
        logger.info("=== 成本优化效果 ===")
        cost_effect = performance_report['cost_optimization']
        logger.info(f"成本降低: {cost_effect['cost_reduction_pct']:.4%}")
        logger.info(f"新成本基础: {cost_effect['new_cost_basis']:.2f}")
        
        logger.info("手动测试完成")
        
    except Exception as e:
        logger.error(f"手动测试异常: {e}")
        
    finally:
        # 停止执行器
        executor.stop()
        logger.info("策略执行器已停止")


if __name__ == '__main__':
    # 运行单元测试
    print("运行T+0策略执行引擎单元测试...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    print("\n" + "="*50)
    print("运行手动集成测试...")
    
    # 运行手动测试
    run_manual_test()