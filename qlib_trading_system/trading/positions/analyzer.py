"""
仓位分析器
Position Analyzer
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import pandas as pd
import numpy as np

from .models import Position, PositionChange, PositionType, PortfolioSummary
from .manager import PositionManager


@dataclass
class PerformanceMetrics:
    """绩效指标"""
    # 收益指标
    total_return: float = 0.0  # 总收益率
    annualized_return: float = 0.0  # 年化收益率
    daily_return_mean: float = 0.0  # 日均收益率
    daily_return_std: float = 0.0  # 日收益率标准差
    
    # 风险指标
    max_drawdown: float = 0.0  # 最大回撤
    max_drawdown_duration: int = 0  # 最大回撤持续天数
    volatility: float = 0.0  # 波动率
    downside_deviation: float = 0.0  # 下行偏差
    
    # 风险调整收益指标
    sharpe_ratio: float = 0.0  # 夏普比率
    sortino_ratio: float = 0.0  # 索提诺比率
    calmar_ratio: float = 0.0  # 卡玛比率
    
    # 交易指标
    total_trades: int = 0  # 总交易次数
    winning_trades: int = 0  # 盈利交易次数
    losing_trades: int = 0  # 亏损交易次数
    win_rate: float = 0.0  # 胜率
    avg_win: float = 0.0  # 平均盈利
    avg_loss: float = 0.0  # 平均亏损
    profit_factor: float = 0.0  # 盈亏比
    
    # 持仓指标
    avg_holding_period: float = 0.0  # 平均持仓天数
    turnover_rate: float = 0.0  # 换手率
    
    def to_dict(self) -> Dict[str, float]:
        """转换为字典格式"""
        return {
            'total_return': self.total_return,
            'annualized_return': self.annualized_return,
            'daily_return_mean': self.daily_return_mean,
            'daily_return_std': self.daily_return_std,
            'max_drawdown': self.max_drawdown,
            'max_drawdown_duration': self.max_drawdown_duration,
            'volatility': self.volatility,
            'downside_deviation': self.downside_deviation,
            'sharpe_ratio': self.sharpe_ratio,
            'sortino_ratio': self.sortino_ratio,
            'calmar_ratio': self.calmar_ratio,
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'losing_trades': self.losing_trades,
            'win_rate': self.win_rate,
            'avg_win': self.avg_win,
            'avg_loss': self.avg_loss,
            'profit_factor': self.profit_factor,
            'avg_holding_period': self.avg_holding_period,
            'turnover_rate': self.turnover_rate
        }


@dataclass
class PositionAnalysis:
    """单个仓位分析结果"""
    symbol: str
    position_type: PositionType
    
    # 基本信息
    open_time: datetime
    close_time: Optional[datetime]
    holding_days: int
    
    # 收益信息
    total_return: float
    total_return_pct: float
    annualized_return_pct: float
    
    # 风险信息
    max_drawdown: float
    max_drawdown_pct: float
    volatility: float
    
    # 交易信息
    total_trades: int
    avg_trade_size: float
    total_commission: float
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'symbol': self.symbol,
            'position_type': self.position_type.value,
            'open_time': self.open_time.isoformat(),
            'close_time': self.close_time.isoformat() if self.close_time else None,
            'holding_days': self.holding_days,
            'total_return': self.total_return,
            'total_return_pct': self.total_return_pct,
            'annualized_return_pct': self.annualized_return_pct,
            'max_drawdown': self.max_drawdown,
            'max_drawdown_pct': self.max_drawdown_pct,
            'volatility': self.volatility,
            'total_trades': self.total_trades,
            'avg_trade_size': self.avg_trade_size,
            'total_commission': self.total_commission
        }


class PositionAnalyzer:
    """
    仓位分析器
    负责分析仓位历史和绩效
    """
    
    def __init__(self, position_manager: PositionManager):
        """
        初始化仓位分析器
        
        Args:
            position_manager: 仓位管理器
        """
        self.position_manager = position_manager
        self.logger = logging.getLogger(__name__)
        
        # 分析缓存
        self.daily_portfolio_values: List[Tuple[datetime, float]] = []
        self.daily_returns: List[float] = []
        self.analysis_cache: Dict[str, Any] = {}
        
        self.logger.info("仓位分析器初始化完成")
    
    def update_daily_portfolio_value(self, date: datetime, portfolio_value: float):
        """
        更新每日组合价值
        
        Args:
            date: 日期
            portfolio_value: 组合价值
        """
        self.daily_portfolio_values.append((date, portfolio_value))
        
        # 计算日收益率
        if len(self.daily_portfolio_values) >= 2:
            prev_value = self.daily_portfolio_values[-2][1]
            if prev_value > 0:
                daily_return = (portfolio_value - prev_value) / prev_value
                self.daily_returns.append(daily_return)
        
        # 保持最近1年的数据
        if len(self.daily_portfolio_values) > 365:
            self.daily_portfolio_values = self.daily_portfolio_values[-365:]
            self.daily_returns = self.daily_returns[-364:]
    
    def analyze_position(self, position: Position) -> PositionAnalysis:
        """
        分析单个仓位
        
        Args:
            position: 仓位对象
            
        Returns:
            仓位分析结果
        """
        # 计算持仓天数
        if position.close_time:
            holding_days = (position.close_time - position.open_time).days
        else:
            holding_days = (datetime.now() - position.open_time).days
        
        # 计算年化收益率
        if holding_days > 0:
            annualized_return_pct = position.unrealized_pnl_pct * (365 / holding_days)
        else:
            annualized_return_pct = 0.0
        
        # 计算平均交易规模
        if position.changes:
            trade_sizes = [abs(change.quantity_change * change.price) for change in position.changes]
            avg_trade_size = np.mean(trade_sizes) if trade_sizes else 0.0
        else:
            avg_trade_size = 0.0
        
        # 计算总手续费（简化计算）
        total_commission = avg_trade_size * 0.001 * position.total_trades  # 假设0.1%手续费
        
        # 计算波动率（基于价格变化）
        if len(position.changes) >= 2:
            price_changes = []
            for i in range(1, len(position.changes)):
                prev_price = position.changes[i-1].price
                curr_price = position.changes[i].price
                if prev_price > 0:
                    price_change = (curr_price - prev_price) / prev_price
                    price_changes.append(price_change)
            
            volatility = np.std(price_changes) * np.sqrt(252) if price_changes else 0.0
        else:
            volatility = 0.0
        
        return PositionAnalysis(
            symbol=position.symbol,
            position_type=position.position_type,
            open_time=position.open_time,
            close_time=position.close_time,
            holding_days=holding_days,
            total_return=position.unrealized_pnl + position.realized_pnl,
            total_return_pct=position.unrealized_pnl_pct + position.realized_pnl_pct,
            annualized_return_pct=annualized_return_pct,
            max_drawdown=position.max_drawdown,
            max_drawdown_pct=position.max_drawdown_pct,
            volatility=volatility,
            total_trades=position.total_trades,
            avg_trade_size=avg_trade_size,
            total_commission=total_commission
        )
    
    def calculate_portfolio_performance(self, start_date: Optional[datetime] = None,
                                      end_date: Optional[datetime] = None) -> PerformanceMetrics:
        """
        计算组合绩效指标
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            绩效指标
        """
        if not self.daily_returns:
            return PerformanceMetrics()
        
        # 过滤日期范围
        returns = self.daily_returns.copy()
        if start_date or end_date:
            filtered_returns = []
            for i, (date, _) in enumerate(self.daily_portfolio_values[1:]):  # 跳过第一个，因为没有收益率
                if start_date and date < start_date:
                    continue
                if end_date and date > end_date:
                    break
                if i < len(returns):
                    filtered_returns.append(returns[i])
            returns = filtered_returns
        
        if not returns:
            return PerformanceMetrics()
        
        returns_array = np.array(returns)
        
        # 基本统计
        total_return = np.prod(1 + returns_array) - 1
        annualized_return = (1 + total_return) ** (252 / len(returns)) - 1 if len(returns) > 0 else 0
        daily_return_mean = np.mean(returns_array)
        daily_return_std = np.std(returns_array)
        volatility = daily_return_std * np.sqrt(252)
        
        # 计算最大回撤
        cumulative_returns = np.cumprod(1 + returns_array)
        running_max = np.maximum.accumulate(cumulative_returns)
        drawdowns = (cumulative_returns - running_max) / running_max
        max_drawdown = np.min(drawdowns)
        
        # 计算最大回撤持续天数
        max_drawdown_duration = 0
        current_duration = 0
        for dd in drawdowns:
            if dd < 0:
                current_duration += 1
                max_drawdown_duration = max(max_drawdown_duration, current_duration)
            else:
                current_duration = 0
        
        # 下行偏差
        negative_returns = returns_array[returns_array < 0]
        downside_deviation = np.std(negative_returns) * np.sqrt(252) if len(negative_returns) > 0 else 0
        
        # 风险调整收益指标
        risk_free_rate = 0.03  # 假设无风险利率3%
        excess_return = annualized_return - risk_free_rate
        
        sharpe_ratio = excess_return / volatility if volatility > 0 else 0
        sortino_ratio = excess_return / downside_deviation if downside_deviation > 0 else 0
        calmar_ratio = annualized_return / abs(max_drawdown) if max_drawdown < 0 else 0
        
        # 交易统计
        positions = self.position_manager.get_all_positions(active_only=False)
        positions.extend(self.position_manager.position_history)
        
        total_trades = sum(p.total_trades for p in positions)
        winning_trades = sum(p.winning_trades for p in positions)
        losing_trades = sum(p.losing_trades for p in positions)
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        # 计算平均盈利和亏损
        all_trades = []
        for position in positions:
            for change in position.changes:
                if change.change_type in ['DECREASE', 'CLOSE']:
                    # 这是一个卖出交易，计算盈亏
                    cost_per_share = position.avg_cost
                    pnl_per_share = change.price - cost_per_share
                    trade_pnl = pnl_per_share * abs(change.quantity_change)
                    all_trades.append(trade_pnl)
        
        if all_trades:
            winning_trades_pnl = [t for t in all_trades if t > 0]
            losing_trades_pnl = [t for t in all_trades if t < 0]
            
            avg_win = np.mean(winning_trades_pnl) if winning_trades_pnl else 0
            avg_loss = np.mean(losing_trades_pnl) if losing_trades_pnl else 0
            
            total_wins = sum(winning_trades_pnl) if winning_trades_pnl else 0
            total_losses = abs(sum(losing_trades_pnl)) if losing_trades_pnl else 0
            profit_factor = total_wins / total_losses if total_losses > 0 else 0
        else:
            avg_win = 0
            avg_loss = 0
            profit_factor = 0
        
        # 持仓统计
        holding_periods = []
        for position in positions:
            if position.close_time:
                holding_days = (position.close_time - position.open_time).days
                holding_periods.append(holding_days)
        
        avg_holding_period = np.mean(holding_periods) if holding_periods else 0
        
        # 换手率（简化计算）
        if len(self.daily_portfolio_values) >= 2:
            total_trade_value = sum(abs(change.quantity_change * change.price) 
                                  for position in positions 
                                  for change in position.changes)
            avg_portfolio_value = np.mean([v for _, v in self.daily_portfolio_values])
            turnover_rate = total_trade_value / avg_portfolio_value if avg_portfolio_value > 0 else 0
        else:
            turnover_rate = 0
        
        return PerformanceMetrics(
            total_return=total_return,
            annualized_return=annualized_return,
            daily_return_mean=daily_return_mean,
            daily_return_std=daily_return_std,
            max_drawdown=max_drawdown,
            max_drawdown_duration=max_drawdown_duration,
            volatility=volatility,
            downside_deviation=downside_deviation,
            sharpe_ratio=sharpe_ratio,
            sortino_ratio=sortino_ratio,
            calmar_ratio=calmar_ratio,
            total_trades=total_trades,
            winning_trades=winning_trades,
            losing_trades=losing_trades,
            win_rate=win_rate,
            avg_win=avg_win,
            avg_loss=avg_loss,
            profit_factor=profit_factor,
            avg_holding_period=avg_holding_period,
            turnover_rate=turnover_rate
        )
    
    def get_position_changes_analysis(self, symbol: Optional[str] = None,
                                    start_date: Optional[datetime] = None,
                                    end_date: Optional[datetime] = None) -> Dict[str, Any]:
        """
        分析仓位变化
        
        Args:
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            仓位变化分析结果
        """
        changes = self.position_manager.get_position_changes(symbol, start_date, end_date)
        
        if not changes:
            return {
                'total_changes': 0,
                'change_types': {},
                'daily_activity': {},
                'largest_trades': []
            }
        
        # 按变化类型统计
        change_types = {}
        for change in changes:
            change_types[change.change_type] = change_types.get(change.change_type, 0) + 1
        
        # 按日期统计活动
        daily_activity = {}
        for change in changes:
            date_str = change.timestamp.strftime('%Y-%m-%d')
            if date_str not in daily_activity:
                daily_activity[date_str] = {'count': 0, 'volume': 0}
            daily_activity[date_str]['count'] += 1
            daily_activity[date_str]['volume'] += abs(change.quantity_change * change.price)
        
        # 找出最大的交易
        largest_trades = sorted(changes, 
                              key=lambda x: abs(x.quantity_change * x.price), 
                              reverse=True)[:10]
        
        return {
            'total_changes': len(changes),
            'change_types': change_types,
            'daily_activity': daily_activity,
            'largest_trades': [
                {
                    'symbol': trade.symbol,
                    'change_type': trade.change_type,
                    'quantity_change': trade.quantity_change,
                    'price': trade.price,
                    'value': abs(trade.quantity_change * trade.price),
                    'timestamp': trade.timestamp.isoformat(),
                    'reason': trade.reason
                }
                for trade in largest_trades
            ]
        }
    
    def get_sector_analysis(self) -> Dict[str, Any]:
        """
        获取行业分析
        
        Returns:
            行业分析结果
        """
        positions = self.position_manager.get_all_positions()
        
        if not positions:
            return {
                'sector_allocation': {},
                'sector_performance': {},
                'sector_risk': {}
            }
        
        # 按行业分组
        sector_positions = {}
        for position in positions:
            sector = position.metadata.get('sector', 'Unknown')
            if sector not in sector_positions:
                sector_positions[sector] = []
            sector_positions[sector].append(position)
        
        # 计算行业配置
        total_value = sum(p.market_value for p in positions)
        sector_allocation = {}
        sector_performance = {}
        sector_risk = {}
        
        for sector, sector_pos in sector_positions.items():
            sector_value = sum(p.market_value for p in sector_pos)
            sector_allocation[sector] = sector_value / total_value if total_value > 0 else 0
            
            # 行业绩效
            sector_pnl = sum(p.unrealized_pnl + p.realized_pnl for p in sector_pos)
            sector_cost = sum(p.total_cost for p in sector_pos)
            sector_performance[sector] = {
                'total_pnl': sector_pnl,
                'pnl_pct': sector_pnl / sector_cost if sector_cost > 0 else 0,
                'position_count': len(sector_pos)
            }
            
            # 行业风险
            sector_drawdown = max(p.max_drawdown_pct for p in sector_pos) if sector_pos else 0
            sector_risk[sector] = {
                'max_drawdown_pct': sector_drawdown,
                'concentration': sector_allocation[sector]
            }
        
        return {
            'sector_allocation': sector_allocation,
            'sector_performance': sector_performance,
            'sector_risk': sector_risk
        }
    
    def generate_performance_report(self, start_date: Optional[datetime] = None,
                                  end_date: Optional[datetime] = None) -> Dict[str, Any]:
        """
        生成绩效报告
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            绩效报告
        """
        # 计算组合绩效
        portfolio_performance = self.calculate_portfolio_performance(start_date, end_date)
        
        # 分析所有仓位
        positions = self.position_manager.get_all_positions(active_only=False)
        positions.extend(self.position_manager.position_history)
        
        position_analyses = []
        for position in positions:
            if start_date and position.open_time < start_date:
                continue
            if end_date and position.open_time > end_date:
                continue
            position_analyses.append(self.analyze_position(position))
        
        # 获取当前组合状态
        current_summary = self.position_manager.get_portfolio_summary()
        
        # 获取仓位变化分析
        changes_analysis = self.get_position_changes_analysis(None, start_date, end_date)
        
        # 获取行业分析
        sector_analysis = self.get_sector_analysis()
        
        return {
            'report_period': {
                'start_date': start_date.isoformat() if start_date else None,
                'end_date': end_date.isoformat() if end_date else None,
                'generated_at': datetime.now().isoformat()
            },
            'portfolio_performance': portfolio_performance.to_dict(),
            'current_portfolio': current_summary.to_dict(),
            'position_analyses': [analysis.to_dict() for analysis in position_analyses],
            'changes_analysis': changes_analysis,
            'sector_analysis': sector_analysis,
            'summary_statistics': {
                'total_positions_analyzed': len(position_analyses),
                'active_positions': current_summary.active_position_count,
                'total_market_value': current_summary.total_market_value,
                'total_pnl': current_summary.total_pnl,
                'total_pnl_pct': current_summary.total_pnl_pct
            }
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'daily_portfolio_values_count': len(self.daily_portfolio_values),
            'daily_returns_count': len(self.daily_returns),
            'latest_performance': self.calculate_portfolio_performance().to_dict(),
            'sector_analysis': self.get_sector_analysis()
        }