"""
仓位管理数据模型
Position Management Data Models
"""

from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any, List
import uuid


class PositionType(Enum):
    """仓位类型枚举"""
    LONG = "LONG"      # 多头仓位
    SHORT = "SHORT"    # 空头仓位
    BASE = "BASE"      # 底仓（长期持有）
    T_PLUS_ZERO = "T_PLUS_ZERO"  # T+0仓位（日内交易）


class PositionStatus(Enum):
    """仓位状态枚举"""
    ACTIVE = "ACTIVE"        # 活跃仓位
    CLOSED = "CLOSED"        # 已平仓
    SUSPENDED = "SUSPENDED"  # 暂停交易
    LIQUIDATING = "LIQUIDATING"  # 清仓中


@dataclass
class PositionChange:
    """仓位变化记录"""
    change_id: str
    position_id: str
    symbol: str
    change_type: str  # 'OPEN', 'INCREASE', 'DECREASE', 'CLOSE'
    quantity_change: int  # 数量变化（正数为增加，负数为减少）
    price: float
    timestamp: datetime
    order_id: Optional[str] = None
    reason: str = ""
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.change_id:
            self.change_id = str(uuid.uuid4())


@dataclass
class Position:
    """仓位模型"""
    # 基本信息
    position_id: str
    symbol: str
    position_type: PositionType
    status: PositionStatus = PositionStatus.ACTIVE
    
    # 数量信息
    quantity: int = 0  # 当前持仓数量
    available_quantity: int = 0  # 可用数量（扣除冻结部分）
    frozen_quantity: int = 0  # 冻结数量
    
    # 成本信息
    avg_cost: float = 0.0  # 平均成本价
    total_cost: float = 0.0  # 总成本
    
    # 市值信息
    current_price: float = 0.0  # 当前价格
    market_value: float = 0.0  # 市值
    unrealized_pnl: float = 0.0  # 未实现盈亏
    unrealized_pnl_pct: float = 0.0  # 未实现盈亏百分比
    
    # 已实现盈亏
    realized_pnl: float = 0.0  # 已实现盈亏
    realized_pnl_pct: float = 0.0  # 已实现盈亏百分比
    
    # 时间信息
    open_time: datetime = field(default_factory=datetime.now)
    update_time: datetime = field(default_factory=datetime.now)
    close_time: Optional[datetime] = None
    
    # 风险信息
    max_drawdown: float = 0.0  # 最大回撤
    max_drawdown_pct: float = 0.0  # 最大回撤百分比
    high_water_mark: float = 0.0  # 历史最高点
    
    # 交易统计
    total_trades: int = 0  # 总交易次数
    winning_trades: int = 0  # 盈利交易次数
    losing_trades: int = 0  # 亏损交易次数
    
    # 变化历史
    changes: List[PositionChange] = field(default_factory=list)
    
    # 其他信息
    tags: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.position_id:
            self.position_id = str(uuid.uuid4())
        
        # 初始化可用数量
        if self.available_quantity == 0:
            self.available_quantity = self.quantity
    
    @property
    def is_long(self) -> bool:
        """是否为多头仓位"""
        return self.position_type in [PositionType.LONG, PositionType.BASE, PositionType.T_PLUS_ZERO]
    
    @property
    def is_short(self) -> bool:
        """是否为空头仓位"""
        return self.position_type == PositionType.SHORT
    
    @property
    def is_base_position(self) -> bool:
        """是否为底仓"""
        return self.position_type == PositionType.BASE
    
    @property
    def is_t_position(self) -> bool:
        """是否为T+0仓位"""
        return self.position_type == PositionType.T_PLUS_ZERO
    
    @property
    def is_active(self) -> bool:
        """仓位是否活跃"""
        return self.status == PositionStatus.ACTIVE and self.quantity > 0
    
    @property
    def win_rate(self) -> float:
        """胜率"""
        if self.total_trades == 0:
            return 0.0
        return self.winning_trades / self.total_trades
    
    def update_price(self, new_price: float) -> None:
        """更新当前价格并重新计算相关指标"""
        self.current_price = new_price
        self.market_value = self.quantity * new_price
        
        # 计算未实现盈亏
        if self.quantity > 0:
            self.unrealized_pnl = self.market_value - self.total_cost
            if self.total_cost > 0:
                self.unrealized_pnl_pct = self.unrealized_pnl / self.total_cost
        
        # 更新最高水位和最大回撤
        if self.market_value > self.high_water_mark:
            self.high_water_mark = self.market_value
        
        if self.high_water_mark > 0:
            current_drawdown = self.high_water_mark - self.market_value
            current_drawdown_pct = current_drawdown / self.high_water_mark
            
            if current_drawdown > self.max_drawdown:
                self.max_drawdown = current_drawdown
                self.max_drawdown_pct = current_drawdown_pct
        
        self.update_time = datetime.now()
    
    def add_quantity(self, quantity: int, price: float, order_id: Optional[str] = None, reason: str = "") -> None:
        """增加仓位数量"""
        if quantity <= 0:
            return
        
        # 记录变化
        change = PositionChange(
            change_id="",
            position_id=self.position_id,
            symbol=self.symbol,
            change_type="INCREASE" if self.quantity > 0 else "OPEN",
            quantity_change=quantity,
            price=price,
            timestamp=datetime.now(),
            order_id=order_id,
            reason=reason
        )
        self.changes.append(change)
        
        # 更新成本信息
        old_total_cost = self.total_cost
        new_cost = quantity * price
        self.total_cost += new_cost
        
        # 更新数量
        self.quantity += quantity
        self.available_quantity += quantity
        
        # 重新计算平均成本
        if self.quantity > 0:
            self.avg_cost = self.total_cost / self.quantity
        
        # 更新市值和盈亏
        self.update_price(self.current_price if self.current_price > 0 else price)
        
        # 更新交易统计
        self.total_trades += 1
        
        self.update_time = datetime.now()
    
    def reduce_quantity(self, quantity: int, price: float, order_id: Optional[str] = None, reason: str = "") -> bool:
        """减少仓位数量"""
        if quantity <= 0 or quantity > self.available_quantity:
            return False
        
        # 记录变化
        change = PositionChange(
            change_id="",
            position_id=self.position_id,
            symbol=self.symbol,
            change_type="CLOSE" if quantity == self.quantity else "DECREASE",
            quantity_change=-quantity,
            price=price,
            timestamp=datetime.now(),
            order_id=order_id,
            reason=reason
        )
        self.changes.append(change)
        
        # 计算已实现盈亏
        cost_per_share = self.avg_cost
        realized_pnl_this_trade = (price - cost_per_share) * quantity
        self.realized_pnl += realized_pnl_this_trade
        
        if self.total_cost > 0:
            self.realized_pnl_pct = self.realized_pnl / self.total_cost
        
        # 更新成本信息
        cost_reduction = cost_per_share * quantity
        self.total_cost -= cost_reduction
        
        # 更新数量
        self.quantity -= quantity
        self.available_quantity -= quantity
        
        # 更新市值和盈亏
        self.update_price(price)
        
        # 更新交易统计
        self.total_trades += 1
        if realized_pnl_this_trade > 0:
            self.winning_trades += 1
        else:
            self.losing_trades += 1
        
        # 如果仓位清空，更新状态
        if self.quantity == 0:
            self.status = PositionStatus.CLOSED
            self.close_time = datetime.now()
        
        self.update_time = datetime.now()
        return True
    
    def freeze_quantity(self, quantity: int) -> bool:
        """冻结指定数量的仓位"""
        if quantity <= 0 or quantity > self.available_quantity:
            return False
        
        self.available_quantity -= quantity
        self.frozen_quantity += quantity
        self.update_time = datetime.now()
        return True
    
    def unfreeze_quantity(self, quantity: int) -> bool:
        """解冻指定数量的仓位"""
        if quantity <= 0 or quantity > self.frozen_quantity:
            return False
        
        self.frozen_quantity -= quantity
        self.available_quantity += quantity
        self.update_time = datetime.now()
        return True
    
    def close_position(self, price: float, order_id: Optional[str] = None, reason: str = "手动平仓") -> bool:
        """平仓"""
        if self.quantity <= 0:
            return False
        
        return self.reduce_quantity(self.quantity, price, order_id, reason)
    
    def get_risk_metrics(self) -> Dict[str, float]:
        """获取风险指标"""
        return {
            'unrealized_pnl': self.unrealized_pnl,
            'unrealized_pnl_pct': self.unrealized_pnl_pct,
            'realized_pnl': self.realized_pnl,
            'realized_pnl_pct': self.realized_pnl_pct,
            'max_drawdown': self.max_drawdown,
            'max_drawdown_pct': self.max_drawdown_pct,
            'win_rate': self.win_rate,
            'market_value': self.market_value,
            'total_cost': self.total_cost
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'position_id': self.position_id,
            'symbol': self.symbol,
            'position_type': self.position_type.value,
            'status': self.status.value,
            'quantity': self.quantity,
            'available_quantity': self.available_quantity,
            'frozen_quantity': self.frozen_quantity,
            'avg_cost': self.avg_cost,
            'total_cost': self.total_cost,
            'current_price': self.current_price,
            'market_value': self.market_value,
            'unrealized_pnl': self.unrealized_pnl,
            'unrealized_pnl_pct': self.unrealized_pnl_pct,
            'realized_pnl': self.realized_pnl,
            'realized_pnl_pct': self.realized_pnl_pct,
            'open_time': self.open_time.isoformat(),
            'update_time': self.update_time.isoformat(),
            'close_time': self.close_time.isoformat() if self.close_time else None,
            'max_drawdown': self.max_drawdown,
            'max_drawdown_pct': self.max_drawdown_pct,
            'high_water_mark': self.high_water_mark,
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'losing_trades': self.losing_trades,
            'win_rate': self.win_rate,
            'changes': [change.__dict__ for change in self.changes],
            'tags': self.tags,
            'metadata': self.metadata,
            'risk_metrics': self.get_risk_metrics()
        }


@dataclass
class PortfolioSummary:
    """投资组合汇总信息"""
    total_market_value: float = 0.0  # 总市值
    total_cost: float = 0.0  # 总成本
    total_unrealized_pnl: float = 0.0  # 总未实现盈亏
    total_realized_pnl: float = 0.0  # 总已实现盈亏
    total_pnl: float = 0.0  # 总盈亏
    total_pnl_pct: float = 0.0  # 总盈亏百分比
    position_count: int = 0  # 持仓数量
    active_position_count: int = 0  # 活跃持仓数量
    max_drawdown: float = 0.0  # 最大回撤
    max_drawdown_pct: float = 0.0  # 最大回撤百分比
    update_time: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'total_market_value': self.total_market_value,
            'total_cost': self.total_cost,
            'total_unrealized_pnl': self.total_unrealized_pnl,
            'total_realized_pnl': self.total_realized_pnl,
            'total_pnl': self.total_pnl,
            'total_pnl_pct': self.total_pnl_pct,
            'position_count': self.position_count,
            'active_position_count': self.active_position_count,
            'max_drawdown': self.max_drawdown,
            'max_drawdown_pct': self.max_drawdown_pct,
            'update_time': self.update_time.isoformat()
        }