"""
仓位优化和再平衡算法
Position Optimizer and Rebalancer
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import numpy as np

from .models import Position, PositionType, PortfolioSummary
from .manager import PositionManager


class OptimizationObjective(Enum):
    """优化目标枚举"""
    MAX_RETURN = "MAX_RETURN"          # 最大化收益
    MIN_RISK = "MIN_RISK"              # 最小化风险
    MAX_SHARPE = "MAX_SHARPE"          # 最大化夏普比率
    RISK_PARITY = "RISK_PARITY"       # 风险平价
    EQUAL_WEIGHT = "EQUAL_WEIGHT"     # 等权重


class RebalanceReason(Enum):
    """再平衡原因枚举"""
    SCHEDULED = "SCHEDULED"            # 定期再平衡
    DRIFT_THRESHOLD = "DRIFT_THRESHOLD"  # 偏离阈值
    RISK_CONTROL = "RISK_CONTROL"      # 风险控制
    SIGNAL_CHANGE = "SIGNAL_CHANGE"    # 信号变化
    MANUAL = "MANUAL"                  # 手动触发


@dataclass
class OptimizationConfig:
    """优化配置"""
    # 基本参数
    objective: OptimizationObjective = OptimizationObjective.MAX_SHARPE
    max_positions: int = 10  # 最大持仓数量
    min_position_weight: float = 0.05  # 最小仓位权重5%
    max_position_weight: float = 0.30  # 最大仓位权重30%
    
    # 风险参数
    max_portfolio_volatility: float = 0.20  # 最大组合波动率20%
    max_tracking_error: float = 0.05  # 最大跟踪误差5%
    
    # 交易成本参数
    transaction_cost_pct: float = 0.001  # 交易成本0.1%
    min_trade_amount: float = 1000  # 最小交易金额
    
    # 再平衡参数
    rebalance_threshold: float = 0.05  # 再平衡阈值5%
    rebalance_frequency_days: int = 7  # 再平衡频率（天）
    
    # 小资金模式特殊配置
    small_capital_mode: bool = False  # 小资金模式
    single_stock_focus: bool = False  # 单股集中投资
    t_plus_zero_enabled: bool = True  # 启用T+0策略


@dataclass
class OptimizationResult:
    """优化结果"""
    target_weights: Dict[str, float]  # 目标权重
    current_weights: Dict[str, float]  # 当前权重
    weight_changes: Dict[str, float]  # 权重变化
    expected_return: float  # 预期收益率
    expected_risk: float  # 预期风险
    sharpe_ratio: float  # 夏普比率
    optimization_time: datetime
    reason: str = ""


@dataclass
class RebalanceAction:
    """再平衡动作"""
    symbol: str
    action_type: str  # 'BUY', 'SELL', 'HOLD'
    current_quantity: int
    target_quantity: int
    quantity_change: int
    current_weight: float
    target_weight: float
    weight_change: float
    estimated_cost: float
    priority: int = 1  # 优先级（1-5）


class PositionOptimizer:
    """
    仓位优化器
    负责仓位优化和再平衡算法
    """
    
    def __init__(self, position_manager: PositionManager, config: Optional[OptimizationConfig] = None):
        """
        初始化仓位优化器
        
        Args:
            position_manager: 仓位管理器
            config: 优化配置
        """
        self.position_manager = position_manager
        self.config = config or OptimizationConfig()
        self.logger = logging.getLogger(__name__)
        
        # 历史数据缓存
        self.price_history: Dict[str, List[float]] = {}
        self.return_history: Dict[str, List[float]] = {}
        
        # 优化历史
        self.optimization_history: List[OptimizationResult] = []
        self.last_rebalance_time: Optional[datetime] = None
        
        self.logger.info("仓位优化器初始化完成")
    
    def update_price_history(self, symbol: str, price: float):
        """
        更新价格历史
        
        Args:
            symbol: 股票代码
            price: 价格
        """
        if symbol not in self.price_history:
            self.price_history[symbol] = []
        
        self.price_history[symbol].append(price)
        
        # 保持最近252个交易日的数据（约1年）
        if len(self.price_history[symbol]) > 252:
            self.price_history[symbol] = self.price_history[symbol][-252:]
        
        # 计算收益率
        if len(self.price_history[symbol]) >= 2:
            if symbol not in self.return_history:
                self.return_history[symbol] = []
            
            returns = np.diff(self.price_history[symbol]) / self.price_history[symbol][:-1]
            self.return_history[symbol] = returns.tolist()
    
    def calculate_expected_returns(self, symbols: List[str]) -> Dict[str, float]:
        """
        计算预期收益率
        
        Args:
            symbols: 股票代码列表
            
        Returns:
            预期收益率字典
        """
        expected_returns = {}
        
        for symbol in symbols:
            if symbol in self.return_history and len(self.return_history[symbol]) > 0:
                # 使用历史平均收益率作为预期收益率
                returns = np.array(self.return_history[symbol])
                expected_returns[symbol] = np.mean(returns) * 252  # 年化收益率
            else:
                # 默认预期收益率
                expected_returns[symbol] = 0.10  # 10%年化收益率
        
        return expected_returns
    
    def calculate_covariance_matrix(self, symbols: List[str]) -> np.ndarray:
        """
        计算协方差矩阵
        
        Args:
            symbols: 股票代码列表
            
        Returns:
            协方差矩阵
        """
        n = len(symbols)
        cov_matrix = np.eye(n) * 0.04  # 默认方差0.04（20%波动率）
        
        # 如果有足够的历史数据，计算实际协方差矩阵
        returns_data = []
        for symbol in symbols:
            if symbol in self.return_history and len(self.return_history[symbol]) > 20:
                returns_data.append(self.return_history[symbol][-60:])  # 最近60个交易日
            else:
                # 使用模拟数据
                returns_data.append(np.random.normal(0, 0.02, 60).tolist())
        
        if len(returns_data) == n:
            returns_matrix = np.array(returns_data).T
            cov_matrix = np.cov(returns_matrix.T) * 252  # 年化协方差矩阵
        
        return cov_matrix
    
    def optimize_portfolio_weights(self, symbols: List[str], current_weights: Dict[str, float]) -> OptimizationResult:
        """
        优化投资组合权重
        
        Args:
            symbols: 股票代码列表
            current_weights: 当前权重
            
        Returns:
            优化结果
        """
        if not symbols:
            return OptimizationResult(
                target_weights={},
                current_weights=current_weights,
                weight_changes={},
                expected_return=0.0,
                expected_risk=0.0,
                sharpe_ratio=0.0,
                optimization_time=datetime.now(),
                reason="无可优化标的"
            )
        
        # 获取预期收益率
        expected_returns = self.calculate_expected_returns(symbols)
        
        # 计算协方差矩阵
        cov_matrix = self.calculate_covariance_matrix(symbols)
        
        # 根据优化目标计算权重
        if self.config.objective == OptimizationObjective.EQUAL_WEIGHT:
            target_weights = self._equal_weight_optimization(symbols)
        elif self.config.objective == OptimizationObjective.MAX_RETURN:
            target_weights = self._max_return_optimization(symbols, expected_returns)
        elif self.config.objective == OptimizationObjective.MIN_RISK:
            target_weights = self._min_risk_optimization(symbols, cov_matrix)
        elif self.config.objective == OptimizationObjective.MAX_SHARPE:
            target_weights = self._max_sharpe_optimization(symbols, expected_returns, cov_matrix)
        else:
            target_weights = self._equal_weight_optimization(symbols)
        
        # 应用约束条件
        target_weights = self._apply_constraints(target_weights)
        
        # 计算权重变化
        weight_changes = {}
        for symbol in symbols:
            current_weight = current_weights.get(symbol, 0.0)
            target_weight = target_weights.get(symbol, 0.0)
            weight_changes[symbol] = target_weight - current_weight
        
        # 计算预期收益和风险
        expected_return = sum(expected_returns[symbol] * target_weights.get(symbol, 0) for symbol in symbols)
        
        # 计算组合风险
        weights_array = np.array([target_weights.get(symbol, 0) for symbol in symbols])
        expected_risk = np.sqrt(np.dot(weights_array, np.dot(cov_matrix, weights_array)))
        
        # 计算夏普比率
        risk_free_rate = 0.03  # 假设无风险利率3%
        sharpe_ratio = (expected_return - risk_free_rate) / expected_risk if expected_risk > 0 else 0
        
        result = OptimizationResult(
            target_weights=target_weights,
            current_weights=current_weights,
            weight_changes=weight_changes,
            expected_return=expected_return,
            expected_risk=expected_risk,
            sharpe_ratio=sharpe_ratio,
            optimization_time=datetime.now(),
            reason=f"优化目标: {self.config.objective.value}"
        )
        
        self.optimization_history.append(result)
        return result
    
    def _equal_weight_optimization(self, symbols: List[str]) -> Dict[str, float]:
        """等权重优化"""
        if not symbols:
            return {}
        
        weight = 1.0 / len(symbols)
        return {symbol: weight for symbol in symbols}
    
    def _max_return_optimization(self, symbols: List[str], expected_returns: Dict[str, float]) -> Dict[str, float]:
        """最大收益优化"""
        if not symbols:
            return {}
        
        # 选择预期收益率最高的股票
        sorted_symbols = sorted(symbols, key=lambda x: expected_returns.get(x, 0), reverse=True)
        
        if self.config.single_stock_focus:
            # 单股集中投资模式
            return {sorted_symbols[0]: 1.0}
        else:
            # 按收益率加权
            total_return = sum(max(expected_returns.get(symbol, 0), 0) for symbol in symbols)
            if total_return == 0:
                return self._equal_weight_optimization(symbols)
            
            weights = {}
            for symbol in symbols:
                weight = max(expected_returns.get(symbol, 0), 0) / total_return
                weights[symbol] = weight
            
            return weights
    
    def _min_risk_optimization(self, symbols: List[str], cov_matrix: np.ndarray) -> Dict[str, float]:
        """最小风险优化"""
        if not symbols or cov_matrix.size == 0:
            return self._equal_weight_optimization(symbols)
        
        try:
            # 最小方差组合
            n = len(symbols)
            ones = np.ones((n, 1))
            inv_cov = np.linalg.inv(cov_matrix)
            
            # 计算最小方差权重
            weights = np.dot(inv_cov, ones) / np.dot(ones.T, np.dot(inv_cov, ones))
            weights = weights.flatten()
            
            # 确保权重为正
            weights = np.maximum(weights, 0)
            weights = weights / np.sum(weights)
            
            return {symbols[i]: weights[i] for i in range(len(symbols))}
            
        except Exception as e:
            self.logger.warning(f"最小风险优化失败，使用等权重: {e}")
            return self._equal_weight_optimization(symbols)
    
    def _max_sharpe_optimization(self, symbols: List[str], expected_returns: Dict[str, float], 
                                cov_matrix: np.ndarray) -> Dict[str, float]:
        """最大夏普比率优化"""
        if not symbols or cov_matrix.size == 0:
            return self._equal_weight_optimization(symbols)
        
        try:
            # 构建收益率向量
            returns_vector = np.array([expected_returns.get(symbol, 0) for symbol in symbols])
            
            # 无风险利率
            risk_free_rate = 0.03
            excess_returns = returns_vector - risk_free_rate
            
            # 计算最优权重
            inv_cov = np.linalg.inv(cov_matrix)
            weights = np.dot(inv_cov, excess_returns)
            
            # 归一化权重
            if np.sum(weights) != 0:
                weights = weights / np.sum(weights)
            else:
                weights = np.ones(len(symbols)) / len(symbols)
            
            # 确保权重为正
            weights = np.maximum(weights, 0)
            weights = weights / np.sum(weights)
            
            return {symbols[i]: weights[i] for i in range(len(symbols))}
            
        except Exception as e:
            self.logger.warning(f"最大夏普比率优化失败，使用等权重: {e}")
            return self._equal_weight_optimization(symbols)
    
    def _apply_constraints(self, weights: Dict[str, float]) -> Dict[str, float]:
        """
        应用约束条件
        
        Args:
            weights: 原始权重
            
        Returns:
            约束后的权重
        """
        if not weights:
            return weights
        
        # 应用最小和最大权重约束
        constrained_weights = {}
        for symbol, weight in weights.items():
            if weight < self.config.min_position_weight:
                constrained_weights[symbol] = 0.0
            elif weight > self.config.max_position_weight:
                constrained_weights[symbol] = self.config.max_position_weight
            else:
                constrained_weights[symbol] = weight
        
        # 重新归一化
        total_weight = sum(constrained_weights.values())
        if total_weight > 0:
            constrained_weights = {symbol: weight / total_weight 
                                 for symbol, weight in constrained_weights.items()}
        
        # 限制持仓数量
        if len(constrained_weights) > self.config.max_positions:
            # 保留权重最大的股票
            sorted_items = sorted(constrained_weights.items(), key=lambda x: x[1], reverse=True)
            constrained_weights = dict(sorted_items[:self.config.max_positions])
            
            # 重新归一化
            total_weight = sum(constrained_weights.values())
            if total_weight > 0:
                constrained_weights = {symbol: weight / total_weight 
                                     for symbol, weight in constrained_weights.items()}
        
        return constrained_weights
    
    def check_rebalance_needed(self) -> Tuple[bool, RebalanceReason]:
        """
        检查是否需要再平衡
        
        Returns:
            (是否需要再平衡, 再平衡原因)
        """
        # 检查定期再平衡
        if self.last_rebalance_time is None:
            return True, RebalanceReason.SCHEDULED
        
        days_since_rebalance = (datetime.now() - self.last_rebalance_time).days
        if days_since_rebalance >= self.config.rebalance_frequency_days:
            return True, RebalanceReason.SCHEDULED
        
        # 检查权重偏离
        positions = self.position_manager.get_all_positions()
        if not positions:
            return False, RebalanceReason.SCHEDULED
        
        # 计算当前权重
        total_value = sum(p.market_value for p in positions)
        if total_value <= 0:
            return False, RebalanceReason.SCHEDULED
        
        current_weights = {p.symbol: p.market_value / total_value for p in positions}
        
        # 获取目标权重（使用最近的优化结果）
        if not self.optimization_history:
            return True, RebalanceReason.SCHEDULED
        
        target_weights = self.optimization_history[-1].target_weights
        
        # 检查权重偏离
        max_drift = 0.0
        for symbol in set(list(current_weights.keys()) + list(target_weights.keys())):
            current_weight = current_weights.get(symbol, 0.0)
            target_weight = target_weights.get(symbol, 0.0)
            drift = abs(current_weight - target_weight)
            max_drift = max(max_drift, drift)
        
        if max_drift > self.config.rebalance_threshold:
            return True, RebalanceReason.DRIFT_THRESHOLD
        
        return False, RebalanceReason.SCHEDULED
    
    def generate_rebalance_actions(self, optimization_result: OptimizationResult, 
                                 total_capital: float) -> List[RebalanceAction]:
        """
        生成再平衡动作
        
        Args:
            optimization_result: 优化结果
            total_capital: 总资本
            
        Returns:
            再平衡动作列表
        """
        actions = []
        positions = self.position_manager.get_all_positions()
        
        # 计算当前持仓
        current_holdings = {p.symbol: p.quantity for p in positions}
        current_prices = {p.symbol: p.current_price for p in positions}
        
        # 为每个目标股票生成动作
        for symbol, target_weight in optimization_result.target_weights.items():
            if target_weight <= 0:
                continue
            
            # 获取当前价格
            current_price = current_prices.get(symbol, 0.0)
            if current_price <= 0:
                # 如果没有当前价格，跳过
                continue
            
            # 计算目标数量
            target_value = total_capital * target_weight
            target_quantity = int(target_value / current_price / 100) * 100  # 整手
            
            # 计算当前数量
            current_quantity = current_holdings.get(symbol, 0)
            
            # 计算变化
            quantity_change = target_quantity - current_quantity
            
            if abs(quantity_change) < 100:  # 小于1手的变化忽略
                continue
            
            # 确定动作类型
            if quantity_change > 0:
                action_type = "BUY"
            elif quantity_change < 0:
                action_type = "SELL"
            else:
                action_type = "HOLD"
            
            # 计算权重
            current_weight = optimization_result.current_weights.get(symbol, 0.0)
            weight_change = target_weight - current_weight
            
            # 估算交易成本
            trade_value = abs(quantity_change) * current_price
            estimated_cost = trade_value * self.config.transaction_cost_pct
            
            # 确定优先级
            priority = 1
            if abs(weight_change) > 0.10:  # 权重变化超过10%
                priority = 5
            elif abs(weight_change) > 0.05:  # 权重变化超过5%
                priority = 3
            
            action = RebalanceAction(
                symbol=symbol,
                action_type=action_type,
                current_quantity=current_quantity,
                target_quantity=target_quantity,
                quantity_change=quantity_change,
                current_weight=current_weight,
                target_weight=target_weight,
                weight_change=weight_change,
                estimated_cost=estimated_cost,
                priority=priority
            )
            
            actions.append(action)
        
        # 处理需要清仓的股票
        for symbol, current_quantity in current_holdings.items():
            if symbol not in optimization_result.target_weights and current_quantity > 0:
                current_price = current_prices.get(symbol, 0.0)
                if current_price > 0:
                    current_weight = optimization_result.current_weights.get(symbol, 0.0)
                    trade_value = current_quantity * current_price
                    estimated_cost = trade_value * self.config.transaction_cost_pct
                    
                    action = RebalanceAction(
                        symbol=symbol,
                        action_type="SELL",
                        current_quantity=current_quantity,
                        target_quantity=0,
                        quantity_change=-current_quantity,
                        current_weight=current_weight,
                        target_weight=0.0,
                        weight_change=-current_weight,
                        estimated_cost=estimated_cost,
                        priority=4  # 清仓优先级较高
                    )
                    
                    actions.append(action)
        
        # 按优先级排序
        actions.sort(key=lambda x: x.priority, reverse=True)
        return actions
    
    def execute_rebalance(self, symbols: List[str], total_capital: float) -> Tuple[OptimizationResult, List[RebalanceAction]]:
        """
        执行再平衡
        
        Args:
            symbols: 候选股票列表
            total_capital: 总资本
            
        Returns:
            (优化结果, 再平衡动作列表)
        """
        # 计算当前权重
        positions = self.position_manager.get_all_positions()
        total_value = sum(p.market_value for p in positions) if positions else total_capital
        current_weights = {p.symbol: p.market_value / total_value for p in positions} if total_value > 0 else {}
        
        # 优化权重
        optimization_result = self.optimize_portfolio_weights(symbols, current_weights)
        
        # 生成再平衡动作
        rebalance_actions = self.generate_rebalance_actions(optimization_result, total_capital)
        
        # 更新最后再平衡时间
        self.last_rebalance_time = datetime.now()
        
        self.logger.info(f"执行再平衡: 目标权重 {optimization_result.target_weights}")
        self.logger.info(f"生成 {len(rebalance_actions)} 个再平衡动作")
        
        return optimization_result, rebalance_actions
    
    def get_optimization_summary(self) -> Dict[str, Any]:
        """
        获取优化汇总信息
        
        Returns:
            优化汇总信息
        """
        if not self.optimization_history:
            return {
                'total_optimizations': 0,
                'last_optimization_time': None,
                'last_rebalance_time': self.last_rebalance_time.isoformat() if self.last_rebalance_time else None,
                'config': self.config.__dict__
            }
        
        latest_result = self.optimization_history[-1]
        
        return {
            'total_optimizations': len(self.optimization_history),
            'last_optimization_time': latest_result.optimization_time.isoformat(),
            'last_rebalance_time': self.last_rebalance_time.isoformat() if self.last_rebalance_time else None,
            'latest_result': {
                'expected_return': latest_result.expected_return,
                'expected_risk': latest_result.expected_risk,
                'sharpe_ratio': latest_result.sharpe_ratio,
                'target_weights': latest_result.target_weights,
                'reason': latest_result.reason
            },
            'config': self.config.__dict__
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'optimization_summary': self.get_optimization_summary(),
            'optimization_history': [
                {
                    'target_weights': result.target_weights,
                    'expected_return': result.expected_return,
                    'expected_risk': result.expected_risk,
                    'sharpe_ratio': result.sharpe_ratio,
                    'optimization_time': result.optimization_time.isoformat(),
                    'reason': result.reason
                }
                for result in self.optimization_history[-10:]  # 最近10次优化
            ]
        }