"""
仓位管理器
Position Manager
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from collections import defaultdict
import threading
import time

from .models import Position, PositionType, PositionStatus, PositionChange, PortfolioSummary
from ..orders.models import Order, OrderSide, OrderStatus


class PositionManager:
    """
    仓位管理器
    负责实时跟踪、同步和管理所有仓位
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化仓位管理器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 仓位存储
        self.positions: Dict[str, Position] = {}  # symbol -> Position
        self.position_history: List[Position] = []  # 历史仓位记录
        
        # 实时价格缓存
        self.current_prices: Dict[str, float] = {}  # symbol -> price
        self.price_update_time: Dict[str, datetime] = {}  # symbol -> update_time
        
        # 同步锁
        self._lock = threading.RLock()
        
        # 配置参数
        self.max_position_count = self.config.get('max_position_count', 10)
        self.price_update_interval = self.config.get('price_update_interval', 1.0)  # 秒
        self.position_sync_interval = self.config.get('position_sync_interval', 5.0)  # 秒
        
        # 启动后台同步任务
        self._running = True
        self._sync_thread = threading.Thread(target=self._sync_positions_loop, daemon=True)
        self._sync_thread.start()
        
        self.logger.info("仓位管理器初始化完成")
    
    def stop(self):
        """停止仓位管理器"""
        self._running = False
        if self._sync_thread.is_alive():
            self._sync_thread.join(timeout=5.0)
        self.logger.info("仓位管理器已停止")
    
    def get_position(self, symbol: str) -> Optional[Position]:
        """
        获取指定股票的仓位
        
        Args:
            symbol: 股票代码
            
        Returns:
            Position对象，如果不存在则返回None
        """
        with self._lock:
            return self.positions.get(symbol)
    
    def get_all_positions(self, active_only: bool = True) -> List[Position]:
        """
        获取所有仓位
        
        Args:
            active_only: 是否只返回活跃仓位
            
        Returns:
            仓位列表
        """
        with self._lock:
            positions = list(self.positions.values())
            if active_only:
                positions = [p for p in positions if p.is_active]
            return positions
    
    def get_positions_by_type(self, position_type: PositionType) -> List[Position]:
        """
        根据仓位类型获取仓位
        
        Args:
            position_type: 仓位类型
            
        Returns:
            仓位列表
        """
        with self._lock:
            return [p for p in self.positions.values() if p.position_type == position_type and p.is_active]
    
    def create_position(self, symbol: str, position_type: PositionType, 
                       initial_quantity: int = 0, initial_price: float = 0.0,
                       order_id: Optional[str] = None, reason: str = "") -> Position:
        """
        创建新仓位
        
        Args:
            symbol: 股票代码
            position_type: 仓位类型
            initial_quantity: 初始数量
            initial_price: 初始价格
            order_id: 关联订单ID
            reason: 创建原因
            
        Returns:
            创建的Position对象
        """
        with self._lock:
            # 检查是否已存在相同类型的仓位
            existing_key = f"{symbol}_{position_type.value}"
            if existing_key in self.positions:
                self.logger.warning(f"仓位已存在: {existing_key}")
                return self.positions[existing_key]
            
            # 创建新仓位
            position = Position(
                position_id="",
                symbol=symbol,
                position_type=position_type,
                status=PositionStatus.ACTIVE
            )
            
            # 如果有初始数量，添加到仓位
            if initial_quantity > 0 and initial_price > 0:
                position.add_quantity(initial_quantity, initial_price, order_id, reason)
            
            # 更新当前价格
            if symbol in self.current_prices:
                position.update_price(self.current_prices[symbol])
            
            # 存储仓位
            self.positions[existing_key] = position
            
            self.logger.info(f"创建新仓位: {symbol} {position_type.value} 数量:{initial_quantity} 价格:{initial_price}")
            return position
    
    def update_position_from_order(self, order: Order) -> bool:
        """
        根据订单更新仓位
        
        Args:
            order: 订单对象
            
        Returns:
            是否更新成功
        """
        if order.status != OrderStatus.FILLED or order.filled_quantity <= 0:
            return False
        
        with self._lock:
            try:
                # 确定仓位类型（这里简化处理，实际应根据订单标签等判断）
                position_type = PositionType.LONG  # 默认为多头
                if 'BASE' in order.tags:
                    position_type = PositionType.BASE
                elif 'T_PLUS_ZERO' in order.tags:
                    position_type = PositionType.T_PLUS_ZERO
                
                position_key = f"{order.symbol}_{position_type.value}"
                position = self.positions.get(position_key)
                
                if order.is_buy:
                    # 买入订单 - 增加仓位
                    if position is None:
                        position = self.create_position(
                            order.symbol, 
                            position_type,
                            order.filled_quantity,
                            order.avg_fill_price,
                            order.order_id,
                            f"订单买入: {order.reason}"
                        )
                    else:
                        position.add_quantity(
                            order.filled_quantity,
                            order.avg_fill_price,
                            order.order_id,
                            f"订单买入: {order.reason}"
                        )
                else:
                    # 卖出订单 - 减少仓位
                    if position is not None:
                        position.reduce_quantity(
                            order.filled_quantity,
                            order.avg_fill_price,
                            order.order_id,
                            f"订单卖出: {order.reason}"
                        )
                    else:
                        self.logger.warning(f"尝试减少不存在的仓位: {order.symbol}")
                        return False
                
                self.logger.info(f"根据订单更新仓位: {order.symbol} {order.side.value} {order.filled_quantity}@{order.avg_fill_price}")
                return True
                
            except Exception as e:
                self.logger.error(f"更新仓位失败: {e}")
                return False
    
    def update_price(self, symbol: str, price: float) -> None:
        """
        更新股票价格
        
        Args:
            symbol: 股票代码
            price: 最新价格
        """
        with self._lock:
            self.current_prices[symbol] = price
            self.price_update_time[symbol] = datetime.now()
            
            # 更新所有相关仓位的价格
            for position_key, position in self.positions.items():
                if position.symbol == symbol and position.is_active:
                    position.update_price(price)
    
    def update_prices(self, prices: Dict[str, float]) -> None:
        """
        批量更新股票价格
        
        Args:
            prices: 股票代码到价格的映射
        """
        for symbol, price in prices.items():
            self.update_price(symbol, price)
    
    def freeze_position(self, symbol: str, quantity: int, position_type: Optional[PositionType] = None) -> bool:
        """
        冻结仓位
        
        Args:
            symbol: 股票代码
            quantity: 冻结数量
            position_type: 仓位类型，如果为None则冻结所有类型
            
        Returns:
            是否冻结成功
        """
        with self._lock:
            if position_type:
                position_key = f"{symbol}_{position_type.value}"
                position = self.positions.get(position_key)
                if position:
                    return position.freeze_quantity(quantity)
                return False
            else:
                # 冻结所有类型的仓位
                remaining_quantity = quantity
                for position_key, position in self.positions.items():
                    if position.symbol == symbol and position.is_active and remaining_quantity > 0:
                        freeze_qty = min(remaining_quantity, position.available_quantity)
                        if position.freeze_quantity(freeze_qty):
                            remaining_quantity -= freeze_qty
                
                return remaining_quantity == 0
    
    def unfreeze_position(self, symbol: str, quantity: int, position_type: Optional[PositionType] = None) -> bool:
        """
        解冻仓位
        
        Args:
            symbol: 股票代码
            quantity: 解冻数量
            position_type: 仓位类型，如果为None则解冻所有类型
            
        Returns:
            是否解冻成功
        """
        with self._lock:
            if position_type:
                position_key = f"{symbol}_{position_type.value}"
                position = self.positions.get(position_key)
                if position:
                    return position.unfreeze_quantity(quantity)
                return False
            else:
                # 解冻所有类型的仓位
                remaining_quantity = quantity
                for position_key, position in self.positions.items():
                    if position.symbol == symbol and position.is_active and remaining_quantity > 0:
                        unfreeze_qty = min(remaining_quantity, position.frozen_quantity)
                        if position.unfreeze_quantity(unfreeze_qty):
                            remaining_quantity -= unfreeze_qty
                
                return remaining_quantity == 0
    
    def close_position(self, symbol: str, position_type: Optional[PositionType] = None, 
                      price: Optional[float] = None, reason: str = "手动平仓") -> bool:
        """
        平仓
        
        Args:
            symbol: 股票代码
            position_type: 仓位类型，如果为None则平掉所有类型
            price: 平仓价格，如果为None则使用当前价格
            reason: 平仓原因
            
        Returns:
            是否平仓成功
        """
        with self._lock:
            if price is None:
                price = self.current_prices.get(symbol, 0.0)
            
            if position_type:
                position_key = f"{symbol}_{position_type.value}"
                position = self.positions.get(position_key)
                if position and position.is_active:
                    success = position.close_position(price, None, reason)
                    if success:
                        # 移动到历史记录
                        self.position_history.append(position)
                        del self.positions[position_key]
                    return success
                return False
            else:
                # 平掉所有类型的仓位
                success_count = 0
                positions_to_close = [(k, p) for k, p in self.positions.items() 
                                    if p.symbol == symbol and p.is_active]
                
                for position_key, position in positions_to_close:
                    if position.close_position(price, None, reason):
                        success_count += 1
                        self.position_history.append(position)
                        del self.positions[position_key]
                
                return success_count > 0
    
    def get_portfolio_summary(self) -> PortfolioSummary:
        """
        获取投资组合汇总信息
        
        Returns:
            PortfolioSummary对象
        """
        with self._lock:
            summary = PortfolioSummary()
            
            active_positions = [p for p in self.positions.values() if p.is_active]
            
            summary.position_count = len(self.positions)
            summary.active_position_count = len(active_positions)
            
            for position in active_positions:
                summary.total_market_value += position.market_value
                summary.total_cost += position.total_cost
                summary.total_unrealized_pnl += position.unrealized_pnl
                summary.total_realized_pnl += position.realized_pnl
                
                # 更新最大回撤
                if position.max_drawdown > summary.max_drawdown:
                    summary.max_drawdown = position.max_drawdown
                if position.max_drawdown_pct > summary.max_drawdown_pct:
                    summary.max_drawdown_pct = position.max_drawdown_pct
            
            # 计算总盈亏
            summary.total_pnl = summary.total_unrealized_pnl + summary.total_realized_pnl
            if summary.total_cost > 0:
                summary.total_pnl_pct = summary.total_pnl / summary.total_cost
            
            summary.update_time = datetime.now()
            return summary
    
    def get_position_changes(self, symbol: Optional[str] = None, 
                           start_time: Optional[datetime] = None,
                           end_time: Optional[datetime] = None) -> List[PositionChange]:
        """
        获取仓位变化记录
        
        Args:
            symbol: 股票代码，如果为None则返回所有股票
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            仓位变化记录列表
        """
        with self._lock:
            changes = []
            
            # 从当前仓位收集变化记录
            for position in self.positions.values():
                if symbol is None or position.symbol == symbol:
                    changes.extend(position.changes)
            
            # 从历史仓位收集变化记录
            for position in self.position_history:
                if symbol is None or position.symbol == symbol:
                    changes.extend(position.changes)
            
            # 时间过滤
            if start_time or end_time:
                filtered_changes = []
                for change in changes:
                    if start_time and change.timestamp < start_time:
                        continue
                    if end_time and change.timestamp > end_time:
                        continue
                    filtered_changes.append(change)
                changes = filtered_changes
            
            # 按时间排序
            changes.sort(key=lambda x: x.timestamp)
            return changes
    
    def get_available_quantity(self, symbol: str, position_type: Optional[PositionType] = None) -> int:
        """
        获取可用数量
        
        Args:
            symbol: 股票代码
            position_type: 仓位类型，如果为None则返回所有类型的总和
            
        Returns:
            可用数量
        """
        with self._lock:
            if position_type:
                position_key = f"{symbol}_{position_type.value}"
                position = self.positions.get(position_key)
                return position.available_quantity if position else 0
            else:
                total_available = 0
                for position in self.positions.values():
                    if position.symbol == symbol and position.is_active:
                        total_available += position.available_quantity
                return total_available
    
    def _sync_positions_loop(self):
        """后台同步仓位循环"""
        while self._running:
            try:
                self._sync_positions()
                time.sleep(self.position_sync_interval)
            except Exception as e:
                self.logger.error(f"仓位同步出错: {e}")
                time.sleep(self.position_sync_interval)
    
    def _sync_positions(self):
        """同步仓位数据（与券商系统同步）"""
        # 这里应该实现与券商系统的同步逻辑
        # 由于是模拟环境，这里只做基本的数据一致性检查
        with self._lock:
            current_time = datetime.now()
            
            # 检查价格更新时效性
            stale_symbols = []
            for symbol, update_time in self.price_update_time.items():
                if (current_time - update_time).total_seconds() > 60:  # 超过1分钟未更新
                    stale_symbols.append(symbol)
            
            if stale_symbols:
                self.logger.warning(f"以下股票价格数据过期: {stale_symbols}")
            
            # 清理已关闭的仓位
            closed_positions = []
            for position_key, position in self.positions.items():
                if position.status == PositionStatus.CLOSED:
                    closed_positions.append(position_key)
            
            for position_key in closed_positions:
                position = self.positions.pop(position_key)
                self.position_history.append(position)
                self.logger.info(f"清理已关闭仓位: {position_key}")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        with self._lock:
            return {
                'positions': {k: v.to_dict() for k, v in self.positions.items()},
                'portfolio_summary': self.get_portfolio_summary().to_dict(),
                'current_prices': self.current_prices,
                'position_count': len(self.positions),
                'active_position_count': len([p for p in self.positions.values() if p.is_active]),
                'update_time': datetime.now().isoformat()
            }