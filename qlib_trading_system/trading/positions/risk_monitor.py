"""
仓位风险监控系统
Position Risk Monitor
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Callable
from enum import Enum
from dataclasses import dataclass
import threading
import time

from .models import Position, PositionType, PortfolioSummary
from .manager import PositionManager


class RiskLevel(Enum):
    """风险等级枚举"""
    LOW = "LOW"          # 低风险
    MEDIUM = "MEDIUM"    # 中等风险
    HIGH = "HIGH"        # 高风险
    CRITICAL = "CRITICAL"  # 严重风险


class AlertType(Enum):
    """警报类型枚举"""
    POSITION_LOSS = "POSITION_LOSS"        # 单仓位亏损
    PORTFOLIO_LOSS = "PORTFOLIO_LOSS"      # 组合亏损
    DRAWDOWN_LIMIT = "DRAWDOWN_LIMIT"      # 回撤限制
    CONCENTRATION = "CONCENTRATION"        # 集中度风险
    LIQUIDITY = "LIQUIDITY"               # 流动性风险
    VOLATILITY = "VOLATILITY"             # 波动率风险
    MARGIN_CALL = "MARGIN_CALL"           # 保证金不足


@dataclass
class RiskAlert:
    """风险警报"""
    alert_id: str
    alert_type: AlertType
    risk_level: RiskLevel
    symbol: Optional[str]
    message: str
    current_value: float
    threshold_value: float
    timestamp: datetime
    is_resolved: bool = False
    resolve_time: Optional[datetime] = None
    
    def resolve(self):
        """解决警报"""
        self.is_resolved = True
        self.resolve_time = datetime.now()


@dataclass
class RiskConfig:
    """风险配置"""
    # 单仓位风险限制
    max_position_loss_pct: float = 0.02  # 单仓位最大亏损2%
    max_position_drawdown_pct: float = 0.05  # 单仓位最大回撤5%
    
    # 组合风险限制
    max_portfolio_loss_pct: float = 0.01  # 组合最大日亏损1%
    max_portfolio_drawdown_pct: float = 0.30  # 组合最大回撤30%（高风险模式）
    
    # 集中度风险限制
    max_single_position_pct: float = 0.30  # 单仓位最大占比30%
    max_sector_concentration_pct: float = 0.80  # 单行业最大占比80%
    
    # 流动性风险限制
    min_daily_volume: float = 50000000  # 最小日成交额5000万
    max_impact_cost_pct: float = 0.01   # 最大冲击成本1%
    
    # 波动率风险限制
    max_position_volatility: float = 0.05  # 最大仓位波动率5%
    max_portfolio_volatility: float = 0.04  # 最大组合波动率4%
    
    # 保证金风险限制
    min_margin_ratio: float = 0.20  # 最小保证金比例20%
    margin_call_ratio: float = 0.15  # 保证金追缴比例15%


class PositionRiskMonitor:
    """
    仓位风险监控器
    实时监控仓位风险并发出警报
    """
    
    def __init__(self, position_manager: PositionManager, config: Optional[RiskConfig] = None):
        """
        初始化风险监控器
        
        Args:
            position_manager: 仓位管理器
            config: 风险配置
        """
        self.position_manager = position_manager
        self.config = config or RiskConfig()
        self.logger = logging.getLogger(__name__)
        
        # 警报存储
        self.active_alerts: Dict[str, RiskAlert] = {}
        self.alert_history: List[RiskAlert] = []
        
        # 风险指标缓存
        self.risk_metrics_cache: Dict[str, Any] = {}
        self.last_update_time = datetime.now()
        
        # 回调函数
        self.alert_callbacks: List[Callable[[RiskAlert], None]] = []
        
        # 监控线程
        self._running = True
        self._monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self._monitor_thread.start()
        
        self.logger.info("仓位风险监控器初始化完成")
    
    def stop(self):
        """停止风险监控器"""
        self._running = False
        if self._monitor_thread.is_alive():
            self._monitor_thread.join(timeout=5.0)
        self.logger.info("仓位风险监控器已停止")
    
    def add_alert_callback(self, callback: Callable[[RiskAlert], None]):
        """
        添加警报回调函数
        
        Args:
            callback: 回调函数，接收RiskAlert参数
        """
        self.alert_callbacks.append(callback)
    
    def check_position_risk(self, position: Position) -> List[RiskAlert]:
        """
        检查单个仓位风险
        
        Args:
            position: 仓位对象
            
        Returns:
            风险警报列表
        """
        alerts = []
        
        # 检查单仓位亏损
        if position.unrealized_pnl_pct < -self.config.max_position_loss_pct:
            alert = RiskAlert(
                alert_id=f"pos_loss_{position.symbol}_{int(datetime.now().timestamp())}",
                alert_type=AlertType.POSITION_LOSS,
                risk_level=RiskLevel.HIGH,
                symbol=position.symbol,
                message=f"仓位亏损超限: {position.symbol} 当前亏损 {position.unrealized_pnl_pct:.2%}",
                current_value=position.unrealized_pnl_pct,
                threshold_value=-self.config.max_position_loss_pct,
                timestamp=datetime.now()
            )
            alerts.append(alert)
        
        # 检查单仓位回撤
        if position.max_drawdown_pct > self.config.max_position_drawdown_pct:
            alert = RiskAlert(
                alert_id=f"pos_dd_{position.symbol}_{int(datetime.now().timestamp())}",
                alert_type=AlertType.DRAWDOWN_LIMIT,
                risk_level=RiskLevel.MEDIUM,
                symbol=position.symbol,
                message=f"仓位回撤超限: {position.symbol} 最大回撤 {position.max_drawdown_pct:.2%}",
                current_value=position.max_drawdown_pct,
                threshold_value=self.config.max_position_drawdown_pct,
                timestamp=datetime.now()
            )
            alerts.append(alert)
        
        return alerts
    
    def check_portfolio_risk(self, summary: PortfolioSummary) -> List[RiskAlert]:
        """
        检查组合风险
        
        Args:
            summary: 组合汇总信息
            
        Returns:
            风险警报列表
        """
        alerts = []
        
        # 检查组合亏损
        if summary.total_pnl_pct < -self.config.max_portfolio_loss_pct:
            alert = RiskAlert(
                alert_id=f"portfolio_loss_{int(datetime.now().timestamp())}",
                alert_type=AlertType.PORTFOLIO_LOSS,
                risk_level=RiskLevel.CRITICAL,
                symbol=None,
                message=f"组合亏损超限: 当前亏损 {summary.total_pnl_pct:.2%}",
                current_value=summary.total_pnl_pct,
                threshold_value=-self.config.max_portfolio_loss_pct,
                timestamp=datetime.now()
            )
            alerts.append(alert)
        
        # 检查组合回撤
        if summary.max_drawdown_pct > self.config.max_portfolio_drawdown_pct:
            alert = RiskAlert(
                alert_id=f"portfolio_dd_{int(datetime.now().timestamp())}",
                alert_type=AlertType.DRAWDOWN_LIMIT,
                risk_level=RiskLevel.HIGH,
                symbol=None,
                message=f"组合回撤超限: 最大回撤 {summary.max_drawdown_pct:.2%}",
                current_value=summary.max_drawdown_pct,
                threshold_value=self.config.max_portfolio_drawdown_pct,
                timestamp=datetime.now()
            )
            alerts.append(alert)
        
        return alerts
    
    def check_concentration_risk(self) -> List[RiskAlert]:
        """
        检查集中度风险
        
        Returns:
            风险警报列表
        """
        alerts = []
        positions = self.position_manager.get_all_positions()
        
        if not positions:
            return alerts
        
        # 计算总市值
        total_market_value = sum(p.market_value for p in positions)
        
        if total_market_value <= 0:
            return alerts
        
        # 检查单仓位集中度
        for position in positions:
            position_pct = position.market_value / total_market_value
            if position_pct > self.config.max_single_position_pct:
                alert = RiskAlert(
                    alert_id=f"concentration_{position.symbol}_{int(datetime.now().timestamp())}",
                    alert_type=AlertType.CONCENTRATION,
                    risk_level=RiskLevel.MEDIUM,
                    symbol=position.symbol,
                    message=f"单仓位集中度过高: {position.symbol} 占比 {position_pct:.2%}",
                    current_value=position_pct,
                    threshold_value=self.config.max_single_position_pct,
                    timestamp=datetime.now()
                )
                alerts.append(alert)
        
        # 检查行业集中度（这里简化处理，实际需要行业分类数据）
        # 假设从metadata中获取行业信息
        sector_values = {}
        for position in positions:
            sector = position.metadata.get('sector', 'Unknown')
            sector_values[sector] = sector_values.get(sector, 0) + position.market_value
        
        for sector, sector_value in sector_values.items():
            sector_pct = sector_value / total_market_value
            if sector_pct > self.config.max_sector_concentration_pct:
                alert = RiskAlert(
                    alert_id=f"sector_concentration_{sector}_{int(datetime.now().timestamp())}",
                    alert_type=AlertType.CONCENTRATION,
                    risk_level=RiskLevel.MEDIUM,
                    symbol=None,
                    message=f"行业集中度过高: {sector} 占比 {sector_pct:.2%}",
                    current_value=sector_pct,
                    threshold_value=self.config.max_sector_concentration_pct,
                    timestamp=datetime.now()
                )
                alerts.append(alert)
        
        return alerts
    
    def check_liquidity_risk(self) -> List[RiskAlert]:
        """
        检查流动性风险
        
        Returns:
            风险警报列表
        """
        alerts = []
        positions = self.position_manager.get_all_positions()
        
        for position in positions:
            # 检查日成交额（从metadata获取）
            daily_volume = position.metadata.get('daily_volume', 0)
            if daily_volume < self.config.min_daily_volume:
                alert = RiskAlert(
                    alert_id=f"liquidity_{position.symbol}_{int(datetime.now().timestamp())}",
                    alert_type=AlertType.LIQUIDITY,
                    risk_level=RiskLevel.MEDIUM,
                    symbol=position.symbol,
                    message=f"流动性不足: {position.symbol} 日成交额 {daily_volume:,.0f}",
                    current_value=daily_volume,
                    threshold_value=self.config.min_daily_volume,
                    timestamp=datetime.now()
                )
                alerts.append(alert)
        
        return alerts
    
    def check_volatility_risk(self) -> List[RiskAlert]:
        """
        检查波动率风险
        
        Returns:
            风险警报列表
        """
        alerts = []
        positions = self.position_manager.get_all_positions()
        
        for position in positions:
            # 检查仓位波动率（从metadata获取）
            volatility = position.metadata.get('volatility', 0)
            if volatility > self.config.max_position_volatility:
                alert = RiskAlert(
                    alert_id=f"volatility_{position.symbol}_{int(datetime.now().timestamp())}",
                    alert_type=AlertType.VOLATILITY,
                    risk_level=RiskLevel.MEDIUM,
                    symbol=position.symbol,
                    message=f"波动率过高: {position.symbol} 波动率 {volatility:.2%}",
                    current_value=volatility,
                    threshold_value=self.config.max_position_volatility,
                    timestamp=datetime.now()
                )
                alerts.append(alert)
        
        return alerts
    
    def check_all_risks(self) -> List[RiskAlert]:
        """
        检查所有风险
        
        Returns:
            风险警报列表
        """
        all_alerts = []
        
        # 检查单仓位风险
        positions = self.position_manager.get_all_positions()
        for position in positions:
            all_alerts.extend(self.check_position_risk(position))
        
        # 检查组合风险
        summary = self.position_manager.get_portfolio_summary()
        all_alerts.extend(self.check_portfolio_risk(summary))
        
        # 检查集中度风险
        all_alerts.extend(self.check_concentration_risk())
        
        # 检查流动性风险
        all_alerts.extend(self.check_liquidity_risk())
        
        # 检查波动率风险
        all_alerts.extend(self.check_volatility_risk())
        
        return all_alerts
    
    def process_alerts(self, alerts: List[RiskAlert]):
        """
        处理警报
        
        Args:
            alerts: 警报列表
        """
        for alert in alerts:
            # 检查是否为新警报
            if alert.alert_id not in self.active_alerts:
                self.active_alerts[alert.alert_id] = alert
                self.alert_history.append(alert)
                
                # 触发回调
                for callback in self.alert_callbacks:
                    try:
                        callback(alert)
                    except Exception as e:
                        self.logger.error(f"警报回调执行失败: {e}")
                
                self.logger.warning(f"新风险警报: {alert.message}")
    
    def resolve_alert(self, alert_id: str):
        """
        解决警报
        
        Args:
            alert_id: 警报ID
        """
        if alert_id in self.active_alerts:
            alert = self.active_alerts[alert_id]
            alert.resolve()
            del self.active_alerts[alert_id]
            self.logger.info(f"警报已解决: {alert.message}")
    
    def get_active_alerts(self, risk_level: Optional[RiskLevel] = None) -> List[RiskAlert]:
        """
        获取活跃警报
        
        Args:
            risk_level: 风险等级过滤
            
        Returns:
            警报列表
        """
        alerts = list(self.active_alerts.values())
        if risk_level:
            alerts = [a for a in alerts if a.risk_level == risk_level]
        return sorted(alerts, key=lambda x: x.timestamp, reverse=True)
    
    def get_risk_summary(self) -> Dict[str, Any]:
        """
        获取风险汇总
        
        Returns:
            风险汇总信息
        """
        active_alerts = list(self.active_alerts.values())
        
        # 按风险等级统计
        risk_counts = {level.value: 0 for level in RiskLevel}
        for alert in active_alerts:
            risk_counts[alert.risk_level.value] += 1
        
        # 按警报类型统计
        alert_type_counts = {alert_type.value: 0 for alert_type in AlertType}
        for alert in active_alerts:
            alert_type_counts[alert.alert_type.value] += 1
        
        # 计算风险评分（0-100）
        risk_score = 0
        for alert in active_alerts:
            if alert.risk_level == RiskLevel.LOW:
                risk_score += 10
            elif alert.risk_level == RiskLevel.MEDIUM:
                risk_score += 25
            elif alert.risk_level == RiskLevel.HIGH:
                risk_score += 50
            elif alert.risk_level == RiskLevel.CRITICAL:
                risk_score += 100
        
        risk_score = min(risk_score, 100)
        
        return {
            'total_alerts': len(active_alerts),
            'risk_score': risk_score,
            'risk_level_counts': risk_counts,
            'alert_type_counts': alert_type_counts,
            'last_update_time': self.last_update_time.isoformat(),
            'config': {
                'max_position_loss_pct': self.config.max_position_loss_pct,
                'max_portfolio_loss_pct': self.config.max_portfolio_loss_pct,
                'max_portfolio_drawdown_pct': self.config.max_portfolio_drawdown_pct,
                'max_single_position_pct': self.config.max_single_position_pct
            }
        }
    
    def _monitor_loop(self):
        """监控循环"""
        while self._running:
            try:
                # 检查所有风险
                alerts = self.check_all_risks()
                
                # 处理警报
                if alerts:
                    self.process_alerts(alerts)
                
                # 清理已解决的警报
                self._cleanup_resolved_alerts()
                
                # 更新缓存
                self.last_update_time = datetime.now()
                
                # 等待下次检查
                time.sleep(5.0)  # 每5秒检查一次
                
            except Exception as e:
                self.logger.error(f"风险监控出错: {e}")
                time.sleep(5.0)
    
    def _cleanup_resolved_alerts(self):
        """清理已解决的警报"""
        # 自动解决一些临时性警报
        current_time = datetime.now()
        alerts_to_resolve = []
        
        for alert_id, alert in self.active_alerts.items():
            # 如果警报超过1小时且风险等级不是严重，自动解决
            if (current_time - alert.timestamp).total_seconds() > 3600:
                if alert.risk_level != RiskLevel.CRITICAL:
                    alerts_to_resolve.append(alert_id)
        
        for alert_id in alerts_to_resolve:
            self.resolve_alert(alert_id)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'active_alerts': [alert.__dict__ for alert in self.active_alerts.values()],
            'risk_summary': self.get_risk_summary(),
            'config': {
                'max_position_loss_pct': self.config.max_position_loss_pct,
                'max_portfolio_loss_pct': self.config.max_portfolio_loss_pct,
                'max_portfolio_drawdown_pct': self.config.max_portfolio_drawdown_pct,
                'max_single_position_pct': self.config.max_single_position_pct,
                'max_sector_concentration_pct': self.config.max_sector_concentration_pct
            }
        }