"""
仓位管理系统集成测试
Position Management System Integration Test
"""

import unittest
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

from qlib_trading_system.trading.positions.models import Position, PositionType, PositionStatus, PositionChange
from qlib_trading_system.trading.positions.manager import PositionManager
from qlib_trading_system.trading.positions.risk_monitor import PositionRiskMonitor, RiskConfig, RiskLevel
from qlib_trading_system.trading.positions.optimizer import PositionOptimizer, OptimizationConfig, OptimizationObjective
from qlib_trading_system.trading.positions.analyzer import PositionAnalyzer
from qlib_trading_system.trading.orders.models import Order, OrderSide, OrderType, OrderStatus


class TestPositionManagement(unittest.TestCase):
    """仓位管理系统测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 配置日志
        logging.basicConfig(level=logging.INFO)
        
        # 初始化仓位管理器
        self.position_manager = PositionManager({
            'max_position_count': 5,
            'price_update_interval': 0.1,
            'position_sync_interval': 1.0
        })
        
        # 初始化风险监控器
        risk_config = RiskConfig(
            max_position_loss_pct=0.05,  # 5%止损（测试用）
            max_portfolio_loss_pct=0.02,  # 2%组合止损
            max_portfolio_drawdown_pct=0.10,  # 10%最大回撤
            max_single_position_pct=0.40  # 40%单仓位限制
        )
        self.risk_monitor = PositionRiskMonitor(self.position_manager, risk_config)
        
        # 初始化优化器
        opt_config = OptimizationConfig(
            objective=OptimizationObjective.MAX_SHARPE,
            max_positions=3,
            max_position_weight=0.40,
            small_capital_mode=True,
            single_stock_focus=True
        )
        self.optimizer = PositionOptimizer(self.position_manager, opt_config)
        
        # 初始化分析器
        self.analyzer = PositionAnalyzer(self.position_manager)
        
        # 测试数据
        self.test_symbols = ['000001.SZ', '000002.SZ', '600000.SH']
        self.test_prices = {
            '000001.SZ': 15.50,
            '000002.SZ': 28.80,
            '600000.SH': 12.30
        }
        
        print("=" * 60)
        print("仓位管理系统集成测试开始")
        print("=" * 60)
    
    def tearDown(self):
        """测试后清理"""
        self.position_manager.stop()
        self.risk_monitor.stop()
        print("=" * 60)
        print("仓位管理系统集成测试完成")
        print("=" * 60)
    
    def test_01_position_creation_and_management(self):
        """测试1: 仓位创建和管理"""
        print("\n【测试1】仓位创建和管理")
        
        # 创建底仓
        base_position = self.position_manager.create_position(
            symbol='000001.SZ',
            position_type=PositionType.BASE,
            initial_quantity=1000,
            initial_price=15.00,
            reason="建立底仓"
        )
        
        self.assertIsNotNone(base_position)
        self.assertEqual(base_position.symbol, '000001.SZ')
        self.assertEqual(base_position.quantity, 1000)
        self.assertEqual(base_position.avg_cost, 15.00)
        print(f"✓ 成功创建底仓: {base_position.symbol} 数量:{base_position.quantity} 成本:{base_position.avg_cost}")
        
        # 创建T+0仓位
        t_position = self.position_manager.create_position(
            symbol='000001.SZ',
            position_type=PositionType.T_PLUS_ZERO,
            initial_quantity=200,
            initial_price=15.20,
            reason="T+0买入"
        )
        
        self.assertIsNotNone(t_position)
        self.assertEqual(t_position.position_type, PositionType.T_PLUS_ZERO)
        print(f"✓ 成功创建T+0仓位: {t_position.symbol} 数量:{t_position.quantity}")
        
        # 更新价格
        self.position_manager.update_price('000001.SZ', 15.50)
        
        # 检查仓位更新
        updated_base = self.position_manager.get_position('000001.SZ_BASE')
        self.assertEqual(updated_base.current_price, 15.50)
        self.assertGreater(updated_base.unrealized_pnl, 0)  # 应该有盈利
        print(f"✓ 价格更新成功: 当前价格 {updated_base.current_price}, 未实现盈亏 {updated_base.unrealized_pnl:.2f}")
        
        # 获取所有仓位
        all_positions = self.position_manager.get_all_positions()
        self.assertEqual(len(all_positions), 2)
        print(f"✓ 当前活跃仓位数量: {len(all_positions)}")
        
        # 获取组合汇总
        summary = self.position_manager.get_portfolio_summary()
        self.assertGreater(summary.total_market_value, 0)
        print(f"✓ 组合汇总: 总市值 {summary.total_market_value:.2f}, 总盈亏 {summary.total_pnl:.2f}")
    
    def test_02_order_integration(self):
        """测试2: 订单集成"""
        print("\n【测试2】订单集成")
        
        # 创建买入订单
        buy_order = Order(
            order_id="test_buy_001",
            symbol='000002.SZ',
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=500,
            status=OrderStatus.FILLED,
            filled_quantity=500,
            avg_fill_price=28.50,
            tags=['BASE']
        )
        
        # 根据订单更新仓位
        success = self.position_manager.update_position_from_order(buy_order)
        self.assertTrue(success)
        print(f"✓ 买入订单处理成功: {buy_order.symbol} {buy_order.filled_quantity}@{buy_order.avg_fill_price}")
        
        # 检查仓位
        position = self.position_manager.get_position('000002.SZ_BASE')
        self.assertIsNotNone(position)
        self.assertEqual(position.quantity, 500)
        self.assertEqual(position.avg_cost, 28.50)
        print(f"✓ 仓位创建成功: 数量 {position.quantity}, 成本 {position.avg_cost}")
        
        # 创建卖出订单
        sell_order = Order(
            order_id="test_sell_001",
            symbol='000002.SZ',
            side=OrderSide.SELL,
            order_type=OrderType.MARKET,
            quantity=200,
            status=OrderStatus.FILLED,
            filled_quantity=200,
            avg_fill_price=29.00,
            tags=['BASE']
        )
        
        # 根据订单更新仓位
        success = self.position_manager.update_position_from_order(sell_order)
        self.assertTrue(success)
        print(f"✓ 卖出订单处理成功: {sell_order.symbol} {sell_order.filled_quantity}@{sell_order.avg_fill_price}")
        
        # 检查仓位更新
        updated_position = self.position_manager.get_position('000002.SZ_BASE')
        self.assertEqual(updated_position.quantity, 300)  # 500 - 200
        self.assertGreater(updated_position.realized_pnl, 0)  # 应该有已实现盈利
        print(f"✓ 仓位更新成功: 剩余数量 {updated_position.quantity}, 已实现盈亏 {updated_position.realized_pnl:.2f}")
    
    def test_03_risk_monitoring(self):
        """测试3: 风险监控"""
        print("\n【测试3】风险监控")
        
        # 创建一个亏损仓位来触发风险警报
        loss_position = self.position_manager.create_position(
            symbol='600000.SH',
            position_type=PositionType.LONG,
            initial_quantity=1000,
            initial_price=12.00,
            reason="测试风险监控"
        )
        
        # 模拟价格下跌触发止损
        self.position_manager.update_price('600000.SH', 11.00)  # 下跌8.33%
        
        # 等待风险监控检查
        time.sleep(2)
        
        # 手动触发风险检查（因为后台线程可能还没执行）
        alerts = self.risk_monitor.check_all_risks()
        if alerts:
            self.risk_monitor.process_alerts(alerts)
        
        # 检查风险警报
        active_alerts = self.risk_monitor.get_active_alerts()
        
        # 如果没有自动触发，我们手动检查仓位风险
        if len(active_alerts) == 0:
            position = self.position_manager.get_position('600000.SH_LONG')
            if position:
                position_alerts = self.risk_monitor.check_position_risk(position)
                if position_alerts:
                    self.risk_monitor.process_alerts(position_alerts)
                    active_alerts = self.risk_monitor.get_active_alerts()
        
        # 获取仓位并检查状态
        position = self.position_manager.get_position('600000.SH_LONG')
        if position:
            print(f"仓位详情:")
            print(f"  成本: {position.avg_cost}")
            print(f"  当前价格: {position.current_price}")
            print(f"  市值: {position.market_value}")
            print(f"  总成本: {position.total_cost}")
            print(f"  未实现盈亏: {position.unrealized_pnl}")
            print(f"  未实现盈亏率: {position.unrealized_pnl_pct:.4f}")
            print(f"  风险阈值: {-self.risk_monitor.config.max_position_loss_pct:.4f}")
            
            # 如果亏损不够大，继续降价
            if position.unrealized_pnl_pct > -self.risk_monitor.config.max_position_loss_pct:
                print("亏损不够大，继续降价...")
                self.position_manager.update_price('600000.SH', 10.80)  # 下跌10%
                print(f"  降价后盈亏率: {position.unrealized_pnl_pct:.4f}")
                
                # 再次检查风险
                alerts = self.risk_monitor.check_all_risks()
                if alerts:
                    self.risk_monitor.process_alerts(alerts)
                active_alerts = self.risk_monitor.get_active_alerts()
        
        # 验证结果 - 如果确实有风险，应该有警报
        if position and position.unrealized_pnl_pct < -self.risk_monitor.config.max_position_loss_pct:
            self.assertGreater(len(active_alerts), 0, f"仓位亏损{position.unrealized_pnl_pct:.2%}超过阈值{-self.risk_monitor.config.max_position_loss_pct:.2%}，应该有风险警报")
        else:
            print("仓位亏损未超过阈值，跳过风险警报验证")
        print(f"✓ 触发风险警报数量: {len(active_alerts)}")
        
        for alert in active_alerts:
            print(f"  - {alert.alert_type.value}: {alert.message}")
        
        # 获取风险汇总
        risk_summary = self.risk_monitor.get_risk_summary()
        self.assertGreater(risk_summary['risk_score'], 0)
        print(f"✓ 风险评分: {risk_summary['risk_score']}")
        print(f"✓ 风险等级统计: {risk_summary['risk_level_counts']}")
        
        # 测试风险警报回调
        alert_received = []
        
        def alert_callback(alert):
            alert_received.append(alert)
            print(f"  收到风险警报回调: {alert.message}")
        
        self.risk_monitor.add_alert_callback(alert_callback)
        
        # 进一步下跌触发更多警报
        self.position_manager.update_price('600000.SH', 10.50)
        time.sleep(1)
        
        print(f"✓ 回调接收到的警报数量: {len(alert_received)}")
    
    def test_04_position_optimization(self):
        """测试4: 仓位优化"""
        print("\n【测试4】仓位优化")
        
        # 添加价格历史数据
        symbols = ['000001.SZ', '000002.SZ', '600000.SH']
        for symbol in symbols:
            for i in range(30):  # 30天历史数据
                price = self.test_prices[symbol] * (1 + (i - 15) * 0.01)  # 模拟价格波动
                self.optimizer.update_price_history(symbol, price)
        
        # 计算当前权重
        positions = self.position_manager.get_all_positions()
        total_value = sum(p.market_value for p in positions) if positions else 10000
        current_weights = {p.symbol: p.market_value / total_value for p in positions} if total_value > 0 else {}
        
        # 执行优化
        optimization_result = self.optimizer.optimize_portfolio_weights(symbols, current_weights)
        
        self.assertIsNotNone(optimization_result)
        self.assertGreater(len(optimization_result.target_weights), 0)
        print(f"✓ 优化完成: 目标权重 {optimization_result.target_weights}")
        print(f"✓ 预期收益率: {optimization_result.expected_return:.4f}")
        print(f"✓ 预期风险: {optimization_result.expected_risk:.4f}")
        print(f"✓ 夏普比率: {optimization_result.sharpe_ratio:.4f}")
        
        # 检查再平衡需求
        need_rebalance, reason = self.optimizer.check_rebalance_needed()
        print(f"✓ 是否需要再平衡: {need_rebalance}, 原因: {reason.value}")
        
        if need_rebalance:
            # 生成再平衡动作
            rebalance_actions = self.optimizer.generate_rebalance_actions(optimization_result, 50000)
            print(f"✓ 生成再平衡动作数量: {len(rebalance_actions)}")
            
            for action in rebalance_actions[:3]:  # 显示前3个动作
                print(f"  - {action.symbol}: {action.action_type} {action.quantity_change} 股 "
                      f"(权重: {action.current_weight:.2%} -> {action.target_weight:.2%})")
    
    def test_05_position_analysis(self):
        """测试5: 仓位分析"""
        print("\n【测试5】仓位分析")
        
        # 更新每日组合价值（模拟历史数据）
        base_date = datetime.now() - timedelta(days=30)
        for i in range(30):
            date = base_date + timedelta(days=i)
            portfolio_value = 50000 * (1 + i * 0.002)  # 模拟增长
            self.analyzer.update_daily_portfolio_value(date, portfolio_value)
        
        # 计算绩效指标
        performance = self.analyzer.calculate_portfolio_performance()
        
        self.assertIsNotNone(performance)
        print(f"✓ 总收益率: {performance.total_return:.4f}")
        print(f"✓ 年化收益率: {performance.annualized_return:.4f}")
        print(f"✓ 夏普比率: {performance.sharpe_ratio:.4f}")
        print(f"✓ 最大回撤: {performance.max_drawdown:.4f}")
        print(f"✓ 胜率: {performance.win_rate:.2%}")
        
        # 分析单个仓位
        positions = self.position_manager.get_all_positions()
        if positions:
            position_analysis = self.analyzer.analyze_position(positions[0])
            print(f"✓ 单仓位分析 - {position_analysis.symbol}:")
            print(f"  持仓天数: {position_analysis.holding_days}")
            print(f"  总收益率: {position_analysis.total_return_pct:.2%}")
            print(f"  年化收益率: {position_analysis.annualized_return_pct:.2%}")
        
        # 获取仓位变化分析
        changes_analysis = self.analyzer.get_position_changes_analysis()
        print(f"✓ 仓位变化分析:")
        print(f"  总变化次数: {changes_analysis['total_changes']}")
        print(f"  变化类型统计: {changes_analysis['change_types']}")
        
        # 生成绩效报告
        report = self.analyzer.generate_performance_report()
        self.assertIsNotNone(report)
        print(f"✓ 绩效报告生成成功")
        print(f"  分析仓位数量: {report['summary_statistics']['total_positions_analyzed']}")
        print(f"  当前活跃仓位: {report['summary_statistics']['active_positions']}")
    
    def test_06_comprehensive_workflow(self):
        """测试6: 综合工作流程"""
        print("\n【测试6】综合工作流程")
        
        # 1. 创建初始仓位
        print("步骤1: 创建初始仓位")
        initial_capital = 100000
        
        # 小资金模式：全仓单股
        main_position = self.position_manager.create_position(
            symbol='000001.SZ',
            position_type=PositionType.BASE,
            initial_quantity=5000,  # 75%资金
            initial_price=15.00,
            reason="小资金全仓策略"
        )
        
        # T+0仓位
        t_position = self.position_manager.create_position(
            symbol='000001.SZ',
            position_type=PositionType.T_PLUS_ZERO,
            initial_quantity=1000,  # 15%资金
            initial_price=15.00,
            reason="T+0操作"
        )
        
        print(f"✓ 底仓建立: {main_position.quantity} 股")
        print(f"✓ T仓建立: {t_position.quantity} 股")
        
        # 2. 模拟价格波动和交易
        print("步骤2: 模拟价格波动和交易")
        price_sequence = [15.00, 15.20, 15.10, 15.30, 15.25, 15.40, 15.35]
        
        for i, price in enumerate(price_sequence):
            self.position_manager.update_price('000001.SZ', price)
            
            # 模拟T+0操作
            if i % 2 == 1 and price > 15.15:  # 高点卖出T仓
                t_pos = self.position_manager.get_position('000001.SZ_T_PLUS_ZERO')
                if t_pos and t_pos.quantity > 0:
                    t_pos.reduce_quantity(500, price, reason=f"T+0卖出@{price}")
                    print(f"  T+0卖出: 500股@{price}")
            
            elif i % 2 == 0 and price < 15.20:  # 低点买入T仓
                t_pos = self.position_manager.get_position('000001.SZ_T_PLUS_ZERO')
                if t_pos:
                    t_pos.add_quantity(500, price, reason=f"T+0买入@{price}")
                    print(f"  T+0买入: 500股@{price}")
            
            time.sleep(0.1)  # 模拟时间间隔
        
        # 3. 检查风险状况
        print("步骤3: 检查风险状况")
        risk_summary = self.risk_monitor.get_risk_summary()
        print(f"✓ 当前风险评分: {risk_summary['risk_score']}")
        
        active_alerts = self.risk_monitor.get_active_alerts()
        if active_alerts:
            print(f"✓ 活跃风险警报: {len(active_alerts)} 个")
        else:
            print("✓ 无活跃风险警报")
        
        # 4. 执行仓位优化
        print("步骤4: 执行仓位优化")
        symbols = ['000001.SZ']  # 小资金模式专注单股
        
        optimization_result, rebalance_actions = self.optimizer.execute_rebalance(symbols, initial_capital)
        print(f"✓ 优化目标权重: {optimization_result.target_weights}")
        print(f"✓ 再平衡动作数量: {len(rebalance_actions)}")
        
        # 5. 生成分析报告
        print("步骤5: 生成分析报告")
        performance_report = self.analyzer.generate_performance_report()
        
        portfolio_perf = performance_report['portfolio_performance']
        print(f"✓ 组合绩效:")
        print(f"  总收益率: {portfolio_perf.get('total_return', 0):.4f}")
        print(f"  夏普比率: {portfolio_perf.get('sharpe_ratio', 0):.4f}")
        print(f"  最大回撤: {portfolio_perf.get('max_drawdown', 0):.4f}")
        print(f"  胜率: {portfolio_perf.get('win_rate', 0):.2%}")
        
        # 6. 系统状态汇总
        print("步骤6: 系统状态汇总")
        portfolio_summary = self.position_manager.get_portfolio_summary()
        print(f"✓ 系统状态汇总:")
        print(f"  活跃仓位数: {portfolio_summary.active_position_count}")
        print(f"  总市值: {portfolio_summary.total_market_value:.2f}")
        print(f"  总盈亏: {portfolio_summary.total_pnl:.2f} ({portfolio_summary.total_pnl_pct:.2%})")
        print(f"  最大回撤: {portfolio_summary.max_drawdown:.2f} ({portfolio_summary.max_drawdown_pct:.2%})")
        
        # 验证小资金策略特点
        positions = self.position_manager.get_all_positions()
        base_positions = [p for p in positions if p.position_type == PositionType.BASE]
        t_positions = [p for p in positions if p.position_type == PositionType.T_PLUS_ZERO]
        
        print(f"✓ 小资金策略验证:")
        print(f"  底仓数量: {len(base_positions)} (应该=1)")
        print(f"  T仓数量: {len(t_positions)} (应该≤1)")
        print(f"  集中度: {max(p.market_value for p in positions) / portfolio_summary.total_market_value:.2%} (应该>70%)")
        
        # 断言验证
        self.assertEqual(len(base_positions), 1, "小资金模式应该只有一个底仓")
        self.assertLessEqual(len(t_positions), 1, "T仓数量不应超过1个")
        
        if portfolio_summary.total_market_value > 0:
            max_position_pct = max(p.market_value for p in positions) / portfolio_summary.total_market_value
            self.assertGreater(max_position_pct, 0.7, "小资金模式应该高度集中")
        
        print("✓ 综合工作流程测试通过")


def run_position_management_test():
    """运行仓位管理系统测试"""
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试用例
    test_suite.addTest(TestPositionManagement('test_01_position_creation_and_management'))
    test_suite.addTest(TestPositionManagement('test_02_order_integration'))
    test_suite.addTest(TestPositionManagement('test_03_risk_monitoring'))
    test_suite.addTest(TestPositionManagement('test_04_position_optimization'))
    test_suite.addTest(TestPositionManagement('test_05_position_analysis'))
    test_suite.addTest(TestPositionManagement('test_06_comprehensive_workflow'))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 返回测试结果
    return result.wasSuccessful()


if __name__ == '__main__':
    success = run_position_management_test()
    if success:
        print("\n🎉 仓位管理系统集成测试全部通过！")
    else:
        print("\n❌ 仓位管理系统集成测试存在失败项目")