#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资金管理系统 - Capital Management System

实现小资金专用配置管理、全仓单股策略风险控制、资金使用效率优化算法和资金流水成本分析系统

Author: Qlib Trading System
Date: 2025-01-30
"""

import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from enum import Enum
import pandas as pd
import numpy as np
from decimal import Decimal, ROUND_HALF_UP

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CapitalMode(Enum):
    """资金模式枚举"""
    SMALL = "small"      # 小资金模式 (<50万)
    MEDIUM = "medium"    # 中等资金模式 (50-500万)
    LARGE = "large"      # 大资金模式 (>500万)


class PositionType(Enum):
    """持仓类型枚举"""
    BASE = "base"        # 底仓
    T_POSITION = "t"     # T仓
    CASH = "cash"        # 现金


class TransactionType(Enum):
    """交易类型枚举"""
    BUY_BASE = "buy_base"           # 买入底仓
    SELL_BASE = "sell_base"         # 卖出底仓
    BUY_T = "buy_t"                 # 买入T仓
    SELL_T = "sell_t"               # 卖出T仓
    DIVIDEND = "dividend"           # 分红
    TRANSFER_IN = "transfer_in"     # 转入资金
    TRANSFER_OUT = "transfer_out"   # 转出资金


@dataclass
class CapitalConfig:
    """资金配置类"""
    total_capital: float                    # 总资金
    mode: CapitalMode                      # 资金模式
    max_stocks: int                        # 最大持股数量
    base_position_ratio: float             # 底仓比例
    t_position_ratio: float                # 做T仓位比例
    cash_reserve_ratio: float              # 现金储备比例
    max_single_stock_ratio: float          # 单股最大仓位比例
    max_daily_loss_ratio: float            # 单日最大亏损比例
    max_drawdown_ratio: float              # 最大回撤比例
    leverage_ratio: float                  # 杠杆比例
    
    def __post_init__(self):
        """初始化后验证配置合理性"""
        total_ratio = self.base_position_ratio + self.t_position_ratio + self.cash_reserve_ratio
        if abs(total_ratio - 1.0) > 0.01:
            raise ValueError(f"资金配置比例总和应为1.0，当前为{total_ratio}")
        
        if self.max_single_stock_ratio > 1.0:
            raise ValueError(f"单股最大仓位比例不能超过1.0，当前为{self.max_single_stock_ratio}")


@dataclass
class Transaction:
    """交易记录类"""
    transaction_id: str                    # 交易ID
    timestamp: datetime                    # 交易时间
    symbol: str                           # 股票代码
    transaction_type: TransactionType     # 交易类型
    quantity: int                         # 数量
    price: float                          # 价格
    amount: float                         # 金额
    commission: float                     # 手续费
    tax: float                           # 税费
    net_amount: float                    # 净金额
    balance_after: float                 # 交易后余额
    notes: str = ""                      # 备注
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        result = asdict(self)
        result['timestamp'] = self.timestamp.isoformat()
        result['transaction_type'] = self.transaction_type.value
        return result


@dataclass
class Position:
    """持仓信息类"""
    symbol: str                          # 股票代码
    position_type: PositionType          # 持仓类型
    quantity: int                        # 持仓数量
    avg_cost: float                      # 平均成本
    current_price: float                 # 当前价格
    market_value: float                  # 市值
    unrealized_pnl: float               # 浮动盈亏
    unrealized_pnl_pct: float           # 浮动盈亏比例
    last_update: datetime               # 最后更新时间
    
    def update_price(self, new_price: float):
        """更新价格和相关计算"""
        self.current_price = new_price
        self.market_value = self.quantity * new_price
        self.unrealized_pnl = self.market_value - (self.quantity * self.avg_cost)
        self.unrealized_pnl_pct = self.unrealized_pnl / (self.quantity * self.avg_cost) if self.avg_cost > 0 else 0
        self.last_update = datetime.now()


class CapitalManager:
    """资金管理器主类"""
    
    def __init__(self, config: CapitalConfig):
        """
        初始化资金管理器
        
        Args:
            config: 资金配置
        """
        self.config = config
        self.total_capital = config.total_capital
        self.available_cash = config.total_capital * config.cash_reserve_ratio
        self.positions: Dict[str, Position] = {}
        self.transactions: List[Transaction] = []
        self.daily_pnl_history: List[Dict] = []
        self.cost_analysis_cache: Dict = {}
        
        # 风险控制参数
        self.daily_loss = 0.0
        self.max_drawdown = 0.0
        self.peak_value = config.total_capital
        
        logger.info(f"资金管理器初始化完成 - 模式: {config.mode.value}, 总资金: {config.total_capital:,.2f}")
    
    def get_capital_allocation(self) -> Dict[str, float]:
        """
        获取资金分配情况
        
        Returns:
            资金分配字典
        """
        base_allocation = self.total_capital * self.config.base_position_ratio
        t_allocation = self.total_capital * self.config.t_position_ratio
        cash_allocation = self.total_capital * self.config.cash_reserve_ratio
        
        return {
            "total_capital": self.total_capital,
            "base_allocation": base_allocation,
            "t_allocation": t_allocation,
            "cash_allocation": cash_allocation,
            "available_cash": self.available_cash,
            "used_base": sum(pos.market_value for pos in self.positions.values() if pos.position_type == PositionType.BASE),
            "used_t": sum(pos.market_value for pos in self.positions.values() if pos.position_type == PositionType.T_POSITION)
        }
    
    def can_buy(self, symbol: str, quantity: int, price: float, position_type: PositionType) -> Tuple[bool, str]:
        """
        检查是否可以买入
        
        Args:
            symbol: 股票代码
            quantity: 数量
            price: 价格
            position_type: 持仓类型
            
        Returns:
            (是否可以买入, 原因)
        """
        required_amount = quantity * price
        commission = self._calculate_commission(required_amount, "buy")
        total_cost = required_amount + commission
        
        # 检查现金是否充足
        if total_cost > self.available_cash:
            return False, f"现金不足: 需要{total_cost:,.2f}, 可用{self.available_cash:,.2f}"
        
        # 检查单股持仓限制
        current_position_value = 0
        if symbol in self.positions:
            current_position_value = self.positions[symbol].market_value
        
        new_position_value = current_position_value + required_amount
        max_single_position = self.total_capital * self.config.max_single_stock_ratio
        
        if new_position_value > max_single_position:
            return False, f"超过单股最大仓位限制: {new_position_value:,.2f} > {max_single_position:,.2f}"
        
        # 检查持股数量限制
        if symbol not in self.positions and len(self.positions) >= self.config.max_stocks:
            return False, f"超过最大持股数量限制: {self.config.max_stocks}"
        
        # 检查日内亏损限制
        max_daily_loss = self.total_capital * self.config.max_daily_loss_ratio
        if self.daily_loss > max_daily_loss:
            return False, f"超过单日最大亏损限制: {self.daily_loss:,.2f} > {max_daily_loss:,.2f}"
        
        return True, "可以买入"
    
    def can_sell(self, symbol: str, quantity: int, position_type: PositionType) -> Tuple[bool, str]:
        """
        检查是否可以卖出
        
        Args:
            symbol: 股票代码
            quantity: 数量
            position_type: 持仓类型
            
        Returns:
            (是否可以卖出, 原因)
        """
        if symbol not in self.positions:
            return False, f"没有持仓: {symbol}"
        
        position = self.positions[symbol]
        if position.position_type != position_type:
            return False, f"持仓类型不匹配: 期望{position_type.value}, 实际{position.position_type.value}"
        
        if position.quantity < quantity:
            return False, f"持仓数量不足: 需要{quantity}, 持有{position.quantity}"
        
        return True, "可以卖出"
    
    def execute_buy(self, symbol: str, quantity: int, price: float, position_type: PositionType, notes: str = "") -> bool:
        """
        执行买入操作
        
        Args:
            symbol: 股票代码
            quantity: 数量
            price: 价格
            position_type: 持仓类型
            notes: 备注
            
        Returns:
            是否执行成功
        """
        can_buy, reason = self.can_buy(symbol, quantity, price, position_type)
        if not can_buy:
            logger.warning(f"买入失败: {symbol} - {reason}")
            return False
        
        amount = quantity * price
        commission = self._calculate_commission(amount, "buy")
        tax = 0  # 买入不收印花税
        net_amount = amount + commission + tax
        
        # 更新现金
        self.available_cash -= net_amount
        
        # 更新持仓
        if symbol in self.positions:
            position = self.positions[symbol]
            total_cost = position.quantity * position.avg_cost + amount
            total_quantity = position.quantity + quantity
            position.avg_cost = total_cost / total_quantity
            position.quantity = total_quantity
            position.market_value = total_quantity * price
        else:
            self.positions[symbol] = Position(
                symbol=symbol,
                position_type=position_type,
                quantity=quantity,
                avg_cost=price,
                current_price=price,
                market_value=amount,
                unrealized_pnl=0,
                unrealized_pnl_pct=0,
                last_update=datetime.now()
            )
        
        # 记录交易
        transaction_type = TransactionType.BUY_BASE if position_type == PositionType.BASE else TransactionType.BUY_T
        transaction = Transaction(
            transaction_id=self._generate_transaction_id(),
            timestamp=datetime.now(),
            symbol=symbol,
            transaction_type=transaction_type,
            quantity=quantity,
            price=price,
            amount=amount,
            commission=commission,
            tax=tax,
            net_amount=net_amount,
            balance_after=self.available_cash,
            notes=notes
        )
        self.transactions.append(transaction)
        
        logger.info(f"买入成功: {symbol} {quantity}股 @{price:.2f} 类型:{position_type.value}")
        return True
    
    def execute_sell(self, symbol: str, quantity: int, price: float, position_type: PositionType, notes: str = "") -> bool:
        """
        执行卖出操作
        
        Args:
            symbol: 股票代码
            quantity: 数量
            price: 价格
            position_type: 持仓类型
            notes: 备注
            
        Returns:
            是否执行成功
        """
        can_sell, reason = self.can_sell(symbol, quantity, position_type)
        if not can_sell:
            logger.warning(f"卖出失败: {symbol} - {reason}")
            return False
        
        amount = quantity * price
        commission = self._calculate_commission(amount, "sell")
        tax = amount * 0.001  # 印花税0.1%
        net_amount = amount - commission - tax
        
        # 更新现金
        self.available_cash += net_amount
        
        # 更新持仓
        position = self.positions[symbol]
        position.quantity -= quantity
        position.market_value = position.quantity * price
        
        # 如果全部卖出，删除持仓
        if position.quantity == 0:
            del self.positions[symbol]
        
        # 记录交易
        transaction_type = TransactionType.SELL_BASE if position_type == PositionType.BASE else TransactionType.SELL_T
        transaction = Transaction(
            transaction_id=self._generate_transaction_id(),
            timestamp=datetime.now(),
            symbol=symbol,
            transaction_type=transaction_type,
            quantity=quantity,
            price=price,
            amount=amount,
            commission=commission,
            tax=tax,
            net_amount=net_amount,
            balance_after=self.available_cash,
            notes=notes
        )
        self.transactions.append(transaction)
        
        logger.info(f"卖出成功: {symbol} {quantity}股 @{price:.2f} 类型:{position_type.value}")
        return True
    
    def update_positions_price(self, price_data: Dict[str, float]):
        """
        更新持仓价格
        
        Args:
            price_data: 价格数据字典 {symbol: price}
        """
        for symbol, position in self.positions.items():
            if symbol in price_data:
                position.update_price(price_data[symbol])
        
        # 更新风险指标
        self._update_risk_metrics()
    
    def get_portfolio_summary(self) -> Dict:
        """
        获取投资组合摘要
        
        Returns:
            投资组合摘要字典
        """
        total_market_value = sum(pos.market_value for pos in self.positions.values())
        total_cost = sum(pos.quantity * pos.avg_cost for pos in self.positions.values())
        total_unrealized_pnl = sum(pos.unrealized_pnl for pos in self.positions.values())
        
        portfolio_value = total_market_value + self.available_cash
        total_return = (portfolio_value - self.total_capital) / self.total_capital
        
        return {
            "total_capital": self.total_capital,
            "available_cash": self.available_cash,
            "total_market_value": total_market_value,
            "portfolio_value": portfolio_value,
            "total_cost": total_cost,
            "total_unrealized_pnl": total_unrealized_pnl,
            "total_return": total_return,
            "total_return_pct": total_return * 100,
            "daily_loss": self.daily_loss,
            "max_drawdown": self.max_drawdown,
            "position_count": len(self.positions),
            "cash_ratio": self.available_cash / portfolio_value if portfolio_value > 0 else 0
        }
    
    def _calculate_commission(self, amount: float, side: str) -> float:
        """
        计算手续费
        
        Args:
            amount: 交易金额
            side: 买卖方向 ("buy" or "sell")
            
        Returns:
            手续费
        """
        # 佣金费率 0.03%，最低5元
        commission_rate = 0.0003
        commission = max(amount * commission_rate, 5.0)
        return commission
    
    def _generate_transaction_id(self) -> str:
        """生成交易ID"""
        return f"T{datetime.now().strftime('%Y%m%d%H%M%S')}{len(self.transactions):04d}"
    
    def _update_risk_metrics(self):
        """更新风险指标"""
        portfolio_value = sum(pos.market_value for pos in self.positions.values()) + self.available_cash
        
        # 更新峰值
        if portfolio_value > self.peak_value:
            self.peak_value = portfolio_value
        
        # 计算回撤
        current_drawdown = (self.peak_value - portfolio_value) / self.peak_value
        if current_drawdown > self.max_drawdown:
            self.max_drawdown = current_drawdown
        
        # 计算当日盈亏
        self.daily_loss = self.total_capital - portfolio_value if portfolio_value < self.total_capital else 0
    
    def calculate_capital_efficiency(self) -> Dict:
        """
        计算资金使用效率
        
        Returns:
            资金效率指标字典
        """
        portfolio_summary = self.get_portfolio_summary()
        
        # 资金利用率
        capital_utilization = (portfolio_summary["total_market_value"] / self.total_capital) * 100
        
        # 现金闲置率
        cash_idle_rate = (self.available_cash / self.total_capital) * 100
        
        # 持仓集中度
        if self.positions:
            position_values = [pos.market_value for pos in self.positions.values()]
            max_position_value = max(position_values)
            concentration_ratio = (max_position_value / portfolio_summary["total_market_value"]) * 100
        else:
            concentration_ratio = 0
        
        # 换手率计算（基于最近30天交易）
        recent_transactions = [t for t in self.transactions 
                             if t.timestamp > datetime.now() - timedelta(days=30)]
        total_turnover = sum(t.amount for t in recent_transactions)
        turnover_rate = (total_turnover / self.total_capital) * 100 if self.total_capital > 0 else 0
        
        # T+0效率计算
        t_transactions = [t for t in recent_transactions 
                         if t.transaction_type in [TransactionType.BUY_T, TransactionType.SELL_T]]
        t_profit = sum(t.net_amount for t in t_transactions if t.transaction_type == TransactionType.SELL_T) - \
                  sum(t.net_amount for t in t_transactions if t.transaction_type == TransactionType.BUY_T)
        t_efficiency = (t_profit / self.total_capital) * 100 if self.total_capital > 0 else 0
        
        return {
            "capital_utilization": capital_utilization,
            "cash_idle_rate": cash_idle_rate,
            "concentration_ratio": concentration_ratio,
            "turnover_rate": turnover_rate,
            "t_efficiency": t_efficiency,
            "efficiency_score": self._calculate_efficiency_score(capital_utilization, cash_idle_rate, t_efficiency)
        }
    
    def _calculate_efficiency_score(self, utilization: float, idle_rate: float, t_efficiency: float) -> float:
        """
        计算综合效率评分
        
        Args:
            utilization: 资金利用率
            idle_rate: 现金闲置率
            t_efficiency: T+0效率
            
        Returns:
            效率评分 (0-100)
        """
        # 权重分配
        utilization_weight = 0.4
        idle_penalty_weight = 0.3
        t_efficiency_weight = 0.3
        
        # 利用率评分 (目标90-95%)
        utilization_score = 100 if 90 <= utilization <= 95 else max(0, 100 - abs(utilization - 92.5) * 2)
        
        # 闲置率惩罚 (目标<10%)
        idle_penalty = max(0, idle_rate - 10) * 5
        
        # T+0效率奖励
        t_bonus = min(t_efficiency * 10, 30)  # 最高30分奖励
        
        efficiency_score = (utilization_score * utilization_weight - 
                          idle_penalty * idle_penalty_weight + 
                          t_bonus * t_efficiency_weight)
        
        return max(0, min(100, efficiency_score))
    
    def optimize_capital_allocation(self, target_stocks: List[str], stock_scores: Dict[str, float]) -> Dict:
        """
        优化资金分配
        
        Args:
            target_stocks: 目标股票列表
            stock_scores: 股票评分字典
            
        Returns:
            优化后的资金分配方案
        """
        if not target_stocks or not stock_scores:
            return {"error": "目标股票列表或评分为空"}
        
        # 根据资金模式确定分配策略
        if self.config.mode == CapitalMode.SMALL:
            return self._optimize_small_capital_allocation(target_stocks, stock_scores)
        elif self.config.mode == CapitalMode.MEDIUM:
            return self._optimize_medium_capital_allocation(target_stocks, stock_scores)
        else:
            return self._optimize_large_capital_allocation(target_stocks, stock_scores)
    
    def _optimize_small_capital_allocation(self, target_stocks: List[str], stock_scores: Dict[str, float]) -> Dict:
        """小资金模式优化分配"""
        # 选择评分最高的股票全仓
        best_stock = max(target_stocks, key=lambda x: stock_scores.get(x, 0))
        
        base_allocation = self.total_capital * self.config.base_position_ratio
        t_allocation = self.total_capital * self.config.t_position_ratio
        
        return {
            "strategy": "全仓单股+做T",
            "allocations": {
                best_stock: {
                    "base_amount": base_allocation,
                    "t_amount": t_allocation,
                    "total_amount": base_allocation + t_allocation,
                    "score": stock_scores.get(best_stock, 0)
                }
            },
            "cash_reserve": self.total_capital * self.config.cash_reserve_ratio,
            "expected_efficiency": 95.0  # 预期效率
        }
    
    def _optimize_medium_capital_allocation(self, target_stocks: List[str], stock_scores: Dict[str, float]) -> Dict:
        """中等资金模式优化分配"""
        # 选择评分前2-3只股票分配
        sorted_stocks = sorted(target_stocks, key=lambda x: stock_scores.get(x, 0), reverse=True)[:3]
        
        total_investable = self.total_capital * (self.config.base_position_ratio + self.config.t_position_ratio)
        
        # 按评分权重分配
        total_score = sum(stock_scores.get(stock, 0) for stock in sorted_stocks)
        allocations = {}
        
        for stock in sorted_stocks:
            score = stock_scores.get(stock, 0)
            weight = score / total_score if total_score > 0 else 1.0 / len(sorted_stocks)
            allocation = total_investable * weight
            
            allocations[stock] = {
                "base_amount": allocation * 0.7,  # 70%做底仓
                "t_amount": allocation * 0.3,     # 30%做T
                "total_amount": allocation,
                "weight": weight,
                "score": score
            }
        
        return {
            "strategy": "多股分散+做T",
            "allocations": allocations,
            "cash_reserve": self.total_capital * self.config.cash_reserve_ratio,
            "expected_efficiency": 85.0
        }
    
    def _optimize_large_capital_allocation(self, target_stocks: List[str], stock_scores: Dict[str, float]) -> Dict:
        """大资金模式优化分配"""
        # 选择评分前5-10只股票分配
        sorted_stocks = sorted(target_stocks, key=lambda x: stock_scores.get(x, 0), reverse=True)[:10]
        
        total_investable = self.total_capital * (self.config.base_position_ratio + self.config.t_position_ratio)
        
        # 使用风险平价模型分配
        allocations = {}
        equal_weight = 1.0 / len(sorted_stocks)
        
        for stock in sorted_stocks:
            score = stock_scores.get(stock, 0)
            # 结合评分和风险平价
            risk_adjusted_weight = equal_weight * (1 + score * 0.2)  # 评分调整权重
            allocation = total_investable * risk_adjusted_weight
            
            allocations[stock] = {
                "base_amount": allocation * 0.8,  # 80%做底仓
                "t_amount": allocation * 0.2,     # 20%做T
                "total_amount": allocation,
                "weight": risk_adjusted_weight,
                "score": score
            }
        
        return {
            "strategy": "分散投资+适度做T",
            "allocations": allocations,
            "cash_reserve": self.total_capital * self.config.cash_reserve_ratio,
            "expected_efficiency": 75.0
        }


class CashFlowAnalyzer:
    """资金流水分析器"""
    
    def __init__(self, capital_manager: CapitalManager):
        """
        初始化资金流水分析器
        
        Args:
            capital_manager: 资金管理器实例
        """
        self.capital_manager = capital_manager
        self.logger = logging.getLogger(__name__)
    
    def generate_cash_flow_report(self, start_date: datetime = None, end_date: datetime = None) -> Dict:
        """
        生成资金流水报告
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            资金流水报告
        """
        if start_date is None:
            start_date = datetime.now() - timedelta(days=30)
        if end_date is None:
            end_date = datetime.now()
        
        # 筛选时间范围内的交易
        transactions = [t for t in self.capital_manager.transactions 
                       if start_date <= t.timestamp <= end_date]
        
        if not transactions:
            return {"error": "指定时间范围内无交易记录"}
        
        # 按日期分组统计
        daily_flows = {}
        for transaction in transactions:
            date_key = transaction.timestamp.strftime('%Y-%m-%d')
            if date_key not in daily_flows:
                daily_flows[date_key] = {
                    "buy_amount": 0,
                    "sell_amount": 0,
                    "commission": 0,
                    "tax": 0,
                    "net_flow": 0,
                    "transaction_count": 0
                }
            
            daily_flow = daily_flows[date_key]
            daily_flow["transaction_count"] += 1
            daily_flow["commission"] += transaction.commission
            daily_flow["tax"] += transaction.tax
            
            if transaction.transaction_type in [TransactionType.BUY_BASE, TransactionType.BUY_T]:
                daily_flow["buy_amount"] += transaction.amount
                daily_flow["net_flow"] -= transaction.net_amount
            else:
                daily_flow["sell_amount"] += transaction.amount
                daily_flow["net_flow"] += transaction.net_amount
        
        # 计算汇总统计
        total_buy = sum(t.amount for t in transactions 
                       if t.transaction_type in [TransactionType.BUY_BASE, TransactionType.BUY_T])
        total_sell = sum(t.amount for t in transactions 
                        if t.transaction_type in [TransactionType.SELL_BASE, TransactionType.SELL_T])
        total_commission = sum(t.commission for t in transactions)
        total_tax = sum(t.tax for t in transactions)
        
        return {
            "period": f"{start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}",
            "summary": {
                "total_buy_amount": total_buy,
                "total_sell_amount": total_sell,
                "total_commission": total_commission,
                "total_tax": total_tax,
                "net_cash_flow": total_sell - total_buy - total_commission - total_tax,
                "transaction_count": len(transactions),
                "avg_transaction_size": (total_buy + total_sell) / len(transactions) if transactions else 0
            },
            "daily_flows": daily_flows,
            "cost_analysis": self._analyze_transaction_costs(transactions)
        }
    
    def _analyze_transaction_costs(self, transactions: List[Transaction]) -> Dict:
        """
        分析交易成本
        
        Args:
            transactions: 交易列表
            
        Returns:
            成本分析结果
        """
        if not transactions:
            return {}
        
        total_amount = sum(t.amount for t in transactions)
        total_commission = sum(t.commission for t in transactions)
        total_tax = sum(t.tax for t in transactions)
        total_cost = total_commission + total_tax
        
        # 成本率分析
        commission_rate = (total_commission / total_amount) * 100 if total_amount > 0 else 0
        tax_rate = (total_tax / total_amount) * 100 if total_amount > 0 else 0
        total_cost_rate = (total_cost / total_amount) * 100 if total_amount > 0 else 0
        
        # 按交易类型分析
        cost_by_type = {}
        for transaction_type in TransactionType:
            type_transactions = [t for t in transactions if t.transaction_type == transaction_type]
            if type_transactions:
                type_amount = sum(t.amount for t in type_transactions)
                type_cost = sum(t.commission + t.tax for t in type_transactions)
                cost_by_type[transaction_type.value] = {
                    "count": len(type_transactions),
                    "amount": type_amount,
                    "cost": type_cost,
                    "cost_rate": (type_cost / type_amount) * 100 if type_amount > 0 else 0
                }
        
        return {
            "total_cost": total_cost,
            "commission_rate": commission_rate,
            "tax_rate": tax_rate,
            "total_cost_rate": total_cost_rate,
            "cost_by_type": cost_by_type,
            "cost_optimization_suggestions": self._generate_cost_optimization_suggestions(total_cost_rate, commission_rate)
        }
    
    def _generate_cost_optimization_suggestions(self, total_cost_rate: float, commission_rate: float) -> List[str]:
        """
        生成成本优化建议
        
        Args:
            total_cost_rate: 总成本率
            commission_rate: 佣金率
            
        Returns:
            优化建议列表
        """
        suggestions = []
        
        if total_cost_rate > 0.5:
            suggestions.append("交易成本过高，建议减少交易频率")
        
        if commission_rate > 0.03:
            suggestions.append("佣金率偏高，建议更换券商或协商降低佣金")
        
        if total_cost_rate > 0.3:
            suggestions.append("考虑增加单笔交易金额以摊薄固定成本")
        
        suggestions.append("优化T+0策略，提高单次交易盈利率")
        suggestions.append("合理控制交易频率，避免过度交易")
        
        return suggestions


class CostAnalyzer:
    """成本分析器"""
    
    def __init__(self, capital_manager: CapitalManager):
        """
        初始化成本分析器
        
        Args:
            capital_manager: 资金管理器实例
        """
        self.capital_manager = capital_manager
        self.logger = logging.getLogger(__name__)
    
    def calculate_position_cost_basis(self, symbol: str) -> Dict:
        """
        计算持仓成本基础
        
        Args:
            symbol: 股票代码
            
        Returns:
            成本分析结果
        """
        if symbol not in self.capital_manager.positions:
            return {"error": f"未找到股票 {symbol} 的持仓"}
        
        position = self.capital_manager.positions[symbol]
        
        # 获取该股票的所有交易记录
        stock_transactions = [t for t in self.capital_manager.transactions if t.symbol == symbol]
        
        if not stock_transactions:
            return {"error": f"未找到股票 {symbol} 的交易记录"}
        
        # 分别计算买入和卖出
        buy_transactions = [t for t in stock_transactions 
                          if t.transaction_type in [TransactionType.BUY_BASE, TransactionType.BUY_T]]
        sell_transactions = [t for t in stock_transactions 
                           if t.transaction_type in [TransactionType.SELL_BASE, TransactionType.SELL_T]]
        
        # 计算加权平均成本
        total_buy_amount = sum(t.amount for t in buy_transactions)
        total_buy_quantity = sum(t.quantity for t in buy_transactions)
        total_sell_quantity = sum(t.quantity for t in sell_transactions)
        
        if total_buy_quantity == 0:
            return {"error": "没有买入交易记录"}
        
        weighted_avg_cost = total_buy_amount / total_buy_quantity
        
        # 计算T+0收益
        t_buy_amount = sum(t.amount for t in buy_transactions if t.transaction_type == TransactionType.BUY_T)
        t_sell_amount = sum(t.net_amount for t in sell_transactions if t.transaction_type == TransactionType.SELL_T)
        t_profit = t_sell_amount - t_buy_amount
        
        # 计算持仓成本优化
        if t_profit > 0 and position.quantity > 0:
            optimized_cost = position.avg_cost - (t_profit / position.quantity)
        else:
            optimized_cost = position.avg_cost
        
        return {
            "symbol": symbol,
            "current_position": {
                "quantity": position.quantity,
                "avg_cost": position.avg_cost,
                "current_price": position.current_price,
                "market_value": position.market_value,
                "unrealized_pnl": position.unrealized_pnl,
                "unrealized_pnl_pct": position.unrealized_pnl_pct
            },
            "cost_analysis": {
                "weighted_avg_cost": weighted_avg_cost,
                "optimized_cost": optimized_cost,
                "cost_reduction": position.avg_cost - optimized_cost,
                "cost_reduction_pct": ((position.avg_cost - optimized_cost) / position.avg_cost) * 100 if position.avg_cost > 0 else 0
            },
            "t_trading_analysis": {
                "t_buy_amount": t_buy_amount,
                "t_sell_amount": t_sell_amount,
                "t_profit": t_profit,
                "t_profit_pct": (t_profit / t_buy_amount) * 100 if t_buy_amount > 0 else 0,
                "t_transaction_count": len([t for t in stock_transactions if 'T' in t.transaction_type.value])
            },
            "transaction_summary": {
                "total_buy_transactions": len(buy_transactions),
                "total_sell_transactions": len(sell_transactions),
                "total_buy_amount": total_buy_amount,
                "total_buy_quantity": total_buy_quantity,
                "total_sell_quantity": total_sell_quantity,
                "avg_buy_price": total_buy_amount / total_buy_quantity if total_buy_quantity > 0 else 0
            }
        }
    
    def generate_cost_optimization_report(self) -> Dict:
        """
        生成成本优化报告
        
        Returns:
            成本优化报告
        """
        if not self.positions:
            return {"error": "当前无持仓"}
        
        optimization_report = {
            "report_time": datetime.now().isoformat(),
            "portfolio_summary": self.get_portfolio_summary(),
            "position_analysis": {},
            "optimization_suggestions": []
        }
        
        total_cost_reduction = 0
        total_t_profit = 0
        
        for symbol, position in self.positions.items():
            cost_analysis = self.calculate_position_cost_basis(symbol)
            if "error" not in cost_analysis:
                optimization_report["position_analysis"][symbol] = cost_analysis
                total_cost_reduction += cost_analysis["cost_analysis"]["cost_reduction"] * position.quantity
                total_t_profit += cost_analysis["t_trading_analysis"]["t_profit"]
        
        # 生成优化建议
        suggestions = []
        
        if total_t_profit > 0:
            suggestions.append(f"T+0交易已产生{total_t_profit:.2f}元收益，有效降低了持仓成本")
        
        if total_cost_reduction > 0:
            suggestions.append(f"通过做T操作，总计降低成本{total_cost_reduction:.2f}元")
        
        # 检查资金使用效率
        efficiency = self.calculate_capital_efficiency()
        if efficiency["capital_utilization"] < 80:
            suggestions.append("资金利用率偏低，建议增加仓位或优化资金配置")
        
        if efficiency["cash_idle_rate"] > 15:
            suggestions.append("现金闲置率过高，建议将部分现金用于做T操作")
        
        optimization_report["optimization_suggestions"] = suggestions
        optimization_report["total_cost_reduction"] = total_cost_reduction
        optimization_report["total_t_profit"] = total_t_profit
        
        return optimization_report


class CapitalEfficiencyOptimizer:
    """资金使用效率优化器"""
    
    def __init__(self, capital_manager: CapitalManager):
        """
        初始化效率优化器
        
        Args:
            capital_manager: 资金管理器实例
        """
        self.capital_manager = capital_manager
        self.logger = logging.getLogger(__name__)
    
    def analyze_efficiency_bottlenecks(self) -> Dict:
        """
        分析效率瓶颈
        
        Returns:
            效率瓶颈分析结果
        """
        efficiency_metrics = self.capital_manager.calculate_capital_efficiency()
        portfolio_summary = self.capital_manager.get_portfolio_summary()
        
        bottlenecks = []
        recommendations = []
        
        # 分析资金利用率
        utilization = efficiency_metrics["capital_utilization"]
        if utilization < 70:
            bottlenecks.append("资金利用率过低")
            recommendations.append("建议增加底仓配置或寻找更多投资机会")
        elif utilization > 95:
            bottlenecks.append("资金利用率过高，缺乏灵活性")
            recommendations.append("建议保留适当现金储备以应对机会和风险")
        
        # 分析现金闲置率
        idle_rate = efficiency_metrics["cash_idle_rate"]
        if idle_rate > 20:
            bottlenecks.append("现金闲置率过高")
            recommendations.append("建议将闲置资金用于做T操作或寻找新的投资标的")
        
        # 分析持仓集中度
        concentration = efficiency_metrics["concentration_ratio"]
        if concentration > 90 and len(self.capital_manager.positions) == 1:
            recommendations.append("全仓单股策略风险较高，建议加强风险监控")
        elif concentration < 30 and len(self.capital_manager.positions) > 5:
            bottlenecks.append("持仓过于分散，可能影响收益")
            recommendations.append("建议集中投资于最优质的标的")
        
        # 分析换手率
        turnover = efficiency_metrics["turnover_rate"]
        if turnover > 500:
            bottlenecks.append("换手率过高，交易成本可能过大")
            recommendations.append("建议优化交易频率，减少不必要的交易")
        elif turnover < 50:
            bottlenecks.append("换手率过低，可能错失交易机会")
            recommendations.append("建议增加做T操作频率，提高资金周转效率")
        
        # 分析T+0效率
        t_efficiency = efficiency_metrics["t_efficiency"]
        if t_efficiency < 0:
            bottlenecks.append("T+0操作产生亏损")
            recommendations.append("建议优化做T策略或暂停做T操作")
        elif t_efficiency < 1:
            recommendations.append("T+0收益偏低，建议优化做T时机选择")
        
        return {
            "analysis_time": datetime.now().isoformat(),
            "efficiency_score": efficiency_metrics["efficiency_score"],
            "bottlenecks": bottlenecks,
            "recommendations": recommendations,
            "detailed_metrics": efficiency_metrics,
            "optimization_priority": self._calculate_optimization_priority(bottlenecks)
        }
    
    def _calculate_optimization_priority(self, bottlenecks: List[str]) -> List[Dict]:
        """
        计算优化优先级
        
        Args:
            bottlenecks: 瓶颈列表
            
        Returns:
            优化优先级列表
        """
        priority_map = {
            "资金利用率过低": {"priority": 1, "impact": "高", "difficulty": "中"},
            "现金闲置率过高": {"priority": 2, "impact": "中", "difficulty": "低"},
            "换手率过高": {"priority": 3, "impact": "中", "difficulty": "中"},
            "T+0操作产生亏损": {"priority": 1, "impact": "高", "difficulty": "高"},
            "持仓过于分散": {"priority": 2, "impact": "中", "difficulty": "中"},
            "换手率过低": {"priority": 3, "impact": "低", "difficulty": "低"}
        }
        
        priorities = []
        for bottleneck in bottlenecks:
            if bottleneck in priority_map:
                priority_info = priority_map[bottleneck].copy()
                priority_info["issue"] = bottleneck
                priorities.append(priority_info)
        
        # 按优先级排序
        priorities.sort(key=lambda x: x["priority"])
        return priorities
    
    def generate_optimization_plan(self) -> Dict:
        """
        生成优化计划
        
        Returns:
            优化计划
        """
        bottleneck_analysis = self.analyze_efficiency_bottlenecks()
        current_config = self.capital_manager.config
        
        optimization_plan = {
            "plan_id": f"OPT_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "created_time": datetime.now().isoformat(),
            "current_efficiency_score": bottleneck_analysis["efficiency_score"],
            "target_efficiency_score": min(bottleneck_analysis["efficiency_score"] + 20, 100),
            "optimization_actions": [],
            "expected_improvements": {},
            "implementation_timeline": {}
        }
        
        # 根据瓶颈生成具体行动计划
        for priority_item in bottleneck_analysis["optimization_priority"]:
            issue = priority_item["issue"]
            
            if "资金利用率过低" in issue:
                optimization_plan["optimization_actions"].append({
                    "action": "提高资金利用率",
                    "details": "将现金储备比例从{:.1%}调整至{:.1%}".format(
                        current_config.cash_reserve_ratio,
                        max(0.02, current_config.cash_reserve_ratio - 0.05)
                    ),
                    "expected_impact": "提高5-10%资金利用率",
                    "timeline": "立即执行"
                })
            
            elif "现金闲置率过高" in issue:
                optimization_plan["optimization_actions"].append({
                    "action": "减少现金闲置",
                    "details": "增加做T操作频率，将部分现金用于短期交易",
                    "expected_impact": "降低现金闲置率至10%以下",
                    "timeline": "1周内实施"
                })
            
            elif "T+0操作产生亏损" in issue:
                optimization_plan["optimization_actions"].append({
                    "action": "优化T+0策略",
                    "details": "暂停做T操作，重新评估做T时机和策略",
                    "expected_impact": "避免进一步亏损，重建T+0盈利能力",
                    "timeline": "立即执行"
                })
            
            elif "换手率过高" in issue:
                optimization_plan["optimization_actions"].append({
                    "action": "控制交易频率",
                    "details": "将每日最大做T次数从{}次降至{}次".format(
                        getattr(current_config, 'max_t_times_per_day', 5),
                        max(2, getattr(current_config, 'max_t_times_per_day', 5) - 2)
                    ),
                    "expected_impact": "降低交易成本，提高净收益",
                    "timeline": "立即执行"
                })
        
        return optimization_plan
    
    def implement_optimization(self, optimization_plan: Dict) -> Dict:
        """
        实施优化计划
        
        Args:
            optimization_plan: 优化计划
            
        Returns:
            实施结果
        """
        implementation_results = {
            "plan_id": optimization_plan["plan_id"],
            "implementation_time": datetime.now().isoformat(),
            "executed_actions": [],
            "failed_actions": [],
            "overall_success": True
        }
        
        for action in optimization_plan["optimization_actions"]:
            try:
                success = self._execute_optimization_action(action)
                if success:
                    implementation_results["executed_actions"].append(action)
                    self.logger.info(f"优化行动执行成功: {action['action']}")
                else:
                    implementation_results["failed_actions"].append(action)
                    self.logger.warning(f"优化行动执行失败: {action['action']}")
            except Exception as e:
                implementation_results["failed_actions"].append({
                    **action,
                    "error": str(e)
                })
                self.logger.error(f"优化行动执行异常: {action['action']} - {e}")
        
        implementation_results["overall_success"] = len(implementation_results["failed_actions"]) == 0
        
        return implementation_results
    
    def _execute_optimization_action(self, action: Dict) -> bool:
        """
        执行具体的优化行动
        
        Args:
            action: 优化行动
            
        Returns:
            是否执行成功
        """
        action_type = action["action"]
        
        if "提高资金利用率" in action_type:
            # 调整现金储备比例
            new_cash_ratio = max(0.02, self.capital_manager.config.cash_reserve_ratio - 0.05)
            self.capital_manager.config.cash_reserve_ratio = new_cash_ratio
            return True
        
        elif "减少现金闲置" in action_type:
            # 这里可以触发更积极的做T策略
            # 实际实现中可能需要调用交易执行模块
            self.logger.info("触发更积极的做T策略")
            return True
        
        elif "优化T+0策略" in action_type:
            # 暂停做T操作的标志
            # 实际实现中需要在交易执行模块中检查这个标志
            self.logger.info("暂停T+0操作，等待策略优化")
            return True
        
        elif "控制交易频率" in action_type:
            # 降低做T频率
            if hasattr(self.capital_manager.config, 'max_t_times_per_day'):
                current_max = self.capital_manager.config.max_t_times_per_day
                self.capital_manager.config.max_t_times_per_day = max(2, current_max - 2)
            return True
        
        return False


# 测试代码
if __name__ == "__main__":
    # 创建测试配置
    from qlib_trading_system.trading.small_capital_config import SmallCapitalConfig, RiskLevel, CapitalMode
    
    test_config = CapitalConfig(
        total_capital=100000.0,
        mode=CapitalMode.SMALL,
        max_stocks=1,
        base_position_ratio=0.75,
        t_position_ratio=0.20,
        cash_reserve_ratio=0.05,
        max_single_stock_ratio=1.0,
        max_daily_loss_ratio=0.03,
        max_drawdown_ratio=0.15,
        leverage_ratio=1.0
    )
    
    # 创建资金管理器
    manager = CapitalManager(test_config)
    
    print("=== 资金管理系统测试 ===")
    
    # 测试买入操作
    success = manager.execute_buy("000001.SZ", 1000, 10.50, PositionType.BASE, "建立底仓")
    print(f"买入底仓: {success}")
    
    # 测试做T买入
    success = manager.execute_buy("000001.SZ", 500, 10.30, PositionType.T_POSITION, "做T买入")
    print(f"做T买入: {success}")
    
    # 更新价格
    manager.update_positions_price({"000001.SZ": 10.80})
    
    # 测试做T卖出
    success = manager.execute_sell("000001.SZ", 500, 10.85, PositionType.T_POSITION, "做T卖出")
    print(f"做T卖出: {success}")
    
    # 获取投资组合摘要
    summary = manager.get_portfolio_summary()
    print(f"投资组合摘要: {summary}")
    
    # 测试资金效率计算
    efficiency = manager.calculate_capital_efficiency()
    print(f"资金效率: {efficiency}")
    
    # 测试成本分析
    cost_analysis = manager.calculate_position_cost_basis("000001.SZ")
    print(f"成本分析: {cost_analysis}")
    
    # 测试资金流水分析
    cash_flow_analyzer = CashFlowAnalyzer(manager)
    cash_flow_report = cash_flow_analyzer.generate_cash_flow_report()
    print(f"资金流水报告: {cash_flow_report}")
    
    # 测试效率优化
    optimizer = CapitalEfficiencyOptimizer(manager)
    bottleneck_analysis = optimizer.analyze_efficiency_bottlenecks()
    print(f"效率瓶颈分析: {bottleneck_analysis}")
    
    optimization_plan = optimizer.generate_optimization_plan()
    print(f"优化计划: {optimization_plan}")
    
    print("=== 测试完成 ===")
        realized_pnl = total_sell_proceeds - sum(
            t.quantity * weighted_avg_cost for t in sell_transactions
        )
        
        # 计算成本降低效果（通过做T）
        t_buy_transactions = [t for t in buy_transactions if t.transaction_type == TransactionType.BUY_T]
        t_sell_transactions = [t for t in sell_transactions if t.transaction_type == TransactionType.SELL_T]
        
        t_profit = sum(t.net_amount for t in t_sell_transactions) - sum(t.net_amount for t in t_buy_transactions)
        cost_reduction_per_share = t_profit / position.quantity if position.quantity > 0 else 0
        
        return {
            "symbol": symbol,
            "current_position": {
                "quantity": position.quantity,
                "avg_cost": position.avg_cost,
                "current_price": position.current_price,
                "market_value": position.market_value,
                "unrealized_pnl": position.unrealized_pnl
            },
            "cost_analysis": {
                "weighted_avg_cost": weighted_avg_cost,
                "total_buy_cost": total_buy_cost,
                "total_sell_proceeds": total_sell_proceeds,
                "realized_pnl": realized_pnl,
                "cost_reduction_per_share": cost_reduction_per_share,
                "effective_cost": position.avg_cost - cost_reduction_per_share
            },
            "transaction_summary": {
                "total_transactions": len(stock_transactions),
                "buy_transactions": len(buy_transactions),
                "sell_transactions": len(sell_transactions),
                "t_transactions": len(t_buy_transactions) + len(t_sell_transactions),
                "t_profit": t_profit
            }
        }
    
    def calculate_portfolio_cost_analysis(self) -> Dict:
        """
        计算整个投资组合的成本分析
        
        Returns:
            投资组合成本分析结果
        """
        portfolio_analysis = {}
        total_cost_reduction = 0
        total_t_profit = 0
        
        for symbol in self.capital_manager.positions.keys():
            analysis = self.calculate_position_cost_basis(symbol)
            if "error" not in analysis:
                portfolio_analysis[symbol] = analysis
                total_cost_reduction += analysis["cost_analysis"]["cost_reduction_per_share"] * analysis["current_position"]["quantity"]
                total_t_profit += analysis["transaction_summary"]["t_profit"]
        
        # 计算整体效率指标
        portfolio_summary = self.capital_manager.get_portfolio_summary()
        cost_efficiency = (total_cost_reduction / self.capital_manager.total_capital) * 100 if self.capital_manager.total_capital > 0 else 0
        
        return {
            "portfolio_summary": portfolio_summary,
            "individual_analysis": portfolio_analysis,
            "overall_metrics": {
                "total_cost_reduction": total_cost_reduction,
                "total_t_profit": total_t_profit,
                "cost_efficiency_pct": cost_efficiency,
                "avg_cost_reduction_per_position": total_cost_reduction / len(portfolio_analysis) if portfolio_analysis else 0
            },
            "optimization_suggestions": self._generate_portfolio_optimization_suggestions(portfolio_analysis)
        }
    
    def _generate_portfolio_optimization_suggestions(self, portfolio_analysis: Dict) -> List[str]:
        """
        生成投资组合优化建议
        
        Args:
            portfolio_analysis: 投资组合分析结果
            
        Returns:
            优化建议列表
        """
        suggestions = []
        
        if not portfolio_analysis:
            return ["暂无持仓，无法生成优化建议"]
        
        # 分析T+0效率
        t_profits = [analysis["transaction_summary"]["t_profit"] for analysis in portfolio_analysis.values()]
        avg_t_profit = sum(t_profits) / len(t_profits) if t_profits else 0
        
        if avg_t_profit < 0:
            suggestions.append("T+0策略整体亏损，建议优化做T时机选择")
        elif avg_t_profit < 100:
            suggestions.append("T+0收益偏低，建议增加做T频率或提高单次收益率")
        
        # 分析成本控制
        cost_reductions = [analysis["cost_analysis"]["cost_reduction_per_share"] for analysis in portfolio_analysis.values()]
        avg_cost_reduction = sum(cost_reductions) / len(cost_reductions) if cost_reductions else 0
        
        if avg_cost_reduction <= 0:
            suggestions.append("成本控制效果不佳，建议优化买入时机")
        
        # 分析持仓集中度
        if len(portfolio_analysis) == 1:
            suggestions.append("当前为全仓单股策略，风险集中度较高，注意风险控制")
        
        suggestions.append("定期评估持仓股票基本面变化")
        suggestions.append("根据市场环境调整做T策略参数")
        
        return suggestions


class CapitalEfficiencyOptimizer:
    """资金使用效率优化器"""
    
    def __init__(self, capital_manager: CapitalManager):
        """
        初始化效率优化器
        
        Args:
            capital_manager: 资金管理器实例
        """
        self.capital_manager = capital_manager
        self.logger = logging.getLogger(__name__)
    
    def optimize_position_sizing(self, target_stocks: List[str], stock_scores: Dict[str, float], 
                                market_conditions: Dict) -> Dict:
        """
        优化仓位配置
        
        Args:
            target_stocks: 目标股票列表
            stock_scores: 股票评分
            market_conditions: 市场环境
            
        Returns:
            优化后的仓位配置
        """
        if not target_stocks or not stock_scores:
            return {"error": "目标股票或评分数据为空"}
        
        # 根据市场环境调整策略
        market_factor = self._calculate_market_factor(market_conditions)
        
        # 获取当前资金配置
        capital_allocation = self.capital_manager.get_capital_allocation()
        
        # 根据资金模式选择优化策略
        if self.capital_manager.config.mode == CapitalMode.SMALL:
            return self._optimize_small_capital_positions(target_stocks, stock_scores, market_factor)
        elif self.capital_manager.config.mode == CapitalMode.MEDIUM:
            return self._optimize_medium_capital_positions(target_stocks, stock_scores, market_factor)
        else:
            return self._optimize_large_capital_positions(target_stocks, stock_scores, market_factor)
    
    def _calculate_market_factor(self, market_conditions: Dict) -> float:
        """
        计算市场环境因子
        
        Args:
            market_conditions: 市场环境数据
            
        Returns:
            市场因子 (0.5-1.5)
        """
        # 默认市场因子
        market_factor = 1.0
        
        # 根据市场趋势调整
        if market_conditions.get("trend") == "bull":
            market_factor *= 1.2
        elif market_conditions.get("trend") == "bear":
            market_factor *= 0.8
        
        # 根据波动率调整
        volatility = market_conditions.get("volatility", 0.02)
        if volatility > 0.03:  # 高波动
            market_factor *= 1.1
        elif volatility < 0.01:  # 低波动
            market_factor *= 0.9
        
        return max(0.5, min(1.5, market_factor))
    
    def _optimize_small_capital_positions(self, target_stocks: List[str], stock_scores: Dict[str, float], 
                                        market_factor: float) -> Dict:
        """小资金模式仓位优化"""
        # 选择评分最高的股票
        best_stock = max(target_stocks, key=lambda x: stock_scores.get(x, 0))
        best_score = stock_scores.get(best_stock, 0)
        
        # 根据评分和市场环境调整仓位
        base_ratio = self.capital_manager.config.base_position_ratio
        t_ratio = self.capital_manager.config.t_position_ratio
        
        # 评分调整
        score_adjustment = (best_score - 0.5) * 0.2  # 评分0.5为中性
        adjusted_base_ratio = min(0.9, max(0.5, base_ratio + score_adjustment))
        adjusted_t_ratio = min(0.3, max(0.1, t_ratio + score_adjustment * 0.5))
        
        # 市场环境调整
        adjusted_base_ratio *= market_factor
        adjusted_t_ratio *= market_factor
        
        # 确保总比例不超过1
        total_investment_ratio = adjusted_base_ratio + adjusted_t_ratio
        if total_investment_ratio > 0.95:
            scale_factor = 0.95 / total_investment_ratio
            adjusted_base_ratio *= scale_factor
            adjusted_t_ratio *= scale_factor
        
        base_amount = self.capital_manager.total_capital * adjusted_base_ratio
        t_amount = self.capital_manager.total_capital * adjusted_t_ratio
        
        return {
            "strategy": "优化全仓单股+做T",
            "selected_stock": best_stock,
            "stock_score": best_score,
            "market_factor": market_factor,
            "position_config": {
                "base_amount": base_amount,
                "t_amount": t_amount,
                "total_amount": base_amount + t_amount,
                "base_ratio": adjusted_base_ratio,
                "t_ratio": adjusted_t_ratio
            },
            "expected_efficiency": min(98, 85 + best_score * 20),
            "risk_assessment": self._assess_single_stock_risk(best_stock, best_score)
        }
    
    def _optimize_medium_capital_positions(self, target_stocks: List[str], stock_scores: Dict[str, float], 
                                         market_factor: float) -> Dict:
        """中等资金模式仓位优化"""
        # 选择前3只股票
        sorted_stocks = sorted(target_stocks, key=lambda x: stock_scores.get(x, 0), reverse=True)[:3]
        
        total_investable = self.capital_manager.total_capital * 0.9  # 保留10%现金
        
        # 使用改进的权重分配算法
        weights = self._calculate_optimized_weights(sorted_stocks, stock_scores, market_factor)
        
        allocations = {}
        for stock, weight in weights.items():
            allocation = total_investable * weight
            allocations[stock] = {
                "base_amount": allocation * 0.7,
                "t_amount": allocation * 0.3,
                "total_amount": allocation,
                "weight": weight,
                "score": stock_scores.get(stock, 0)
            }
        
        return {
            "strategy": "优化多股分散+做T",
            "allocations": allocations,
            "market_factor": market_factor,
            "expected_efficiency": 80 + sum(stock_scores.get(stock, 0) for stock in sorted_stocks) * 5,
            "diversification_benefit": len(sorted_stocks) * 2
        }
    
    def _optimize_large_capital_positions(self, target_stocks: List[str], stock_scores: Dict[str, float], 
                                        market_factor: float) -> Dict:
        """大资金模式仓位优化"""
        # 选择前8只股票
        sorted_stocks = sorted(target_stocks, key=lambda x: stock_scores.get(x, 0), reverse=True)[:8]
        
        total_investable = self.capital_manager.total_capital * 0.85  # 保留15%现金
        
        # 使用风险平价+评分加权的混合模型
        weights = self._calculate_risk_parity_weights(sorted_stocks, stock_scores, market_factor)
        
        allocations = {}
        for stock, weight in weights.items():
            allocation = total_investable * weight
            allocations[stock] = {
                "base_amount": allocation * 0.8,
                "t_amount": allocation * 0.2,
                "total_amount": allocation,
                "weight": weight,
                "score": stock_scores.get(stock, 0)
            }
        
        return {
            "strategy": "风险平价+适度做T",
            "allocations": allocations,
            "market_factor": market_factor,
            "expected_efficiency": 70 + sum(stock_scores.get(stock, 0) for stock in sorted_stocks) * 2,
            "risk_diversification": len(sorted_stocks) * 3
        }
    
    def _calculate_optimized_weights(self, stocks: List[str], scores: Dict[str, float], 
                                   market_factor: float) -> Dict[str, float]:
        """计算优化权重"""
        if not stocks:
            return {}
        
        # 基础权重（基于评分）
        total_score = sum(scores.get(stock, 0) for stock in stocks)
        base_weights = {stock: scores.get(stock, 0) / total_score for stock in stocks} if total_score > 0 else {stock: 1.0/len(stocks) for stock in stocks}
        
        # 市场环境调整
        adjusted_weights = {}
        for stock, weight in base_weights.items():
            adjusted_weight = weight * (1 + (scores.get(stock, 0.5) - 0.5) * market_factor * 0.3)
            adjusted_weights[stock] = adjusted_weight
        
        # 归一化权重
        total_weight = sum(adjusted_weights.values())
        return {stock: weight / total_weight for stock, weight in adjusted_weights.items()}
    
    def _calculate_risk_parity_weights(self, stocks: List[str], scores: Dict[str, float], 
                                     market_factor: float) -> Dict[str, float]:
        """计算风险平价权重"""
        # 简化的风险平价模型（假设所有股票风险相等）
        equal_weight = 1.0 / len(stocks)
        
        # 根据评分进行微调
        adjusted_weights = {}
        for stock in stocks:
            score_adjustment = (scores.get(stock, 0.5) - 0.5) * 0.1  # 小幅调整
            adjusted_weights[stock] = equal_weight * (1 + score_adjustment * market_factor)
        
        # 归一化
        total_weight = sum(adjusted_weights.values())
        return {stock: weight / total_weight for stock, weight in adjusted_weights.items()}
    
    def _assess_single_stock_risk(self, stock: str, score: float) -> Dict:
        """评估单股风险"""
        return {
            "concentration_risk": "高" if score < 0.7 else "中",
            "liquidity_risk": "低",  # 假设已通过流动性筛选
            "fundamental_risk": "低" if score > 0.8 else "中" if score > 0.6 else "高",
            "overall_risk": "可接受" if score > 0.6 else "需谨慎"
        }
    
    def generate_efficiency_report(self) -> Dict:
        """
        生成效率分析报告
        
        Returns:
            效率分析报告
        """
        # 获取基础数据
        efficiency_metrics = self.capital_manager.calculate_capital_efficiency()
        portfolio_summary = self.capital_manager.get_portfolio_summary()
        
        # 计算效率趋势
        efficiency_trend = self._calculate_efficiency_trend()
        
        # 生成改进建议
        improvement_suggestions = self._generate_improvement_suggestions(efficiency_metrics)
        
        return {
            "report_time": datetime.now().isoformat(),
            "current_efficiency": efficiency_metrics,
            "portfolio_status": portfolio_summary,
            "efficiency_trend": efficiency_trend,
            "improvement_suggestions": improvement_suggestions,
            "benchmark_comparison": self._compare_with_benchmark(efficiency_metrics)
        }
    
    def _calculate_efficiency_trend(self) -> Dict:
        """计算效率趋势"""
        # 简化实现，实际应该基于历史数据
        return {
            "trend_direction": "上升",
            "trend_strength": "中等",
            "recent_change": "+2.3%",
            "volatility": "低"
        }
    
    def _generate_improvement_suggestions(self, efficiency_metrics: Dict) -> List[str]:
        """生成改进建议"""
        suggestions = []
        
        utilization = efficiency_metrics.get("capital_utilization", 0)
        idle_rate = efficiency_metrics.get("cash_idle_rate", 0)
        t_efficiency = efficiency_metrics.get("t_efficiency", 0)
        
        if utilization < 80:
            suggestions.append("资金利用率偏低，建议增加仓位")
        elif utilization > 95:
            suggestions.append("资金利用率过高，建议保留适当现金缓冲")
        
        if idle_rate > 15:
            suggestions.append("现金闲置率过高，建议寻找投资机会")
        
        if t_efficiency < 0:
            suggestions.append("T+0策略亏损，建议暂停做T或调整策略")
        elif t_efficiency < 1:
            suggestions.append("T+0效率偏低，建议优化做T时机")
        
        return suggestions
    
    def _compare_with_benchmark(self, efficiency_metrics: Dict) -> Dict:
        """与基准比较"""
        # 设定基准值
        benchmarks = {
            "capital_utilization": 85.0,
            "cash_idle_rate": 10.0,
            "t_efficiency": 2.0,
            "efficiency_score": 75.0
        }
        
        comparison = {}
        for metric, benchmark in benchmarks.items():
            current_value = efficiency_metrics.get(metric, 0)
            comparison[metric] = {
                "current": current_value,
                "benchmark": benchmark,
                "difference": current_value - benchmark,
                "performance": "优于基准" if current_value > benchmark else "低于基准"
            }
        
        return comparisonransaction_type in [TransactionType.BUY_BASE, TransactionType.BUY_T]]
        sell_transactions = [t for t in stock_transactions 
                           if t.transaction_type in [TransactionType.SELL_BASE, TransactionType.SELL_T]]
        
        # 计算加权平均成本
        total_buy_amount = sum(t.amount for t in buy_transactions)
        total_buy_quantity = sum(t.quantity for t in buy_transactions)
        total_buy_cost = sum(t.net_amount for t in buy_transactions)
        
        weighted_avg_cost = total_buy_cost / total_buy_quantity if total_buy_quantity > 0 else 0
        
        # 计算已实现盈亏
        total_sell_amount = sum(t.amount for t in sell_transactions)
        total_sell_quantity = sum(t.quantity for t in sell_transactions)
        total_sell_proceeds = sum(t.net_amount for t in sell_transactions)
        
        realized_cost = (total_sell_quantity * weighted_avg_cost) if total_sell_quantity > 0 else 0
        realized_pnl = total_sell_proceeds - realized_cost
        
        # T+0操作分析
        t_buy_transactions = [t for t in buy_transactions if t.transaction_type == TransactionType.BUY_T]
        t_sell_transactions = [t for t in sell_transactions if t.transaction_type == TransactionType.SELL_T]
        
        t_profit = sum(t.net_amount for t in t_sell_transactions) - sum(t.net_amount for t in t_buy_transactions)
        t_count = len(t_buy_transactions) + len(t_sell_transactions)
        
        return {
            "symbol": symbol,
            "current_position": {
                "quantity": position.quantity,
                "avg_cost": position.avg_cost,
                "current_price": position.current_price,
                "market_value": position.market_value,
                "unrealized_pnl": position.unrealized_pnl,
                "unrealized_pnl_pct": position.unrealized_pnl_pct
            },
            "cost_analysis": {
                "weighted_avg_cost": weighted_avg_cost,
                "total_investment": total_buy_cost,
                "realized_pnl": realized_pnl,
                "t_profit": t_profit,
                "t_transaction_count": t_count,
                "cost_reduction_from_t": t_profit / total_buy_quantity if total_buy_quantity > 0 else 0
            },
            "transaction_summary": {
                "buy_count": len(buy_transactions),
                "sell_count": len(sell_transactions),
                "total_buy_quantity": total_buy_quantity,
                "total_sell_quantity": total_sell_quantity,
                "avg_buy_price": total_buy_amount / total_buy_quantity if total_buy_quantity > 0 else 0,
                "avg_sell_price": total_sell_amount / total_sell_quantity if total_sell_quantity > 0 else 0
            }
        }
    
    def generate_cost_optimization_report(self) -> Dict:
        """
        生成成本优化报告
        
        Returns:
            成本优化报告
        """
        if not self.capital_manager.positions:
            return {"error": "当前无持仓"}
        
        optimization_report = {
            "timestamp": datetime.now().isoformat(),
            "positions_analysis": {},
            "overall_optimization": {},
            "recommendations": []
        }
        
        total_unrealized_pnl = 0
        total_t_profit = 0
        
        # 分析每个持仓
        for symbol in self.capital_manager.positions:
            cost_analysis = self.calculate_position_cost_basis(symbol)
            if "error" not in cost_analysis:
                optimization_report["positions_analysis"][symbol] = cost_analysis
                total_unrealized_pnl += cost_analysis["current_position"]["unrealized_pnl"]
                total_t_profit += cost_analysis["cost_analysis"]["t_profit"]
        
        # 整体优化分析
        portfolio_summary = self.capital_manager.get_portfolio_summary()
        optimization_report["overall_optimization"] = {
            "total_unrealized_pnl": total_unrealized_pnl,
            "total_t_profit": total_t_profit,
            "portfolio_return": portfolio_summary["total_return_pct"],
            "cost_efficiency_score": self._calculate_cost_efficiency_score(total_t_profit, portfolio_summary["total_return"])
        }
        
        # 生成优化建议
        optimization_report["recommendations"] = self._generate_optimization_recommendations(
            optimization_report["positions_analysis"], 
            optimization_report["overall_optimization"]
        )
        
        return optimization_report
    
    def _calculate_cost_efficiency_score(self, t_profit: float, total_return: float) -> float:
        """
        计算成本效率评分
        
        Args:
            t_profit: T+0总盈利
            total_return: 总收益率
            
        Returns:
            效率评分 (0-100)
        """
        # T+0贡献度
        t_contribution = (t_profit / self.capital_manager.total_capital) * 100 if self.capital_manager.total_capital > 0 else 0
        
        # 基础评分
        base_score = 50
        
        # T+0效率奖励
        t_bonus = min(t_contribution * 5, 30)  # 最高30分
        
        # 总收益率奖励
        return_bonus = min(total_return * 100 * 0.5, 20)  # 最高20分
        
        efficiency_score = base_score + t_bonus + return_bonus
        return max(0, min(100, efficiency_score))
    
    def _generate_optimization_recommendations(self, positions_analysis: Dict, overall_optimization: Dict) -> List[str]:
        """
        生成优化建议
        
        Args:
            positions_analysis: 持仓分析
            overall_optimization: 整体优化分析
            
        Returns:
            优化建议列表
        """
        recommendations = []
        
        # 基于T+0效率的建议
        if overall_optimization["total_t_profit"] < 0:
            recommendations.append("T+0操作出现亏损，建议优化做T策略或减少做T频率")
        elif overall_optimization["total_t_profit"] / self.capital_manager.total_capital < 0.01:
            recommendations.append("T+0效率偏低，建议提高做T技巧或选择波动性更大的股票")
        
        # 基于持仓分析的建议
        for symbol, analysis in positions_analysis.items():
            unrealized_pnl_pct = analysis["current_position"]["unrealized_pnl_pct"]
            
            if unrealized_pnl_pct < -0.05:  # 亏损超过5%
                recommendations.append(f"{symbol}: 亏损较大({unrealized_pnl_pct:.1%})，建议评估是否止损")
            elif unrealized_pnl_pct > 0.2:  # 盈利超过20%
                recommendations.append(f"{symbol}: 盈利丰厚({unrealized_pnl_pct:.1%})，建议考虑部分获利了结")
            
            # T+0效率分析
            t_profit = analysis["cost_analysis"]["t_profit"]
            if t_profit < 0:
                recommendations.append(f"{symbol}: T+0操作亏损，建议暂停该股票的做T操作")
        
        # 资金配置建议
        portfolio_summary = self.capital_manager.get_portfolio_summary()
        if portfolio_summary["cash_ratio"] > 0.2:
            recommendations.append("现金比例过高，建议增加投资仓位")
        elif portfolio_summary["cash_ratio"] < 0.05:
            recommendations.append("现金比例过低，建议保留适当现金应对机会")
        
        return recommendations


def create_capital_config(capital_amount: float, mode: str = None) -> CapitalConfig:
    """
    创建资金配置
    
    Args:
        capital_amount: 资金金额
        mode: 资金模式 ("small", "medium", "large")，如果不指定则自动判断
        
    Returns:
        资金配置对象
    """
    # 自动判断资金模式
    if mode is None:
        if capital_amount < 500000:
            mode = "small"
        elif capital_amount < 5000000:
            mode = "medium"
        else:
            mode = "large"
    
    capital_mode = CapitalMode(mode)
    
    # 根据模式设置不同的配置参数
    if capital_mode == CapitalMode.SMALL:
        return CapitalConfig(
            total_capital=capital_amount,
            mode=capital_mode,
            max_stocks=1,                    # 只持有1只股票
            base_position_ratio=0.75,        # 75%做底仓
            t_position_ratio=0.20,           # 20%做T
            cash_reserve_ratio=0.05,         # 5%现金储备
            max_single_stock_ratio=0.95,     # 单股最大95%仓位
            max_daily_loss_ratio=0.03,       # 单日最大亏损3%
            max_drawdown_ratio=0.30,         # 最大回撤30%
            leverage_ratio=1.0               # 不使用杠杆
        )
    elif capital_mode == CapitalMode.MEDIUM:
        return CapitalConfig(
            total_capital=capital_amount,
            mode=capital_mode,
            max_stocks=3,                    # 最多持有3只股票
            base_position_ratio=0.70,        # 70%做底仓
            t_position_ratio=0.20,           # 20%做T
            cash_reserve_ratio=0.10,         # 10%现金储备
            max_single_stock_ratio=0.50,     # 单股最大50%仓位
            max_daily_loss_ratio=0.02,       # 单日最大亏损2%
            max_drawdown_ratio=0.25,         # 最大回撤25%
            leverage_ratio=1.2               # 适度杠杆
        )
    else:  # LARGE
        return CapitalConfig(
            total_capital=capital_amount,
            mode=capital_mode,
            max_stocks=10,                   # 最多持有10只股票
            base_position_ratio=0.65,        # 65%做底仓
            t_position_ratio=0.15,           # 15%做T
            cash_reserve_ratio=0.20,         # 20%现金储备
            max_single_stock_ratio=0.30,     # 单股最大30%仓位
            max_daily_loss_ratio=0.015,      # 单日最大亏损1.5%
            max_drawdown_ratio=0.20,         # 最大回撤20%
            leverage_ratio=1.5               # 较高杠杆
        )


if __name__ == "__main__":
    # 示例使用
    print("=== 资金管理系统测试 ===")
    
    # 创建小资金配置
    config = create_capital_config(100000, "small")
    print(f"资金配置: {config}")
    
    # 创建资金管理器
    manager = CapitalManager(config)
    
    # 测试资金分配
    allocation = manager.get_capital_allocation()
    print(f"资金分配: {allocation}")
    
    # 测试买入操作
    success = manager.execute_buy("000001", 1000, 10.0, PositionType.BASE, "建立底仓")
    print(f"买入结果: {success}")
    
    # 测试投资组合摘要
    summary = manager.get_portfolio_summary()
    print(f"投资组合摘要: {summary}")
    
    # 测试资金效率计算
    efficiency = manager.calculate_capital_efficiency()
    print(f"资金效率: {efficiency}")
    
    print("=== 测试完成 ===")