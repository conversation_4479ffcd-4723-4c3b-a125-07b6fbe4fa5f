"""
T+0风险控制器
实现做T策略的风险控制和止损机制
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, field
from enum import Enum
import logging
from datetime import datetime, timedelta
from collections import deque
import json

from .t_plus_zero_engine import TPosition, TSignal, TTradeRecord, TPositionType, TSignalType
from ..utils.config.integration_adapter import get_module_config

logger = logging.getLogger(__name__)


class RiskLevel(Enum):
    """风险等级"""
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    CRITICAL = "CRITICAL"


class RiskAction(Enum):
    """风险处理动作"""
    ALLOW = "ALLOW"          # 允许交易
    REDUCE = "REDUCE"        # 减少仓位
    STOP = "STOP"            # 停止交易
    LIQUIDATE = "LIQUIDATE"  # 强制平仓


@dataclass
class RiskMetrics:
    """风险指标"""
    position_risk: float = 0.0      # 仓位风险
    concentration_risk: float = 0.0  # 集中度风险
    liquidity_risk: float = 0.0     # 流动性风险
    volatility_risk: float = 0.0    # 波动率风险
    drawdown_risk: float = 0.0      # 回撤风险
    time_risk: float = 0.0          # 时间风险
    
    overall_risk: float = 0.0       # 综合风险
    risk_level: RiskLevel = RiskLevel.LOW
    
    def calculate_overall_risk(self):
        """计算综合风险"""
        weights = {
            'position': 0.25,
            'concentration': 0.20,
            'liquidity': 0.15,
            'volatility': 0.20,
            'drawdown': 0.15,
            'time': 0.05
        }
        
        self.overall_risk = (
            self.position_risk * weights['position'] +
            self.concentration_risk * weights['concentration'] +
            self.liquidity_risk * weights['liquidity'] +
            self.volatility_risk * weights['volatility'] +
            self.drawdown_risk * weights['drawdown'] +
            self.time_risk * weights['time']
        )
        
        # 确定风险等级
        if self.overall_risk < 0.3:
            self.risk_level = RiskLevel.LOW
        elif self.overall_risk < 0.6:
            self.risk_level = RiskLevel.MEDIUM
        elif self.overall_risk < 0.8:
            self.risk_level = RiskLevel.HIGH
        else:
            self.risk_level = RiskLevel.CRITICAL


@dataclass
class RiskLimit:
    """风险限制"""
    max_position_ratio: float = 0.3     # 最大仓位比例
    max_single_loss: float = 0.02       # 单笔最大亏损2%
    max_daily_loss: float = 0.05        # 日最大亏损5%
    max_drawdown: float = 0.15          # 最大回撤15%
    max_holding_time: int = 120         # 最大持有时间120分钟
    min_liquidity: float = 1000000      # 最小流动性100万
    max_volatility: float = 0.05        # 最大波动率5%
    
    # 止损参数
    stop_loss_pct: float = 0.015        # 止损比例1.5%
    trailing_stop_pct: float = 0.01     # 移动止损1%
    time_stop_minutes: int = 90         # 时间止损90分钟


@dataclass
class RiskAlert:
    """风险警报"""
    alert_type: str
    risk_level: RiskLevel
    message: str
    suggested_action: RiskAction
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            'alert_type': self.alert_type,
            'risk_level': self.risk_level.value,
            'message': self.message,
            'suggested_action': self.suggested_action.value,
            'timestamp': self.timestamp.isoformat()
        }


class TRiskController:
    """T+0风险控制器"""
    
    def __init__(self, config: Dict = None):
        """
        初始化风险控制器

        Args:
            config: 配置参数
        """
        self.config = config or {}

        # 从新配置系统加载风险控制配置
        self._load_risk_config()

        # 风险限制
        self.risk_limits = RiskLimit()
        if 'risk_limits' in self.config:
            for key, value in self.config['risk_limits'].items():
                if hasattr(self.risk_limits, key):
                    setattr(self.risk_limits, key, value)
        
        # 风险监控状态
        self.current_positions = {}  # symbol -> TPosition
        self.t_positions = {}        # symbol -> List[TPosition]
        self.daily_pnl = 0.0
        self.max_drawdown = 0.0
        self.peak_value = 0.0
        
        # 风险历史
        self.risk_history = deque(maxlen=1000)
        self.alert_history = deque(maxlen=100)
        
        # 紧急状态
        self.emergency_mode = False
        self.trading_suspended = False
        
        # 统计数据
        self.daily_stats = {
            'trades': 0,
            'successful_trades': 0,
            'total_profit': 0.0,
            'max_single_loss': 0.0,
            'risk_violations': 0
        }

        logger.info("T+0风险控制器初始化完成")

    def _load_risk_config(self):
        """从新配置系统加载风险控制配置"""
        try:
            # 获取风险控制配置
            risk_config = get_module_config('risk_control')
            if risk_config and 'parameters' in risk_config:
                params = risk_config['parameters']
                # 更新配置参数
                if 'max_position_ratio' in params:
                    self.config['max_position_ratio'] = params['max_position_ratio']
                if 'max_single_loss' in params:
                    self.config['max_single_loss'] = params['max_single_loss']
                if 'max_daily_loss' in params:
                    self.config['max_daily_loss'] = params['max_daily_loss']
                if 'stop_loss_pct' in params:
                    self.config['stop_loss_pct'] = params['stop_loss_pct']
                if 'max_holding_time' in params:
                    self.config['max_holding_time'] = params['max_holding_time']
                if 'circuit_breaker_enabled' in params:
                    self.config['circuit_breaker_enabled'] = params['circuit_breaker_enabled']
                if 'risk_monitoring_interval' in params:
                    self.config['risk_monitoring_interval'] = params['risk_monitoring_interval']

                logger.info("已从新配置系统加载风险控制配置")
            else:
                logger.warning("未找到风险控制配置，使用默认配置")

        except Exception as e:
            logger.error(f"加载风险控制配置失败: {e}")

    def check_pre_trade_risk(self, signal: TSignal, base_position: TPosition,
                           market_data: pd.DataFrame) -> Tuple[bool, List[RiskAlert]]:
        """
        交易前风险检查
        
        Args:
            signal: 交易信号
            base_position: 底仓信息
            market_data: 市场数据
            
        Returns:
            (是否允许交易, 风险警报列表)
        """
        alerts = []
        
        # 紧急模式检查
        if self.emergency_mode or self.trading_suspended:
            alerts.append(RiskAlert(
                alert_type="EMERGENCY_MODE",
                risk_level=RiskLevel.CRITICAL,
                message="系统处于紧急模式，禁止交易",
                suggested_action=RiskAction.STOP
            ))
            return False, alerts
        
        # 计算风险指标
        risk_metrics = self._calculate_risk_metrics(signal, base_position, market_data)
        
        # 仓位风险检查
        position_check, position_alerts = self._check_position_risk(signal, base_position)
        alerts.extend(position_alerts)
        
        # 流动性风险检查
        liquidity_check, liquidity_alerts = self._check_liquidity_risk(signal, market_data)
        alerts.extend(liquidity_alerts)
        
        # 波动率风险检查
        volatility_check, volatility_alerts = self._check_volatility_risk(signal, market_data)
        alerts.extend(volatility_alerts)
        
        # 时间风险检查
        time_check, time_alerts = self._check_time_risk(signal)
        alerts.extend(time_alerts)
        
        # 日度风险检查
        daily_check, daily_alerts = self._check_daily_risk(signal)
        alerts.extend(daily_alerts)
        
        # 综合判断
        all_checks = [position_check, liquidity_check, volatility_check, time_check, daily_check]
        allow_trade = all(all_checks) and risk_metrics.risk_level != RiskLevel.CRITICAL
        
        # 记录风险历史
        self.risk_history.append({
            'timestamp': datetime.now(),
            'symbol': signal.symbol,
            'signal_type': signal.signal_type.value,
            'risk_metrics': risk_metrics,
            'allow_trade': allow_trade,
            'alerts': [alert.to_dict() for alert in alerts]
        })
        
        return allow_trade, alerts
    
    def monitor_position_risk(self, positions: Dict[str, TPosition], 
                            market_data: Dict[str, pd.DataFrame]) -> List[RiskAlert]:
        """
        监控持仓风险
        
        Args:
            positions: 当前持仓
            market_data: 市场数据
            
        Returns:
            风险警报列表
        """
        alerts = []
        
        for symbol, position in positions.items():
            if symbol not in market_data:
                continue
            
            data = market_data[symbol]
            current_price = data['close'].iloc[-1]
            
            # 更新仓位价格
            position.update_price(current_price)
            
            # 止损检查
            stop_loss_alerts = self._check_stop_loss(position)
            alerts.extend(stop_loss_alerts)
            
            # 时间止损检查
            time_stop_alerts = self._check_time_stop(position)
            alerts.extend(time_stop_alerts)
            
            # 移动止损检查
            trailing_stop_alerts = self._check_trailing_stop(position, data)
            alerts.extend(trailing_stop_alerts)
            
            # 异常波动检查
            volatility_alerts = self._check_abnormal_volatility(position, data)
            alerts.extend(volatility_alerts)
        
        # 更新组合风险
        self._update_portfolio_risk(positions)
        
        # 检查组合级别风险
        portfolio_alerts = self._check_portfolio_risk()
        alerts.extend(portfolio_alerts)
        
        # 记录警报
        for alert in alerts:
            self.alert_history.append(alert)
            logger.warning(f"风险警报: {alert.message}")
        
        return alerts
    
    def _calculate_risk_metrics(self, signal: TSignal, base_position: TPosition, 
                               market_data: pd.DataFrame) -> RiskMetrics:
        """计算风险指标"""
        metrics = RiskMetrics()
        
        # 仓位风险
        if signal.signal_type == TSignalType.BUY_T:
            position_value = signal.suggested_shares * signal.entry_price
            total_value = base_position.shares * base_position.current_price
            metrics.position_risk = position_value / total_value if total_value > 0 else 0
        else:
            metrics.position_risk = signal.suggested_shares / base_position.shares if base_position.shares > 0 else 0
        
        # 流动性风险
        if len(market_data) > 0:
            avg_volume = market_data['volume'].rolling(20).mean().iloc[-1]
            current_volume = market_data['volume'].iloc[-1]
            metrics.liquidity_risk = max(0, 1 - current_volume / avg_volume) if avg_volume > 0 else 1
        
        # 波动率风险
        if len(market_data) > 10:
            returns = market_data['close'].pct_change().dropna()
            volatility = returns.std()
            metrics.volatility_risk = min(volatility / 0.05, 1.0)  # 归一化到0-1
        
        # 时间风险
        if signal.holding_time > self.risk_limits.max_holding_time:
            metrics.time_risk = (signal.holding_time - self.risk_limits.max_holding_time) / self.risk_limits.max_holding_time
        
        # 计算综合风险
        metrics.calculate_overall_risk()
        
        return metrics
    
    def _check_position_risk(self, signal: TSignal, base_position: TPosition) -> Tuple[bool, List[RiskAlert]]:
        """检查仓位风险"""
        alerts = []
        
        # 检查仓位比例
        if signal.signal_type == TSignalType.BUY_T:
            position_ratio = signal.suggested_shares * signal.entry_price / (base_position.shares * base_position.current_price)
            if position_ratio > self.risk_limits.max_position_ratio:
                alerts.append(RiskAlert(
                    alert_type="POSITION_RATIO",
                    risk_level=RiskLevel.HIGH,
                    message=f"T仓位比例过高: {position_ratio:.2%} > {self.risk_limits.max_position_ratio:.2%}",
                    suggested_action=RiskAction.REDUCE
                ))
                return False, alerts
        
        # 检查预期损失
        expected_loss = signal.suggested_shares * signal.entry_price * self.risk_limits.stop_loss_pct
        position_value = base_position.shares * base_position.current_price
        loss_ratio = expected_loss / position_value if position_value > 0 else 0
        
        if loss_ratio > self.risk_limits.max_single_loss:
            alerts.append(RiskAlert(
                alert_type="EXPECTED_LOSS",
                risk_level=RiskLevel.HIGH,
                message=f"预期损失过高: {loss_ratio:.2%} > {self.risk_limits.max_single_loss:.2%}",
                suggested_action=RiskAction.REDUCE
            ))
            return False, alerts
        
        return True, alerts
    
    def _check_liquidity_risk(self, signal: TSignal, market_data: pd.DataFrame) -> Tuple[bool, List[RiskAlert]]:
        """检查流动性风险"""
        alerts = []
        
        if len(market_data) < 5:
            return True, alerts
        
        # 检查成交量
        avg_volume = market_data['volume'].rolling(5).mean().iloc[-1]
        trade_volume = signal.suggested_shares
        
        if avg_volume < self.risk_limits.min_liquidity:
            alerts.append(RiskAlert(
                alert_type="LOW_LIQUIDITY",
                risk_level=RiskLevel.MEDIUM,
                message=f"流动性不足: 平均成交量 {avg_volume:.0f} < {self.risk_limits.min_liquidity:.0f}",
                suggested_action=RiskAction.REDUCE
            ))
        
        # 检查交易量占比
        if avg_volume > 0 and trade_volume / avg_volume > 0.1:  # 交易量不超过平均成交量的10%
            alerts.append(RiskAlert(
                alert_type="HIGH_TRADE_RATIO",
                risk_level=RiskLevel.MEDIUM,
                message=f"交易量占比过高: {trade_volume/avg_volume:.2%}",
                suggested_action=RiskAction.REDUCE
            ))
        
        return len(alerts) == 0, alerts
    
    def _check_volatility_risk(self, signal: TSignal, market_data: pd.DataFrame) -> Tuple[bool, List[RiskAlert]]:
        """检查波动率风险"""
        alerts = []
        
        if len(market_data) < 10:
            return True, alerts
        
        # 计算波动率
        returns = market_data['close'].pct_change().dropna()
        volatility = returns.std()
        
        if volatility > self.risk_limits.max_volatility:
            alerts.append(RiskAlert(
                alert_type="HIGH_VOLATILITY",
                risk_level=RiskLevel.HIGH,
                message=f"波动率过高: {volatility:.3f} > {self.risk_limits.max_volatility:.3f}",
                suggested_action=RiskAction.REDUCE
            ))
            return False, alerts
        
        return True, alerts
    
    def _check_time_risk(self, signal: TSignal) -> Tuple[bool, List[RiskAlert]]:
        """检查时间风险"""
        alerts = []
        
        # 检查建议持有时间
        if signal.holding_time > self.risk_limits.max_holding_time:
            alerts.append(RiskAlert(
                alert_type="LONG_HOLDING_TIME",
                risk_level=RiskLevel.MEDIUM,
                message=f"建议持有时间过长: {signal.holding_time}分钟 > {self.risk_limits.max_holding_time}分钟",
                suggested_action=RiskAction.REDUCE
            ))
        
        # 检查交易时间
        now = datetime.now()
        if now.hour < 9 or now.hour >= 15:  # 非交易时间
            alerts.append(RiskAlert(
                alert_type="NON_TRADING_HOURS",
                risk_level=RiskLevel.HIGH,
                message="非交易时间",
                suggested_action=RiskAction.STOP
            ))
            return False, alerts
        
        return True, alerts
    
    def _check_daily_risk(self, signal: TSignal) -> Tuple[bool, List[RiskAlert]]:
        """检查日度风险"""
        alerts = []
        
        # 检查日度亏损
        if abs(self.daily_pnl) > self.risk_limits.max_daily_loss:
            alerts.append(RiskAlert(
                alert_type="DAILY_LOSS_LIMIT",
                risk_level=RiskLevel.CRITICAL,
                message=f"日度亏损超限: {self.daily_pnl:.2%} > {self.risk_limits.max_daily_loss:.2%}",
                suggested_action=RiskAction.STOP
            ))
            return False, alerts
        
        # 检查最大回撤
        if self.max_drawdown > self.risk_limits.max_drawdown:
            alerts.append(RiskAlert(
                alert_type="MAX_DRAWDOWN",
                risk_level=RiskLevel.CRITICAL,
                message=f"最大回撤超限: {self.max_drawdown:.2%} > {self.risk_limits.max_drawdown:.2%}",
                suggested_action=RiskAction.LIQUIDATE
            ))
            return False, alerts
        
        return True, alerts
    
    def _check_stop_loss(self, position: TPosition) -> List[RiskAlert]:
        """检查止损"""
        alerts = []
        
        if position.position_type == TPositionType.BASE:
            return alerts  # 底仓不止损
        
        # 计算止损价格
        if position.position_type == TPositionType.T_LONG:
            stop_price = position.avg_cost * (1 - self.risk_limits.stop_loss_pct)
            if position.current_price <= stop_price:
                alerts.append(RiskAlert(
                    alert_type="STOP_LOSS",
                    risk_level=RiskLevel.HIGH,
                    message=f"T多仓触发止损: {position.current_price:.2f} <= {stop_price:.2f}",
                    suggested_action=RiskAction.LIQUIDATE
                ))
        
        elif position.position_type == TPositionType.T_SHORT:
            stop_price = position.avg_cost * (1 + self.risk_limits.stop_loss_pct)
            if position.current_price >= stop_price:
                alerts.append(RiskAlert(
                    alert_type="STOP_LOSS",
                    risk_level=RiskLevel.HIGH,
                    message=f"T空仓触发止损: {position.current_price:.2f} >= {stop_price:.2f}",
                    suggested_action=RiskAction.LIQUIDATE
                ))
        
        return alerts
    
    def _check_time_stop(self, position: TPosition) -> List[RiskAlert]:
        """检查时间止损"""
        alerts = []
        
        if position.position_type == TPositionType.BASE:
            return alerts  # 底仓不时间止损
        
        if position.holding_minutes > self.risk_limits.time_stop_minutes:
            alerts.append(RiskAlert(
                alert_type="TIME_STOP",
                risk_level=RiskLevel.MEDIUM,
                message=f"T仓持有时间过长: {position.holding_minutes}分钟 > {self.risk_limits.time_stop_minutes}分钟",
                suggested_action=RiskAction.LIQUIDATE
            ))
        
        return alerts
    
    def _check_trailing_stop(self, position: TPosition, market_data: pd.DataFrame) -> List[RiskAlert]:
        """检查移动止损"""
        alerts = []
        
        if position.position_type == TPositionType.BASE or len(market_data) < 10:
            return alerts
        
        # 计算最高价（多仓）或最低价（空仓）
        recent_data = market_data.tail(10)
        
        if position.position_type == TPositionType.T_LONG:
            highest_price = recent_data['high'].max()
            trailing_stop_price = highest_price * (1 - self.risk_limits.trailing_stop_pct)
            
            if position.current_price <= trailing_stop_price:
                alerts.append(RiskAlert(
                    alert_type="TRAILING_STOP",
                    risk_level=RiskLevel.MEDIUM,
                    message=f"T多仓触发移动止损: {position.current_price:.2f} <= {trailing_stop_price:.2f}",
                    suggested_action=RiskAction.LIQUIDATE
                ))
        
        elif position.position_type == TPositionType.T_SHORT:
            lowest_price = recent_data['low'].min()
            trailing_stop_price = lowest_price * (1 + self.risk_limits.trailing_stop_pct)
            
            if position.current_price >= trailing_stop_price:
                alerts.append(RiskAlert(
                    alert_type="TRAILING_STOP",
                    risk_level=RiskLevel.MEDIUM,
                    message=f"T空仓触发移动止损: {position.current_price:.2f} >= {trailing_stop_price:.2f}",
                    suggested_action=RiskAction.LIQUIDATE
                ))
        
        return alerts
    
    def _check_abnormal_volatility(self, position: TPosition, market_data: pd.DataFrame) -> List[RiskAlert]:
        """检查异常波动"""
        alerts = []
        
        if len(market_data) < 20:
            return alerts
        
        # 计算当前波动率
        returns = market_data['close'].pct_change().tail(10).dropna()
        current_volatility = returns.std()
        
        # 计算历史波动率
        historical_returns = market_data['close'].pct_change().tail(50).dropna()
        historical_volatility = historical_returns.std()
        
        # 异常波动检测
        if current_volatility > historical_volatility * 2:
            alerts.append(RiskAlert(
                alert_type="ABNORMAL_VOLATILITY",
                risk_level=RiskLevel.HIGH,
                message=f"异常波动: 当前波动率 {current_volatility:.3f} > 历史波动率 {historical_volatility:.3f} * 2",
                suggested_action=RiskAction.REDUCE
            ))
        
        return alerts
    
    def _update_portfolio_risk(self, positions: Dict[str, TPosition]):
        """更新组合风险"""
        total_value = 0.0
        total_pnl = 0.0
        
        for position in positions.values():
            position_value = position.shares * position.current_price
            total_value += position_value
            total_pnl += position.unrealized_pnl
        
        # 更新峰值和回撤
        if total_value > self.peak_value:
            self.peak_value = total_value
        
        if self.peak_value > 0:
            current_drawdown = (self.peak_value - total_value) / self.peak_value
            self.max_drawdown = max(self.max_drawdown, current_drawdown)
        
        # 更新日度盈亏
        self.daily_pnl = total_pnl / total_value if total_value > 0 else 0
    
    def _check_portfolio_risk(self) -> List[RiskAlert]:
        """检查组合风险"""
        alerts = []
        
        # 检查最大回撤
        if self.max_drawdown > self.risk_limits.max_drawdown:
            alerts.append(RiskAlert(
                alert_type="PORTFOLIO_DRAWDOWN",
                risk_level=RiskLevel.CRITICAL,
                message=f"组合最大回撤超限: {self.max_drawdown:.2%}",
                suggested_action=RiskAction.LIQUIDATE
            ))
        
        # 检查日度亏损
        if self.daily_pnl < -self.risk_limits.max_daily_loss:
            alerts.append(RiskAlert(
                alert_type="PORTFOLIO_DAILY_LOSS",
                risk_level=RiskLevel.CRITICAL,
                message=f"组合日度亏损超限: {self.daily_pnl:.2%}",
                suggested_action=RiskAction.STOP
            ))
        
        return alerts
    
    def activate_emergency_mode(self, reason: str):
        """激活紧急模式"""
        self.emergency_mode = True
        self.trading_suspended = True
        
        alert = RiskAlert(
            alert_type="EMERGENCY_MODE",
            risk_level=RiskLevel.CRITICAL,
            message=f"激活紧急模式: {reason}",
            suggested_action=RiskAction.LIQUIDATE
        )
        
        self.alert_history.append(alert)
        logger.critical(f"紧急模式激活: {reason}")
    
    def deactivate_emergency_mode(self):
        """解除紧急模式"""
        self.emergency_mode = False
        self.trading_suspended = False
        logger.info("紧急模式已解除")
    
    def update_trade_result(self, trade: TTradeRecord):
        """更新交易结果"""
        self.daily_stats['trades'] += 1
        
        if trade.profit > 0:
            self.daily_stats['successful_trades'] += 1
        
        self.daily_stats['total_profit'] += trade.profit
        
        if trade.profit < 0:
            loss_ratio = abs(trade.profit) / (trade.shares * trade.price)
            self.daily_stats['max_single_loss'] = max(self.daily_stats['max_single_loss'], loss_ratio)
    
    def get_risk_summary(self) -> Dict[str, Any]:
        """获取风险摘要"""
        return {
            'emergency_mode': self.emergency_mode,
            'trading_suspended': self.trading_suspended,
            'daily_pnl': self.daily_pnl,
            'max_drawdown': self.max_drawdown,
            'daily_stats': self.daily_stats.copy(),
            'recent_alerts': [alert.to_dict() for alert in list(self.alert_history)[-10:]],
            'risk_violations': self.daily_stats['risk_violations']
        }
    
    def reset_daily_stats(self):
        """重置日度统计"""
        self.daily_stats = {
            'trades': 0,
            'successful_trades': 0,
            'total_profit': 0.0,
            'max_single_loss': 0.0,
            'risk_violations': 0
        }
        self.daily_pnl = 0.0
        logger.info("日度统计已重置")
    
    def export_risk_report(self, filepath: str):
        """导出风险报告"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'risk_summary': self.get_risk_summary(),
            'risk_limits': {
                'max_position_ratio': self.risk_limits.max_position_ratio,
                'max_single_loss': self.risk_limits.max_single_loss,
                'max_daily_loss': self.risk_limits.max_daily_loss,
                'max_drawdown': self.risk_limits.max_drawdown,
                'stop_loss_pct': self.risk_limits.stop_loss_pct
            },
            'recent_risk_history': list(self.risk_history)[-50:],
            'alert_history': [alert.to_dict() for alert in self.alert_history]
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"风险报告已导出到: {filepath}")