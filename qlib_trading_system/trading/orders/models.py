"""
订单数据模型
Order Data Models
"""

from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any, List
import uuid


class OrderType(Enum):
    """订单类型枚举"""
    MARKET = "MARKET"  # 市价单
    LIMIT = "LIMIT"    # 限价单
    STOP = "STOP"      # 止损单
    STOP_LIMIT = "STOP_LIMIT"  # 止损限价单
    TWAP = "TWAP"      # 时间加权平均价格算法单
    VWAP = "VWAP"      # 成交量加权平均价格算法单


class OrderSide(Enum):
    """订单方向枚举"""
    BUY = "BUY"        # 买入
    SELL = "SELL"      # 卖出


class OrderStatus(Enum):
    """订单状态枚举"""
    PENDING = "PENDING"              # 待提交
    SUBMITTED = "SUBMITTED"          # 已提交
    PARTIALLY_FILLED = "PARTIALLY_FILLED"  # 部分成交
    FILLED = "FILLED"                # 完全成交
    CANCELLED = "CANCELLED"          # 已取消
    REJECTED = "REJECTED"            # 已拒绝
    EXPIRED = "EXPIRED"              # 已过期
    FAILED = "FAILED"                # 失败


class OrderPriority(Enum):
    """订单优先级枚举"""
    LOW = 1      # 低优先级
    NORMAL = 2   # 普通优先级
    HIGH = 3     # 高优先级
    URGENT = 4   # 紧急优先级


@dataclass
class OrderFill:
    """订单成交记录"""
    fill_id: str
    order_id: str
    symbol: str
    side: OrderSide
    quantity: int
    price: float
    commission: float
    timestamp: datetime
    exchange_id: Optional[str] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.fill_id:
            self.fill_id = str(uuid.uuid4())


@dataclass
class Order:
    """订单模型"""
    # 基本信息
    order_id: str
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: int
    
    # 价格信息
    price: Optional[float] = None
    stop_price: Optional[float] = None
    
    # 状态信息
    status: OrderStatus = OrderStatus.PENDING
    filled_quantity: int = 0
    remaining_quantity: int = 0
    avg_fill_price: float = 0.0
    
    # 时间信息
    create_time: datetime = field(default_factory=datetime.now)
    submit_time: Optional[datetime] = None
    update_time: Optional[datetime] = None
    expire_time: Optional[datetime] = None
    
    # 执行信息
    priority: OrderPriority = OrderPriority.NORMAL
    parent_order_id: Optional[str] = None  # 父订单ID（用于算法订单）
    child_order_ids: List[str] = field(default_factory=list)  # 子订单ID列表
    
    # 算法订单参数
    algo_params: Dict[str, Any] = field(default_factory=dict)
    
    # 风险控制参数
    max_position_pct: float = 1.0  # 最大仓位百分比
    max_daily_trades: int = 100    # 单日最大交易次数
    
    # 成交记录
    fills: List[OrderFill] = field(default_factory=list)
    
    # 其他信息
    reason: str = ""               # 下单原因
    source: str = "SYSTEM"         # 订单来源
    tags: List[str] = field(default_factory=list)  # 订单标签
    metadata: Dict[str, Any] = field(default_factory=dict)  # 元数据
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.order_id:
            self.order_id = str(uuid.uuid4())
        
        if self.remaining_quantity == 0:
            self.remaining_quantity = self.quantity
    
    @property
    def is_buy(self) -> bool:
        """是否为买入订单"""
        return self.side == OrderSide.BUY
    
    @property
    def is_sell(self) -> bool:
        """是否为卖出订单"""
        return self.side == OrderSide.SELL
    
    @property
    def is_active(self) -> bool:
        """订单是否处于活跃状态"""
        return self.status in [OrderStatus.PENDING, OrderStatus.SUBMITTED, OrderStatus.PARTIALLY_FILLED]
    
    @property
    def is_completed(self) -> bool:
        """订单是否已完成"""
        return self.status in [OrderStatus.FILLED, OrderStatus.CANCELLED, OrderStatus.REJECTED, OrderStatus.EXPIRED, OrderStatus.FAILED]
    
    @property
    def fill_ratio(self) -> float:
        """成交比例"""
        if self.quantity == 0:
            return 0.0
        return self.filled_quantity / self.quantity
    
    def add_fill(self, fill: OrderFill) -> None:
        """添加成交记录"""
        self.fills.append(fill)
        self.filled_quantity += fill.quantity
        self.remaining_quantity = max(0, self.quantity - self.filled_quantity)
        
        # 更新平均成交价格
        total_value = sum(f.quantity * f.price for f in self.fills)
        self.avg_fill_price = total_value / self.filled_quantity if self.filled_quantity > 0 else 0.0
        
        # 更新订单状态
        if self.remaining_quantity == 0:
            self.status = OrderStatus.FILLED
        elif self.filled_quantity > 0:
            self.status = OrderStatus.PARTIALLY_FILLED
        
        self.update_time = datetime.now()
    
    def cancel(self, reason: str = "") -> None:
        """取消订单"""
        if self.is_active:
            self.status = OrderStatus.CANCELLED
            self.update_time = datetime.now()
            if reason:
                self.reason = f"{self.reason}; 取消原因: {reason}" if self.reason else f"取消原因: {reason}"
    
    def reject(self, reason: str = "") -> None:
        """拒绝订单"""
        self.status = OrderStatus.REJECTED
        self.update_time = datetime.now()
        if reason:
            self.reason = f"{self.reason}; 拒绝原因: {reason}" if self.reason else f"拒绝原因: {reason}"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'order_id': self.order_id,
            'symbol': self.symbol,
            'side': self.side.value,
            'order_type': self.order_type.value,
            'quantity': self.quantity,
            'price': self.price,
            'stop_price': self.stop_price,
            'status': self.status.value,
            'filled_quantity': self.filled_quantity,
            'remaining_quantity': self.remaining_quantity,
            'avg_fill_price': self.avg_fill_price,
            'create_time': self.create_time.isoformat(),
            'submit_time': self.submit_time.isoformat() if self.submit_time else None,
            'update_time': self.update_time.isoformat() if self.update_time else None,
            'expire_time': self.expire_time.isoformat() if self.expire_time else None,
            'priority': self.priority.value,
            'parent_order_id': self.parent_order_id,
            'child_order_ids': self.child_order_ids,
            'algo_params': self.algo_params,
            'max_position_pct': self.max_position_pct,
            'max_daily_trades': self.max_daily_trades,
            'fills': [fill.__dict__ for fill in self.fills],
            'reason': self.reason,
            'source': self.source,
            'tags': self.tags,
            'metadata': self.metadata
        }


@dataclass
class AlgoOrderConfig:
    """算法订单配置"""
    algo_type: str  # TWAP, VWAP, POV等
    start_time: datetime
    end_time: datetime
    participation_rate: float = 0.1  # 参与率（0-1）
    max_slice_size: int = 1000       # 最大切片大小
    min_slice_size: int = 100        # 最小切片大小
    price_limit: Optional[float] = None  # 价格限制
    urgency: float = 0.5             # 紧急程度（0-1）
    risk_aversion: float = 0.5       # 风险厌恶程度（0-1）
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'algo_type': self.algo_type,
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat(),
            'participation_rate': self.participation_rate,
            'max_slice_size': self.max_slice_size,
            'min_slice_size': self.min_slice_size,
            'price_limit': self.price_limit,
            'urgency': self.urgency,
            'risk_aversion': self.risk_aversion
        }