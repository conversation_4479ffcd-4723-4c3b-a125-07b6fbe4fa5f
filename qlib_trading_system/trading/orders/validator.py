"""
订单验证器
Order Validator - 订单风险检查和预处理逻辑
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass

from .models import Order, OrderType, OrderSide, OrderStatus, OrderPriority


@dataclass
class ValidationResult:
    """验证结果"""
    is_valid: bool
    error_code: str = ""
    error_message: str = ""
    warnings: List[str] = None
    
    def __post_init__(self):
        if self.warnings is None:
            self.warnings = []


@dataclass
class RiskLimits:
    """风险限制配置"""
    # 仓位限制
    max_position_value: float = 1000000.0      # 最大持仓价值
    max_single_position_pct: float = 0.3       # 单只股票最大仓位比例
    max_sector_position_pct: float = 0.5       # 单个行业最大仓位比例
    
    # 交易限制
    max_daily_trades: int = 100                # 单日最大交易次数
    max_order_value: float = 100000.0          # 单笔订单最大价值
    min_order_value: float = 1000.0            # 单笔订单最小价值
    
    # 价格限制
    max_price_deviation_pct: float = 0.05      # 最大价格偏离百分比
    min_price: float = 0.01                    # 最小价格
    max_price: float = 10000.0                 # 最大价格
    
    # 时间限制
    market_open_time: str = "09:30"            # 市场开盘时间
    market_close_time: str = "15:00"           # 市场收盘时间
    order_cutoff_time: str = "14:55"           # 订单截止时间
    
    # 资金限制
    min_available_cash: float = 10000.0        # 最小可用资金
    max_leverage: float = 1.0                  # 最大杠杆倍数


class OrderValidator:
    """订单验证器"""
    
    def __init__(self, risk_limits: Optional[RiskLimits] = None):
        """
        初始化订单验证器
        
        Args:
            risk_limits: 风险限制配置
        """
        self.logger = logging.getLogger(__name__)
        self.risk_limits = risk_limits or RiskLimits()
        
        # 交易统计
        self.daily_trade_count: Dict[str, int] = {}  # 每日交易次数统计
        self.position_cache: Dict[str, float] = {}   # 持仓缓存
        self.last_prices: Dict[str, float] = {}      # 最新价格缓存
        
    def validate_order(self, order: Order, account_info: Dict[str, Any]) -> ValidationResult:
        """
        验证订单
        
        Args:
            order: 待验证的订单
            account_info: 账户信息
            
        Returns:
            ValidationResult: 验证结果
        """
        try:
            # 基础验证
            result = self._validate_basic_info(order)
            if not result.is_valid:
                return result
            
            # 市场时间验证
            result = self._validate_market_time(order)
            if not result.is_valid:
                return result
            
            # 价格验证
            result = self._validate_price(order)
            if not result.is_valid:
                return result
            
            # 数量验证
            result = self._validate_quantity(order)
            if not result.is_valid:
                return result
            
            # 资金验证
            result = self._validate_funds(order, account_info)
            if not result.is_valid:
                return result
            
            # 仓位验证
            result = self._validate_position(order, account_info)
            if not result.is_valid:
                return result
            
            # 交易频率验证
            result = self._validate_trading_frequency(order)
            if not result.is_valid:
                return result
            
            # 风险验证
            result = self._validate_risk_limits(order, account_info)
            if not result.is_valid:
                return result
            
            self.logger.info(f"订单验证通过: {order.order_id}")
            return ValidationResult(is_valid=True)
            
        except Exception as e:
            self.logger.error(f"订单验证异常: {e}")
            return ValidationResult(
                is_valid=False,
                error_code="VALIDATION_ERROR",
                error_message=f"订单验证异常: {str(e)}"
            )
    
    def _validate_basic_info(self, order: Order) -> ValidationResult:
        """验证基础信息"""
        # 检查必填字段
        if not order.symbol:
            return ValidationResult(
                is_valid=False,
                error_code="MISSING_SYMBOL",
                error_message="股票代码不能为空"
            )
        
        if order.quantity <= 0:
            return ValidationResult(
                is_valid=False,
                error_code="INVALID_QUANTITY",
                error_message="订单数量必须大于0"
            )
        
        # 检查股票代码格式
        if not self._is_valid_symbol(order.symbol):
            return ValidationResult(
                is_valid=False,
                error_code="INVALID_SYMBOL",
                error_message=f"无效的股票代码: {order.symbol}"
            )
        
        return ValidationResult(is_valid=True)
    
    def _validate_market_time(self, order: Order) -> ValidationResult:
        """验证市场时间"""
        # 测试模式：跳过时间检查
        if hasattr(self, 'test_mode') and self.test_mode:
            return ValidationResult(is_valid=True)
            
        now = datetime.now()
        current_time = now.strftime("%H:%M")
        
        # 检查是否在交易时间内
        if not self._is_trading_time(current_time):
            return ValidationResult(
                is_valid=False,
                error_code="MARKET_CLOSED",
                error_message=f"当前时间 {current_time} 不在交易时间内"
            )
        
        # 检查是否接近收盘
        if current_time > self.risk_limits.order_cutoff_time:
            return ValidationResult(
                is_valid=False,
                error_code="ORDER_CUTOFF",
                error_message=f"已过订单截止时间 {self.risk_limits.order_cutoff_time}"
            )
        
        return ValidationResult(is_valid=True)
    
    def _validate_price(self, order: Order) -> ValidationResult:
        """验证价格"""
        warnings = []
        
        # 限价单必须有价格
        if order.order_type == OrderType.LIMIT and order.price is None:
            return ValidationResult(
                is_valid=False,
                error_code="MISSING_PRICE",
                error_message="限价单必须指定价格"
            )
        
        # 价格范围检查
        if order.price is not None:
            if order.price < self.risk_limits.min_price:
                return ValidationResult(
                    is_valid=False,
                    error_code="PRICE_TOO_LOW",
                    error_message=f"价格 {order.price} 低于最小限制 {self.risk_limits.min_price}"
                )
            
            if order.price > self.risk_limits.max_price:
                return ValidationResult(
                    is_valid=False,
                    error_code="PRICE_TOO_HIGH",
                    error_message=f"价格 {order.price} 高于最大限制 {self.risk_limits.max_price}"
                )
            
            # 价格偏离检查
            last_price = self.last_prices.get(order.symbol)
            if last_price:
                deviation = abs(order.price - last_price) / last_price
                if deviation > self.risk_limits.max_price_deviation_pct:
                    warnings.append(f"价格偏离最新价格 {deviation:.2%}，超过限制 {self.risk_limits.max_price_deviation_pct:.2%}")
        
        return ValidationResult(is_valid=True, warnings=warnings)
    
    def _validate_quantity(self, order: Order) -> ValidationResult:
        """验证数量"""
        # 检查是否为100的整数倍（A股交易规则）
        if order.quantity % 100 != 0:
            return ValidationResult(
                is_valid=False,
                error_code="INVALID_LOT_SIZE",
                error_message=f"订单数量 {order.quantity} 必须为100的整数倍"
            )
        
        return ValidationResult(is_valid=True)
    
    def _validate_funds(self, order: Order, account_info: Dict[str, Any]) -> ValidationResult:
        """验证资金"""
        available_cash = account_info.get('available_cash', 0)
        
        # 买入订单需要检查资金
        if order.is_buy:
            estimated_cost = self._estimate_order_cost(order)
            
            if estimated_cost > available_cash:
                return ValidationResult(
                    is_valid=False,
                    error_code="INSUFFICIENT_FUNDS",
                    error_message=f"资金不足，需要 {estimated_cost:.2f}，可用 {available_cash:.2f}"
                )
            
            # 检查最小可用资金
            if available_cash - estimated_cost < self.risk_limits.min_available_cash:
                return ValidationResult(
                    is_valid=False,
                    error_code="MIN_CASH_VIOLATION",
                    error_message=f"交易后可用资金将低于最小限制 {self.risk_limits.min_available_cash}"
                )
        
        return ValidationResult(is_valid=True)
    
    def _validate_position(self, order: Order, account_info: Dict[str, Any]) -> ValidationResult:
        """验证仓位"""
        total_value = account_info.get('total_value', 0)
        current_position = self.position_cache.get(order.symbol, 0)
        
        if order.is_buy:
            # 计算交易后的仓位
            estimated_cost = self._estimate_order_cost(order)
            new_position_value = current_position + estimated_cost
            position_pct = new_position_value / total_value if total_value > 0 else 0
            
            # 检查单只股票仓位限制
            if position_pct > self.risk_limits.max_single_position_pct:
                return ValidationResult(
                    is_valid=False,
                    error_code="POSITION_LIMIT_EXCEEDED",
                    error_message=f"单只股票仓位 {position_pct:.2%} 超过限制 {self.risk_limits.max_single_position_pct:.2%}"
                )
        
        elif order.is_sell:
            # 检查是否有足够的持仓
            if current_position < order.quantity:
                return ValidationResult(
                    is_valid=False,
                    error_code="INSUFFICIENT_POSITION",
                    error_message=f"持仓不足，当前持仓 {current_position}，卖出数量 {order.quantity}"
                )
        
        return ValidationResult(is_valid=True)
    
    def _validate_trading_frequency(self, order: Order) -> ValidationResult:
        """验证交易频率"""
        today = datetime.now().strftime("%Y-%m-%d")
        symbol_key = f"{today}_{order.symbol}"
        
        current_count = self.daily_trade_count.get(symbol_key, 0)
        
        # 检查单日交易次数限制
        if current_count >= self.risk_limits.max_daily_trades:
            return ValidationResult(
                is_valid=False,
                error_code="DAILY_TRADE_LIMIT",
                error_message=f"股票 {order.symbol} 今日交易次数 {current_count} 已达到限制 {self.risk_limits.max_daily_trades}"
            )
        
        return ValidationResult(is_valid=True)
    
    def _validate_risk_limits(self, order: Order, account_info: Dict[str, Any]) -> ValidationResult:
        """验证风险限制"""
        warnings = []
        
        # 检查订单价值限制
        estimated_cost = self._estimate_order_cost(order)
        
        if estimated_cost > self.risk_limits.max_order_value:
            return ValidationResult(
                is_valid=False,
                error_code="ORDER_VALUE_TOO_HIGH",
                error_message=f"订单价值 {estimated_cost:.2f} 超过限制 {self.risk_limits.max_order_value:.2f}"
            )
        
        if estimated_cost < self.risk_limits.min_order_value:
            warnings.append(f"订单价值 {estimated_cost:.2f} 低于建议最小值 {self.risk_limits.min_order_value:.2f}")
        
        return ValidationResult(is_valid=True, warnings=warnings)
    
    def _is_valid_symbol(self, symbol: str) -> bool:
        """检查股票代码是否有效"""
        # A股股票代码格式检查
        if len(symbol) != 6:
            return False
        
        if not symbol.isdigit():
            return False
        
        # 检查交易所代码
        if symbol.startswith(('000', '001', '002', '003')):  # 深交所
            return True
        elif symbol.startswith(('600', '601', '603', '605')):  # 上交所
            return True
        elif symbol.startswith(('300')):  # 创业板
            return True
        elif symbol.startswith(('688')):  # 科创板
            return True
        
        return False
    
    def _is_trading_time(self, current_time: str) -> bool:
        """检查是否在交易时间内"""
        # 上午交易时间: 09:30-11:30
        # 下午交易时间: 13:00-15:00
        # 测试模式：允许所有时间交易
        if hasattr(self, 'test_mode') and self.test_mode:
            return True
            
        if "09:30" <= current_time <= "11:30":
            return True
        if "13:00" <= current_time <= "15:00":
            return True
        return False
    
    def _estimate_order_cost(self, order: Order) -> float:
        """估算订单成本"""
        if order.price:
            base_cost = order.quantity * order.price
        else:
            # 市价单使用最新价格估算
            last_price = self.last_prices.get(order.symbol, 10.0)  # 默认价格
            base_cost = order.quantity * last_price
        
        # 加上手续费估算（千分之一）
        commission = base_cost * 0.001
        return base_cost + commission
    
    def update_daily_trade_count(self, symbol: str) -> None:
        """更新每日交易次数"""
        today = datetime.now().strftime("%Y-%m-%d")
        symbol_key = f"{today}_{symbol}"
        self.daily_trade_count[symbol_key] = self.daily_trade_count.get(symbol_key, 0) + 1
    
    def update_position_cache(self, symbol: str, quantity: int) -> None:
        """更新持仓缓存"""
        self.position_cache[symbol] = self.position_cache.get(symbol, 0) + quantity
    
    def update_price_cache(self, symbol: str, price: float) -> None:
        """更新价格缓存"""
        self.last_prices[symbol] = price
    
    def reset_daily_counters(self) -> None:
        """重置每日计数器"""
        today = datetime.now().strftime("%Y-%m-%d")
        # 清理过期的计数器
        keys_to_remove = [k for k in self.daily_trade_count.keys() if not k.startswith(today)]
        for key in keys_to_remove:
            del self.daily_trade_count[key]
        
        self.logger.info(f"已重置每日计数器，清理了 {len(keys_to_remove)} 个过期记录")