"""
智能订单管理系统
Intelligent Order Management System - 核心订单管理器
"""

import logging
import asyncio
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Any, Tuple
from dataclasses import dataclass
from collections import defaultdict, deque
from queue import PriorityQueue
import threading

from .models import Order, OrderType, OrderSide, OrderStatus, OrderPriority, OrderFill, AlgoOrderConfig
from .validator import OrderValidator, ValidationResult, RiskLimits
from .algo_executor import AlgoOrderManager, MarketData
from .monitor import OrderMonitor, OrderReporter, OrderAlert, AlertLevel


@dataclass
class OrderRequest:
    """订单请求"""
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: int
    price: Optional[float] = None
    stop_price: Optional[float] = None
    priority: OrderPriority = OrderPriority.NORMAL
    algo_config: Optional[AlgoOrderConfig] = None
    reason: str = ""
    source: str = "SYSTEM"
    tags: List[str] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []
        if self.metadata is None:
            self.metadata = {}


class OrderManager:
    """智能订单管理系统"""
    
    def __init__(self, 
                 broker_adapter,
                 market_data_provider: Optional[Callable] = None,
                 risk_limits: Optional[RiskLimits] = None):
        """
        初始化订单管理器
        
        Args:
            broker_adapter: 券商接口适配器
            market_data_provider: 市场数据提供者
            risk_limits: 风险限制配置
        """
        self.logger = logging.getLogger(__name__)
        self.broker_adapter = broker_adapter
        self.market_data_provider = market_data_provider
        
        # 初始化各个组件
        self.validator = OrderValidator(risk_limits)
        self.monitor = OrderMonitor(self._handle_alert)
        self.reporter = OrderReporter(self.monitor)
        self.algo_manager = AlgoOrderManager(self._execute_order, self._get_market_data)
        
        # 订单存储
        self.orders: Dict[str, Order] = {}
        self.order_queue = PriorityQueue()
        self.pending_orders: Dict[str, Order] = {}
        self.active_orders: Dict[str, Order] = {}
        self.completed_orders: Dict[str, Order] = {}
        
        # 执行统计
        self.execution_stats = {
            'total_orders': 0,
            'successful_orders': 0,
            'failed_orders': 0,
            'cancelled_orders': 0,
            'total_volume': 0,
            'total_value': 0.0
        }
        
        # 每日交易限制跟踪
        self.daily_trade_counts: Dict[str, int] = defaultdict(int)
        
        # 控制标志
        self.is_running = False
        self.processing_thread = None
        
        # 回调函数
        self.order_update_callbacks: List[Callable] = []
        self.fill_callbacks: List[Callable] = []
        
        self.logger.info("订单管理系统初始化完成")
    
    def start(self):
        """启动订单管理系统"""
        if not self.is_running:
            self.is_running = True
            self.processing_thread = threading.Thread(target=self._processing_loop, daemon=True)
            self.processing_thread.start()
            self.monitor.start_monitoring()
            self.logger.info("订单管理系统已启动")
    
    def stop(self):
        """停止订单管理系统"""
        self.is_running = False
        self.monitor.stop_monitoring()
        if self.processing_thread:
            self.processing_thread.join(timeout=5)
        self.logger.info("订单管理系统已停止")
    
    def _processing_loop(self):
        """订单处理循环"""
        while self.is_running:
            try:
                # 处理队列中的订单
                if not self.order_queue.empty():
                    priority, timestamp, order_id = self.order_queue.get(timeout=1)
                    if order_id in self.pending_orders:
                        order = self.pending_orders[order_id]
                        asyncio.run(self._process_order(order))
                
                # 检查活跃订单状态
                self._check_active_orders()
                
            except Exception as e:
                if self.is_running:  # 只在运行时记录错误
                    self.logger.error(f"订单处理循环异常: {e}")
                    asyncio.sleep(1)
    
    async def submit_order(self, request: OrderRequest) -> Tuple[bool, str, Optional[str]]:
        """
        提交订单
        
        Args:
            request: 订单请求
            
        Returns:
            Tuple[bool, str, Optional[str]]: (是否成功, 消息, 订单ID)
        """
        try:
            # 创建订单对象
            order = Order(
                order_id=str(uuid.uuid4()),
                symbol=request.symbol,
                side=request.side,
                order_type=request.order_type,
                quantity=request.quantity,
                price=request.price,
                stop_price=request.stop_price,
                priority=request.priority,
                reason=request.reason,
                source=request.source,
                tags=request.tags,
                metadata=request.metadata
            )
            
            # 如果是算法订单，保存算法配置
            if request.algo_config:
                order.algo_params = request.algo_config.to_dict()
            
            # 验证订单
            account_info = await self._get_account_info()
            validation_result = self.validator.validate_order(order, account_info)
            
            if not validation_result.is_valid:
                self.logger.warning(f"订单验证失败: {validation_result.error_message}")
                return False, validation_result.error_message, None
            
            # 检查每日交易次数限制（需求2.4）
            today = datetime.now().strftime('%Y-%m-%d')
            daily_key = f"{today}_{request.symbol}"
            if self.daily_trade_counts[daily_key] >= order.max_daily_trades:
                error_msg = f"股票 {request.symbol} 今日交易次数已达到限制 {order.max_daily_trades}"
                self.logger.warning(error_msg)
                return False, error_msg, None
            
            # 添加到系统
            self.orders[order.order_id] = order
            self.pending_orders[order.order_id] = order
            self.monitor.add_order(order)
            
            # 添加到处理队列
            priority_value = -request.priority.value  # 负值使高优先级排在前面
            timestamp = datetime.now().timestamp()
            self.order_queue.put((priority_value, timestamp, order.order_id))
            
            # 更新统计
            self.execution_stats['total_orders'] += 1
            
            self.logger.info(f"订单已提交: {order.order_id}, 股票: {request.symbol}, 方向: {request.side.value}, 数量: {request.quantity}")
            
            # 发送警告信息
            if validation_result.warnings:
                for warning in validation_result.warnings:
                    self.logger.warning(f"订单警告 {order.order_id}: {warning}")
            
            return True, "订单提交成功", order.order_id
            
        except Exception as e:
            self.logger.error(f"提交订单异常: {e}")
            return False, f"提交订单异常: {str(e)}", None
    
    async def _process_order(self, order: Order):
        """处理单个订单"""
        try:
            self.logger.info(f"开始处理订单: {order.order_id}")
            
            # 更新订单状态
            order.status = OrderStatus.SUBMITTED
            order.submit_time = datetime.now()
            self._update_order(order)
            
            # 移动到活跃订单
            if order.order_id in self.pending_orders:
                del self.pending_orders[order.order_id]
            self.active_orders[order.order_id] = order
            
            # 根据订单类型执行
            if order.order_type in [OrderType.TWAP, OrderType.VWAP]:
                # 算法订单
                await self._execute_algo_order(order)
            else:
                # 普通订单
                await self._execute_regular_order(order)
                
        except Exception as e:
            self.logger.error(f"处理订单异常 {order.order_id}: {e}")
            order.status = OrderStatus.FAILED
            order.reason = f"{order.reason}; 处理异常: {str(e)}"
            self._update_order(order)
            self._move_to_completed(order)
    
    async def _execute_regular_order(self, order: Order):
        """执行普通订单"""
        try:
            # 调用券商接口执行订单
            if order.order_type == OrderType.MARKET:
                result = await self.broker_adapter.place_market_order(
                    order.symbol, order.quantity, order.side.value
                )
            elif order.order_type == OrderType.LIMIT:
                result = await self.broker_adapter.place_limit_order(
                    order.symbol, order.quantity, order.price, order.side.value
                )
            else:
                raise ValueError(f"不支持的订单类型: {order.order_type}")
            
            if result.get('success', False):
                # 模拟成交（实际应该从券商接口获取）
                fill = OrderFill(
                    fill_id=str(uuid.uuid4()),
                    order_id=order.order_id,
                    symbol=order.symbol,
                    side=order.side,
                    quantity=order.quantity,
                    price=order.price or 10.0,  # 模拟价格
                    commission=order.quantity * (order.price or 10.0) * 0.001,  # 模拟手续费
                    timestamp=datetime.now()
                )
                
                order.add_fill(fill)
                self._update_order(order)
                
                # 更新每日交易计数
                self._update_daily_trade_count(order.symbol)
                
                # 调用成交回调
                await self._notify_fill_callbacks(fill)
                
                self.logger.info(f"订单执行成功: {order.order_id}, 成交数量: {fill.quantity}")
                
            else:
                order.status = OrderStatus.REJECTED
                order.reason = f"{order.reason}; 券商拒绝: {result.get('message', '未知错误')}"
                self._update_order(order)
                
        except Exception as e:
            self.logger.error(f"执行订单异常 {order.order_id}: {e}")
            order.status = OrderStatus.FAILED
            order.reason = f"{order.reason}; 执行异常: {str(e)}"
            self._update_order(order)
        finally:
            if order.is_completed:
                self._move_to_completed(order)
    
    async def _execute_algo_order(self, order: Order):
        """执行算法订单"""
        try:
            # 从订单参数中恢复算法配置
            algo_config = AlgoOrderConfig(**order.algo_params)
            
            # 执行算法订单
            fills = await self.algo_manager.execute_algo_order(order, algo_config)
            
            # 处理成交记录
            for fill in fills:
                order.add_fill(fill)
                await self._notify_fill_callbacks(fill)
            
            # 更新每日交易计数
            if fills:
                self._update_daily_trade_count(order.symbol)
            
            self._update_order(order)
            self.logger.info(f"算法订单执行完成: {order.order_id}, 总成交: {sum(f.quantity for f in fills)}")
            
        except Exception as e:
            self.logger.error(f"执行算法订单异常 {order.order_id}: {e}")
            order.status = OrderStatus.FAILED
            order.reason = f"{order.reason}; 算法执行异常: {str(e)}"
            self._update_order(order)
        finally:
            self._move_to_completed(order)
    
    def cancel_order(self, order_id: str, reason: str = "") -> bool:
        """
        取消订单
        
        Args:
            order_id: 订单ID
            reason: 取消原因
            
        Returns:
            bool: 是否成功取消
        """
        try:
            if order_id not in self.orders:
                self.logger.warning(f"未找到订单: {order_id}")
                return False
            
            order = self.orders[order_id]
            
            if not order.is_active:
                self.logger.warning(f"订单不是活跃状态，无法取消: {order_id}")
                return False
            
            # 如果是算法订单，取消算法执行
            if order.order_type in [OrderType.TWAP, OrderType.VWAP]:
                self.algo_manager.cancel_algo_order(order_id)
            
            # 更新订单状态
            order.cancel(reason)
            self._update_order(order)
            self._move_to_completed(order)
            
            # 更新统计
            self.execution_stats['cancelled_orders'] += 1
            
            self.logger.info(f"订单已取消: {order_id}, 原因: {reason}")
            return True
            
        except Exception as e:
            self.logger.error(f"取消订单异常 {order_id}: {e}")
            return False
    
    def get_order(self, order_id: str) -> Optional[Order]:
        """获取订单"""
        return self.orders.get(order_id)
    
    def get_orders_by_symbol(self, symbol: str) -> List[Order]:
        """获取指定股票的所有订单"""
        return [order for order in self.orders.values() if order.symbol == symbol]
    
    def get_active_orders(self) -> List[Order]:
        """获取活跃订单列表"""
        return list(self.active_orders.values())
    
    def get_order_status(self, order_id: str) -> Optional[OrderStatus]:
        """获取订单状态"""
        order = self.orders.get(order_id)
        return order.status if order else None
    
    def _update_order(self, order: Order):
        """更新订单"""
        order.update_time = datetime.now()
        self.orders[order.order_id] = order
        self.monitor.update_order(order)
        
        # 调用更新回调
        asyncio.create_task(self._notify_order_update_callbacks(order))
    
    def _move_to_completed(self, order: Order):
        """将订单移动到已完成列表"""
        if order.order_id in self.active_orders:
            del self.active_orders[order.order_id]
        self.completed_orders[order.order_id] = order
        
        # 更新统计
        if order.status == OrderStatus.FILLED:
            self.execution_stats['successful_orders'] += 1
            self.execution_stats['total_volume'] += order.filled_quantity
            self.execution_stats['total_value'] += sum(f.quantity * f.price for f in order.fills)
        elif order.status in [OrderStatus.REJECTED, OrderStatus.FAILED]:
            self.execution_stats['failed_orders'] += 1
    
    def _check_active_orders(self):
        """检查活跃订单状态"""
        current_time = datetime.now()
        
        for order_id, order in list(self.active_orders.items()):
            # 检查订单是否过期
            if order.expire_time and current_time > order.expire_time:
                order.status = OrderStatus.EXPIRED
                self._update_order(order)
                self._move_to_completed(order)
                self.logger.info(f"订单已过期: {order_id}")
    
    def _update_daily_trade_count(self, symbol: str):
        """更新每日交易次数"""
        today = datetime.now().strftime('%Y-%m-%d')
        daily_key = f"{today}_{symbol}"
        self.daily_trade_counts[daily_key] += 1
        
        # 同时更新验证器的计数
        self.validator.update_daily_trade_count(symbol)
    
    async def _get_account_info(self) -> Dict[str, Any]:
        """获取账户信息"""
        try:
            if hasattr(self.broker_adapter, 'get_account_info'):
                return await self.broker_adapter.get_account_info()
            else:
                # 模拟账户信息
                return {
                    'available_cash': 100000.0,
                    'total_value': 500000.0,
                    'positions': {}
                }
        except Exception as e:
            self.logger.error(f"获取账户信息异常: {e}")
            return {'available_cash': 0, 'total_value': 0, 'positions': {}}
    
    async def _get_market_data(self, symbol: str) -> MarketData:
        """获取市场数据"""
        try:
            if self.market_data_provider:
                data = await self.market_data_provider(symbol)
                return MarketData(**data)
            else:
                # 模拟市场数据
                return MarketData(
                    symbol=symbol,
                    price=10.0,
                    volume=1000000,
                    bid_price=9.99,
                    ask_price=10.01,
                    bid_volume=10000,
                    ask_volume=10000,
                    timestamp=datetime.now()
                )
        except Exception as e:
            self.logger.error(f"获取市场数据异常 {symbol}: {e}")
            # 返回默认数据
            return MarketData(
                symbol=symbol,
                price=10.0,
                volume=1000000,
                bid_price=9.99,
                ask_price=10.01,
                bid_volume=10000,
                ask_volume=10000,
                timestamp=datetime.now()
            )
    
    async def _execute_order(self, order: Order) -> List[OrderFill]:
        """执行订单（供算法管理器调用）"""
        # 模拟订单执行
        fill = OrderFill(
            fill_id=str(uuid.uuid4()),
            order_id=order.order_id,
            symbol=order.symbol,
            side=order.side,
            quantity=order.quantity,
            price=order.price or 10.0,
            commission=order.quantity * (order.price or 10.0) * 0.001,
            timestamp=datetime.now()
        )
        return [fill]
    
    async def _handle_alert(self, alert: OrderAlert):
        """处理告警"""
        self.logger.warning(f"订单告警: {alert.message}")
        
        # 根据告警级别采取行动
        if alert.level == AlertLevel.CRITICAL:
            # 严重告警，可能需要暂停相关订单
            order = self.orders.get(alert.order_id)
            if order and order.is_active:
                self.cancel_order(alert.order_id, f"严重告警自动取消: {alert.message}")
    
    async def _notify_order_update_callbacks(self, order: Order):
        """通知订单更新回调"""
        for callback in self.order_update_callbacks:
            try:
                await callback(order)
            except Exception as e:
                self.logger.error(f"订单更新回调异常: {e}")
    
    async def _notify_fill_callbacks(self, fill: OrderFill):
        """通知成交回调"""
        for callback in self.fill_callbacks:
            try:
                await callback(fill)
            except Exception as e:
                self.logger.error(f"成交回调异常: {e}")
    
    def add_order_update_callback(self, callback: Callable):
        """添加订单更新回调"""
        self.order_update_callbacks.append(callback)
    
    def add_fill_callback(self, callback: Callable):
        """添加成交回调"""
        self.fill_callbacks.append(callback)
    
    def get_execution_stats(self) -> Dict[str, Any]:
        """获取执行统计"""
        return self.execution_stats.copy()
    
    def get_daily_trade_count(self, symbol: str) -> int:
        """获取指定股票今日交易次数"""
        today = datetime.now().strftime('%Y-%m-%d')
        daily_key = f"{today}_{symbol}"
        return self.daily_trade_counts.get(daily_key, 0)
    
    def generate_daily_report(self, date: Optional[str] = None):
        """生成日度报告"""
        return self.reporter.generate_daily_report(date)
    
    def export_report(self, report, format: str = 'json', filepath: Optional[str] = None) -> str:
        """导出报告"""
        return self.reporter.export_report(report, format, filepath)
    
    def reset_daily_counters(self):
        """重置每日计数器"""
        today = datetime.now().strftime('%Y-%m-%d')
        # 清理过期的计数器
        keys_to_remove = [k for k in self.daily_trade_counts.keys() if not k.startswith(today)]
        for key in keys_to_remove:
            del self.daily_trade_counts[key]
        
        # 同时重置验证器的计数器
        self.validator.reset_daily_counters()
        
        self.logger.info(f"已重置每日计数器，清理了 {len(keys_to_remove)} 个过期记录")