"""
订单监控和报告系统
Order Monitor and Reporting System - 实现订单执行监控和报告系统
"""

import logging
import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
from enum import Enum

from .models import Order, OrderStatus, OrderFill, OrderType, OrderSide


class AlertLevel(Enum):
    """告警级别"""
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


@dataclass
class OrderAlert:
    """订单告警"""
    alert_id: str
    order_id: str
    level: AlertLevel
    message: str
    timestamp: datetime
    resolved: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'alert_id': self.alert_id,
            'order_id': self.order_id,
            'level': self.level.value,
            'message': self.message,
            'timestamp': self.timestamp.isoformat(),
            'resolved': self.resolved
        }


@dataclass
class OrderMetrics:
    """订单指标"""
    order_id: str
    symbol: str
    
    # 执行指标
    total_quantity: int
    filled_quantity: int
    remaining_quantity: int
    fill_ratio: float
    avg_fill_price: float
    
    # 时间指标
    create_time: datetime
    first_fill_time: Optional[datetime]
    last_fill_time: Optional[datetime]
    completion_time: Optional[datetime]
    execution_duration: Optional[float]  # 秒
    
    # 成本指标
    total_value: float
    total_commission: float
    slippage: float  # 滑点
    implementation_shortfall: float  # 实施缺口
    
    # 市场影响
    market_impact: float
    participation_rate: float
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'order_id': self.order_id,
            'symbol': self.symbol,
            'total_quantity': self.total_quantity,
            'filled_quantity': self.filled_quantity,
            'remaining_quantity': self.remaining_quantity,
            'fill_ratio': self.fill_ratio,
            'avg_fill_price': self.avg_fill_price,
            'create_time': self.create_time.isoformat(),
            'first_fill_time': self.first_fill_time.isoformat() if self.first_fill_time else None,
            'last_fill_time': self.last_fill_time.isoformat() if self.last_fill_time else None,
            'completion_time': self.completion_time.isoformat() if self.completion_time else None,
            'execution_duration': self.execution_duration,
            'total_value': self.total_value,
            'total_commission': self.total_commission,
            'slippage': self.slippage,
            'implementation_shortfall': self.implementation_shortfall,
            'market_impact': self.market_impact,
            'participation_rate': self.participation_rate
        }


@dataclass
class DailyReport:
    """日度报告"""
    date: str
    
    # 订单统计
    total_orders: int
    completed_orders: int
    cancelled_orders: int
    failed_orders: int
    
    # 成交统计
    total_volume: int
    total_value: float
    total_commission: float
    
    # 性能指标
    avg_fill_ratio: float
    avg_execution_time: float
    avg_slippage: float
    
    # 按股票统计
    symbol_stats: Dict[str, Dict[str, Any]]
    
    # 按算法统计
    algo_stats: Dict[str, Dict[str, Any]]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)


class OrderMonitor:
    """订单监控器"""
    
    def __init__(self, alert_callback: Optional[Callable] = None):
        """
        初始化订单监控器
        
        Args:
            alert_callback: 告警回调函数
        """
        self.logger = logging.getLogger(__name__)
        self.alert_callback = alert_callback
        
        # 监控数据
        self.orders: Dict[str, Order] = {}
        self.order_metrics: Dict[str, OrderMetrics] = {}
        self.alerts: List[OrderAlert] = []
        self.market_data_cache: Dict[str, Dict] = {}
        
        # 监控配置
        self.monitoring_config = {
            'max_execution_time': 3600,  # 最大执行时间（秒）
            'min_fill_ratio': 0.8,      # 最小成交比例
            'max_slippage': 0.01,       # 最大滑点
            'alert_thresholds': {
                'execution_time': 1800,  # 执行时间告警阈值
                'fill_ratio': 0.5,       # 成交比例告警阈值
                'slippage': 0.005        # 滑点告警阈值
            }
        }
        
        # 统计数据
        self.daily_stats = defaultdict(lambda: {
            'orders': 0,
            'volume': 0,
            'value': 0.0,
            'commission': 0.0
        })
        
        # 启动监控任务
        self.monitoring_task = None
        self.is_monitoring = False
    
    def start_monitoring(self):
        """启动监控"""
        if not self.is_monitoring:
            self.is_monitoring = True
            self.monitoring_task = asyncio.create_task(self._monitoring_loop())
            self.logger.info("订单监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        if self.monitoring_task:
            self.monitoring_task.cancel()
        self.logger.info("订单监控已停止")
    
    async def _monitoring_loop(self):
        """监控循环"""
        while self.is_monitoring:
            try:
                await self._check_orders()
                await asyncio.sleep(10)  # 每10秒检查一次
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"监控循环异常: {e}")
                await asyncio.sleep(30)
    
    async def _check_orders(self):
        """检查订单状态"""
        current_time = datetime.now()
        
        for order_id, order in self.orders.items():
            if not order.is_active:
                continue
            
            # 检查执行时间
            execution_time = (current_time - order.create_time).total_seconds()
            if execution_time > self.monitoring_config['alert_thresholds']['execution_time']:
                await self._create_alert(
                    order_id,
                    AlertLevel.WARNING,
                    f"订单执行时间过长: {execution_time:.1f}秒"
                )
            
            # 检查成交比例
            if order.filled_quantity > 0:
                fill_ratio = order.filled_quantity / order.quantity
                if fill_ratio < self.monitoring_config['alert_thresholds']['fill_ratio']:
                    await self._create_alert(
                        order_id,
                        AlertLevel.WARNING,
                        f"订单成交比例过低: {fill_ratio:.2%}"
                    )
            
            # 更新订单指标
            await self._update_order_metrics(order)
    
    def add_order(self, order: Order):
        """添加订单到监控"""
        self.orders[order.order_id] = order
        self.logger.info(f"添加订单到监控: {order.order_id}")
        
        # 初始化订单指标
        self._initialize_order_metrics(order)
    
    def update_order(self, order: Order):
        """更新订单状态"""
        if order.order_id in self.orders:
            self.orders[order.order_id] = order
            asyncio.create_task(self._update_order_metrics(order))
    
    def remove_order(self, order_id: str):
        """从监控中移除订单"""
        if order_id in self.orders:
            del self.orders[order_id]
            self.logger.info(f"从监控中移除订单: {order_id}")
    
    def _initialize_order_metrics(self, order: Order):
        """初始化订单指标"""
        self.order_metrics[order.order_id] = OrderMetrics(
            order_id=order.order_id,
            symbol=order.symbol,
            total_quantity=order.quantity,
            filled_quantity=0,
            remaining_quantity=order.quantity,
            fill_ratio=0.0,
            avg_fill_price=0.0,
            create_time=order.create_time,
            first_fill_time=None,
            last_fill_time=None,
            completion_time=None,
            execution_duration=None,
            total_value=0.0,
            total_commission=0.0,
            slippage=0.0,
            implementation_shortfall=0.0,
            market_impact=0.0,
            participation_rate=0.0
        )
    
    async def _update_order_metrics(self, order: Order):
        """更新订单指标"""
        if order.order_id not in self.order_metrics:
            self._initialize_order_metrics(order)
        
        metrics = self.order_metrics[order.order_id]
        
        # 更新基本指标
        metrics.filled_quantity = order.filled_quantity
        metrics.remaining_quantity = order.remaining_quantity
        metrics.fill_ratio = order.fill_ratio
        metrics.avg_fill_price = order.avg_fill_price
        
        # 更新时间指标
        if order.fills:
            metrics.first_fill_time = min(fill.timestamp for fill in order.fills)
            metrics.last_fill_time = max(fill.timestamp for fill in order.fills)
        
        if order.is_completed:
            metrics.completion_time = datetime.now()
            metrics.execution_duration = (metrics.completion_time - metrics.create_time).total_seconds()
        
        # 更新成本指标
        metrics.total_value = sum(fill.quantity * fill.price for fill in order.fills)
        metrics.total_commission = sum(fill.commission for fill in order.fills)
        
        # 计算滑点
        if order.price and order.avg_fill_price > 0:
            if order.is_buy:
                metrics.slippage = (order.avg_fill_price - order.price) / order.price
            else:
                metrics.slippage = (order.price - order.avg_fill_price) / order.price
        
        # 检查滑点告警
        if abs(metrics.slippage) > self.monitoring_config['alert_thresholds']['slippage']:
            await self._create_alert(
                order.order_id,
                AlertLevel.WARNING,
                f"订单滑点过大: {metrics.slippage:.2%}"
            )
    
    async def _create_alert(self, order_id: str, level: AlertLevel, message: str):
        """创建告警"""
        alert = OrderAlert(
            alert_id=f"{order_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            order_id=order_id,
            level=level,
            message=message,
            timestamp=datetime.now()
        )
        
        self.alerts.append(alert)
        self.logger.warning(f"订单告警: {alert.message}")
        
        # 调用告警回调
        if self.alert_callback:
            try:
                await self.alert_callback(alert)
            except Exception as e:
                self.logger.error(f"告警回调执行失败: {e}")
    
    def get_order_metrics(self, order_id: str) -> Optional[OrderMetrics]:
        """获取订单指标"""
        return self.order_metrics.get(order_id)
    
    def get_all_metrics(self) -> Dict[str, OrderMetrics]:
        """获取所有订单指标"""
        return self.order_metrics.copy()
    
    def get_alerts(self, order_id: Optional[str] = None, level: Optional[AlertLevel] = None) -> List[OrderAlert]:
        """获取告警列表"""
        alerts = self.alerts
        
        if order_id:
            alerts = [alert for alert in alerts if alert.order_id == order_id]
        
        if level:
            alerts = [alert for alert in alerts if alert.level == level]
        
        return alerts
    
    def resolve_alert(self, alert_id: str):
        """解决告警"""
        for alert in self.alerts:
            if alert.alert_id == alert_id:
                alert.resolved = True
                self.logger.info(f"告警已解决: {alert_id}")
                break


class OrderReporter:
    """订单报告生成器"""
    
    def __init__(self, monitor: OrderMonitor):
        """
        初始化报告生成器
        
        Args:
            monitor: 订单监控器
        """
        self.logger = logging.getLogger(__name__)
        self.monitor = monitor
    
    def generate_daily_report(self, date: Optional[str] = None) -> DailyReport:
        """
        生成日度报告
        
        Args:
            date: 日期，格式YYYY-MM-DD，默认为今天
            
        Returns:
            DailyReport: 日度报告
        """
        if date is None:
            date = datetime.now().strftime('%Y-%m-%d')
        
        # 筛选当日订单
        daily_orders = self._get_daily_orders(date)
        
        # 计算统计指标
        total_orders = len(daily_orders)
        completed_orders = len([o for o in daily_orders if o.status == OrderStatus.FILLED])
        cancelled_orders = len([o for o in daily_orders if o.status == OrderStatus.CANCELLED])
        failed_orders = len([o for o in daily_orders if o.status in [OrderStatus.REJECTED, OrderStatus.FAILED]])
        
        # 成交统计
        total_volume = sum(o.filled_quantity for o in daily_orders)
        total_value = sum(sum(f.quantity * f.price for f in o.fills) for o in daily_orders)
        total_commission = sum(sum(f.commission for f in o.fills) for o in daily_orders)
        
        # 性能指标
        fill_ratios = [o.fill_ratio for o in daily_orders if o.filled_quantity > 0]
        avg_fill_ratio = sum(fill_ratios) / len(fill_ratios) if fill_ratios else 0
        
        execution_times = []
        slippages = []
        
        for order in daily_orders:
            if order.order_id in self.monitor.order_metrics:
                metrics = self.monitor.order_metrics[order.order_id]
                if metrics.execution_duration:
                    execution_times.append(metrics.execution_duration)
                if metrics.slippage != 0:
                    slippages.append(abs(metrics.slippage))
        
        avg_execution_time = sum(execution_times) / len(execution_times) if execution_times else 0
        avg_slippage = sum(slippages) / len(slippages) if slippages else 0
        
        # 按股票统计
        symbol_stats = self._calculate_symbol_stats(daily_orders)
        
        # 按算法统计
        algo_stats = self._calculate_algo_stats(daily_orders)
        
        report = DailyReport(
            date=date,
            total_orders=total_orders,
            completed_orders=completed_orders,
            cancelled_orders=cancelled_orders,
            failed_orders=failed_orders,
            total_volume=total_volume,
            total_value=total_value,
            total_commission=total_commission,
            avg_fill_ratio=avg_fill_ratio,
            avg_execution_time=avg_execution_time,
            avg_slippage=avg_slippage,
            symbol_stats=symbol_stats,
            algo_stats=algo_stats
        )
        
        self.logger.info(f"生成日度报告: {date}")
        return report
    
    def _get_daily_orders(self, date: str) -> List[Order]:
        """获取指定日期的订单"""
        target_date = datetime.strptime(date, '%Y-%m-%d').date()
        
        daily_orders = []
        for order in self.monitor.orders.values():
            if order.create_time.date() == target_date:
                daily_orders.append(order)
        
        return daily_orders
    
    def _calculate_symbol_stats(self, orders: List[Order]) -> Dict[str, Dict[str, Any]]:
        """计算按股票的统计"""
        symbol_stats = defaultdict(lambda: {
            'orders': 0,
            'volume': 0,
            'value': 0.0,
            'commission': 0.0,
            'avg_fill_ratio': 0.0
        })
        
        for order in orders:
            stats = symbol_stats[order.symbol]
            stats['orders'] += 1
            stats['volume'] += order.filled_quantity
            
            order_value = sum(f.quantity * f.price for f in order.fills)
            order_commission = sum(f.commission for f in order.fills)
            
            stats['value'] += order_value
            stats['commission'] += order_commission
        
        # 计算平均成交比例
        for symbol, stats in symbol_stats.items():
            symbol_orders = [o for o in orders if o.symbol == symbol and o.filled_quantity > 0]
            if symbol_orders:
                fill_ratios = [o.fill_ratio for o in symbol_orders]
                stats['avg_fill_ratio'] = sum(fill_ratios) / len(fill_ratios)
        
        return dict(symbol_stats)
    
    def _calculate_algo_stats(self, orders: List[Order]) -> Dict[str, Dict[str, Any]]:
        """计算按算法的统计"""
        algo_stats = defaultdict(lambda: {
            'orders': 0,
            'volume': 0,
            'value': 0.0,
            'avg_execution_time': 0.0,
            'avg_slippage': 0.0
        })
        
        for order in orders:
            algo_type = order.order_type.value
            stats = algo_stats[algo_type]
            stats['orders'] += 1
            stats['volume'] += order.filled_quantity
            
            order_value = sum(f.quantity * f.price for f in order.fills)
            stats['value'] += order_value
        
        # 计算平均执行时间和滑点
        for algo_type, stats in algo_stats.items():
            algo_orders = [o for o in orders if o.order_type.value == algo_type]
            
            execution_times = []
            slippages = []
            
            for order in algo_orders:
                if order.order_id in self.monitor.order_metrics:
                    metrics = self.monitor.order_metrics[order.order_id]
                    if metrics.execution_duration:
                        execution_times.append(metrics.execution_duration)
                    if metrics.slippage != 0:
                        slippages.append(abs(metrics.slippage))
            
            if execution_times:
                stats['avg_execution_time'] = sum(execution_times) / len(execution_times)
            if slippages:
                stats['avg_slippage'] = sum(slippages) / len(slippages)
        
        return dict(algo_stats)
    
    def export_report(self, report: DailyReport, format: str = 'json', filepath: Optional[str] = None) -> str:
        """
        导出报告
        
        Args:
            report: 报告对象
            format: 导出格式 ('json', 'csv')
            filepath: 文件路径，默认自动生成
            
        Returns:
            str: 导出文件路径
        """
        if filepath is None:
            filepath = f"order_report_{report.date}.{format}"
        
        if format.lower() == 'json':
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(report.to_dict(), f, ensure_ascii=False, indent=2)
        elif format.lower() == 'csv':
            # 简化的CSV导出
            import csv
            with open(filepath, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(['指标', '数值'])
                writer.writerow(['日期', report.date])
                writer.writerow(['总订单数', report.total_orders])
                writer.writerow(['完成订单数', report.completed_orders])
                writer.writerow(['取消订单数', report.cancelled_orders])
                writer.writerow(['失败订单数', report.failed_orders])
                writer.writerow(['总成交量', report.total_volume])
                writer.writerow(['总成交额', f"{report.total_value:.2f}"])
                writer.writerow(['总手续费', f"{report.total_commission:.2f}"])
                writer.writerow(['平均成交比例', f"{report.avg_fill_ratio:.2%}"])
                writer.writerow(['平均执行时间', f"{report.avg_execution_time:.1f}秒"])
                writer.writerow(['平均滑点', f"{report.avg_slippage:.4f}"])
        else:
            raise ValueError(f"不支持的导出格式: {format}")
        
        self.logger.info(f"报告已导出: {filepath}")
        return filepath