"""
算法订单执行器
Algorithm Order Executor - 实现TWAP、VWAP等订单执行优化算法
"""

import logging
import asyncio
import math
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
from abc import ABC, abstractmethod

from .models import Order, OrderType, OrderSide, OrderStatus, AlgoOrderConfig, OrderFill


@dataclass
class MarketData:
    """市场数据"""
    symbol: str
    price: float
    volume: int
    bid_price: float
    ask_price: float
    bid_volume: int
    ask_volume: int
    timestamp: datetime
    
    @property
    def mid_price(self) -> float:
        """中间价"""
        return (self.bid_price + self.ask_price) / 2
    
    @property
    def spread(self) -> float:
        """买卖价差"""
        return self.ask_price - self.bid_price
    
    @property
    def spread_pct(self) -> float:
        """买卖价差百分比"""
        return self.spread / self.mid_price if self.mid_price > 0 else 0


@dataclass
class SliceOrder:
    """切片订单"""
    slice_id: str
    parent_order_id: str
    symbol: str
    side: OrderSide
    quantity: int
    target_price: Optional[float] = None
    max_price: Optional[float] = None
    min_price: Optional[float] = None
    urgency: float = 0.5  # 紧急程度 0-1
    created_time: datetime = None
    
    def __post_init__(self):
        if self.created_time is None:
            self.created_time = datetime.now()


class AlgorithmExecutor(ABC):
    """算法执行器基类"""
    
    def __init__(self, order_executor: Callable, market_data_provider: Callable):
        """
        初始化算法执行器
        
        Args:
            order_executor: 订单执行函数
            market_data_provider: 市场数据提供函数
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self.order_executor = order_executor
        self.market_data_provider = market_data_provider
        self.is_running = False
        
    @abstractmethod
    async def execute(self, order: Order, config: AlgoOrderConfig) -> List[OrderFill]:
        """执行算法订单"""
        pass
    
    def stop(self):
        """停止执行"""
        self.is_running = False


class TWAPExecutor(AlgorithmExecutor):
    """时间加权平均价格算法执行器"""
    
    async def execute(self, order: Order, config: AlgoOrderConfig) -> List[OrderFill]:
        """
        执行TWAP算法
        
        TWAP策略将大订单分解为多个小订单，在指定时间段内均匀执行
        """
        self.logger.info(f"开始执行TWAP算法，订单ID: {order.order_id}")
        
        fills = []
        self.is_running = True
        
        try:
            # 从字符串恢复为datetime对象
            if isinstance(config.start_time, str):
                config.start_time = datetime.fromisoformat(config.start_time)
            if isinstance(config.end_time, str):
                config.end_time = datetime.fromisoformat(config.end_time)
                
            # 计算执行参数
            total_duration = (config.end_time - config.start_time).total_seconds()
            slice_count = max(1, int(total_duration / 60))  # 每分钟一个切片
            slice_size = max(config.min_slice_size, order.quantity // slice_count)
            slice_interval = total_duration / slice_count
            
            remaining_quantity = order.quantity
            slice_number = 0
            
            self.logger.info(f"TWAP参数: 总时长={total_duration}s, 切片数={slice_count}, 切片大小={slice_size}, 间隔={slice_interval}s")
            
            # 等待开始时间
            if datetime.now() < config.start_time:
                wait_time = (config.start_time - datetime.now()).total_seconds()
                self.logger.info(f"等待开始时间，等待 {wait_time:.1f} 秒")
                await asyncio.sleep(wait_time)
            
            # 执行切片订单
            while remaining_quantity > 0 and self.is_running and datetime.now() < config.end_time:
                slice_number += 1
                
                # 计算当前切片大小
                current_slice_size = min(slice_size, remaining_quantity)
                if slice_number == slice_count:  # 最后一个切片包含所有剩余数量
                    current_slice_size = remaining_quantity
                
                # 获取市场数据
                market_data = await self.market_data_provider(order.symbol)
                
                # 创建切片订单
                slice_order = SliceOrder(
                    slice_id=f"{order.order_id}_slice_{slice_number}",
                    parent_order_id=order.order_id,
                    symbol=order.symbol,
                    side=order.side,
                    quantity=current_slice_size,
                    target_price=self._calculate_twap_price(market_data, config),
                    urgency=config.urgency
                )
                
                # 执行切片订单
                slice_fills = await self._execute_slice(slice_order, market_data, config)
                fills.extend(slice_fills)
                
                # 更新剩余数量
                filled_quantity = sum(fill.quantity for fill in slice_fills)
                remaining_quantity -= filled_quantity
                
                self.logger.info(f"TWAP切片 {slice_number} 执行完成，成交 {filled_quantity}，剩余 {remaining_quantity}")
                
                # 等待下一个切片
                if remaining_quantity > 0 and datetime.now() < config.end_time:
                    await asyncio.sleep(slice_interval)
            
            # 处理剩余数量（如果时间到了但还有未成交的）
            if remaining_quantity > 0:
                self.logger.warning(f"TWAP执行结束，仍有 {remaining_quantity} 未成交")
                # 可以选择以市价单快速成交剩余数量
                if config.urgency > 0.8:
                    market_data = await self.market_data_provider(order.symbol)
                    final_slice = SliceOrder(
                        slice_id=f"{order.order_id}_final",
                        parent_order_id=order.order_id,
                        symbol=order.symbol,
                        side=order.side,
                        quantity=remaining_quantity,
                        urgency=1.0
                    )
                    final_fills = await self._execute_slice(final_slice, market_data, config)
                    fills.extend(final_fills)
            
            self.logger.info(f"TWAP算法执行完成，总成交 {sum(fill.quantity for fill in fills)} 股")
            return fills
            
        except Exception as e:
            self.logger.error(f"TWAP执行异常: {e}")
            raise
        finally:
            self.is_running = False
    
    def _calculate_twap_price(self, market_data: MarketData, config: AlgoOrderConfig) -> float:
        """计算TWAP目标价格"""
        # 基于中间价和紧急程度调整
        base_price = market_data.mid_price
        
        # 根据紧急程度调整价格
        if config.urgency > 0.7:
            # 高紧急程度，接近市价
            return market_data.ask_price if market_data.ask_price else base_price
        elif config.urgency < 0.3:
            # 低紧急程度，更保守的价格
            spread_adjustment = market_data.spread * 0.3
            return base_price - spread_adjustment
        else:
            # 中等紧急程度，使用中间价
            return base_price
    
    async def _execute_slice(self, slice_order: SliceOrder, market_data: MarketData, config: AlgoOrderConfig) -> List[OrderFill]:
        """执行单个切片订单"""
        try:
            # 创建实际订单
            actual_order = Order(
                order_id=slice_order.slice_id,
                symbol=slice_order.symbol,
                side=slice_order.side,
                order_type=OrderType.LIMIT,
                quantity=slice_order.quantity,
                price=slice_order.target_price,
                parent_order_id=slice_order.parent_order_id
            )
            
            # 执行订单
            fills = await self.order_executor(actual_order)
            return fills
            
        except Exception as e:
            self.logger.error(f"切片订单执行失败: {e}")
            return []


class VWAPExecutor(AlgorithmExecutor):
    """成交量加权平均价格算法执行器"""
    
    def __init__(self, order_executor: Callable, market_data_provider: Callable):
        super().__init__(order_executor, market_data_provider)
        self.volume_profile = {}  # 历史成交量分布
        
    async def execute(self, order: Order, config: AlgoOrderConfig) -> List[OrderFill]:
        """
        执行VWAP算法
        
        VWAP策略根据历史成交量分布来分配订单执行时间和数量
        """
        self.logger.info(f"开始执行VWAP算法，订单ID: {order.order_id}")
        
        fills = []
        self.is_running = True
        
        try:
            # 从字符串恢复为datetime对象
            if isinstance(config.start_time, str):
                config.start_time = datetime.fromisoformat(config.start_time)
            if isinstance(config.end_time, str):
                config.end_time = datetime.fromisoformat(config.end_time)
                
            # 获取历史成交量分布
            volume_profile = await self._get_volume_profile(order.symbol, config)
            
            # 计算执行计划
            execution_plan = self._create_vwap_plan(order, config, volume_profile)
            
            self.logger.info(f"VWAP执行计划: {len(execution_plan)} 个时间段")
            
            # 等待开始时间
            if datetime.now() < config.start_time:
                wait_time = (config.start_time - datetime.now()).total_seconds()
                await asyncio.sleep(wait_time)
            
            # 按计划执行
            for i, plan_item in enumerate(execution_plan):
                if not self.is_running or datetime.now() > config.end_time:
                    break
                
                # 等待到执行时间
                if datetime.now() < plan_item['time']:
                    wait_time = (plan_item['time'] - datetime.now()).total_seconds()
                    if wait_time > 0:
                        await asyncio.sleep(wait_time)
                
                # 获取当前市场数据
                market_data = await self.market_data_provider(order.symbol)
                
                # 调整执行数量（基于当前市场流动性）
                adjusted_quantity = self._adjust_quantity_for_liquidity(
                    plan_item['quantity'], market_data, config
                )
                
                if adjusted_quantity > 0:
                    # 创建切片订单
                    slice_order = SliceOrder(
                        slice_id=f"{order.order_id}_vwap_{i}",
                        parent_order_id=order.order_id,
                        symbol=order.symbol,
                        side=order.side,
                        quantity=adjusted_quantity,
                        target_price=self._calculate_vwap_price(market_data, config),
                        urgency=config.urgency
                    )
                    
                    # 执行切片
                    slice_fills = await self._execute_slice(slice_order, market_data, config)
                    fills.extend(slice_fills)
                    
                    filled_quantity = sum(fill.quantity for fill in slice_fills)
                    self.logger.info(f"VWAP时段 {i+1} 执行完成，计划 {plan_item['quantity']}，实际成交 {filled_quantity}")
            
            self.logger.info(f"VWAP算法执行完成，总成交 {sum(fill.quantity for fill in fills)} 股")
            return fills
            
        except Exception as e:
            self.logger.error(f"VWAP执行异常: {e}")
            raise
        finally:
            self.is_running = False
    
    async def _get_volume_profile(self, symbol: str, config: AlgoOrderConfig) -> Dict[str, float]:
        """获取历史成交量分布"""
        # 这里应该从历史数据中获取成交量分布
        # 简化实现：使用典型的A股成交量分布模式
        return {
            "09:30-10:00": 0.15,  # 开盘高成交量
            "10:00-10:30": 0.12,
            "10:30-11:00": 0.08,
            "11:00-11:30": 0.06,
            "13:00-13:30": 0.10,
            "13:30-14:00": 0.08,
            "14:00-14:30": 0.12,
            "14:30-15:00": 0.29   # 收盘高成交量
        }
    
    def _create_vwap_plan(self, order: Order, config: AlgoOrderConfig, volume_profile: Dict[str, float]) -> List[Dict]:
        """创建VWAP执行计划"""
        plan = []
        total_duration = (config.end_time - config.start_time).total_seconds()
        
        # 将执行时间分成多个时段
        time_slots = 8  # 分成8个时段
        slot_duration = total_duration / time_slots
        
        for i in range(time_slots):
            slot_start = config.start_time + timedelta(seconds=i * slot_duration)
            slot_end = slot_start + timedelta(seconds=slot_duration)
            
            # 根据历史成交量分布分配数量
            time_key = f"{slot_start.strftime('%H:%M')}-{slot_end.strftime('%H:%M')}"
            volume_weight = volume_profile.get(time_key, 1.0 / time_slots)
            
            # 考虑参与率
            planned_quantity = int(order.quantity * volume_weight * config.participation_rate)
            planned_quantity = max(config.min_slice_size, min(config.max_slice_size, planned_quantity))
            
            if planned_quantity > 0:
                plan.append({
                    'time': slot_start,
                    'quantity': planned_quantity,
                    'volume_weight': volume_weight
                })
        
        return plan
    
    def _adjust_quantity_for_liquidity(self, planned_quantity: int, market_data: MarketData, config: AlgoOrderConfig) -> int:
        """根据当前流动性调整执行数量"""
        # 基于买卖盘深度调整数量
        available_liquidity = market_data.bid_volume if market_data.bid_volume > 0 else 1000
        
        # 不超过可用流动性的参与率
        max_quantity = int(available_liquidity * config.participation_rate)
        
        return min(planned_quantity, max_quantity)
    
    def _calculate_vwap_price(self, market_data: MarketData, config: AlgoOrderConfig) -> float:
        """计算VWAP目标价格"""
        # 基于当前VWAP和市场状况调整价格
        base_price = market_data.mid_price
        
        # 根据风险厌恶程度调整
        if config.risk_aversion > 0.7:
            # 高风险厌恶，更保守的价格
            return base_price - market_data.spread * 0.2
        elif config.risk_aversion < 0.3:
            # 低风险厌恶，更激进的价格
            return market_data.ask_price if market_data.ask_price else base_price
        else:
            return base_price
    
    async def _execute_slice(self, slice_order: SliceOrder, market_data: MarketData, config: AlgoOrderConfig) -> List[OrderFill]:
        """执行单个切片订单"""
        try:
            actual_order = Order(
                order_id=slice_order.slice_id,
                symbol=slice_order.symbol,
                side=slice_order.side,
                order_type=OrderType.LIMIT,
                quantity=slice_order.quantity,
                price=slice_order.target_price,
                parent_order_id=slice_order.parent_order_id
            )
            
            fills = await self.order_executor(actual_order)
            return fills
            
        except Exception as e:
            self.logger.error(f"VWAP切片订单执行失败: {e}")
            return []


class POVExecutor(AlgorithmExecutor):
    """参与率算法执行器 (Percentage of Volume)"""
    
    async def execute(self, order: Order, config: AlgoOrderConfig) -> List[OrderFill]:
        """
        执行POV算法
        
        POV策略根据市场成交量的固定百分比来执行订单
        """
        self.logger.info(f"开始执行POV算法，订单ID: {order.order_id}，参与率: {config.participation_rate:.2%}")
        
        fills = []
        self.is_running = True
        remaining_quantity = order.quantity
        
        try:
            # 从字符串恢复为datetime对象
            if isinstance(config.start_time, str):
                config.start_time = datetime.fromisoformat(config.start_time)
            if isinstance(config.end_time, str):
                config.end_time = datetime.fromisoformat(config.end_time)
                
            # 等待开始时间
            if datetime.now() < config.start_time:
                wait_time = (config.start_time - datetime.now()).total_seconds()
                await asyncio.sleep(wait_time)
            
            # 持续监控市场成交量并执行
            while remaining_quantity > 0 and self.is_running and datetime.now() < config.end_time:
                # 获取市场数据
                market_data = await self.market_data_provider(order.symbol)
                
                # 计算基于成交量的执行数量
                market_volume = market_data.volume
                target_quantity = int(market_volume * config.participation_rate)
                
                # 限制在配置范围内
                target_quantity = max(config.min_slice_size, min(config.max_slice_size, target_quantity))
                target_quantity = min(target_quantity, remaining_quantity)
                
                if target_quantity > 0:
                    # 创建切片订单
                    slice_order = SliceOrder(
                        slice_id=f"{order.order_id}_pov_{datetime.now().strftime('%H%M%S')}",
                        parent_order_id=order.order_id,
                        symbol=order.symbol,
                        side=order.side,
                        quantity=target_quantity,
                        target_price=market_data.mid_price,
                        urgency=config.urgency
                    )
                    
                    # 执行切片
                    slice_fills = await self._execute_slice(slice_order, market_data, config)
                    fills.extend(slice_fills)
                    
                    filled_quantity = sum(fill.quantity for fill in slice_fills)
                    remaining_quantity -= filled_quantity
                    
                    self.logger.info(f"POV执行，市场成交量: {market_volume}，目标数量: {target_quantity}，实际成交: {filled_quantity}")
                
                # 等待一段时间再次检查
                await asyncio.sleep(30)  # 30秒检查一次
            
            self.logger.info(f"POV算法执行完成，总成交 {sum(fill.quantity for fill in fills)} 股")
            return fills
            
        except Exception as e:
            self.logger.error(f"POV执行异常: {e}")
            raise
        finally:
            self.is_running = False
    
    async def _execute_slice(self, slice_order: SliceOrder, market_data: MarketData, config: AlgoOrderConfig) -> List[OrderFill]:
        """执行单个切片订单"""
        try:
            actual_order = Order(
                order_id=slice_order.slice_id,
                symbol=slice_order.symbol,
                side=slice_order.side,
                order_type=OrderType.LIMIT,
                quantity=slice_order.quantity,
                price=slice_order.target_price,
                parent_order_id=slice_order.parent_order_id
            )
            
            fills = await self.order_executor(actual_order)
            return fills
            
        except Exception as e:
            self.logger.error(f"POV切片订单执行失败: {e}")
            return []


class AlgoOrderManager:
    """算法订单管理器"""
    
    def __init__(self, order_executor: Callable, market_data_provider: Callable):
        """
        初始化算法订单管理器
        
        Args:
            order_executor: 订单执行函数
            market_data_provider: 市场数据提供函数
        """
        self.logger = logging.getLogger(__name__)
        self.order_executor = order_executor
        self.market_data_provider = market_data_provider
        
        # 初始化各种算法执行器
        self.executors = {
            'TWAP': TWAPExecutor(order_executor, market_data_provider),
            'VWAP': VWAPExecutor(order_executor, market_data_provider),
            'POV': POVExecutor(order_executor, market_data_provider)
        }
        
        # 运行中的算法订单
        self.running_orders: Dict[str, AlgorithmExecutor] = {}
    
    async def execute_algo_order(self, order: Order, config: AlgoOrderConfig) -> List[OrderFill]:
        """
        执行算法订单
        
        Args:
            order: 订单对象
            config: 算法配置
            
        Returns:
            List[OrderFill]: 成交记录列表
        """
        algo_type = config.algo_type.upper()
        
        if algo_type not in self.executors:
            raise ValueError(f"不支持的算法类型: {algo_type}")
        
        executor = self.executors[algo_type]
        self.running_orders[order.order_id] = executor
        
        try:
            self.logger.info(f"开始执行算法订单: {order.order_id}, 算法: {algo_type}")
            fills = await executor.execute(order, config)
            self.logger.info(f"算法订单执行完成: {order.order_id}, 成交数量: {sum(fill.quantity for fill in fills)}")
            return fills
            
        except Exception as e:
            self.logger.error(f"算法订单执行失败: {order.order_id}, 错误: {e}")
            raise
        finally:
            # 清理运行记录
            if order.order_id in self.running_orders:
                del self.running_orders[order.order_id]
    
    def cancel_algo_order(self, order_id: str) -> bool:
        """
        取消算法订单
        
        Args:
            order_id: 订单ID
            
        Returns:
            bool: 是否成功取消
        """
        if order_id in self.running_orders:
            executor = self.running_orders[order_id]
            executor.stop()
            self.logger.info(f"已取消算法订单: {order_id}")
            return True
        else:
            self.logger.warning(f"未找到运行中的算法订单: {order_id}")
            return False
    
    def get_running_orders(self) -> List[str]:
        """获取运行中的算法订单列表"""
        return list(self.running_orders.keys())
    
    def is_order_running(self, order_id: str) -> bool:
        """检查订单是否正在运行"""
        return order_id in self.running_orders