"""
订单管理系统集成测试
Order Management System Integration Test - 完整测试智能订单管理系统
"""

import asyncio
import logging
import time
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, List

from .manager import OrderManager, OrderRequest
from .models import OrderType, OrderSide, OrderPriority, AlgoOrderConfig, Order, OrderFill
from .validator import RiskLimits
from .monitor import OrderMonitor, AlertLevel


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)


class MockBrokerAdapter:
    """模拟券商接口"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.account_info = {
            'available_cash': 100000.0,
            'total_value': 500000.0,
            'positions': {'000001': 1000, '600000': 2000}
        }
        self.order_counter = 0
        self.execution_delay = 0.1  # 模拟执行延迟
    
    async def get_account_info(self) -> Dict[str, Any]:
        """获取账户信息"""
        return self.account_info.copy()
    
    async def place_market_order(self, symbol: str, quantity: int, side: str) -> Dict[str, Any]:
        """下市价单"""
        await asyncio.sleep(self.execution_delay)
        self.order_counter += 1
        
        # 模拟成功执行
        return {
            'success': True,
            'broker_order_id': f'BROKER_{self.order_counter}',
            'message': '订单已提交'
        }
    
    async def place_limit_order(self, symbol: str, quantity: int, price: float, side: str) -> Dict[str, Any]:
        """下限价单"""
        await asyncio.sleep(self.execution_delay)
        self.order_counter += 1
        
        # 模拟成功执行
        return {
            'success': True,
            'broker_order_id': f'BROKER_{self.order_counter}',
            'message': '订单已提交'
        }


async def mock_market_data_provider(symbol: str) -> Dict[str, Any]:
    """模拟市场数据提供者"""
    # 模拟不同股票的价格
    prices = {
        '000001': 10.50,
        '600000': 15.80,
        '000002': 8.30,
        '600036': 25.60
    }
    
    base_price = prices.get(symbol, 10.0)
    
    return {
        'symbol': symbol,
        'price': base_price,
        'volume': 1000000,
        'bid_price': base_price - 0.01,
        'ask_price': base_price + 0.01,
        'bid_volume': 10000,
        'ask_volume': 10000,
        'timestamp': datetime.now()
    }


class OrderManagementSystemTest:
    """订单管理系统测试类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.broker_adapter = MockBrokerAdapter()
        
        # 配置风险限制
        self.risk_limits = RiskLimits(
            max_position_value=50000.0,
            max_single_position_pct=0.2,
            max_daily_trades=50,
            max_order_value=20000.0,
            min_order_value=1000.0,
            max_price_deviation_pct=0.05,
            min_available_cash=5000.0
        )
        
        # 初始化订单管理器
        self.order_manager = OrderManager(
            broker_adapter=self.broker_adapter,
            market_data_provider=mock_market_data_provider,
            risk_limits=self.risk_limits
        )
        
        # 测试结果统计
        self.test_results = {
            'total_tests': 0,
            'passed_tests': 0,
            'failed_tests': 0,
            'test_details': []
        }
    
    async def run_all_tests(self):
        """运行所有测试"""
        self.logger.info("=" * 60)
        self.logger.info("开始订单管理系统集成测试")
        self.logger.info("=" * 60)
        
        # 启动订单管理器
        self.order_manager.start()
        
        try:
            # 1. 测试订单生命周期管理
            await self.test_order_lifecycle_management()
            
            # 2. 测试订单执行优化算法
            await self.test_order_execution_algorithms()
            
            # 3. 测试订单风险检查和预处理
            await self.test_order_risk_validation()
            
            # 4. 测试订单执行监控和报告
            await self.test_order_monitoring_and_reporting()
            
            # 5. 测试高并发场景
            await self.test_concurrent_orders()
            
            # 6. 测试异常处理
            await self.test_error_handling()
            
        finally:
            # 停止订单管理器
            self.order_manager.stop()
        
        # 输出测试结果
        self._print_test_summary()
    
    async def test_order_lifecycle_management(self):
        """测试订单生命周期管理"""
        self.logger.info("\n" + "=" * 40)
        self.logger.info("测试1: 订单生命周期管理")
        self.logger.info("=" * 40)
        
        test_cases = [
            {
                'name': '市价买入订单',
                'request': OrderRequest(
                    symbol='000001',
                    side=OrderSide.BUY,
                    order_type=OrderType.MARKET,
                    quantity=1000,
                    reason='测试市价买入'
                )
            },
            {
                'name': '限价卖出订单',
                'request': OrderRequest(
                    symbol='600000',
                    side=OrderSide.SELL,
                    order_type=OrderType.LIMIT,
                    quantity=500,
                    price=15.90,
                    reason='测试限价卖出'
                )
            },
            {
                'name': '高优先级订单',
                'request': OrderRequest(
                    symbol='000002',
                    side=OrderSide.BUY,
                    order_type=OrderType.MARKET,
                    quantity=800,
                    priority=OrderPriority.HIGH,
                    reason='测试高优先级订单'
                )
            }
        ]
        
        for test_case in test_cases:
            await self._run_single_test(
                test_case['name'],
                self._test_single_order_lifecycle,
                test_case['request']
            )
    
    async def _test_single_order_lifecycle(self, request: OrderRequest):
        """测试单个订单的生命周期"""
        # 提交订单
        success, message, order_id = await self.order_manager.submit_order(request)
        
        if not success:
            raise Exception(f"订单提交失败: {message}")
        
        self.logger.info(f"订单提交成功: {order_id}")
        
        # 等待订单处理
        await asyncio.sleep(1)
        
        # 检查订单状态
        order = self.order_manager.get_order(order_id)
        if not order:
            raise Exception(f"未找到订单: {order_id}")
        
        self.logger.info(f"订单状态: {order.status.value}, 成交数量: {order.filled_quantity}")
        
        # 验证订单基本信息
        assert order.symbol == request.symbol
        assert order.side == request.side
        assert order.order_type == request.order_type
        assert order.quantity == request.quantity
        
        # 等待订单完成
        max_wait = 10
        wait_count = 0
        while not order.is_completed and wait_count < max_wait:
            await asyncio.sleep(0.5)
            order = self.order_manager.get_order(order_id)
            wait_count += 1
        
        if order.is_completed:
            self.logger.info(f"订单已完成: {order.status.value}")
            
            # 验证成交记录
            if order.fills:
                total_filled = sum(fill.quantity for fill in order.fills)
                self.logger.info(f"成交记录数: {len(order.fills)}, 总成交量: {total_filled}")
        else:
            self.logger.warning(f"订单未在预期时间内完成: {order.status.value}")
    
    async def test_order_execution_algorithms(self):
        """测试订单执行优化算法（TWAP、VWAP等）"""
        self.logger.info("\n" + "=" * 40)
        self.logger.info("测试2: 订单执行优化算法")
        self.logger.info("=" * 40)
        
        # 测试TWAP算法
        await self._run_single_test(
            'TWAP算法订单',
            self._test_twap_algorithm
        )
        
        # 测试VWAP算法
        await self._run_single_test(
            'VWAP算法订单',
            self._test_vwap_algorithm
        )
    
    async def _test_twap_algorithm(self):
        """测试TWAP算法"""
        # 创建TWAP算法配置
        start_time = datetime.now() + timedelta(seconds=2)
        end_time = start_time + timedelta(seconds=10)
        
        algo_config = AlgoOrderConfig(
            algo_type='TWAP',
            start_time=start_time,
            end_time=end_time,
            participation_rate=0.2,
            max_slice_size=500,
            min_slice_size=100,
            urgency=0.5
        )
        
        request = OrderRequest(
            symbol='000001',
            side=OrderSide.BUY,
            order_type=OrderType.TWAP,
            quantity=2000,
            algo_config=algo_config,
            reason='测试TWAP算法'
        )
        
        # 提交算法订单
        success, message, order_id = await self.order_manager.submit_order(request)
        
        if not success:
            raise Exception(f"TWAP订单提交失败: {message}")
        
        self.logger.info(f"TWAP订单提交成功: {order_id}")
        
        # 等待算法执行完成
        await asyncio.sleep(15)
        
        # 检查执行结果
        order = self.order_manager.get_order(order_id)
        if not order:
            raise Exception(f"未找到TWAP订单: {order_id}")
        
        self.logger.info(f"TWAP订单执行完成: 状态={order.status.value}, 成交={order.filled_quantity}/{order.quantity}")
        
        # 验证算法参数
        assert 'algo_type' in order.algo_params
        assert order.algo_params['algo_type'] == 'TWAP'
    
    async def _test_vwap_algorithm(self):
        """测试VWAP算法"""
        # 创建VWAP算法配置
        start_time = datetime.now() + timedelta(seconds=2)
        end_time = start_time + timedelta(seconds=12)
        
        algo_config = AlgoOrderConfig(
            algo_type='VWAP',
            start_time=start_time,
            end_time=end_time,
            participation_rate=0.15,
            max_slice_size=400,
            min_slice_size=100,
            urgency=0.6,
            risk_aversion=0.4
        )
        
        request = OrderRequest(
            symbol='600000',
            side=OrderSide.BUY,
            order_type=OrderType.VWAP,
            quantity=1500,
            algo_config=algo_config,
            reason='测试VWAP算法'
        )
        
        # 提交算法订单
        success, message, order_id = await self.order_manager.submit_order(request)
        
        if not success:
            raise Exception(f"VWAP订单提交失败: {message}")
        
        self.logger.info(f"VWAP订单提交成功: {order_id}")
        
        # 等待算法执行完成
        await asyncio.sleep(18)
        
        # 检查执行结果
        order = self.order_manager.get_order(order_id)
        if not order:
            raise Exception(f"未找到VWAP订单: {order_id}")
        
        self.logger.info(f"VWAP订单执行完成: 状态={order.status.value}, 成交={order.filled_quantity}/{order.quantity}")
        
        # 验证算法参数
        assert 'algo_type' in order.algo_params
        assert order.algo_params['algo_type'] == 'VWAP'
    
    async def test_order_risk_validation(self):
        """测试订单风险检查和预处理逻辑"""
        self.logger.info("\n" + "=" * 40)
        self.logger.info("测试3: 订单风险检查和预处理")
        self.logger.info("=" * 40)
        
        # 测试各种风险检查场景
        risk_test_cases = [
            {
                'name': '资金不足检查',
                'request': OrderRequest(
                    symbol='000001',
                    side=OrderSide.BUY,
                    order_type=OrderType.MARKET,
                    quantity=20000,  # 超大数量，资金不足
                    reason='测试资金不足'
                ),
                'should_fail': True
            },
            {
                'name': '订单价值过大检查',
                'request': OrderRequest(
                    symbol='600036',
                    side=OrderSide.BUY,
                    order_type=OrderType.LIMIT,
                    quantity=1000,
                    price=50.0,  # 价值超过限制
                    reason='测试订单价值过大'
                ),
                'should_fail': True
            },
            {
                'name': '股票代码格式检查',
                'request': OrderRequest(
                    symbol='INVALID',  # 无效股票代码
                    side=OrderSide.BUY,
                    order_type=OrderType.MARKET,
                    quantity=100,
                    reason='测试无效股票代码'
                ),
                'should_fail': True
            },
            {
                'name': '数量不是100整数倍检查',
                'request': OrderRequest(
                    symbol='000001',
                    side=OrderSide.BUY,
                    order_type=OrderType.MARKET,
                    quantity=150,  # 不是100的整数倍
                    reason='测试数量格式'
                ),
                'should_fail': True
            },
            {
                'name': '限价单缺少价格检查',
                'request': OrderRequest(
                    symbol='000001',
                    side=OrderSide.BUY,
                    order_type=OrderType.LIMIT,
                    quantity=100,
                    # price=None,  # 限价单缺少价格
                    reason='测试限价单缺少价格'
                ),
                'should_fail': True
            },
            {
                'name': '正常订单通过检查',
                'request': OrderRequest(
                    symbol='000001',
                    side=OrderSide.BUY,
                    order_type=OrderType.LIMIT,
                    quantity=500,
                    price=10.50,
                    reason='测试正常订单'
                ),
                'should_fail': False
            }
        ]
        
        for test_case in risk_test_cases:
            await self._run_single_test(
                test_case['name'],
                self._test_risk_validation,
                test_case['request'],
                test_case['should_fail']
            )
    
    async def _test_risk_validation(self, request: OrderRequest, should_fail: bool):
        """测试风险验证"""
        success, message, order_id = await self.order_manager.submit_order(request)
        
        if should_fail:
            if success:
                raise Exception(f"预期失败但成功了: {message}")
            else:
                self.logger.info(f"风险检查正确拒绝: {message}")
        else:
            if not success:
                raise Exception(f"预期成功但失败了: {message}")
            else:
                self.logger.info(f"风险检查正确通过: {order_id}")
                
                # 等待处理完成
                await asyncio.sleep(1)
    
    async def test_order_monitoring_and_reporting(self):
        """测试订单执行监控和报告系统"""
        self.logger.info("\n" + "=" * 40)
        self.logger.info("测试4: 订单执行监控和报告")
        self.logger.info("=" * 40)
        
        # 提交一些测试订单
        test_orders = []
        
        for i in range(3):
            request = OrderRequest(
                symbol=f'00000{i+1}',
                side=OrderSide.BUY,
                order_type=OrderType.MARKET,
                quantity=500 * (i + 1),
                reason=f'监控测试订单{i+1}'
            )
            
            success, message, order_id = await self.order_manager.submit_order(request)
            if success:
                test_orders.append(order_id)
                self.logger.info(f"提交监控测试订单: {order_id}")
        
        # 等待订单处理
        await asyncio.sleep(3)
        
        # 测试监控功能
        await self._run_single_test(
            '订单监控功能',
            self._test_order_monitoring,
            test_orders
        )
        
        # 测试报告生成
        await self._run_single_test(
            '报告生成功能',
            self._test_report_generation
        )
    
    async def _test_order_monitoring(self, order_ids: List[str]):
        """测试订单监控功能"""
        monitor = self.order_manager.monitor
        
        # 检查订单是否被监控
        for order_id in order_ids:
            order = self.order_manager.get_order(order_id)
            if order:
                # 检查订单指标
                metrics = monitor.get_order_metrics(order_id)
                if metrics:
                    self.logger.info(f"订单 {order_id} 监控指标: 成交比例={metrics.fill_ratio:.2%}")
                else:
                    self.logger.warning(f"订单 {order_id} 缺少监控指标")
        
        # 检查告警功能
        alerts = monitor.get_alerts()
        self.logger.info(f"当前告警数量: {len(alerts)}")
        
        for alert in alerts[-5:]:  # 显示最近5个告警
            self.logger.info(f"告警: {alert.level.value} - {alert.message}")
    
    async def _test_report_generation(self):
        """测试报告生成功能"""
        reporter = self.order_manager.reporter
        
        # 生成日度报告
        today = datetime.now().strftime('%Y-%m-%d')
        report = reporter.generate_daily_report(today)
        
        self.logger.info(f"日度报告生成成功:")
        self.logger.info(f"  日期: {report.date}")
        self.logger.info(f"  总订单数: {report.total_orders}")
        self.logger.info(f"  完成订单数: {report.completed_orders}")
        self.logger.info(f"  总成交量: {report.total_volume}")
        self.logger.info(f"  总成交额: {report.total_value:.2f}")
        self.logger.info(f"  平均成交比例: {report.avg_fill_ratio:.2%}")
        
        # 测试报告导出
        json_file = reporter.export_report(report, 'json')
        csv_file = reporter.export_report(report, 'csv')
        
        self.logger.info(f"报告导出成功: JSON={json_file}, CSV={csv_file}")
        
        # 验证报告内容
        assert report.total_orders >= 0
        assert report.completed_orders >= 0
        assert report.total_volume >= 0
        assert report.total_value >= 0
    
    async def test_concurrent_orders(self):
        """测试高并发订单处理"""
        self.logger.info("\n" + "=" * 40)
        self.logger.info("测试5: 高并发订单处理")
        self.logger.info("=" * 40)
        
        await self._run_single_test(
            '并发订单提交',
            self._test_concurrent_order_submission
        )
    
    async def _test_concurrent_order_submission(self):
        """测试并发订单提交"""
        # 创建多个并发订单
        concurrent_requests = []
        
        for i in range(10):
            request = OrderRequest(
                symbol='000001',
                side=OrderSide.BUY if i % 2 == 0 else OrderSide.SELL,
                order_type=OrderType.MARKET,
                quantity=100 * (i + 1),
                reason=f'并发测试订单{i+1}'
            )
            concurrent_requests.append(request)
        
        # 并发提交订单
        start_time = time.time()
        
        tasks = []
        for request in concurrent_requests:
            task = asyncio.create_task(self.order_manager.submit_order(request))
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # 统计结果
        successful_orders = 0
        failed_orders = 0
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                self.logger.error(f"并发订单 {i+1} 异常: {result}")
                failed_orders += 1
            else:
                success, message, order_id = result
                if success:
                    successful_orders += 1
                    self.logger.info(f"并发订单 {i+1} 成功: {order_id}")
                else:
                    failed_orders += 1
                    self.logger.warning(f"并发订单 {i+1} 失败: {message}")
        
        self.logger.info(f"并发测试完成: 耗时={execution_time:.2f}s, 成功={successful_orders}, 失败={failed_orders}")
        
        # 等待订单处理完成
        await asyncio.sleep(3)
        
        # 验证系统状态
        stats = self.order_manager.get_execution_stats()
        self.logger.info(f"系统统计: {stats}")
    
    async def test_error_handling(self):
        """测试异常处理"""
        self.logger.info("\n" + "=" * 40)
        self.logger.info("测试6: 异常处理")
        self.logger.info("=" * 40)
        
        # 测试订单取消
        await self._run_single_test(
            '订单取消功能',
            self._test_order_cancellation
        )
        
        # 测试系统恢复
        await self._run_single_test(
            '系统恢复功能',
            self._test_system_recovery
        )
    
    async def _test_order_cancellation(self):
        """测试订单取消功能"""
        # 提交一个订单
        request = OrderRequest(
            symbol='000001',
            side=OrderSide.BUY,
            order_type=OrderType.LIMIT,
            quantity=1000,
            price=10.00,
            reason='测试取消功能'
        )
        
        success, message, order_id = await self.order_manager.submit_order(request)
        
        if not success:
            raise Exception(f"订单提交失败: {message}")
        
        self.logger.info(f"提交测试订单: {order_id}")
        
        # 等待一小段时间
        await asyncio.sleep(0.5)
        
        # 取消订单
        cancel_success = self.order_manager.cancel_order(order_id, "测试取消")
        
        if not cancel_success:
            raise Exception("订单取消失败")
        
        self.logger.info(f"订单取消成功: {order_id}")
        
        # 验证订单状态
        order = self.order_manager.get_order(order_id)
        if order and order.is_completed:
            self.logger.info(f"订单状态已更新: {order.status.value}")
        else:
            raise Exception("订单状态未正确更新")
    
    async def _test_system_recovery(self):
        """测试系统恢复功能"""
        # 获取当前统计
        initial_stats = self.order_manager.get_execution_stats()
        
        # 重置每日计数器
        self.order_manager.reset_daily_counters()
        self.logger.info("每日计数器已重置")
        
        # 提交新订单验证系统正常
        request = OrderRequest(
            symbol='000001',
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=100,
            reason='系统恢复测试'
        )
        
        success, message, order_id = await self.order_manager.submit_order(request)
        
        if not success:
            raise Exception(f"系统恢复后订单提交失败: {message}")
        
        self.logger.info(f"系统恢复后订单提交成功: {order_id}")
        
        # 等待处理
        await asyncio.sleep(1)
        
        # 验证统计更新
        final_stats = self.order_manager.get_execution_stats()
        self.logger.info(f"系统统计更新: {final_stats}")
    
    async def _run_single_test(self, test_name: str, test_func, *args):
        """运行单个测试"""
        self.test_results['total_tests'] += 1
        
        try:
            self.logger.info(f"\n开始测试: {test_name}")
            await test_func(*args)
            self.test_results['passed_tests'] += 1
            self.test_results['test_details'].append({
                'name': test_name,
                'status': 'PASSED',
                'message': '测试通过'
            })
            self.logger.info(f"✓ 测试通过: {test_name}")
            
        except Exception as e:
            self.test_results['failed_tests'] += 1
            self.test_results['test_details'].append({
                'name': test_name,
                'status': 'FAILED',
                'message': str(e)
            })
            self.logger.error(f"✗ 测试失败: {test_name} - {e}")
    
    def _print_test_summary(self):
        """打印测试总结"""
        self.logger.info("\n" + "=" * 60)
        self.logger.info("测试总结")
        self.logger.info("=" * 60)
        
        total = self.test_results['total_tests']
        passed = self.test_results['passed_tests']
        failed = self.test_results['failed_tests']
        
        self.logger.info(f"总测试数: {total}")
        self.logger.info(f"通过测试: {passed}")
        self.logger.info(f"失败测试: {failed}")
        self.logger.info(f"成功率: {(passed/total*100):.1f}%" if total > 0 else "0%")
        
        if failed > 0:
            self.logger.info("\n失败的测试:")
            for detail in self.test_results['test_details']:
                if detail['status'] == 'FAILED':
                    self.logger.info(f"  - {detail['name']}: {detail['message']}")
        
        self.logger.info("\n" + "=" * 60)
        
        # 输出最终统计
        if hasattr(self.order_manager, 'get_execution_stats'):
            final_stats = self.order_manager.get_execution_stats()
            self.logger.info("最终系统统计:")
            for key, value in final_stats.items():
                self.logger.info(f"  {key}: {value}")


async def main():
    """主测试函数"""
    test_system = OrderManagementSystemTest()
    await test_system.run_all_tests()


if __name__ == "__main__":
    # 运行测试
    asyncio.run(main())