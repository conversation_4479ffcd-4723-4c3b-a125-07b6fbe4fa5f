# T+0策略执行引擎

## 概述

T+0策略执行引擎是一个完整的基于底仓的T+0交易系统，专门为中国股市设计。系统实现了智能时机识别、成本优化和全面的风险控制，能够在保持底仓的基础上通过T+0操作降低持仓成本并获取额外收益。

## 核心功能

### 1. 时机分析器 (TTimingAnalyzer)

**功能特点：**
- 基于多种技术指标的综合分析（RSI、MACD、布林带等）
- 支撑阻力位识别
- 成交量异常检测
- 价格动量分析
- 相对底仓成本的位置分析

**主要方法：**
```python
# 分析T+0交易时机
signal = analyzer.analyze_t_timing(market_data, base_position)

# 信号包含：
# - 信号类型：BUY_T, SELL_T, HOLD_T
# - 信号强度：0-1
# - 置信度：0-1
# - 建议股数
# - 预期收益率
# - 止损止盈价格
```

### 2. 成本优化器 (TCostOptimizer)

**功能特点：**
- 基于底仓成本的优化策略
- 交易成本计算（佣金、印花税）
- 风险收益比评估
- 成本降低效果跟踪

**主要方法：**
```python
# 优化T+0策略
optimization_result = optimizer.optimize_t_strategy(base_position, current_price, market_data)

# 结果包含：
# - 推荐动作：BUY_T, SELL_T, HOLD
# - 最优股数
# - 预期成本降低
# - 风险收益比
# - 置信度
```

### 3. 风险控制器 (TRiskController)

**功能特点：**
- 多层次风险监控
- 实时止损机制
- 仓位比例控制
- 流动性风险检查
- 异常波动检测
- 紧急熔断机制

**风险指标：**
- 仓位风险：T仓占底仓比例
- 集中度风险：单一标的集中度
- 流动性风险：成交量充足性
- 波动率风险：价格波动程度
- 回撤风险：最大回撤控制
- 时间风险：持仓时间控制

**主要方法：**
```python
# 交易前风险检查
allow_trade, alerts = risk_controller.check_pre_trade_risk(signal, base_position, market_data)

# 持仓风险监控
alerts = risk_controller.monitor_position_risk(positions, market_data)
```

### 4. 策略执行器 (TStrategyExecutor)

**功能特点：**
- 多线程实时分析和监控
- 自动化交易决策
- 完整的生命周期管理
- 实时状态监控
- 性能统计和报告

**执行流程：**
1. 实时数据接收和缓存
2. 技术分析和信号生成
3. 成本优化建议
4. 风险检查和控制
5. 交易执行和持仓更新
6. 持续监控和风险管理

## 使用示例

### 基本使用

```python
from qlib_trading_system.trading import TStrategyExecutor, ExecutionConfig

# 创建执行配置
config = ExecutionConfig(
    symbol="000001.SZ",
    base_shares=2000,
    base_cost=12.50,
    max_t_ratio=0.25,           # 最大T仓比例25%
    min_profit_threshold=0.005,  # 最小盈利阈值0.5%
    stop_loss_pct=0.015,        # 止损比例1.5%
    max_holding_time=120,       # 最大持有时间120分钟
    max_daily_trades=20         # 日最大交易次数
)

# 创建策略执行器
executor = TStrategyExecutor(config)

# 设置回调函数
def on_signal_generated(signal, allow_trade, alerts):
    print(f"信号: {signal.signal_type.value}, 强度: {signal.strength:.3f}")

def on_trade_executed(trade_record):
    print(f"交易: {trade_record.trade_type} {trade_record.shares}股")

config.on_signal_generated = on_signal_generated
config.on_trade_executed = on_trade_executed

# 启动执行器
executor.start()

# 更新市场数据
executor.update_market_data(market_data)

# 获取状态
status = executor.get_status()
print(f"状态: {status['status']}, 交易: {status['statistics']['total_trades']}笔")

# 停止执行器
executor.stop()
```

### 独立组件使用

```python
from qlib_trading_system.trading import TTimingAnalyzer, TCostOptimizer, TRiskController

# 时机分析
analyzer = TTimingAnalyzer()
signal = analyzer.analyze_t_timing(market_data, base_position)

# 成本优化
optimizer = TCostOptimizer()
optimization = optimizer.optimize_t_strategy(base_position, current_price, market_data)

# 风险控制
risk_controller = TRiskController()
allow_trade, alerts = risk_controller.check_pre_trade_risk(signal, base_position, market_data)
```

## 配置参数

### 执行配置 (ExecutionConfig)

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| symbol | str | - | 股票代码 |
| base_shares | int | - | 底仓股数 |
| base_cost | float | - | 底仓成本 |
| max_t_ratio | float | 0.25 | 最大T仓比例 |
| min_profit_threshold | float | 0.005 | 最小盈利阈值 |
| analysis_interval | int | 30 | 分析间隔（秒） |
| stop_loss_pct | float | 0.015 | 止损比例 |
| max_holding_time | int | 120 | 最大持有时间（分钟） |
| max_daily_trades | int | 20 | 日最大交易次数 |

### 风险限制 (RiskLimit)

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| max_position_ratio | float | 0.3 | 最大仓位比例 |
| max_single_loss | float | 0.02 | 单笔最大亏损 |
| max_daily_loss | float | 0.05 | 日最大亏损 |
| max_drawdown | float | 0.15 | 最大回撤 |
| max_holding_time | int | 120 | 最大持有时间 |
| min_liquidity | float | 1000000 | 最小流动性 |
| stop_loss_pct | float | 0.015 | 止损比例 |

## 数据结构

### 交易信号 (TSignal)

```python
@dataclass
class TSignal:
    symbol: str                 # 股票代码
    signal_type: TSignalType    # 信号类型
    strength: float             # 信号强度 0-1
    confidence: float           # 置信度 0-1
    expected_return: float      # 预期收益率
    suggested_shares: int       # 建议交易股数
    entry_price: float          # 建议入场价格
    stop_loss: float           # 止损价格
    take_profit: float         # 止盈价格
    holding_time: int          # 建议持有时间（分钟）
    risk_level: float          # 风险等级
    reason: str                # 信号原因
```

### 仓位信息 (TPosition)

```python
@dataclass
class TPosition:
    symbol: str                 # 股票代码
    position_type: TPositionType # 仓位类型
    shares: int                 # 股数
    avg_cost: float            # 平均成本
    current_price: float       # 当前价格
    unrealized_pnl: float      # 未实现盈亏
    unrealized_pnl_pct: float  # 未实现盈亏率
    entry_time: datetime       # 入场时间
    holding_minutes: int       # 持有时间（分钟）
```

### 交易记录 (TTradeRecord)

```python
@dataclass
class TTradeRecord:
    symbol: str                 # 股票代码
    trade_type: str            # 交易类型
    shares: int                # 股数
    price: float               # 价格
    timestamp: datetime        # 时间戳
    cost: float                # 交易成本
    base_cost_before: float    # 交易前底仓成本
    base_cost_after: float     # 交易后底仓成本
    profit: float              # 实现利润
    success: bool              # 交易是否成功
```

## 风险警报类型

| 警报类型 | 说明 | 建议动作 |
|----------|------|----------|
| POSITION_RATIO | T仓位比例过高 | 减少仓位 |
| EXPECTED_LOSS | 预期损失过高 | 减少仓位 |
| LOW_LIQUIDITY | 流动性不足 | 减少仓位 |
| HIGH_VOLATILITY | 波动率过高 | 减少仓位 |
| STOP_LOSS | 触发止损 | 强制平仓 |
| TIME_STOP | 时间止损 | 强制平仓 |
| TRAILING_STOP | 移动止损 | 强制平仓 |
| DAILY_LOSS_LIMIT | 日度亏损超限 | 停止交易 |
| MAX_DRAWDOWN | 最大回撤超限 | 强制平仓 |
| EMERGENCY_MODE | 紧急模式 | 停止所有交易 |

## 性能指标

### 交易统计
- 总交易次数
- 成功交易次数
- 成功率
- 总盈利
- 平均每笔盈利

### 成本优化效果
- 总利润
- 成本降低百分比
- 新成本基础
- 交易次数
- 成功率

### 风险指标
- 实时PnL
- 最大回撤
- 日度盈亏
- 风险违规次数
- 紧急模式激活次数

## 测试和演示

### 运行单元测试
```bash
python test_t_strategy_standalone.py
```

### 运行完整演示
```bash
python demo_t_strategy.py
```

演示包含：
1. 时机分析器功能展示
2. 成本优化器功能展示
3. 风险控制器功能展示
4. 完整策略执行演示

## 注意事项

1. **交易时间限制**：系统会检查交易时间，非交易时间会拒绝交易
2. **流动性要求**：确保标的有足够的流动性支持T+0操作
3. **风险控制**：严格遵守风险限制，避免过度交易
4. **成本控制**：考虑交易成本对收益的影响
5. **数据质量**：确保市场数据的准确性和及时性

## 扩展功能

系统设计为高度可扩展，可以轻松添加：
- 新的技术指标
- 自定义风险规则
- 不同的成本优化策略
- 多标的组合管理
- 机器学习模型集成

## 日志和监控

系统提供详细的日志记录：
- 信号生成日志
- 交易执行日志
- 风险警报日志
- 性能统计日志

日志文件：`t_strategy_demo.log`

## 技术架构

- **多线程设计**：分析线程和监控线程独立运行
- **事件驱动**：基于回调机制的事件处理
- **模块化设计**：各组件独立，易于测试和维护
- **配置驱动**：通过配置文件灵活调整参数
- **异常处理**：完善的异常处理和恢复机制