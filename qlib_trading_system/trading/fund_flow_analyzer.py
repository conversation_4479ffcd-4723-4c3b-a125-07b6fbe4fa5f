#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资金流水和成本分析系统 - Fund Flow and Cost Analysis System

提供详细的资金流水分析、成本分析和投资绩效评估功能

Author: Qlib Trading System
Date: 2025-01-30
"""

import logging
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from enum import Enum
import pandas as pd
import numpy as np
from decimal import Decimal, ROUND_HALF_UP

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class FlowType(Enum):
    """资金流向类型枚举"""
    INFLOW = "inflow"       # 资金流入
    OUTFLOW = "outflow"     # 资金流出
    INTERNAL = "internal"   # 内部转换


class CostType(Enum):
    """成本类型枚举"""
    COMMISSION = "commission"   # 佣金
    TAX = "tax"                # 税费
    SLIPPAGE = "slippage"      # 滑点
    OPPORTUNITY = "opportunity" # 机会成本


@dataclass
class FlowRecord:
    """资金流水记录类"""
    record_id: str                  # 记录ID
    timestamp: datetime             # 时间戳
    symbol: str                     # 股票代码
    flow_type: FlowType            # 流向类型
    amount: float                  # 金额
    balance_before: float          # 操作前余额
    balance_after: float           # 操作后余额
    description: str               # 描述
    related_transaction_id: str = ""  # 关联交易ID
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        result = asdict(self)
        result['timestamp'] = self.timestamp.isoformat()
        result['flow_type'] = self.flow_type.value
        return result


@dataclass
class CostAnalysis:
    """成本分析结果类"""
    symbol: str                    # 股票代码
    total_cost: float             # 总成本
    commission_cost: float        # 佣金成本
    tax_cost: float              # 税费成本
    slippage_cost: float         # 滑点成本
    opportunity_cost: float      # 机会成本
    cost_ratio: float            # 成本率
    avg_cost_per_trade: float    # 平均每笔交易成本
    cost_efficiency_score: float # 成本效率评分
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return asdict(self)


class FundFlowAnalyzer:
    """资金流水分析器"""
    
    def __init__(self, data_dir: str = "data/fund_flow"):
        """
        初始化资金流水分析器
        
        Args:
            data_dir: 数据存储目录
        """
        self.data_dir = data_dir
        self.flow_records: List[FlowRecord] = []
        self.logger = logging.getLogger(__name__)
        
        # 确保数据目录存在
        os.makedirs(data_dir, exist_ok=True)
        
        # 加载历史记录
        self._load_flow_records()
    
    def _load_flow_records(self):
        """加载历史流水记录"""
        flow_file = os.path.join(self.data_dir, "flow_records.json")
        if os.path.exists(flow_file):
            try:
                with open(flow_file, 'r', encoding='utf-8') as f:
                    records_data = json.load(f)
                
                for record_data in records_data:
                    record_data['timestamp'] = datetime.fromisoformat(record_data['timestamp'])
                    record_data['flow_type'] = FlowType(record_data['flow_type'])
                    self.flow_records.append(FlowRecord(**record_data))
                
                self.logger.info(f"加载了{len(self.flow_records)}条流水记录")
            except Exception as e:
                self.logger.error(f"加载流水记录失败: {e}")
    
    def _save_flow_records(self):
        """保存流水记录"""
        flow_file = os.path.join(self.data_dir, "flow_records.json")
        try:
            records_data = [record.to_dict() for record in self.flow_records]
            with open(flow_file, 'w', encoding='utf-8') as f:
                json.dump(records_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存流水记录失败: {e}")
    
    def add_flow_record(self, symbol: str, flow_type: FlowType, amount: float,
                       balance_before: float, balance_after: float, 
                       description: str, related_transaction_id: str = "") -> str:
        """
        添加资金流水记录
        
        Args:
            symbol: 股票代码
            flow_type: 流向类型
            amount: 金额
            balance_before: 操作前余额
            balance_after: 操作后余额
            description: 描述
            related_transaction_id: 关联交易ID
            
        Returns:
            记录ID
        """
        record_id = f"FLOW_{datetime.now().strftime('%Y%m%d%H%M%S')}_{len(self.flow_records):04d}"
        
        record = FlowRecord(
            record_id=record_id,
            timestamp=datetime.now(),
            symbol=symbol,
            flow_type=flow_type,
            amount=amount,
            balance_before=balance_before,
            balance_after=balance_after,
            description=description,
            related_transaction_id=related_transaction_id
        )
        
        self.flow_records.append(record)
        self._save_flow_records()
        
        self.logger.info(f"添加流水记录: {symbol} {flow_type.value} {amount:.2f}")
        return record_id
    
    def generate_flow_report(self, start_date: datetime = None, 
                           end_date: datetime = None, symbol: str = None) -> Dict:
        """
        生成资金流水报告
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            symbol: 股票代码（可选）
            
        Returns:
            流水报告字典
        """
        if start_date is None:
            start_date = datetime.now() - timedelta(days=30)
        if end_date is None:
            end_date = datetime.now()
        
        # 筛选记录
        filtered_records = [
            record for record in self.flow_records
            if start_date <= record.timestamp <= end_date
            and (symbol is None or record.symbol == symbol)
        ]
        
        if not filtered_records:
            return {
                "error": "指定条件下无流水记录",
                "period": f"{start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}",
                "symbol": symbol or "全部"
            }
        
        # 按日期分组统计
        daily_flows = self._analyze_daily_flows(filtered_records)
        
        # 按股票分组统计
        stock_flows = self._analyze_stock_flows(filtered_records)
        
        # 计算汇总统计
        total_inflow = sum(r.amount for r in filtered_records if r.flow_type == FlowType.INFLOW)
        total_outflow = sum(r.amount for r in filtered_records if r.flow_type == FlowType.OUTFLOW)
        net_flow = total_inflow - total_outflow
        
        return {
            "report_time": datetime.now().isoformat(),
            "period": f"{start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}",
            "symbol_filter": symbol or "全部",
            "summary": {
                "total_records": len(filtered_records),
                "total_inflow": total_inflow,
                "total_outflow": total_outflow,
                "net_flow": net_flow,
                "avg_flow_per_day": net_flow / max((end_date - start_date).days, 1),
                "flow_frequency": len(filtered_records) / max((end_date - start_date).days, 1)
            },
            "daily_flows": daily_flows,
            "stock_flows": stock_flows,
            "flow_analysis": self._analyze_flow_patterns(filtered_records)
        }
    
    def _analyze_daily_flows(self, records: List[FlowRecord]) -> Dict:
        """分析日度资金流水"""
        daily_flows = {}
        
        for record in records:
            date_key = record.timestamp.strftime('%Y-%m-%d')
            if date_key not in daily_flows:
                daily_flows[date_key] = {
                    "inflow": 0,
                    "outflow": 0,
                    "net_flow": 0,
                    "record_count": 0
                }
            
            daily_flow = daily_flows[date_key]
            daily_flow["record_count"] += 1
            
            if record.flow_type == FlowType.INFLOW:
                daily_flow["inflow"] += record.amount
            elif record.flow_type == FlowType.OUTFLOW:
                daily_flow["outflow"] += record.amount
            
            daily_flow["net_flow"] = daily_flow["inflow"] - daily_flow["outflow"]
        
        return daily_flows
    
    def _analyze_stock_flows(self, records: List[FlowRecord]) -> Dict:
        """分析个股资金流水"""
        stock_flows = {}
        
        for record in records:
            if record.symbol not in stock_flows:
                stock_flows[record.symbol] = {
                    "inflow": 0,
                    "outflow": 0,
                    "net_flow": 0,
                    "record_count": 0,
                    "first_flow_time": record.timestamp.isoformat(),
                    "last_flow_time": record.timestamp.isoformat()
                }
            
            stock_flow = stock_flows[record.symbol]
            stock_flow["record_count"] += 1
            stock_flow["last_flow_time"] = record.timestamp.isoformat()
            
            if record.flow_type == FlowType.INFLOW:
                stock_flow["inflow"] += record.amount
            elif record.flow_type == FlowType.OUTFLOW:
                stock_flow["outflow"] += record.amount
            
            stock_flow["net_flow"] = stock_flow["inflow"] - stock_flow["outflow"]
        
        return stock_flows
    
    def _analyze_flow_patterns(self, records: List[FlowRecord]) -> Dict:
        """分析资金流动模式"""
        if not records:
            return {}
        
        # 时间分布分析
        hour_distribution = {}
        for record in records:
            hour = record.timestamp.hour
            hour_distribution[hour] = hour_distribution.get(hour, 0) + 1
        
        # 金额分布分析
        amounts = [record.amount for record in records]
        amount_stats = {
            "min_amount": min(amounts),
            "max_amount": max(amounts),
            "avg_amount": np.mean(amounts),
            "median_amount": np.median(amounts),
            "std_amount": np.std(amounts)
        }
        
        # 流向分析
        flow_type_stats = {}
        for flow_type in FlowType:
            count = len([r for r in records if r.flow_type == flow_type])
            flow_type_stats[flow_type.value] = count
        
        return {
            "hour_distribution": hour_distribution,
            "amount_statistics": amount_stats,
            "flow_type_statistics": flow_type_stats,
            "peak_flow_hour": max(hour_distribution.items(), key=lambda x: x[1])[0] if hour_distribution else None
        }
    
    def calculate_cost_analysis(self, symbol: str, start_date: datetime = None,
                              end_date: datetime = None) -> CostAnalysis:
        """
        计算成本分析
        
        Args:
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            成本分析结果
        """
        if start_date is None:
            start_date = datetime.now() - timedelta(days=30)
        if end_date is None:
            end_date = datetime.now()
        
        # 筛选相关记录
        symbol_records = [
            record for record in self.flow_records
            if record.symbol == symbol and start_date <= record.timestamp <= end_date
        ]
        
        if not symbol_records:
            return CostAnalysis(
                symbol=symbol,
                total_cost=0,
                commission_cost=0,
                tax_cost=0,
                slippage_cost=0,
                opportunity_cost=0,
                cost_ratio=0,
                avg_cost_per_trade=0,
                cost_efficiency_score=0
            )
        
        # 计算各类成本（这里需要根据实际交易记录来计算）
        total_amount = sum(abs(record.amount) for record in symbol_records)
        
        # 估算成本（实际应该从交易记录中获取）
        commission_cost = total_amount * 0.0003  # 假设佣金率0.03%
        tax_cost = sum(record.amount for record in symbol_records if record.flow_type == FlowType.OUTFLOW) * 0.001  # 印花税0.1%
        slippage_cost = total_amount * 0.0001  # 假设滑点成本0.01%
        opportunity_cost = 0  # 机会成本需要更复杂的计算
        
        total_cost = commission_cost + tax_cost + slippage_cost + opportunity_cost
        cost_ratio = total_cost / total_amount if total_amount > 0 else 0
        avg_cost_per_trade = total_cost / len(symbol_records) if symbol_records else 0
        
        # 计算成本效率评分（0-100分）
        cost_efficiency_score = max(0, 100 - cost_ratio * 10000)  # 成本率越低分数越高
        
        return CostAnalysis(
            symbol=symbol,
            total_cost=total_cost,
            commission_cost=commission_cost,
            tax_cost=tax_cost,
            slippage_cost=slippage_cost,
            opportunity_cost=opportunity_cost,
            cost_ratio=cost_ratio,
            avg_cost_per_trade=avg_cost_per_trade,
            cost_efficiency_score=cost_efficiency_score
        )
    
    def generate_cost_optimization_report(self, symbol: str = None) -> Dict:
        """
        生成成本优化报告
        
        Args:
            symbol: 股票代码（可选）
            
        Returns:
            成本优化报告
        """
        symbols = [symbol] if symbol else list(set(record.symbol for record in self.flow_records))
        
        cost_analyses = {}
        total_cost = 0
        total_amount = 0
        
        for sym in symbols:
            analysis = self.calculate_cost_analysis(sym)
            cost_analyses[sym] = analysis.to_dict()
            total_cost += analysis.total_cost
            
            # 计算该股票的总交易金额
            symbol_records = [r for r in self.flow_records if r.symbol == sym]
            total_amount += sum(abs(record.amount) for record in symbol_records)
        
        # 整体成本分析
        overall_cost_ratio = total_cost / total_amount if total_amount > 0 else 0
        
        # 生成优化建议
        optimization_suggestions = self._generate_cost_optimization_suggestions(
            overall_cost_ratio, cost_analyses
        )
        
        return {
            "report_time": datetime.now().isoformat(),
            "symbol_filter": symbol or "全部",
            "overall_metrics": {
                "total_cost": total_cost,
                "total_amount": total_amount,
                "overall_cost_ratio": overall_cost_ratio,
                "avg_efficiency_score": np.mean([analysis["cost_efficiency_score"] for analysis in cost_analyses.values()]) if cost_analyses else 0
            },
            "symbol_analyses": cost_analyses,
            "optimization_suggestions": optimization_suggestions,
            "cost_ranking": self._rank_symbols_by_cost_efficiency(cost_analyses)
        }
    
    def _generate_cost_optimization_suggestions(self, overall_cost_ratio: float, 
                                              cost_analyses: Dict) -> List[str]:
        """生成成本优化建议"""
        suggestions = []
        
        if overall_cost_ratio > 0.005:  # 成本率超过0.5%
            suggestions.append("🔴 整体交易成本偏高，建议优化交易策略")
        
        if overall_cost_ratio > 0.003:  # 成本率超过0.3%
            suggestions.append("📊 考虑增加单笔交易金额以摊薄固定成本")
        
        # 分析高成本股票
        high_cost_symbols = [
            symbol for symbol, analysis in cost_analyses.items()
            if analysis["cost_ratio"] > 0.004
        ]
        
        if high_cost_symbols:
            suggestions.append(f"⚠️ 以下股票成本较高，需要重点优化: {', '.join(high_cost_symbols[:3])}")
        
        # 通用建议
        suggestions.extend([
            "💡 优化交易时机，避免在流动性差的时段交易",
            "🎯 设置合理的价格区间，减少滑点成本",
            "📈 提高交易成功率，减少无效交易",
            "⚖️ 平衡交易频率与成本控制"
        ])
        
        return suggestions
    
    def _rank_symbols_by_cost_efficiency(self, cost_analyses: Dict) -> List[Dict]:
        """按成本效率对股票排序"""
        rankings = []
        
        for symbol, analysis in cost_analyses.items():
            rankings.append({
                "symbol": symbol,
                "cost_efficiency_score": analysis["cost_efficiency_score"],
                "cost_ratio": analysis["cost_ratio"],
                "total_cost": analysis["total_cost"]
            })
        
        # 按效率评分降序排列
        rankings.sort(key=lambda x: x["cost_efficiency_score"], reverse=True)
        
        return rankings
    
    def export_flow_data(self, file_path: str, start_date: datetime = None,
                        end_date: datetime = None, format_type: str = "csv") -> bool:
        """
        导出流水数据
        
        Args:
            file_path: 文件路径
            start_date: 开始日期
            end_date: 结束日期
            format_type: 格式类型 ("csv", "excel", "json")
            
        Returns:
            是否导出成功
        """
        try:
            if start_date is None:
                start_date = datetime.now() - timedelta(days=30)
            if end_date is None:
                end_date = datetime.now()
            
            # 筛选记录
            filtered_records = [
                record for record in self.flow_records
                if start_date <= record.timestamp <= end_date
            ]
            
            if format_type.lower() == "csv":
                df = pd.DataFrame([record.to_dict() for record in filtered_records])
                df.to_csv(file_path, index=False, encoding='utf-8-sig')
            
            elif format_type.lower() == "excel":
                df = pd.DataFrame([record.to_dict() for record in filtered_records])
                df.to_excel(file_path, index=False)
            
            elif format_type.lower() == "json":
                data = [record.to_dict() for record in filtered_records]
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
            
            else:
                raise ValueError(f"不支持的格式类型: {format_type}")
            
            self.logger.info(f"流水数据导出成功: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"导出流水数据失败: {e}")
            return False
    
    def get_flow_statistics(self) -> Dict:
        """获取流水统计信息"""
        if not self.flow_records:
            return {"error": "无流水记录"}
        
        # 时间范围
        timestamps = [record.timestamp for record in self.flow_records]
        earliest_time = min(timestamps)
        latest_time = max(timestamps)
        
        # 金额统计
        amounts = [record.amount for record in self.flow_records]
        
        # 股票统计
        symbols = list(set(record.symbol for record in self.flow_records))
        
        # 流向统计
        inflow_records = [r for r in self.flow_records if r.flow_type == FlowType.INFLOW]
        outflow_records = [r for r in self.flow_records if r.flow_type == FlowType.OUTFLOW]
        
        return {
            "total_records": len(self.flow_records),
            "time_range": {
                "earliest": earliest_time.isoformat(),
                "latest": latest_time.isoformat(),
                "span_days": (latest_time - earliest_time).days
            },
            "amount_statistics": {
                "total_amount": sum(abs(amount) for amount in amounts),
                "avg_amount": np.mean([abs(amount) for amount in amounts]),
                "max_amount": max(abs(amount) for amount in amounts),
                "min_amount": min(abs(amount) for amount in amounts)
            },
            "symbol_statistics": {
                "total_symbols": len(symbols),
                "symbols": symbols
            },
            "flow_statistics": {
                "inflow_count": len(inflow_records),
                "outflow_count": len(outflow_records),
                "total_inflow": sum(record.amount for record in inflow_records),
                "total_outflow": sum(record.amount for record in outflow_records),
                "net_flow": sum(record.amount for record in inflow_records) - sum(record.amount for record in outflow_records)
            }
        }


# 测试代码
if __name__ == "__main__":
    # 创建测试分析器
    analyzer = FundFlowAnalyzer("test_data/fund_flow")
    
    print("=== 资金流水分析器测试 ===")
    
    # 添加测试记录
    analyzer.add_flow_record("000001.SZ", FlowType.OUTFLOW, 100000, 500000, 400000, "买入股票")
    analyzer.add_flow_record("000001.SZ", FlowType.INFLOW, 110000, 400000, 510000, "卖出股票")
    analyzer.add_flow_record("000002.SZ", FlowType.OUTFLOW, 50000, 510000, 460000, "买入股票")
    
    print("测试记录添加完成")
    
    # 生成流水报告
    flow_report = analyzer.generate_flow_report()
    print(f"流水报告: {json.dumps(flow_report, ensure_ascii=False, indent=2)}")
    
    # 计算成本分析
    cost_analysis = analyzer.calculate_cost_analysis("000001.SZ")
    print(f"成本分析: {cost_analysis.to_dict()}")
    
    # 生成成本优化报告
    cost_report = analyzer.generate_cost_optimization_report()
    print(f"成本优化报告: {json.dumps(cost_report, ensure_ascii=False, indent=2)}")
    
    # 获取统计信息
    statistics = analyzer.get_flow_statistics()
    print(f"统计信息: {json.dumps(statistics, ensure_ascii=False, indent=2)}")
    
    print("=== 测试完成 ===")