#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小资金专用配置管理 - Small Capital Configuration Management

专门为小资金账户（<50万）设计的配置管理系统，支持全仓单股+做T策略

Author: Qlib Trading System
Date: 2025-01-30
"""

import logging
import json
import os
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum
import pandas as pd

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class RiskLevel(Enum):
    """风险等级枚举"""
    CONSERVATIVE = "conservative"    # 保守型
    MODERATE = "moderate"           # 稳健型
    AGGRESSIVE = "aggressive"       # 激进型
    EXTREME = "extreme"            # 极端型


@dataclass
class SmallCapitalConfig:
    """小资金专用配置类"""
    # 基础配置
    account_id: str                         # 账户ID
    total_capital: float                    # 总资金
    risk_level: RiskLevel                   # 风险等级
    
    # 仓位配置
    base_position_ratio: float = 0.75       # 底仓比例 (75%)
    t_position_ratio: float = 0.20          # 做T仓位比例 (20%)
    cash_reserve_ratio: float = 0.05        # 现金储备比例 (5%)
    
    # 风险控制配置
    max_single_stock_ratio: float = 1.0     # 单股最大仓位比例 (100% - 全仓单股)
    max_daily_loss_ratio: float = 0.03      # 单日最大亏损比例 (3%)
    max_drawdown_ratio: float = 0.15        # 最大回撤比例 (15%)
    stop_loss_ratio: float = 0.05           # 止损比例 (5%)
    
    # 做T策略配置
    t_profit_target: float = 0.02           # T+0目标收益率 (2%)
    t_stop_loss: float = 0.01               # T+0止损比例 (1%)
    max_t_times_per_day: int = 5            # 每日最大做T次数
    min_t_interval_minutes: int = 30        # 最小做T间隔(分钟)
    
    # 交易时间配置
    trading_start_time: str = "09:30"       # 交易开始时间
    trading_end_time: str = "15:00"         # 交易结束时间
    avoid_opening_minutes: int = 15         # 避开开盘前N分钟
    avoid_closing_minutes: int = 15         # 避开收盘前N分钟
    
    # 股票筛选配置
    min_market_cap: float = 50.0            # 最小市值(亿元)
    max_market_cap: float = 1000.0          # 最大市值(亿元)
    min_daily_volume: float = 5.0           # 最小日成交额(亿元)
    avoid_st_stocks: bool = True            # 避开ST股票
    avoid_new_stocks_days: int = 60         # 避开新股上市N天内
    
    # 成本控制配置
    commission_rate: float = 0.0003         # 佣金费率 (0.03%)
    min_commission: float = 5.0             # 最低佣金
    stamp_tax_rate: float = 0.001           # 印花税率 (0.1%)
    
    def __post_init__(self):
        """初始化后验证配置合理性"""
        self._validate_config()
    
    def _validate_config(self):
        """验证配置合理性"""
        # 验证资金分配比例
        total_ratio = self.base_position_ratio + self.t_position_ratio + self.cash_reserve_ratio
        if abs(total_ratio - 1.0) > 0.01:
            raise ValueError(f"资金配置比例总和应为1.0，当前为{total_ratio:.3f}")
        
        # 验证风险参数
        if self.max_daily_loss_ratio <= 0 or self.max_daily_loss_ratio > 0.2:
            raise ValueError(f"单日最大亏损比例应在0-20%之间，当前为{self.max_daily_loss_ratio*100:.1f}%")
        
        if self.max_drawdown_ratio <= 0 or self.max_drawdown_ratio > 0.5:
            raise ValueError(f"最大回撤比例应在0-50%之间，当前为{self.max_drawdown_ratio*100:.1f}%")
        
        # 验证做T参数
        if self.t_profit_target <= 0 or self.t_profit_target > 0.1:
            raise ValueError(f"T+0目标收益率应在0-10%之间，当前为{self.t_profit_target*100:.1f}%")
        
        if self.max_t_times_per_day <= 0 or self.max_t_times_per_day > 20:
            raise ValueError(f"每日最大做T次数应在1-20次之间，当前为{self.max_t_times_per_day}")
        
        logger.info(f"小资金配置验证通过 - 账户:{self.account_id}, 资金:{self.total_capital:,.2f}, 风险等级:{self.risk_level.value}")
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        result = asdict(self)
        result['risk_level'] = self.risk_level.value
        return result
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'SmallCapitalConfig':
        """从字典创建配置"""
        if 'risk_level' in data:
            data['risk_level'] = RiskLevel(data['risk_level'])
        return cls(**data)
    
    def save_to_file(self, file_path: str):
        """保存配置到文件"""
        config_data = self.to_dict()
        config_data['created_time'] = datetime.now().isoformat()
        
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"小资金配置已保存到: {file_path}")
    
    @classmethod
    def load_from_file(cls, file_path: str) -> 'SmallCapitalConfig':
        """从文件加载配置"""
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"配置文件不存在: {file_path}")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        # 移除时间戳字段
        config_data.pop('created_time', None)
        
        logger.info(f"小资金配置已从文件加载: {file_path}")
        return cls.from_dict(config_data)


class SmallCapitalConfigManager:
    """小资金配置管理器"""
    
    def __init__(self, config_dir: str = "config/small_capital"):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置文件目录
        """
        self.config_dir = config_dir
        self.configs: Dict[str, SmallCapitalConfig] = {}
        self.logger = logging.getLogger(__name__)
        
        # 确保配置目录存在
        os.makedirs(config_dir, exist_ok=True)
        
        # 加载现有配置
        self._load_existing_configs()
    
    def _load_existing_configs(self):
        """加载现有配置文件"""
        if not os.path.exists(self.config_dir):
            return
        
        for filename in os.listdir(self.config_dir):
            if filename.endswith('.json'):
                file_path = os.path.join(self.config_dir, filename)
                try:
                    config = SmallCapitalConfig.load_from_file(file_path)
                    self.configs[config.account_id] = config
                    self.logger.info(f"加载配置: {config.account_id}")
                except Exception as e:
                    self.logger.error(f"加载配置文件失败 {filename}: {e}")
    
    def create_config(self, account_id: str, total_capital: float, risk_level: RiskLevel) -> SmallCapitalConfig:
        """
        创建新的小资金配置
        
        Args:
            account_id: 账户ID
            total_capital: 总资金
            risk_level: 风险等级
            
        Returns:
            小资金配置对象
        """
        # 根据风险等级调整参数
        config_params = self._get_risk_adjusted_params(risk_level)
        
        config = SmallCapitalConfig(
            account_id=account_id,
            total_capital=total_capital,
            risk_level=risk_level,
            **config_params
        )
        
        # 保存配置
        self.configs[account_id] = config
        file_path = os.path.join(self.config_dir, f"{account_id}.json")
        config.save_to_file(file_path)
        
        self.logger.info(f"创建小资金配置: {account_id}, 资金: {total_capital:,.2f}, 风险等级: {risk_level.value}")
        return config
    
    def _get_risk_adjusted_params(self, risk_level: RiskLevel) -> Dict:
        """
        根据风险等级获取调整后的参数
        
        Args:
            risk_level: 风险等级
            
        Returns:
            调整后的参数字典
        """
        if risk_level == RiskLevel.CONSERVATIVE:
            return {
                "base_position_ratio": 0.60,
                "t_position_ratio": 0.15,
                "cash_reserve_ratio": 0.25,
                "max_daily_loss_ratio": 0.02,
                "max_drawdown_ratio": 0.10,
                "stop_loss_ratio": 0.03,
                "t_profit_target": 0.015,
                "max_t_times_per_day": 3
            }
        elif risk_level == RiskLevel.MODERATE:
            return {
                "base_position_ratio": 0.70,
                "t_position_ratio": 0.20,
                "cash_reserve_ratio": 0.10,
                "max_daily_loss_ratio": 0.025,
                "max_drawdown_ratio": 0.12,
                "stop_loss_ratio": 0.04,
                "t_profit_target": 0.02,
                "max_t_times_per_day": 4
            }
        elif risk_level == RiskLevel.AGGRESSIVE:
            return {
                "base_position_ratio": 0.75,
                "t_position_ratio": 0.20,
                "cash_reserve_ratio": 0.05,
                "max_daily_loss_ratio": 0.03,
                "max_drawdown_ratio": 0.15,
                "stop_loss_ratio": 0.05,
                "t_profit_target": 0.025,
                "max_t_times_per_day": 5
            }
        else:  # EXTREME
            return {
                "base_position_ratio": 0.80,
                "t_position_ratio": 0.18,
                "cash_reserve_ratio": 0.02,
                "max_daily_loss_ratio": 0.05,
                "max_drawdown_ratio": 0.20,
                "stop_loss_ratio": 0.08,
                "t_profit_target": 0.03,
                "max_t_times_per_day": 8
            }
    
    def get_config(self, account_id: str) -> Optional[SmallCapitalConfig]:
        """
        获取配置
        
        Args:
            account_id: 账户ID
            
        Returns:
            配置对象或None
        """
        return self.configs.get(account_id)
    
    def update_config(self, account_id: str, **kwargs) -> bool:
        """
        更新配置
        
        Args:
            account_id: 账户ID
            **kwargs: 要更新的参数
            
        Returns:
            是否更新成功
        """
        if account_id not in self.configs:
            self.logger.error(f"配置不存在: {account_id}")
            return False
        
        config = self.configs[account_id]
        
        # 更新参数
        for key, value in kwargs.items():
            if hasattr(config, key):
                setattr(config, key, value)
            else:
                self.logger.warning(f"未知配置参数: {key}")
        
        # 重新验证配置
        try:
            config._validate_config()
        except ValueError as e:
            self.logger.error(f"配置验证失败: {e}")
            return False
        
        # 保存更新后的配置
        file_path = os.path.join(self.config_dir, f"{account_id}.json")
        config.save_to_file(file_path)
        
        self.logger.info(f"配置更新成功: {account_id}")
        return True
    
    def delete_config(self, account_id: str) -> bool:
        """
        删除配置
        
        Args:
            account_id: 账户ID
            
        Returns:
            是否删除成功
        """
        if account_id not in self.configs:
            self.logger.error(f"配置不存在: {account_id}")
            return False
        
        # 删除内存中的配置
        del self.configs[account_id]
        
        # 删除配置文件
        file_path = os.path.join(self.config_dir, f"{account_id}.json")
        if os.path.exists(file_path):
            os.remove(file_path)
        
        self.logger.info(f"配置删除成功: {account_id}")
        return True
    
    def list_configs(self) -> List[Dict]:
        """
        列出所有配置
        
        Returns:
            配置列表
        """
        return [
            {
                "account_id": config.account_id,
                "total_capital": config.total_capital,
                "risk_level": config.risk_level.value,
                "base_position_ratio": config.base_position_ratio,
                "t_position_ratio": config.t_position_ratio
            }
            for config in self.configs.values()
        ]
    
    def get_optimal_config_for_capital(self, total_capital: float) -> SmallCapitalConfig:
        """
        根据资金量获取最优配置
        
        Args:
            total_capital: 总资金
            
        Returns:
            最优配置
        """
        # 根据资金量推荐风险等级
        if total_capital < 50000:  # 5万以下
            risk_level = RiskLevel.CONSERVATIVE
        elif total_capital < 100000:  # 5-10万
            risk_level = RiskLevel.MODERATE
        elif total_capital < 300000:  # 10-30万
            risk_level = RiskLevel.AGGRESSIVE
        else:  # 30万以上
            risk_level = RiskLevel.EXTREME
        
        # 生成临时账户ID
        temp_account_id = f"temp_{datetime.now().strftime('%Y%m%d%H%M%S')}"
        
        return SmallCapitalConfig(
            account_id=temp_account_id,
            total_capital=total_capital,
            risk_level=risk_level,
            **self._get_risk_adjusted_params(risk_level)
        )


# 预定义配置模板
SMALL_CAPITAL_TEMPLATES = {
    "ultra_conservative": {
        "name": "超保守型",
        "description": "适合新手或极度厌恶风险的投资者",
        "risk_level": RiskLevel.CONSERVATIVE,
        "base_position_ratio": 0.50,
        "t_position_ratio": 0.10,
        "cash_reserve_ratio": 0.40,
        "max_daily_loss_ratio": 0.015,
        "max_t_times_per_day": 2
    },
    "balanced": {
        "name": "均衡型",
        "description": "平衡收益与风险，适合大多数投资者",
        "risk_level": RiskLevel.MODERATE,
        "base_position_ratio": 0.70,
        "t_position_ratio": 0.20,
        "cash_reserve_ratio": 0.10,
        "max_daily_loss_ratio": 0.025,
        "max_t_times_per_day": 4
    },
    "growth_focused": {
        "name": "成长型",
        "description": "追求较高收益，能承受一定风险",
        "risk_level": RiskLevel.AGGRESSIVE,
        "base_position_ratio": 0.75,
        "t_position_ratio": 0.20,
        "cash_reserve_ratio": 0.05,
        "max_daily_loss_ratio": 0.03,
        "max_t_times_per_day": 5
    },
    "high_frequency": {
        "name": "高频交易型",
        "description": "专业投资者，追求极致收益",
        "risk_level": RiskLevel.EXTREME,
        "base_position_ratio": 0.80,
        "t_position_ratio": 0.18,
        "cash_reserve_ratio": 0.02,
        "max_daily_loss_ratio": 0.05,
        "max_t_times_per_day": 8
    }
}