"""
T+0策略执行引擎
实现基于底仓的T+0交易逻辑，包括最优时机识别、成本优化和风险控制
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, field
from enum import Enum
import logging
from datetime import datetime, timedelta
from collections import deque
import json

logger = logging.getLogger(__name__)


class TSignalType(Enum):
    """T+0信号类型"""
    BUY_T = "BUY_T"      # 买入T仓
    SELL_T = "SELL_T"    # 卖出T仓
    HOLD_T = "HOLD_T"    # 持有T仓
    CLOSE_T = "CLOSE_T"  # 平仓T仓


class TPositionType(Enum):
    """T仓位类型"""
    BASE = "BASE"        # 底仓
    T_LONG = "T_LONG"    # T仓多头
    T_SHORT = "T_SHORT"  # T仓空头（通过卖出底仓实现）


@dataclass
class TPosition:
    """T+0仓位信息"""
    symbol: str
    position_type: TPositionType
    shares: int
    avg_cost: float
    current_price: float
    unrealized_pnl: float
    unrealized_pnl_pct: float
    entry_time: datetime
    holding_minutes: int
    
    # T仓特有属性
    base_shares: int = 0  # 对应的底仓数量
    t_cost_basis: float = 0.0  # T仓成本基础
    expected_profit: float = 0.0  # 预期利润
    risk_level: float = 0.0  # 风险等级
    
    def update_price(self, new_price: float):
        """更新价格和盈亏"""
        self.current_price = new_price
        if self.position_type == TPositionType.T_LONG:
            self.unrealized_pnl = (new_price - self.avg_cost) * self.shares
        elif self.position_type == TPositionType.T_SHORT:
            self.unrealized_pnl = (self.avg_cost - new_price) * self.shares
        else:  # BASE
            self.unrealized_pnl = (new_price - self.avg_cost) * self.shares
        
        if self.avg_cost > 0:
            self.unrealized_pnl_pct = self.unrealized_pnl / (self.avg_cost * abs(self.shares))
        
        # 更新持有时间
        self.holding_minutes = int((datetime.now() - self.entry_time).total_seconds() / 60)


@dataclass
class TSignal:
    """T+0交易信号"""
    symbol: str
    signal_type: TSignalType
    strength: float  # 信号强度 0-1
    confidence: float  # 置信度 0-1
    expected_return: float  # 预期收益率
    suggested_shares: int  # 建议交易股数
    entry_price: float  # 建议入场价格
    stop_loss: Optional[float] = None  # 止损价格
    take_profit: Optional[float] = None  # 止盈价格
    holding_time: int = 30  # 建议持有时间（分钟）
    risk_level: float = 0.5  # 风险等级
    reason: str = ""  # 信号原因
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class TTradeRecord:
    """T+0交易记录"""
    symbol: str
    trade_type: str  # 'BUY_T', 'SELL_T', 'CLOSE_T'
    shares: int
    price: float
    timestamp: datetime
    cost: float  # 交易成本
    base_cost_before: float  # 交易前底仓成本
    base_cost_after: float  # 交易后底仓成本
    profit: float = 0.0  # 实现利润
    success: bool = True  # 交易是否成功


class TTimingAnalyzer:
    """T+0时机分析器"""
    
    def __init__(self, config: Dict = None):
        """
        初始化时机分析器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        
        # 技术指标参数
        self.rsi_period = self.config.get('rsi_period', 14)
        self.macd_fast = self.config.get('macd_fast', 12)
        self.macd_slow = self.config.get('macd_slow', 26)
        self.macd_signal = self.config.get('macd_signal', 9)
        self.bb_period = self.config.get('bb_period', 20)
        self.bb_std = self.config.get('bb_std', 2)
        
        # 做T参数
        self.min_profit_threshold = self.config.get('min_profit_threshold', 0.005)  # 最小盈利阈值0.5%
        self.max_holding_time = self.config.get('max_holding_time', 120)  # 最大持有时间120分钟
        self.volume_threshold = self.config.get('volume_threshold', 1.5)  # 成交量阈值
        
        # 历史数据缓存
        self.price_history = deque(maxlen=100)
        self.volume_history = deque(maxlen=100)
        self.indicator_cache = {}
    
    def analyze_t_timing(self, data: pd.DataFrame, base_position: TPosition) -> TSignal:
        """
        分析T+0交易时机
        
        Args:
            data: 实时行情数据
            base_position: 底仓信息
            
        Returns:
            T+0交易信号
        """
        if len(data) < 30:
            return self._create_hold_signal(base_position.symbol, "数据不足")
        
        current_price = data['close'].iloc[-1]
        
        # 更新历史数据
        self._update_history(data)
        
        # 计算技术指标
        indicators = self._calculate_indicators(data)
        
        # 分析买入T仓时机
        buy_t_signal = self._analyze_buy_t_timing(data, indicators, base_position, current_price)
        
        # 分析卖出T仓时机
        sell_t_signal = self._analyze_sell_t_timing(data, indicators, base_position, current_price)
        
        # 选择最优信号
        if buy_t_signal.strength > sell_t_signal.strength:
            return buy_t_signal
        elif sell_t_signal.strength > 0.6:
            return sell_t_signal
        else:
            return self._create_hold_signal(base_position.symbol, "无明确信号")
    
    def _update_history(self, data: pd.DataFrame):
        """更新历史数据"""
        if len(data) > 0:
            self.price_history.append(data['close'].iloc[-1])
            self.volume_history.append(data['volume'].iloc[-1])
    
    def _calculate_indicators(self, data: pd.DataFrame) -> Dict[str, Any]:
        """计算技术指标"""
        indicators = {}
        
        try:
            # RSI指标
            indicators['rsi'] = self._calculate_rsi(data['close'], self.rsi_period)
            
            # MACD指标
            macd_line, signal_line, histogram = self._calculate_macd(
                data['close'], self.macd_fast, self.macd_slow, self.macd_signal
            )
            indicators['macd'] = {
                'macd': macd_line,
                'signal': signal_line,
                'histogram': histogram
            }
            
            # 布林带
            bb_upper, bb_middle, bb_lower = self._calculate_bollinger_bands(
                data['close'], self.bb_period, self.bb_std
            )
            indicators['bollinger'] = {
                'upper': bb_upper,
                'middle': bb_middle,
                'lower': bb_lower
            }
            
            # 成交量指标
            indicators['volume_ratio'] = data['volume'].iloc[-1] / data['volume'].rolling(20).mean().iloc[-1]
            
            # 价格动量
            indicators['momentum_5'] = data['close'].iloc[-1] / data['close'].iloc[-6] - 1  # 5分钟动量
            indicators['momentum_15'] = data['close'].iloc[-1] / data['close'].iloc[-16] - 1  # 15分钟动量
            
            # 支撑阻力位
            indicators['support'], indicators['resistance'] = self._calculate_support_resistance(data)
            
        except Exception as e:
            logger.warning(f"计算技术指标失败: {e}")
            # 返回默认值
            indicators = {
                'rsi': 50,
                'macd': {'macd': 0, 'signal': 0, 'histogram': 0},
                'bollinger': {'upper': 0, 'middle': 0, 'lower': 0},
                'volume_ratio': 1.0,
                'momentum_5': 0,
                'momentum_15': 0,
                'support': data['low'].min(),
                'resistance': data['high'].max()
            }
        
        return indicators
    
    def _analyze_buy_t_timing(self, data: pd.DataFrame, indicators: Dict, 
                             base_position: TPosition, current_price: float) -> TSignal:
        """分析买入T仓时机"""
        signal_strength = 0.0
        signal_reasons = []
        
        # RSI超卖信号
        rsi = indicators.get('rsi', 50)
        if rsi < 30:
            signal_strength += 0.3
            signal_reasons.append(f"RSI超卖({rsi:.1f})")
        elif rsi < 40:
            signal_strength += 0.15
            signal_reasons.append(f"RSI偏低({rsi:.1f})")
        
        # MACD金叉信号
        macd_data = indicators.get('macd', {})
        if (macd_data.get('macd', 0) > macd_data.get('signal', 0) and 
            len(data) > 1 and 
            self._is_macd_golden_cross(data)):
            signal_strength += 0.25
            signal_reasons.append("MACD金叉")
        
        # 布林带下轨支撑
        bollinger = indicators.get('bollinger', {})
        bb_lower = bollinger.get('lower', current_price)
        if current_price <= bb_lower * 1.002:  # 接近下轨
            signal_strength += 0.2
            signal_reasons.append("接近布林带下轨")
        
        # 支撑位反弹
        support = indicators.get('support', 0)
        if support > 0 and current_price <= support * 1.005:
            signal_strength += 0.15
            signal_reasons.append("接近支撑位")
        
        # 成交量放大
        volume_ratio = indicators.get('volume_ratio', 1.0)
        if volume_ratio > self.volume_threshold:
            signal_strength += 0.1
            signal_reasons.append(f"成交量放大({volume_ratio:.1f}倍)")
        
        # 短期超跌
        momentum_5 = indicators.get('momentum_5', 0)
        if momentum_5 < -0.02:  # 5分钟跌幅超过2%
            signal_strength += 0.15
            signal_reasons.append(f"短期超跌({momentum_5:.2%})")
        
        # 相对底仓成本的位置
        if current_price < base_position.avg_cost * 0.98:  # 低于底仓成本2%
            signal_strength += 0.1
            signal_reasons.append("低于底仓成本")
        
        # 计算建议交易数量
        suggested_shares = self._calculate_t_shares(base_position, signal_strength, 'BUY')
        
        # 计算预期收益
        expected_return = self._estimate_expected_return(data, indicators, 'BUY')
        
        # 计算止损止盈
        stop_loss = current_price * 0.985  # 1.5%止损
        take_profit = current_price * (1 + expected_return)
        
        return TSignal(
            symbol=base_position.symbol,
            signal_type=TSignalType.BUY_T,
            strength=min(signal_strength, 1.0),
            confidence=min(signal_strength * 1.2, 1.0),
            expected_return=expected_return,
            suggested_shares=suggested_shares,
            entry_price=current_price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            holding_time=self._estimate_holding_time(signal_strength),
            risk_level=self._calculate_risk_level(indicators, 'BUY'),
            reason="; ".join(signal_reasons) if signal_reasons else "技术指标综合分析",
            timestamp=datetime.now()
        )
    
    def _analyze_sell_t_timing(self, data: pd.DataFrame, indicators: Dict,
                              base_position: TPosition, current_price: float) -> TSignal:
        """分析卖出T仓时机（通过卖出部分底仓实现）"""
        signal_strength = 0.0
        signal_reasons = []
        
        # RSI超买信号
        rsi = indicators.get('rsi', 50)
        if rsi > 70:
            signal_strength += 0.3
            signal_reasons.append(f"RSI超买({rsi:.1f})")
        elif rsi > 60:
            signal_strength += 0.15
            signal_reasons.append(f"RSI偏高({rsi:.1f})")
        
        # MACD死叉信号
        macd_data = indicators.get('macd', {})
        if (macd_data.get('macd', 0) < macd_data.get('signal', 0) and 
            len(data) > 1 and 
            self._is_macd_death_cross(data)):
            signal_strength += 0.25
            signal_reasons.append("MACD死叉")
        
        # 布林带上轨阻力
        bollinger = indicators.get('bollinger', {})
        bb_upper = bollinger.get('upper', current_price)
        if current_price >= bb_upper * 0.998:  # 接近上轨
            signal_strength += 0.2
            signal_reasons.append("接近布林带上轨")
        
        # 阻力位承压
        resistance = indicators.get('resistance', float('inf'))
        if resistance < float('inf') and current_price >= resistance * 0.995:
            signal_strength += 0.15
            signal_reasons.append("接近阻力位")
        
        # 短期超涨
        momentum_5 = indicators.get('momentum_5', 0)
        if momentum_5 > 0.02:  # 5分钟涨幅超过2%
            signal_strength += 0.15
            signal_reasons.append(f"短期超涨({momentum_5:.2%})")
        
        # 相对底仓成本的位置
        if current_price > base_position.avg_cost * 1.02:  # 高于底仓成本2%
            signal_strength += 0.1
            signal_reasons.append("高于底仓成本")
        
        # 计算建议交易数量
        suggested_shares = self._calculate_t_shares(base_position, signal_strength, 'SELL')
        
        # 计算预期收益
        expected_return = self._estimate_expected_return(data, indicators, 'SELL')
        
        # 计算止损止盈
        stop_loss = current_price * 1.015  # 1.5%止损
        take_profit = current_price * (1 - expected_return)
        
        return TSignal(
            symbol=base_position.symbol,
            signal_type=TSignalType.SELL_T,
            strength=min(signal_strength, 1.0),
            confidence=min(signal_strength * 1.2, 1.0),
            expected_return=expected_return,
            suggested_shares=suggested_shares,
            entry_price=current_price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            holding_time=self._estimate_holding_time(signal_strength),
            risk_level=self._calculate_risk_level(indicators, 'SELL'),
            reason="; ".join(signal_reasons) if signal_reasons else "技术指标综合分析",
            timestamp=datetime.now()
        )
    
    def _calculate_t_shares(self, base_position: TPosition, signal_strength: float, direction: str) -> int:
        """计算T仓交易数量"""
        # 基础交易比例（基于信号强度）
        base_ratio = min(signal_strength * 0.3, 0.25)  # 最大25%
        
        # 根据底仓数量计算
        max_t_shares = int(base_position.shares * base_ratio)
        
        # 确保最小交易单位
        min_shares = 100  # 最小1手
        
        # 根据方向调整
        if direction == 'BUY':
            # 买入T仓，考虑资金限制
            suggested_shares = max(min_shares, max_t_shares)
        else:  # SELL
            # 卖出T仓，不能超过底仓数量
            suggested_shares = min(max_t_shares, base_position.shares - min_shares)
            suggested_shares = max(0, suggested_shares)
        
        # 确保是100的整数倍
        suggested_shares = (suggested_shares // 100) * 100
        
        return suggested_shares
    
    def _estimate_expected_return(self, data: pd.DataFrame, indicators: Dict, direction: str) -> float:
        """估算预期收益率"""
        base_return = 0.01  # 基础预期收益1%
        
        # 基于波动率调整
        volatility = data['close'].pct_change().tail(20).std()
        volatility_factor = min(volatility * 50, 2.0)  # 波动率因子
        
        # 基于RSI调整
        rsi = indicators.get('rsi', 50)
        if direction == 'BUY':
            rsi_factor = max(0.5, (50 - rsi) / 20)  # RSI越低，预期收益越高
        else:
            rsi_factor = max(0.5, (rsi - 50) / 20)  # RSI越高，预期收益越高
        
        # 基于成交量调整
        volume_ratio = indicators.get('volume_ratio', 1.0)
        volume_factor = min(volume_ratio / 2, 1.5)
        
        expected_return = base_return * volatility_factor * rsi_factor * volume_factor
        
        return min(expected_return, 0.05)  # 最大预期收益5%
    
    def _estimate_holding_time(self, signal_strength: float) -> int:
        """估算建议持有时间"""
        base_time = 30  # 基础持有时间30分钟
        
        # 信号强度越高，建议持有时间越长
        strength_factor = 1 + signal_strength
        
        holding_time = int(base_time * strength_factor)
        
        return min(holding_time, self.max_holding_time)
    
    def _calculate_risk_level(self, indicators: Dict, direction: str) -> float:
        """计算风险等级"""
        base_risk = 0.5
        
        # 基于波动率的风险
        volume_ratio = indicators.get('volume_ratio', 1.0)
        volume_risk = min(volume_ratio / 3, 0.3)
        
        # 基于RSI的风险
        rsi = indicators.get('rsi', 50)
        if direction == 'BUY':
            rsi_risk = max(0, (rsi - 30) / 40)  # RSI越高风险越大
        else:
            rsi_risk = max(0, (70 - rsi) / 40)  # RSI越低风险越大
        
        total_risk = base_risk + volume_risk + rsi_risk * 0.2
        
        return min(total_risk, 1.0)
    
    def _create_hold_signal(self, symbol: str, reason: str) -> TSignal:
        """创建持有信号"""
        return TSignal(
            symbol=symbol,
            signal_type=TSignalType.HOLD_T,
            strength=0.0,
            confidence=0.0,
            expected_return=0.0,
            suggested_shares=0,
            entry_price=0.0,
            reason=reason,
            timestamp=datetime.now()
        )
    
    def _calculate_rsi(self, prices: pd.Series, period: int) -> float:
        """计算RSI指标"""
        if len(prices) < period + 1:
            return 50.0
        
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi.iloc[-1] if not pd.isna(rsi.iloc[-1]) else 50.0
    
    def _calculate_macd(self, prices: pd.Series, fast: int, slow: int, signal: int) -> Tuple[float, float, float]:
        """计算MACD指标"""
        if len(prices) < slow + signal:
            return 0.0, 0.0, 0.0
        
        ema_fast = prices.ewm(span=fast).mean()
        ema_slow = prices.ewm(span=slow).mean()
        macd_line = ema_fast - ema_slow
        signal_line = macd_line.ewm(span=signal).mean()
        histogram = macd_line - signal_line
        
        return (
            macd_line.iloc[-1] if not pd.isna(macd_line.iloc[-1]) else 0.0,
            signal_line.iloc[-1] if not pd.isna(signal_line.iloc[-1]) else 0.0,
            histogram.iloc[-1] if not pd.isna(histogram.iloc[-1]) else 0.0
        )
    
    def _calculate_bollinger_bands(self, prices: pd.Series, period: int, std_dev: float) -> Tuple[float, float, float]:
        """计算布林带"""
        if len(prices) < period:
            current_price = prices.iloc[-1]
            return current_price, current_price, current_price
        
        sma = prices.rolling(window=period).mean()
        std = prices.rolling(window=period).std()
        
        upper = sma + (std * std_dev)
        lower = sma - (std * std_dev)
        
        return (
            upper.iloc[-1] if not pd.isna(upper.iloc[-1]) else prices.iloc[-1],
            sma.iloc[-1] if not pd.isna(sma.iloc[-1]) else prices.iloc[-1],
            lower.iloc[-1] if not pd.isna(lower.iloc[-1]) else prices.iloc[-1]
        )
    
    def _calculate_support_resistance(self, data: pd.DataFrame) -> Tuple[float, float]:
        """计算支撑阻力位"""
        if len(data) < 20:
            return data['low'].min(), data['high'].max()
        
        # 使用最近20个周期的高低点
        recent_data = data.tail(20)
        
        # 支撑位：最近低点的平均值
        support = recent_data['low'].rolling(5).min().mean()
        
        # 阻力位：最近高点的平均值
        resistance = recent_data['high'].rolling(5).max().mean()
        
        return support, resistance
    
    def _is_macd_golden_cross(self, data: pd.DataFrame) -> bool:
        """判断MACD是否金叉"""
        if len(data) < 2:
            return False
        
        try:
            # 计算最近两个周期的MACD
            prices = data['close']
            ema_fast = prices.ewm(span=self.macd_fast).mean()
            ema_slow = prices.ewm(span=self.macd_slow).mean()
            macd_line = ema_fast - ema_slow
            signal_line = macd_line.ewm(span=self.macd_signal).mean()
            
            # 当前周期MACD > Signal，前一周期MACD <= Signal
            current_macd = macd_line.iloc[-1]
            current_signal = signal_line.iloc[-1]
            prev_macd = macd_line.iloc[-2]
            prev_signal = signal_line.iloc[-2]
            
            return current_macd > current_signal and prev_macd <= prev_signal
        except:
            return False
    
    def _is_macd_death_cross(self, data: pd.DataFrame) -> bool:
        """判断MACD是否死叉"""
        if len(data) < 2:
            return False
        
        try:
            # 计算最近两个周期的MACD
            prices = data['close']
            ema_fast = prices.ewm(span=self.macd_fast).mean()
            ema_slow = prices.ewm(span=self.macd_slow).mean()
            macd_line = ema_fast - ema_slow
            signal_line = macd_line.ewm(span=self.macd_signal).mean()
            
            # 当前周期MACD < Signal，前一周期MACD >= Signal
            current_macd = macd_line.iloc[-1]
            current_signal = signal_line.iloc[-1]
            prev_macd = macd_line.iloc[-2]
            prev_signal = signal_line.iloc[-2]
            
            return current_macd < current_signal and prev_macd >= prev_signal
        except:
            return False


class TCostOptimizer:
    """T+0成本优化器"""
    
    def __init__(self, config: Dict = None):
        """
        初始化成本优化器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        
        # 交易成本参数
        self.commission_rate = self.config.get('commission_rate', 0.0003)  # 佣金费率0.03%
        self.stamp_tax_rate = self.config.get('stamp_tax_rate', 0.001)  # 印花税0.1%（仅卖出）
        self.min_commission = self.config.get('min_commission', 5.0)  # 最小佣金5元
        
        # 成本优化参数
        self.target_cost_reduction = self.config.get('target_cost_reduction', 0.01)  # 目标成本降低1%
        self.max_t_ratio = self.config.get('max_t_ratio', 0.3)  # 最大T仓比例30%
        
        # 历史交易记录
        self.trade_history = deque(maxlen=1000)
        self.daily_stats = {}
    
    def optimize_t_strategy(self, base_position: TPosition, current_price: float, 
                           market_data: pd.DataFrame) -> Dict[str, Any]:
        """
        优化T+0策略
        
        Args:
            base_position: 底仓信息
            current_price: 当前价格
            market_data: 市场数据
            
        Returns:
            优化建议
        """
        optimization_result = {
            'recommended_action': 'HOLD',
            'optimal_shares': 0,
            'expected_cost_reduction': 0.0,
            'risk_reward_ratio': 0.0,
            'confidence': 0.0,
            'reasons': []
        }
        
        # 计算当前成本基础
        current_cost_basis = self._calculate_cost_basis(base_position)
        
        # 分析做T机会
        t_opportunities = self._analyze_t_opportunities(base_position, current_price, market_data)
        
        # 选择最优策略
        best_opportunity = self._select_best_opportunity(t_opportunities, current_cost_basis)
        
        if best_opportunity:
            optimization_result.update(best_opportunity)
        
        return optimization_result
    
    def _calculate_cost_basis(self, position: TPosition) -> float:
        """计算成本基础"""
        return position.avg_cost
    
    def _analyze_t_opportunities(self, base_position: TPosition, current_price: float, 
                                market_data: pd.DataFrame) -> List[Dict]:
        """分析做T机会"""
        opportunities = []
        
        # 分析买入T仓机会
        if current_price < base_position.avg_cost * 0.99:  # 低于成本1%
            buy_opportunity = self._analyze_buy_t_opportunity(base_position, current_price, market_data)
            if buy_opportunity:
                opportunities.append(buy_opportunity)
        
        # 分析卖出T仓机会
        if current_price > base_position.avg_cost * 1.01:  # 高于成本1%
            sell_opportunity = self._analyze_sell_t_opportunity(base_position, current_price, market_data)
            if sell_opportunity:
                opportunities.append(sell_opportunity)
        
        return opportunities
    
    def _analyze_buy_t_opportunity(self, base_position: TPosition, current_price: float,
                                  market_data: pd.DataFrame) -> Optional[Dict]:
        """分析买入T仓机会"""
        # 计算可买入数量
        max_buy_shares = int(base_position.shares * self.max_t_ratio)
        max_buy_shares = (max_buy_shares // 100) * 100  # 整手
        
        if max_buy_shares < 100:
            return None
        
        # 计算交易成本
        trade_cost = self._calculate_trade_cost(max_buy_shares, current_price, 'BUY')
        
        # 估算预期收益
        expected_return = self._estimate_t_return(market_data, 'BUY')
        expected_profit = max_buy_shares * current_price * expected_return - trade_cost
        
        # 计算成本降低效果
        if expected_profit > 0:
            cost_reduction = expected_profit / (base_position.shares * base_position.avg_cost)
        else:
            cost_reduction = 0
        
        # 风险收益比
        risk = self._calculate_t_risk(market_data, 'BUY')
        risk_reward_ratio = expected_return / max(risk, 0.001)
        
        if cost_reduction > 0.002 and risk_reward_ratio > 1.5:  # 成本降低0.2%且风险收益比>1.5
            return {
                'recommended_action': 'BUY_T',
                'optimal_shares': max_buy_shares,
                'expected_cost_reduction': cost_reduction,
                'risk_reward_ratio': risk_reward_ratio,
                'confidence': min(cost_reduction * 50, 1.0),
                'reasons': [f'预期成本降低{cost_reduction:.3%}', f'风险收益比{risk_reward_ratio:.2f}']
            }
        
        return None
    
    def _analyze_sell_t_opportunity(self, base_position: TPosition, current_price: float,
                                   market_data: pd.DataFrame) -> Optional[Dict]:
        """分析卖出T仓机会"""
        # 计算可卖出数量（保留底仓）
        min_keep_shares = max(100, int(base_position.shares * 0.7))  # 至少保留70%
        max_sell_shares = base_position.shares - min_keep_shares
        max_sell_shares = (max_sell_shares // 100) * 100  # 整手
        
        if max_sell_shares < 100:
            return None
        
        # 计算交易成本
        trade_cost = self._calculate_trade_cost(max_sell_shares, current_price, 'SELL')
        
        # 估算预期收益
        expected_return = self._estimate_t_return(market_data, 'SELL')
        expected_profit = max_sell_shares * current_price * expected_return - trade_cost
        
        # 计算成本降低效果
        if expected_profit > 0:
            cost_reduction = expected_profit / (base_position.shares * base_position.avg_cost)
        else:
            cost_reduction = 0
        
        # 风险收益比
        risk = self._calculate_t_risk(market_data, 'SELL')
        risk_reward_ratio = expected_return / max(risk, 0.001)
        
        if cost_reduction > 0.002 and risk_reward_ratio > 1.5:
            return {
                'recommended_action': 'SELL_T',
                'optimal_shares': max_sell_shares,
                'expected_cost_reduction': cost_reduction,
                'risk_reward_ratio': risk_reward_ratio,
                'confidence': min(cost_reduction * 50, 1.0),
                'reasons': [f'预期成本降低{cost_reduction:.3%}', f'风险收益比{risk_reward_ratio:.2f}']
            }
        
        return None
    
    def _select_best_opportunity(self, opportunities: List[Dict], current_cost: float) -> Optional[Dict]:
        """选择最佳机会"""
        if not opportunities:
            return None
        
        # 按成本降低效果排序
        opportunities.sort(key=lambda x: x['expected_cost_reduction'], reverse=True)
        
        return opportunities[0]
    
    def _calculate_trade_cost(self, shares: int, price: float, direction: str) -> float:
        """计算交易成本"""
        trade_value = shares * price
        
        # 佣金
        commission = max(trade_value * self.commission_rate, self.min_commission)
        
        # 印花税（仅卖出）
        stamp_tax = trade_value * self.stamp_tax_rate if direction == 'SELL' else 0
        
        # 过户费（忽略，金额很小）
        
        return commission + stamp_tax
    
    def _estimate_t_return(self, market_data: pd.DataFrame, direction: str) -> float:
        """估算T仓预期收益率"""
        if len(market_data) < 10:
            return 0.005  # 默认0.5%
        
        # 基于历史波动率估算
        returns = market_data['close'].pct_change().dropna()
        volatility = returns.std()
        
        # 基于方向调整
        if direction == 'BUY':
            # 买入T仓，期望价格上涨
            expected_return = min(volatility * 2, 0.03)  # 最大3%
        else:
            # 卖出T仓，期望价格下跌后回补
            expected_return = min(volatility * 1.5, 0.025)  # 最大2.5%
        
        return max(expected_return, 0.005)  # 最小0.5%
    
    def _calculate_t_risk(self, market_data: pd.DataFrame, direction: str) -> float:
        """计算T仓风险"""
        if len(market_data) < 10:
            return 0.01  # 默认1%风险
        
        # 基于历史波动率
        returns = market_data['close'].pct_change().dropna()
        volatility = returns.std()
        
        # 风险为波动率的一定倍数
        risk = volatility * 1.5
        
        return min(max(risk, 0.005), 0.05)  # 风险在0.5%-5%之间
    
    def calculate_cost_reduction_effect(self, base_position: TPosition, 
                                      t_trades: List[TTradeRecord]) -> Dict[str, float]:
        """
        计算成本降低效果
        
        Args:
            base_position: 底仓信息
            t_trades: T仓交易记录
            
        Returns:
            成本降低效果统计
        """
        if not t_trades:
            return {
                'total_profit': 0.0,
                'cost_reduction_pct': 0.0,
                'new_cost_basis': base_position.avg_cost,
                'trade_count': 0,
                'success_rate': 0.0
            }
        
        # 计算总利润
        total_profit = sum(trade.profit for trade in t_trades)
        
        # 计算成本降低百分比
        original_cost = base_position.avg_cost * base_position.shares
        cost_reduction_pct = total_profit / original_cost if original_cost > 0 else 0
        
        # 计算新的成本基础
        new_cost_basis = base_position.avg_cost - (total_profit / base_position.shares)
        
        # 计算成功率
        successful_trades = sum(1 for trade in t_trades if trade.profit > 0)
        success_rate = successful_trades / len(t_trades) if t_trades else 0
        
        return {
            'total_profit': total_profit,
            'cost_reduction_pct': cost_reduction_pct,
            'new_cost_basis': new_cost_basis,
            'trade_count': len(t_trades),
            'success_rate': success_rate
        }
    
    def record_trade(self, trade: TTradeRecord):
        """记录交易"""
        self.trade_history.append(trade)
        
        # 更新日统计
        date_key = trade.timestamp.strftime('%Y-%m-%d')
        if date_key not in self.daily_stats:
            self.daily_stats[date_key] = {
                'trades': 0,
                'profit': 0.0,
                'cost_reduction': 0.0
            }
        
        self.daily_stats[date_key]['trades'] += 1
        self.daily_stats[date_key]['profit'] += trade.profit
    
    def get_daily_performance(self, date: str = None) -> Dict[str, Any]:
        """获取日度表现"""
        if date is None:
            date = datetime.now().strftime('%Y-%m-%d')
        
        return self.daily_stats.get(date, {
            'trades': 0,
            'profit': 0.0,
            'cost_reduction': 0.0
        })