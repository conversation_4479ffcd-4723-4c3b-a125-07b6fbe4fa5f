"""
交易状态同步和监控系统
实现实时交易监控、状态同步和异常检测
"""

import logging
import threading
import time
import json
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, deque
import queue

from .broker_interface import Order, OrderStatus, Trade, Position, Account
from .broker_adapter import BrokerAdapter
from .order_router import OrderRouter


class AlertLevel(Enum):
    """告警级别枚举"""
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


@dataclass
class TradingAlert:
    """交易告警数据类"""
    alert_id: str
    level: AlertLevel
    title: str
    message: str
    source: str
    timestamp: datetime = field(default_factory=datetime.now)
    acknowledged: bool = False
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class TradingMetrics:
    """交易指标数据类"""
    timestamp: datetime
    total_orders: int = 0
    successful_orders: int = 0
    failed_orders: int = 0
    pending_orders: int = 0
    total_trades: int = 0
    total_volume: float = 0.0
    total_value: float = 0.0
    avg_response_time: float = 0.0
    success_rate: float = 0.0
    pnl: float = 0.0
    positions_count: int = 0


class TradingMonitor:
    """交易监控系统
    
    负责实时监控交易状态、同步数据和异常检测
    提供告警机制和性能统计功能
    """
    
    def __init__(self, broker_adapter: BrokerAdapter, order_router: OrderRouter, 
                 config: Dict[str, Any]):
        """初始化交易监控系统
        
        Args:
            broker_adapter: 券商适配器
            order_router: 订单路由器
            config: 监控配置
        """
        self.broker_adapter = broker_adapter
        self.order_router = order_router
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 监控配置
        self.sync_interval = config.get('sync_interval', 5)  # 同步间隔（秒）
        self.metrics_interval = config.get('metrics_interval', 60)  # 指标计算间隔
        self.alert_retention_hours = config.get('alert_retention_hours', 24)
        self.metrics_retention_hours = config.get('metrics_retention_hours', 168)  # 7天
        
        # 数据存储
        self.orders_cache: Dict[str, Order] = {}
        self.trades_cache: List[Trade] = []
        self.positions_cache: Dict[str, Position] = {}
        self.account_cache: Optional[Account] = None
        
        # 告警系统
        self.alerts: List[TradingAlert] = []
        self.alert_handlers: List[Callable[[TradingAlert], None]] = []
        self.alert_queue = queue.Queue()
        
        # 性能指标
        self.metrics_history: deque = deque(maxlen=self.metrics_retention_hours)
        self.current_metrics = TradingMetrics(timestamp=datetime.now())
        
        # 异常检测配置
        self.anomaly_config = config.get('anomaly_detection', {})
        self.max_order_failure_rate = self.anomaly_config.get('max_order_failure_rate', 0.1)
        self.max_response_time = self.anomaly_config.get('max_response_time', 5.0)
        self.min_success_rate = self.anomaly_config.get('min_success_rate', 0.95)
        
        # 监控状态
        self.is_monitoring = False
        self.last_sync_time: Optional[datetime] = None
        self.sync_errors = 0
        self.max_sync_errors = config.get('max_sync_errors', 5)
        
        # 线程管理
        self.monitor_thread: Optional[threading.Thread] = None
        self.metrics_thread: Optional[threading.Thread] = None
        self.alert_thread: Optional[threading.Thread] = None
        
        # 统计信息
        self.stats = {
            'total_syncs': 0,
            'successful_syncs': 0,
            'failed_syncs': 0,
            'alerts_generated': 0,
            'anomalies_detected': 0
        }
        
        self.logger.info("交易监控系统初始化完成")
    
    def start_monitoring(self):
        """启动监控"""
        if self.is_monitoring:
            self.logger.warning("监控系统已在运行")
            return
        
        self.is_monitoring = True
        
        # 启动数据同步线程
        self.monitor_thread = threading.Thread(
            target=self._monitor_worker,
            name="TradingMonitor",
            daemon=True
        )
        self.monitor_thread.start()
        
        # 启动指标计算线程
        self.metrics_thread = threading.Thread(
            target=self._metrics_worker,
            name="MetricsCalculator",
            daemon=True
        )
        self.metrics_thread.start()
        
        # 启动告警处理线程
        self.alert_thread = threading.Thread(
            target=self._alert_worker,
            name="AlertHandler",
            daemon=True
        )
        self.alert_thread.start()
        
        self.logger.info("交易监控系统已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        
        # 等待线程结束
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        
        if self.metrics_thread and self.metrics_thread.is_alive():
            self.metrics_thread.join(timeout=5)
        
        if self.alert_thread and self.alert_thread.is_alive():
            self.alert_thread.join(timeout=5)
        
        self.logger.info("交易监控系统已停止")
    
    def _monitor_worker(self):
        """监控工作线程"""
        while self.is_monitoring:
            try:
                start_time = time.time()
                
                # 同步数据
                self._sync_trading_data()
                
                # 检测异常
                self._detect_anomalies()
                
                # 更新统计
                sync_time = time.time() - start_time
                self.stats['total_syncs'] += 1
                self.stats['successful_syncs'] += 1
                self.sync_errors = 0
                self.last_sync_time = datetime.now()
                
                # 记录同步时间过长的情况
                if sync_time > 2.0:
                    self._generate_alert(
                        AlertLevel.WARNING,
                        "数据同步耗时过长",
                        f"数据同步耗时 {sync_time:.2f} 秒",
                        "TradingMonitor"
                    )
                
                time.sleep(self.sync_interval)
                
            except Exception as e:
                self.sync_errors += 1
                self.stats['failed_syncs'] += 1
                
                self.logger.error(f"数据同步异常: {e}")
                
                # 连续同步失败告警
                if self.sync_errors >= self.max_sync_errors:
                    self._generate_alert(
                        AlertLevel.CRITICAL,
                        "数据同步连续失败",
                        f"数据同步已连续失败 {self.sync_errors} 次",
                        "TradingMonitor"
                    )
                
                time.sleep(min(self.sync_interval * 2, 30))  # 失败时延长等待时间
    
    def _sync_trading_data(self):
        """同步交易数据"""
        try:
            # 同步账户信息
            self.account_cache = self.broker_adapter.get_account_info()
            
            # 同步持仓信息
            positions = self.broker_adapter.get_positions()
            self.positions_cache = {pos.symbol: pos for pos in positions}
            
            # 同步订单信息
            orders = self.broker_adapter.get_orders()
            for order in orders:
                old_order = self.orders_cache.get(order.order_id)
                self.orders_cache[order.order_id] = order
                
                # 检测订单状态变化
                if old_order and old_order.status != order.status:
                    self._on_order_status_changed(old_order, order)
            
            # 同步成交记录
            trades = self.broker_adapter.get_trades()
            new_trades = []
            
            existing_trade_ids = {trade.trade_id for trade in self.trades_cache}
            for trade in trades:
                if trade.trade_id not in existing_trade_ids:
                    new_trades.append(trade)
            
            if new_trades:
                self.trades_cache.extend(new_trades)
                # 保持成交记录数量限制
                max_trades = self.config.get('max_trades_cache', 10000)
                if len(self.trades_cache) > max_trades:
                    self.trades_cache = self.trades_cache[-max_trades:]
                
                # 通知新成交
                for trade in new_trades:
                    self._on_new_trade(trade)
            
        except Exception as e:
            self.logger.error(f"同步交易数据失败: {e}")
            raise
    
    def _on_order_status_changed(self, old_order: Order, new_order: Order):
        """处理订单状态变化
        
        Args:
            old_order: 旧订单状态
            new_order: 新订单状态
        """
        self.logger.info(
            f"订单状态变化: {new_order.order_id} "
            f"{old_order.status.value} -> {new_order.status.value}"
        )
        
        # 订单失败告警
        if new_order.status in [OrderStatus.REJECTED, OrderStatus.ERROR]:
            self._generate_alert(
                AlertLevel.ERROR,
                "订单执行失败",
                f"订单 {new_order.order_id} 执行失败: {new_order.error_msg or '未知错误'}",
                "OrderExecution",
                metadata={
                    'order_id': new_order.order_id,
                    'symbol': new_order.symbol,
                    'side': new_order.side.value,
                    'quantity': new_order.quantity,
                    'error_msg': new_order.error_msg
                }
            )
        
        # 订单成交通知
        elif new_order.status == OrderStatus.FILLED:
            self.logger.info(
                f"订单成交: {new_order.order_id} {new_order.symbol} "
                f"{new_order.side.value} {new_order.filled_quantity}@{new_order.avg_fill_price}"
            )
    
    def _on_new_trade(self, trade: Trade):
        """处理新成交记录
        
        Args:
            trade: 成交记录
        """
        self.logger.info(
            f"新成交: {trade.trade_id} {trade.symbol} "
            f"{trade.side.value} {trade.quantity}@{trade.price}"
        )
        
        # 大额成交告警
        trade_value = trade.quantity * trade.price
        large_trade_threshold = self.config.get('large_trade_threshold', 100000)
        
        if trade_value > large_trade_threshold:
            self._generate_alert(
                AlertLevel.INFO,
                "大额成交",
                f"成交金额 {trade_value:,.2f} 元超过阈值",
                "TradeExecution",
                metadata={
                    'trade_id': trade.trade_id,
                    'symbol': trade.symbol,
                    'value': trade_value
                }
            )
    
    def _detect_anomalies(self):
        """检测交易异常"""
        try:
            # 检测订单失败率异常
            self._check_order_failure_rate()
            
            # 检测响应时间异常
            self._check_response_time()
            
            # 检测成功率异常
            self._check_success_rate()
            
            # 检测持仓异常
            self._check_position_anomalies()
            
            # 检测账户异常
            self._check_account_anomalies()
            
        except Exception as e:
            self.logger.error(f"异常检测失败: {e}")
    
    def _check_order_failure_rate(self):
        """检查订单失败率"""
        if not self.orders_cache:
            return
        
        recent_orders = [
            order for order in self.orders_cache.values()
            if order.create_time and 
            order.create_time > datetime.now() - timedelta(minutes=30)
        ]
        
        if len(recent_orders) < 10:  # 样本太少
            return
        
        failed_orders = [
            order for order in recent_orders
            if order.status in [OrderStatus.REJECTED, OrderStatus.ERROR]
        ]
        
        failure_rate = len(failed_orders) / len(recent_orders)
        
        if failure_rate > self.max_order_failure_rate:
            self.stats['anomalies_detected'] += 1
            self._generate_alert(
                AlertLevel.ERROR,
                "订单失败率异常",
                f"近30分钟订单失败率 {failure_rate:.2%} 超过阈值 {self.max_order_failure_rate:.2%}",
                "AnomalyDetection",
                metadata={
                    'failure_rate': failure_rate,
                    'failed_count': len(failed_orders),
                    'total_count': len(recent_orders)
                }
            )
    
    def _check_response_time(self):
        """检查响应时间异常"""
        routing_stats = self.order_router.get_routing_statistics()
        avg_routing_time = routing_stats.get('avg_routing_time', 0)
        
        if avg_routing_time > self.max_response_time:
            self.stats['anomalies_detected'] += 1
            self._generate_alert(
                AlertLevel.WARNING,
                "响应时间异常",
                f"平均路由时间 {avg_routing_time:.2f} 秒超过阈值 {self.max_response_time} 秒",
                "AnomalyDetection",
                metadata={'avg_routing_time': avg_routing_time}
            )
    
    def _check_success_rate(self):
        """检查成功率异常"""
        broker_status = self.broker_adapter.get_broker_status()
        stats = broker_status.get('statistics', {})
        
        total_orders = stats.get('total_orders', 0)
        successful_orders = stats.get('successful_orders', 0)
        
        if total_orders > 0:
            success_rate = successful_orders / total_orders
            
            if success_rate < self.min_success_rate:
                self.stats['anomalies_detected'] += 1
                self._generate_alert(
                    AlertLevel.ERROR,
                    "成功率异常",
                    f"订单成功率 {success_rate:.2%} 低于阈值 {self.min_success_rate:.2%}",
                    "AnomalyDetection",
                    metadata={
                        'success_rate': success_rate,
                        'successful_orders': successful_orders,
                        'total_orders': total_orders
                    }
                )
    
    def _check_position_anomalies(self):
        """检查持仓异常"""
        if not self.positions_cache or not self.account_cache:
            return
        
        # 检查持仓集中度
        total_market_value = self.account_cache.market_value
        if total_market_value > 0:
            for symbol, position in self.positions_cache.items():
                concentration = position.market_value / total_market_value
                max_concentration = self.config.get('max_position_concentration', 0.5)
                
                if concentration > max_concentration:
                    self._generate_alert(
                        AlertLevel.WARNING,
                        "持仓集中度过高",
                        f"{symbol} 持仓占比 {concentration:.2%} 超过阈值 {max_concentration:.2%}",
                        "RiskMonitoring",
                        metadata={
                            'symbol': symbol,
                            'concentration': concentration,
                            'position_value': position.market_value
                        }
                    )
        
        # 检查持仓亏损
        for symbol, position in self.positions_cache.items():
            if position.unrealized_pnl_pct < -0.1:  # 亏损超过10%
                self._generate_alert(
                    AlertLevel.WARNING,
                    "持仓亏损较大",
                    f"{symbol} 未实现亏损 {position.unrealized_pnl_pct:.2%}",
                    "RiskMonitoring",
                    metadata={
                        'symbol': symbol,
                        'unrealized_pnl_pct': position.unrealized_pnl_pct,
                        'unrealized_pnl': position.unrealized_pnl
                    }
                )
    
    def _check_account_anomalies(self):
        """检查账户异常"""
        if not self.account_cache:
            return
        
        # 检查可用资金过低
        cash_ratio = self.account_cache.available_cash / self.account_cache.total_assets
        min_cash_ratio = self.config.get('min_cash_ratio', 0.05)
        
        if cash_ratio < min_cash_ratio:
            self._generate_alert(
                AlertLevel.WARNING,
                "可用资金不足",
                f"可用资金占比 {cash_ratio:.2%} 低于阈值 {min_cash_ratio:.2%}",
                "RiskMonitoring",
                metadata={
                    'cash_ratio': cash_ratio,
                    'available_cash': self.account_cache.available_cash,
                    'total_assets': self.account_cache.total_assets
                }
            )
        
        # 检查总亏损
        if self.account_cache.profit_loss < 0:
            loss_ratio = abs(self.account_cache.profit_loss) / self.account_cache.total_assets
            max_loss_ratio = self.config.get('max_loss_ratio', 0.2)
            
            if loss_ratio > max_loss_ratio:
                self._generate_alert(
                    AlertLevel.CRITICAL,
                    "账户亏损过大",
                    f"账户亏损比例 {loss_ratio:.2%} 超过阈值 {max_loss_ratio:.2%}",
                    "RiskMonitoring",
                    metadata={
                        'loss_ratio': loss_ratio,
                        'profit_loss': self.account_cache.profit_loss
                    }
                )
    
    def _metrics_worker(self):
        """指标计算工作线程"""
        while self.is_monitoring:
            try:
                self._calculate_metrics()
                time.sleep(self.metrics_interval)
            except Exception as e:
                self.logger.error(f"指标计算异常: {e}")
                time.sleep(30)
    
    def _calculate_metrics(self):
        """计算交易指标"""
        try:
            metrics = TradingMetrics(timestamp=datetime.now())
            
            # 订单统计
            if self.orders_cache:
                metrics.total_orders = len(self.orders_cache)
                metrics.successful_orders = sum(
                    1 for order in self.orders_cache.values()
                    if order.status == OrderStatus.FILLED
                )
                metrics.failed_orders = sum(
                    1 for order in self.orders_cache.values()
                    if order.status in [OrderStatus.REJECTED, OrderStatus.ERROR]
                )
                metrics.pending_orders = sum(
                    1 for order in self.orders_cache.values()
                    if order.status in [OrderStatus.PENDING, OrderStatus.SUBMITTED]
                )
                
                if metrics.total_orders > 0:
                    metrics.success_rate = metrics.successful_orders / metrics.total_orders
            
            # 成交统计
            if self.trades_cache:
                metrics.total_trades = len(self.trades_cache)
                metrics.total_volume = sum(trade.quantity for trade in self.trades_cache)
                metrics.total_value = sum(
                    trade.quantity * trade.price for trade in self.trades_cache
                )
            
            # 路由统计
            routing_stats = self.order_router.get_routing_statistics()
            metrics.avg_response_time = routing_stats.get('avg_routing_time', 0)
            
            # 账户统计
            if self.account_cache:
                metrics.pnl = self.account_cache.profit_loss
            
            # 持仓统计
            metrics.positions_count = len(self.positions_cache)
            
            # 保存指标
            self.current_metrics = metrics
            self.metrics_history.append(metrics)
            
        except Exception as e:
            self.logger.error(f"计算交易指标失败: {e}")
    
    def _alert_worker(self):
        """告警处理工作线程"""
        while self.is_monitoring:
            try:
                # 处理告警队列
                try:
                    alert = self.alert_queue.get(timeout=1)
                    self._process_alert(alert)
                except queue.Empty:
                    continue
                
                # 清理过期告警
                self._cleanup_old_alerts()
                
            except Exception as e:
                self.logger.error(f"告警处理异常: {e}")
                time.sleep(5)
    
    def _generate_alert(self, level: AlertLevel, title: str, message: str, 
                       source: str, metadata: Optional[Dict[str, Any]] = None):
        """生成告警
        
        Args:
            level: 告警级别
            title: 告警标题
            message: 告警消息
            source: 告警源
            metadata: 元数据
        """
        alert = TradingAlert(
            alert_id=f"ALERT_{int(time.time() * 1000)}",
            level=level,
            title=title,
            message=message,
            source=source,
            metadata=metadata or {}
        )
        
        self.alerts.append(alert)
        self.alert_queue.put(alert)
        self.stats['alerts_generated'] += 1
        
        self.logger.log(
            self._get_log_level(level),
            f"[{level.value}] {title}: {message}"
        )
    
    def _process_alert(self, alert: TradingAlert):
        """处理告警
        
        Args:
            alert: 告警对象
        """
        # 调用所有告警处理器
        for handler in self.alert_handlers:
            try:
                handler(alert)
            except Exception as e:
                self.logger.error(f"告警处理器执行失败: {e}")
    
    def _get_log_level(self, alert_level: AlertLevel) -> int:
        """获取日志级别
        
        Args:
            alert_level: 告警级别
            
        Returns:
            int: 日志级别
        """
        level_map = {
            AlertLevel.INFO: logging.INFO,
            AlertLevel.WARNING: logging.WARNING,
            AlertLevel.ERROR: logging.ERROR,
            AlertLevel.CRITICAL: logging.CRITICAL
        }
        return level_map.get(alert_level, logging.INFO)
    
    def _cleanup_old_alerts(self):
        """清理过期告警"""
        cutoff_time = datetime.now() - timedelta(hours=self.alert_retention_hours)
        self.alerts = [
            alert for alert in self.alerts
            if alert.timestamp > cutoff_time
        ]
    
    def add_alert_handler(self, handler: Callable[[TradingAlert], None]):
        """添加告警处理器
        
        Args:
            handler: 告警处理函数
        """
        self.alert_handlers.append(handler)
        self.logger.info(f"添加告警处理器: {handler.__name__}")
    
    def get_current_status(self) -> Dict[str, Any]:
        """获取当前监控状态
        
        Returns:
            Dict[str, Any]: 监控状态信息
        """
        return {
            'is_monitoring': self.is_monitoring,
            'last_sync_time': self.last_sync_time.isoformat() if self.last_sync_time else None,
            'sync_errors': self.sync_errors,
            'statistics': self.stats.copy(),
            'current_metrics': {
                'timestamp': self.current_metrics.timestamp.isoformat(),
                'total_orders': self.current_metrics.total_orders,
                'successful_orders': self.current_metrics.successful_orders,
                'failed_orders': self.current_metrics.failed_orders,
                'pending_orders': self.current_metrics.pending_orders,
                'success_rate': self.current_metrics.success_rate,
                'total_trades': self.current_metrics.total_trades,
                'total_volume': self.current_metrics.total_volume,
                'total_value': self.current_metrics.total_value,
                'avg_response_time': self.current_metrics.avg_response_time,
                'pnl': self.current_metrics.pnl,
                'positions_count': self.current_metrics.positions_count
            },
            'active_alerts_count': len([a for a in self.alerts if not a.acknowledged]),
            'cache_status': {
                'orders_count': len(self.orders_cache),
                'trades_count': len(self.trades_cache),
                'positions_count': len(self.positions_cache),
                'account_cached': self.account_cache is not None
            }
        }
    
    def get_alerts(self, level: Optional[AlertLevel] = None, 
                  acknowledged: Optional[bool] = None) -> List[TradingAlert]:
        """获取告警列表
        
        Args:
            level: 可选的告警级别过滤
            acknowledged: 可选的确认状态过滤
            
        Returns:
            List[TradingAlert]: 告警列表
        """
        alerts = self.alerts.copy()
        
        if level:
            alerts = [alert for alert in alerts if alert.level == level]
        
        if acknowledged is not None:
            alerts = [alert for alert in alerts if alert.acknowledged == acknowledged]
        
        # 按时间倒序排列
        alerts.sort(key=lambda x: x.timestamp, reverse=True)
        
        return alerts
    
    def acknowledge_alert(self, alert_id: str) -> bool:
        """确认告警
        
        Args:
            alert_id: 告警ID
            
        Returns:
            bool: 确认是否成功
        """
        for alert in self.alerts:
            if alert.alert_id == alert_id:
                alert.acknowledged = True
                self.logger.info(f"告警已确认: {alert_id}")
                return True
        
        return False
    
    def get_metrics_history(self, hours: int = 24) -> List[TradingMetrics]:
        """获取指标历史
        
        Args:
            hours: 历史小时数
            
        Returns:
            List[TradingMetrics]: 指标历史列表
        """
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        return [
            metrics for metrics in self.metrics_history
            if metrics.timestamp > cutoff_time
        ]
    
    def export_monitoring_data(self) -> Dict[str, Any]:
        """导出监控数据
        
        Returns:
            Dict[str, Any]: 监控数据
        """
        return {
            'status': self.get_current_status(),
            'alerts': [
                {
                    'alert_id': alert.alert_id,
                    'level': alert.level.value,
                    'title': alert.title,
                    'message': alert.message,
                    'source': alert.source,
                    'timestamp': alert.timestamp.isoformat(),
                    'acknowledged': alert.acknowledged,
                    'metadata': alert.metadata
                }
                for alert in self.alerts
            ],
            'metrics_history': [
                {
                    'timestamp': metrics.timestamp.isoformat(),
                    'total_orders': metrics.total_orders,
                    'successful_orders': metrics.successful_orders,
                    'failed_orders': metrics.failed_orders,
                    'success_rate': metrics.success_rate,
                    'total_trades': metrics.total_trades,
                    'total_volume': metrics.total_volume,
                    'total_value': metrics.total_value,
                    'avg_response_time': metrics.avg_response_time,
                    'pnl': metrics.pnl,
                    'positions_count': metrics.positions_count
                }
                for metrics in self.metrics_history
            ]
        }
    
    def __del__(self):
        """析构函数"""
        try:
            self.stop_monitoring()
        except Exception:
            pass