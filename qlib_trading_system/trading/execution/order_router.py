"""
订单路由和负载均衡模块
实现智能订单路由、负载均衡和订单分片功能
"""

import logging
import threading
import time
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import queue
from concurrent.futures import Thread<PERSON>oolExecutor, Future
import math

from .broker_interface import (
    Order, OrderSide, OrderType, OrderStatus, Position, Account,
    BrokerException, OrderException
)
from .broker_adapter import BrokerAdapter


class RoutingStrategy(Enum):
    """路由策略枚举"""
    ROUND_ROBIN = "ROUND_ROBIN"        # 轮询
    WEIGHTED = "WEIGHTED"              # 加权
    LEAST_LOADED = "LEAST_LOADED"      # 最少负载
    FASTEST_RESPONSE = "FASTEST_RESPONSE"  # 最快响应
    COST_OPTIMIZED = "COST_OPTIMIZED"  # 成本优化


@dataclass
class OrderFragment:
    """订单分片数据类"""
    fragment_id: str
    parent_order_id: str
    broker_name: str
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: int
    price: Optional[float] = None
    status: OrderStatus = OrderStatus.PENDING
    broker_order_id: Optional[str] = None
    filled_quantity: int = 0
    avg_fill_price: float = 0.0
    create_time: datetime = field(default_factory=datetime.now)
    update_time: Optional[datetime] = None
    error_msg: Optional[str] = None


@dataclass
class BrokerLoad:
    """券商负载信息"""
    broker_name: str
    pending_orders: int = 0
    processing_orders: int = 0
    avg_response_time: float = 0.0
    success_rate: float = 1.0
    last_update: datetime = field(default_factory=datetime.now)


@dataclass
class RoutingRule:
    """路由规则配置"""
    min_order_size: int = 0
    max_order_size: int = float('inf')
    preferred_brokers: List[str] = field(default_factory=list)
    excluded_brokers: List[str] = field(default_factory=list)
    max_fragments: int = 5
    fragment_size: int = 1000


class OrderRouter:
    """订单路由器
    
    负责智能订单路由、负载均衡和大订单分片
    支持多种路由策略和动态负载均衡
    """
    
    def __init__(self, broker_adapter: BrokerAdapter, config: Dict[str, Any]):
        """初始化订单路由器
        
        Args:
            broker_adapter: 券商适配器实例
            config: 路由配置
        """
        self.broker_adapter = broker_adapter
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 路由策略
        self.routing_strategy = RoutingStrategy(
            config.get('routing_strategy', 'WEIGHTED')
        )
        
        # 路由规则
        self.routing_rules = self._load_routing_rules(config.get('routing_rules', {}))
        
        # 券商负载信息
        self.broker_loads: Dict[str, BrokerLoad] = {}
        self.load_update_interval = config.get('load_update_interval', 10)
        
        # 订单分片管理
        self.order_fragments: Dict[str, List[OrderFragment]] = {}
        self.fragment_lock = threading.Lock()
        
        # 性能统计
        self.routing_stats = {
            'total_routed': 0,
            'successful_routes': 0,
            'failed_routes': 0,
            'fragments_created': 0,
            'avg_routing_time': 0.0
        }
        
        # 响应时间追踪
        self.response_times: Dict[str, List[float]] = {}
        self.max_response_samples = 100
        
        # 线程池
        self.executor = ThreadPoolExecutor(
            max_workers=config.get('max_workers', 10),
            thread_name_prefix="OrderRouter"
        )
        
        # 初始化券商负载信息
        self._initialize_broker_loads()
        
        # 启动负载监控线程
        self._start_load_monitor()
    
    def _load_routing_rules(self, rules_config: Dict[str, Any]) -> Dict[str, RoutingRule]:
        """加载路由规则配置
        
        Args:
            rules_config: 规则配置字典
            
        Returns:
            Dict[str, RoutingRule]: 路由规则字典
        """
        rules = {}
        
        for rule_name, rule_config in rules_config.items():
            rules[rule_name] = RoutingRule(
                min_order_size=rule_config.get('min_order_size', 0),
                max_order_size=rule_config.get('max_order_size', float('inf')),
                preferred_brokers=rule_config.get('preferred_brokers', []),
                excluded_brokers=rule_config.get('excluded_brokers', []),
                max_fragments=rule_config.get('max_fragments', 5),
                fragment_size=rule_config.get('fragment_size', 1000)
            )
        
        # 默认规则
        if 'default' not in rules:
            rules['default'] = RoutingRule()
        
        return rules
    
    def _initialize_broker_loads(self):
        """初始化券商负载信息"""
        broker_status = self.broker_adapter.get_broker_status()
        
        for broker_name in broker_status['broker_health'].keys():
            self.broker_loads[broker_name] = BrokerLoad(broker_name=broker_name)
            self.response_times[broker_name] = []
    
    def _start_load_monitor(self):
        """启动负载监控线程"""
        def load_monitor_worker():
            while True:
                try:
                    self._update_broker_loads()
                    time.sleep(self.load_update_interval)
                except Exception as e:
                    self.logger.error(f"负载监控异常: {e}")
                    time.sleep(5)
        
        monitor_thread = threading.Thread(target=load_monitor_worker, daemon=True)
        monitor_thread.start()
        self.logger.info("负载监控线程已启动")
    
    def _update_broker_loads(self):
        """更新券商负载信息"""
        for broker_name, load_info in self.broker_loads.items():
            try:
                # 获取券商当前订单数量
                orders = self.broker_adapter.get_orders()
                pending_count = sum(1 for order in orders if order.status == OrderStatus.PENDING)
                processing_count = sum(1 for order in orders if order.status == OrderStatus.SUBMITTED)
                
                # 计算平均响应时间
                response_times = self.response_times.get(broker_name, [])
                avg_response = sum(response_times) / len(response_times) if response_times else 0.0
                
                # 更新负载信息
                load_info.pending_orders = pending_count
                load_info.processing_orders = processing_count
                load_info.avg_response_time = avg_response
                load_info.last_update = datetime.now()
                
            except Exception as e:
                self.logger.warning(f"更新券商 {broker_name} 负载信息失败: {e}")
    
    def _select_broker(self, order: Order, available_brokers: List[str]) -> str:
        """根据路由策略选择券商
        
        Args:
            order: 订单对象
            available_brokers: 可用券商列表
            
        Returns:
            str: 选中的券商名称
        """
        if not available_brokers:
            raise OrderException("没有可用的券商")
        
        if len(available_brokers) == 1:
            return available_brokers[0]
        
        if self.routing_strategy == RoutingStrategy.ROUND_ROBIN:
            return self._round_robin_select(available_brokers)
        elif self.routing_strategy == RoutingStrategy.WEIGHTED:
            return self._weighted_select(available_brokers)
        elif self.routing_strategy == RoutingStrategy.LEAST_LOADED:
            return self._least_loaded_select(available_brokers)
        elif self.routing_strategy == RoutingStrategy.FASTEST_RESPONSE:
            return self._fastest_response_select(available_brokers)
        elif self.routing_strategy == RoutingStrategy.COST_OPTIMIZED:
            return self._cost_optimized_select(available_brokers, order)
        else:
            return available_brokers[0]
    
    def _round_robin_select(self, brokers: List[str]) -> str:
        """轮询选择券商"""
        if not hasattr(self, '_round_robin_index'):
            self._round_robin_index = 0
        
        selected = brokers[self._round_robin_index % len(brokers)]
        self._round_robin_index += 1
        return selected
    
    def _weighted_select(self, brokers: List[str]) -> str:
        """加权选择券商"""
        broker_status = self.broker_adapter.get_broker_status()
        weights = []
        
        for broker in brokers:
            # 基础权重
            base_weight = self.broker_adapter.broker_weights.get(broker, 1.0)
            
            # 健康状态权重
            health_weight = 1.0 if broker_status['broker_health'].get(broker, False) else 0.1
            
            # 负载权重（负载越低权重越高）
            load_info = self.broker_loads.get(broker)
            if load_info:
                load_factor = 1.0 / (1.0 + load_info.pending_orders + load_info.processing_orders)
            else:
                load_factor = 1.0
            
            total_weight = base_weight * health_weight * load_factor
            weights.append(total_weight)
        
        # 根据权重选择
        total_weight = sum(weights)
        if total_weight == 0:
            return brokers[0]
        
        import random
        rand_val = random.random() * total_weight
        cumulative = 0
        
        for i, weight in enumerate(weights):
            cumulative += weight
            if rand_val <= cumulative:
                return brokers[i]
        
        return brokers[-1]
    
    def _least_loaded_select(self, brokers: List[str]) -> str:
        """选择负载最小的券商"""
        min_load = float('inf')
        selected_broker = brokers[0]
        
        for broker in brokers:
            load_info = self.broker_loads.get(broker)
            if load_info:
                total_load = load_info.pending_orders + load_info.processing_orders
                if total_load < min_load:
                    min_load = total_load
                    selected_broker = broker
        
        return selected_broker
    
    def _fastest_response_select(self, brokers: List[str]) -> str:
        """选择响应最快的券商"""
        min_response_time = float('inf')
        selected_broker = brokers[0]
        
        for broker in brokers:
            load_info = self.broker_loads.get(broker)
            if load_info and load_info.avg_response_time < min_response_time:
                min_response_time = load_info.avg_response_time
                selected_broker = broker
        
        return selected_broker
    
    def _cost_optimized_select(self, brokers: List[str], order: Order) -> str:
        """成本优化选择券商"""
        # 简化实现：选择手续费最低的券商
        # 实际实现中需要考虑各券商的费率结构
        return brokers[0]
    
    def _get_routing_rule(self, order: Order) -> RoutingRule:
        """获取适用的路由规则
        
        Args:
            order: 订单对象
            
        Returns:
            RoutingRule: 适用的路由规则
        """
        order_value = order.quantity * (order.price or 0)
        
        for rule_name, rule in self.routing_rules.items():
            if rule_name == 'default':
                continue
            
            if (rule.min_order_size <= order_value <= rule.max_order_size):
                return rule
        
        return self.routing_rules['default']
    
    def _should_fragment_order(self, order: Order, rule: RoutingRule) -> bool:
        """判断是否需要分片订单
        
        Args:
            order: 订单对象
            rule: 路由规则
            
        Returns:
            bool: 是否需要分片
        """
        return (order.quantity > rule.fragment_size and 
                rule.max_fragments > 1)
    
    def _create_order_fragments(self, order: Order, rule: RoutingRule) -> List[OrderFragment]:
        """创建订单分片
        
        Args:
            order: 原始订单
            rule: 路由规则
            
        Returns:
            List[OrderFragment]: 订单分片列表
        """
        fragments = []
        remaining_quantity = order.quantity
        fragment_count = min(
            math.ceil(order.quantity / rule.fragment_size),
            rule.max_fragments
        )
        
        # 计算每个分片的大小
        base_fragment_size = order.quantity // fragment_count
        extra_quantity = order.quantity % fragment_count
        
        for i in range(fragment_count):
            fragment_size = base_fragment_size
            if i < extra_quantity:
                fragment_size += 1
            
            fragment = OrderFragment(
                fragment_id=f"{order.order_id}_frag_{i+1}",
                parent_order_id=order.order_id,
                broker_name="",  # 稍后分配
                symbol=order.symbol,
                side=order.side,
                order_type=order.order_type,
                quantity=fragment_size,
                price=order.price
            )
            fragments.append(fragment)
        
        return fragments
    
    def route_order(self, order: Order) -> List[str]:
        """路由订单到合适的券商
        
        Args:
            order: 订单对象
            
        Returns:
            List[str]: 订单ID列表（如果分片则返回多个ID）
        """
        start_time = time.time()
        
        try:
            self.routing_stats['total_routed'] += 1
            
            # 获取路由规则
            rule = self._get_routing_rule(order)
            
            # 获取可用券商列表
            broker_status = self.broker_adapter.get_broker_status()
            available_brokers = [
                name for name, is_healthy in broker_status['broker_health'].items()
                if is_healthy and name not in rule.excluded_brokers
            ]
            
            # 应用偏好券商
            if rule.preferred_brokers:
                preferred_available = [
                    broker for broker in rule.preferred_brokers
                    if broker in available_brokers
                ]
                if preferred_available:
                    available_brokers = preferred_available
            
            if not available_brokers:
                raise OrderException("没有可用的券商满足路由规则")
            
            # 判断是否需要分片
            if self._should_fragment_order(order, rule):
                return self._route_fragmented_order(order, rule, available_brokers)
            else:
                return self._route_single_order(order, available_brokers)
        
        except Exception as e:
            self.routing_stats['failed_routes'] += 1
            self.logger.error(f"订单路由失败: {e}")
            raise
        
        finally:
            # 更新路由时间统计
            routing_time = time.time() - start_time
            self._update_routing_stats(routing_time)
    
    def _route_single_order(self, order: Order, available_brokers: List[str]) -> List[str]:
        """路由单个订单
        
        Args:
            order: 订单对象
            available_brokers: 可用券商列表
            
        Returns:
            List[str]: 订单ID列表
        """
        selected_broker = self._select_broker(order, available_brokers)
        
        # 记录响应时间
        start_time = time.time()
        
        try:
            order_id = self.broker_adapter.place_order(order)
            
            # 记录响应时间
            response_time = time.time() - start_time
            self._record_response_time(selected_broker, response_time)
            
            self.routing_stats['successful_routes'] += 1
            self.logger.info(f"订单路由成功: {order.symbol} -> {selected_broker}")
            
            return [order_id]
        
        except Exception as e:
            response_time = time.time() - start_time
            self._record_response_time(selected_broker, response_time)
            raise
    
    def _route_fragmented_order(self, order: Order, rule: RoutingRule, 
                              available_brokers: List[str]) -> List[str]:
        """路由分片订单
        
        Args:
            order: 原始订单
            rule: 路由规则
            available_brokers: 可用券商列表
            
        Returns:
            List[str]: 订单ID列表
        """
        # 创建订单分片
        fragments = self._create_order_fragments(order, rule)
        self.routing_stats['fragments_created'] += len(fragments)
        
        # 为每个分片分配券商
        order_ids = []
        
        with self.fragment_lock:
            self.order_fragments[order.order_id] = fragments
        
        for fragment in fragments:
            try:
                # 选择券商
                selected_broker = self._select_broker(order, available_brokers)
                fragment.broker_name = selected_broker
                
                # 创建实际订单
                fragment_order = Order(
                    order_id=fragment.fragment_id,
                    symbol=fragment.symbol,
                    side=fragment.side,
                    order_type=fragment.order_type,
                    quantity=fragment.quantity,
                    price=fragment.price,
                    create_time=datetime.now()
                )
                
                # 提交订单
                start_time = time.time()
                broker_order_id = self.broker_adapter.place_order(fragment_order)
                response_time = time.time() - start_time
                
                # 更新分片信息
                fragment.broker_order_id = broker_order_id
                fragment.status = OrderStatus.SUBMITTED
                fragment.update_time = datetime.now()
                
                # 记录响应时间
                self._record_response_time(selected_broker, response_time)
                
                order_ids.append(broker_order_id)
                
                self.logger.info(
                    f"订单分片路由成功: {fragment.fragment_id} -> {selected_broker}"
                )
                
            except Exception as e:
                fragment.status = OrderStatus.ERROR
                fragment.error_msg = str(e)
                fragment.update_time = datetime.now()
                
                self.logger.error(f"订单分片路由失败: {fragment.fragment_id} - {e}")
        
        if order_ids:
            self.routing_stats['successful_routes'] += 1
        
        return order_ids
    
    def _record_response_time(self, broker_name: str, response_time: float):
        """记录券商响应时间
        
        Args:
            broker_name: 券商名称
            response_time: 响应时间（秒）
        """
        if broker_name not in self.response_times:
            self.response_times[broker_name] = []
        
        response_times = self.response_times[broker_name]
        response_times.append(response_time)
        
        # 保持样本数量限制
        if len(response_times) > self.max_response_samples:
            response_times.pop(0)
    
    def _update_routing_stats(self, routing_time: float):
        """更新路由统计信息
        
        Args:
            routing_time: 路由耗时
        """
        total_routed = self.routing_stats['total_routed']
        current_avg = self.routing_stats['avg_routing_time']
        
        # 计算新的平均路由时间
        new_avg = (current_avg * (total_routed - 1) + routing_time) / total_routed
        self.routing_stats['avg_routing_time'] = new_avg
    
    def get_order_fragments(self, parent_order_id: str) -> List[OrderFragment]:
        """获取订单分片信息
        
        Args:
            parent_order_id: 父订单ID
            
        Returns:
            List[OrderFragment]: 订单分片列表
        """
        with self.fragment_lock:
            return self.order_fragments.get(parent_order_id, []).copy()
    
    def update_fragment_status(self, fragment_id: str, status: OrderStatus, 
                             filled_quantity: int = 0, avg_fill_price: float = 0.0):
        """更新订单分片状态
        
        Args:
            fragment_id: 分片ID
            status: 新状态
            filled_quantity: 成交数量
            avg_fill_price: 平均成交价格
        """
        with self.fragment_lock:
            for fragments in self.order_fragments.values():
                for fragment in fragments:
                    if fragment.fragment_id == fragment_id:
                        fragment.status = status
                        fragment.filled_quantity = filled_quantity
                        fragment.avg_fill_price = avg_fill_price
                        fragment.update_time = datetime.now()
                        break
    
    def get_routing_statistics(self) -> Dict[str, Any]:
        """获取路由统计信息
        
        Returns:
            Dict[str, Any]: 路由统计信息
        """
        stats = self.routing_stats.copy()
        
        # 添加券商负载信息
        stats['broker_loads'] = {
            name: {
                'pending_orders': load.pending_orders,
                'processing_orders': load.processing_orders,
                'avg_response_time': load.avg_response_time,
                'success_rate': load.success_rate
            }
            for name, load in self.broker_loads.items()
        }
        
        # 添加响应时间统计
        stats['response_times'] = {
            broker: {
                'avg': sum(times) / len(times) if times else 0,
                'min': min(times) if times else 0,
                'max': max(times) if times else 0,
                'samples': len(times)
            }
            for broker, times in self.response_times.items()
        }
        
        return stats
    
    def cleanup_completed_fragments(self, max_age_hours: int = 24):
        """清理已完成的订单分片
        
        Args:
            max_age_hours: 最大保留时间（小时）
        """
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        
        with self.fragment_lock:
            to_remove = []
            
            for parent_order_id, fragments in self.order_fragments.items():
                # 检查是否所有分片都已完成且超过保留时间
                all_completed = all(
                    fragment.status in [OrderStatus.FILLED, OrderStatus.CANCELLED, OrderStatus.ERROR]
                    for fragment in fragments
                )
                
                if all_completed:
                    oldest_update = min(
                        fragment.update_time or fragment.create_time
                        for fragment in fragments
                    )
                    
                    if oldest_update < cutoff_time:
                        to_remove.append(parent_order_id)
            
            for parent_order_id in to_remove:
                del self.order_fragments[parent_order_id]
                self.logger.info(f"清理已完成的订单分片: {parent_order_id}")
    
    def __del__(self):
        """析构函数"""
        try:
            self.executor.shutdown(wait=True)
        except Exception:
            pass