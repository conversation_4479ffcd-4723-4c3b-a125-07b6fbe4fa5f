"""
模拟券商实现
用于测试和开发环境的模拟券商接口
"""

import logging
import random
import time
import uuid
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from threading import Lock

from .broker_interface import (
    BrokerInterface, Account, Position, Order, Trade, OrderSide, OrderType, OrderStatus,
    BrokerException, ConnectionException, OrderException
)


class MockBroker(BrokerInterface):
    """模拟券商实现
    
    提供完整的券商接口模拟，支持订单管理、持仓跟踪等功能
    主要用于测试和开发环境
    """
    
    def __init__(self, config: Dict[str, Any]):
        """初始化模拟券商
        
        Args:
            config: 配置信息
        """
        super().__init__(config)
        self.logger = logging.getLogger(f"{__name__}.{config.get('name', 'MockBroker')}")
        
        # 模拟配置
        self.broker_name = config.get('name', 'MockBroker')
        self.initial_cash = config.get('initial_cash', 1000000.0)  # 初始资金100万
        self.commission_rate = config.get('commission_rate', 0.0003)  # 手续费率0.03%
        self.min_commission = config.get('min_commission', 5.0)  # 最小手续费5元
        
        # 模拟延迟配置
        self.connection_delay = config.get('connection_delay', 0.1)
        self.order_delay = config.get('order_delay', 0.05)
        self.query_delay = config.get('query_delay', 0.02)
        
        # 模拟失败率
        self.connection_failure_rate = config.get('connection_failure_rate', 0.01)
        self.order_failure_rate = config.get('order_failure_rate', 0.02)
        
        # 账户信息
        self.account = Account(
            account_id=config.get('account_id', 'MOCK_ACCOUNT_001'),
            total_assets=self.initial_cash,
            available_cash=self.initial_cash,
            market_value=0.0,
            frozen_cash=0.0,
            profit_loss=0.0,
            update_time=datetime.now()
        )
        
        # 持仓信息
        self.positions: Dict[str, Position] = {}
        
        # 订单管理
        self.orders: Dict[str, Order] = {}
        self.trades: List[Trade] = []
        
        # 线程锁
        self.lock = Lock()
        
        # 模拟价格数据
        self.mock_prices: Dict[str, float] = {}
        self._initialize_mock_prices()
        
        self.logger.info(f"模拟券商 {self.broker_name} 初始化完成")
    
    def _initialize_mock_prices(self):
        """初始化模拟价格数据"""
        # 一些常见股票的模拟价格
        mock_stocks = [
            '000001.SZ', '000002.SZ', '600000.SH', '600036.SH', '000858.SZ',
            '002415.SZ', '300059.SZ', '600519.SH', '000725.SZ', '002304.SZ'
        ]
        
        for symbol in mock_stocks:
            self.mock_prices[symbol] = random.uniform(10.0, 100.0)
    
    def _simulate_delay(self, delay_type: str):
        """模拟网络延迟
        
        Args:
            delay_type: 延迟类型
        """
        if delay_type == 'connection':
            time.sleep(self.connection_delay)
        elif delay_type == 'order':
            time.sleep(self.order_delay)
        elif delay_type == 'query':
            time.sleep(self.query_delay)
    
    def _simulate_failure(self, failure_type: str) -> bool:
        """模拟操作失败
        
        Args:
            failure_type: 失败类型
            
        Returns:
            bool: 是否失败
        """
        if failure_type == 'connection':
            return random.random() < self.connection_failure_rate
        elif failure_type == 'order':
            return random.random() < self.order_failure_rate
        
        return False
    
    def _get_mock_price(self, symbol: str) -> float:
        """获取模拟价格
        
        Args:
            symbol: 股票代码
            
        Returns:
            float: 模拟价格
        """
        if symbol not in self.mock_prices:
            self.mock_prices[symbol] = random.uniform(10.0, 100.0)
        
        # 添加随机波动
        base_price = self.mock_prices[symbol]
        volatility = random.uniform(-0.02, 0.02)  # ±2%波动
        new_price = base_price * (1 + volatility)
        self.mock_prices[symbol] = max(new_price, 0.01)  # 确保价格为正
        
        return self.mock_prices[symbol]
    
    def _calculate_commission(self, quantity: int, price: float) -> float:
        """计算手续费
        
        Args:
            quantity: 数量
            price: 价格
            
        Returns:
            float: 手续费
        """
        trade_value = quantity * price
        commission = trade_value * self.commission_rate
        return max(commission, self.min_commission)
    
    def _update_account(self):
        """更新账户信息"""
        with self.lock:
            # 计算持仓市值
            total_market_value = 0.0
            for position in self.positions.values():
                current_price = self._get_mock_price(position.symbol)
                market_value = position.quantity * current_price
                total_market_value += market_value
                
                # 更新持仓信息
                position.market_price = current_price
                position.market_value = market_value
                position.unrealized_pnl = market_value - position.quantity * position.avg_cost
                if position.quantity > 0:
                    position.unrealized_pnl_pct = position.unrealized_pnl / (position.quantity * position.avg_cost)
                else:
                    position.unrealized_pnl_pct = 0.0
                position.update_time = datetime.now()
            
            # 更新账户信息
            self.account.market_value = total_market_value
            self.account.total_assets = self.account.available_cash + self.account.frozen_cash + total_market_value
            self.account.profit_loss = self.account.total_assets - self.initial_cash
            self.account.update_time = datetime.now()
    
    def connect(self) -> bool:
        """连接到模拟券商
        
        Returns:
            bool: 连接是否成功
        """
        self._simulate_delay('connection')
        
        if self._simulate_failure('connection'):
            self.logger.warning(f"模拟券商 {self.broker_name} 连接失败")
            return False
        
        self.is_connected = True
        self.last_heartbeat = datetime.now()
        self.logger.info(f"模拟券商 {self.broker_name} 连接成功")
        return True
    
    def disconnect(self) -> bool:
        """断开连接
        
        Returns:
            bool: 断开是否成功
        """
        self.is_connected = False
        self.logger.info(f"模拟券商 {self.broker_name} 断开连接")
        return True
    
    def is_market_open(self) -> bool:
        """检查市场是否开放
        
        Returns:
            bool: 市场是否开放
        """
        now = datetime.now()
        
        # 模拟A股交易时间
        # 周一到周五，上午9:30-11:30，下午13:00-15:00
        if now.weekday() >= 5:  # 周末
            return False
        
        time_str = now.strftime('%H:%M')
        morning_open = '09:30' <= time_str <= '11:30'
        afternoon_open = '13:00' <= time_str <= '15:00'
        
        return morning_open or afternoon_open
    
    def get_account_info(self) -> Account:
        """获取账户信息
        
        Returns:
            Account: 账户信息
        """
        self._simulate_delay('query')
        
        if not self.is_connected:
            raise ConnectionException("券商未连接")
        
        self._update_account()
        return self.account
    
    def get_positions(self) -> List[Position]:
        """获取持仓信息
        
        Returns:
            List[Position]: 持仓信息列表
        """
        self._simulate_delay('query')
        
        if not self.is_connected:
            raise ConnectionException("券商未连接")
        
        self._update_account()
        
        with self.lock:
            return [pos for pos in self.positions.values() if pos.quantity > 0]
    
    def get_position(self, symbol: str) -> Optional[Position]:
        """获取指定股票的持仓信息
        
        Args:
            symbol: 股票代码
            
        Returns:
            Optional[Position]: 持仓信息
        """
        self._simulate_delay('query')
        
        if not self.is_connected:
            raise ConnectionException("券商未连接")
        
        self._update_account()
        
        with self.lock:
            position = self.positions.get(symbol)
            return position if position and position.quantity > 0 else None
    
    def place_order(self, order: Order) -> str:
        """提交订单
        
        Args:
            order: 订单对象
            
        Returns:
            str: 订单ID
        """
        self._simulate_delay('order')
        
        if not self.is_connected:
            raise ConnectionException("券商未连接")
        
        if self._simulate_failure('order'):
            raise OrderException("模拟订单提交失败")
        
        # 生成订单ID
        order_id = f"MOCK_{uuid.uuid4().hex[:8].upper()}"
        order.order_id = order_id
        order.status = OrderStatus.SUBMITTED
        order.create_time = datetime.now()
        order.update_time = datetime.now()
        
        with self.lock:
            self.orders[order_id] = order
        
        # 模拟订单处理
        self._process_order_async(order)
        
        self.logger.info(f"订单提交成功: {order_id} {order.symbol} {order.side.value} {order.quantity}")
        return order_id
    
    def _process_order_async(self, order: Order):
        """异步处理订单
        
        Args:
            order: 订单对象
        """
        import threading
        
        def process_order():
            try:
                # 模拟处理延迟
                time.sleep(random.uniform(0.1, 0.5))
                
                # 获取当前价格
                current_price = self._get_mock_price(order.symbol)
                
                # 模拟成交价格
                if order.order_type == OrderType.MARKET:
                    fill_price = current_price
                else:
                    # 限价单：检查是否能成交
                    if order.side == OrderSide.BUY and order.price >= current_price:
                        fill_price = min(order.price, current_price)
                    elif order.side == OrderSide.SELL and order.price <= current_price:
                        fill_price = max(order.price, current_price)
                    else:
                        # 价格不满足，订单挂起
                        return
                
                # 检查资金和持仓
                if not self._check_order_feasibility(order, fill_price):
                    order.status = OrderStatus.REJECTED
                    order.error_msg = "资金或持仓不足"
                    order.update_time = datetime.now()
                    return
                
                # 执行成交
                self._execute_trade(order, fill_price)
                
            except Exception as e:
                order.status = OrderStatus.ERROR
                order.error_msg = str(e)
                order.update_time = datetime.now()
                self.logger.error(f"订单处理异常: {order.order_id} - {e}")
        
        thread = threading.Thread(target=process_order)
        thread.daemon = True
        thread.start()
    
    def _check_order_feasibility(self, order: Order, price: float) -> bool:
        """检查订单可行性
        
        Args:
            order: 订单对象
            price: 成交价格
            
        Returns:
            bool: 是否可行
        """
        with self.lock:
            if order.side == OrderSide.BUY:
                # 买入：检查资金
                required_cash = order.quantity * price
                commission = self._calculate_commission(order.quantity, price)
                total_required = required_cash + commission
                
                return self.account.available_cash >= total_required
            
            else:
                # 卖出：检查持仓
                position = self.positions.get(order.symbol)
                if not position:
                    return False
                
                return position.available_quantity >= order.quantity
    
    def _execute_trade(self, order: Order, fill_price: float):
        """执行成交
        
        Args:
            order: 订单对象
            fill_price: 成交价格
        """
        with self.lock:
            commission = self._calculate_commission(order.quantity, fill_price)
            
            # 创建成交记录
            trade = Trade(
                trade_id=f"TRADE_{uuid.uuid4().hex[:8].upper()}",
                order_id=order.order_id,
                symbol=order.symbol,
                side=order.side,
                quantity=order.quantity,
                price=fill_price,
                commission=commission,
                trade_time=datetime.now()
            )
            
            self.trades.append(trade)
            
            # 更新订单状态
            order.status = OrderStatus.FILLED
            order.filled_quantity = order.quantity
            order.avg_fill_price = fill_price
            order.commission = commission
            order.update_time = datetime.now()
            
            # 更新账户和持仓
            if order.side == OrderSide.BUY:
                self._update_position_buy(order.symbol, order.quantity, fill_price)
                self.account.available_cash -= (order.quantity * fill_price + commission)
            else:
                self._update_position_sell(order.symbol, order.quantity, fill_price)
                self.account.available_cash += (order.quantity * fill_price - commission)
            
            self.logger.info(f"订单成交: {order.order_id} 价格: {fill_price} 手续费: {commission}")
    
    def _update_position_buy(self, symbol: str, quantity: int, price: float):
        """更新买入持仓
        
        Args:
            symbol: 股票代码
            quantity: 数量
            price: 价格
        """
        if symbol not in self.positions:
            self.positions[symbol] = Position(
                symbol=symbol,
                quantity=0,
                available_quantity=0,
                avg_cost=0.0,
                market_price=price,
                market_value=0.0,
                unrealized_pnl=0.0,
                unrealized_pnl_pct=0.0,
                update_time=datetime.now()
            )
        
        position = self.positions[symbol]
        
        # 计算新的平均成本
        total_cost = position.quantity * position.avg_cost + quantity * price
        total_quantity = position.quantity + quantity
        
        position.avg_cost = total_cost / total_quantity if total_quantity > 0 else 0
        position.quantity = total_quantity
        position.available_quantity = total_quantity  # 简化：假设T+1后可用
        position.market_price = price
        position.market_value = total_quantity * price
        position.update_time = datetime.now()
    
    def _update_position_sell(self, symbol: str, quantity: int, price: float):
        """更新卖出持仓
        
        Args:
            symbol: 股票代码
            quantity: 数量
            price: 价格
        """
        if symbol in self.positions:
            position = self.positions[symbol]
            position.quantity -= quantity
            position.available_quantity -= quantity
            position.market_price = price
            position.market_value = position.quantity * price
            position.update_time = datetime.now()
            
            # 如果持仓为0，保留记录但数量为0
            if position.quantity <= 0:
                position.quantity = 0
                position.available_quantity = 0
                position.market_value = 0.0
    
    def cancel_order(self, order_id: str) -> bool:
        """撤销订单
        
        Args:
            order_id: 订单ID
            
        Returns:
            bool: 撤销是否成功
        """
        self._simulate_delay('order')
        
        if not self.is_connected:
            raise ConnectionException("券商未连接")
        
        with self.lock:
            order = self.orders.get(order_id)
            if not order:
                return False
            
            if order.status in [OrderStatus.FILLED, OrderStatus.CANCELLED]:
                return False
            
            order.status = OrderStatus.CANCELLED
            order.update_time = datetime.now()
            
            self.logger.info(f"订单撤销成功: {order_id}")
            return True
    
    def get_order_status(self, order_id: str) -> Optional[Order]:
        """查询订单状态
        
        Args:
            order_id: 订单ID
            
        Returns:
            Optional[Order]: 订单信息
        """
        self._simulate_delay('query')
        
        if not self.is_connected:
            raise ConnectionException("券商未连接")
        
        with self.lock:
            return self.orders.get(order_id)
    
    def get_orders(self, symbol: Optional[str] = None) -> List[Order]:
        """获取订单列表
        
        Args:
            symbol: 可选的股票代码过滤
            
        Returns:
            List[Order]: 订单列表
        """
        self._simulate_delay('query')
        
        if not self.is_connected:
            raise ConnectionException("券商未连接")
        
        with self.lock:
            orders = list(self.orders.values())
            
            if symbol:
                orders = [order for order in orders if order.symbol == symbol]
            
            # 按创建时间倒序排列
            orders.sort(key=lambda x: x.create_time or datetime.min, reverse=True)
            
            return orders
    
    def get_trades(self, symbol: Optional[str] = None) -> List[Trade]:
        """获取成交记录
        
        Args:
            symbol: 可选的股票代码过滤
            
        Returns:
            List[Trade]: 成交记录列表
        """
        self._simulate_delay('query')
        
        if not self.is_connected:
            raise ConnectionException("券商未连接")
        
        with self.lock:
            trades = self.trades.copy()
            
            if symbol:
                trades = [trade for trade in trades if trade.symbol == symbol]
            
            # 按成交时间倒序排列
            trades.sort(key=lambda x: x.trade_time, reverse=True)
            
            return trades
    
    def heartbeat(self) -> bool:
        """心跳检测
        
        Returns:
            bool: 连接是否正常
        """
        if not self.is_connected:
            return False
        
        try:
            self._simulate_delay('query')
            
            if self._simulate_failure('connection'):
                self.is_connected = False
                return False
            
            self.last_heartbeat = datetime.now()
            return True
            
        except Exception:
            self.is_connected = False
            return False
    
    def get_mock_statistics(self) -> Dict[str, Any]:
        """获取模拟统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        with self.lock:
            return {
                'broker_name': self.broker_name,
                'total_orders': len(self.orders),
                'total_trades': len(self.trades),
                'positions_count': len([p for p in self.positions.values() if p.quantity > 0]),
                'account_info': {
                    'total_assets': self.account.total_assets,
                    'available_cash': self.account.available_cash,
                    'market_value': self.account.market_value,
                    'profit_loss': self.account.profit_loss
                },
                'order_status_distribution': self._get_order_status_distribution(),
                'mock_prices_count': len(self.mock_prices)
            }
    
    def _get_order_status_distribution(self) -> Dict[str, int]:
        """获取订单状态分布
        
        Returns:
            Dict[str, int]: 状态分布
        """
        distribution = {}
        for order in self.orders.values():
            status = order.status.value
            distribution[status] = distribution.get(status, 0) + 1
        return distribution