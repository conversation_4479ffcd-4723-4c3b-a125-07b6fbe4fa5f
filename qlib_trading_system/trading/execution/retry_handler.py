"""
交易接口容错和重试逻辑模块
实现智能重试、熔断机制和故障恢复
"""

import logging
import time
import threading
from typing import Dict, List, Optional, Any, Callable, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import random
import functools

from .broker_interface import BrokerException, ConnectionException, OrderException


class RetryStrategy(Enum):
    """重试策略枚举"""
    FIXED_DELAY = "FIXED_DELAY"          # 固定延迟
    EXPONENTIAL_BACKOFF = "EXPONENTIAL_BACKOFF"  # 指数退避
    LINEAR_BACKOFF = "LINEAR_BACKOFF"    # 线性退避
    RANDOM_JITTER = "RANDOM_JITTER"      # 随机抖动


class CircuitState(Enum):
    """熔断器状态枚举"""
    CLOSED = "CLOSED"      # 关闭状态（正常）
    OPEN = "OPEN"          # 开启状态（熔断）
    HALF_OPEN = "HALF_OPEN"  # 半开状态（试探）


@dataclass
class RetryConfig:
    """重试配置数据类"""
    max_attempts: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    strategy: RetryStrategy = RetryStrategy.EXPONENTIAL_BACKOFF
    jitter: bool = True
    backoff_multiplier: float = 2.0
    
    # 异常类型配置
    retryable_exceptions: List[type] = field(default_factory=lambda: [
        ConnectionException, BrokerException
    ])
    non_retryable_exceptions: List[type] = field(default_factory=lambda: [
        OrderException  # 订单异常通常不应重试
    ])


@dataclass
class CircuitBreakerConfig:
    """熔断器配置数据类"""
    failure_threshold: int = 5      # 失败阈值
    success_threshold: int = 3      # 成功阈值（半开状态）
    timeout: float = 60.0          # 熔断超时时间（秒）
    
    # 监控窗口
    window_size: int = 100         # 监控窗口大小
    min_requests: int = 10         # 最小请求数


@dataclass
class OperationResult:
    """操作结果数据类"""
    success: bool = False
    result: Any = None
    exception: Optional[Exception] = None
    attempts: int = 0
    total_time: float = 0.0
    retry_delays: List[float] = field(default_factory=list)


class CircuitBreaker:
    """熔断器实现
    
    提供熔断保护，防止系统在故障时继续尝试失败的操作
    """
    
    def __init__(self, name: str, config: CircuitBreakerConfig):
        """初始化熔断器
        
        Args:
            name: 熔断器名称
            config: 熔断器配置
        """
        self.name = name
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.{name}")
        
        # 状态管理
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time: Optional[datetime] = None
        self.state_change_time = datetime.now()
        
        # 统计信息
        self.request_history: List[bool] = []  # True=成功, False=失败
        self.total_requests = 0
        self.total_failures = 0
        
        # 线程锁
        self.lock = threading.Lock()
    
    def call(self, func: Callable, *args, **kwargs) -> Any:
        """通过熔断器调用函数
        
        Args:
            func: 要调用的函数
            *args: 函数参数
            **kwargs: 函数关键字参数
            
        Returns:
            函数返回值
            
        Raises:
            Exception: 如果熔断器开启或函数执行失败
        """
        with self.lock:
            self.total_requests += 1
            
            # 检查熔断器状态
            if self.state == CircuitState.OPEN:
                if self._should_attempt_reset():
                    self._transition_to_half_open()
                else:
                    raise BrokerException(f"熔断器 {self.name} 处于开启状态")
            
            try:
                # 执行函数
                result = func(*args, **kwargs)
                self._on_success()
                return result
                
            except Exception as e:
                self._on_failure()
                raise
    
    def _should_attempt_reset(self) -> bool:
        """判断是否应该尝试重置熔断器
        
        Returns:
            bool: 是否应该重置
        """
        if not self.last_failure_time:
            return True
        
        elapsed = (datetime.now() - self.last_failure_time).total_seconds()
        return elapsed >= self.config.timeout
    
    def _transition_to_half_open(self):
        """转换到半开状态"""
        self.state = CircuitState.HALF_OPEN
        self.success_count = 0
        self.state_change_time = datetime.now()
        self.logger.info(f"熔断器 {self.name} 转换到半开状态")
    
    def _on_success(self):
        """处理成功调用"""
        self.request_history.append(True)
        self._trim_history()
        
        if self.state == CircuitState.HALF_OPEN:
            self.success_count += 1
            if self.success_count >= self.config.success_threshold:
                self._transition_to_closed()
        elif self.state == CircuitState.CLOSED:
            self.failure_count = 0  # 重置失败计数
    
    def _on_failure(self):
        """处理失败调用"""
        self.request_history.append(False)
        self._trim_history()
        self.total_failures += 1
        self.last_failure_time = datetime.now()
        
        if self.state == CircuitState.HALF_OPEN:
            self._transition_to_open()
        elif self.state == CircuitState.CLOSED:
            self.failure_count += 1
            if self._should_trip():
                self._transition_to_open()
    
    def _should_trip(self) -> bool:
        """判断是否应该触发熔断
        
        Returns:
            bool: 是否应该熔断
        """
        # 检查失败次数
        if self.failure_count >= self.config.failure_threshold:
            return True
        
        # 检查失败率（如果有足够的请求样本）
        if len(self.request_history) >= self.config.min_requests:
            failure_rate = self.request_history.count(False) / len(self.request_history)
            return failure_rate >= 0.5  # 50%失败率触发熔断
        
        return False
    
    def _transition_to_open(self):
        """转换到开启状态"""
        self.state = CircuitState.OPEN
        self.state_change_time = datetime.now()
        self.logger.warning(f"熔断器 {self.name} 触发熔断")
    
    def _transition_to_closed(self):
        """转换到关闭状态"""
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.state_change_time = datetime.now()
        self.logger.info(f"熔断器 {self.name} 恢复正常")
    
    def _trim_history(self):
        """修剪历史记录"""
        if len(self.request_history) > self.config.window_size:
            self.request_history = self.request_history[-self.config.window_size:]
    
    def get_status(self) -> Dict[str, Any]:
        """获取熔断器状态
        
        Returns:
            Dict[str, Any]: 状态信息
        """
        with self.lock:
            failure_rate = 0.0
            if self.request_history:
                failure_rate = self.request_history.count(False) / len(self.request_history)
            
            return {
                'name': self.name,
                'state': self.state.value,
                'failure_count': self.failure_count,
                'success_count': self.success_count,
                'total_requests': self.total_requests,
                'total_failures': self.total_failures,
                'failure_rate': failure_rate,
                'last_failure_time': self.last_failure_time.isoformat() if self.last_failure_time else None,
                'state_change_time': self.state_change_time.isoformat(),
                'uptime_seconds': (datetime.now() - self.state_change_time).total_seconds()
            }
    
    def reset(self):
        """重置熔断器"""
        with self.lock:
            self.state = CircuitState.CLOSED
            self.failure_count = 0
            self.success_count = 0
            self.request_history.clear()
            self.state_change_time = datetime.now()
            self.logger.info(f"熔断器 {self.name} 已重置")


class RetryHandler:
    """重试处理器
    
    提供智能重试机制，支持多种重试策略和熔断保护
    """
    
    def __init__(self, config: Dict[str, Any]):
        """初始化重试处理器
        
        Args:
            config: 配置信息
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 默认重试配置
        self.default_retry_config = RetryConfig(
            max_attempts=config.get('max_attempts', 3),
            base_delay=config.get('base_delay', 1.0),
            max_delay=config.get('max_delay', 60.0),
            strategy=RetryStrategy(config.get('strategy', 'EXPONENTIAL_BACKOFF')),
            jitter=config.get('jitter', True),
            backoff_multiplier=config.get('backoff_multiplier', 2.0)
        )
        
        # 熔断器管理
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self.circuit_breaker_config = CircuitBreakerConfig(
            failure_threshold=config.get('circuit_failure_threshold', 5),
            success_threshold=config.get('circuit_success_threshold', 3),
            timeout=config.get('circuit_timeout', 60.0),
            window_size=config.get('circuit_window_size', 100),
            min_requests=config.get('circuit_min_requests', 10)
        )
        
        # 统计信息
        self.stats = {
            'total_operations': 0,
            'successful_operations': 0,
            'failed_operations': 0,
            'total_retries': 0,
            'circuit_breaker_trips': 0
        }
    
    def get_circuit_breaker(self, name: str) -> CircuitBreaker:
        """获取或创建熔断器
        
        Args:
            name: 熔断器名称
            
        Returns:
            CircuitBreaker: 熔断器实例
        """
        if name not in self.circuit_breakers:
            self.circuit_breakers[name] = CircuitBreaker(name, self.circuit_breaker_config)
        
        return self.circuit_breakers[name]
    
    def execute_with_retry(self, func: Callable, *args, 
                          retry_config: Optional[RetryConfig] = None,
                          circuit_breaker_name: Optional[str] = None,
                          **kwargs) -> OperationResult:
        """执行带重试的操作
        
        Args:
            func: 要执行的函数
            *args: 函数参数
            retry_config: 重试配置
            circuit_breaker_name: 熔断器名称
            **kwargs: 函数关键字参数
            
        Returns:
            OperationResult: 操作结果
        """
        config = retry_config or self.default_retry_config
        start_time = time.time()
        
        self.stats['total_operations'] += 1
        
        result = OperationResult()
        last_exception = None
        
        for attempt in range(config.max_attempts):
            try:
                result.attempts = attempt + 1
                
                # 使用熔断器（如果指定）
                if circuit_breaker_name:
                    circuit_breaker = self.get_circuit_breaker(circuit_breaker_name)
                    operation_result = circuit_breaker.call(func, *args, **kwargs)
                else:
                    operation_result = func(*args, **kwargs)
                
                # 成功
                result.success = True
                result.result = operation_result
                result.total_time = time.time() - start_time
                
                self.stats['successful_operations'] += 1
                
                if attempt > 0:
                    self.logger.info(f"操作在第 {attempt + 1} 次尝试后成功")
                
                return result
                
            except Exception as e:
                last_exception = e
                
                # 检查是否应该重试
                if not self._should_retry(e, config, attempt):
                    break
                
                # 计算延迟时间
                if attempt < config.max_attempts - 1:  # 不是最后一次尝试
                    delay = self._calculate_delay(attempt, config)
                    result.retry_delays.append(delay)
                    
                    self.stats['total_retries'] += 1
                    
                    self.logger.warning(
                        f"操作失败，{delay:.2f}秒后进行第 {attempt + 2} 次尝试: {e}"
                    )
                    
                    time.sleep(delay)
        
        # 所有重试都失败
        result.success = False
        result.exception = last_exception
        result.total_time = time.time() - start_time
        
        self.stats['failed_operations'] += 1
        
        self.logger.error(f"操作在 {config.max_attempts} 次尝试后仍然失败: {last_exception}")
        
        return result
    
    def _should_retry(self, exception: Exception, config: RetryConfig, attempt: int) -> bool:
        """判断是否应该重试
        
        Args:
            exception: 异常对象
            config: 重试配置
            attempt: 当前尝试次数
            
        Returns:
            bool: 是否应该重试
        """
        # 检查是否达到最大尝试次数
        if attempt >= config.max_attempts - 1:
            return False
        
        # 检查异常类型
        exception_type = type(exception)
        
        # 明确不可重试的异常
        for non_retryable in config.non_retryable_exceptions:
            if issubclass(exception_type, non_retryable):
                self.logger.debug(f"异常类型 {exception_type.__name__} 不可重试")
                return False
        
        # 明确可重试的异常
        for retryable in config.retryable_exceptions:
            if issubclass(exception_type, retryable):
                return True
        
        # 默认不重试未知异常类型
        self.logger.debug(f"未知异常类型 {exception_type.__name__}，不重试")
        return False
    
    def _calculate_delay(self, attempt: int, config: RetryConfig) -> float:
        """计算重试延迟时间
        
        Args:
            attempt: 当前尝试次数（从0开始）
            config: 重试配置
            
        Returns:
            float: 延迟时间（秒）
        """
        if config.strategy == RetryStrategy.FIXED_DELAY:
            delay = config.base_delay
        
        elif config.strategy == RetryStrategy.EXPONENTIAL_BACKOFF:
            delay = config.base_delay * (config.backoff_multiplier ** attempt)
        
        elif config.strategy == RetryStrategy.LINEAR_BACKOFF:
            delay = config.base_delay * (attempt + 1)
        
        elif config.strategy == RetryStrategy.RANDOM_JITTER:
            delay = config.base_delay + random.uniform(0, config.base_delay)
        
        else:
            delay = config.base_delay
        
        # 限制最大延迟
        delay = min(delay, config.max_delay)
        
        # 添加抖动（如果启用）
        if config.jitter and config.strategy != RetryStrategy.RANDOM_JITTER:
            jitter = delay * 0.1 * random.uniform(-1, 1)
            delay = max(0, delay + jitter)
        
        return delay
    
    def retry_decorator(self, retry_config: Optional[RetryConfig] = None,
                       circuit_breaker_name: Optional[str] = None):
        """重试装饰器
        
        Args:
            retry_config: 重试配置
            circuit_breaker_name: 熔断器名称
            
        Returns:
            装饰器函数
        """
        def decorator(func):
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                result = self.execute_with_retry(
                    func, *args,
                    retry_config=retry_config,
                    circuit_breaker_name=circuit_breaker_name,
                    **kwargs
                )
                
                if result.success:
                    return result.result
                else:
                    raise result.exception
            
            return wrapper
        return decorator
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        stats = self.stats.copy()
        
        # 计算成功率
        if stats['total_operations'] > 0:
            stats['success_rate'] = stats['successful_operations'] / stats['total_operations']
        else:
            stats['success_rate'] = 0.0
        
        # 计算平均重试次数
        if stats['successful_operations'] > 0:
            stats['avg_retries'] = stats['total_retries'] / stats['successful_operations']
        else:
            stats['avg_retries'] = 0.0
        
        # 熔断器状态
        stats['circuit_breakers'] = {
            name: breaker.get_status()
            for name, breaker in self.circuit_breakers.items()
        }
        
        return stats
    
    def reset_statistics(self):
        """重置统计信息"""
        self.stats = {
            'total_operations': 0,
            'successful_operations': 0,
            'failed_operations': 0,
            'total_retries': 0,
            'circuit_breaker_trips': 0
        }
        
        # 重置所有熔断器
        for breaker in self.circuit_breakers.values():
            breaker.reset()
        
        self.logger.info("重试处理器统计信息已重置")
    
    def create_retry_config(self, **kwargs) -> RetryConfig:
        """创建重试配置
        
        Args:
            **kwargs: 配置参数
            
        Returns:
            RetryConfig: 重试配置对象
        """
        config_dict = {
            'max_attempts': kwargs.get('max_attempts', self.default_retry_config.max_attempts),
            'base_delay': kwargs.get('base_delay', self.default_retry_config.base_delay),
            'max_delay': kwargs.get('max_delay', self.default_retry_config.max_delay),
            'strategy': RetryStrategy(kwargs.get('strategy', self.default_retry_config.strategy.value)),
            'jitter': kwargs.get('jitter', self.default_retry_config.jitter),
            'backoff_multiplier': kwargs.get('backoff_multiplier', self.default_retry_config.backoff_multiplier)
        }
        
        return RetryConfig(**config_dict)


# 全局重试处理器实例
_global_retry_handler: Optional[RetryHandler] = None


def get_retry_handler(config: Optional[Dict[str, Any]] = None) -> RetryHandler:
    """获取全局重试处理器实例
    
    Args:
        config: 配置信息（仅在首次调用时使用）
        
    Returns:
        RetryHandler: 重试处理器实例
    """
    global _global_retry_handler
    
    if _global_retry_handler is None:
        _global_retry_handler = RetryHandler(config or {})
    
    return _global_retry_handler


def with_retry(retry_config: Optional[RetryConfig] = None,
               circuit_breaker_name: Optional[str] = None):
    """重试装饰器的便捷函数
    
    Args:
        retry_config: 重试配置
        circuit_breaker_name: 熔断器名称
        
    Returns:
        装饰器函数
    """
    handler = get_retry_handler()
    return handler.retry_decorator(retry_config, circuit_breaker_name)