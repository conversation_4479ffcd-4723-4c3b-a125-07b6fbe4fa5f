"""
券商接口基础定义模块
定义统一的券商接口规范，支持多券商适配
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
import time
from datetime import datetime


class OrderSide(Enum):
    """订单方向枚举"""
    BUY = "BUY"
    SELL = "SELL"


class OrderType(Enum):
    """订单类型枚举"""
    MARKET = "MARKET"  # 市价单
    LIMIT = "LIMIT"    # 限价单


class OrderStatus(Enum):
    """订单状态枚举"""
    PENDING = "PENDING"        # 待提交
    SUBMITTED = "SUBMITTED"    # 已提交
    PARTIAL_FILLED = "PARTIAL_FILLED"  # 部分成交
    FILLED = "FILLED"          # 完全成交
    CANCELLED = "CANCELLED"    # 已撤销
    REJECTED = "REJECTED"      # 被拒绝
    ERROR = "ERROR"            # 错误状态


@dataclass
class Account:
    """账户信息数据类"""
    account_id: str
    total_assets: float      # 总资产
    available_cash: float    # 可用资金
    market_value: float      # 持仓市值
    frozen_cash: float       # 冻结资金
    profit_loss: float       # 盈亏
    update_time: datetime


@dataclass
class Position:
    """持仓信息数据类"""
    symbol: str
    quantity: int            # 持仓数量
    available_quantity: int  # 可用数量
    avg_cost: float         # 平均成本
    market_price: float     # 市场价格
    market_value: float     # 市值
    unrealized_pnl: float   # 未实现盈亏
    unrealized_pnl_pct: float  # 未实现盈亏百分比
    update_time: datetime


@dataclass
class Order:
    """订单信息数据类"""
    order_id: str
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: int
    price: Optional[float] = None
    status: OrderStatus = OrderStatus.PENDING
    filled_quantity: int = 0
    avg_fill_price: float = 0.0
    commission: float = 0.0
    create_time: Optional[datetime] = None
    update_time: Optional[datetime] = None
    error_msg: Optional[str] = None


@dataclass
class Trade:
    """成交记录数据类"""
    trade_id: str
    order_id: str
    symbol: str
    side: OrderSide
    quantity: int
    price: float
    commission: float
    trade_time: datetime


class BrokerInterface(ABC):
    """券商接口抽象基类
    
    定义所有券商适配器必须实现的标准接口
    """
    
    def __init__(self, config: Dict[str, Any]):
        """初始化券商接口
        
        Args:
            config: 券商配置信息
        """
        self.config = config
        self.is_connected = False
        self.last_heartbeat = None
        
    @abstractmethod
    def connect(self) -> bool:
        """连接到券商服务器
        
        Returns:
            bool: 连接是否成功
        """
        pass
    
    @abstractmethod
    def disconnect(self) -> bool:
        """断开券商连接
        
        Returns:
            bool: 断开是否成功
        """
        pass
    
    @abstractmethod
    def is_market_open(self) -> bool:
        """检查市场是否开放
        
        Returns:
            bool: 市场是否开放
        """
        pass
    
    @abstractmethod
    def get_account_info(self) -> Account:
        """获取账户信息
        
        Returns:
            Account: 账户信息对象
        """
        pass
    
    @abstractmethod
    def get_positions(self) -> List[Position]:
        """获取持仓信息
        
        Returns:
            List[Position]: 持仓信息列表
        """
        pass
    
    @abstractmethod
    def get_position(self, symbol: str) -> Optional[Position]:
        """获取指定股票的持仓信息
        
        Args:
            symbol: 股票代码
            
        Returns:
            Optional[Position]: 持仓信息，如果没有持仓则返回None
        """
        pass
    
    @abstractmethod
    def place_order(self, order: Order) -> str:
        """提交订单
        
        Args:
            order: 订单对象
            
        Returns:
            str: 订单ID
            
        Raises:
            BrokerException: 订单提交失败时抛出
        """
        pass
    
    @abstractmethod
    def cancel_order(self, order_id: str) -> bool:
        """撤销订单
        
        Args:
            order_id: 订单ID
            
        Returns:
            bool: 撤销是否成功
        """
        pass
    
    @abstractmethod
    def get_order_status(self, order_id: str) -> Optional[Order]:
        """查询订单状态
        
        Args:
            order_id: 订单ID
            
        Returns:
            Optional[Order]: 订单信息，如果订单不存在则返回None
        """
        pass
    
    @abstractmethod
    def get_orders(self, symbol: Optional[str] = None) -> List[Order]:
        """获取订单列表
        
        Args:
            symbol: 可选的股票代码过滤
            
        Returns:
            List[Order]: 订单列表
        """
        pass
    
    @abstractmethod
    def get_trades(self, symbol: Optional[str] = None) -> List[Trade]:
        """获取成交记录
        
        Args:
            symbol: 可选的股票代码过滤
            
        Returns:
            List[Trade]: 成交记录列表
        """
        pass
    
    def heartbeat(self) -> bool:
        """心跳检测
        
        Returns:
            bool: 连接是否正常
        """
        try:
            # 默认实现：尝试获取账户信息来检测连接状态
            self.get_account_info()
            self.last_heartbeat = datetime.now()
            return True
        except Exception:
            self.is_connected = False
            return False
    
    def place_market_order(self, symbol: str, quantity: int, side: OrderSide) -> str:
        """提交市价单的便捷方法
        
        Args:
            symbol: 股票代码
            quantity: 数量
            side: 买卖方向
            
        Returns:
            str: 订单ID
        """
        order = Order(
            order_id="",  # 将由券商分配
            symbol=symbol,
            side=side,
            order_type=OrderType.MARKET,
            quantity=quantity,
            create_time=datetime.now()
        )
        return self.place_order(order)
    
    def place_limit_order(self, symbol: str, quantity: int, price: float, side: OrderSide) -> str:
        """提交限价单的便捷方法
        
        Args:
            symbol: 股票代码
            quantity: 数量
            price: 价格
            side: 买卖方向
            
        Returns:
            str: 订单ID
        """
        order = Order(
            order_id="",  # 将由券商分配
            symbol=symbol,
            side=side,
            order_type=OrderType.LIMIT,
            quantity=quantity,
            price=price,
            create_time=datetime.now()
        )
        return self.place_order(order)


class BrokerException(Exception):
    """券商接口异常类"""
    
    def __init__(self, message: str, error_code: Optional[str] = None):
        super().__init__(message)
        self.error_code = error_code
        self.timestamp = datetime.now()


class ConnectionException(BrokerException):
    """连接异常"""
    pass


class OrderException(BrokerException):
    """订单异常"""
    pass


class AuthenticationException(BrokerException):
    """认证异常"""
    pass