"""
券商适配器管理模块
实现多券商API统一接口封装和管理
"""

import logging
import threading
import time
from typing import Dict, List, Optional, Any, Type
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, Future
import json

from .broker_interface import (
    BrokerInterface, Account, Position, Order, Trade, OrderSide, OrderType, OrderStatus,
    BrokerException, ConnectionException, OrderException
)


class BrokerAdapter:
    """券商适配器管理器
    
    负责管理多个券商接口，提供统一的交易接口
    支持负载均衡、容错和自动重试机制
    """
    
    def __init__(self, config: Dict[str, Any]):
        """初始化券商适配器
        
        Args:
            config: 配置信息，包含券商列表和相关参数
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 券商接口实例字典
        self.brokers: Dict[str, BrokerInterface] = {}
        
        # 主要券商和备用券商
        self.primary_broker: Optional[str] = None
        self.backup_brokers: List[str] = []
        
        # 负载均衡相关
        self.broker_weights: Dict[str, float] = {}
        self.broker_health: Dict[str, bool] = {}
        self.last_health_check: Dict[str, datetime] = {}
        
        # 重试配置
        self.max_retries = config.get('max_retries', 3)
        self.retry_delay = config.get('retry_delay', 1.0)
        self.health_check_interval = config.get('health_check_interval', 30)
        
        # 线程池用于并发操作
        self.executor = ThreadPoolExecutor(max_workers=5, thread_name_prefix="BrokerAdapter")
        
        # 监控统计
        self.stats = {
            'total_orders': 0,
            'successful_orders': 0,
            'failed_orders': 0,
            'retry_count': 0,
            'broker_switches': 0
        }
        
        # 初始化券商接口
        self._initialize_brokers()
        
        # 启动健康检查线程
        self._start_health_monitor()
    
    def _initialize_brokers(self):
        """初始化所有配置的券商接口"""
        brokers_config = self.config.get('brokers', {})
        
        for broker_name, broker_config in brokers_config.items():
            try:
                # 动态加载券商实现类
                broker_class = self._load_broker_class(broker_config['class'])
                broker_instance = broker_class(broker_config)
                
                self.brokers[broker_name] = broker_instance
                self.broker_weights[broker_name] = broker_config.get('weight', 1.0)
                self.broker_health[broker_name] = False
                
                # 尝试连接
                if broker_instance.connect():
                    self.broker_health[broker_name] = True
                    self.logger.info(f"券商 {broker_name} 连接成功")
                    
                    # 设置主要券商
                    if broker_config.get('primary', False):
                        self.primary_broker = broker_name
                    else:
                        self.backup_brokers.append(broker_name)
                else:
                    self.logger.warning(f"券商 {broker_name} 连接失败")
                    
            except Exception as e:
                self.logger.error(f"初始化券商 {broker_name} 失败: {e}")
        
        # 如果没有指定主要券商，选择第一个健康的券商
        if not self.primary_broker:
            for broker_name, is_healthy in self.broker_health.items():
                if is_healthy:
                    self.primary_broker = broker_name
                    break
        
        self.logger.info(f"主要券商: {self.primary_broker}, 备用券商: {self.backup_brokers}")
    
    def _load_broker_class(self, class_path: str) -> Type[BrokerInterface]:
        """动态加载券商实现类
        
        Args:
            class_path: 类路径，格式如 "module.ClassName"
            
        Returns:
            Type[BrokerInterface]: 券商接口类
        """
        module_path, class_name = class_path.rsplit('.', 1)
        module = __import__(module_path, fromlist=[class_name])
        return getattr(module, class_name)
    
    def _start_health_monitor(self):
        """启动健康检查监控线程"""
        def health_check_worker():
            while True:
                try:
                    self._check_all_brokers_health()
                    time.sleep(self.health_check_interval)
                except Exception as e:
                    self.logger.error(f"健康检查异常: {e}")
                    time.sleep(5)
        
        health_thread = threading.Thread(target=health_check_worker, daemon=True)
        health_thread.start()
        self.logger.info("健康检查监控线程已启动")
    
    def _check_all_brokers_health(self):
        """检查所有券商的健康状态"""
        for broker_name, broker in self.brokers.items():
            try:
                is_healthy = broker.heartbeat()
                old_health = self.broker_health[broker_name]
                self.broker_health[broker_name] = is_healthy
                self.last_health_check[broker_name] = datetime.now()
                
                # 记录状态变化
                if old_health != is_healthy:
                    status = "健康" if is_healthy else "异常"
                    self.logger.info(f"券商 {broker_name} 状态变更为: {status}")
                    
            except Exception as e:
                self.broker_health[broker_name] = False
                self.logger.warning(f"券商 {broker_name} 健康检查失败: {e}")
    
    def _get_available_broker(self, exclude: Optional[List[str]] = None) -> Optional[str]:
        """获取可用的券商
        
        Args:
            exclude: 要排除的券商列表
            
        Returns:
            Optional[str]: 可用券商名称，如果没有可用券商则返回None
        """
        exclude = exclude or []
        
        # 优先使用主要券商
        if (self.primary_broker and 
            self.primary_broker not in exclude and 
            self.broker_health.get(self.primary_broker, False)):
            return self.primary_broker
        
        # 使用备用券商，按权重排序
        available_brokers = [
            (name, weight) for name, weight in self.broker_weights.items()
            if (name not in exclude and 
                self.broker_health.get(name, False))
        ]
        
        if available_brokers:
            # 按权重降序排序
            available_brokers.sort(key=lambda x: x[1], reverse=True)
            return available_brokers[0][0]
        
        return None
    
    def _execute_with_retry(self, operation: str, func, *args, **kwargs):
        """带重试机制的操作执行
        
        Args:
            operation: 操作名称
            func: 要执行的函数
            *args: 函数参数
            **kwargs: 函数关键字参数
            
        Returns:
            操作结果
            
        Raises:
            BrokerException: 所有重试都失败时抛出
        """
        last_exception = None
        tried_brokers = []
        
        for attempt in range(self.max_retries):
            try:
                # 获取可用券商
                broker_name = self._get_available_broker(exclude=tried_brokers)
                if not broker_name:
                    raise BrokerException("没有可用的券商接口")
                
                broker = self.brokers[broker_name]
                tried_brokers.append(broker_name)
                
                # 执行操作
                result = func(broker, *args, **kwargs)
                
                # 记录成功统计
                if operation == 'place_order':
                    self.stats['successful_orders'] += 1
                
                return result
                
            except Exception as e:
                last_exception = e
                self.stats['retry_count'] += 1
                
                self.logger.warning(
                    f"{operation} 第 {attempt + 1} 次尝试失败 (券商: {broker_name}): {e}"
                )
                
                # 标记券商为不健康
                if broker_name in self.broker_health:
                    self.broker_health[broker_name] = False
                
                # 如果不是最后一次尝试，等待后重试
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay * (attempt + 1))
        
        # 所有重试都失败
        if operation == 'place_order':
            self.stats['failed_orders'] += 1
        
        raise BrokerException(f"{operation} 操作失败，已重试 {self.max_retries} 次") from last_exception
    
    def connect(self) -> bool:
        """连接所有券商
        
        Returns:
            bool: 是否至少有一个券商连接成功
        """
        success_count = 0
        
        for broker_name, broker in self.brokers.items():
            try:
                if broker.connect():
                    self.broker_health[broker_name] = True
                    success_count += 1
                    self.logger.info(f"券商 {broker_name} 连接成功")
                else:
                    self.broker_health[broker_name] = False
                    self.logger.warning(f"券商 {broker_name} 连接失败")
            except Exception as e:
                self.broker_health[broker_name] = False
                self.logger.error(f"券商 {broker_name} 连接异常: {e}")
        
        return success_count > 0
    
    def disconnect(self) -> bool:
        """断开所有券商连接
        
        Returns:
            bool: 断开是否成功
        """
        success_count = 0
        
        for broker_name, broker in self.brokers.items():
            try:
                if broker.disconnect():
                    success_count += 1
                    self.logger.info(f"券商 {broker_name} 断开连接成功")
            except Exception as e:
                self.logger.error(f"券商 {broker_name} 断开连接失败: {e}")
        
        # 关闭线程池
        self.executor.shutdown(wait=True)
        
        return success_count == len(self.brokers)
    
    def is_market_open(self) -> bool:
        """检查市场是否开放
        
        Returns:
            bool: 市场是否开放
        """
        def _check_market(broker):
            return broker.is_market_open()
        
        try:
            return self._execute_with_retry('is_market_open', _check_market)
        except Exception as e:
            self.logger.error(f"检查市场状态失败: {e}")
            return False
    
    def get_account_info(self) -> Account:
        """获取账户信息
        
        Returns:
            Account: 账户信息
        """
        def _get_account(broker):
            return broker.get_account_info()
        
        return self._execute_with_retry('get_account_info', _get_account)
    
    def get_positions(self) -> List[Position]:
        """获取持仓信息
        
        Returns:
            List[Position]: 持仓信息列表
        """
        def _get_positions(broker):
            return broker.get_positions()
        
        return self._execute_with_retry('get_positions', _get_positions)
    
    def get_position(self, symbol: str) -> Optional[Position]:
        """获取指定股票的持仓信息
        
        Args:
            symbol: 股票代码
            
        Returns:
            Optional[Position]: 持仓信息
        """
        def _get_position(broker, symbol):
            return broker.get_position(symbol)
        
        return self._execute_with_retry('get_position', _get_position, symbol)
    
    def place_order(self, order: Order) -> str:
        """提交订单
        
        Args:
            order: 订单对象
            
        Returns:
            str: 订单ID
        """
        def _place_order(broker, order):
            return broker.place_order(order)
        
        self.stats['total_orders'] += 1
        
        try:
            order_id = self._execute_with_retry('place_order', _place_order, order)
            self.logger.info(f"订单提交成功: {order.symbol} {order.side.value} {order.quantity} 订单ID: {order_id}")
            return order_id
        except Exception as e:
            self.logger.error(f"订单提交失败: {order.symbol} {order.side.value} {order.quantity} 错误: {e}")
            raise
    
    def cancel_order(self, order_id: str) -> bool:
        """撤销订单
        
        Args:
            order_id: 订单ID
            
        Returns:
            bool: 撤销是否成功
        """
        def _cancel_order(broker, order_id):
            return broker.cancel_order(order_id)
        
        return self._execute_with_retry('cancel_order', _cancel_order, order_id)
    
    def get_order_status(self, order_id: str) -> Optional[Order]:
        """查询订单状态
        
        Args:
            order_id: 订单ID
            
        Returns:
            Optional[Order]: 订单信息
        """
        def _get_order_status(broker, order_id):
            return broker.get_order_status(order_id)
        
        return self._execute_with_retry('get_order_status', _get_order_status, order_id)
    
    def get_orders(self, symbol: Optional[str] = None) -> List[Order]:
        """获取订单列表
        
        Args:
            symbol: 可选的股票代码过滤
            
        Returns:
            List[Order]: 订单列表
        """
        def _get_orders(broker, symbol):
            return broker.get_orders(symbol)
        
        return self._execute_with_retry('get_orders', _get_orders, symbol)
    
    def get_trades(self, symbol: Optional[str] = None) -> List[Trade]:
        """获取成交记录
        
        Args:
            symbol: 可选的股票代码过滤
            
        Returns:
            List[Trade]: 成交记录列表
        """
        def _get_trades(broker, symbol):
            return broker.get_trades(symbol)
        
        return self._execute_with_retry('get_trades', _get_trades, symbol)
    
    def place_market_order(self, symbol: str, quantity: int, side: OrderSide) -> str:
        """提交市价单
        
        Args:
            symbol: 股票代码
            quantity: 数量
            side: 买卖方向
            
        Returns:
            str: 订单ID
        """
        order = Order(
            order_id="",
            symbol=symbol,
            side=side,
            order_type=OrderType.MARKET,
            quantity=quantity,
            create_time=datetime.now()
        )
        return self.place_order(order)
    
    def place_limit_order(self, symbol: str, quantity: int, price: float, side: OrderSide) -> str:
        """提交限价单
        
        Args:
            symbol: 股票代码
            quantity: 数量
            price: 价格
            side: 买卖方向
            
        Returns:
            str: 订单ID
        """
        order = Order(
            order_id="",
            symbol=symbol,
            side=side,
            order_type=OrderType.LIMIT,
            quantity=quantity,
            price=price,
            create_time=datetime.now()
        )
        return self.place_order(order)
    
    def get_broker_status(self) -> Dict[str, Any]:
        """获取券商状态信息
        
        Returns:
            Dict[str, Any]: 券商状态信息
        """
        status = {
            'primary_broker': self.primary_broker,
            'backup_brokers': self.backup_brokers,
            'broker_health': self.broker_health.copy(),
            'last_health_check': {
                name: time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.mktime(dt.timetuple())))
                for name, dt in self.last_health_check.items()
            },
            'statistics': self.stats.copy()
        }
        return status
    
    def switch_primary_broker(self, broker_name: str) -> bool:
        """切换主要券商
        
        Args:
            broker_name: 新的主要券商名称
            
        Returns:
            bool: 切换是否成功
        """
        if broker_name not in self.brokers:
            self.logger.error(f"券商 {broker_name} 不存在")
            return False
        
        if not self.broker_health.get(broker_name, False):
            self.logger.error(f"券商 {broker_name} 状态不健康，无法切换")
            return False
        
        old_primary = self.primary_broker
        self.primary_broker = broker_name
        
        # 更新备用券商列表
        if old_primary and old_primary != broker_name:
            if old_primary not in self.backup_brokers:
                self.backup_brokers.append(old_primary)
        
        if broker_name in self.backup_brokers:
            self.backup_brokers.remove(broker_name)
        
        self.stats['broker_switches'] += 1
        self.logger.info(f"主要券商已切换: {old_primary} -> {broker_name}")
        
        return True
    
    def __del__(self):
        """析构函数，确保资源清理"""
        try:
            self.disconnect()
        except Exception:
            pass