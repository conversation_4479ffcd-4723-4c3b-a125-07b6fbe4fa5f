"""
券商接口适配层集成测试
测试多券商API统一接口封装、订单路由、负载均衡、容错重试和监控系统
"""

import logging
import time
import threading
from typing import Dict, List, Any
from datetime import datetime
import json

# 解决模块导入问题
import sys
import os

# 获取项目根目录
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

try:
    # 尝试使用绝对导入
    from qlib_trading_system.trading.execution.broker_interface import (
        Order, OrderSide, OrderType, OrderStatus, BrokerException, ConnectionException
    )
    from qlib_trading_system.trading.execution.broker_adapter import BrokerAdapter
    from qlib_trading_system.trading.execution.order_router import OrderRouter, RoutingStrategy
    from qlib_trading_system.trading.execution.trading_monitor import TradingMonitor
    from qlib_trading_system.trading.execution.retry_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>, Retry<PERSON>onfig, RetryStrategy
    from qlib_trading_system.trading.execution.mock_broker import MockBroker
except ImportError:
    # 如果绝对导入失败，尝试相对导入
    try:
        from .broker_interface import (
            Order, OrderSide, OrderType, OrderStatus, BrokerException, ConnectionException
        )
        from .broker_adapter import BrokerAdapter
        from .order_router import OrderRouter, RoutingStrategy
        from .trading_monitor import TradingMonitor
        from .retry_handler import RetryHandler, RetryConfig, RetryStrategy
        from .mock_broker import MockBroker
    except ImportError:
        # 最后尝试直接导入
        import broker_interface
        import broker_adapter
        import order_router
        import trading_monitor
        import retry_handler
        import mock_broker
        
        Order = broker_interface.Order
        OrderSide = broker_interface.OrderSide
        OrderType = broker_interface.OrderType
        OrderStatus = broker_interface.OrderStatus
        BrokerException = broker_interface.BrokerException
        ConnectionException = broker_interface.ConnectionException
        
        BrokerAdapter = broker_adapter.BrokerAdapter
        OrderRouter = order_router.OrderRouter
        RoutingStrategy = order_router.RoutingStrategy
        TradingMonitor = trading_monitor.TradingMonitor
        RetryHandler = retry_handler.RetryHandler
        RetryConfig = retry_handler.RetryConfig
        RetryStrategy = retry_handler.RetryStrategy
        MockBroker = mock_broker.MockBroker


def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('broker_integration_test.log', encoding='utf-8')
        ]
    )


def create_test_config() -> Dict[str, Any]:
    """创建测试配置"""
    return {
        'brokers': {
            'mock_broker_1': {
                'class': 'qlib_trading_system.trading.execution.mock_broker.MockBroker',
                'name': 'MockBroker1',
                'primary': True,
                'weight': 1.0,
                'initial_cash': 1000000.0,
                'commission_rate': 0.0003,
                'connection_delay': 0.05,
                'order_delay': 0.02,
                'connection_failure_rate': 0.01,
                'order_failure_rate': 0.02
            },
            'mock_broker_2': {
                'class': 'qlib_trading_system.trading.execution.mock_broker.MockBroker',
                'name': 'MockBroker2',
                'primary': False,
                'weight': 0.8,
                'initial_cash': 800000.0,
                'commission_rate': 0.0005,
                'connection_delay': 0.08,
                'order_delay': 0.03,
                'connection_failure_rate': 0.02,
                'order_failure_rate': 0.03
            },
            'mock_broker_3': {
                'class': 'qlib_trading_system.trading.execution.mock_broker.MockBroker',
                'name': 'MockBroker3',
                'primary': False,
                'weight': 0.6,
                'initial_cash': 500000.0,
                'commission_rate': 0.0004,
                'connection_delay': 0.1,
                'order_delay': 0.05,
                'connection_failure_rate': 0.03,
                'order_failure_rate': 0.05
            }
        },
        'max_retries': 3,
        'retry_delay': 1.0,
        'health_check_interval': 10
    }


def create_router_config() -> Dict[str, Any]:
    """创建路由器配置"""
    return {
        'routing_strategy': 'WEIGHTED',
        'max_workers': 5,
        'load_update_interval': 5,
        'routing_rules': {
            'small_orders': {
                'min_order_size': 0,
                'max_order_size': 10000,
                'preferred_brokers': ['mock_broker_1'],
                'max_fragments': 1,
                'fragment_size': 5000
            },
            'large_orders': {
                'min_order_size': 50000,
                'max_order_size': float('inf'),
                'max_fragments': 5,
                'fragment_size': 2000
            },
            'default': {
                'max_fragments': 3,
                'fragment_size': 1000
            }
        }
    }


def create_monitor_config() -> Dict[str, Any]:
    """创建监控配置"""
    return {
        'sync_interval': 3,
        'metrics_interval': 10,
        'alert_retention_hours': 24,
        'max_trades_cache': 1000,
        'large_trade_threshold': 50000,
        'anomaly_detection': {
            'max_order_failure_rate': 0.1,
            'max_response_time': 3.0,
            'min_success_rate': 0.9
        },
        'max_position_concentration': 0.4,
        'min_cash_ratio': 0.1,
        'max_loss_ratio': 0.15
    }


def create_retry_config() -> Dict[str, Any]:
    """创建重试配置"""
    return {
        'max_attempts': 3,
        'base_delay': 0.5,
        'max_delay': 10.0,
        'strategy': 'EXPONENTIAL_BACKOFF',
        'jitter': True,
        'backoff_multiplier': 2.0,
        'circuit_failure_threshold': 3,
        'circuit_success_threshold': 2,
        'circuit_timeout': 30.0
    }


class BrokerIntegrationTest:
    """券商接口适配层集成测试类"""
    
    def __init__(self):
        """初始化测试"""
        self.logger = logging.getLogger(__name__)
        
        # 创建组件
        self.broker_adapter = BrokerAdapter(create_test_config())
        self.order_router = OrderRouter(self.broker_adapter, create_router_config())
        self.trading_monitor = TradingMonitor(
            self.broker_adapter, 
            self.order_router, 
            create_monitor_config()
        )
        self.retry_handler = RetryHandler(create_retry_config())
        
        # 测试数据
        self.test_symbols = ['000001.SZ', '600000.SH', '000858.SZ', '002415.SZ', '300059.SZ']
        self.test_orders: List[Order] = []
        self.test_results: Dict[str, Any] = {}
        
        self.logger.info("券商接口适配层集成测试初始化完成")
    
    def run_all_tests(self) -> bool:
        """运行所有测试
        
        Returns:
            bool: 测试是否全部通过
        """
        self.logger.info("开始运行券商接口适配层集成测试")
        
        try:
            # 启动监控
            self.trading_monitor.start_monitoring()
            
            # 运行各项测试
            tests = [
                ('连接测试', self.test_broker_connections),
                ('基础交易测试', self.test_basic_trading),
                ('订单路由测试', self.test_order_routing),
                ('负载均衡测试', self.test_load_balancing),
                ('容错重试测试', self.test_fault_tolerance),
                ('监控系统测试', self.test_monitoring_system),
                ('性能压力测试', self.test_performance)
            ]
            
            passed_tests = 0
            total_tests = len(tests)
            
            for test_name, test_func in tests:
                self.logger.info(f"运行测试: {test_name}")
                try:
                    if test_func():
                        self.logger.info(f"✓ {test_name} 通过")
                        passed_tests += 1
                    else:
                        self.logger.error(f"✗ {test_name} 失败")
                except Exception as e:
                    self.logger.error(f"✗ {test_name} 异常: {e}")
                
                # 测试间隔
                time.sleep(2)
            
            # 生成测试报告
            self.generate_test_report()
            
            success_rate = passed_tests / total_tests
            self.logger.info(f"测试完成: {passed_tests}/{total_tests} 通过 ({success_rate:.1%})")
            
            return success_rate >= 0.8  # 80%通过率认为测试成功
            
        except Exception as e:
            self.logger.error(f"测试运行异常: {e}")
            return False
        
        finally:
            # 停止监控
            self.trading_monitor.stop_monitoring()
            
            # 断开连接
            self.broker_adapter.disconnect()
    
    def test_broker_connections(self) -> bool:
        """测试券商连接功能"""
        try:
            # 测试连接
            if not self.broker_adapter.connect():
                self.logger.error("券商连接失败")
                return False
            
            # 检查券商状态
            status = self.broker_adapter.get_broker_status()
            healthy_brokers = sum(1 for is_healthy in status['broker_health'].values() if is_healthy)
            
            if healthy_brokers == 0:
                self.logger.error("没有健康的券商")
                return False
            
            self.logger.info(f"成功连接 {healthy_brokers} 个券商")
            
            # 测试市场状态
            is_market_open = self.broker_adapter.is_market_open()
            self.logger.info(f"市场开放状态: {is_market_open}")
            
            # 测试账户信息
            account = self.broker_adapter.get_account_info()
            self.logger.info(f"账户总资产: {account.total_assets:,.2f}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"连接测试异常: {e}")
            return False
    
    def test_basic_trading(self) -> bool:
        """测试基础交易功能"""
        try:
            symbol = self.test_symbols[0]
            
            # 创建测试订单
            buy_order = Order(
                order_id="",
                symbol=symbol,
                side=OrderSide.BUY,
                order_type=OrderType.MARKET,
                quantity=100,
                create_time=datetime.now()
            )
            
            # 提交买入订单
            order_id = self.broker_adapter.place_order(buy_order)
            self.test_orders.append(buy_order)
            
            self.logger.info(f"买入订单提交成功: {order_id}")
            
            # 等待订单处理
            time.sleep(2)
            
            # 查询订单状态
            order_status = self.broker_adapter.get_order_status(order_id)
            if not order_status:
                self.logger.error("无法查询订单状态")
                return False
            
            self.logger.info(f"订单状态: {order_status.status.value}")
            
            # 查询持仓
            position = self.broker_adapter.get_position(symbol)
            if position:
                self.logger.info(f"持仓数量: {position.quantity}")
            
            # 如果订单成交，尝试卖出
            if order_status.status == OrderStatus.FILLED:
                sell_order = Order(
                    order_id="",
                    symbol=symbol,
                    side=OrderSide.SELL,
                    order_type=OrderType.MARKET,
                    quantity=50,  # 卖出一半
                    create_time=datetime.now()
                )
                
                sell_order_id = self.broker_adapter.place_order(sell_order)
                self.test_orders.append(sell_order)
                
                self.logger.info(f"卖出订单提交成功: {sell_order_id}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"基础交易测试异常: {e}")
            return False
    
    def test_order_routing(self) -> bool:
        """测试订单路由功能"""
        try:
            # 测试小订单路由
            small_order = Order(
                order_id="ROUTE_TEST_SMALL",
                symbol=self.test_symbols[1],
                side=OrderSide.BUY,
                order_type=OrderType.MARKET,
                quantity=200,
                create_time=datetime.now()
            )
            
            order_ids = self.order_router.route_order(small_order)
            self.logger.info(f"小订单路由结果: {len(order_ids)} 个订单")
            
            # 测试大订单分片
            large_order = Order(
                order_id="ROUTE_TEST_LARGE",
                symbol=self.test_symbols[2],
                side=OrderSide.BUY,
                order_type=OrderType.MARKET,
                quantity=5000,  # 大订单，应该被分片
                create_time=datetime.now()
            )
            
            order_ids = self.order_router.route_order(large_order)
            self.logger.info(f"大订单路由结果: {len(order_ids)} 个订单")
            
            # 检查分片信息
            fragments = self.order_router.get_order_fragments("ROUTE_TEST_LARGE")
            self.logger.info(f"订单分片数量: {len(fragments)}")
            
            for fragment in fragments:
                self.logger.info(f"分片: {fragment.fragment_id} 券商: {fragment.broker_name} 数量: {fragment.quantity}")
            
            # 获取路由统计
            stats = self.order_router.get_routing_statistics()
            self.logger.info(f"路由统计: 总路由 {stats['total_routed']}, 成功 {stats['successful_routes']}")
            
            return len(order_ids) > 0
            
        except Exception as e:
            self.logger.error(f"订单路由测试异常: {e}")
            return False
    
    def test_load_balancing(self) -> bool:
        """测试负载均衡功能"""
        try:
            # 并发提交多个订单测试负载均衡
            def submit_order(symbol: str, quantity: int):
                order = Order(
                    order_id=f"LB_TEST_{symbol}_{quantity}",
                    symbol=symbol,
                    side=OrderSide.BUY,
                    order_type=OrderType.MARKET,
                    quantity=quantity,
                    create_time=datetime.now()
                )
                
                try:
                    order_ids = self.order_router.route_order(order)
                    return len(order_ids) > 0
                except Exception as e:
                    self.logger.error(f"负载均衡测试订单失败: {e}")
                    return False
            
            # 创建多个线程并发提交订单
            threads = []
            results = []
            
            for i, symbol in enumerate(self.test_symbols):
                thread = threading.Thread(
                    target=lambda s=symbol, q=(i+1)*100: results.append(submit_order(s, q))
                )
                threads.append(thread)
                thread.start()
            
            # 等待所有线程完成
            for thread in threads:
                thread.join()
            
            success_count = sum(1 for result in results if result)
            self.logger.info(f"负载均衡测试: {success_count}/{len(results)} 成功")
            
            # 检查券商负载分布
            stats = self.order_router.get_routing_statistics()
            broker_loads = stats.get('broker_loads', {})
            
            for broker_name, load_info in broker_loads.items():
                self.logger.info(
                    f"券商 {broker_name} 负载: "
                    f"待处理 {load_info['pending_orders']}, "
                    f"处理中 {load_info['processing_orders']}, "
                    f"响应时间 {load_info['avg_response_time']:.3f}s"
                )
            
            return success_count >= len(results) * 0.8  # 80%成功率
            
        except Exception as e:
            self.logger.error(f"负载均衡测试异常: {e}")
            return False
    
    def test_fault_tolerance(self) -> bool:
        """测试容错重试功能"""
        try:
            # 创建重试配置
            retry_config = RetryConfig(
                max_attempts=3,
                base_delay=0.5,
                strategy=RetryStrategy.EXPONENTIAL_BACKOFF
            )
            
            # 测试重试机制
            def failing_operation():
                """模拟可能失败的操作"""
                import random
                if random.random() < 0.6:  # 60%失败率
                    raise ConnectionException("模拟连接失败")
                return "操作成功"
            
            # 使用重试处理器执行操作
            result = self.retry_handler.execute_with_retry(
                failing_operation,
                retry_config=retry_config,
                circuit_breaker_name="test_circuit"
            )
            
            if result.success:
                self.logger.info(f"重试操作成功，尝试次数: {result.attempts}")
            else:
                self.logger.warning(f"重试操作失败，尝试次数: {result.attempts}")
            
            # 测试熔断器
            circuit_breaker = self.retry_handler.get_circuit_breaker("test_circuit")
            circuit_status = circuit_breaker.get_status()
            
            self.logger.info(f"熔断器状态: {circuit_status['state']}")
            self.logger.info(f"熔断器失败次数: {circuit_status['failure_count']}")
            
            # 获取重试统计
            retry_stats = self.retry_handler.get_statistics()
            self.logger.info(f"重试统计: {retry_stats}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"容错重试测试异常: {e}")
            return False
    
    def test_monitoring_system(self) -> bool:
        """测试监控系统功能"""
        try:
            # 等待监控系统收集数据
            time.sleep(5)
            
            # 检查监控状态
            monitor_status = self.trading_monitor.get_current_status()
            self.logger.info(f"监控系统状态: {monitor_status['is_monitoring']}")
            self.logger.info(f"最后同步时间: {monitor_status['last_sync_time']}")
            
            # 检查当前指标
            current_metrics = monitor_status['current_metrics']
            self.logger.info(f"当前指标:")
            self.logger.info(f"  总订单数: {current_metrics['total_orders']}")
            self.logger.info(f"  成功订单数: {current_metrics['successful_orders']}")
            self.logger.info(f"  成功率: {current_metrics['success_rate']:.2%}")
            self.logger.info(f"  总成交数: {current_metrics['total_trades']}")
            self.logger.info(f"  持仓数量: {current_metrics['positions_count']}")
            
            # 检查告警
            alerts = self.trading_monitor.get_alerts()
            self.logger.info(f"告警数量: {len(alerts)}")
            
            for alert in alerts[:5]:  # 显示前5个告警
                self.logger.info(f"告警: [{alert.level.value}] {alert.title}")
            
            # 检查缓存状态
            cache_status = monitor_status['cache_status']
            self.logger.info(f"缓存状态:")
            self.logger.info(f"  订单缓存: {cache_status['orders_count']}")
            self.logger.info(f"  成交缓存: {cache_status['trades_count']}")
            self.logger.info(f"  持仓缓存: {cache_status['positions_count']}")
            
            return monitor_status['is_monitoring']
            
        except Exception as e:
            self.logger.error(f"监控系统测试异常: {e}")
            return False
    
    def test_performance(self) -> bool:
        """测试性能压力"""
        try:
            self.logger.info("开始性能压力测试")
            
            start_time = time.time()
            order_count = 50
            successful_orders = 0
            
            # 并发提交大量订单
            def submit_batch_orders():
                nonlocal successful_orders
                for i in range(10):
                    try:
                        symbol = self.test_symbols[i % len(self.test_symbols)]
                        order = Order(
                            order_id=f"PERF_TEST_{threading.current_thread().ident}_{i}",
                            symbol=symbol,
                            side=OrderSide.BUY,
                            order_type=OrderType.MARKET,
                            quantity=100,
                            create_time=datetime.now()
                        )
                        
                        order_ids = self.order_router.route_order(order)
                        if order_ids:
                            successful_orders += 1
                            
                    except Exception as e:
                        self.logger.warning(f"性能测试订单失败: {e}")
            
            # 创建多个线程
            threads = []
            for _ in range(5):
                thread = threading.Thread(target=submit_batch_orders)
                threads.append(thread)
                thread.start()
            
            # 等待所有线程完成
            for thread in threads:
                thread.join()
            
            elapsed_time = time.time() - start_time
            
            self.logger.info(f"性能测试完成:")
            self.logger.info(f"  总耗时: {elapsed_time:.2f}秒")
            self.logger.info(f"  成功订单: {successful_orders}/{order_count}")
            self.logger.info(f"  平均TPS: {successful_orders/elapsed_time:.2f}")
            
            # 检查系统资源使用情况
            broker_status = self.broker_adapter.get_broker_status()
            routing_stats = self.order_router.get_routing_statistics()
            
            self.logger.info(f"券商统计: {broker_status['statistics']}")
            self.logger.info(f"路由统计: 平均路由时间 {routing_stats['avg_routing_time']:.3f}s")
            
            return successful_orders >= order_count * 0.8  # 80%成功率
            
        except Exception as e:
            self.logger.error(f"性能测试异常: {e}")
            return False
    
    def generate_test_report(self):
        """生成测试报告"""
        try:
            report = {
                'test_time': datetime.now().isoformat(),
                'broker_status': self.broker_adapter.get_broker_status(),
                'routing_statistics': self.order_router.get_routing_statistics(),
                'monitoring_status': self.trading_monitor.get_current_status(),
                'retry_statistics': self.retry_handler.get_statistics(),
                'test_orders_count': len(self.test_orders)
            }
            
            # 保存报告到文件
            with open('broker_integration_test_report.json', 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False, default=str)
            
            self.logger.info("测试报告已生成: broker_integration_test_report.json")
            
        except Exception as e:
            self.logger.error(f"生成测试报告失败: {e}")


def main():
    """主函数"""
    setup_logging()
    
    logger = logging.getLogger(__name__)
    logger.info("启动券商接口适配层集成测试")
    
    try:
        # 创建并运行测试
        test = BrokerIntegrationTest()
        success = test.run_all_tests()
        
        if success:
            logger.info("🎉 券商接口适配层集成测试全部通过！")
            return 0
        else:
            logger.error("❌ 券商接口适配层集成测试失败")
            return 1
            
    except Exception as e:
        logger.error(f"测试运行异常: {e}")
        return 1


if __name__ == "__main__":
    exit(main())