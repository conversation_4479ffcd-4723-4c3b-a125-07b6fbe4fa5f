#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全仓单股策略风险控制器 - Single Stock Risk Controller

专门为全仓单股+做T策略设计的风险控制系统，提供多层次风险保护

Author: Qlib Trading System
Date: 2025-01-30
"""

import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import pandas as pd
import numpy as np

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class RiskLevel(Enum):
    """风险等级枚举"""
    LOW = "low"           # 低风险
    MEDIUM = "medium"     # 中等风险
    HIGH = "high"         # 高风险
    CRITICAL = "critical" # 严重风险


class RiskEvent(Enum):
    """风险事件类型枚举"""
    POSITION_LOSS = "position_loss"           # 持仓亏损
    DAILY_LOSS = "daily_loss"                # 日亏损
    DRAWDOWN = "drawdown"                    # 回撤
    VOLUME_ANOMALY = "volume_anomaly"        # 成交量异常
    PRICE_ANOMALY = "price_anomaly"          # 价格异常
    LIQUIDITY_RISK = "liquidity_risk"        # 流动性风险
    CONCENTRATION_RISK = "concentration_risk" # 集中度风险


@dataclass
class RiskAlert:
    """风险警报类"""
    alert_id: str                    # 警报ID
    timestamp: datetime              # 时间戳
    symbol: str                      # 股票代码
    risk_type: RiskEvent            # 风险类型
    risk_level: RiskLevel           # 风险等级
    current_value: float            # 当前值
    threshold_value: float          # 阈值
    message: str                    # 警报消息
    suggested_action: str           # 建议行动
    is_resolved: bool = False       # 是否已解决
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            "alert_id": self.alert_id,
            "timestamp": self.timestamp.isoformat(),
            "symbol": self.symbol,
            "risk_type": self.risk_type.value,
            "risk_level": self.risk_level.value,
            "current_value": self.current_value,
            "threshold_value": self.threshold_value,
            "message": self.message,
            "suggested_action": self.suggested_action,
            "is_resolved": self.is_resolved
        }


@dataclass
class SingleStockRiskConfig:
    """单股风险控制配置"""
    # 基础风险参数
    max_position_loss_pct: float = 0.05      # 最大持仓亏损比例 (5%)
    max_daily_loss_pct: float = 0.03         # 最大日亏损比例 (3%)
    max_drawdown_pct: float = 0.15           # 最大回撤比例 (15%)
    
    # 止损参数
    stop_loss_pct: float = 0.08              # 止损比例 (8%)
    trailing_stop_pct: float = 0.05          # 移动止损比例 (5%)
    profit_protect_pct: float = 0.10         # 盈利保护比例 (10%)
    
    # 流动性风险参数
    min_daily_volume: float = 50000000       # 最小日成交额 (5000万)
    max_bid_ask_spread_pct: float = 0.02     # 最大买卖价差比例 (2%)
    min_market_depth: int = 1000000          # 最小市场深度 (100万股)
    
    # 异常检测参数
    price_volatility_threshold: float = 0.15 # 价格波动率阈值 (15%)
    volume_spike_threshold: float = 3.0      # 成交量异常倍数 (3倍)
    price_gap_threshold: float = 0.05        # 价格跳空阈值 (5%)
    
    # 时间控制参数
    max_holding_days: int = 90               # 最大持仓天数
    risk_check_interval_seconds: int = 30    # 风险检查间隔(秒)
    
    def validate(self):
        """验证配置合理性"""
        if self.max_position_loss_pct <= 0 or self.max_position_loss_pct > 0.5:
            raise ValueError("最大持仓亏损比例应在0-50%之间")
        
        if self.stop_loss_pct <= 0 or self.stop_loss_pct > 0.2:
            raise ValueError("止损比例应在0-20%之间")
        
        if self.max_drawdown_pct <= 0 or self.max_drawdown_pct > 0.5:
            raise ValueError("最大回撤比例应在0-50%之间")


class SingleStockRiskController:
    """全仓单股策略风险控制器"""
    
    def __init__(self, config: SingleStockRiskConfig):
        """
        初始化风险控制器
        
        Args:
            config: 风险控制配置
        """
        self.config = config
        self.config.validate()
        
        # 风险状态跟踪
        self.current_alerts: List[RiskAlert] = []
        self.risk_history: List[RiskAlert] = []
        self.last_check_time = datetime.now()
        
        # 持仓跟踪
        self.position_entry_price = 0.0
        self.position_entry_time = None
        self.highest_price = 0.0
        self.lowest_price = float('inf')
        
        # 风险指标
        self.current_drawdown = 0.0
        self.max_drawdown = 0.0
        self.daily_pnl = 0.0
        self.position_pnl = 0.0
        
        logger.info("单股风险控制器初始化完成")
    
    def update_position(self, symbol: str, current_price: float, position_value: float, 
                       cost_basis: float, daily_volume: float = 0):
        """
        更新持仓信息并进行风险检查
        
        Args:
            symbol: 股票代码
            current_price: 当前价格
            position_value: 持仓市值
            cost_basis: 成本基础
            daily_volume: 日成交额
        """
        # 更新价格跟踪
        if self.position_entry_price == 0:
            self.position_entry_price = cost_basis
            self.position_entry_time = datetime.now()
        
        self.highest_price = max(self.highest_price, current_price)
        self.lowest_price = min(self.lowest_price, current_price)
        
        # 计算盈亏
        self.position_pnl = (current_price - cost_basis) / cost_basis if cost_basis > 0 else 0
        
        # 计算回撤
        if self.highest_price > 0:
            current_drawdown = (self.highest_price - current_price) / self.highest_price
            self.current_drawdown = current_drawdown
            self.max_drawdown = max(self.max_drawdown, current_drawdown)
        
        # 执行风险检查
        self._perform_risk_checks(symbol, current_price, position_value, cost_basis, daily_volume)
        
        self.last_check_time = datetime.now()
    
    def _perform_risk_checks(self, symbol: str, current_price: float, position_value: float,
                           cost_basis: float, daily_volume: float):
        """
        执行全面风险检查
        
        Args:
            symbol: 股票代码
            current_price: 当前价格
            position_value: 持仓市值
            cost_basis: 成本基础
            daily_volume: 日成交额
        """
        # 1. 持仓亏损检查
        self._check_position_loss(symbol, current_price, cost_basis)
        
        # 2. 回撤检查
        self._check_drawdown(symbol)
        
        # 3. 止损检查
        self._check_stop_loss(symbol, current_price, cost_basis)
        
        # 4. 流动性风险检查
        self._check_liquidity_risk(symbol, daily_volume)
        
        # 5. 价格异常检查
        self._check_price_anomaly(symbol, current_price)
        
        # 6. 持仓时间检查
        self._check_holding_period(symbol)
        
        # 7. 集中度风险检查
        self._check_concentration_risk(symbol, position_value)
    
    def _check_position_loss(self, symbol: str, current_price: float, cost_basis: float):
        """检查持仓亏损"""
        if cost_basis <= 0:
            return
        
        loss_pct = (cost_basis - current_price) / cost_basis
        
        if loss_pct > self.config.max_position_loss_pct:
            alert = RiskAlert(
                alert_id=self._generate_alert_id(),
                timestamp=datetime.now(),
                symbol=symbol,
                risk_type=RiskEvent.POSITION_LOSS,
                risk_level=RiskLevel.HIGH,
                current_value=loss_pct,
                threshold_value=self.config.max_position_loss_pct,
                message=f"持仓亏损{loss_pct:.2%}超过阈值{self.config.max_position_loss_pct:.2%}",
                suggested_action="考虑止损或减仓"
            )
            self._add_alert(alert)
    
    def _check_drawdown(self, symbol: str):
        """检查回撤"""
        if self.current_drawdown > self.config.max_drawdown_pct:
            risk_level = RiskLevel.CRITICAL if self.current_drawdown > self.config.max_drawdown_pct * 1.5 else RiskLevel.HIGH
            
            alert = RiskAlert(
                alert_id=self._generate_alert_id(),
                timestamp=datetime.now(),
                symbol=symbol,
                risk_type=RiskEvent.DRAWDOWN,
                risk_level=risk_level,
                current_value=self.current_drawdown,
                threshold_value=self.config.max_drawdown_pct,
                message=f"回撤{self.current_drawdown:.2%}超过阈值{self.config.max_drawdown_pct:.2%}",
                suggested_action="立即止损或大幅减仓" if risk_level == RiskLevel.CRITICAL else "考虑减仓"
            )
            self._add_alert(alert)
    
    def _check_stop_loss(self, symbol: str, current_price: float, cost_basis: float):
        """检查止损"""
        if cost_basis <= 0:
            return
        
        # 固定止损检查
        loss_pct = (cost_basis - current_price) / cost_basis
        if loss_pct > self.config.stop_loss_pct:
            alert = RiskAlert(
                alert_id=self._generate_alert_id(),
                timestamp=datetime.now(),
                symbol=symbol,
                risk_type=RiskEvent.POSITION_LOSS,
                risk_level=RiskLevel.CRITICAL,
                current_value=loss_pct,
                threshold_value=self.config.stop_loss_pct,
                message=f"触发止损线，亏损{loss_pct:.2%}",
                suggested_action="立即执行止损"
            )
            self._add_alert(alert)
        
        # 移动止损检查
        if self.highest_price > cost_basis:  # 有盈利时才启用移动止损
            trailing_stop_price = self.highest_price * (1 - self.config.trailing_stop_pct)
            if current_price < trailing_stop_price:
                alert = RiskAlert(
                    alert_id=self._generate_alert_id(),
                    timestamp=datetime.now(),
                    symbol=symbol,
                    risk_type=RiskEvent.POSITION_LOSS,
                    risk_level=RiskLevel.HIGH,
                    current_value=current_price,
                    threshold_value=trailing_stop_price,
                    message=f"触发移动止损，当前价{current_price:.2f}低于移动止损价{trailing_stop_price:.2f}",
                    suggested_action="考虑止损保护利润"
                )
                self._add_alert(alert)
    
    def _check_liquidity_risk(self, symbol: str, daily_volume: float):
        """检查流动性风险"""
        if daily_volume > 0 and daily_volume < self.config.min_daily_volume:
            alert = RiskAlert(
                alert_id=self._generate_alert_id(),
                timestamp=datetime.now(),
                symbol=symbol,
                risk_type=RiskEvent.LIQUIDITY_RISK,
                risk_level=RiskLevel.MEDIUM,
                current_value=daily_volume,
                threshold_value=self.config.min_daily_volume,
                message=f"日成交额{daily_volume:,.0f}低于最小要求{self.config.min_daily_volume:,.0f}",
                suggested_action="关注流动性，准备减仓"
            )
            self._add_alert(alert)
    
    def _check_price_anomaly(self, symbol: str, current_price: float):
        """检查价格异常"""
        if self.position_entry_price <= 0:
            return
        
        # 检查价格跳空
        price_change = abs(current_price - self.position_entry_price) / self.position_entry_price
        if price_change > self.config.price_gap_threshold:
            risk_level = RiskLevel.HIGH if price_change > self.config.price_gap_threshold * 2 else RiskLevel.MEDIUM
            
            alert = RiskAlert(
                alert_id=self._generate_alert_id(),
                timestamp=datetime.now(),
                symbol=symbol,
                risk_type=RiskEvent.PRICE_ANOMALY,
                risk_level=risk_level,
                current_value=price_change,
                threshold_value=self.config.price_gap_threshold,
                message=f"价格异常变动{price_change:.2%}",
                suggested_action="检查是否有重大消息，考虑调整仓位"
            )
            self._add_alert(alert)
    
    def _check_holding_period(self, symbol: str):
        """检查持仓时间"""
        if self.position_entry_time is None:
            return
        
        holding_days = (datetime.now() - self.position_entry_time).days
        if holding_days > self.config.max_holding_days:
            alert = RiskAlert(
                alert_id=self._generate_alert_id(),
                timestamp=datetime.now(),
                symbol=symbol,
                risk_type=RiskEvent.CONCENTRATION_RISK,
                risk_level=RiskLevel.MEDIUM,
                current_value=holding_days,
                threshold_value=self.config.max_holding_days,
                message=f"持仓时间{holding_days}天超过最大持仓期限",
                suggested_action="考虑换股或调整策略"
            )
            self._add_alert(alert)
    
    def _check_concentration_risk(self, symbol: str, position_value: float):
        """检查集中度风险"""
        # 对于全仓单股策略，集中度风险主要体现在单一标的风险
        # 这里可以检查是否需要分散投资
        if position_value > 0:  # 有持仓时提醒集中度风险
            alert = RiskAlert(
                alert_id=self._generate_alert_id(),
                timestamp=datetime.now(),
                symbol=symbol,
                risk_type=RiskEvent.CONCENTRATION_RISK,
                risk_level=RiskLevel.LOW,
                current_value=1.0,  # 100%集中度
                threshold_value=1.0,
                message="全仓单股策略存在高集中度风险",
                suggested_action="密切监控标的基本面变化"
            )
            # 只在没有相同类型警报时添加
            if not any(a.risk_type == RiskEvent.CONCENTRATION_RISK and not a.is_resolved 
                      for a in self.current_alerts):
                self._add_alert(alert)
    
    def _add_alert(self, alert: RiskAlert):
        """添加风险警报"""
        # 检查是否已存在相同类型的未解决警报
        existing_alert = next((a for a in self.current_alerts 
                             if a.symbol == alert.symbol and 
                                a.risk_type == alert.risk_type and 
                                not a.is_resolved), None)
        
        if existing_alert:
            # 更新现有警报
            existing_alert.current_value = alert.current_value
            existing_alert.timestamp = alert.timestamp
            existing_alert.message = alert.message
        else:
            # 添加新警报
            self.current_alerts.append(alert)
            self.risk_history.append(alert)
            logger.warning(f"风险警报: {alert.message}")
    
    def _generate_alert_id(self) -> str:
        """生成警报ID"""
        return f"RISK_{datetime.now().strftime('%Y%m%d%H%M%S')}_{len(self.risk_history):04d}"
    
    def get_current_risk_status(self) -> Dict:
        """
        获取当前风险状态
        
        Returns:
            风险状态字典
        """
        active_alerts = [a for a in self.current_alerts if not a.is_resolved]
        
        # 计算风险等级
        if any(a.risk_level == RiskLevel.CRITICAL for a in active_alerts):
            overall_risk = RiskLevel.CRITICAL
        elif any(a.risk_level == RiskLevel.HIGH for a in active_alerts):
            overall_risk = RiskLevel.HIGH
        elif any(a.risk_level == RiskLevel.MEDIUM for a in active_alerts):
            overall_risk = RiskLevel.MEDIUM
        else:
            overall_risk = RiskLevel.LOW
        
        return {
            "overall_risk_level": overall_risk.value,
            "active_alerts_count": len(active_alerts),
            "total_alerts_count": len(self.risk_history),
            "current_drawdown": self.current_drawdown,
            "max_drawdown": self.max_drawdown,
            "position_pnl": self.position_pnl,
            "holding_days": (datetime.now() - self.position_entry_time).days if self.position_entry_time else 0,
            "last_check_time": self.last_check_time.isoformat(),
            "active_alerts": [a.to_dict() for a in active_alerts]
        }
    
    def resolve_alert(self, alert_id: str) -> bool:
        """
        解决风险警报
        
        Args:
            alert_id: 警报ID
            
        Returns:
            是否成功解决
        """
        alert = next((a for a in self.current_alerts if a.alert_id == alert_id), None)
        if alert:
            alert.is_resolved = True
            logger.info(f"风险警报已解决: {alert_id}")
            return True
        return False
    
    def get_risk_suggestions(self) -> List[str]:
        """
        获取风险管理建议
        
        Returns:
            建议列表
        """
        suggestions = []
        active_alerts = [a for a in self.current_alerts if not a.is_resolved]
        
        # 基于当前风险状态给出建议
        if any(a.risk_level == RiskLevel.CRITICAL for a in active_alerts):
            suggestions.append("⚠️ 存在严重风险，建议立即止损或大幅减仓")
        
        if self.current_drawdown > self.config.max_drawdown_pct * 0.8:
            suggestions.append("📉 回撤接近警戒线，建议控制仓位")
        
        if self.position_pnl < -self.config.max_position_loss_pct * 0.8:
            suggestions.append("💰 持仓亏损较大，建议重新评估投资逻辑")
        
        if self.position_entry_time and (datetime.now() - self.position_entry_time).days > self.config.max_holding_days * 0.8:
            suggestions.append("⏰ 持仓时间较长，建议考虑换股")
        
        # 通用建议
        suggestions.extend([
            "📊 定期检查基本面变化",
            "🎯 设置合理的止盈止损点",
            "📈 关注市场整体趋势",
            "⚖️ 保持风险收益平衡"
        ])
        
        return suggestions
    
    def generate_risk_report(self) -> Dict:
        """
        生成风险报告
        
        Returns:
            风险报告字典
        """
        risk_status = self.get_current_risk_status()
        suggestions = self.get_risk_suggestions()
        
        # 统计风险事件
        risk_event_stats = {}
        for event_type in RiskEvent:
            count = len([a for a in self.risk_history if a.risk_type == event_type])
            risk_event_stats[event_type.value] = count
        
        return {
            "report_time": datetime.now().isoformat(),
            "risk_status": risk_status,
            "risk_suggestions": suggestions,
            "risk_event_statistics": risk_event_stats,
            "performance_metrics": {
                "max_drawdown": self.max_drawdown,
                "current_drawdown": self.current_drawdown,
                "position_pnl": self.position_pnl,
                "highest_price": self.highest_price,
                "lowest_price": self.lowest_price if self.lowest_price != float('inf') else 0,
                "entry_price": self.position_entry_price
            },
            "config_summary": {
                "max_position_loss_pct": self.config.max_position_loss_pct,
                "max_drawdown_pct": self.config.max_drawdown_pct,
                "stop_loss_pct": self.config.stop_loss_pct,
                "max_holding_days": self.config.max_holding_days
            }
        }


# 测试代码
if __name__ == "__main__":
    # 创建测试配置
    config = SingleStockRiskConfig(
        max_position_loss_pct=0.05,
        max_daily_loss_pct=0.03,
        max_drawdown_pct=0.15,
        stop_loss_pct=0.08
    )
    
    # 创建风险控制器
    controller = SingleStockRiskController(config)
    
    print("=== 单股风险控制器测试 ===")
    
    # 模拟持仓更新
    controller.update_position("000001.SZ", 10.50, 100000, 10.00, 80000000)
    print("初始持仓更新完成")
    
    # 模拟价格下跌
    controller.update_position("000001.SZ", 9.20, 92000, 10.00, 75000000)
    print("价格下跌更新完成")
    
    # 获取风险状态
    risk_status = controller.get_current_risk_status()
    print(f"风险状态: {risk_status}")
    
    # 生成风险报告
    risk_report = controller.generate_risk_report()
    print(f"风险报告: {risk_report}")
    
    print("=== 测试完成 ===")