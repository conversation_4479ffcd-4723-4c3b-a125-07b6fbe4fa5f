"""
FastAPI中间件
FastAPI Middleware

提供请求日志、速率限制、认证、监控等中间件功能
"""

import time
import logging
from typing import Callable
from fastapi import Request, Response, HTTPException
from starlette.middleware.base import BaseHTTPMiddleware

logger = logging.getLogger(__name__)


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """请求日志中间件"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        start_time = time.time()
        
        # 生成请求ID
        request_id = f"req_{int(time.time() * 1000000)}"
        request.state.request_id = request_id
        
        # 获取请求信息
        method = request.method
        url = str(request.url)
        client_ip = request.client.host if request.client else "unknown"
        user_agent = request.headers.get("user-agent", "unknown")
        
        # 记录请求开始
        logger.info(f"请求开始: {request_id} {method} {url} from {client_ip}")
        
        try:
            # 处理请求
            response = await call_next(request)
            
            # 计算响应时间
            process_time = (time.time() - start_time) * 1000
            
            # 记录请求结束
            logger.info(
                f"请求结束: {request_id} {method} {url} "
                f"{response.status_code} {process_time:.2f}ms"
            )
            
            # 记录到监控系统
            api_monitor = getattr(request.app.state, 'api_monitor', None)
            if api_monitor:
                await api_monitor.record_request(
                    request_id=request_id,
                    endpoint=request.url.path,
                    method=method,
                    user_id=getattr(request.state, 'user_id', None),
                    ip_address=client_ip,
                    user_agent=user_agent,
                    request_size=int(request.headers.get("content-length", 0)),
                    response_time_ms=process_time,
                    status_code=response.status_code,
                    response_size=len(response.body) if hasattr(response, 'body') else 0
                )
            
            # 添加响应头
            response.headers["X-Request-ID"] = request_id
            response.headers["X-Process-Time"] = f"{process_time:.2f}ms"
            
            return response
            
        except Exception as e:
            # 计算响应时间
            process_time = (time.time() - start_time) * 1000
            
            # 记录错误
            logger.error(f"请求错误: {request_id} {method} {url} {str(e)} {process_time:.2f}ms")
            
            # 记录到监控系统
            api_monitor = getattr(request.app.state, 'api_monitor', None)
            if api_monitor:
                await api_monitor.record_request(
                    request_id=request_id,
                    endpoint=request.url.path,
                    method=method,
                    user_id=getattr(request.state, 'user_id', None),
                    ip_address=client_ip,
                    user_agent=user_agent,
                    request_size=int(request.headers.get("content-length", 0)),
                    response_time_ms=process_time,
                    status_code=500,
                    response_size=0,
                    error_message=str(e)
                )
            
            raise


class RateLimitMiddleware(BaseHTTPMiddleware):
    """速率限制中间件"""
    
    def __init__(self, app, requests_per_minute: int = 100, burst_size: int = 20):
        super().__init__(app)
        self.requests_per_minute = requests_per_minute
        self.burst_size = burst_size
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 跳过健康检查和静态文件
        if request.url.path in ["/health", "/docs", "/redoc", "/openapi.json"]:
            return await call_next(request)
        
        # 获取用户标识
        user_id = self._get_user_identifier(request)
        
        # 检查速率限制
        api_monitor = getattr(request.app.state, 'api_monitor', None)
        if api_monitor and api_monitor.rate_limiter:
            if not await api_monitor.rate_limiter.is_allowed(user_id):
                # 获取剩余请求数
                remaining = await api_monitor.rate_limiter.get_remaining_requests(user_id)
                
                raise HTTPException(
                    status_code=429,
                    detail="请求频率过高，请稍后再试",
                    headers={
                        "X-RateLimit-Limit": str(self.requests_per_minute),
                        "X-RateLimit-Remaining": str(remaining),
                        "X-RateLimit-Reset": str(int(time.time()) + 60)
                    }
                )
        
        return await call_next(request)
    
    def _get_user_identifier(self, request: Request) -> str:
        """获取用户标识符"""
        # 优先使用用户ID
        user_id = getattr(request.state, 'user_id', None)
        if user_id:
            return f"user:{user_id}"
        
        # 使用IP地址
        client_ip = request.client.host if request.client else "unknown"
        return f"ip:{client_ip}"


class AuthenticationMiddleware(BaseHTTPMiddleware):
    """认证中间件"""
    
    def __init__(self, app):
        super().__init__(app)
        # 不需要认证的路径
        self.public_paths = {
            "/health",
            "/info",
            "/docs",
            "/redoc",
            "/openapi.json",
            "/api/v1/auth/login"
        }
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 检查是否是公开路径
        if request.url.path in self.public_paths:
            return await call_next(request)
        
        # 获取认证管理器
        auth_manager = getattr(request.app.state, 'auth_manager', None)
        if not auth_manager:
            return await call_next(request)
        
        # 尝试从Authorization头获取token
        authorization = request.headers.get("Authorization")
        if authorization and authorization.startswith("Bearer "):
            token = authorization.split(" ")[1]
            payload = await auth_manager.verify_token(token)
            if payload:
                request.state.user_id = payload.get('user_id')
                request.state.username = payload.get('username')
                request.state.role = payload.get('role')
                request.state.permissions = payload.get('permissions', [])
                return await call_next(request)
        
        # 尝试从X-API-Key头获取API密钥
        api_key = request.headers.get("X-API-Key")
        if api_key:
            user = await auth_manager.verify_api_key(api_key)
            if user:
                request.state.user_id = user.user_id
                request.state.username = user.username
                request.state.role = user.role.value
                request.state.permissions = [p.value for p in user.permissions]
                return await call_next(request)
        
        # 如果没有有效的认证信息，继续处理（让具体的端点处理认证）
        return await call_next(request)


class CORSMiddleware(BaseHTTPMiddleware):
    """CORS中间件"""
    
    def __init__(
        self, 
        app,
        allow_origins: list = None,
        allow_methods: list = None,
        allow_headers: list = None,
        allow_credentials: bool = True
    ):
        super().__init__(app)
        self.allow_origins = allow_origins or ["*"]
        self.allow_methods = allow_methods or ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"]
        self.allow_headers = allow_headers or ["*"]
        self.allow_credentials = allow_credentials
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 处理预检请求
        if request.method == "OPTIONS":
            response = Response()
            self._add_cors_headers(response, request)
            return response
        
        # 处理正常请求
        response = await call_next(request)
        self._add_cors_headers(response, request)
        return response
    
    def _add_cors_headers(self, response: Response, request: Request):
        """添加CORS头"""
        origin = request.headers.get("origin")
        
        # 检查origin是否被允许
        if "*" in self.allow_origins or (origin and origin in self.allow_origins):
            response.headers["Access-Control-Allow-Origin"] = origin or "*"
        
        response.headers["Access-Control-Allow-Methods"] = ", ".join(self.allow_methods)
        response.headers["Access-Control-Allow-Headers"] = ", ".join(self.allow_headers)
        
        if self.allow_credentials:
            response.headers["Access-Control-Allow-Credentials"] = "true"


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """安全头中间件"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        response = await call_next(request)
        
        # 添加安全头
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        response.headers["Content-Security-Policy"] = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
            "style-src 'self' 'unsafe-inline'; "
            "img-src 'self' data: https:; "
            "font-src 'self' data:; "
            "connect-src 'self' ws: wss:;"
        )
        
        return response


class CompressionMiddleware(BaseHTTPMiddleware):
    """压缩中间件"""
    
    def __init__(self, app, minimum_size: int = 1024):
        super().__init__(app)
        self.minimum_size = minimum_size
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        response = await call_next(request)
        
        # 检查是否支持gzip压缩
        accept_encoding = request.headers.get("accept-encoding", "")
        if "gzip" not in accept_encoding.lower():
            return response
        
        # 检查响应大小
        if hasattr(response, 'body') and len(response.body) < self.minimum_size:
            return response
        
        # 检查内容类型
        content_type = response.headers.get("content-type", "")
        compressible_types = [
            "application/json",
            "application/javascript",
            "text/html",
            "text/css",
            "text/plain",
            "text/xml"
        ]
        
        if not any(ct in content_type for ct in compressible_types):
            return response
        
        # 这里可以添加实际的gzip压缩逻辑
        # 为了简化，我们只添加头部
        response.headers["Content-Encoding"] = "gzip"
        response.headers["Vary"] = "Accept-Encoding"
        
        return response
