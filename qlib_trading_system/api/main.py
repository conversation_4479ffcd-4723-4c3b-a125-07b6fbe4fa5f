"""
FastAPI主应用入口
FastAPI Main Application Entry

创建和配置FastAPI应用，支持异步处理、WebSocket和消息队列
"""

import os
import logging
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import FastAPI, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from fastapi.openapi.docs import get_swagger_ui_html
from fastapi.openapi.utils import get_openapi

import uvicorn

from .auth import AuthManager
from .monitoring import APIMonitor
from .websocket import WebSocketManager
from .messaging import MessageQueue
from .routers import auth_router, config_router, monitoring_router, trading_router, websocket_router
from .middleware import RequestLoggingMiddleware, RateLimitMiddleware

logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化
    logger.info("启动FastAPI应用...")
    
    # 初始化组件
    await app.state.auth_manager.initialize()
    await app.state.api_monitor.initialize()
    await app.state.websocket_manager.initialize()
    await app.state.message_queue.initialize()
    
    logger.info("FastAPI应用启动完成")
    
    yield
    
    # 关闭时清理
    logger.info("关闭FastAPI应用...")
    
    await app.state.message_queue.cleanup()
    await app.state.websocket_manager.cleanup()
    await app.state.api_monitor.cleanup()
    await app.state.auth_manager.cleanup()
    
    logger.info("FastAPI应用关闭完成")


def create_app(config: Dict[str, Any] = None) -> FastAPI:
    """
    创建FastAPI应用
    
    Args:
        config: 配置字典
        
    Returns:
        FastAPI应用实例
    """
    # 默认配置
    default_config = {
        'title': 'Qlib Trading System API',
        'description': '量化交易系统高性能异步API接口',
        'version': '2.0.0',
        'debug': os.environ.get('DEBUG', 'False').lower() == 'true',
        'cors_origins': [
            'http://localhost:3000',
            'http://localhost:8080',
            'http://localhost:8000'
        ],
        'trusted_hosts': ['localhost', '127.0.0.1', '*.trading-system.com'],
        'rate_limit': {
            'requests_per_minute': 100,
            'burst_size': 20
        },
        'jwt_secret': os.environ.get('JWT_SECRET_KEY', 'dev-jwt-secret-change-in-production'),
        'rabbitmq_url': os.environ.get('RABBITMQ_URL', 'amqp://guest:guest@localhost:5672/'),
        'redis_url': os.environ.get('REDIS_URL', 'redis://localhost:6379/0')
    }
    
    # 合并配置
    if config:
        default_config.update(config)
    
    # 创建FastAPI应用
    app = FastAPI(
        title=default_config['title'],
        description=default_config['description'],
        version=default_config['version'],
        lifespan=lifespan,
        docs_url="/docs" if default_config['debug'] else None,
        redoc_url="/redoc" if default_config['debug'] else None,
        openapi_url="/openapi.json" if default_config['debug'] else None
    )
    
    # 存储配置
    app.state.config = default_config
    
    # 初始化组件
    app.state.auth_manager = AuthManager(
        secret_key=default_config['jwt_secret'],
        redis_url=default_config['redis_url']
    )
    
    app.state.api_monitor = APIMonitor(
        redis_url=default_config['redis_url']
    )
    
    app.state.websocket_manager = WebSocketManager()
    
    app.state.message_queue = MessageQueue(
        rabbitmq_url=default_config['rabbitmq_url']
    )
    
    # 添加中间件
    setup_middleware(app, default_config)
    
    # 注册路由
    setup_routes(app)
    
    # 注册异常处理器
    setup_exception_handlers(app)
    
    logger.info("FastAPI应用创建完成")
    return app


def setup_middleware(app: FastAPI, config: Dict[str, Any]):
    """设置中间件"""
    
    # CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=config['cors_origins'],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 可信主机中间件
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=config['trusted_hosts']
    )
    
    # 速率限制中间件
    app.add_middleware(
        RateLimitMiddleware,
        requests_per_minute=config['rate_limit']['requests_per_minute'],
        burst_size=config['rate_limit']['burst_size']
    )
    
    # 请求日志中间件
    app.add_middleware(RequestLoggingMiddleware)


def setup_routes(app: FastAPI):
    """设置路由"""
    
    # API路由前缀
    api_prefix = "/api/v1"
    
    # 注册路由
    app.include_router(auth_router, prefix=f"{api_prefix}/auth", tags=["认证"])
    app.include_router(config_router, prefix=f"{api_prefix}/config", tags=["配置管理"])
    app.include_router(monitoring_router, prefix=f"{api_prefix}/monitoring", tags=["监控"])
    app.include_router(trading_router, prefix=f"{api_prefix}/trading", tags=["交易"])
    app.include_router(websocket_router, tags=["WebSocket"])
    
    # 健康检查端点
    @app.get("/health")
    async def health_check():
        """健康检查"""
        return {
            "status": "healthy",
            "timestamp": "2025-07-31T15:30:00.000000",
            "version": app.state.config['version'],
            "service": "qlib-trading-system-api"
        }
    
    @app.get("/health/detailed")
    async def detailed_health_check():
        """详细健康检查"""
        try:
            # 检查各个组件状态
            auth_status = await app.state.auth_manager.health_check()
            monitor_status = await app.state.api_monitor.health_check()
            websocket_status = await app.state.websocket_manager.health_check()
            mq_status = await app.state.message_queue.health_check()
            
            return {
                "status": "healthy",
                "timestamp": "2025-07-31T15:30:00.000000",
                "version": app.state.config['version'],
                "components": {
                    "auth_manager": auth_status,
                    "api_monitor": monitor_status,
                    "websocket_manager": websocket_status,
                    "message_queue": mq_status
                }
            }
            
        except Exception as e:
            logger.error(f"详细健康检查失败: {e}")
            return JSONResponse(
                status_code=503,
                content={
                    "status": "unhealthy",
                    "error": str(e),
                    "timestamp": "2025-07-31T15:30:00.000000"
                }
            )
    
    # API信息端点
    @app.get("/info")
    async def api_info():
        """API信息"""
        return {
            "title": app.state.config['title'],
            "version": app.state.config['version'],
            "description": app.state.config['description'],
            "documentation_url": "/docs",
            "openapi_spec_url": "/openapi.json",
            "websocket_url": "/ws",
            "endpoints": {
                "auth": f"{api_prefix}/auth",
                "config": f"{api_prefix}/config",
                "trading": f"{api_prefix}/trading",
                "monitoring": f"{api_prefix}/monitoring"
            },
            "authentication": {
                "jwt": {
                    "header": "Authorization",
                    "format": "Bearer <token>"
                },
                "api_key": {
                    "header": "X-API-Key",
                    "format": "<api_key>"
                }
            }
        }


def setup_exception_handlers(app: FastAPI):
    """设置异常处理器"""
    
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        """HTTP异常处理器"""
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "error": exc.detail,
                "status_code": exc.status_code,
                "path": str(request.url.path),
                "method": request.method,
                "timestamp": "2025-07-31T15:30:00.000000"
            }
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        """通用异常处理器"""
        logger.error(f"未处理的异常: {exc}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={
                "error": "Internal Server Error",
                "message": "服务器内部错误",
                "status_code": 500,
                "path": str(request.url.path),
                "method": request.method,
                "timestamp": "2025-07-31T15:30:00.000000"
            }
        )


def run_app(
    app: FastAPI = None,
    host: str = "0.0.0.0",
    port: int = 8000,
    workers: int = 1,
    reload: bool = False
):
    """
    运行FastAPI应用
    
    Args:
        app: FastAPI应用实例
        host: 主机地址
        port: 端口号
        workers: 工作进程数
        reload: 是否启用热重载
    """
    if app is None:
        app = create_app()
    
    logger.info(f"启动FastAPI服务器: http://{host}:{port}")
    
    uvicorn.run(
        app,
        host=host,
        port=port,
        workers=workers,
        reload=reload,
        access_log=True,
        log_level="info"
    )


if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 创建并运行应用
    app = create_app()
    run_app(app, reload=True)
