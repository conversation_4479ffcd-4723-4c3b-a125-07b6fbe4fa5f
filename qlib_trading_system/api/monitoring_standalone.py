"""
独立的API监控系统（不依赖Flask）
Standalone API Monitoring System (Flask-independent)

用于测试的独立监控系统
"""

import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
from threading import Lock
import logging

logger = logging.getLogger(__name__)


@dataclass
class APIRequest:
    """API请求记录"""
    request_id: str
    endpoint: str
    method: str
    user_id: Optional[str]
    ip_address: str
    user_agent: str
    request_time: datetime
    response_time: Optional[datetime]
    duration_ms: Optional[float]
    status_code: Optional[int]
    request_size: int
    response_size: Optional[int]
    error_message: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['request_time'] = self.request_time.isoformat()
        if self.response_time:
            data['response_time'] = self.response_time.isoformat()
        return data


@dataclass
class APIMetrics:
    """API指标"""
    total_requests: int
    successful_requests: int
    failed_requests: int
    avg_response_time: float
    min_response_time: float
    max_response_time: float
    requests_per_minute: float
    error_rate: float
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)


class RateLimiter:
    """速率限制器"""
    
    def __init__(self, max_requests: int = 100, window_seconds: int = 60):
        """初始化速率限制器"""
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.requests = defaultdict(deque)
        self.lock = Lock()
    
    def is_allowed(self, user_id: str) -> bool:
        """检查是否允许请求"""
        with self.lock:
            now = time.time()
            user_requests = self.requests[user_id]
            
            # 清理过期的请求记录
            while user_requests and user_requests[0] < now - self.window_seconds:
                user_requests.popleft()
            
            # 检查是否超过限制
            if len(user_requests) >= self.max_requests:
                return False
            
            # 记录当前请求
            user_requests.append(now)
            return True
    
    def get_remaining_requests(self, user_id: str) -> int:
        """获取剩余请求数"""
        with self.lock:
            now = time.time()
            user_requests = self.requests[user_id]
            
            # 清理过期的请求记录
            while user_requests and user_requests[0] < now - self.window_seconds:
                user_requests.popleft()
            
            return max(0, self.max_requests - len(user_requests))


class APIMonitor:
    """API监控器"""
    
    def __init__(self, max_history: int = 10000):
        """初始化API监控器"""
        self.max_history = max_history
        
        # 请求历史记录
        self.request_history: deque = deque(maxlen=max_history)
        
        # 实时统计
        self.endpoint_stats = defaultdict(lambda: {
            'count': 0,
            'total_time': 0.0,
            'error_count': 0,
            'last_request': None
        })
        
        # 用户统计
        self.user_stats = defaultdict(lambda: {
            'request_count': 0,
            'error_count': 0,
            'total_time': 0.0,
            'last_request': None
        })
        
        # 速率限制器
        self.rate_limiter = RateLimiter()
        
        # 线程锁
        self.lock = Lock()
        
        logger.info("API监控器初始化完成")
    
    def start_request(self, endpoint: str, method: str, user_id: Optional[str] = None) -> str:
        """开始请求监控"""
        request_id = f"req_{int(time.time() * 1000000)}"
        
        # 创建请求记录
        api_request = APIRequest(
            request_id=request_id,
            endpoint=endpoint,
            method=method,
            user_id=user_id,
            ip_address="127.0.0.1",
            user_agent="test-client",
            request_time=datetime.now(),
            response_time=None,
            duration_ms=None,
            status_code=None,
            request_size=0,
            response_size=None
        )
        
        # 模拟存储当前请求
        self._current_request = api_request
        
        return request_id
    
    def end_request(
        self, 
        request_id: str, 
        status_code: int, 
        response_size: int = 0,
        error_message: Optional[str] = None
    ):
        """结束请求监控"""
        if not hasattr(self, '_current_request'):
            return
        
        api_request = self._current_request
        
        # 更新请求记录
        api_request.response_time = datetime.now()
        api_request.duration_ms = (
            api_request.response_time - api_request.request_time
        ).total_seconds() * 1000
        api_request.status_code = status_code
        api_request.response_size = response_size
        api_request.error_message = error_message
        
        # 添加到历史记录
        with self.lock:
            self.request_history.append(api_request)
            
            # 更新端点统计
            endpoint_key = f"{api_request.method} {api_request.endpoint}"
            stats = self.endpoint_stats[endpoint_key]
            stats['count'] += 1
            stats['total_time'] += api_request.duration_ms
            stats['last_request'] = api_request.request_time
            
            if status_code >= 400:
                stats['error_count'] += 1
            
            # 更新用户统计
            if api_request.user_id:
                user_stats = self.user_stats[api_request.user_id]
                user_stats['request_count'] += 1
                user_stats['total_time'] += api_request.duration_ms
                user_stats['last_request'] = api_request.request_time
                
                if status_code >= 400:
                    user_stats['error_count'] += 1
    
    def get_metrics(self, time_range_minutes: int = 60) -> APIMetrics:
        """获取API指标"""
        with self.lock:
            # 过滤时间范围内的请求
            cutoff_time = datetime.now() - timedelta(minutes=time_range_minutes)
            recent_requests = [
                req for req in self.request_history
                if req.request_time >= cutoff_time and req.duration_ms is not None
            ]
            
            if not recent_requests:
                return APIMetrics(
                    total_requests=0,
                    successful_requests=0,
                    failed_requests=0,
                    avg_response_time=0.0,
                    min_response_time=0.0,
                    max_response_time=0.0,
                    requests_per_minute=0.0,
                    error_rate=0.0
                )
            
            # 计算指标
            total_requests = len(recent_requests)
            successful_requests = sum(1 for req in recent_requests if req.status_code < 400)
            failed_requests = total_requests - successful_requests
            
            response_times = [req.duration_ms for req in recent_requests]
            avg_response_time = sum(response_times) / len(response_times)
            min_response_time = min(response_times)
            max_response_time = max(response_times)
            
            requests_per_minute = total_requests / time_range_minutes
            error_rate = failed_requests / total_requests if total_requests > 0 else 0.0
            
            return APIMetrics(
                total_requests=total_requests,
                successful_requests=successful_requests,
                failed_requests=failed_requests,
                avg_response_time=avg_response_time,
                min_response_time=min_response_time,
                max_response_time=max_response_time,
                requests_per_minute=requests_per_minute,
                error_rate=error_rate
            )
    
    def get_endpoint_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取端点统计"""
        with self.lock:
            stats = {}
            for endpoint, data in self.endpoint_stats.items():
                avg_time = data['total_time'] / data['count'] if data['count'] > 0 else 0
                error_rate = data['error_count'] / data['count'] if data['count'] > 0 else 0
                
                stats[endpoint] = {
                    'request_count': data['count'],
                    'avg_response_time': avg_time,
                    'error_count': data['error_count'],
                    'error_rate': error_rate,
                    'last_request': data['last_request'].isoformat() if data['last_request'] else None
                }
            
            return stats
    
    def get_user_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取用户统计"""
        with self.lock:
            stats = {}
            for user_id, data in self.user_stats.items():
                avg_time = data['total_time'] / data['request_count'] if data['request_count'] > 0 else 0
                error_rate = data['error_count'] / data['request_count'] if data['request_count'] > 0 else 0
                
                stats[user_id] = {
                    'request_count': data['request_count'],
                    'avg_response_time': avg_time,
                    'error_count': data['error_count'],
                    'error_rate': error_rate,
                    'last_request': data['last_request'].isoformat() if data['last_request'] else None
                }
            
            return stats
    
    def get_slow_requests(self, threshold_ms: float = 1000, limit: int = 10) -> List[Dict[str, Any]]:
        """获取慢请求"""
        with self.lock:
            slow_requests = [
                req for req in self.request_history
                if req.duration_ms and req.duration_ms > threshold_ms
            ]
            
            # 按响应时间倒序排列
            slow_requests.sort(key=lambda x: x.duration_ms, reverse=True)
            
            return [req.to_dict() for req in slow_requests[:limit]]
    
    def get_error_requests(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取错误请求"""
        with self.lock:
            error_requests = [
                req for req in self.request_history
                if req.status_code and req.status_code >= 400
            ]
            
            # 按时间倒序排列
            error_requests.sort(key=lambda x: x.request_time, reverse=True)
            
            return [req.to_dict() for req in error_requests[:limit]]
    
    def check_rate_limit(self, user_id: str) -> bool:
        """检查速率限制"""
        return self.rate_limiter.is_allowed(user_id)
    
    def get_rate_limit_info(self, user_id: str) -> Dict[str, Any]:
        """获取速率限制信息"""
        return {
            'max_requests': self.rate_limiter.max_requests,
            'window_seconds': self.rate_limiter.window_seconds,
            'remaining_requests': self.rate_limiter.get_remaining_requests(user_id)
        }
