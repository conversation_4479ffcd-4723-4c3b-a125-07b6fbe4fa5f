"""
API路由定义
API Routes Definition

定义所有API端点和路由
"""

import json
from flask import Flask, Blueprint, request, jsonify
from .auth import AuthManager, require_auth, require_api_key, require_permission, Permission, UserRole
from .monitoring import APIMonitor, monitor_api
from .documentation import APIDocGenerator, HTTPMethod


def create_api_routes(
    app: Flask, 
    auth_manager: AuthManager, 
    api_monitor: APIMonitor, 
    doc_generator: APIDocGenerator
):
    """
    创建API路由
    
    Args:
        app: Flask应用实例
        auth_manager: 认证管理器
        api_monitor: API监控器
        doc_generator: 文档生成器
    """
    # 创建API蓝图
    api_bp = Blueprint('api', __name__, url_prefix='/api/v1')
    
    # 注册各个模块的路由
    register_auth_routes(api_bp, auth_manager, api_monitor, doc_generator)
    register_config_routes(api_bp, auth_manager, api_monitor, doc_generator)
    register_monitoring_routes(api_bp, auth_manager, api_monitor, doc_generator)
    register_documentation_routes(api_bp, auth_manager, api_monitor, doc_generator)
    
    # 注册蓝图
    app.register_blueprint(api_bp)


def register_auth_routes(
    bp: Blueprint, 
    auth_manager: AuthManager, 
    api_monitor: APIMonitor, 
    doc_generator: APIDocGenerator
):
    """注册认证相关路由"""
    
    @bp.route('/auth/login', methods=['POST'])
    @monitor_api(api_monitor)
    def login():
        """用户登录"""
        try:
            data = request.get_json()
            if not data or 'username' not in data or 'password' not in data:
                return jsonify({'error': '缺少用户名或密码'}), 400
            
            username = data['username']
            password = data['password']
            
            # 认证用户
            user = auth_manager.authenticate_user(username, password)
            if not user:
                return jsonify({'error': '用户名或密码错误'}), 401
            
            # 生成Token
            token = auth_manager.generate_token(user)
            
            return jsonify({
                'token': token,
                'user': user.to_dict(),
                'expires_in': 24 * 3600  # 24小时
            })
            
        except Exception as e:
            return jsonify({'error': f'登录失败: {str(e)}'}), 500
    
    @bp.route('/auth/logout', methods=['POST'])
    @require_auth(auth_manager)
    @monitor_api(api_monitor)
    def logout():
        """用户登出"""
        # JWT是无状态的，客户端删除token即可
        return jsonify({'message': '登出成功'})
    
    @bp.route('/auth/profile', methods=['GET'])
    @require_auth(auth_manager)
    @monitor_api(api_monitor)
    def get_profile():
        """获取用户信息"""
        return jsonify(request.current_user.to_dict())
    
    @bp.route('/auth/users', methods=['GET'])
    @require_auth(auth_manager)
    @require_permission(Permission.MANAGE_USERS)
    @monitor_api(api_monitor)
    def list_users():
        """列出所有用户"""
        users = auth_manager.list_users()
        return jsonify([user.to_dict() for user in users])
    
    @bp.route('/auth/users', methods=['POST'])
    @require_auth(auth_manager)
    @require_permission(Permission.MANAGE_USERS)
    @monitor_api(api_monitor)
    def create_user():
        """创建用户"""
        try:
            data = request.get_json()
            required_fields = ['username', 'email', 'password', 'role']
            
            if not data or not all(field in data for field in required_fields):
                return jsonify({'error': '缺少必需字段'}), 400
            
            # 验证角色
            try:
                role = UserRole(data['role'])
            except ValueError:
                return jsonify({'error': '无效的用户角色'}), 400
            
            # 创建用户
            user_id = auth_manager.create_user(
                username=data['username'],
                email=data['email'],
                password=data['password'],
                role=role,
                created_by=request.current_user.username
            )
            
            if not user_id:
                return jsonify({'error': '创建用户失败'}), 500
            
            user = auth_manager.get_user_by_id(user_id)
            return jsonify(user.to_dict()), 201
            
        except Exception as e:
            return jsonify({'error': f'创建用户失败: {str(e)}'}), 500
    
    @bp.route('/auth/api-key', methods=['POST'])
    @require_auth(auth_manager)
    @monitor_api(api_monitor)
    def generate_api_key():
        """生成API密钥"""
        try:
            api_key = auth_manager.generate_api_key(request.current_user.user_id)
            if not api_key:
                return jsonify({'error': '生成API密钥失败'}), 500
            
            return jsonify({
                'api_key': api_key,
                'message': 'API密钥生成成功，请妥善保管'
            })
            
        except Exception as e:
            return jsonify({'error': f'生成API密钥失败: {str(e)}'}), 500
    
    # 注册文档
    doc_generator.register_endpoint(
        path='/auth/login',
        method=HTTPMethod.POST,
        summary='用户登录',
        description='使用用户名和密码进行登录认证',
        request_body={
            'content': {
                'application/json': {
                    'schema': {
                        'type': 'object',
                        'properties': {
                            'username': {'type': 'string', 'description': '用户名'},
                            'password': {'type': 'string', 'description': '密码'}
                        },
                        'required': ['username', 'password']
                    },
                    'example': {
                        'username': 'admin',
                        'password': 'admin123'
                    }
                }
            }
        },
        responses={
            200: {
                'description': '登录成功',
                'content': {
                    'application/json': {
                        'schema': {
                            'type': 'object',
                            'properties': {
                                'token': {'type': 'string'},
                                'user': {'type': 'object'},
                                'expires_in': {'type': 'integer'}
                            }
                        }
                    }
                }
            },
            401: {'description': '认证失败'}
        },
        tags=['认证'],
        requires_auth=False
    )


def register_config_routes(
    bp: Blueprint, 
    auth_manager: AuthManager, 
    api_monitor: APIMonitor, 
    doc_generator: APIDocGenerator
):
    """注册配置管理相关路由"""
    
    @bp.route('/config/<level>/<name>', methods=['GET'])
    @require_auth(auth_manager)
    @require_permission(Permission.VIEW_CONFIG)
    @monitor_api(api_monitor)
    def get_config(level, name):
        """获取配置"""
        try:
            # 这里应该集成配置管理系统
            # 暂时返回示例数据
            return jsonify({
                'level': level,
                'name': name,
                'config': {
                    'example_setting': 'example_value'
                },
                'version': 'v1.0.0',
                'last_updated': '2025-07-31T14:36:36.867283'
            })
            
        except Exception as e:
            return jsonify({'error': f'获取配置失败: {str(e)}'}), 500
    
    @bp.route('/config/<level>/<name>', methods=['PUT'])
    @require_auth(auth_manager)
    @require_permission(Permission.MANAGE_CONFIG)
    @monitor_api(api_monitor)
    def update_config(level, name):
        """更新配置"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'error': '缺少配置数据'}), 400
            
            # 这里应该集成配置管理系统
            # 暂时返回示例响应
            return jsonify({
                'message': '配置更新成功',
                'level': level,
                'name': name,
                'version': 'v1.0.1'
            })
            
        except Exception as e:
            return jsonify({'error': f'更新配置失败: {str(e)}'}), 500
    
    # 注册文档
    doc_generator.register_endpoint(
        path='/config/{level}/{name}',
        method=HTTPMethod.GET,
        summary='获取配置',
        description='根据层级和名称获取配置信息',
        parameters=[
            {
                'name': 'level',
                'in': 'path',
                'required': True,
                'schema': {'type': 'string', 'enum': ['global', 'strategy', 'stock']},
                'description': '配置层级'
            },
            {
                'name': 'name',
                'in': 'path',
                'required': True,
                'schema': {'type': 'string'},
                'description': '配置名称'
            }
        ],
        responses={
            200: {'description': '获取成功'},
            404: {'description': '配置不存在'}
        },
        tags=['配置管理'],
        permissions=[Permission.VIEW_CONFIG.value]
    )


def register_monitoring_routes(
    bp: Blueprint, 
    auth_manager: AuthManager, 
    api_monitor: APIMonitor, 
    doc_generator: APIDocGenerator
):
    """注册监控相关路由"""
    
    @bp.route('/monitoring/metrics', methods=['GET'])
    @require_auth(auth_manager)
    @require_permission(Permission.VIEW_LOGS)
    @monitor_api(api_monitor)
    def get_metrics():
        """获取API指标"""
        try:
            time_range = request.args.get('time_range', 60, type=int)
            metrics = api_monitor.get_metrics(time_range_minutes=time_range)
            
            return jsonify(metrics.to_dict())
            
        except Exception as e:
            return jsonify({'error': f'获取指标失败: {str(e)}'}), 500
    
    @bp.route('/monitoring/endpoints', methods=['GET'])
    @require_auth(auth_manager)
    @require_permission(Permission.VIEW_LOGS)
    @monitor_api(api_monitor)
    def get_endpoint_stats():
        """获取端点统计"""
        try:
            stats = api_monitor.get_endpoint_stats()
            return jsonify(stats)
            
        except Exception as e:
            return jsonify({'error': f'获取端点统计失败: {str(e)}'}), 500
    
    @bp.route('/monitoring/users', methods=['GET'])
    @require_auth(auth_manager)
    @require_permission(Permission.VIEW_LOGS)
    @monitor_api(api_monitor)
    def get_user_stats():
        """获取用户统计"""
        try:
            stats = api_monitor.get_user_stats()
            return jsonify(stats)
            
        except Exception as e:
            return jsonify({'error': f'获取用户统计失败: {str(e)}'}), 500
    
    @bp.route('/monitoring/slow-requests', methods=['GET'])
    @require_auth(auth_manager)
    @require_permission(Permission.VIEW_LOGS)
    @monitor_api(api_monitor)
    def get_slow_requests():
        """获取慢请求"""
        try:
            threshold = request.args.get('threshold', 1000, type=float)
            limit = request.args.get('limit', 10, type=int)
            
            slow_requests = api_monitor.get_slow_requests(threshold, limit)
            return jsonify(slow_requests)
            
        except Exception as e:
            return jsonify({'error': f'获取慢请求失败: {str(e)}'}), 500


def register_documentation_routes(
    bp: Blueprint, 
    auth_manager: AuthManager, 
    api_monitor: APIMonitor, 
    doc_generator: APIDocGenerator
):
    """注册文档相关路由"""
    
    @bp.route('/openapi.json', methods=['GET'])
    def get_openapi_spec():
        """获取OpenAPI规范"""
        try:
            spec = doc_generator.generate_openapi_spec()
            return jsonify(spec)
            
        except Exception as e:
            return jsonify({'error': f'生成OpenAPI规范失败: {str(e)}'}), 500
    
    @bp.route('/postman.json', methods=['GET'])
    def get_postman_collection():
        """获取Postman集合"""
        try:
            collection = doc_generator.generate_postman_collection()
            return jsonify(collection)
            
        except Exception as e:
            return jsonify({'error': f'生成Postman集合失败: {str(e)}'}), 500
    
    @bp.route('/client-code/<language>', methods=['GET'])
    def get_client_code(language):
        """获取客户端代码"""
        try:
            code = doc_generator.create_test_client_code(language)
            return jsonify({
                'language': language,
                'code': code
            })
            
        except ValueError as e:
            return jsonify({'error': str(e)}), 400
        except Exception as e:
            return jsonify({'error': f'生成客户端代码失败: {str(e)}'}), 500
    
    @bp.route('/curl-examples', methods=['GET'])
    def get_curl_examples():
        """获取cURL示例"""
        try:
            examples = doc_generator.generate_curl_examples()
            return jsonify(examples)
            
        except Exception as e:
            return jsonify({'error': f'生成cURL示例失败: {str(e)}'}), 500
