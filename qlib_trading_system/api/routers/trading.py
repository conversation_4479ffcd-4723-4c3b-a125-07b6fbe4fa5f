"""
交易路由
Trading Routes

提供交易执行、持仓查询、订单管理等功能
"""

from typing import List, Dict, Any
from fastapi import APIRouter, Depends
from pydantic import BaseModel

from ..auth import User, Permission, get_current_user_from_token, require_permission

router = APIRouter()


class Position(BaseModel):
    """持仓信息"""
    symbol: str
    quantity: int
    avg_price: float
    current_price: float
    pnl: float
    pnl_percent: float


class Order(BaseModel):
    """订单信息"""
    order_id: str
    symbol: str
    side: str
    quantity: int
    price: float
    status: str
    created_time: str


@router.get("/health", summary="交易系统健康检查")
async def trading_health():
    """交易系统健康检查"""
    return {"status": "healthy", "service": "trading-system"}


@router.get("/positions", response_model=List[Position], summary="获取持仓")
async def get_positions(
    current_user: User = Depends(require_permission(Permission.VIEW_POSITIONS))
):
    """获取当前持仓"""
    return [
        Position(
            symbol="000001.SZ",
            quantity=1000,
            avg_price=12.50,
            current_price=12.80,
            pnl=300.0,
            pnl_percent=2.4
        ),
        Position(
            symbol="600000.SH",
            quantity=500,
            avg_price=8.20,
            current_price=8.15,
            pnl=-25.0,
            pnl_percent=-0.6
        )
    ]


@router.get("/orders", response_model=List[Order], summary="获取订单")
async def get_orders(
    current_user: User = Depends(require_permission(Permission.MANAGE_ORDERS))
):
    """获取订单列表"""
    return [
        Order(
            order_id="ORD001",
            symbol="000001.SZ",
            side="buy",
            quantity=1000,
            price=12.50,
            status="filled",
            created_time="2025-07-31T09:30:00.000000"
        ),
        Order(
            order_id="ORD002",
            symbol="600000.SH",
            side="sell",
            quantity=200,
            price=8.25,
            status="pending",
            created_time="2025-07-31T10:15:00.000000"
        )
    ]
