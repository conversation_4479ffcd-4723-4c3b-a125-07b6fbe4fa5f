"""
WebSocket路由
WebSocket Routes

提供WebSocket连接管理和实时数据推送
"""

from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, Query
from typing import Optional
import json
import logging

logger = logging.getLogger(__name__)

router = APIRouter()


@router.websocket("/ws")
async def websocket_endpoint(
    websocket: WebSocket,
    token: Optional[str] = Query(None),
    connection_type: str = Query("client")
):
    """
    WebSocket连接端点
    
    Args:
        websocket: WebSocket连接
        token: 认证token（可选）
        connection_type: 连接类型
    """
    try:
        # 生成连接ID
        import time
        connection_id = f"ws_{int(time.time() * 1000000)}"
        
        # 获取WebSocket管理器
        websocket_manager = getattr(websocket.app.state, 'websocket_manager', None)
        if not websocket_manager:
            await websocket.close(code=1011, reason="WebSocket服务未启用")
            return
        
        # 建立连接
        from ..websocket import ConnectionType
        conn_type = ConnectionType.CLIENT
        if connection_type == "admin":
            conn_type = ConnectionType.ADMIN
        elif connection_type == "trader":
            conn_type = ConnectionType.TRADER
        elif connection_type == "monitor":
            conn_type = ConnectionType.MONITOR
        
        connection = await websocket_manager.connect(
            websocket=websocket,
            connection_id=connection_id,
            user_id=None,  # 这里可以从token解析用户ID
            connection_type=conn_type
        )
        
        logger.info(f"WebSocket连接建立: {connection_id}")
        
        # 消息循环
        while True:
            try:
                # 接收消息
                data = await websocket.receive_text()
                
                # 处理消息
                await websocket_manager.handle_message(connection_id, data)
                
            except WebSocketDisconnect:
                logger.info(f"WebSocket连接断开: {connection_id}")
                break
            except Exception as e:
                logger.error(f"WebSocket消息处理错误: {e}")
                await websocket_manager.send_to_connection(
                    connection_id,
                    "error",
                    {"error": f"消息处理失败: {str(e)}"}
                )
    
    except Exception as e:
        logger.error(f"WebSocket连接错误: {e}")
        try:
            await websocket.close(code=1011, reason=f"连接错误: {str(e)}")
        except:
            pass
    
    finally:
        # 清理连接
        if 'websocket_manager' in locals() and 'connection_id' in locals():
            await websocket_manager.disconnect(connection_id)


@router.get("/ws/stats", summary="获取WebSocket统计")
async def get_websocket_stats(websocket_manager=Depends(lambda: None)):
    """获取WebSocket连接统计"""
    # 这里应该从app.state获取websocket_manager
    # 为了演示，返回模拟数据
    return {
        "active_connections": 5,
        "total_connections": 25,
        "messages_sent": 1250,
        "messages_received": 890,
        "topics": ["config_updates", "market_data", "trade_signals"],
        "connection_types": {
            "client": 3,
            "admin": 1,
            "trader": 1,
            "monitor": 0
        }
    }
