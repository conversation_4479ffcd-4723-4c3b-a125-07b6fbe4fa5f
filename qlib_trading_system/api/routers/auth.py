"""
认证路由
Authentication Routes

提供用户认证、权限管理、API密钥管理等功能
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Request
from pydantic import BaseModel, EmailStr

from ..auth import (
    AuthManager, User, UserRole, Permission,
    get_current_user_from_token, get_current_user_from_api_key,
    require_permission
)

router = APIRouter()


# Pydantic模型
class LoginRequest(BaseModel):
    """登录请求"""
    username: str
    password: str


class LoginResponse(BaseModel):
    """登录响应"""
    token: str
    user: dict
    expires_in: int


class CreateUserRequest(BaseModel):
    """创建用户请求"""
    username: str
    email: EmailStr
    password: str
    role: str


class UserResponse(BaseModel):
    """用户响应"""
    user_id: str
    username: str
    email: str
    role: str
    permissions: List[str]
    is_active: bool
    created_time: str
    last_login: Optional[str] = None
    has_api_key: bool


class APIKeyResponse(BaseModel):
    """API密钥响应"""
    api_key: str
    message: str


def get_auth_manager(request: Request) -> AuthManager:
    """获取认证管理器依赖"""
    return request.app.state.auth_manager


@router.post("/login", response_model=LoginResponse, summary="用户登录")
async def login(
    login_data: LoginRequest,
    auth_manager: AuthManager = Depends(get_auth_manager)
):
    """
    用户登录
    
    使用用户名和密码进行登录认证，返回JWT Token
    """
    user = await auth_manager.authenticate_user(login_data.username, login_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    token = auth_manager.generate_token(user)
    
    return LoginResponse(
        token=token,
        user=user.to_dict(),
        expires_in=24 * 3600  # 24小时
    )


@router.post("/logout", summary="用户登出")
async def logout(current_user: User = Depends(get_current_user_from_token)):
    """
    用户登出
    
    JWT是无状态的，客户端删除token即可
    """
    return {"message": "登出成功", "user": current_user.username}


@router.get("/profile", response_model=UserResponse, summary="获取用户信息")
async def get_profile(current_user: User = Depends(get_current_user_from_token)):
    """
    获取当前用户信息
    
    返回当前登录用户的详细信息
    """
    return UserResponse(**current_user.to_dict())


@router.get("/users", response_model=List[UserResponse], summary="列出所有用户")
async def list_users(
    current_user: User = Depends(require_permission(Permission.MANAGE_USERS)),
    auth_manager: AuthManager = Depends(get_auth_manager)
):
    """
    列出所有用户
    
    需要用户管理权限
    """
    # 这里需要实现获取所有用户的逻辑
    # 由于当前使用Redis存储，需要扫描所有用户键
    # 为了简化，返回当前用户信息
    return [UserResponse(**current_user.to_dict())]


@router.post("/users", response_model=UserResponse, summary="创建用户")
async def create_user(
    user_data: CreateUserRequest,
    current_user: User = Depends(require_permission(Permission.MANAGE_USERS)),
    auth_manager: AuthManager = Depends(get_auth_manager)
):
    """
    创建新用户
    
    需要用户管理权限
    """
    try:
        # 验证角色
        try:
            role = UserRole(user_data.role)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"无效的用户角色: {user_data.role}"
            )
        
        # 创建用户
        user_id = await auth_manager.create_user(
            username=user_data.username,
            email=user_data.email,
            password=user_data.password,
            role=role,
            created_by=current_user.username
        )
        
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="创建用户失败"
            )
        
        # 获取创建的用户信息
        new_user = await auth_manager.get_user_by_id(user_id)
        if not new_user:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="获取新创建的用户信息失败"
            )
        
        return UserResponse(**new_user.to_dict())
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建用户失败: {str(e)}"
        )


@router.post("/api-key", response_model=APIKeyResponse, summary="生成API密钥")
async def generate_api_key(
    current_user: User = Depends(get_current_user_from_token),
    auth_manager: AuthManager = Depends(get_auth_manager)
):
    """
    生成API密钥
    
    为当前用户生成新的API密钥
    """
    api_key = await auth_manager.generate_api_key(current_user.user_id)
    if not api_key:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="生成API密钥失败"
        )
    
    return APIKeyResponse(
        api_key=api_key,
        message="API密钥生成成功，请妥善保管"
    )


@router.get("/verify-token", summary="验证Token")
async def verify_token(current_user: User = Depends(get_current_user_from_token)):
    """
    验证JWT Token
    
    检查当前Token是否有效
    """
    return {
        "valid": True,
        "user": current_user.to_dict(),
        "message": "Token有效"
    }


@router.get("/verify-api-key", summary="验证API密钥")
async def verify_api_key(current_user: User = Depends(get_current_user_from_api_key)):
    """
    验证API密钥
    
    检查当前API密钥是否有效
    """
    return {
        "valid": True,
        "user": current_user.to_dict(),
        "message": "API密钥有效"
    }


@router.get("/permissions", summary="获取权限列表")
async def get_permissions():
    """
    获取所有可用权限列表
    
    返回系统中定义的所有权限
    """
    return {
        "permissions": [
            {
                "name": perm.name,
                "value": perm.value,
                "description": _get_permission_description(perm)
            }
            for perm in Permission
        ]
    }


@router.get("/roles", summary="获取角色列表")
async def get_roles():
    """
    获取所有可用角色列表
    
    返回系统中定义的所有角色及其权限
    """
    from ..auth import AuthManager
    
    # 创建临时认证管理器实例来获取角色权限映射
    temp_auth = AuthManager("temp", "redis://localhost")
    role_permissions = temp_auth._init_role_permissions()
    
    return {
        "roles": [
            {
                "name": role.name,
                "value": role.value,
                "description": _get_role_description(role),
                "permissions": [perm.value for perm in permissions]
            }
            for role, permissions in role_permissions.items()
        ]
    }


def _get_permission_description(permission: Permission) -> str:
    """获取权限描述"""
    descriptions = {
        Permission.MANAGE_CONFIG: "管理系统配置",
        Permission.VIEW_CONFIG: "查看系统配置",
        Permission.EXECUTE_TRADE: "执行交易操作",
        Permission.VIEW_POSITIONS: "查看持仓信息",
        Permission.MANAGE_ORDERS: "管理交易订单",
        Permission.VIEW_MARKET_DATA: "查看市场数据",
        Permission.EXPORT_DATA: "导出数据",
        Permission.SYSTEM_ADMIN: "系统管理员权限",
        Permission.VIEW_LOGS: "查看系统日志",
        Permission.MANAGE_USERS: "管理用户",
        Permission.API_ACCESS: "API访问权限",
        Permission.API_ADMIN: "API管理权限"
    }
    return descriptions.get(permission, "未知权限")


def _get_role_description(role: UserRole) -> str:
    """获取角色描述"""
    descriptions = {
        UserRole.ADMIN: "系统管理员，拥有所有权限",
        UserRole.TRADER: "交易员，可以执行交易和查看相关数据",
        UserRole.ANALYST: "分析师，可以查看和分析数据",
        UserRole.VIEWER: "查看者，只能查看基本信息",
        UserRole.API_USER: "API用户，通过API访问系统"
    }
    return descriptions.get(role, "未知角色")
