"""
监控路由
Monitoring Routes

提供系统监控、性能指标、健康检查等功能
"""

from typing import Dict, Any
from fastapi import APIRouter, Depends, Request
from pydantic import BaseModel

from ..auth import User, Permission, get_current_user_from_token, require_permission

router = APIRouter()


class SystemMetrics(BaseModel):
    """系统指标"""
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    network_io: Dict[str, float]
    active_connections: int
    requests_per_minute: float


@router.get("/health", summary="系统健康检查")
async def system_health():
    """系统健康检查"""
    return {
        "status": "healthy",
        "timestamp": "2025-07-31T15:30:00.000000",
        "services": {
            "api": "healthy",
            "database": "healthy",
            "redis": "healthy",
            "rabbitmq": "healthy"
        }
    }


@router.get("/metrics", response_model=SystemMetrics, summary="获取系统指标")
async def get_metrics(
    current_user: User = Depends(require_permission(Permission.VIEW_LOGS))
):
    """获取系统性能指标"""
    return SystemMetrics(
        cpu_usage=45.2,
        memory_usage=67.8,
        disk_usage=23.4,
        network_io={"in": 1024.5, "out": 2048.3},
        active_connections=15,
        requests_per_minute=120.5
    )


@router.get("/api-stats", summary="获取API统计")
async def get_api_stats(
    request: Request,
    current_user: User = Depends(require_permission(Permission.VIEW_LOGS))
):
    """获取API统计信息"""
    api_monitor = getattr(request.app.state, 'api_monitor', None)
    if api_monitor:
        metrics = await api_monitor.get_metrics()
        return metrics.to_dict()
    
    return {"message": "API监控未启用"}
