"""
FastAPI认证和访问控制系统
FastAPI Authentication and Access Control System

实现JWT认证、权限管理和访问控制功能，支持异步操作
"""

import jwt
import hashlib
import secrets
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass
from enum import Enum
import logging

from fastapi import HTTPException, Depends, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials, APIKeyHeader
from passlib.context import CryptContext
import redis.asyncio as redis

logger = logging.getLogger(__name__)


class UserRole(Enum):
    """用户角色枚举"""
    ADMIN = "admin"
    TRADER = "trader"
    ANALYST = "analyst"
    VIEWER = "viewer"
    API_USER = "api_user"


class Permission(Enum):
    """权限枚举"""
    # 配置管理权限
    MANAGE_CONFIG = "manage_config"
    VIEW_CONFIG = "view_config"
    
    # 交易权限
    EXECUTE_TRADE = "execute_trade"
    VIEW_POSITIONS = "view_positions"
    MANAGE_ORDERS = "manage_orders"
    
    # 数据权限
    VIEW_MARKET_DATA = "view_market_data"
    EXPORT_DATA = "export_data"
    
    # 系统权限
    SYSTEM_ADMIN = "system_admin"
    VIEW_LOGS = "view_logs"
    MANAGE_USERS = "manage_users"
    
    # API权限
    API_ACCESS = "api_access"
    API_ADMIN = "api_admin"


@dataclass
class User:
    """用户信息"""
    user_id: str
    username: str
    email: str
    role: UserRole
    permissions: List[Permission]
    is_active: bool
    created_time: datetime
    last_login: Optional[datetime] = None
    api_key: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "user_id": self.user_id,
            "username": self.username,
            "email": self.email,
            "role": self.role.value,
            "permissions": [p.value for p in self.permissions],
            "is_active": self.is_active,
            "created_time": self.created_time.isoformat(),
            "last_login": self.last_login.isoformat() if self.last_login else None,
            "has_api_key": bool(self.api_key)
        }


class AuthManager:
    """异步认证管理器"""
    
    def __init__(self, secret_key: str, redis_url: str, token_expiry_hours: int = 24):
        """
        初始化认证管理器
        
        Args:
            secret_key: JWT密钥
            redis_url: Redis连接URL
            token_expiry_hours: Token过期时间（小时）
        """
        self.secret_key = secret_key
        self.token_expiry_hours = token_expiry_hours
        self.redis_url = redis_url
        
        # 密码加密上下文
        self.pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        
        # Redis连接
        self.redis_client: Optional[redis.Redis] = None
        
        # 角色权限映射
        self.role_permissions = self._init_role_permissions()
        
        logger.info("FastAPI认证管理器初始化完成")
    
    async def initialize(self):
        """异步初始化"""
        try:
            # 连接Redis
            self.redis_client = redis.from_url(self.redis_url)
            await self.redis_client.ping()
            
            # 创建默认管理员用户
            await self._create_default_admin()
            
            logger.info("认证管理器异步初始化完成")
            
        except Exception as e:
            logger.error(f"认证管理器初始化失败: {e}")
            raise
    
    async def cleanup(self):
        """清理资源"""
        if self.redis_client:
            await self.redis_client.close()
    
    async def health_check(self) -> str:
        """健康检查"""
        try:
            if self.redis_client:
                await self.redis_client.ping()
                return "healthy"
            return "unhealthy"
        except Exception:
            return "unhealthy"
    
    def _init_role_permissions(self) -> Dict[UserRole, List[Permission]]:
        """初始化角色权限映射"""
        return {
            UserRole.ADMIN: [
                Permission.SYSTEM_ADMIN,
                Permission.MANAGE_CONFIG,
                Permission.VIEW_CONFIG,
                Permission.EXECUTE_TRADE,
                Permission.VIEW_POSITIONS,
                Permission.MANAGE_ORDERS,
                Permission.VIEW_MARKET_DATA,
                Permission.EXPORT_DATA,
                Permission.VIEW_LOGS,
                Permission.MANAGE_USERS,
                Permission.API_ACCESS,
                Permission.API_ADMIN
            ],
            UserRole.TRADER: [
                Permission.VIEW_CONFIG,
                Permission.EXECUTE_TRADE,
                Permission.VIEW_POSITIONS,
                Permission.MANAGE_ORDERS,
                Permission.VIEW_MARKET_DATA,
                Permission.API_ACCESS
            ],
            UserRole.ANALYST: [
                Permission.VIEW_CONFIG,
                Permission.VIEW_POSITIONS,
                Permission.VIEW_MARKET_DATA,
                Permission.EXPORT_DATA,
                Permission.API_ACCESS
            ],
            UserRole.VIEWER: [
                Permission.VIEW_CONFIG,
                Permission.VIEW_POSITIONS,
                Permission.VIEW_MARKET_DATA,
                Permission.API_ACCESS
            ],
            UserRole.API_USER: [
                Permission.API_ACCESS,
                Permission.VIEW_MARKET_DATA,
                Permission.VIEW_POSITIONS
            ]
        }
    
    async def _create_default_admin(self):
        """创建默认管理员用户"""
        admin_user_data = {
            "user_id": "admin_001",
            "username": "admin",
            "email": "<EMAIL>",
            "role": UserRole.ADMIN.value,
            "permissions": [p.value for p in self.role_permissions[UserRole.ADMIN]],
            "is_active": True,
            "created_time": datetime.now().isoformat()
        }
        
        # 存储到Redis
        await self.redis_client.hset(
            "users:admin_001",
            mapping=admin_user_data
        )
        
        await self.redis_client.hset(
            "username_to_id",
            "admin",
            "admin_001"
        )
        
        # 设置默认密码
        await self._set_user_password("admin_001", "admin123")
        
        logger.info("默认管理员用户已创建")
    
    def _hash_password(self, password: str) -> str:
        """密码哈希"""
        return self.pwd_context.hash(password)
    
    def _verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """验证密码"""
        return self.pwd_context.verify(plain_password, hashed_password)
    
    async def _set_user_password(self, user_id: str, password: str):
        """设置用户密码"""
        hashed_password = self._hash_password(password)
        await self.redis_client.hset(
            "user_passwords",
            user_id,
            hashed_password
        )
    
    async def create_user(
        self,
        username: str,
        email: str,
        password: str,
        role: UserRole,
        created_by: str
    ) -> Optional[str]:
        """
        创建用户
        
        Args:
            username: 用户名
            email: 邮箱
            password: 密码
            role: 角色
            created_by: 创建者
            
        Returns:
            用户ID，失败返回None
        """
        try:
            # 检查用户名是否已存在
            existing_user_id = await self.redis_client.hget("username_to_id", username)
            if existing_user_id:
                raise ValueError(f"用户名已存在: {username}")
            
            # 生成用户ID
            user_count = await self.redis_client.hlen("username_to_id")
            user_id = f"{role.value}_{user_count + 1:03d}"
            
            # 创建用户数据
            user_data = {
                "user_id": user_id,
                "username": username,
                "email": email,
                "role": role.value,
                "permissions": [p.value for p in self.role_permissions.get(role, [])],
                "is_active": True,
                "created_time": datetime.now().isoformat()
            }
            
            # 存储用户
            await self.redis_client.hset(f"users:{user_id}", mapping=user_data)
            await self.redis_client.hset("username_to_id", username, user_id)
            
            # 设置密码
            await self._set_user_password(user_id, password)
            
            logger.info(f"用户创建成功: {username} ({user_id}), 创建者: {created_by}")
            return user_id
            
        except Exception as e:
            logger.error(f"创建用户失败: {e}")
            return None
    
    async def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """
        用户认证
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            用户对象，认证失败返回None
        """
        try:
            # 获取用户ID
            user_id = await self.redis_client.hget("username_to_id", username)
            if not user_id:
                return None
            
            user_id = user_id.decode() if isinstance(user_id, bytes) else user_id
            
            # 获取用户数据
            user_data = await self.redis_client.hgetall(f"users:{user_id}")
            if not user_data:
                return None
            
            # 解码Redis数据
            user_data = {k.decode() if isinstance(k, bytes) else k: 
                        v.decode() if isinstance(v, bytes) else v 
                        for k, v in user_data.items()}
            
            # 检查用户是否活跃
            if not user_data.get('is_active', 'false').lower() == 'true':
                return None
            
            # 验证密码
            stored_password = await self.redis_client.hget("user_passwords", user_id)
            if not stored_password:
                return None
            
            stored_password = stored_password.decode() if isinstance(stored_password, bytes) else stored_password
            
            if not self._verify_password(password, stored_password):
                return None
            
            # 创建用户对象
            user = User(
                user_id=user_data['user_id'],
                username=user_data['username'],
                email=user_data['email'],
                role=UserRole(user_data['role']),
                permissions=[Permission(p) for p in eval(user_data['permissions'])],
                is_active=user_data['is_active'].lower() == 'true',
                created_time=datetime.fromisoformat(user_data['created_time']),
                last_login=datetime.now()
            )
            
            # 更新最后登录时间
            await self.redis_client.hset(
                f"users:{user_id}",
                "last_login",
                datetime.now().isoformat()
            )
            
            logger.info(f"用户认证成功: {username}")
            return user
            
        except Exception as e:
            logger.error(f"用户认证失败: {e}")
            return None
    
    def generate_token(self, user: User) -> str:
        """
        生成JWT Token
        
        Args:
            user: 用户对象
            
        Returns:
            JWT Token
        """
        payload = {
            'user_id': user.user_id,
            'username': user.username,
            'role': user.role.value,
            'permissions': [p.value for p in user.permissions],
            'exp': datetime.utcnow() + timedelta(hours=self.token_expiry_hours),
            'iat': datetime.utcnow()
        }
        
        return jwt.encode(payload, self.secret_key, algorithm='HS256')
    
    async def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """
        验证JWT Token
        
        Args:
            token: JWT Token
            
        Returns:
            Token载荷，验证失败返回None
        """
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=['HS256'])
            
            # 检查用户是否仍然活跃
            user_id = payload.get('user_id')
            user_data = await self.redis_client.hgetall(f"users:{user_id}")
            
            if not user_data:
                return None
            
            # 解码数据
            user_data = {k.decode() if isinstance(k, bytes) else k: 
                        v.decode() if isinstance(v, bytes) else v 
                        for k, v in user_data.items()}
            
            if not user_data.get('is_active', 'false').lower() == 'true':
                return None
            
            return payload
            
        except jwt.ExpiredSignatureError:
            logger.warning("Token已过期")
            return None
        except jwt.InvalidTokenError as e:
            logger.warning(f"Token无效: {e}")
            return None
    
    async def generate_api_key(self, user_id: str) -> Optional[str]:
        """
        生成API密钥
        
        Args:
            user_id: 用户ID
            
        Returns:
            API密钥，失败返回None
        """
        try:
            # 检查用户是否存在
            user_data = await self.redis_client.hgetall(f"users:{user_id}")
            if not user_data:
                raise ValueError(f"用户不存在: {user_id}")
            
            # 生成API密钥
            api_key = f"ak_{secrets.token_urlsafe(32)}"
            
            # 存储API密钥
            await self.redis_client.hset(f"users:{user_id}", "api_key", api_key)
            await self.redis_client.hset("api_keys", api_key, user_id)
            
            logger.info(f"API密钥已生成: {user_id}")
            return api_key
            
        except Exception as e:
            logger.error(f"生成API密钥失败: {e}")
            return None
    
    async def verify_api_key(self, api_key: str) -> Optional[User]:
        """
        验证API密钥
        
        Args:
            api_key: API密钥
            
        Returns:
            用户对象，验证失败返回None
        """
        try:
            # 获取用户ID
            user_id = await self.redis_client.hget("api_keys", api_key)
            if not user_id:
                return None
            
            user_id = user_id.decode() if isinstance(user_id, bytes) else user_id
            
            # 获取用户数据
            user_data = await self.redis_client.hgetall(f"users:{user_id}")
            if not user_data:
                return None
            
            # 解码数据
            user_data = {k.decode() if isinstance(k, bytes) else k: 
                        v.decode() if isinstance(v, bytes) else v 
                        for k, v in user_data.items()}
            
            # 检查用户是否活跃
            if not user_data.get('is_active', 'false').lower() == 'true':
                return None
            
            # 创建用户对象
            user = User(
                user_id=user_data['user_id'],
                username=user_data['username'],
                email=user_data['email'],
                role=UserRole(user_data['role']),
                permissions=[Permission(p) for p in eval(user_data['permissions'])],
                is_active=True,
                created_time=datetime.fromisoformat(user_data['created_time']),
                api_key=api_key
            )
            
            return user
            
        except Exception as e:
            logger.error(f"API密钥验证失败: {e}")
            return None
    
    def has_permission(self, user: User, permission: Permission) -> bool:
        """
        检查用户权限
        
        Args:
            user: 用户对象
            permission: 权限
            
        Returns:
            是否有权限
        """
        return permission in user.permissions
    
    async def get_user_by_id(self, user_id: str) -> Optional[User]:
        """根据ID获取用户"""
        try:
            user_data = await self.redis_client.hgetall(f"users:{user_id}")
            if not user_data:
                return None
            
            # 解码数据
            user_data = {k.decode() if isinstance(k, bytes) else k: 
                        v.decode() if isinstance(v, bytes) else v 
                        for k, v in user_data.items()}
            
            return User(
                user_id=user_data['user_id'],
                username=user_data['username'],
                email=user_data['email'],
                role=UserRole(user_data['role']),
                permissions=[Permission(p) for p in eval(user_data['permissions'])],
                is_active=user_data['is_active'].lower() == 'true',
                created_time=datetime.fromisoformat(user_data['created_time']),
                last_login=datetime.fromisoformat(user_data['last_login']) if user_data.get('last_login') else None,
                api_key=user_data.get('api_key')
            )
            
        except Exception as e:
            logger.error(f"获取用户失败: {e}")
            return None


# FastAPI依赖项
security = HTTPBearer()
api_key_header = APIKeyHeader(name="X-API-Key")


async def get_current_user_from_token(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    auth_manager: AuthManager = Depends()
) -> User:
    """从JWT Token获取当前用户"""
    payload = await auth_manager.verify_token(credentials.credentials)
    if not payload:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token无效或已过期",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    user = await auth_manager.get_user_by_id(payload['user_id'])
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户不存在或已禁用",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return user


async def get_current_user_from_api_key(
    api_key: str = Depends(api_key_header),
    auth_manager: AuthManager = Depends()
) -> User:
    """从API密钥获取当前用户"""
    user = await auth_manager.verify_api_key(api_key)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="API密钥无效",
        )
    
    return user


def require_permission(permission: Permission):
    """需要特定权限的依赖项"""
    def permission_checker(current_user: User = Depends(get_current_user_from_token)) -> User:
        if not current_user.permissions or permission not in current_user.permissions:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足"
            )
        return current_user
    
    return permission_checker
