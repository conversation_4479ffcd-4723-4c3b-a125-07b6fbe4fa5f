"""
WebSocket管理器
WebSocket Manager

提供实时数据推送、配置更新通知、系统状态广播等WebSocket功能
"""

import json
import asyncio
import logging
from typing import Dict, List, Set, Any, Optional
from datetime import datetime
from enum import Enum

from fastapi import WebSocket, WebSocketDisconnect
from fastapi.websockets import WebSocketState

logger = logging.getLogger(__name__)


class MessageType(Enum):
    """消息类型枚举"""
    # 系统消息
    SYSTEM_STATUS = "system_status"
    HEARTBEAT = "heartbeat"
    ERROR = "error"
    
    # 配置消息
    CONFIG_UPDATED = "config_updated"
    CONFIG_VALIDATION_ERROR = "config_validation_error"
    
    # 交易消息
    TRADE_SIGNAL = "trade_signal"
    POSITION_UPDATE = "position_update"
    ORDER_STATUS = "order_status"
    
    # 市场数据消息
    MARKET_DATA = "market_data"
    PRICE_ALERT = "price_alert"
    
    # 监控消息
    PERFORMANCE_METRICS = "performance_metrics"
    RISK_ALERT = "risk_alert"


class ConnectionType(Enum):
    """连接类型枚举"""
    ADMIN = "admin"
    TRADER = "trader"
    MONITOR = "monitor"
    CLIENT = "client"


class WebSocketConnection:
    """WebSocket连接封装"""
    
    def __init__(
        self, 
        websocket: WebSocket, 
        connection_id: str,
        user_id: Optional[str] = None,
        connection_type: ConnectionType = ConnectionType.CLIENT
    ):
        self.websocket = websocket
        self.connection_id = connection_id
        self.user_id = user_id
        self.connection_type = connection_type
        self.connected_at = datetime.now()
        self.last_heartbeat = datetime.now()
        self.subscriptions: Set[str] = set()
    
    async def send_message(self, message_type: MessageType, data: Any):
        """发送消息"""
        try:
            if self.websocket.client_state == WebSocketState.CONNECTED:
                message = {
                    "type": message_type.value,
                    "data": data,
                    "timestamp": datetime.now().isoformat(),
                    "connection_id": self.connection_id
                }
                await self.websocket.send_text(json.dumps(message))
                return True
        except Exception as e:
            logger.error(f"发送WebSocket消息失败: {e}")
            return False
    
    async def send_heartbeat(self):
        """发送心跳"""
        await self.send_message(MessageType.HEARTBEAT, {"status": "alive"})
        self.last_heartbeat = datetime.now()
    
    def subscribe(self, topic: str):
        """订阅主题"""
        self.subscriptions.add(topic)
    
    def unsubscribe(self, topic: str):
        """取消订阅主题"""
        self.subscriptions.discard(topic)
    
    def is_subscribed(self, topic: str) -> bool:
        """检查是否订阅了主题"""
        return topic in self.subscriptions


class WebSocketManager:
    """WebSocket管理器"""
    
    def __init__(self):
        """初始化WebSocket管理器"""
        # 活跃连接
        self.active_connections: Dict[str, WebSocketConnection] = {}
        
        # 主题订阅
        self.topic_subscribers: Dict[str, Set[str]] = {}
        
        # 心跳任务
        self.heartbeat_task: Optional[asyncio.Task] = None
        
        # 统计信息
        self.stats = {
            "total_connections": 0,
            "active_connections": 0,
            "messages_sent": 0,
            "messages_received": 0,
            "errors": 0
        }
        
        logger.info("WebSocket管理器初始化完成")
    
    async def initialize(self):
        """异步初始化"""
        # 启动心跳任务
        self.heartbeat_task = asyncio.create_task(self._heartbeat_loop())
        logger.info("WebSocket管理器异步初始化完成")
    
    async def cleanup(self):
        """清理资源"""
        # 停止心跳任务
        if self.heartbeat_task:
            self.heartbeat_task.cancel()
            try:
                await self.heartbeat_task
            except asyncio.CancelledError:
                pass
        
        # 关闭所有连接
        for connection in list(self.active_connections.values()):
            await self.disconnect(connection.connection_id)
        
        logger.info("WebSocket管理器清理完成")
    
    async def health_check(self) -> str:
        """健康检查"""
        try:
            active_count = len(self.active_connections)
            if active_count >= 0:  # WebSocket服务总是健康的
                return "healthy"
            return "unhealthy"
        except Exception:
            return "unhealthy"
    
    async def connect(
        self, 
        websocket: WebSocket, 
        connection_id: str,
        user_id: Optional[str] = None,
        connection_type: ConnectionType = ConnectionType.CLIENT
    ) -> WebSocketConnection:
        """建立WebSocket连接"""
        try:
            await websocket.accept()
            
            connection = WebSocketConnection(
                websocket=websocket,
                connection_id=connection_id,
                user_id=user_id,
                connection_type=connection_type
            )
            
            self.active_connections[connection_id] = connection
            self.stats["total_connections"] += 1
            self.stats["active_connections"] = len(self.active_connections)
            
            # 发送欢迎消息
            await connection.send_message(
                MessageType.SYSTEM_STATUS,
                {
                    "status": "connected",
                    "connection_id": connection_id,
                    "server_time": datetime.now().isoformat()
                }
            )
            
            logger.info(f"WebSocket连接建立: {connection_id}, 用户: {user_id}")
            return connection
            
        except Exception as e:
            logger.error(f"建立WebSocket连接失败: {e}")
            raise
    
    async def disconnect(self, connection_id: str):
        """断开WebSocket连接"""
        try:
            connection = self.active_connections.get(connection_id)
            if connection:
                # 清理订阅
                for topic in list(connection.subscriptions):
                    await self.unsubscribe(connection_id, topic)
                
                # 关闭连接
                if connection.websocket.client_state == WebSocketState.CONNECTED:
                    await connection.websocket.close()
                
                # 移除连接
                del self.active_connections[connection_id]
                self.stats["active_connections"] = len(self.active_connections)
                
                logger.info(f"WebSocket连接断开: {connection_id}")
                
        except Exception as e:
            logger.error(f"断开WebSocket连接失败: {e}")
    
    async def send_to_connection(
        self, 
        connection_id: str, 
        message_type: MessageType, 
        data: Any
    ) -> bool:
        """向指定连接发送消息"""
        connection = self.active_connections.get(connection_id)
        if connection:
            success = await connection.send_message(message_type, data)
            if success:
                self.stats["messages_sent"] += 1
            else:
                self.stats["errors"] += 1
            return success
        return False
    
    async def send_to_user(
        self, 
        user_id: str, 
        message_type: MessageType, 
        data: Any
    ) -> int:
        """向指定用户的所有连接发送消息"""
        sent_count = 0
        for connection in self.active_connections.values():
            if connection.user_id == user_id:
                if await connection.send_message(message_type, data):
                    sent_count += 1
                    self.stats["messages_sent"] += 1
                else:
                    self.stats["errors"] += 1
        return sent_count
    
    async def broadcast(
        self, 
        message_type: MessageType, 
        data: Any,
        connection_type: Optional[ConnectionType] = None
    ) -> int:
        """广播消息到所有连接或指定类型的连接"""
        sent_count = 0
        for connection in self.active_connections.values():
            if connection_type is None or connection.connection_type == connection_type:
                if await connection.send_message(message_type, data):
                    sent_count += 1
                    self.stats["messages_sent"] += 1
                else:
                    self.stats["errors"] += 1
        return sent_count
    
    async def subscribe(self, connection_id: str, topic: str) -> bool:
        """订阅主题"""
        connection = self.active_connections.get(connection_id)
        if connection:
            connection.subscribe(topic)
            
            # 添加到主题订阅者列表
            if topic not in self.topic_subscribers:
                self.topic_subscribers[topic] = set()
            self.topic_subscribers[topic].add(connection_id)
            
            logger.info(f"连接 {connection_id} 订阅主题: {topic}")
            return True
        return False
    
    async def unsubscribe(self, connection_id: str, topic: str) -> bool:
        """取消订阅主题"""
        connection = self.active_connections.get(connection_id)
        if connection:
            connection.unsubscribe(topic)
            
            # 从主题订阅者列表移除
            if topic in self.topic_subscribers:
                self.topic_subscribers[topic].discard(connection_id)
                if not self.topic_subscribers[topic]:
                    del self.topic_subscribers[topic]
            
            logger.info(f"连接 {connection_id} 取消订阅主题: {topic}")
            return True
        return False
    
    async def publish_to_topic(
        self, 
        topic: str, 
        message_type: MessageType, 
        data: Any
    ) -> int:
        """向主题发布消息"""
        sent_count = 0
        subscribers = self.topic_subscribers.get(topic, set())
        
        for connection_id in list(subscribers):  # 使用list()避免迭代时修改
            connection = self.active_connections.get(connection_id)
            if connection and connection.is_subscribed(topic):
                if await connection.send_message(message_type, data):
                    sent_count += 1
                    self.stats["messages_sent"] += 1
                else:
                    self.stats["errors"] += 1
                    # 如果发送失败，可能连接已断开，清理订阅
                    await self.unsubscribe(connection_id, topic)
        
        return sent_count
    
    async def handle_message(self, connection_id: str, message: str):
        """处理接收到的消息"""
        try:
            data = json.loads(message)
            message_type = data.get("type")
            payload = data.get("data", {})
            
            self.stats["messages_received"] += 1
            
            connection = self.active_connections.get(connection_id)
            if not connection:
                return
            
            # 处理不同类型的消息
            if message_type == "subscribe":
                topic = payload.get("topic")
                if topic:
                    await self.subscribe(connection_id, topic)
                    await connection.send_message(
                        MessageType.SYSTEM_STATUS,
                        {"status": "subscribed", "topic": topic}
                    )
            
            elif message_type == "unsubscribe":
                topic = payload.get("topic")
                if topic:
                    await self.unsubscribe(connection_id, topic)
                    await connection.send_message(
                        MessageType.SYSTEM_STATUS,
                        {"status": "unsubscribed", "topic": topic}
                    )
            
            elif message_type == "heartbeat":
                await connection.send_heartbeat()
            
            elif message_type == "get_subscriptions":
                await connection.send_message(
                    MessageType.SYSTEM_STATUS,
                    {
                        "subscriptions": list(connection.subscriptions),
                        "connection_info": {
                            "connection_id": connection.connection_id,
                            "user_id": connection.user_id,
                            "connected_at": connection.connected_at.isoformat(),
                            "connection_type": connection.connection_type.value
                        }
                    }
                )
            
            else:
                await connection.send_message(
                    MessageType.ERROR,
                    {"error": f"未知消息类型: {message_type}"}
                )
                
        except json.JSONDecodeError:
            await self.send_to_connection(
                connection_id,
                MessageType.ERROR,
                {"error": "无效的JSON格式"}
            )
        except Exception as e:
            logger.error(f"处理WebSocket消息失败: {e}")
            await self.send_to_connection(
                connection_id,
                MessageType.ERROR,
                {"error": f"处理消息失败: {str(e)}"}
            )
    
    async def _heartbeat_loop(self):
        """心跳循环"""
        while True:
            try:
                await asyncio.sleep(30)  # 每30秒发送一次心跳
                
                # 检查所有连接的心跳状态
                current_time = datetime.now()
                disconnected_connections = []
                
                for connection_id, connection in self.active_connections.items():
                    # 如果超过60秒没有心跳，认为连接已断开
                    if (current_time - connection.last_heartbeat).total_seconds() > 60:
                        disconnected_connections.append(connection_id)
                    else:
                        # 发送心跳
                        await connection.send_heartbeat()
                
                # 清理断开的连接
                for connection_id in disconnected_connections:
                    await self.disconnect(connection_id)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"心跳循环错误: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            **self.stats,
            "topics": list(self.topic_subscribers.keys()),
            "topic_subscriber_counts": {
                topic: len(subscribers) 
                for topic, subscribers in self.topic_subscribers.items()
            }
        }
    
    def get_connections_info(self) -> List[Dict[str, Any]]:
        """获取连接信息"""
        return [
            {
                "connection_id": conn.connection_id,
                "user_id": conn.user_id,
                "connection_type": conn.connection_type.value,
                "connected_at": conn.connected_at.isoformat(),
                "last_heartbeat": conn.last_heartbeat.isoformat(),
                "subscriptions": list(conn.subscriptions)
            }
            for conn in self.active_connections.values()
        ]
