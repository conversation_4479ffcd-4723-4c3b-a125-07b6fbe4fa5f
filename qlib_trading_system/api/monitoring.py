"""
FastAPI监控系统
FastAPI Monitoring System

提供异步API监控、性能指标收集、速率限制等功能
"""

import time
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Set
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import json

import redis.asyncio as redis
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

logger = logging.getLogger(__name__)


@dataclass
class APIRequest:
    """API请求记录"""
    request_id: str
    endpoint: str
    method: str
    user_id: Optional[str]
    ip_address: str
    user_agent: str
    request_time: datetime
    response_time: Optional[datetime]
    duration_ms: Optional[float]
    status_code: Optional[int]
    request_size: int
    response_size: Optional[int]
    error_message: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['request_time'] = self.request_time.isoformat()
        if self.response_time:
            data['response_time'] = self.response_time.isoformat()
        return data


@dataclass
class APIMetrics:
    """API指标"""
    total_requests: int
    successful_requests: int
    failed_requests: int
    avg_response_time: float
    min_response_time: float
    max_response_time: float
    requests_per_minute: float
    error_rate: float
    p95_response_time: float
    p99_response_time: float
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)


class RateLimiter:
    """异步速率限制器"""
    
    def __init__(
        self, 
        redis_client: redis.Redis,
        max_requests: int = 100, 
        window_seconds: int = 60,
        burst_size: int = 20
    ):
        """
        初始化速率限制器
        
        Args:
            redis_client: Redis客户端
            max_requests: 窗口期内最大请求数
            window_seconds: 时间窗口（秒）
            burst_size: 突发请求大小
        """
        self.redis_client = redis_client
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.burst_size = burst_size
    
    async def is_allowed(self, user_id: str) -> bool:
        """
        检查是否允许请求
        
        Args:
            user_id: 用户ID
            
        Returns:
            是否允许请求
        """
        try:
            now = time.time()
            window_start = now - self.window_seconds
            
            # 使用Redis的滑动窗口算法
            pipe = self.redis_client.pipeline()
            
            # 移除过期的请求记录
            pipe.zremrangebyscore(f"rate_limit:{user_id}", 0, window_start)
            
            # 获取当前窗口内的请求数
            pipe.zcard(f"rate_limit:{user_id}")
            
            # 执行管道
            results = await pipe.execute()
            current_requests = results[1]
            
            # 检查是否超过限制
            if current_requests >= self.max_requests:
                return False
            
            # 记录当前请求
            await self.redis_client.zadd(
                f"rate_limit:{user_id}",
                {str(now): now}
            )
            
            # 设置过期时间
            await self.redis_client.expire(
                f"rate_limit:{user_id}",
                self.window_seconds * 2
            )
            
            return True
            
        except Exception as e:
            logger.error(f"速率限制检查失败: {e}")
            # 如果Redis出错，允许请求通过
            return True
    
    async def get_remaining_requests(self, user_id: str) -> int:
        """
        获取剩余请求数
        
        Args:
            user_id: 用户ID
            
        Returns:
            剩余请求数
        """
        try:
            now = time.time()
            window_start = now - self.window_seconds
            
            # 清理过期记录并获取当前请求数
            pipe = self.redis_client.pipeline()
            pipe.zremrangebyscore(f"rate_limit:{user_id}", 0, window_start)
            pipe.zcard(f"rate_limit:{user_id}")
            
            results = await pipe.execute()
            current_requests = results[1]
            
            return max(0, self.max_requests - current_requests)
            
        except Exception as e:
            logger.error(f"获取剩余请求数失败: {e}")
            return self.max_requests


class APIMonitor:
    """异步API监控器"""
    
    def __init__(self, redis_url: str, max_history: int = 10000):
        """
        初始化API监控器
        
        Args:
            redis_url: Redis连接URL
            max_history: 最大历史记录数
        """
        self.redis_url = redis_url
        self.max_history = max_history
        
        # Redis连接
        self.redis_client: Optional[redis.Redis] = None
        
        # 速率限制器
        self.rate_limiter: Optional[RateLimiter] = None
        
        # 内存中的实时统计
        self.endpoint_stats = defaultdict(lambda: {
            'count': 0,
            'total_time': 0.0,
            'error_count': 0,
            'last_request': None,
            'response_times': deque(maxlen=1000)  # 保存最近1000个响应时间
        })
        
        self.user_stats = defaultdict(lambda: {
            'request_count': 0,
            'error_count': 0,
            'total_time': 0.0,
            'last_request': None
        })
        
        # 全局统计
        self.global_stats = {
            'total_requests': 0,
            'total_errors': 0,
            'start_time': datetime.now()
        }
        
        logger.info("API监控器初始化完成")
    
    async def initialize(self):
        """异步初始化"""
        try:
            # 连接Redis
            self.redis_client = redis.from_url(self.redis_url)
            await self.redis_client.ping()
            
            # 初始化速率限制器
            self.rate_limiter = RateLimiter(self.redis_client)
            
            # 启动清理任务
            asyncio.create_task(self._cleanup_task())
            
            logger.info("API监控器异步初始化完成")
            
        except Exception as e:
            logger.error(f"API监控器初始化失败: {e}")
            raise
    
    async def cleanup(self):
        """清理资源"""
        if self.redis_client:
            await self.redis_client.close()
    
    async def health_check(self) -> str:
        """健康检查"""
        try:
            if self.redis_client:
                await self.redis_client.ping()
                return "healthy"
            return "unhealthy"
        except Exception:
            return "unhealthy"
    
    async def record_request(
        self,
        request_id: str,
        endpoint: str,
        method: str,
        user_id: Optional[str],
        ip_address: str,
        user_agent: str,
        request_size: int,
        response_time_ms: float,
        status_code: int,
        response_size: int,
        error_message: Optional[str] = None
    ):
        """
        记录API请求
        
        Args:
            request_id: 请求ID
            endpoint: 端点
            method: HTTP方法
            user_id: 用户ID
            ip_address: IP地址
            user_agent: 用户代理
            request_size: 请求大小
            response_time_ms: 响应时间（毫秒）
            status_code: 状态码
            response_size: 响应大小
            error_message: 错误消息
        """
        try:
            # 创建请求记录
            api_request = APIRequest(
                request_id=request_id,
                endpoint=endpoint,
                method=method,
                user_id=user_id,
                ip_address=ip_address,
                user_agent=user_agent,
                request_time=datetime.now() - timedelta(milliseconds=response_time_ms),
                response_time=datetime.now(),
                duration_ms=response_time_ms,
                status_code=status_code,
                request_size=request_size,
                response_size=response_size,
                error_message=error_message
            )
            
            # 存储到Redis
            await self._store_request_to_redis(api_request)
            
            # 更新内存统计
            await self._update_memory_stats(api_request)
            
        except Exception as e:
            logger.error(f"记录API请求失败: {e}")
    
    async def _store_request_to_redis(self, api_request: APIRequest):
        """存储请求到Redis"""
        try:
            # 存储请求详情
            await self.redis_client.lpush(
                "api_requests",
                json.dumps(api_request.to_dict())
            )
            
            # 限制列表长度
            await self.redis_client.ltrim("api_requests", 0, self.max_history - 1)
            
            # 存储到时间序列（用于时间范围查询）
            timestamp = int(api_request.request_time.timestamp())
            await self.redis_client.zadd(
                "api_requests_timeline",
                {api_request.request_id: timestamp}
            )
            
            # 存储请求详情的哈希
            await self.redis_client.hset(
                f"api_request:{api_request.request_id}",
                mapping=api_request.to_dict()
            )
            
            # 设置过期时间（7天）
            await self.redis_client.expire(
                f"api_request:{api_request.request_id}",
                7 * 24 * 3600
            )
            
        except Exception as e:
            logger.error(f"存储请求到Redis失败: {e}")
    
    async def _update_memory_stats(self, api_request: APIRequest):
        """更新内存统计"""
        try:
            # 更新端点统计
            endpoint_key = f"{api_request.method} {api_request.endpoint}"
            stats = self.endpoint_stats[endpoint_key]
            stats['count'] += 1
            stats['total_time'] += api_request.duration_ms
            stats['last_request'] = api_request.request_time
            stats['response_times'].append(api_request.duration_ms)
            
            if api_request.status_code >= 400:
                stats['error_count'] += 1
            
            # 更新用户统计
            if api_request.user_id:
                user_stats = self.user_stats[api_request.user_id]
                user_stats['request_count'] += 1
                user_stats['total_time'] += api_request.duration_ms
                user_stats['last_request'] = api_request.request_time
                
                if api_request.status_code >= 400:
                    user_stats['error_count'] += 1
            
            # 更新全局统计
            self.global_stats['total_requests'] += 1
            if api_request.status_code >= 400:
                self.global_stats['total_errors'] += 1
            
        except Exception as e:
            logger.error(f"更新内存统计失败: {e}")
    
    async def get_metrics(self, time_range_minutes: int = 60) -> APIMetrics:
        """
        获取API指标
        
        Args:
            time_range_minutes: 时间范围（分钟）
            
        Returns:
            API指标
        """
        try:
            # 从Redis获取时间范围内的请求
            end_time = datetime.now()
            start_time = end_time - timedelta(minutes=time_range_minutes)
            
            start_timestamp = int(start_time.timestamp())
            end_timestamp = int(end_time.timestamp())
            
            # 获取时间范围内的请求ID
            request_ids = await self.redis_client.zrangebyscore(
                "api_requests_timeline",
                start_timestamp,
                end_timestamp
            )
            
            if not request_ids:
                return APIMetrics(
                    total_requests=0,
                    successful_requests=0,
                    failed_requests=0,
                    avg_response_time=0.0,
                    min_response_time=0.0,
                    max_response_time=0.0,
                    requests_per_minute=0.0,
                    error_rate=0.0,
                    p95_response_time=0.0,
                    p99_response_time=0.0
                )
            
            # 获取请求详情
            response_times = []
            successful_requests = 0
            failed_requests = 0
            
            for request_id in request_ids:
                request_data = await self.redis_client.hgetall(f"api_request:{request_id.decode()}")
                if request_data:
                    # 解码数据
                    request_data = {k.decode(): v.decode() for k, v in request_data.items()}
                    
                    duration_ms = float(request_data.get('duration_ms', 0))
                    status_code = int(request_data.get('status_code', 500))
                    
                    response_times.append(duration_ms)
                    
                    if status_code < 400:
                        successful_requests += 1
                    else:
                        failed_requests += 1
            
            # 计算指标
            total_requests = len(response_times)
            avg_response_time = sum(response_times) / total_requests if total_requests > 0 else 0
            min_response_time = min(response_times) if response_times else 0
            max_response_time = max(response_times) if response_times else 0
            requests_per_minute = total_requests / time_range_minutes if time_range_minutes > 0 else 0
            error_rate = failed_requests / total_requests if total_requests > 0 else 0
            
            # 计算百分位数
            if response_times:
                sorted_times = sorted(response_times)
                p95_index = int(len(sorted_times) * 0.95)
                p99_index = int(len(sorted_times) * 0.99)
                p95_response_time = sorted_times[p95_index] if p95_index < len(sorted_times) else max_response_time
                p99_response_time = sorted_times[p99_index] if p99_index < len(sorted_times) else max_response_time
            else:
                p95_response_time = 0.0
                p99_response_time = 0.0
            
            return APIMetrics(
                total_requests=total_requests,
                successful_requests=successful_requests,
                failed_requests=failed_requests,
                avg_response_time=avg_response_time,
                min_response_time=min_response_time,
                max_response_time=max_response_time,
                requests_per_minute=requests_per_minute,
                error_rate=error_rate,
                p95_response_time=p95_response_time,
                p99_response_time=p99_response_time
            )
            
        except Exception as e:
            logger.error(f"获取API指标失败: {e}")
            return APIMetrics(
                total_requests=0,
                successful_requests=0,
                failed_requests=0,
                avg_response_time=0.0,
                min_response_time=0.0,
                max_response_time=0.0,
                requests_per_minute=0.0,
                error_rate=0.0,
                p95_response_time=0.0,
                p99_response_time=0.0
            )
    
    async def get_endpoint_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取端点统计"""
        stats = {}
        for endpoint, data in self.endpoint_stats.items():
            avg_time = data['total_time'] / data['count'] if data['count'] > 0 else 0
            error_rate = data['error_count'] / data['count'] if data['count'] > 0 else 0
            
            # 计算响应时间百分位数
            response_times = list(data['response_times'])
            if response_times:
                sorted_times = sorted(response_times)
                p95_index = int(len(sorted_times) * 0.95)
                p99_index = int(len(sorted_times) * 0.99)
                p95_time = sorted_times[p95_index] if p95_index < len(sorted_times) else max(sorted_times)
                p99_time = sorted_times[p99_index] if p99_index < len(sorted_times) else max(sorted_times)
            else:
                p95_time = 0.0
                p99_time = 0.0
            
            stats[endpoint] = {
                'request_count': data['count'],
                'avg_response_time': avg_time,
                'p95_response_time': p95_time,
                'p99_response_time': p99_time,
                'error_count': data['error_count'],
                'error_rate': error_rate,
                'last_request': data['last_request'].isoformat() if data['last_request'] else None
            }
        
        return stats
    
    async def get_user_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取用户统计"""
        stats = {}
        for user_id, data in self.user_stats.items():
            avg_time = data['total_time'] / data['request_count'] if data['request_count'] > 0 else 0
            error_rate = data['error_count'] / data['request_count'] if data['request_count'] > 0 else 0
            
            stats[user_id] = {
                'request_count': data['request_count'],
                'avg_response_time': avg_time,
                'error_count': data['error_count'],
                'error_rate': error_rate,
                'last_request': data['last_request'].isoformat() if data['last_request'] else None
            }
        
        return stats
    
    async def get_slow_requests(self, threshold_ms: float = 1000, limit: int = 10) -> List[Dict[str, Any]]:
        """获取慢请求"""
        try:
            # 从Redis获取最近的请求
            recent_requests = await self.redis_client.lrange("api_requests", 0, 1000)
            
            slow_requests = []
            for request_data in recent_requests:
                try:
                    request_dict = json.loads(request_data)
                    if request_dict.get('duration_ms', 0) > threshold_ms:
                        slow_requests.append(request_dict)
                except json.JSONDecodeError:
                    continue
            
            # 按响应时间倒序排列
            slow_requests.sort(key=lambda x: x.get('duration_ms', 0), reverse=True)
            
            return slow_requests[:limit]
            
        except Exception as e:
            logger.error(f"获取慢请求失败: {e}")
            return []
    
    async def _cleanup_task(self):
        """清理任务"""
        while True:
            try:
                await asyncio.sleep(3600)  # 每小时执行一次
                
                # 清理过期的时间线数据
                cutoff_time = int((datetime.now() - timedelta(days=7)).timestamp())
                await self.redis_client.zremrangebyscore(
                    "api_requests_timeline",
                    0,
                    cutoff_time
                )
                
                logger.info("API监控数据清理完成")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"API监控数据清理失败: {e}")


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """请求日志中间件"""
    
    async def dispatch(self, request: Request, call_next):
        start_time = time.time()
        
        # 生成请求ID
        request_id = f"req_{int(time.time() * 1000000)}"
        request.state.request_id = request_id
        
        # 记录请求开始
        logger.info(f"请求开始: {request_id} {request.method} {request.url.path}")
        
        # 处理请求
        response = await call_next(request)
        
        # 计算响应时间
        process_time = (time.time() - start_time) * 1000
        
        # 记录请求结束
        logger.info(
            f"请求结束: {request_id} {request.method} {request.url.path} "
            f"{response.status_code} {process_time:.2f}ms"
        )
        
        # 添加响应头
        response.headers["X-Request-ID"] = request_id
        response.headers["X-Process-Time"] = f"{process_time:.2f}ms"
        
        return response


class RateLimitMiddleware(BaseHTTPMiddleware):
    """速率限制中间件"""
    
    def __init__(self, app, requests_per_minute: int = 100, burst_size: int = 20):
        super().__init__(app)
        self.requests_per_minute = requests_per_minute
        self.burst_size = burst_size
    
    async def dispatch(self, request: Request, call_next):
        # 获取用户标识（IP地址或用户ID）
        user_id = request.client.host if request.client else "unknown"
        
        # 检查速率限制
        api_monitor = getattr(request.app.state, 'api_monitor', None)
        if api_monitor and api_monitor.rate_limiter:
            if not await api_monitor.rate_limiter.is_allowed(user_id):
                from fastapi import HTTPException
                raise HTTPException(status_code=429, detail="请求频率过高，请稍后再试")
        
        return await call_next(request)
