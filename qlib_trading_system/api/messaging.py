"""
消息队列系统
Message Queue System

基于RabbitMQ的异步消息队列，支持发布/订阅、任务队列、RPC等模式
"""

import json
import asyncio
import logging
from typing import Dict, Any, Optional, Callable, List
from datetime import datetime
from enum import Enum
from dataclasses import dataclass, asdict

import aio_pika
from aio_pika import Message, DeliveryMode, ExchangeType
from aio_pika.abc import AbstractConnection, AbstractChannel, AbstractExchange, AbstractQueue

logger = logging.getLogger(__name__)


class MessagePriority(Enum):
    """消息优先级"""
    LOW = 1
    NORMAL = 5
    HIGH = 8
    CRITICAL = 10


class ExchangeName(Enum):
    """交换机名称"""
    CONFIG = "config_exchange"
    TRADING = "trading_exchange"
    MARKET_DATA = "market_data_exchange"
    MONITORING = "monitoring_exchange"
    SYSTEM = "system_exchange"


@dataclass
class QueueMessage:
    """队列消息"""
    message_id: str
    message_type: str
    payload: Dict[str, Any]
    priority: MessagePriority
    timestamp: datetime
    source: str
    correlation_id: Optional[str] = None
    reply_to: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        data['priority'] = self.priority.value
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'QueueMessage':
        """从字典创建"""
        data['timestamp'] = datetime.fromisoformat(data['timestamp'])
        data['priority'] = MessagePriority(data['priority'])
        return cls(**data)


class MessageQueue:
    """异步消息队列管理器"""
    
    def __init__(self, rabbitmq_url: str):
        """
        初始化消息队列管理器
        
        Args:
            rabbitmq_url: RabbitMQ连接URL
        """
        self.rabbitmq_url = rabbitmq_url
        self.connection: Optional[AbstractConnection] = None
        self.channel: Optional[AbstractChannel] = None
        self.exchanges: Dict[str, AbstractExchange] = {}
        self.queues: Dict[str, AbstractQueue] = {}
        self.consumers: Dict[str, Callable] = {}
        
        # 统计信息
        self.stats = {
            "messages_published": 0,
            "messages_consumed": 0,
            "errors": 0,
            "active_consumers": 0
        }
        
        logger.info("消息队列管理器初始化完成")
    
    async def initialize(self):
        """异步初始化"""
        try:
            # 建立连接
            self.connection = await aio_pika.connect_robust(self.rabbitmq_url)
            self.channel = await self.connection.channel()
            
            # 设置QoS
            await self.channel.set_qos(prefetch_count=10)
            
            # 创建交换机
            await self._create_exchanges()
            
            # 创建队列
            await self._create_queues()
            
            logger.info("消息队列管理器异步初始化完成")
            
        except Exception as e:
            logger.error(f"消息队列初始化失败: {e}")
            raise
    
    async def cleanup(self):
        """清理资源"""
        try:
            # 停止所有消费者
            for queue_name in list(self.consumers.keys()):
                await self.stop_consumer(queue_name)
            
            # 关闭连接
            if self.connection and not self.connection.is_closed:
                await self.connection.close()
            
            logger.info("消息队列管理器清理完成")
            
        except Exception as e:
            logger.error(f"消息队列清理失败: {e}")
    
    async def health_check(self) -> str:
        """健康检查"""
        try:
            if self.connection and not self.connection.is_closed:
                return "healthy"
            return "unhealthy"
        except Exception:
            return "unhealthy"
    
    async def _create_exchanges(self):
        """创建交换机"""
        exchange_configs = [
            (ExchangeName.CONFIG, ExchangeType.TOPIC),
            (ExchangeName.TRADING, ExchangeType.TOPIC),
            (ExchangeName.MARKET_DATA, ExchangeType.FANOUT),
            (ExchangeName.MONITORING, ExchangeType.TOPIC),
            (ExchangeName.SYSTEM, ExchangeType.TOPIC)
        ]
        
        for exchange_name, exchange_type in exchange_configs:
            exchange = await self.channel.declare_exchange(
                exchange_name.value,
                exchange_type,
                durable=True
            )
            self.exchanges[exchange_name.value] = exchange
            logger.info(f"创建交换机: {exchange_name.value} ({exchange_type.name})")
    
    async def _create_queues(self):
        """创建队列"""
        queue_configs = [
            # 配置相关队列
            ("config_updates", ExchangeName.CONFIG, "config.update.*"),
            ("config_validation", ExchangeName.CONFIG, "config.validation.*"),
            
            # 交易相关队列
            ("trade_signals", ExchangeName.TRADING, "trade.signal.*"),
            ("order_management", ExchangeName.TRADING, "trade.order.*"),
            ("position_updates", ExchangeName.TRADING, "trade.position.*"),
            
            # 市场数据队列
            ("market_data_realtime", ExchangeName.MARKET_DATA, ""),
            ("price_alerts", ExchangeName.MARKET_DATA, ""),
            
            # 监控相关队列
            ("performance_metrics", ExchangeName.MONITORING, "monitoring.performance.*"),
            ("risk_alerts", ExchangeName.MONITORING, "monitoring.risk.*"),
            ("system_alerts", ExchangeName.MONITORING, "monitoring.system.*"),
            
            # 系统相关队列
            ("system_events", ExchangeName.SYSTEM, "system.event.*"),
            ("audit_logs", ExchangeName.SYSTEM, "system.audit.*")
        ]
        
        for queue_name, exchange_name, routing_key in queue_configs:
            queue = await self.channel.declare_queue(
                queue_name,
                durable=True,
                arguments={"x-max-priority": 10}  # 支持消息优先级
            )
            
            # 绑定到交换机
            exchange = self.exchanges[exchange_name.value]
            if routing_key:  # Topic交换机需要路由键
                await queue.bind(exchange, routing_key)
            else:  # Fanout交换机不需要路由键
                await queue.bind(exchange)
            
            self.queues[queue_name] = queue
            logger.info(f"创建队列: {queue_name} -> {exchange_name.value}")
    
    async def publish_message(
        self,
        exchange_name: str,
        routing_key: str,
        message: QueueMessage,
        delivery_mode: DeliveryMode = DeliveryMode.PERSISTENT
    ) -> bool:
        """
        发布消息
        
        Args:
            exchange_name: 交换机名称
            routing_key: 路由键
            message: 消息对象
            delivery_mode: 投递模式
            
        Returns:
            是否发布成功
        """
        try:
            exchange = self.exchanges.get(exchange_name)
            if not exchange:
                raise ValueError(f"交换机不存在: {exchange_name}")
            
            # 创建消息
            aio_message = Message(
                json.dumps(message.to_dict()).encode(),
                priority=message.priority.value,
                message_id=message.message_id,
                timestamp=message.timestamp,
                correlation_id=message.correlation_id,
                reply_to=message.reply_to,
                delivery_mode=delivery_mode,
                headers={
                    "message_type": message.message_type,
                    "source": message.source
                }
            )
            
            # 发布消息
            await exchange.publish(aio_message, routing_key)
            
            self.stats["messages_published"] += 1
            logger.debug(f"消息已发布: {message.message_id} -> {exchange_name}/{routing_key}")
            return True
            
        except Exception as e:
            logger.error(f"发布消息失败: {e}")
            self.stats["errors"] += 1
            return False
    
    async def start_consumer(
        self,
        queue_name: str,
        callback: Callable[[QueueMessage], None],
        auto_ack: bool = False
    ) -> bool:
        """
        启动消费者
        
        Args:
            queue_name: 队列名称
            callback: 消息处理回调函数
            auto_ack: 是否自动确认
            
        Returns:
            是否启动成功
        """
        try:
            queue = self.queues.get(queue_name)
            if not queue:
                raise ValueError(f"队列不存在: {queue_name}")
            
            async def message_handler(message: aio_pika.IncomingMessage):
                try:
                    # 解析消息
                    data = json.loads(message.body.decode())
                    queue_message = QueueMessage.from_dict(data)
                    
                    # 调用回调函数
                    if asyncio.iscoroutinefunction(callback):
                        await callback(queue_message)
                    else:
                        callback(queue_message)
                    
                    # 确认消息
                    if not auto_ack:
                        message.ack()
                    
                    self.stats["messages_consumed"] += 1
                    logger.debug(f"消息已处理: {queue_message.message_id}")
                    
                except Exception as e:
                    logger.error(f"处理消息失败: {e}")
                    self.stats["errors"] += 1
                    
                    # 拒绝消息并重新入队
                    if not auto_ack:
                        message.nack(requeue=True)
            
            # 开始消费
            consumer_tag = await queue.consume(message_handler, no_ack=auto_ack)
            self.consumers[queue_name] = consumer_tag
            self.stats["active_consumers"] += 1
            
            logger.info(f"消费者已启动: {queue_name}")
            return True
            
        except Exception as e:
            logger.error(f"启动消费者失败: {e}")
            return False
    
    async def stop_consumer(self, queue_name: str) -> bool:
        """
        停止消费者
        
        Args:
            queue_name: 队列名称
            
        Returns:
            是否停止成功
        """
        try:
            consumer_tag = self.consumers.get(queue_name)
            if consumer_tag:
                queue = self.queues.get(queue_name)
                if queue:
                    await queue.cancel(consumer_tag)
                
                del self.consumers[queue_name]
                self.stats["active_consumers"] -= 1
                
                logger.info(f"消费者已停止: {queue_name}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"停止消费者失败: {e}")
            return False
    
    async def publish_config_update(
        self,
        config_level: str,
        config_name: str,
        config_data: Dict[str, Any],
        updated_by: str
    ):
        """发布配置更新消息"""
        message = QueueMessage(
            message_id=f"config_update_{int(datetime.now().timestamp() * 1000)}",
            message_type="config_update",
            payload={
                "config_level": config_level,
                "config_name": config_name,
                "config_data": config_data,
                "updated_by": updated_by
            },
            priority=MessagePriority.HIGH,
            timestamp=datetime.now(),
            source="config_manager"
        )
        
        routing_key = f"config.update.{config_level}.{config_name}"
        await self.publish_message(
            ExchangeName.CONFIG.value,
            routing_key,
            message
        )
    
    async def publish_trade_signal(
        self,
        symbol: str,
        signal_type: str,
        signal_data: Dict[str, Any],
        strategy_name: str
    ):
        """发布交易信号消息"""
        message = QueueMessage(
            message_id=f"trade_signal_{int(datetime.now().timestamp() * 1000)}",
            message_type="trade_signal",
            payload={
                "symbol": symbol,
                "signal_type": signal_type,
                "signal_data": signal_data,
                "strategy_name": strategy_name
            },
            priority=MessagePriority.HIGH,
            timestamp=datetime.now(),
            source="trading_engine"
        )
        
        routing_key = f"trade.signal.{symbol}.{signal_type}"
        await self.publish_message(
            ExchangeName.TRADING.value,
            routing_key,
            message
        )
    
    async def publish_market_data(self, market_data: Dict[str, Any]):
        """发布市场数据消息"""
        message = QueueMessage(
            message_id=f"market_data_{int(datetime.now().timestamp() * 1000)}",
            message_type="market_data",
            payload=market_data,
            priority=MessagePriority.NORMAL,
            timestamp=datetime.now(),
            source="data_service"
        )
        
        await self.publish_message(
            ExchangeName.MARKET_DATA.value,
            "",  # Fanout交换机不需要路由键
            message
        )
    
    async def publish_risk_alert(
        self,
        alert_type: str,
        alert_data: Dict[str, Any],
        severity: str = "medium"
    ):
        """发布风险警报消息"""
        priority_map = {
            "low": MessagePriority.LOW,
            "medium": MessagePriority.NORMAL,
            "high": MessagePriority.HIGH,
            "critical": MessagePriority.CRITICAL
        }
        
        message = QueueMessage(
            message_id=f"risk_alert_{int(datetime.now().timestamp() * 1000)}",
            message_type="risk_alert",
            payload={
                "alert_type": alert_type,
                "alert_data": alert_data,
                "severity": severity
            },
            priority=priority_map.get(severity, MessagePriority.NORMAL),
            timestamp=datetime.now(),
            source="risk_controller"
        )
        
        routing_key = f"monitoring.risk.{alert_type}.{severity}"
        await self.publish_message(
            ExchangeName.MONITORING.value,
            routing_key,
            message
        )
    
    async def publish_system_event(
        self,
        event_type: str,
        event_data: Dict[str, Any],
        source: str
    ):
        """发布系统事件消息"""
        message = QueueMessage(
            message_id=f"system_event_{int(datetime.now().timestamp() * 1000)}",
            message_type="system_event",
            payload={
                "event_type": event_type,
                "event_data": event_data
            },
            priority=MessagePriority.NORMAL,
            timestamp=datetime.now(),
            source=source
        )
        
        routing_key = f"system.event.{event_type}"
        await self.publish_message(
            ExchangeName.SYSTEM.value,
            routing_key,
            message
        )
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            **self.stats,
            "exchanges": list(self.exchanges.keys()),
            "queues": list(self.queues.keys()),
            "active_consumers": list(self.consumers.keys())
        }
