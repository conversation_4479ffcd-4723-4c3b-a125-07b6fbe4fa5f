"""
风险预警通知系统
Risk Alert Notification System

实现多渠道风险预警通知功能，包括邮件、短信、钉钉等
"""

import asyncio
import logging
import smtplib
import json
import requests
from datetime import datetime
from typing import Dict, List, Optional, Any
import threading
import time
from dataclasses import dataclass

# 邮件相关导入，处理可能的导入错误
try:
    from email.mime.text import MimeText
    from email.mime.multipart import MimeMultipart
    from email.header import Header
    EMAIL_AVAILABLE = True
except ImportError:
    EMAIL_AVAILABLE = False
    print("警告: 邮件功能不可用，email.mime模块导入失败")

from .realtime_risk_monitor import RiskAlert, RiskMetrics

logger = logging.getLogger(__name__)


@dataclass
class NotificationConfig:
    """通知配置"""
    # 邮件配置
    email_enabled: bool = False
    smtp_server: str = "smtp.qq.com"
    smtp_port: int = 587
    email_user: str = ""
    email_password: str = ""
    email_recipients: List[str] = None
    
    # 钉钉配置
    dingtalk_enabled: bool = False
    dingtalk_webhook: str = ""
    dingtalk_secret: str = ""
    
    # 企业微信配置
    wechat_enabled: bool = False
    wechat_webhook: str = ""
    
    # 短信配置（阿里云短信服务）
    sms_enabled: bool = False
    sms_access_key: str = ""
    sms_access_secret: str = ""
    sms_sign_name: str = ""
    sms_template_code: str = ""
    sms_phone_numbers: List[str] = None
    
    # 通知频率控制
    min_notification_interval: int = 300  # 最小通知间隔（秒）
    max_notifications_per_hour: int = 10  # 每小时最大通知数量
    
    def __post_init__(self):
        if self.email_recipients is None:
            self.email_recipients = []
        if self.sms_phone_numbers is None:
            self.sms_phone_numbers = []


class AlertNotifier:
    """风险预警通知器"""
    
    def __init__(self, config: NotificationConfig):
        """
        初始化预警通知器
        
        Args:
            config: 通知配置
        """
        self.config = config
        self.notification_history = {}  # 通知历史记录
        self.notification_count = {}  # 通知计数
        self.last_notification_time = {}  # 最后通知时间
        
        logger.info("风险预警通知器初始化完成")
    
    async def send_alert_notification(self, alert: RiskAlert, metrics: RiskMetrics = None):
        """
        发送预警通知
        
        Args:
            alert: 风险预警
            metrics: 当前风险指标
        """
        try:
            # 检查通知频率限制
            if not self._should_send_notification(alert):
                logger.debug(f"跳过通知发送，频率限制: {alert.alert_id}")
                return
            
            # 准备通知内容
            notification_content = self._prepare_notification_content(alert, metrics)
            
            # 发送通知任务列表
            tasks = []
            
            # 邮件通知
            if self.config.email_enabled and self.config.email_recipients:
                tasks.append(self._send_email_notification(notification_content))
            
            # 钉钉通知
            if self.config.dingtalk_enabled and self.config.dingtalk_webhook:
                tasks.append(self._send_dingtalk_notification(notification_content))
            
            # 企业微信通知
            if self.config.wechat_enabled and self.config.wechat_webhook:
                tasks.append(self._send_wechat_notification(notification_content))
            
            # 短信通知（仅限严重预警）
            if (self.config.sms_enabled and 
                self.config.sms_phone_numbers and 
                alert.severity in ['CRITICAL']):
                tasks.append(self._send_sms_notification(notification_content))
            
            # 并发发送所有通知
            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)
                
                # 更新通知记录
                self._update_notification_record(alert)
                
                logger.info(f"预警通知已发送: {alert.alert_id} - {alert.message}")
            else:
                logger.warning("没有启用任何通知渠道")
                
        except Exception as e:
            logger.error(f"发送预警通知失败: {e}")
    
    def _should_send_notification(self, alert: RiskAlert) -> bool:
        """
        检查是否应该发送通知
        
        Args:
            alert: 风险预警
            
        Returns:
            是否应该发送通知
        """
        current_time = datetime.now()
        alert_key = f"{alert.alert_type}_{alert.severity}"
        
        # 检查最小通知间隔
        if alert_key in self.last_notification_time:
            time_diff = (current_time - self.last_notification_time[alert_key]).total_seconds()
            if time_diff < self.config.min_notification_interval:
                return False
        
        # 检查每小时通知数量限制
        hour_key = current_time.strftime("%Y%m%d%H")
        if hour_key not in self.notification_count:
            self.notification_count[hour_key] = 0
        
        if self.notification_count[hour_key] >= self.config.max_notifications_per_hour:
            return False
        
        return True
    
    def _update_notification_record(self, alert: RiskAlert):
        """
        更新通知记录
        
        Args:
            alert: 风险预警
        """
        current_time = datetime.now()
        alert_key = f"{alert.alert_type}_{alert.severity}"
        hour_key = current_time.strftime("%Y%m%d%H")
        
        # 更新最后通知时间
        self.last_notification_time[alert_key] = current_time
        
        # 更新通知计数
        if hour_key not in self.notification_count:
            self.notification_count[hour_key] = 0
        self.notification_count[hour_key] += 1
        
        # 清理过期的计数记录
        self._cleanup_old_records()
    
    def _cleanup_old_records(self):
        """清理过期的通知记录"""
        current_hour = datetime.now().strftime("%Y%m%d%H")
        
        # 保留最近24小时的记录
        keys_to_remove = []
        for key in self.notification_count.keys():
            if key < current_hour and len(key) == 10:  # 格式检查
                try:
                    key_time = datetime.strptime(key, "%Y%m%d%H")
                    if (datetime.now() - key_time).total_seconds() > 86400:  # 24小时
                        keys_to_remove.append(key)
                except ValueError:
                    keys_to_remove.append(key)
        
        for key in keys_to_remove:
            del self.notification_count[key]
    
    def _prepare_notification_content(self, alert: RiskAlert, metrics: RiskMetrics = None) -> Dict[str, str]:
        """
        准备通知内容
        
        Args:
            alert: 风险预警
            metrics: 当前风险指标
            
        Returns:
            通知内容字典
        """
        # 严重程度映射
        severity_map = {
            'INFO': '📘 信息',
            'WARNING': '⚠️ 警告',
            'CRITICAL': '🚨 严重'
        }
        
        # 预警类型映射
        alert_type_map = {
            'DRAWDOWN': '回撤预警',
            'VAR': 'VaR预警',
            'CONCENTRATION': '持仓集中度预警',
            'LEVERAGE': '杠杆预警',
            'PNL': '盈亏预警'
        }
        
        severity_text = severity_map.get(alert.severity, alert.severity)
        alert_type_text = alert_type_map.get(alert.alert_type, alert.alert_type)
        
        # 基础内容
        title = f"【{severity_text}】{alert_type_text}"
        
        content_lines = [
            f"🕐 时间: {alert.timestamp.strftime('%Y-%m-%d %H:%M:%S')}",
            f"📊 类型: {alert_type_text}",
            f"⚡ 严重程度: {severity_text}",
            f"📝 详情: {alert.message}",
            f"📈 当前值: {alert.current_value:.4f}",
            f"🎯 阈值: {alert.threshold_value:.4f}",
            f"💡 建议: {alert.suggested_action}"
        ]
        
        # 添加当前风险指标（如果提供）
        if metrics:
            content_lines.extend([
                "",
                "📊 当前风险状况:",
                f"💰 总资产: {metrics.total_value:.2f} 元",
                f"📈 总盈亏: {metrics.total_pnl:.2f} 元",
                f"📉 当前回撤: {metrics.current_drawdown:.2%}",
                f"🎯 风险等级: {metrics.risk_level}",
                f"⚖️ 持仓集中度: {metrics.position_concentration:.2%}",
                f"📊 杠杆比率: {metrics.leverage_ratio:.2f}"
            ])
        
        content = "\n".join(content_lines)
        
        return {
            'title': title,
            'content': content,
            'severity': alert.severity,
            'alert_type': alert.alert_type
        }
    
    async def _send_email_notification(self, notification: Dict[str, str]):
        """
        发送邮件通知
        
        Args:
            notification: 通知内容
        """
        if not EMAIL_AVAILABLE:
            logger.warning("邮件功能不可用，跳过邮件发送")
            return
            
        try:
            # 创建邮件
            msg = MimeMultipart()
            msg['From'] = self.config.email_user
            msg['To'] = ', '.join(self.config.email_recipients)
            msg['Subject'] = Header(notification['title'], 'utf-8')
            
            # 邮件正文
            body = MimeText(notification['content'], 'plain', 'utf-8')
            msg.attach(body)
            
            # 发送邮件
            with smtplib.SMTP(self.config.smtp_server, self.config.smtp_port) as server:
                server.starttls()
                server.login(self.config.email_user, self.config.email_password)
                server.send_message(msg)
            
            logger.info("邮件通知发送成功")
            
        except Exception as e:
            logger.error(f"发送邮件通知失败: {e}")
    
    async def _send_dingtalk_notification(self, notification: Dict[str, str]):
        """
        发送钉钉通知
        
        Args:
            notification: 通知内容
        """
        try:
            # 钉钉消息格式
            message = {
                "msgtype": "text",
                "text": {
                    "content": f"{notification['title']}\n\n{notification['content']}"
                },
                "at": {
                    "isAtAll": notification['severity'] == 'CRITICAL'
                }
            }
            
            # 发送请求
            response = requests.post(
                self.config.dingtalk_webhook,
                json=message,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('errcode') == 0:
                    logger.info("钉钉通知发送成功")
                else:
                    logger.error(f"钉钉通知发送失败: {result.get('errmsg')}")
            else:
                logger.error(f"钉钉通知发送失败: HTTP {response.status_code}")
                
        except Exception as e:
            logger.error(f"发送钉钉通知失败: {e}")
    
    async def _send_wechat_notification(self, notification: Dict[str, str]):
        """
        发送企业微信通知
        
        Args:
            notification: 通知内容
        """
        try:
            # 企业微信消息格式
            message = {
                "msgtype": "text",
                "text": {
                    "content": f"{notification['title']}\n\n{notification['content']}"
                }
            }
            
            # 发送请求
            response = requests.post(
                self.config.wechat_webhook,
                json=message,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('errcode') == 0:
                    logger.info("企业微信通知发送成功")
                else:
                    logger.error(f"企业微信通知发送失败: {result.get('errmsg')}")
            else:
                logger.error(f"企业微信通知发送失败: HTTP {response.status_code}")
                
        except Exception as e:
            logger.error(f"发送企业微信通知失败: {e}")
    
    async def _send_sms_notification(self, notification: Dict[str, str]):
        """
        发送短信通知（阿里云短信服务）
        
        Args:
            notification: 通知内容
        """
        try:
            # 这里是阿里云短信服务的示例代码
            # 实际使用时需要安装 alibabacloud_dysmsapi20170525 包
            logger.info("短信通知功能需要配置阿里云短信服务")
            
            # 简化的短信内容
            sms_content = f"{notification['title']}: {notification['content'][:50]}..."
            
            # 这里应该调用阿里云短信API
            # 由于需要额外的依赖包，这里只记录日志
            logger.info(f"模拟发送短信: {sms_content}")
            
        except Exception as e:
            logger.error(f"发送短信通知失败: {e}")
    
    def send_test_notification(self):
        """发送测试通知"""
        test_alert = RiskAlert(
            alert_id="TEST_ALERT",
            timestamp=datetime.now(),
            alert_type="TEST",
            severity="INFO",
            message="这是一条测试预警消息",
            current_value=0.1,
            threshold_value=0.05,
            suggested_action="这是测试，无需采取行动"
        )
        
        # 使用异步方式发送
        asyncio.create_task(self.send_alert_notification(test_alert))
        logger.info("测试通知已发送")


class NotificationManager:
    """通知管理器"""
    
    def __init__(self, config: NotificationConfig):
        """
        初始化通知管理器
        
        Args:
            config: 通知配置
        """
        self.config = config
        self.notifier = AlertNotifier(config)
        self.is_running = False
        self.notification_queue = asyncio.Queue()
        self.worker_task = None
        
        logger.info("通知管理器初始化完成")
    
    async def start(self):
        """启动通知管理器"""
        if self.is_running:
            logger.warning("通知管理器已在运行中")
            return
        
        self.is_running = True
        self.worker_task = asyncio.create_task(self._notification_worker())
        logger.info("通知管理器已启动")
    
    async def stop(self):
        """停止通知管理器"""
        self.is_running = False
        if self.worker_task:
            self.worker_task.cancel()
            try:
                await self.worker_task
            except asyncio.CancelledError:
                pass
        logger.info("通知管理器已停止")
    
    async def queue_notification(self, alert: RiskAlert, metrics: RiskMetrics = None):
        """
        将通知加入队列
        
        Args:
            alert: 风险预警
            metrics: 当前风险指标
        """
        await self.notification_queue.put((alert, metrics))
    
    async def _notification_worker(self):
        """通知工作线程"""
        while self.is_running:
            try:
                # 从队列获取通知任务
                alert, metrics = await asyncio.wait_for(
                    self.notification_queue.get(), 
                    timeout=1.0
                )
                
                # 发送通知
                await self.notifier.send_alert_notification(alert, metrics)
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                logger.error(f"通知工作线程错误: {e}")
                await asyncio.sleep(1)


if __name__ == "__main__":
    # 测试代码
    print("风险预警通知系统测试")
    
    # 创建通知配置
    config = NotificationConfig(
        email_enabled=False,  # 测试时关闭邮件
        dingtalk_enabled=False,  # 测试时关闭钉钉
        wechat_enabled=False,  # 测试时关闭微信
        sms_enabled=False  # 测试时关闭短信
    )
    
    # 创建通知器
    notifier = AlertNotifier(config)
    
    # 发送测试通知
    notifier.send_test_notification()
    
    print("测试完成")