"""
集成风险监控系统
Integrated Risk Monitoring System

整合实时风险监控、预警通知、仪表板等功能的完整风险管理系统
"""

import asyncio
import logging
import json
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from pathlib import Path

from .realtime_risk_monitor import RealtimeRiskMonitor, RiskMetrics, RiskAlert
from .alert_notifier import AlertNotifier, NotificationConfig, NotificationManager
from .risk_dashboard import RiskDashboard

logger = logging.getLogger(__name__)


class IntegratedRiskSystem:
    """集成风险监控系统"""
    
    def __init__(self, config: Dict = None):
        """
        初始化集成风险监控系统
        
        Args:
            config: 系统配置
        """
        self.config = config or {}
        self.is_running = False
        
        # 初始化各个组件
        self._init_components()
        
        # 事件回调
        self.alert_callbacks = []  # 预警回调函数列表
        self.metrics_callbacks = []  # 指标更新回调函数列表
        
        # 系统状态
        self.start_time = None
        self.last_health_check = None
        self.system_status = "STOPPED"
        
        logger.info("集成风险监控系统初始化完成")
    
    def _init_components(self):
        """初始化系统组件"""
        # 风险监控器配置
        monitor_config = self.config.get('monitor', {})
        self.risk_monitor = RealtimeRiskMonitor(monitor_config)
        
        # 通知系统配置
        notification_config_dict = self.config.get('notification', {})
        notification_config = NotificationConfig(**notification_config_dict)
        self.notification_manager = NotificationManager(notification_config)
        
        # 仪表板配置
        dashboard_config = self.config.get('dashboard', {})
        dashboard_port = dashboard_config.get('port', 8080)
        self.dashboard = RiskDashboard(self.risk_monitor, dashboard_port)
        
        # 创建仪表板模板
        self.dashboard.create_dashboard_template()
        
        logger.info("系统组件初始化完成")
    
    async def start_system(self):
        """启动完整的风险监控系统"""
        if self.is_running:
            logger.warning("风险监控系统已在运行中")
            return
        
        try:
            self.is_running = True
            self.start_time = datetime.now()
            self.system_status = "STARTING"
            
            logger.info("正在启动集成风险监控系统...")
            
            # 启动风险监控器
            self.risk_monitor.start_monitoring()
            logger.info("✓ 风险监控器已启动")
            
            # 启动通知管理器
            await self.notification_manager.start()
            logger.info("✓ 通知管理器已启动")
            
            # 启动仪表板
            self.dashboard.start_server()
            logger.info("✓ 风险仪表板已启动")
            
            # 启动系统监控循环
            asyncio.create_task(self._system_monitoring_loop())
            
            self.system_status = "RUNNING"
            logger.info("🚀 集成风险监控系统启动完成")
            
            # 发送系统启动通知
            await self._send_system_notification("系统启动", "风险监控系统已成功启动", "INFO")
            
        except Exception as e:
            self.system_status = "ERROR"
            logger.error(f"启动风险监控系统失败: {e}")
            raise
    
    async def stop_system(self):
        """停止风险监控系统"""
        if not self.is_running:
            logger.warning("风险监控系统未在运行")
            return
        
        try:
            self.system_status = "STOPPING"
            logger.info("正在停止集成风险监控系统...")
            
            # 发送系统停止通知
            await self._send_system_notification("系统停止", "风险监控系统正在停止", "INFO")
            
            # 停止各个组件
            self.risk_monitor.stop_monitoring()
            logger.info("✓ 风险监控器已停止")
            
            await self.notification_manager.stop()
            logger.info("✓ 通知管理器已停止")
            
            self.dashboard.stop_server()
            logger.info("✓ 风险仪表板已停止")
            
            self.is_running = False
            self.system_status = "STOPPED"
            logger.info("🛑 集成风险监控系统已停止")
            
        except Exception as e:
            self.system_status = "ERROR"
            logger.error(f"停止风险监控系统失败: {e}")
            raise
    
    async def _system_monitoring_loop(self):
        """系统监控主循环"""
        while self.is_running:
            try:
                # 健康检查
                await self._health_check()
                
                # 检查新的预警
                await self._check_and_process_alerts()
                
                # 执行指标回调
                await self._execute_metrics_callbacks()
                
                # 等待下一次检查
                await asyncio.sleep(5)  # 每5秒检查一次
                
            except Exception as e:
                logger.error(f"系统监控循环错误: {e}")
                await asyncio.sleep(1)
    
    async def _health_check(self):
        """系统健康检查"""
        try:
            current_time = datetime.now()
            
            # 检查各组件状态
            monitor_healthy = self.risk_monitor.is_running
            dashboard_healthy = self.dashboard.is_running
            notification_healthy = self.notification_manager.is_running
            
            # 检查数据更新时间
            last_update = self.risk_monitor.last_update_time
            update_delay = (current_time - last_update).total_seconds()
            data_healthy = update_delay < 60  # 数据更新延迟不超过60秒
            
            # 综合健康状态
            system_healthy = all([monitor_healthy, dashboard_healthy, notification_healthy, data_healthy])
            
            if not system_healthy:
                logger.warning(f"系统健康检查异常: 监控器={monitor_healthy}, 仪表板={dashboard_healthy}, "
                             f"通知={notification_healthy}, 数据={data_healthy}")
                
                # 如果系统不健康，尝试重启组件
                await self._handle_unhealthy_system()
            
            self.last_health_check = current_time
            
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
    
    async def _handle_unhealthy_system(self):
        """处理不健康的系统状态"""
        try:
            logger.info("检测到系统异常，尝试恢复...")
            
            # 重启风险监控器
            if not self.risk_monitor.is_running:
                self.risk_monitor.start_monitoring()
                logger.info("风险监控器已重启")
            
            # 重启仪表板
            if not self.dashboard.is_running:
                self.dashboard.start_server()
                logger.info("风险仪表板已重启")
            
            # 发送系统恢复通知
            await self._send_system_notification("系统恢复", "检测到异常并已自动恢复", "WARNING")
            
        except Exception as e:
            logger.error(f"系统恢复失败: {e}")
            await self._send_system_notification("系统故障", f"系统恢复失败: {e}", "CRITICAL")
    
    async def _check_and_process_alerts(self):
        """检查并处理新的预警"""
        try:
            # 获取当前活跃预警
            current_alerts = self.risk_monitor.get_active_alerts()
            
            # 获取当前风险指标
            current_metrics = self.risk_monitor.get_current_metrics()
            
            # 处理每个预警
            for alert in current_alerts:
                # 发送通知
                await self.notification_manager.queue_notification(alert, current_metrics)
                
                # 执行预警回调
                await self._execute_alert_callbacks(alert, current_metrics)
            
        except Exception as e:
            logger.error(f"处理预警失败: {e}")
    
    async def _execute_alert_callbacks(self, alert: RiskAlert, metrics: RiskMetrics):
        """执行预警回调函数"""
        for callback in self.alert_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(alert, metrics)
                else:
                    callback(alert, metrics)
            except Exception as e:
                logger.error(f"执行预警回调失败: {e}")
    
    async def _execute_metrics_callbacks(self):
        """执行指标更新回调函数"""
        try:
            current_metrics = self.risk_monitor.get_current_metrics()
            
            for callback in self.metrics_callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(current_metrics)
                    else:
                        callback(current_metrics)
                except Exception as e:
                    logger.error(f"执行指标回调失败: {e}")
                    
        except Exception as e:
            logger.error(f"执行指标回调失败: {e}")
    
    async def _send_system_notification(self, title: str, message: str, severity: str):
        """发送系统通知"""
        try:
            system_alert = RiskAlert(
                alert_id=f"SYSTEM_{int(time.time())}",
                timestamp=datetime.now(),
                alert_type="SYSTEM",
                severity=severity,
                message=message,
                current_value=0.0,
                threshold_value=0.0,
                suggested_action="请检查系统状态"
            )
            
            await self.notification_manager.queue_notification(system_alert)
            
        except Exception as e:
            logger.error(f"发送系统通知失败: {e}")
    
    def update_positions(self, positions_data: Dict[str, Dict]):
        """
        更新持仓数据
        
        Args:
            positions_data: 持仓数据
        """
        self.risk_monitor.update_positions(positions_data)
    
    def update_account_info(self, account_data: Dict):
        """
        更新账户信息
        
        Args:
            account_data: 账户数据
        """
        self.risk_monitor.update_account_info(account_data)
    
    def get_system_status(self) -> Dict[str, Any]:
        """
        获取系统状态
        
        Returns:
            系统状态信息
        """
        uptime = (datetime.now() - self.start_time).total_seconds() if self.start_time else 0
        
        return {
            'system_status': self.system_status,
            'is_running': self.is_running,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'uptime_seconds': uptime,
            'last_health_check': self.last_health_check.isoformat() if self.last_health_check else None,
            'components': {
                'risk_monitor': self.risk_monitor.is_running,
                'notification_manager': self.notification_manager.is_running,
                'dashboard': self.dashboard.is_running
            },
            'dashboard_url': f"http://localhost:{self.dashboard.port}",
            'risk_summary': self.risk_monitor.get_risk_summary()
        }
    
    def add_alert_callback(self, callback: Callable):
        """
        添加预警回调函数
        
        Args:
            callback: 回调函数，接收 (alert, metrics) 参数
        """
        self.alert_callbacks.append(callback)
        logger.info(f"已添加预警回调函数: {callback.__name__}")
    
    def add_metrics_callback(self, callback: Callable):
        """
        添加指标更新回调函数
        
        Args:
            callback: 回调函数，接收 (metrics) 参数
        """
        self.metrics_callbacks.append(callback)
        logger.info(f"已添加指标回调函数: {callback.__name__}")
    
    def export_system_report(self, filepath: str = None) -> str:
        """
        导出系统报告
        
        Args:
            filepath: 导出文件路径
            
        Returns:
            报告内容
        """
        try:
            system_status = self.get_system_status()
            risk_report = self.risk_monitor.export_risk_report()
            
            report_data = {
                'report_time': datetime.now().isoformat(),
                'system_status': system_status,
                'risk_report': json.loads(risk_report) if risk_report != "{}" else {}
            }
            
            report_json = json.dumps(report_data, indent=2, ensure_ascii=False)
            
            if filepath:
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(report_json)
                logger.info(f"系统报告已导出到: {filepath}")
            
            return report_json
            
        except Exception as e:
            logger.error(f"导出系统报告失败: {e}")
            return "{}"


# 系统工厂类
class RiskSystemFactory:
    """风险系统工厂"""
    
    @staticmethod
    def create_system(config_file: str = None, config_dict: Dict = None) -> IntegratedRiskSystem:
        """
        创建风险监控系统
        
        Args:
            config_file: 配置文件路径
            config_dict: 配置字典
            
        Returns:
            集成风险监控系统实例
        """
        config = {}
        
        # 从文件加载配置
        if config_file and Path(config_file).exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                logger.info(f"已加载配置文件: {config_file}")
            except Exception as e:
                logger.error(f"加载配置文件失败: {e}")
        
        # 合并字典配置
        if config_dict:
            config.update(config_dict)
        
        # 使用默认配置
        if not config:
            config = RiskSystemFactory.get_default_config()
            logger.info("使用默认配置")
        
        return IntegratedRiskSystem(config)
    
    @staticmethod
    def get_default_config() -> Dict:
        """获取默认配置"""
        return {
            'monitor': {
                'max_drawdown': 0.30,
                'daily_loss_limit': 0.15,
                'var_limit': 0.10,
                'position_concentration': 0.50,
                'leverage_limit': 2.0,
                'update_interval': 1.0
            },
            'notification': {
                'email_enabled': False,
                'dingtalk_enabled': False,
                'wechat_enabled': False,
                'sms_enabled': False,
                'min_notification_interval': 300,
                'max_notifications_per_hour': 10
            },
            'dashboard': {
                'port': 8080
            }
        }
    
    @staticmethod
    def save_config(config: Dict, filepath: str):
        """
        保存配置到文件
        
        Args:
            config: 配置字典
            filepath: 文件路径
        """
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            logger.info(f"配置已保存到: {filepath}")
        except Exception as e:
            logger.error(f"保存配置失败: {e}")


if __name__ == "__main__":
    # 测试代码
    async def main():
        print("集成风险监控系统测试")
        
        # 创建系统
        system = RiskSystemFactory.create_system()
        
        # 添加回调函数
        def alert_callback(alert, metrics):
            print(f"收到预警: {alert.message}")
        
        def metrics_callback(metrics):
            print(f"风险等级: {metrics.risk_level}, 回撤: {metrics.current_drawdown:.2%}")
        
        system.add_alert_callback(alert_callback)
        system.add_metrics_callback(metrics_callback)
        
        try:
            # 启动系统
            await system.start_system()
            
            # 模拟数据更新
            positions_data = {
                'AAPL': {
                    'quantity': 100,
                    'avg_cost': 150.0,
                    'current_price': 155.0,
                    'beta': 1.2,
                    'volatility': 0.25
                }
            }
            
            system.update_positions(positions_data)
            
            account_data = {
                'total_capital': 100000.0,
                'available_cash': 20000.0,
                'total_value': 95000.0,
                'initial_capital': 100000.0
            }
            
            system.update_account_info(account_data)
            
            # 运行一段时间
            print("系统运行中，访问 http://localhost:8080 查看仪表板")
            await asyncio.sleep(30)
            
            # 获取系统状态
            status = system.get_system_status()
            print(f"系统状态: {json.dumps(status, indent=2, ensure_ascii=False)}")
            
        finally:
            # 停止系统
            await system.stop_system()
    
    # 运行测试
    asyncio.run(main())