"""
实时风险监控引擎测试
Test for Real-time Risk Monitoring Engine

测试实时PnL计算、动态风险指标计算、风险预警和报警机制
"""

import asyncio
import json
import logging
import time
import unittest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
import sys
import os
import numpy as np

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))

from qlib_trading_system.risk.monitors.realtime_risk_monitor import (
    RealtimeRiskMonitor, RiskMetrics, RiskAlert, PositionRisk
)
from qlib_trading_system.risk.monitors.alert_notifier import (
    AlertNotifier, NotificationConfig, NotificationManager
)
from qlib_trading_system.risk.monitors.risk_dashboard import RiskDashboard
from qlib_trading_system.risk.monitors.integrated_risk_system import (
    IntegratedRiskSystem, RiskSystemFactory
)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class TestRealtimeRiskMonitor(unittest.TestCase):
    """实时风险监控引擎测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.config = {
            'max_drawdown': 0.20,
            'daily_loss_limit': 0.10,
            'var_limit': 0.08,
            'position_concentration': 0.40,
            'leverage_limit': 1.8,
            'update_interval': 0.5
        }
        self.monitor = RealtimeRiskMonitor(self.config)
        
    def tearDown(self):
        """测试后清理"""
        if self.monitor.is_running:
            self.monitor.stop_monitoring()
    
    def test_monitor_initialization(self):
        """测试监控器初始化"""
        print("\n=== 测试监控器初始化 ===")
        
        # 检查初始化状态
        self.assertFalse(self.monitor.is_running)
        self.assertEqual(len(self.monitor.positions), 0)
        self.assertEqual(len(self.monitor.active_alerts), 0)
        
        # 检查配置
        self.assertEqual(self.monitor.risk_thresholds['max_drawdown'], 0.20)
        self.assertEqual(self.monitor.risk_thresholds['daily_loss_limit'], 0.10)
        
        print("✓ 监控器初始化测试通过")
    
    def test_position_update(self):
        """测试持仓数据更新"""
        print("\n=== 测试持仓数据更新 ===")
        
        # 模拟持仓数据
        positions_data = {
            'AAPL': {
                'quantity': 100,
                'avg_cost': 150.0,
                'current_price': 155.0,
                'beta': 1.2,
                'volatility': 0.25
            },
            'GOOGL': {
                'quantity': 50,
                'avg_cost': 2800.0,
                'current_price': 2750.0,
                'beta': 1.1,
                'volatility': 0.30
            }
        }
        
        # 更新持仓
        self.monitor.update_positions(positions_data)
        
        # 验证持仓数据
        self.assertEqual(len(self.monitor.positions), 2)
        self.assertIn('AAPL', self.monitor.positions)
        self.assertIn('GOOGL', self.monitor.positions)
        
        # 检查AAPL持仓
        aapl_pos = self.monitor.positions['AAPL']
        self.assertEqual(aapl_pos.quantity, 100)
        self.assertEqual(aapl_pos.avg_cost, 150.0)
        self.assertEqual(aapl_pos.current_price, 155.0)
        self.assertEqual(aapl_pos.unrealized_pnl, 500.0)  # (155-150)*100
        
        # 检查GOOGL持仓
        googl_pos = self.monitor.positions['GOOGL']
        self.assertEqual(googl_pos.quantity, 50)
        self.assertEqual(googl_pos.unrealized_pnl, -2500.0)  # (2750-2800)*50
        
        print(f"✓ AAPL持仓: 数量={aapl_pos.quantity}, 成本={aapl_pos.avg_cost}, 现价={aapl_pos.current_price}, 盈亏={aapl_pos.unrealized_pnl}")
        print(f"✓ GOOGL持仓: 数量={googl_pos.quantity}, 成本={googl_pos.avg_cost}, 现价={googl_pos.current_price}, 盈亏={googl_pos.unrealized_pnl}")
        print("✓ 持仓数据更新测试通过")
    
    def test_risk_metrics_calculation(self):
        """测试风险指标计算"""
        print("\n=== 测试风险指标计算 ===")
        
        # 设置账户信息
        account_data = {
            'total_capital': 100000.0,
            'available_cash': 20000.0,
            'total_value': 95000.0,
            'initial_capital': 100000.0
        }
        self.monitor.update_account_info(account_data)
        
        # 添加持仓数据
        positions_data = {
            'AAPL': {
                'quantity': 100,
                'avg_cost': 150.0,
                'current_price': 145.0,  # 亏损
                'beta': 1.2,
                'volatility': 0.25
            }
        }
        self.monitor.update_positions(positions_data)
        
        # 更新风险指标
        self.monitor.update_risk_metrics()
        
        # 检查计算结果
        metrics = self.monitor.get_current_metrics()
        
        self.assertIsInstance(metrics, RiskMetrics)
        self.assertEqual(metrics.total_pnl, -5000.0)  # 95000 - 100000
        self.assertEqual(metrics.unrealized_pnl, -500.0)  # (145-150)*100
        self.assertGreater(metrics.position_concentration, 0)
        
        print(f"✓ 总盈亏: {metrics.total_pnl}")
        print(f"✓ 未实现盈亏: {metrics.unrealized_pnl}")
        print(f"✓ 当前回撤: {metrics.current_drawdown:.2%}")
        print(f"✓ 持仓集中度: {metrics.position_concentration:.2%}")
        print(f"✓ 风险等级: {metrics.risk_level}")
        print("✓ 风险指标计算测试通过")
    
    def test_var_calculation(self):
        """测试VaR计算"""
        print("\n=== 测试VaR计算 ===")
        
        # 模拟历史PnL数据
        np.random.seed(42)  # 设置随机种子以确保结果可重现
        for i in range(50):
            pnl = 100000 + np.random.normal(0, 1000) * i  # 模拟PnL变化
            self.monitor.pnl_history.append(pnl)
        
        # 计算VaR
        var_1d, var_5d = self.monitor._calculate_var()
        
        self.assertGreater(var_1d, 0)
        self.assertGreater(var_5d, var_1d)  # 5日VaR应该大于1日VaR
        
        print(f"✓ 1日VaR: {var_1d:.2f}")
        print(f"✓ 5日VaR: {var_5d:.2f}")
        print("✓ VaR计算测试通过")
    
    def test_alert_generation(self):
        """测试预警生成"""
        print("\n=== 测试预警生成 ===")
        
        # 设置会触发预警的数据
        account_data = {
            'total_capital': 100000.0,
            'available_cash': 10000.0,
            'total_value': 70000.0,  # 30%回撤
            'initial_capital': 100000.0
        }
        self.monitor.update_account_info(account_data)
        
        # 添加集中度过高的持仓
        positions_data = {
            'AAPL': {
                'quantity': 1000,
                'avg_cost': 100.0,
                'current_price': 70.0,  # 大幅亏损
                'beta': 1.2,
                'volatility': 0.25
            }
        }
        self.monitor.update_positions(positions_data)
        
        # 更新指标并检查预警
        self.monitor.update_risk_metrics()
        self.monitor.check_risk_alerts()
        
        # 检查是否生成了预警
        active_alerts = self.monitor.get_active_alerts()
        self.assertGreater(len(active_alerts), 0)
        
        print(f"✓ 生成了 {len(active_alerts)} 个预警:")
        for alert in active_alerts:
            print(f"  - {alert.alert_type}: {alert.message}")
        
        print("✓ 预警生成测试通过")
    
    def test_monitoring_loop(self):
        """测试监控循环"""
        print("\n=== 测试监控循环 ===")
        
        # 启动监控
        self.monitor.start_monitoring()
        self.assertTrue(self.monitor.is_running)
        
        # 运行一段时间
        time.sleep(2)
        
        # 检查是否正常运行
        self.assertTrue(self.monitor.is_running)
        
        # 停止监控
        self.monitor.stop_monitoring()
        self.assertFalse(self.monitor.is_running)
        
        print("✓ 监控循环测试通过")
    
    def test_risk_report_export(self):
        """测试风险报告导出"""
        print("\n=== 测试风险报告导出 ===")
        
        # 添加一些数据
        positions_data = {
            'AAPL': {
                'quantity': 100,
                'avg_cost': 150.0,
                'current_price': 155.0,
                'beta': 1.2,
                'volatility': 0.25
            }
        }
        self.monitor.update_positions(positions_data)
        self.monitor.update_risk_metrics()
        
        # 导出报告
        report = self.monitor.export_risk_report()
        
        # 验证报告内容
        self.assertIsInstance(report, str)
        self.assertNotEqual(report, "{}")
        
        # 解析JSON
        report_data = json.loads(report)
        self.assertIn('report_time', report_data)
        self.assertIn('risk_metrics', report_data)
        self.assertIn('positions', report_data)
        
        print("✓ 风险报告导出测试通过")
        print(f"  报告大小: {len(report)} 字符")


class TestAlertNotifier(unittest.TestCase):
    """预警通知系统测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.config = NotificationConfig(
            email_enabled=False,  # 测试时关闭实际通知
            dingtalk_enabled=False,
            wechat_enabled=False,
            sms_enabled=False
        )
        self.notifier = AlertNotifier(self.config)
    
    def test_notification_config(self):
        """测试通知配置"""
        print("\n=== 测试通知配置 ===")
        
        self.assertFalse(self.config.email_enabled)
        self.assertFalse(self.config.dingtalk_enabled)
        self.assertEqual(self.config.min_notification_interval, 300)
        self.assertEqual(self.config.max_notifications_per_hour, 10)
        
        print("✓ 通知配置测试通过")
    
    def test_notification_content_preparation(self):
        """测试通知内容准备"""
        print("\n=== 测试通知内容准备 ===")
        
        # 创建测试预警
        alert = RiskAlert(
            alert_id="TEST_ALERT",
            timestamp=datetime.now(),
            alert_type="DRAWDOWN",
            severity="WARNING",
            message="测试回撤预警",
            current_value=0.15,
            threshold_value=0.10,
            suggested_action="建议减仓"
        )
        
        # 准备通知内容
        content = self.notifier._prepare_notification_content(alert)
        
        self.assertIn('title', content)
        self.assertIn('content', content)
        self.assertIn('severity', content)
        self.assertIn('回撤预警', content['title'])
        self.assertIn('测试回撤预警', content['content'])
        
        print(f"✓ 通知标题: {content['title']}")
        print(f"✓ 通知内容长度: {len(content['content'])} 字符")
        print("✓ 通知内容准备测试通过")
    
    def test_notification_frequency_control(self):
        """测试通知频率控制"""
        print("\n=== 测试通知频率控制 ===")
        
        alert = RiskAlert(
            alert_id="FREQ_TEST",
            timestamp=datetime.now(),
            alert_type="TEST",
            severity="INFO",
            message="频率测试",
            current_value=0.1,
            threshold_value=0.05,
            suggested_action="无需操作"
        )
        
        # 第一次应该允许发送
        should_send_1 = self.notifier._should_send_notification(alert)
        self.assertTrue(should_send_1)
        
        # 更新记录
        self.notifier._update_notification_record(alert)
        
        # 立即再次检查，应该被频率限制
        should_send_2 = self.notifier._should_send_notification(alert)
        self.assertFalse(should_send_2)
        
        print("✓ 通知频率控制测试通过")


class TestRiskDashboard(unittest.TestCase):
    """风险仪表板测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.monitor = RealtimeRiskMonitor()
        self.dashboard = RiskDashboard(self.monitor, port=8081)  # 使用不同端口避免冲突
    
    def tearDown(self):
        """测试后清理"""
        if self.dashboard.is_running:
            self.dashboard.stop_server()
        if self.monitor.is_running:
            self.monitor.stop_monitoring()
    
    def test_dashboard_initialization(self):
        """测试仪表板初始化"""
        print("\n=== 测试仪表板初始化 ===")
        
        self.assertEqual(self.dashboard.port, 8081)
        self.assertFalse(self.dashboard.is_running)
        self.assertEqual(len(self.dashboard.websocket_connections), 0)
        
        print("✓ 仪表板初始化测试通过")
    
    def test_chart_generation(self):
        """测试图表生成"""
        print("\n=== 测试图表生成 ===")
        
        # 添加一些历史数据
        for i in range(10):
            self.monitor.pnl_history.append(100000 + i * 1000)
        
        # 生成PnL图表
        pnl_chart = self.dashboard.generate_pnl_chart()
        
        if pnl_chart:
            self.assertIsInstance(pnl_chart, str)
            self.assertGreater(len(pnl_chart), 100)  # Base64编码的图片应该比较长
            print(f"✓ PnL图表生成成功，大小: {len(pnl_chart)} 字符")
        else:
            print("⚠ PnL图表生成为空（可能是matplotlib配置问题）")
        
        print("✓ 图表生成测试通过")
    
    def test_template_creation(self):
        """测试模板创建"""
        print("\n=== 测试模板创建 ===")
        
        # 创建模板
        self.dashboard.create_dashboard_template()
        
        # 检查模板文件是否存在
        from pathlib import Path
        template_path = Path(__file__).parent / "templates" / "dashboard.html"
        
        if template_path.exists():
            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            self.assertIn('风险监控仪表板', content)
            self.assertIn('PnL走势图', content)
            self.assertIn('回撤监控图', content)
            
            print(f"✓ 模板文件已创建: {template_path}")
            print(f"✓ 模板大小: {len(content)} 字符")
        else:
            print("⚠ 模板文件未创建")
        
        print("✓ 模板创建测试通过")


class TestIntegratedRiskSystem(unittest.TestCase):
    """集成风险系统测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.config = {
            'monitor': {
                'max_drawdown': 0.25,
                'daily_loss_limit': 0.12,
                'update_interval': 0.5
            },
            'notification': {
                'email_enabled': False,
                'dingtalk_enabled': False,
                'min_notification_interval': 60
            },
            'dashboard': {
                'port': 8082
            }
        }
        self.system = IntegratedRiskSystem(self.config)
    
    def tearDown(self):
        """测试后清理"""
        if self.system.is_running:
            asyncio.run(self.system.stop_system())
    
    def test_system_initialization(self):
        """测试系统初始化"""
        print("\n=== 测试集成系统初始化 ===")
        
        self.assertFalse(self.system.is_running)
        self.assertEqual(self.system.system_status, "STOPPED")
        self.assertIsNotNone(self.system.risk_monitor)
        self.assertIsNotNone(self.system.notification_manager)
        self.assertIsNotNone(self.system.dashboard)
        
        print("✓ 集成系统初始化测试通过")
    
    def test_system_startup_shutdown(self):
        """测试系统启动和关闭"""
        print("\n=== 测试系统启动和关闭 ===")
        
        async def test_lifecycle():
            # 启动系统
            await self.system.start_system()
            self.assertTrue(self.system.is_running)
            self.assertEqual(self.system.system_status, "RUNNING")
            
            # 等待一段时间
            await asyncio.sleep(2)
            
            # 检查组件状态
            self.assertTrue(self.system.risk_monitor.is_running)
            self.assertTrue(self.system.notification_manager.is_running)
            
            # 停止系统
            await self.system.stop_system()
            self.assertFalse(self.system.is_running)
            self.assertEqual(self.system.system_status, "STOPPED")
        
        asyncio.run(test_lifecycle())
        print("✓ 系统启动和关闭测试通过")
    
    def test_callback_system(self):
        """测试回调系统"""
        print("\n=== 测试回调系统 ===")
        
        # 回调函数计数器
        alert_count = 0
        metrics_count = 0
        
        def alert_callback(alert, metrics):
            nonlocal alert_count
            alert_count += 1
            print(f"  收到预警回调: {alert.message}")
        
        def metrics_callback(metrics):
            nonlocal metrics_count
            metrics_count += 1
            print(f"  收到指标回调: 风险等级={metrics.risk_level}")
        
        # 添加回调
        self.system.add_alert_callback(alert_callback)
        self.system.add_metrics_callback(metrics_callback)
        
        self.assertEqual(len(self.system.alert_callbacks), 1)
        self.assertEqual(len(self.system.metrics_callbacks), 1)
        
        print("✓ 回调系统测试通过")
    
    def test_system_status(self):
        """测试系统状态获取"""
        print("\n=== 测试系统状态获取 ===")
        
        status = self.system.get_system_status()
        
        self.assertIn('system_status', status)
        self.assertIn('is_running', status)
        self.assertIn('components', status)
        self.assertIn('dashboard_url', status)
        self.assertIn('risk_summary', status)
        
        print(f"✓ 系统状态: {status['system_status']}")
        print(f"✓ 仪表板URL: {status['dashboard_url']}")
        print("✓ 系统状态获取测试通过")


class TestRiskSystemFactory(unittest.TestCase):
    """风险系统工厂测试类"""
    
    def test_default_config(self):
        """测试默认配置"""
        print("\n=== 测试默认配置 ===")
        
        config = RiskSystemFactory.get_default_config()
        
        self.assertIn('monitor', config)
        self.assertIn('notification', config)
        self.assertIn('dashboard', config)
        
        # 检查监控配置
        monitor_config = config['monitor']
        self.assertIn('max_drawdown', monitor_config)
        self.assertIn('daily_loss_limit', monitor_config)
        
        print("✓ 默认配置测试通过")
    
    def test_system_creation(self):
        """测试系统创建"""
        print("\n=== 测试系统创建 ===")
        
        # 使用默认配置创建系统
        system = RiskSystemFactory.create_system()
        
        self.assertIsInstance(system, IntegratedRiskSystem)
        self.assertIsNotNone(system.risk_monitor)
        self.assertIsNotNone(system.notification_manager)
        self.assertIsNotNone(system.dashboard)
        
        print("✓ 系统创建测试通过")


def run_comprehensive_test():
    """运行综合测试"""
    print("🚀 开始实时风险监控引擎综合测试")
    print("=" * 60)
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试用例
    test_suite.addTest(unittest.makeSuite(TestRealtimeRiskMonitor))
    test_suite.addTest(unittest.makeSuite(TestAlertNotifier))
    test_suite.addTest(unittest.makeSuite(TestRiskDashboard))
    test_suite.addTest(unittest.makeSuite(TestIntegratedRiskSystem))
    test_suite.addTest(unittest.makeSuite(TestRiskSystemFactory))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    print(f"✅ 成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"❌ 失败: {len(result.failures)}")
    print(f"💥 错误: {len(result.errors)}")
    print(f"📈 成功率: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    if result.failures:
        print("\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback}")
    
    if result.errors:
        print("\n💥 错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback}")
    
    return result.wasSuccessful()


async def run_integration_demo():
    """运行集成演示"""
    print("\n🎯 开始集成风险监控系统演示")
    print("=" * 60)
    
    try:
        # 创建系统
        print("1. 创建集成风险监控系统...")
        system = RiskSystemFactory.create_system()
        
        # 添加回调函数
        def alert_callback(alert, metrics):
            print(f"🚨 预警回调: {alert.alert_type} - {alert.message}")
        
        def metrics_callback(metrics):
            print(f"📊 指标更新: 风险等级={metrics.risk_level}, 回撤={metrics.current_drawdown:.2%}")
        
        system.add_alert_callback(alert_callback)
        system.add_metrics_callback(metrics_callback)
        
        # 启动系统
        print("2. 启动风险监控系统...")
        await system.start_system()
        
        # 模拟交易数据更新
        print("3. 模拟交易数据更新...")
        
        # 初始持仓
        positions_data = {
            'AAPL': {
                'quantity': 100,
                'avg_cost': 150.0,
                'current_price': 155.0,
                'beta': 1.2,
                'volatility': 0.25
            },
            'GOOGL': {
                'quantity': 10,
                'avg_cost': 2800.0,
                'current_price': 2850.0,
                'beta': 1.1,
                'volatility': 0.30
            }
        }
        
        account_data = {
            'total_capital': 100000.0,
            'available_cash': 30000.0,
            'total_value': 105000.0,
            'initial_capital': 100000.0
        }
        
        system.update_positions(positions_data)
        system.update_account_info(account_data)
        
        print("4. 系统运行中，监控风险指标...")
        await asyncio.sleep(3)
        
        # 模拟价格下跌触发预警
        print("5. 模拟价格下跌，触发风险预警...")
        positions_data['AAPL']['current_price'] = 120.0  # 大幅下跌
        positions_data['GOOGL']['current_price'] = 2500.0  # 大幅下跌
        account_data['total_value'] = 75000.0  # 总资产下降
        
        system.update_positions(positions_data)
        system.update_account_info(account_data)
        
        await asyncio.sleep(3)
        
        # 获取系统状态
        print("6. 获取系统状态...")
        status = system.get_system_status()
        print(f"   系统状态: {status['system_status']}")
        print(f"   运行时间: {status['uptime_seconds']:.1f}秒")
        print(f"   仪表板地址: {status['dashboard_url']}")
        
        # 导出系统报告
        print("7. 导出系统报告...")
        report_file = f"risk_system_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        report = system.export_system_report(report_file)
        print(f"   报告已导出: {report_file}")
        
        print("8. 演示完成，系统将继续运行30秒...")
        print(f"   请访问 {status['dashboard_url']} 查看实时仪表板")
        await asyncio.sleep(30)
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 停止系统
        print("9. 停止风险监控系统...")
        await system.stop_system()
        print("✅ 集成演示完成")


if __name__ == "__main__":
    print("🛡️ 实时风险监控引擎 - 完整测试套件")
    print("=" * 60)
    
    # 运行单元测试
    test_success = run_comprehensive_test()
    
    if test_success:
        print("\n✅ 所有单元测试通过！")
        
        # 询问是否运行集成演示
        try:
            response = input("\n是否运行集成演示？(y/n): ").lower().strip()
            if response in ['y', 'yes', '是']:
                asyncio.run(run_integration_demo())
        except KeyboardInterrupt:
            print("\n用户取消演示")
    else:
        print("\n❌ 部分测试失败，请检查错误信息")
    
    print("\n🎉 测试完成！")