"""
实时风险监控引擎
Real-time Risk Monitoring Engine

实现实时PnL计算、动态风险指标计算、风险预警和报警机制
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from collections import deque
import numpy as np
import pandas as pd
from threading import Lock, Thread
import json
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


@dataclass
class RiskMetrics:
    """风险指标数据类"""
    timestamp: datetime
    total_pnl: float = 0.0
    daily_pnl: float = 0.0
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0
    total_value: float = 0.0
    cash_balance: float = 0.0
    position_value: float = 0.0
    max_drawdown: float = 0.0
    current_drawdown: float = 0.0
    var_1d: float = 0.0  # 1日VaR
    var_5d: float = 0.0  # 5日VaR
    sharpe_ratio: float = 0.0
    volatility: float = 0.0
    win_rate: float = 0.0
    profit_loss_ratio: float = 0.0
    position_concentration: float = 0.0  # 持仓集中度
    leverage_ratio: float = 0.0  # 杠杆比率
    risk_level: str = "LOW"  # LOW, MEDIUM, HIGH, CRITICAL


@dataclass
class RiskAlert:
    """风险预警数据类"""
    alert_id: str
    timestamp: datetime
    alert_type: str  # DRAWDOWN, VAR, CONCENTRATION, LEVERAGE, PNL
    severity: str  # INFO, WARNING, CRITICAL
    message: str
    current_value: float
    threshold_value: float
    suggested_action: str
    is_resolved: bool = False


@dataclass
class PositionRisk:
    """单个持仓风险数据"""
    symbol: str
    quantity: float
    avg_cost: float
    current_price: float
    market_value: float
    unrealized_pnl: float
    unrealized_pnl_pct: float
    weight: float  # 在总资产中的权重
    beta: float = 1.0
    volatility: float = 0.0
    var_contribution: float = 0.0


class RealtimeRiskMonitor:
    """实时风险监控引擎"""
    
    def __init__(self, config: Dict = None):
        """
        初始化实时风险监控引擎
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        self.is_running = False
        self.lock = Lock()
        
        # 风险阈值配置
        self.risk_thresholds = {
            'max_drawdown': self.config.get('max_drawdown', 0.30),  # 最大回撤30%
            'daily_loss_limit': self.config.get('daily_loss_limit', 0.15),  # 日亏损限制15%
            'var_limit': self.config.get('var_limit', 0.10),  # VaR限制10%
            'position_concentration': self.config.get('position_concentration', 0.50),  # 单股最大50%
            'leverage_limit': self.config.get('leverage_limit', 2.0),  # 最大杠杆2倍
        }
        
        # 数据存储
        self.pnl_history = deque(maxlen=1000)  # 存储最近1000个PnL数据点
        self.risk_metrics_history = deque(maxlen=500)  # 存储最近500个风险指标
        self.active_alerts = {}  # 活跃的风险预警
        self.alert_history = deque(maxlen=1000)  # 预警历史
        
        # 当前状态
        self.current_metrics = RiskMetrics(timestamp=datetime.now())
        self.positions = {}  # symbol -> PositionRisk
        self.account_info = {
            'total_capital': 100000.0,  # 总资本
            'available_cash': 100000.0,  # 可用现金
            'total_value': 100000.0,  # 总资产价值
            'initial_capital': 100000.0,  # 初始资本
        }
        
        # 计算相关
        self.last_update_time = datetime.now()
        self.update_interval = self.config.get('update_interval', 1.0)  # 更新间隔（秒）
        
        logger.info("实时风险监控引擎初始化完成")
    
    def start_monitoring(self):
        """启动实时监控"""
        if self.is_running:
            logger.warning("风险监控已在运行中")
            return
        
        self.is_running = True
        logger.info("启动实时风险监控")
        
        # 启动监控线程
        monitor_thread = Thread(target=self._monitoring_loop, daemon=True)
        monitor_thread.start()
    
    def stop_monitoring(self):
        """停止实时监控"""
        self.is_running = False
        logger.info("停止实时风险监控")
    
    def _monitoring_loop(self):
        """监控主循环"""
        while self.is_running:
            try:
                start_time = time.time()
                
                # 更新风险指标
                self.update_risk_metrics()
                
                # 检查风险预警
                self.check_risk_alerts()
                
                # 计算处理时间
                processing_time = time.time() - start_time
                
                # 控制更新频率
                sleep_time = max(0, self.update_interval - processing_time)
                time.sleep(sleep_time)
                
            except Exception as e:
                logger.error(f"风险监控循环出错: {e}")
                time.sleep(1.0)
    
    def update_positions(self, positions_data: Dict[str, Dict]):
        """
        更新持仓数据
        
        Args:
            positions_data: 持仓数据 {symbol: {quantity, avg_cost, current_price, ...}}
        """
        with self.lock:
            self.positions.clear()
            total_market_value = 0.0
            
            for symbol, pos_data in positions_data.items():
                if pos_data.get('quantity', 0) == 0:
                    continue
                
                quantity = pos_data['quantity']
                avg_cost = pos_data['avg_cost']
                current_price = pos_data['current_price']
                market_value = quantity * current_price
                unrealized_pnl = (current_price - avg_cost) * quantity
                unrealized_pnl_pct = (current_price - avg_cost) / avg_cost if avg_cost > 0 else 0
                
                position_risk = PositionRisk(
                    symbol=symbol,
                    quantity=quantity,
                    avg_cost=avg_cost,
                    current_price=current_price,
                    market_value=market_value,
                    unrealized_pnl=unrealized_pnl,
                    unrealized_pnl_pct=unrealized_pnl_pct,
                    weight=0.0,  # 稍后计算
                    beta=pos_data.get('beta', 1.0),
                    volatility=pos_data.get('volatility', 0.2)
                )
                
                self.positions[symbol] = position_risk
                total_market_value += market_value
            
            # 计算权重
            if total_market_value > 0:
                for position in self.positions.values():
                    position.weight = position.market_value / total_market_value
            
            # 更新账户信息
            self.account_info['total_value'] = self.account_info['available_cash'] + total_market_value
            
            logger.debug(f"更新持仓数据: {len(self.positions)}个持仓, 总市值: {total_market_value:.2f}")
    
    def update_account_info(self, account_data: Dict):
        """
        更新账户信息
        
        Args:
            account_data: 账户数据
        """
        with self.lock:
            self.account_info.update(account_data)
            logger.debug(f"更新账户信息: 总资产 {self.account_info['total_value']:.2f}")
    
    def update_risk_metrics(self):
        """更新风险指标"""
        try:
            with self.lock:
                now = datetime.now()
                
                # 计算PnL相关指标
                total_unrealized_pnl = sum(pos.unrealized_pnl for pos in self.positions.values())
                total_position_value = sum(pos.market_value for pos in self.positions.values())
                total_value = self.account_info['total_value']
                initial_capital = self.account_info['initial_capital']
                
                # 计算总PnL
                total_pnl = total_value - initial_capital
                
                # 计算当日PnL（简化实现，实际应该基于当日开始时的资产价值）
                daily_pnl = total_pnl  # 简化处理
                
                # 计算回撤
                if len(self.pnl_history) > 0:
                    peak_value = max(self.pnl_history)
                    current_drawdown = (peak_value - total_pnl) / peak_value if peak_value > 0 else 0
                    max_drawdown = max(self.current_metrics.max_drawdown, current_drawdown)
                else:
                    current_drawdown = 0
                    max_drawdown = 0
                
                # 计算VaR
                var_1d, var_5d = self._calculate_var()
                
                # 计算其他指标
                sharpe_ratio = self._calculate_sharpe_ratio()
                volatility = self._calculate_volatility()
                win_rate = self._calculate_win_rate()
                profit_loss_ratio = self._calculate_profit_loss_ratio()
                
                # 计算持仓集中度（最大单股权重）
                position_concentration = max([pos.weight for pos in self.positions.values()]) if self.positions else 0
                
                # 计算杠杆比率
                leverage_ratio = total_position_value / total_value if total_value > 0 else 0
                
                # 确定风险等级
                risk_level = self._determine_risk_level(current_drawdown, var_1d, position_concentration, leverage_ratio)
                
                # 更新当前指标
                self.current_metrics = RiskMetrics(
                    timestamp=now,
                    total_pnl=total_pnl,
                    daily_pnl=daily_pnl,
                    unrealized_pnl=total_unrealized_pnl,
                    realized_pnl=total_pnl - total_unrealized_pnl,
                    total_value=total_value,
                    cash_balance=self.account_info['available_cash'],
                    position_value=total_position_value,
                    max_drawdown=max_drawdown,
                    current_drawdown=current_drawdown,
                    var_1d=var_1d,
                    var_5d=var_5d,
                    sharpe_ratio=sharpe_ratio,
                    volatility=volatility,
                    win_rate=win_rate,
                    profit_loss_ratio=profit_loss_ratio,
                    position_concentration=position_concentration,
                    leverage_ratio=leverage_ratio,
                    risk_level=risk_level
                )
                
                # 保存历史数据
                self.pnl_history.append(total_pnl)
                self.risk_metrics_history.append(self.current_metrics)
                
                self.last_update_time = now
                
        except Exception as e:
            logger.error(f"更新风险指标时出错: {e}")
    
    def _calculate_var(self, confidence_level: float = 0.95) -> Tuple[float, float]:
        """
        计算VaR (Value at Risk)
        
        Args:
            confidence_level: 置信水平
            
        Returns:
            (1日VaR, 5日VaR)
        """
        if len(self.pnl_history) < 10:
            return 0.0, 0.0
        
        try:
            # 计算收益率序列
            pnl_array = np.array(list(self.pnl_history))
            returns = np.diff(pnl_array) / pnl_array[:-1]
            returns = returns[~np.isnan(returns)]  # 移除NaN值
            
            if len(returns) < 5:
                return 0.0, 0.0
            
            # 计算VaR
            var_1d = np.percentile(returns, (1 - confidence_level) * 100)
            var_5d = var_1d * np.sqrt(5)  # 简化的时间调整
            
            # 转换为绝对金额
            current_value = self.account_info['total_value']
            var_1d_amount = abs(var_1d * current_value)
            var_5d_amount = abs(var_5d * current_value)
            
            return var_1d_amount, var_5d_amount
            
        except Exception as e:
            logger.error(f"计算VaR时出错: {e}")
            return 0.0, 0.0
    
    def _calculate_sharpe_ratio(self) -> float:
        """计算夏普比率"""
        if len(self.pnl_history) < 10:
            return 0.0
        
        try:
            pnl_array = np.array(list(self.pnl_history))
            returns = np.diff(pnl_array) / pnl_array[:-1]
            returns = returns[~np.isnan(returns)]
            
            if len(returns) < 5:
                return 0.0
            
            mean_return = np.mean(returns)
            std_return = np.std(returns)
            
            if std_return == 0:
                return 0.0
            
            # 假设无风险利率为3%年化
            risk_free_rate = 0.03 / 252  # 日化无风险利率
            sharpe = (mean_return - risk_free_rate) / std_return
            
            return sharpe * np.sqrt(252)  # 年化夏普比率
            
        except Exception as e:
            logger.error(f"计算夏普比率时出错: {e}")
            return 0.0
    
    def _calculate_volatility(self) -> float:
        """计算波动率"""
        if len(self.pnl_history) < 10:
            return 0.0
        
        try:
            pnl_array = np.array(list(self.pnl_history))
            returns = np.diff(pnl_array) / pnl_array[:-1]
            returns = returns[~np.isnan(returns)]
            
            if len(returns) < 5:
                return 0.0
            
            volatility = np.std(returns) * np.sqrt(252)  # 年化波动率
            return volatility
            
        except Exception as e:
            logger.error(f"计算波动率时出错: {e}")
            return 0.0
    
    def _calculate_win_rate(self) -> float:
        """计算胜率"""
        if len(self.pnl_history) < 10:
            return 0.0
        
        try:
            pnl_array = np.array(list(self.pnl_history))
            returns = np.diff(pnl_array)
            
            if len(returns) == 0:
                return 0.0
            
            win_count = np.sum(returns > 0)
            total_count = len(returns)
            
            return win_count / total_count
            
        except Exception as e:
            logger.error(f"计算胜率时出错: {e}")
            return 0.0
    
    def _calculate_profit_loss_ratio(self) -> float:
        """计算盈亏比"""
        if len(self.pnl_history) < 10:
            return 0.0
        
        try:
            pnl_array = np.array(list(self.pnl_history))
            returns = np.diff(pnl_array)
            
            if len(returns) == 0:
                return 0.0
            
            profits = returns[returns > 0]
            losses = returns[returns < 0]
            
            if len(profits) == 0 or len(losses) == 0:
                return 0.0
            
            avg_profit = np.mean(profits)
            avg_loss = abs(np.mean(losses))
            
            if avg_loss == 0:
                return 0.0
            
            return avg_profit / avg_loss
            
        except Exception as e:
            logger.error(f"计算盈亏比时出错: {e}")
            return 0.0
    
    def _determine_risk_level(self, drawdown: float, var: float, concentration: float, leverage: float) -> str:
        """
        确定风险等级
        
        Args:
            drawdown: 当前回撤
            var: VaR值
            concentration: 持仓集中度
            leverage: 杠杆比率
            
        Returns:
            风险等级: LOW, MEDIUM, HIGH, CRITICAL
        """
        risk_score = 0
        
        # 回撤风险评分
        if drawdown > 0.25:
            risk_score += 3
        elif drawdown > 0.15:
            risk_score += 2
        elif drawdown > 0.10:
            risk_score += 1
        
        # VaR风险评分
        var_pct = var / self.account_info['total_value'] if self.account_info['total_value'] > 0 else 0
        if var_pct > 0.15:
            risk_score += 3
        elif var_pct > 0.10:
            risk_score += 2
        elif var_pct > 0.05:
            risk_score += 1
        
        # 集中度风险评分
        if concentration > 0.80:
            risk_score += 3
        elif concentration > 0.60:
            risk_score += 2
        elif concentration > 0.40:
            risk_score += 1
        
        # 杠杆风险评分
        if leverage > 2.5:
            risk_score += 3
        elif leverage > 2.0:
            risk_score += 2
        elif leverage > 1.5:
            risk_score += 1
        
        # 确定风险等级
        if risk_score >= 8:
            return "CRITICAL"
        elif risk_score >= 5:
            return "HIGH"
        elif risk_score >= 2:
            return "MEDIUM"
        else:
            return "LOW"   
 
    def check_risk_alerts(self):
        """检查风险预警"""
        try:
            current_time = datetime.now()
            
            # 检查回撤预警
            self._check_drawdown_alert()
            
            # 检查VaR预警
            self._check_var_alert()
            
            # 检查持仓集中度预警
            self._check_concentration_alert()
            
            # 检查杠杆预警
            self._check_leverage_alert()
            
            # 检查日亏损预警
            self._check_daily_loss_alert()
            
            # 清理已解决的预警
            self._cleanup_resolved_alerts()
            
        except Exception as e:
            logger.error(f"检查风险预警时出错: {e}")
    
    def _check_drawdown_alert(self):
        """检查回撤预警"""
        current_drawdown = self.current_metrics.current_drawdown
        threshold = self.risk_thresholds['max_drawdown']
        
        alert_id = "DRAWDOWN_ALERT"
        
        if current_drawdown > threshold:
            if alert_id not in self.active_alerts:
                alert = RiskAlert(
                    alert_id=alert_id,
                    timestamp=datetime.now(),
                    alert_type="DRAWDOWN",
                    severity="CRITICAL" if current_drawdown > threshold * 1.2 else "WARNING",
                    message=f"当前回撤 {current_drawdown:.2%} 超过阈值 {threshold:.2%}",
                    current_value=current_drawdown,
                    threshold_value=threshold,
                    suggested_action="建议减仓或停止交易"
                )
                self.active_alerts[alert_id] = alert
                self.alert_history.append(alert)
                logger.warning(f"触发回撤预警: {alert.message}")
        else:
            if alert_id in self.active_alerts:
                self.active_alerts[alert_id].is_resolved = True
                logger.info("回撤预警已解除")
    
    def _check_var_alert(self):
        """检查VaR预警"""
        var_1d = self.current_metrics.var_1d
        total_value = self.current_metrics.total_value
        var_pct = var_1d / total_value if total_value > 0 else 0
        threshold = self.risk_thresholds['var_limit']
        
        alert_id = "VAR_ALERT"
        
        if var_pct > threshold:
            if alert_id not in self.active_alerts:
                alert = RiskAlert(
                    alert_id=alert_id,
                    timestamp=datetime.now(),
                    alert_type="VAR",
                    severity="WARNING",
                    message=f"1日VaR {var_pct:.2%} 超过阈值 {threshold:.2%}",
                    current_value=var_pct,
                    threshold_value=threshold,
                    suggested_action="建议降低仓位风险"
                )
                self.active_alerts[alert_id] = alert
                self.alert_history.append(alert)
                logger.warning(f"触发VaR预警: {alert.message}")
        else:
            if alert_id in self.active_alerts:
                self.active_alerts[alert_id].is_resolved = True
                logger.info("VaR预警已解除")
    
    def _check_concentration_alert(self):
        """检查持仓集中度预警"""
        concentration = self.current_metrics.position_concentration
        threshold = self.risk_thresholds['position_concentration']
        
        alert_id = "CONCENTRATION_ALERT"
        
        if concentration > threshold:
            if alert_id not in self.active_alerts:
                alert = RiskAlert(
                    alert_id=alert_id,
                    timestamp=datetime.now(),
                    alert_type="CONCENTRATION",
                    severity="WARNING",
                    message=f"持仓集中度 {concentration:.2%} 超过阈值 {threshold:.2%}",
                    current_value=concentration,
                    threshold_value=threshold,
                    suggested_action="建议分散持仓"
                )
                self.active_alerts[alert_id] = alert
                self.alert_history.append(alert)
                logger.warning(f"触发持仓集中度预警: {alert.message}")
        else:
            if alert_id in self.active_alerts:
                self.active_alerts[alert_id].is_resolved = True
                logger.info("持仓集中度预警已解除")
    
    def _check_leverage_alert(self):
        """检查杠杆预警"""
        leverage = self.current_metrics.leverage_ratio
        threshold = self.risk_thresholds['leverage_limit']
        
        alert_id = "LEVERAGE_ALERT"
        
        if leverage > threshold:
            if alert_id not in self.active_alerts:
                alert = RiskAlert(
                    alert_id=alert_id,
                    timestamp=datetime.now(),
                    alert_type="LEVERAGE",
                    severity="WARNING",
                    message=f"杠杆比率 {leverage:.2f} 超过阈值 {threshold:.2f}",
                    current_value=leverage,
                    threshold_value=threshold,
                    suggested_action="建议降低杠杆"
                )
                self.active_alerts[alert_id] = alert
                self.alert_history.append(alert)
                logger.warning(f"触发杠杆预警: {alert.message}")
        else:
            if alert_id in self.active_alerts:
                self.active_alerts[alert_id].is_resolved = True
                logger.info("杠杆预警已解除")
    
    def _check_daily_loss_alert(self):
        """检查日亏损预警"""
        daily_pnl = self.current_metrics.daily_pnl
        total_value = self.current_metrics.total_value
        daily_loss_pct = abs(daily_pnl) / total_value if total_value > 0 and daily_pnl < 0 else 0
        threshold = self.risk_thresholds['daily_loss_limit']
        
        alert_id = "DAILY_LOSS_ALERT"
        
        if daily_loss_pct > threshold:
            if alert_id not in self.active_alerts:
                alert = RiskAlert(
                    alert_id=alert_id,
                    timestamp=datetime.now(),
                    alert_type="PNL",
                    severity="CRITICAL",
                    message=f"当日亏损 {daily_loss_pct:.2%} 超过阈值 {threshold:.2%}",
                    current_value=daily_loss_pct,
                    threshold_value=threshold,
                    suggested_action="建议立即停止交易"
                )
                self.active_alerts[alert_id] = alert
                self.alert_history.append(alert)
                logger.critical(f"触发日亏损预警: {alert.message}")
        else:
            if alert_id in self.active_alerts:
                self.active_alerts[alert_id].is_resolved = True
                logger.info("日亏损预警已解除")
    
    def _cleanup_resolved_alerts(self):
        """清理已解决的预警"""
        resolved_alerts = [alert_id for alert_id, alert in self.active_alerts.items() if alert.is_resolved]
        for alert_id in resolved_alerts:
            del self.active_alerts[alert_id]
    
    def get_current_metrics(self) -> RiskMetrics:
        """获取当前风险指标"""
        with self.lock:
            return self.current_metrics
    
    def get_active_alerts(self) -> List[RiskAlert]:
        """获取活跃预警"""
        with self.lock:
            return list(self.active_alerts.values())
    
    def get_position_risks(self) -> Dict[str, PositionRisk]:
        """获取持仓风险"""
        with self.lock:
            return self.positions.copy()
    
    def get_risk_summary(self) -> Dict[str, Any]:
        """获取风险摘要"""
        with self.lock:
            return {
                'timestamp': self.current_metrics.timestamp.isoformat(),
                'risk_level': self.current_metrics.risk_level,
                'total_pnl': self.current_metrics.total_pnl,
                'current_drawdown': self.current_metrics.current_drawdown,
                'max_drawdown': self.current_metrics.max_drawdown,
                'var_1d': self.current_metrics.var_1d,
                'position_concentration': self.current_metrics.position_concentration,
                'leverage_ratio': self.current_metrics.leverage_ratio,
                'active_alerts_count': len(self.active_alerts),
                'active_alerts': [
                    {
                        'type': alert.alert_type,
                        'severity': alert.severity,
                        'message': alert.message
                    }
                    for alert in self.active_alerts.values()
                ]
            }
    
    def export_risk_report(self, filepath: str = None) -> str:
        """
        导出风险报告
        
        Args:
            filepath: 导出文件路径
            
        Returns:
            报告内容
        """
        try:
            report_data = {
                'report_time': datetime.now().isoformat(),
                'risk_metrics': {
                    'timestamp': self.current_metrics.timestamp.isoformat(),
                    'total_pnl': self.current_metrics.total_pnl,
                    'daily_pnl': self.current_metrics.daily_pnl,
                    'unrealized_pnl': self.current_metrics.unrealized_pnl,
                    'realized_pnl': self.current_metrics.realized_pnl,
                    'total_value': self.current_metrics.total_value,
                    'cash_balance': self.current_metrics.cash_balance,
                    'position_value': self.current_metrics.position_value,
                    'max_drawdown': self.current_metrics.max_drawdown,
                    'current_drawdown': self.current_metrics.current_drawdown,
                    'var_1d': self.current_metrics.var_1d,
                    'var_5d': self.current_metrics.var_5d,
                    'sharpe_ratio': self.current_metrics.sharpe_ratio,
                    'volatility': self.current_metrics.volatility,
                    'win_rate': self.current_metrics.win_rate,
                    'profit_loss_ratio': self.current_metrics.profit_loss_ratio,
                    'position_concentration': self.current_metrics.position_concentration,
                    'leverage_ratio': self.current_metrics.leverage_ratio,
                    'risk_level': self.current_metrics.risk_level
                },
                'positions': {
                    symbol: {
                        'quantity': pos.quantity,
                        'avg_cost': pos.avg_cost,
                        'current_price': pos.current_price,
                        'market_value': pos.market_value,
                        'unrealized_pnl': pos.unrealized_pnl,
                        'unrealized_pnl_pct': pos.unrealized_pnl_pct,
                        'weight': pos.weight,
                        'beta': pos.beta,
                        'volatility': pos.volatility
                    }
                    for symbol, pos in self.positions.items()
                },
                'active_alerts': [
                    {
                        'alert_id': alert.alert_id,
                        'timestamp': alert.timestamp.isoformat(),
                        'alert_type': alert.alert_type,
                        'severity': alert.severity,
                        'message': alert.message,
                        'current_value': alert.current_value,
                        'threshold_value': alert.threshold_value,
                        'suggested_action': alert.suggested_action
                    }
                    for alert in self.active_alerts.values()
                ],
                'risk_thresholds': self.risk_thresholds
            }
            
            report_json = json.dumps(report_data, indent=2, ensure_ascii=False)
            
            if filepath:
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(report_json)
                logger.info(f"风险报告已导出到: {filepath}")
            
            return report_json
            
        except Exception as e:
            logger.error(f"导出风险报告时出错: {e}")
            return "{}"
    
    def reset_metrics(self):
        """重置风险指标"""
        with self.lock:
            self.pnl_history.clear()
            self.risk_metrics_history.clear()
            self.active_alerts.clear()
            self.alert_history.clear()
            self.current_metrics = RiskMetrics(timestamp=datetime.now())
            logger.info("风险指标已重置")


# 风险监控工厂类
class RiskMonitorFactory:
    """风险监控工厂"""
    
    @staticmethod
    def create_monitor(monitor_type: str = "realtime", config: Dict = None) -> RealtimeRiskMonitor:
        """
        创建风险监控器
        
        Args:
            monitor_type: 监控器类型
            config: 配置参数
            
        Returns:
            风险监控器实例
        """
        if monitor_type == "realtime":
            return RealtimeRiskMonitor(config)
        else:
            raise ValueError(f"不支持的监控器类型: {monitor_type}")


if __name__ == "__main__":
    # 测试代码
    print("实时风险监控引擎测试")
    
    # 创建监控器
    config = {
        'max_drawdown': 0.20,
        'daily_loss_limit': 0.10,
        'var_limit': 0.08,
        'position_concentration': 0.40,
        'leverage_limit': 1.8,
        'update_interval': 2.0
    }
    
    monitor = RealtimeRiskMonitor(config)
    
    # 模拟数据更新
    print("开始监控...")
    monitor.start_monitoring()
    
    # 模拟持仓数据
    positions_data = {
        'AAPL': {
            'quantity': 100,
            'avg_cost': 150.0,
            'current_price': 155.0,
            'beta': 1.2,
            'volatility': 0.25
        },
        'TSLA': {
            'quantity': 50,
            'avg_cost': 800.0,
            'current_price': 750.0,
            'beta': 1.8,
            'volatility': 0.45
        }
    }
    
    monitor.update_positions(positions_data)
    
    # 更新账户信息
    account_data = {
        'total_capital': 100000.0,
        'available_cash': 20000.0,
        'total_value': 95000.0,
        'initial_capital': 100000.0
    }
    
    monitor.update_account_info(account_data)
    
    # 等待几秒钟让监控运行
    time.sleep(5)
    
    # 获取风险摘要
    summary = monitor.get_risk_summary()
    print(f"风险摘要: {json.dumps(summary, indent=2, ensure_ascii=False)}")
    
    # 获取当前指标
    metrics = monitor.get_current_metrics()
    print(f"当前风险等级: {metrics.risk_level}")
    print(f"当前回撤: {metrics.current_drawdown:.2%}")
    print(f"1日VaR: {metrics.var_1d:.2f}")
    
    # 停止监控
    monitor.stop_monitoring()
    print("监控已停止")