"""
风险仪表板和可视化界面
Risk Dashboard and Visualization Interface

提供实时风险监控的Web界面和可视化功能
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from pathlib import Path
import threading
import time

try:
    from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Request
    from fastapi.responses import HTMLResponse, JSONResponse
    from fastapi.staticfiles import StaticFiles
    from fastapi.templating import Jinja2Templates
    import uvicorn
    FASTAPI_AVAILABLE = True
except ImportError:
    FASTAPI_AVAILABLE = False
    print("警告: FastAPI未安装，Web仪表板功能不可用")

import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.font_manager import FontProperties
import numpy as np
import pandas as pd
from io import BytesIO
import base64

from .realtime_risk_monitor import RealtimeRiskMonitor, RiskMetrics, RiskAlert

# 配置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

logger = logging.getLogger(__name__)


class RiskDashboard:
    """风险仪表板"""
    
    def __init__(self, risk_monitor: RealtimeRiskMonitor, port: int = 8080):
        """
        初始化风险仪表板
        
        Args:
            risk_monitor: 风险监控器实例
            port: Web服务端口
        """
        self.risk_monitor = risk_monitor
        self.port = port
        self.app = None
        self.server_thread = None
        self.is_running = False
        
        # WebSocket连接管理
        self.websocket_connections = set()
        
        # 创建FastAPI应用
        if FASTAPI_AVAILABLE:
            self._create_app()
        
        logger.info(f"风险仪表板初始化完成，端口: {port}")
    
    def _create_app(self):
        """创建FastAPI应用"""
        self.app = FastAPI(title="风险监控仪表板", version="1.0.0")
        
        # 静态文件和模板
        templates_dir = Path(__file__).parent / "templates"
        static_dir = Path(__file__).parent / "static"
        
        # 创建目录（如果不存在）
        templates_dir.mkdir(exist_ok=True)
        static_dir.mkdir(exist_ok=True)
        
        # 配置模板和静态文件
        templates = Jinja2Templates(directory=str(templates_dir))
        if static_dir.exists():
            self.app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")
        
        # 路由定义
        @self.app.get("/", response_class=HTMLResponse)
        async def dashboard_home(request: Request):
            """仪表板主页"""
            return templates.TemplateResponse("dashboard.html", {"request": request})
        
        @self.app.get("/api/risk/summary")
        async def get_risk_summary():
            """获取风险摘要API"""
            try:
                summary = self.risk_monitor.get_risk_summary()
                return JSONResponse(content=summary)
            except Exception as e:
                logger.error(f"获取风险摘要失败: {e}")
                return JSONResponse(content={"error": str(e)}, status_code=500)
        
        @self.app.get("/api/risk/metrics")
        async def get_risk_metrics():
            """获取详细风险指标API"""
            try:
                metrics = self.risk_monitor.get_current_metrics()
                return JSONResponse(content={
                    "timestamp": metrics.timestamp.isoformat(),
                    "total_pnl": metrics.total_pnl,
                    "daily_pnl": metrics.daily_pnl,
                    "unrealized_pnl": metrics.unrealized_pnl,
                    "realized_pnl": metrics.realized_pnl,
                    "total_value": metrics.total_value,
                    "cash_balance": metrics.cash_balance,
                    "position_value": metrics.position_value,
                    "max_drawdown": metrics.max_drawdown,
                    "current_drawdown": metrics.current_drawdown,
                    "var_1d": metrics.var_1d,
                    "var_5d": metrics.var_5d,
                    "sharpe_ratio": metrics.sharpe_ratio,
                    "volatility": metrics.volatility,
                    "win_rate": metrics.win_rate,
                    "profit_loss_ratio": metrics.profit_loss_ratio,
                    "position_concentration": metrics.position_concentration,
                    "leverage_ratio": metrics.leverage_ratio,
                    "risk_level": metrics.risk_level
                })
            except Exception as e:
                logger.error(f"获取风险指标失败: {e}")
                return JSONResponse(content={"error": str(e)}, status_code=500)
        
        @self.app.get("/api/risk/alerts")
        async def get_risk_alerts():
            """获取风险预警API"""
            try:
                alerts = self.risk_monitor.get_active_alerts()
                return JSONResponse(content=[
                    {
                        "alert_id": alert.alert_id,
                        "timestamp": alert.timestamp.isoformat(),
                        "alert_type": alert.alert_type,
                        "severity": alert.severity,
                        "message": alert.message,
                        "current_value": alert.current_value,
                        "threshold_value": alert.threshold_value,
                        "suggested_action": alert.suggested_action
                    }
                    for alert in alerts
                ])
            except Exception as e:
                logger.error(f"获取风险预警失败: {e}")
                return JSONResponse(content={"error": str(e)}, status_code=500)
        
        @self.app.get("/api/risk/positions")
        async def get_position_risks():
            """获取持仓风险API"""
            try:
                positions = self.risk_monitor.get_position_risks()
                return JSONResponse(content={
                    symbol: {
                        "quantity": pos.quantity,
                        "avg_cost": pos.avg_cost,
                        "current_price": pos.current_price,
                        "market_value": pos.market_value,
                        "unrealized_pnl": pos.unrealized_pnl,
                        "unrealized_pnl_pct": pos.unrealized_pnl_pct,
                        "weight": pos.weight,
                        "beta": pos.beta,
                        "volatility": pos.volatility,
                        "var_contribution": pos.var_contribution
                    }
                    for symbol, pos in positions.items()
                })
            except Exception as e:
                logger.error(f"获取持仓风险失败: {e}")
                return JSONResponse(content={"error": str(e)}, status_code=500)
        
        @self.app.get("/api/risk/charts/pnl")
        async def get_pnl_chart():
            """获取PnL图表"""
            try:
                chart_data = self.generate_pnl_chart()
                return JSONResponse(content={"chart": chart_data})
            except Exception as e:
                logger.error(f"生成PnL图表失败: {e}")
                return JSONResponse(content={"error": str(e)}, status_code=500)
        
        @self.app.get("/api/risk/charts/drawdown")
        async def get_drawdown_chart():
            """获取回撤图表"""
            try:
                chart_data = self.generate_drawdown_chart()
                return JSONResponse(content={"chart": chart_data})
            except Exception as e:
                logger.error(f"生成回撤图表失败: {e}")
                return JSONResponse(content={"error": str(e)}, status_code=500)
        
        @self.app.websocket("/ws/risk")
        async def websocket_endpoint(websocket: WebSocket):
            """WebSocket实时数据推送"""
            await websocket.accept()
            self.websocket_connections.add(websocket)
            
            try:
                while True:
                    # 发送实时风险数据
                    summary = self.risk_monitor.get_risk_summary()
                    await websocket.send_json(summary)
                    await asyncio.sleep(2)  # 每2秒推送一次
                    
            except WebSocketDisconnect:
                self.websocket_connections.discard(websocket)
            except Exception as e:
                logger.error(f"WebSocket连接错误: {e}")
                self.websocket_connections.discard(websocket)
    
    def start_server(self):
        """启动Web服务器"""
        if not FASTAPI_AVAILABLE:
            logger.error("FastAPI未安装，无法启动Web服务器")
            return
        
        if self.is_running:
            logger.warning("Web服务器已在运行中")
            return
        
        self.is_running = True
        
        def run_server():
            try:
                uvicorn.run(
                    self.app,
                    host="0.0.0.0",
                    port=self.port,
                    log_level="info"
                )
            except Exception as e:
                logger.error(f"启动Web服务器失败: {e}")
                self.is_running = False
        
        self.server_thread = threading.Thread(target=run_server, daemon=True)
        self.server_thread.start()
        
        logger.info(f"风险仪表板已启动: http://localhost:{self.port}")
    
    def stop_server(self):
        """停止Web服务器"""
        self.is_running = False
        logger.info("风险仪表板已停止")
    
    def generate_pnl_chart(self) -> str:
        """
        生成PnL图表
        
        Returns:
            Base64编码的图表图片
        """
        try:
            # 获取PnL历史数据
            pnl_history = list(self.risk_monitor.pnl_history)
            
            if len(pnl_history) < 2:
                return ""
            
            # 创建时间序列
            now = datetime.now()
            timestamps = [now - timedelta(seconds=i) for i in range(len(pnl_history)-1, -1, -1)]
            
            # 创建图表
            fig, ax = plt.subplots(figsize=(12, 6))
            ax.plot(timestamps, pnl_history, linewidth=2, color='blue', label='总PnL')
            ax.axhline(y=0, color='red', linestyle='--', alpha=0.7, label='盈亏平衡线')
            
            # 设置标题和标签
            ax.set_title('实时PnL走势图', fontsize=16, fontweight='bold')
            ax.set_xlabel('时间', fontsize=12)
            ax.set_ylabel('PnL (元)', fontsize=12)
            
            # 格式化x轴
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M:%S'))
            ax.xaxis.set_major_locator(mdates.MinuteLocator(interval=5))
            plt.xticks(rotation=45)
            
            # 添加网格和图例
            ax.grid(True, alpha=0.3)
            ax.legend()
            
            # 调整布局
            plt.tight_layout()
            
            # 转换为Base64
            buffer = BytesIO()
            plt.savefig(buffer, format='png', dpi=100, bbox_inches='tight')
            buffer.seek(0)
            chart_data = base64.b64encode(buffer.getvalue()).decode()
            plt.close(fig)
            
            return chart_data
            
        except Exception as e:
            logger.error(f"生成PnL图表失败: {e}")
            return ""
    
    def generate_drawdown_chart(self) -> str:
        """
        生成回撤图表
        
        Returns:
            Base64编码的图表图片
        """
        try:
            # 获取风险指标历史数据
            metrics_history = list(self.risk_monitor.risk_metrics_history)
            
            if len(metrics_history) < 2:
                return ""
            
            # 提取回撤数据
            timestamps = [m.timestamp for m in metrics_history]
            drawdowns = [m.current_drawdown * 100 for m in metrics_history]  # 转换为百分比
            max_drawdowns = [m.max_drawdown * 100 for m in metrics_history]
            
            # 创建图表
            fig, ax = plt.subplots(figsize=(12, 6))
            ax.fill_between(timestamps, drawdowns, 0, alpha=0.3, color='red', label='当前回撤')
            ax.plot(timestamps, max_drawdowns, linewidth=2, color='darkred', label='最大回撤')
            
            # 添加阈值线
            threshold = self.risk_monitor.risk_thresholds.get('max_drawdown', 0.3) * 100
            ax.axhline(y=threshold, color='orange', linestyle='--', alpha=0.7, 
                      label=f'回撤阈值 ({threshold:.1f}%)')
            
            # 设置标题和标签
            ax.set_title('回撤监控图', fontsize=16, fontweight='bold')
            ax.set_xlabel('时间', fontsize=12)
            ax.set_ylabel('回撤 (%)', fontsize=12)
            
            # 格式化y轴为百分比
            ax.set_ylim(0, max(max(drawdowns) * 1.1, threshold * 1.2))
            
            # 格式化x轴
            if len(timestamps) > 1:
                ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
                ax.xaxis.set_major_locator(mdates.MinuteLocator(interval=10))
                plt.xticks(rotation=45)
            
            # 添加网格和图例
            ax.grid(True, alpha=0.3)
            ax.legend()
            
            # 调整布局
            plt.tight_layout()
            
            # 转换为Base64
            buffer = BytesIO()
            plt.savefig(buffer, format='png', dpi=100, bbox_inches='tight')
            buffer.seek(0)
            chart_data = base64.b64encode(buffer.getvalue()).decode()
            plt.close(fig)
            
            return chart_data
            
        except Exception as e:
            logger.error(f"生成回撤图表失败: {e}")
            return ""
    
    def generate_position_pie_chart(self) -> str:
        """
        生成持仓分布饼图
        
        Returns:
            Base64编码的图表图片
        """
        try:
            positions = self.risk_monitor.get_position_risks()
            
            if not positions:
                return ""
            
            # 准备数据
            symbols = list(positions.keys())
            weights = [pos.weight * 100 for pos in positions.values()]  # 转换为百分比
            
            # 创建饼图
            fig, ax = plt.subplots(figsize=(10, 8))
            colors = plt.cm.Set3(np.linspace(0, 1, len(symbols)))
            
            wedges, texts, autotexts = ax.pie(
                weights, 
                labels=symbols, 
                autopct='%1.1f%%',
                colors=colors,
                startangle=90
            )
            
            # 设置标题
            ax.set_title('持仓分布图', fontsize=16, fontweight='bold')
            
            # 调整文本样式
            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_fontweight('bold')
            
            # 转换为Base64
            buffer = BytesIO()
            plt.savefig(buffer, format='png', dpi=100, bbox_inches='tight')
            buffer.seek(0)
            chart_data = base64.b64encode(buffer.getvalue()).decode()
            plt.close(fig)
            
            return chart_data
            
        except Exception as e:
            logger.error(f"生成持仓分布图失败: {e}")
            return ""
    
    def create_dashboard_template(self):
        """创建仪表板HTML模板"""
        template_path = Path(__file__).parent / "templates" / "dashboard.html"
        template_path.parent.mkdir(exist_ok=True)
        
        html_content = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>风险监控仪表板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card-title {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .metric-label {
            color: #666;
            font-size: 0.9em;
        }
        
        .risk-level {
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
            text-align: center;
            margin: 10px 0;
        }
        
        .risk-low { background-color: #d4edda; color: #155724; }
        .risk-medium { background-color: #fff3cd; color: #856404; }
        .risk-high { background-color: #f8d7da; color: #721c24; }
        .risk-critical { background-color: #f5c6cb; color: #721c24; animation: blink 1s infinite; }
        
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.5; }
        }
        
        .alert-item {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 10px;
            margin: 5px 0;
        }
        
        .alert-critical {
            background: #f8d7da;
            border-color: #f5c6cb;
        }
        
        .chart-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin: 20px 0;
        }
        
        .chart-image {
            width: 100%;
            height: auto;
            border-radius: 5px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-online { background-color: #28a745; }
        .status-offline { background-color: #dc3545; }
        
        .refresh-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: background 0.3s ease;
        }
        
        .refresh-btn:hover {
            background: #5a6fd8;
        }
        
        .footer {
            text-align: center;
            padding: 20px;
            color: #666;
            border-top: 1px solid #ddd;
            margin-top: 40px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🛡️ 风险监控仪表板</h1>
        <div class="subtitle">
            <span class="status-indicator status-online"></span>
            实时监控系统运行中
        </div>
    </div>
    
    <div class="container">
        <div class="dashboard-grid">
            <div class="card">
                <div class="card-title">📊 总体概况</div>
                <div class="metric-value" id="total-pnl">--</div>
                <div class="metric-label">总盈亏 (元)</div>
                <div class="metric-value" id="total-value">--</div>
                <div class="metric-label">总资产 (元)</div>
            </div>
            
            <div class="card">
                <div class="card-title">📉 风险指标</div>
                <div class="metric-value" id="current-drawdown">--</div>
                <div class="metric-label">当前回撤</div>
                <div class="metric-value" id="var-1d">--</div>
                <div class="metric-label">1日VaR (元)</div>
            </div>
            
            <div class="card">
                <div class="card-title">⚠️ 风险等级</div>
                <div class="risk-level" id="risk-level">--</div>
                <div class="metric-value" id="position-concentration">--</div>
                <div class="metric-label">持仓集中度</div>
            </div>
            
            <div class="card">
                <div class="card-title">🚨 活跃预警</div>
                <div id="active-alerts">
                    <div class="metric-label">暂无预警</div>
                </div>
            </div>
        </div>
        
        <div class="chart-container">
            <div class="card-title">📈 PnL走势图</div>
            <img id="pnl-chart" class="chart-image" src="" alt="PnL图表加载中...">
        </div>
        
        <div class="chart-container">
            <div class="card-title">📉 回撤监控图</div>
            <img id="drawdown-chart" class="chart-image" src="" alt="回撤图表加载中...">
        </div>
        
        <div style="text-align: center; margin: 20px 0;">
            <button class="refresh-btn" onclick="refreshData()">🔄 刷新数据</button>
        </div>
    </div>
    
    <div class="footer">
        <p>© 2024 Qlib交易系统 - 实时风险监控仪表板</p>
        <p>最后更新: <span id="last-update">--</span></p>
    </div>
    
    <script>
        // WebSocket连接
        let ws = null;
        
        function connectWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws/risk`;
            
            ws = new WebSocket(wsUrl);
            
            ws.onopen = function(event) {
                console.log('WebSocket连接已建立');
                document.querySelector('.status-indicator').className = 'status-indicator status-online';
            };
            
            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                updateDashboard(data);
            };
            
            ws.onclose = function(event) {
                console.log('WebSocket连接已关闭');
                document.querySelector('.status-indicator').className = 'status-indicator status-offline';
                // 5秒后重连
                setTimeout(connectWebSocket, 5000);
            };
            
            ws.onerror = function(error) {
                console.error('WebSocket错误:', error);
            };
        }
        
        function updateDashboard(data) {
            // 更新基本指标
            document.getElementById('total-pnl').textContent = 
                data.total_pnl ? data.total_pnl.toFixed(2) : '--';
            document.getElementById('total-value').textContent = 
                data.total_value ? data.total_value.toFixed(2) : '--';
            document.getElementById('current-drawdown').textContent = 
                data.current_drawdown ? (data.current_drawdown * 100).toFixed(2) + '%' : '--';
            document.getElementById('var-1d').textContent = 
                data.var_1d ? data.var_1d.toFixed(2) : '--';
            document.getElementById('position-concentration').textContent = 
                data.position_concentration ? (data.position_concentration * 100).toFixed(1) + '%' : '--';
            
            // 更新风险等级
            const riskLevelElement = document.getElementById('risk-level');
            const riskLevel = data.risk_level || 'UNKNOWN';
            riskLevelElement.textContent = getRiskLevelText(riskLevel);
            riskLevelElement.className = `risk-level risk-${riskLevel.toLowerCase()}`;
            
            // 更新预警信息
            const alertsContainer = document.getElementById('active-alerts');
            if (data.active_alerts && data.active_alerts.length > 0) {
                alertsContainer.innerHTML = data.active_alerts.map(alert => 
                    `<div class="alert-item ${alert.severity === 'CRITICAL' ? 'alert-critical' : ''}">
                        <strong>${alert.type}:</strong> ${alert.message}
                    </div>`
                ).join('');
            } else {
                alertsContainer.innerHTML = '<div class="metric-label">暂无预警</div>';
            }
            
            // 更新时间戳
            document.getElementById('last-update').textContent = new Date().toLocaleString();
        }
        
        function getRiskLevelText(level) {
            const levelMap = {
                'LOW': '低风险',
                'MEDIUM': '中等风险',
                'HIGH': '高风险',
                'CRITICAL': '极高风险'
            };
            return levelMap[level] || level;
        }
        
        async function refreshData() {
            try {
                // 刷新图表
                const pnlResponse = await fetch('/api/risk/charts/pnl');
                const pnlData = await pnlResponse.json();
                if (pnlData.chart) {
                    document.getElementById('pnl-chart').src = `data:image/png;base64,${pnlData.chart}`;
                }
                
                const drawdownResponse = await fetch('/api/risk/charts/drawdown');
                const drawdownData = await drawdownResponse.json();
                if (drawdownData.chart) {
                    document.getElementById('drawdown-chart').src = `data:image/png;base64,${drawdownData.chart}`;
                }
                
                console.log('数据已刷新');
            } catch (error) {
                console.error('刷新数据失败:', error);
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            connectWebSocket();
            refreshData();
            
            // 每30秒自动刷新图表
            setInterval(refreshData, 30000);
        });
    </script>
</body>
</html>
        """
        
        with open(template_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logger.info(f"仪表板模板已创建: {template_path}")


if __name__ == "__main__":
    # 测试代码
    from .realtime_risk_monitor import RealtimeRiskMonitor
    
    print("风险仪表板测试")
    
    # 创建风险监控器
    monitor = RealtimeRiskMonitor()
    monitor.start_monitoring()
    
    # 创建仪表板
    dashboard = RiskDashboard(monitor, port=8080)
    dashboard.create_dashboard_template()
    
    # 启动Web服务器
    dashboard.start_server()
    
    print("仪表板已启动，访问 http://localhost:8080")
    print("按 Ctrl+C 退出")
    
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n正在关闭...")
        dashboard.stop_server()
        monitor.stop_monitoring()