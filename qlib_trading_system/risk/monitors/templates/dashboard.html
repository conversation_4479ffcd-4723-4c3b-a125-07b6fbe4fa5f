
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>风险监控仪表板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card-title {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .metric-label {
            color: #666;
            font-size: 0.9em;
        }
        
        .risk-level {
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
            text-align: center;
            margin: 10px 0;
        }
        
        .risk-low { background-color: #d4edda; color: #155724; }
        .risk-medium { background-color: #fff3cd; color: #856404; }
        .risk-high { background-color: #f8d7da; color: #721c24; }
        .risk-critical { background-color: #f5c6cb; color: #721c24; animation: blink 1s infinite; }
        
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.5; }
        }
        
        .alert-item {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 10px;
            margin: 5px 0;
        }
        
        .alert-critical {
            background: #f8d7da;
            border-color: #f5c6cb;
        }
        
        .chart-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin: 20px 0;
        }
        
        .chart-image {
            width: 100%;
            height: auto;
            border-radius: 5px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-online { background-color: #28a745; }
        .status-offline { background-color: #dc3545; }
        
        .refresh-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: background 0.3s ease;
        }
        
        .refresh-btn:hover {
            background: #5a6fd8;
        }
        
        .footer {
            text-align: center;
            padding: 20px;
            color: #666;
            border-top: 1px solid #ddd;
            margin-top: 40px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🛡️ 风险监控仪表板</h1>
        <div class="subtitle">
            <span class="status-indicator status-online"></span>
            实时监控系统运行中
        </div>
    </div>
    
    <div class="container">
        <div class="dashboard-grid">
            <div class="card">
                <div class="card-title">📊 总体概况</div>
                <div class="metric-value" id="total-pnl">--</div>
                <div class="metric-label">总盈亏 (元)</div>
                <div class="metric-value" id="total-value">--</div>
                <div class="metric-label">总资产 (元)</div>
            </div>
            
            <div class="card">
                <div class="card-title">📉 风险指标</div>
                <div class="metric-value" id="current-drawdown">--</div>
                <div class="metric-label">当前回撤</div>
                <div class="metric-value" id="var-1d">--</div>
                <div class="metric-label">1日VaR (元)</div>
            </div>
            
            <div class="card">
                <div class="card-title">⚠️ 风险等级</div>
                <div class="risk-level" id="risk-level">--</div>
                <div class="metric-value" id="position-concentration">--</div>
                <div class="metric-label">持仓集中度</div>
            </div>
            
            <div class="card">
                <div class="card-title">🚨 活跃预警</div>
                <div id="active-alerts">
                    <div class="metric-label">暂无预警</div>
                </div>
            </div>
        </div>
        
        <div class="chart-container">
            <div class="card-title">📈 PnL走势图</div>
            <img id="pnl-chart" class="chart-image" src="" alt="PnL图表加载中...">
        </div>
        
        <div class="chart-container">
            <div class="card-title">📉 回撤监控图</div>
            <img id="drawdown-chart" class="chart-image" src="" alt="回撤图表加载中...">
        </div>
        
        <div style="text-align: center; margin: 20px 0;">
            <button class="refresh-btn" onclick="refreshData()">🔄 刷新数据</button>
        </div>
    </div>
    
    <div class="footer">
        <p>© 2024 Qlib交易系统 - 实时风险监控仪表板</p>
        <p>最后更新: <span id="last-update">--</span></p>
    </div>
    
    <script>
        // WebSocket连接
        let ws = null;
        
        function connectWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws/risk`;
            
            ws = new WebSocket(wsUrl);
            
            ws.onopen = function(event) {
                console.log('WebSocket连接已建立');
                document.querySelector('.status-indicator').className = 'status-indicator status-online';
            };
            
            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                updateDashboard(data);
            };
            
            ws.onclose = function(event) {
                console.log('WebSocket连接已关闭');
                document.querySelector('.status-indicator').className = 'status-indicator status-offline';
                // 5秒后重连
                setTimeout(connectWebSocket, 5000);
            };
            
            ws.onerror = function(error) {
                console.error('WebSocket错误:', error);
            };
        }
        
        function updateDashboard(data) {
            // 更新基本指标
            document.getElementById('total-pnl').textContent = 
                data.total_pnl ? data.total_pnl.toFixed(2) : '--';
            document.getElementById('total-value').textContent = 
                data.total_value ? data.total_value.toFixed(2) : '--';
            document.getElementById('current-drawdown').textContent = 
                data.current_drawdown ? (data.current_drawdown * 100).toFixed(2) + '%' : '--';
            document.getElementById('var-1d').textContent = 
                data.var_1d ? data.var_1d.toFixed(2) : '--';
            document.getElementById('position-concentration').textContent = 
                data.position_concentration ? (data.position_concentration * 100).toFixed(1) + '%' : '--';
            
            // 更新风险等级
            const riskLevelElement = document.getElementById('risk-level');
            const riskLevel = data.risk_level || 'UNKNOWN';
            riskLevelElement.textContent = getRiskLevelText(riskLevel);
            riskLevelElement.className = `risk-level risk-${riskLevel.toLowerCase()}`;
            
            // 更新预警信息
            const alertsContainer = document.getElementById('active-alerts');
            if (data.active_alerts && data.active_alerts.length > 0) {
                alertsContainer.innerHTML = data.active_alerts.map(alert => 
                    `<div class="alert-item ${alert.severity === 'CRITICAL' ? 'alert-critical' : ''}">
                        <strong>${alert.type}:</strong> ${alert.message}
                    </div>`
                ).join('');
            } else {
                alertsContainer.innerHTML = '<div class="metric-label">暂无预警</div>';
            }
            
            // 更新时间戳
            document.getElementById('last-update').textContent = new Date().toLocaleString();
        }
        
        function getRiskLevelText(level) {
            const levelMap = {
                'LOW': '低风险',
                'MEDIUM': '中等风险',
                'HIGH': '高风险',
                'CRITICAL': '极高风险'
            };
            return levelMap[level] || level;
        }
        
        async function refreshData() {
            try {
                // 刷新图表
                const pnlResponse = await fetch('/api/risk/charts/pnl');
                const pnlData = await pnlResponse.json();
                if (pnlData.chart) {
                    document.getElementById('pnl-chart').src = `data:image/png;base64,${pnlData.chart}`;
                }
                
                const drawdownResponse = await fetch('/api/risk/charts/drawdown');
                const drawdownData = await drawdownResponse.json();
                if (drawdownData.chart) {
                    document.getElementById('drawdown-chart').src = `data:image/png;base64,${drawdownData.chart}`;
                }
                
                console.log('数据已刷新');
            } catch (error) {
                console.error('刷新数据失败:', error);
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            connectWebSocket();
            refreshData();
            
            // 每30秒自动刷新图表
            setInterval(refreshData, 30000);
        });
    </script>
</body>
</html>
        