# 极端行情熔断机制

## 概述

极端行情熔断机制是qlib交易系统的核心风险管理组件，专门用于检测和应对市场异常情况。

## 主要功能

### 1. 多级市场异常检测
- **价格异常检测**: 监控个股和大盘的异常价格波动
- **成交量异常检测**: 识别成交量的异常放大或萎缩
- **波动率异常检测**: 检测市场波动率的异常激增
- **黑天鹅事件检测**: 基于新闻和统计分析的异常事件识别

### 2. 自动熔断和防御策略
- **多级警报系统**: NORMAL → WARNING → DANGER → CRITICAL → EMERGENCY
- **自动防御策略**: 根据异常严重程度自动激活相应防御措施
- **熔断冷却机制**: 防止频繁触发熔断导致系统不稳定

### 3. 异常交易行为检测
- **交易频率异常**: 检测过度频繁的交易行为
- **交易规模异常**: 识别异常大小的交易订单
- **风险违规检测**: 监控仓位、杠杆等风险指标违规

### 4. 紧急止损和强制平仓
- **紧急退出机制**: 在极端情况下自动清算持仓
- **强制平仓功能**: 当风险超过阈值时强制平仓
- **连续亏损控制**: 检测连续亏损并调整交易策略

## 使用方法

```python
from qlib_trading_system.risk.circuit_breaker_manager import initialize_circuit_breaker

# 初始化熔断机制
manager = initialize_circuit_breaker()

# 更新市场数据
market_data = {
    "market": {"index_change": -0.05},
    "stocks": {"000001": {"current_price": 9.0, "reference_price": 10.0}}
}
manager.update_market_data(market_data)
```

## 配置说明

熔断机制支持灵活的配置，主要参数包括：
- 价格异常阈值
- 成交量异常阈值  
- 风险控制阈值
- 时间窗口设置
- 防御策略配置

详细配置请参考 `config/circuit_breaker_config.py`

## 测试验证

运行集成测试验证功能：
```bash
python test_circuit_breaker_simple.py
```