"""
异常交易行为检测模块
检测和识别异常的交易行为模式
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import statistics

from ..utils.logging.logger import get_logger


class TradingAnomalyType(Enum):
    """交易异常类型"""
    EXCESSIVE_FREQUENCY = "EXCESSIVE_FREQUENCY"     # 交易频率过高
    UNUSUAL_SIZE = "UNUSUAL_SIZE"                   # 异常交易规模
    RAPID_POSITION_CHANGE = "RAPID_POSITION_CHANGE" # 快速仓位变化
    ABNORMAL_TIMING = "ABNORMAL_TIMING"             # 异常交易时机
    PATTERN_DEVIATION = "PATTERN_DEVIATION"         # 交易模式偏离
    RISK_VIOLATION = "RISK_VIOLATION"               # 风险规则违反
    SYSTEM_MALFUNCTION = "SYSTEM_MALFUNCTION"       # 系统故障迹象


@dataclass
class TradingAnomalyEvent:
    """交易异常事件"""
    anomaly_type: TradingAnomalyType
    severity: str  # LOW, MEDIUM, HIGH, CRITICAL
    timestamp: datetime
    symbol: Optional[str] = None
    description: str = ""
    metrics: Dict[str, float] = None
    suggested_actions: List[str] = None
    
    def __post_init__(self):
        if self.metrics is None:
            self.metrics = {}
        if self.suggested_actions is None:
            self.suggested_actions = []


class TradingPatternAnalyzer:
    """交易模式分析器"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.historical_patterns = {}
        self.baseline_metrics = {}
    
    def analyze_trading_frequency(self, trades: List[Dict[str, Any]], 
                                time_window: int = 3600) -> Optional[TradingAnomalyEvent]:
        """分析交易频率异常"""
        if not trades:
            return None
        
        current_time = datetime.now()
        recent_trades = [
            trade for trade in trades 
            if (current_time - trade['timestamp']).seconds <= time_window
        ]
        
        trade_count = len(recent_trades)
        
        # 计算基准频率（过去30天平均）
        baseline_frequency = self.baseline_metrics.get('avg_hourly_trades', 10)
        
        # 检测异常高频交易
        if trade_count > baseline_frequency * 3:  # 超过基准3倍
            return TradingAnomalyEvent(
                anomaly_type=TradingAnomalyType.EXCESSIVE_FREQUENCY,
                severity="HIGH",
                timestamp=current_time,
                description=f"交易频率异常：{time_window//60}分钟内执行{trade_count}笔交易",
                metrics={
                    "trade_count": trade_count,
                    "baseline_frequency": baseline_frequency,
                    "frequency_ratio": trade_count / baseline_frequency
                },
                suggested_actions=["PAUSE_TRADING", "CHECK_ALGORITHM", "REVIEW_PARAMETERS"]
            )
        
        return None
    
    def analyze_position_size(self, trades: List[Dict[str, Any]]) -> Optional[TradingAnomalyEvent]:
        """分析交易规模异常"""
        if not trades:
            return None
        
        recent_trades = trades[-20:]  # 最近20笔交易
        trade_sizes = [abs(trade.get('quantity', 0)) for trade in recent_trades]
        
        if not trade_sizes:
            return None
        
        # 计算统计指标
        mean_size = statistics.mean(trade_sizes)
        std_size = statistics.stdev(trade_sizes) if len(trade_sizes) > 1 else 0
        
        # 检查最新交易是否异常
        latest_size = trade_sizes[-1]
        
        if std_size > 0:
            z_score = abs((latest_size - mean_size) / std_size)
            
            # 3-sigma规则检测异常
            if z_score > 3:
                return TradingAnomalyEvent(
                    anomaly_type=TradingAnomalyType.UNUSUAL_SIZE,
                    severity="MEDIUM" if z_score < 4 else "HIGH",
                    timestamp=datetime.now(),
                    description=f"交易规模异常：当前交易{latest_size}股，Z-score={z_score:.2f}",
                    metrics={
                        "trade_size": latest_size,
                        "mean_size": mean_size,
                        "z_score": z_score
                    },
                    suggested_actions=["VERIFY_ORDER", "CHECK_POSITION_LIMITS"]
                )
        
        return None
    
    def analyze_rapid_position_changes(self, position_history: List[Dict[str, Any]]) -> Optional[TradingAnomalyEvent]:
        """分析快速仓位变化"""
        if len(position_history) < 2:
            return None
        
        # 计算仓位变化率
        position_changes = []
        for i in range(1, len(position_history)):
            prev_pos = position_history[i-1].get('total_position', 0)
            curr_pos = position_history[i].get('total_position', 0)
            
            if prev_pos != 0:
                change_rate = abs((curr_pos - prev_pos) / prev_pos)
                position_changes.append(change_rate)
        
        if not position_changes:
            return None
        
        # 检测异常快速变化
        max_change_rate = max(position_changes)
        
        if max_change_rate > 0.5:  # 50%以上的仓位变化
            return TradingAnomalyEvent(
                anomaly_type=TradingAnomalyType.RAPID_POSITION_CHANGE,
                severity="HIGH" if max_change_rate > 0.8 else "MEDIUM",
                timestamp=datetime.now(),
                description=f"仓位快速变化：变化幅度{max_change_rate:.1%}",
                metrics={
                    "max_change_rate": max_change_rate,
                    "avg_change_rate": statistics.mean(position_changes)
                },
                suggested_actions=["REVIEW_STRATEGY", "CHECK_RISK_CONTROLS"]
            )
        
        return None
    
    def analyze_trading_timing(self, trades: List[Dict[str, Any]]) -> Optional[TradingAnomalyEvent]:
        """分析交易时机异常"""
        if not trades:
            return None
        
        # 分析交易时间分布
        trading_hours = []
        for trade in trades[-50:]:  # 最近50笔交易
            trade_time = trade.get('timestamp', datetime.now())
            if isinstance(trade_time, str):
                trade_time = datetime.fromisoformat(trade_time)
            trading_hours.append(trade_time.hour)
        
        if not trading_hours:
            return None
        
        # 检测非交易时间的交易
        abnormal_hours = [hour for hour in trading_hours if hour < 9 or hour > 15]
        
        if len(abnormal_hours) > len(trading_hours) * 0.1:  # 超过10%的异常时间交易
            return TradingAnomalyEvent(
                anomaly_type=TradingAnomalyType.ABNORMAL_TIMING,
                severity="MEDIUM",
                timestamp=datetime.now(),
                description=f"异常交易时机：{len(abnormal_hours)}笔交易发生在非正常时间",
                metrics={
                    "abnormal_trades": len(abnormal_hours),
                    "total_trades": len(trading_hours),
                    "abnormal_ratio": len(abnormal_hours) / len(trading_hours)
                },
                suggested_actions=["CHECK_SYSTEM_CLOCK", "VERIFY_TRADING_SCHEDULE"]
            )
        
        return None


class RiskViolationDetector:
    """风险违规检测器"""
    
    def __init__(self, risk_limits: Dict[str, float]):
        self.logger = get_logger(__name__)
        self.risk_limits = risk_limits
    
    def check_position_limits(self, positions: Dict[str, Any]) -> List[TradingAnomalyEvent]:
        """检查仓位限制违规"""
        violations = []
        
        total_position_value = sum(
            pos.get('market_value', 0) for pos in positions.values()
        )
        
        account_value = self.risk_limits.get('account_value', 1000000)
        max_position_ratio = self.risk_limits.get('max_position_ratio', 0.3)
        
        for symbol, position in positions.items():
            position_value = position.get('market_value', 0)
            position_ratio = position_value / account_value if account_value > 0 else 0
            
            if position_ratio > max_position_ratio:
                violations.append(TradingAnomalyEvent(
                    anomaly_type=TradingAnomalyType.RISK_VIOLATION,
                    severity="HIGH",
                    timestamp=datetime.now(),
                    symbol=symbol,
                    description=f"仓位超限：{symbol}占比{position_ratio:.1%}，限制{max_position_ratio:.1%}",
                    metrics={
                        "position_ratio": position_ratio,
                        "limit_ratio": max_position_ratio,
                        "position_value": position_value
                    },
                    suggested_actions=["REDUCE_POSITION", "REBALANCE_PORTFOLIO"]
                ))
        
        return violations
    
    def check_drawdown_limits(self, account_data: Dict[str, Any]) -> Optional[TradingAnomalyEvent]:
        """检查回撤限制违规"""
        current_drawdown = account_data.get('max_drawdown', 0)
        max_allowed_drawdown = self.risk_limits.get('max_drawdown', 0.2)
        
        if current_drawdown > max_allowed_drawdown:
            return TradingAnomalyEvent(
                anomaly_type=TradingAnomalyType.RISK_VIOLATION,
                severity="CRITICAL",
                timestamp=datetime.now(),
                description=f"回撤超限：当前{current_drawdown:.1%}，限制{max_allowed_drawdown:.1%}",
                metrics={
                    "current_drawdown": current_drawdown,
                    "max_allowed_drawdown": max_allowed_drawdown
                },
                suggested_actions=["STOP_TRADING", "REDUCE_RISK", "REVIEW_STRATEGY"]
            )
        
        return None
    
    def check_leverage_limits(self, account_data: Dict[str, Any]) -> Optional[TradingAnomalyEvent]:
        """检查杠杆限制违规"""
        current_leverage = account_data.get('leverage_ratio', 1.0)
        max_leverage = self.risk_limits.get('max_leverage', 2.0)
        
        if current_leverage > max_leverage:
            return TradingAnomalyEvent(
                anomaly_type=TradingAnomalyType.RISK_VIOLATION,
                severity="HIGH",
                timestamp=datetime.now(),
                description=f"杠杆超限：当前{current_leverage:.1f}倍，限制{max_leverage:.1f}倍",
                metrics={
                    "current_leverage": current_leverage,
                    "max_leverage": max_leverage
                },
                suggested_actions=["REDUCE_LEVERAGE", "CLOSE_POSITIONS"]
            )
        
        return None


class SystemMalfunctionDetector:
    """系统故障检测器"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.error_counts = {}
        self.response_times = []
    
    def detect_error_patterns(self, error_logs: List[Dict[str, Any]]) -> Optional[TradingAnomalyEvent]:
        """检测错误模式"""
        if not error_logs:
            return None
        
        # 统计最近1小时的错误
        current_time = datetime.now()
        recent_errors = [
            error for error in error_logs
            if (current_time - error.get('timestamp', current_time)).seconds <= 3600
        ]
        
        error_count = len(recent_errors)
        
        if error_count > 10:  # 1小时内超过10个错误
            return TradingAnomalyEvent(
                anomaly_type=TradingAnomalyType.SYSTEM_MALFUNCTION,
                severity="HIGH",
                timestamp=current_time,
                description=f"系统错误频发：1小时内{error_count}个错误",
                metrics={"error_count": error_count},
                suggested_actions=["CHECK_SYSTEM", "PAUSE_TRADING", "INVESTIGATE_ERRORS"]
            )
        
        return None
    
    def detect_performance_degradation(self, response_times: List[float]) -> Optional[TradingAnomalyEvent]:
        """检测性能下降"""
        if len(response_times) < 10:
            return None
        
        recent_times = response_times[-20:]  # 最近20次响应时间
        avg_response_time = statistics.mean(recent_times)
        
        # 基准响应时间（毫秒）
        baseline_response_time = 100
        
        if avg_response_time > baseline_response_time * 3:  # 响应时间超过基准3倍
            return TradingAnomalyEvent(
                anomaly_type=TradingAnomalyType.SYSTEM_MALFUNCTION,
                severity="MEDIUM",
                timestamp=datetime.now(),
                description=f"系统响应缓慢：平均响应时间{avg_response_time:.1f}ms",
                metrics={
                    "avg_response_time": avg_response_time,
                    "baseline_response_time": baseline_response_time
                },
                suggested_actions=["CHECK_PERFORMANCE", "OPTIMIZE_SYSTEM"]
            )
        
        return None


class AnomalousTradeDetector:
    """异常交易检测器主类"""
    
    def __init__(self, risk_limits: Optional[Dict[str, float]] = None):
        self.logger = get_logger(__name__)
        
        # 初始化各个检测器
        self.pattern_analyzer = TradingPatternAnalyzer()
        self.risk_violation_detector = RiskViolationDetector(
            risk_limits or self._get_default_risk_limits()
        )
        self.system_malfunction_detector = SystemMalfunctionDetector()
        
        # 检测历史
        self.detection_history = []
    
    def _get_default_risk_limits(self) -> Dict[str, float]:
        """获取默认风险限制"""
        return {
            'account_value': 1000000,      # 账户价值
            'max_position_ratio': 0.3,     # 最大单仓位比例
            'max_drawdown': 0.2,           # 最大回撤
            'max_leverage': 2.0,           # 最大杠杆
            'max_daily_trades': 100,       # 最大日交易次数
            'max_hourly_trades': 20        # 最大小时交易次数
        }
    
    def detect_all_anomalies(self, trading_data: Dict[str, Any]) -> List[TradingAnomalyEvent]:
        """检测所有类型的异常"""
        anomalies = []
        
        try:
            # 检测交易模式异常
            trades = trading_data.get('trades', [])
            positions = trading_data.get('positions', {})
            position_history = trading_data.get('position_history', [])
            account_data = trading_data.get('account', {})
            error_logs = trading_data.get('error_logs', [])
            response_times = trading_data.get('response_times', [])
            
            # 交易频率异常
            frequency_anomaly = self.pattern_analyzer.analyze_trading_frequency(trades)
            if frequency_anomaly:
                anomalies.append(frequency_anomaly)
            
            # 交易规模异常
            size_anomaly = self.pattern_analyzer.analyze_position_size(trades)
            if size_anomaly:
                anomalies.append(size_anomaly)
            
            # 快速仓位变化
            position_anomaly = self.pattern_analyzer.analyze_rapid_position_changes(position_history)
            if position_anomaly:
                anomalies.append(position_anomaly)
            
            # 交易时机异常
            timing_anomaly = self.pattern_analyzer.analyze_trading_timing(trades)
            if timing_anomaly:
                anomalies.append(timing_anomaly)
            
            # 风险违规检测
            position_violations = self.risk_violation_detector.check_position_limits(positions)
            anomalies.extend(position_violations)
            
            drawdown_violation = self.risk_violation_detector.check_drawdown_limits(account_data)
            if drawdown_violation:
                anomalies.append(drawdown_violation)
            
            leverage_violation = self.risk_violation_detector.check_leverage_limits(account_data)
            if leverage_violation:
                anomalies.append(leverage_violation)
            
            # 系统故障检测
            error_anomaly = self.system_malfunction_detector.detect_error_patterns(error_logs)
            if error_anomaly:
                anomalies.append(error_anomaly)
            
            performance_anomaly = self.system_malfunction_detector.detect_performance_degradation(response_times)
            if performance_anomaly:
                anomalies.append(performance_anomaly)
            
            # 记录检测历史
            self.detection_history.append({
                'timestamp': datetime.now(),
                'anomaly_count': len(anomalies),
                'anomalies': [anomaly.anomaly_type.value for anomaly in anomalies]
            })
            
            # 只保留最近1000条记录
            if len(self.detection_history) > 1000:
                self.detection_history = self.detection_history[-1000:]
            
        except Exception as e:
            self.logger.error(f"异常检测过程出错: {e}")
        
        return anomalies
    
    def get_detection_summary(self) -> Dict[str, Any]:
        """获取检测摘要"""
        if not self.detection_history:
            return {"total_detections": 0, "anomaly_types": {}}
        
        total_detections = len(self.detection_history)
        anomaly_type_counts = {}
        
        for record in self.detection_history:
            for anomaly_type in record['anomalies']:
                anomaly_type_counts[anomaly_type] = anomaly_type_counts.get(anomaly_type, 0) + 1
        
        return {
            "total_detections": total_detections,
            "anomaly_types": anomaly_type_counts,
            "last_detection": self.detection_history[-1]['timestamp'].isoformat(),
            "avg_anomalies_per_detection": sum(
                record['anomaly_count'] for record in self.detection_history
            ) / total_detections
        }
    
    def update_baseline_metrics(self, trading_data: Dict[str, Any]) -> None:
        """更新基准指标"""
        trades = trading_data.get('trades', [])
        
        if trades:
            # 计算平均小时交易次数
            trade_times = [trade.get('timestamp', datetime.now()) for trade in trades[-100:]]
            if trade_times:
                time_span_hours = (max(trade_times) - min(trade_times)).total_seconds() / 3600
                if time_span_hours > 0:
                    avg_hourly_trades = len(trade_times) / time_span_hours
                    self.pattern_analyzer.baseline_metrics['avg_hourly_trades'] = avg_hourly_trades