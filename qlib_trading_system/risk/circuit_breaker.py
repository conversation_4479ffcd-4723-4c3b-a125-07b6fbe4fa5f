"""
极端行情熔断机制模块
实现多级市场异常检测、自动熔断和防御策略系统
"""

import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from enum import Enum
import pandas as pd
import numpy as np
from threading import Lock
import json

from ..utils.config.manager import ConfigManager
from ..utils.logging.logger import get_logger


class AlertLevel(Enum):
    """警报级别枚举"""
    NORMAL = "NORMAL"           # 正常状态
    WARNING = "WARNING"         # 警告状态
    DANGER = "DANGER"          # 危险状态
    CRITICAL = "CRITICAL"      # 严重状态
    EMERGENCY = "EMERGENCY"    # 紧急状态


class MarketCondition(Enum):
    """市场状态枚举"""
    NORMAL = "NORMAL"           # 正常市场
    VOLATILE = "VOLATILE"       # 波动市场
    CRASH = "CRASH"            # 崩盘市场
    PANIC = "PANIC"            # 恐慌市场
    BLACK_SWAN = "BLACK_SWAN"  # 黑天鹅事件


@dataclass
class MarketAnomalyEvent:
    """市场异常事件数据类"""
    event_type: str
    severity: AlertLevel
    timestamp: datetime
    symbol: Optional[str] = None
    description: str = ""
    metrics: Dict[str, float] = field(default_factory=dict)
    suggested_actions: List[str] = field(default_factory=list)


@dataclass
class CircuitBreakerConfig:
    """熔断机制配置"""
    # 价格异常阈值
    single_stock_drop_threshold: float = -0.08  # 单股跌幅8%触发警告
    single_stock_crash_threshold: float = -0.15  # 单股跌幅15%触发熔断
    market_drop_threshold: float = -0.03         # 大盘跌幅3%触发警告
    market_crash_threshold: float = -0.05        # 大盘跌幅5%触发熔断
    
    # 成交量异常阈值
    volume_surge_threshold: float = 3.0          # 成交量放大3倍
    volume_dry_threshold: float = 0.3            # 成交量萎缩至30%
    
    # 波动率异常阈值
    volatility_spike_threshold: float = 2.0      # 波动率激增2倍
    
    # 连续亏损阈值
    consecutive_loss_days: int = 3               # 连续亏损天数
    daily_loss_threshold: float = -0.01          # 日亏损1%
    
    # 时间窗口设置
    anomaly_detection_window: int = 300          # 异常检测时间窗口(秒)
    circuit_breaker_cooldown: int = 1800         # 熔断冷却时间(秒)


class MarketAnomalyDetector:
    """市场异常检测器"""
    
    def __init__(self, config: CircuitBreakerConfig):
        self.config = config
        self.logger = get_logger(__name__)
        self._price_history = {}
        self._volume_history = {}
        self._volatility_history = {}
        self._lock = Lock()
    
    def detect_price_anomaly(self, symbol: str, current_price: float, 
                           reference_price: float) -> Optional[MarketAnomalyEvent]:
        """检测价格异常"""
        price_change = (current_price - reference_price) / reference_price
        
        if price_change <= self.config.single_stock_crash_threshold:
            return MarketAnomalyEvent(
                event_type="PRICE_CRASH",
                severity=AlertLevel.CRITICAL,
                timestamp=datetime.now(),
                symbol=symbol,
                description=f"股票{symbol}价格暴跌{price_change:.2%}",
                metrics={"price_change": price_change},
                suggested_actions=["EMERGENCY_EXIT", "STOP_TRADING"]
            )
        elif price_change <= self.config.single_stock_drop_threshold:
            return MarketAnomalyEvent(
                event_type="PRICE_DROP",
                severity=AlertLevel.WARNING,
                timestamp=datetime.now(),
                symbol=symbol,
                description=f"股票{symbol}价格下跌{price_change:.2%}",
                metrics={"price_change": price_change},
                suggested_actions=["REDUCE_POSITION", "MONITOR_CLOSELY"]
            )
        
        return None
    
    def detect_volume_anomaly(self, symbol: str, current_volume: float,
                            avg_volume: float) -> Optional[MarketAnomalyEvent]:
        """检测成交量异常"""
        volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1.0
        
        if volume_ratio >= self.config.volume_surge_threshold:
            return MarketAnomalyEvent(
                event_type="VOLUME_SURGE",
                severity=AlertLevel.WARNING,
                timestamp=datetime.now(),
                symbol=symbol,
                description=f"股票{symbol}成交量异常放大{volume_ratio:.1f}倍",
                metrics={"volume_ratio": volume_ratio},
                suggested_actions=["MONITOR_CLOSELY", "CHECK_NEWS"]
            )
        elif volume_ratio <= self.config.volume_dry_threshold:
            return MarketAnomalyEvent(
                event_type="VOLUME_DRY",
                severity=AlertLevel.DANGER,
                timestamp=datetime.now(),
                symbol=symbol,
                description=f"股票{symbol}成交量严重萎缩至{volume_ratio:.1%}",
                metrics={"volume_ratio": volume_ratio},
                suggested_actions=["GRADUAL_EXIT", "AVOID_NEW_POSITION"]
            )
        
        return None
    
    def detect_market_anomaly(self, market_data: Dict[str, float]) -> Optional[MarketAnomalyEvent]:
        """检测市场整体异常"""
        index_change = market_data.get("index_change", 0.0)
        market_volume_ratio = market_data.get("volume_ratio", 1.0)
        
        # 检测市场崩盘
        if index_change <= self.config.market_crash_threshold:
            return MarketAnomalyEvent(
                event_type="MARKET_CRASH",
                severity=AlertLevel.EMERGENCY,
                timestamp=datetime.now(),
                description=f"市场出现崩盘，指数下跌{index_change:.2%}",
                metrics={"index_change": index_change, "volume_ratio": market_volume_ratio},
                suggested_actions=["FULL_DEFENSIVE_MODE", "EMERGENCY_LIQUIDATION"]
            )
        elif index_change <= self.config.market_drop_threshold:
            return MarketAnomalyEvent(
                event_type="MARKET_DROP",
                severity=AlertLevel.DANGER,
                timestamp=datetime.now(),
                description=f"市场大幅下跌{index_change:.2%}",
                metrics={"index_change": index_change},
                suggested_actions=["DEFENSIVE_MODE", "REDUCE_EXPOSURE"]
            )
        
        return None
    
    def detect_volatility_spike(self, symbol: str, current_volatility: float,
                              avg_volatility: float) -> Optional[MarketAnomalyEvent]:
        """检测波动率异常激增"""
        volatility_ratio = current_volatility / avg_volatility if avg_volatility > 0 else 1.0
        
        if volatility_ratio >= self.config.volatility_spike_threshold:
            return MarketAnomalyEvent(
                event_type="VOLATILITY_SPIKE",
                severity=AlertLevel.WARNING,
                timestamp=datetime.now(),
                symbol=symbol,
                description=f"股票{symbol}波动率激增{volatility_ratio:.1f}倍",
                metrics={"volatility_ratio": volatility_ratio},
                suggested_actions=["REDUCE_POSITION_SIZE", "INCREASE_STOP_LOSS"]
            )
        
        return None


class BlackSwanDetector:
    """黑天鹅事件检测器"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.news_keywords = {
            "negative": ["立案调查", "ST", "退市", "减持", "财务造假", "监管处罚", 
                        "停牌", "重大违法", "欺诈", "内幕交易", "操纵市场"],
            "market_risk": ["熔断", "暴跌", "崩盘", "恐慌", "流动性危机", 
                           "系统性风险", "金融危机", "经济衰退"]
        }
    
    def detect_news_anomaly(self, news_data: List[str]) -> Optional[MarketAnomalyEvent]:
        """检测新闻异常事件"""
        negative_score = 0
        market_risk_score = 0
        
        for news in news_data:
            for keyword in self.news_keywords["negative"]:
                if keyword in news:
                    negative_score += 1
            for keyword in self.news_keywords["market_risk"]:
                if keyword in news:
                    market_risk_score += 2  # 市场风险权重更高
        
        total_risk_score = negative_score + market_risk_score
        
        if total_risk_score >= 5:
            return MarketAnomalyEvent(
                event_type="BLACK_SWAN_NEWS",
                severity=AlertLevel.EMERGENCY,
                timestamp=datetime.now(),
                description=f"检测到重大负面新闻，风险评分{total_risk_score}",
                metrics={"risk_score": total_risk_score},
                suggested_actions=["IMMEDIATE_REVIEW", "CONSIDER_EXIT"]
            )
        elif total_risk_score >= 3:
            return MarketAnomalyEvent(
                event_type="NEGATIVE_NEWS",
                severity=AlertLevel.DANGER,
                timestamp=datetime.now(),
                description=f"检测到负面新闻，风险评分{total_risk_score}",
                metrics={"risk_score": total_risk_score},
                suggested_actions=["MONITOR_CLOSELY", "PREPARE_EXIT"]
            )
        
        return None
    
    def detect_statistical_anomaly(self, price_series: pd.Series) -> Optional[MarketAnomalyEvent]:
        """检测统计异常（基于历史数据的异常检测）"""
        if len(price_series) < 20:
            return None
        
        # 计算Z-score
        returns = price_series.pct_change().dropna()
        if len(returns) < 10:
            return None
        
        current_return = returns.iloc[-1]
        mean_return = returns.mean()
        std_return = returns.std()
        
        if std_return == 0:
            return None
        
        z_score = abs((current_return - mean_return) / std_return)
        
        # 3-sigma规则：超过3个标准差认为是异常
        if z_score > 3:
            return MarketAnomalyEvent(
                event_type="STATISTICAL_ANOMALY",
                severity=AlertLevel.CRITICAL,
                timestamp=datetime.now(),
                description=f"检测到统计异常，Z-score={z_score:.2f}",
                metrics={"z_score": z_score, "current_return": current_return},
                suggested_actions=["STATISTICAL_REVIEW", "RISK_ASSESSMENT"]
            )
        
        return None


class DefenseStrategy:
    """防御策略系统"""
    
    def __init__(self, config: CircuitBreakerConfig):
        self.config = config
        self.logger = get_logger(__name__)
        self.active_defenses = {}
        self._lock = Lock()
    
    def get_defense_actions(self, anomaly: MarketAnomalyEvent) -> Dict[str, Any]:
        """根据异常事件获取防御行动"""
        defense_map = {
            "PRICE_CRASH": {
                "stop_new_orders": True,
                "emergency_exit": True,
                "position_reduction": 1.0,  # 全部清仓
                "trading_halt": True,
                "alert_level": "CRITICAL"
            },
            "PRICE_DROP": {
                "stop_new_orders": False,
                "emergency_exit": False,
                "position_reduction": 0.3,  # 减仓30%
                "trading_halt": False,
                "alert_level": "WARNING"
            },
            "VOLUME_SURGE": {
                "stop_new_orders": False,
                "emergency_exit": False,
                "position_reduction": 0.0,
                "trading_halt": False,
                "alert_level": "INFO",
                "monitor_closely": True
            },
            "VOLUME_DRY": {
                "stop_new_orders": True,
                "emergency_exit": False,
                "position_reduction": 0.5,  # 减仓50%
                "trading_halt": False,
                "alert_level": "WARNING"
            },
            "MARKET_CRASH": {
                "stop_new_orders": True,
                "emergency_exit": True,
                "position_reduction": 1.0,  # 全部清仓
                "trading_halt": True,
                "alert_level": "EMERGENCY",
                "defensive_mode": True
            },
            "BLACK_SWAN_NEWS": {
                "stop_new_orders": True,
                "emergency_exit": True,
                "position_reduction": 0.8,  # 减仓80%
                "trading_halt": True,
                "alert_level": "CRITICAL"
            },
            "STATISTICAL_ANOMALY": {
                "stop_new_orders": True,
                "emergency_exit": False,
                "position_reduction": 0.4,  # 减仓40%
                "trading_halt": False,
                "alert_level": "WARNING"
            }
        }
        
        return defense_map.get(anomaly.event_type, {
            "stop_new_orders": False,
            "emergency_exit": False,
            "position_reduction": 0.0,
            "trading_halt": False,
            "alert_level": "INFO"
        })
    
    def activate_defense(self, anomaly: MarketAnomalyEvent) -> Dict[str, Any]:
        """激活防御策略"""
        with self._lock:
            defense_actions = self.get_defense_actions(anomaly)
            
            # 记录防御激活
            self.active_defenses[anomaly.event_type] = {
                "anomaly": anomaly,
                "actions": defense_actions,
                "activated_at": datetime.now()
            }
            
            self.logger.critical(
                f"激活防御策略: {anomaly.event_type} - {anomaly.description}"
            )
            
            return defense_actions
    
    def deactivate_defense(self, event_type: str) -> bool:
        """停用防御策略"""
        with self._lock:
            if event_type in self.active_defenses:
                del self.active_defenses[event_type]
                self.logger.info(f"停用防御策略: {event_type}")
                return True
            return False
    
    def get_active_defenses(self) -> Dict[str, Any]:
        """获取当前激活的防御策略"""
        with self._lock:
            return self.active_defenses.copy()


class EmergencyStopLoss:
    """紧急止损系统"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.emergency_exits = {}
        self._lock = Lock()
    
    def should_emergency_exit(self, symbol: str, position_data: Dict[str, Any],
                            anomaly: MarketAnomalyEvent) -> bool:
        """判断是否需要紧急退出"""
        # 严重异常直接退出
        if anomaly.severity in [AlertLevel.CRITICAL, AlertLevel.EMERGENCY]:
            return True
        
        # 检查持仓亏损情况
        unrealized_pnl_pct = position_data.get("unrealized_pnl_pct", 0.0)
        if unrealized_pnl_pct <= -0.15:  # 亏损超过15%
            return True
        
        # 检查流动性风险
        if anomaly.event_type == "VOLUME_DRY":
            return True
        
        # 检查连续下跌
        consecutive_drops = position_data.get("consecutive_drops", 0)
        if consecutive_drops >= 3:
            return True
        
        return False
    
    def execute_emergency_exit(self, symbol: str, position_size: float,
                             exit_reason: str) -> Dict[str, Any]:
        """执行紧急退出"""
        with self._lock:
            exit_order = {
                "symbol": symbol,
                "action": "SELL",
                "quantity": position_size,
                "order_type": "MARKET",
                "urgency": "EMERGENCY",
                "reason": exit_reason,
                "timestamp": datetime.now()
            }
            
            self.emergency_exits[symbol] = exit_order
            
            self.logger.critical(
                f"执行紧急退出: {symbol}, 数量: {position_size}, 原因: {exit_reason}"
            )
            
            return exit_order
    
    def force_liquidation(self, positions: Dict[str, Any]) -> List[Dict[str, Any]]:
        """强制清算所有持仓"""
        liquidation_orders = []
        
        for symbol, position in positions.items():
            if position.get("quantity", 0) > 0:
                exit_order = self.execute_emergency_exit(
                    symbol=symbol,
                    position_size=position["quantity"],
                    exit_reason="FORCE_LIQUIDATION"
                )
                liquidation_orders.append(exit_order)
        
        self.logger.critical(f"强制清算完成，共处理{len(liquidation_orders)}个持仓")
        return liquidation_orders


class CircuitBreaker:
    """主熔断控制器"""
    
    def __init__(self, config: Optional[CircuitBreakerConfig] = None):
        self.config = config or CircuitBreakerConfig()
        self.logger = get_logger(__name__)
        
        # 初始化各个组件
        self.anomaly_detector = MarketAnomalyDetector(self.config)
        self.black_swan_detector = BlackSwanDetector()
        self.defense_strategy = DefenseStrategy(self.config)
        self.emergency_stop_loss = EmergencyStopLoss()
        
        # 状态管理
        self.is_active = True
        self.circuit_breaker_triggered = False
        self.last_trigger_time = None
        self.consecutive_loss_days = 0
        self.daily_pnl_history = []
        
        self._lock = Lock()
    
    def check_market_conditions(self, market_data: Dict[str, Any]) -> List[MarketAnomalyEvent]:
        """检查市场状况并识别异常"""
        anomalies = []
        
        try:
            # 检测价格异常
            for symbol, price_data in market_data.get("stocks", {}).items():
                current_price = price_data.get("current_price", 0)
                reference_price = price_data.get("reference_price", current_price)
                
                price_anomaly = self.anomaly_detector.detect_price_anomaly(
                    symbol, current_price, reference_price
                )
                if price_anomaly:
                    anomalies.append(price_anomaly)
                
                # 检测成交量异常
                current_volume = price_data.get("volume", 0)
                avg_volume = price_data.get("avg_volume", current_volume)
                
                volume_anomaly = self.anomaly_detector.detect_volume_anomaly(
                    symbol, current_volume, avg_volume
                )
                if volume_anomaly:
                    anomalies.append(volume_anomaly)
            
            # 检测市场整体异常
            market_anomaly = self.anomaly_detector.detect_market_anomaly(
                market_data.get("market", {})
            )
            if market_anomaly:
                anomalies.append(market_anomaly)
            
            # 检测黑天鹅事件
            news_data = market_data.get("news", [])
            if news_data:
                news_anomaly = self.black_swan_detector.detect_news_anomaly(news_data)
                if news_anomaly:
                    anomalies.append(news_anomaly)
            
        except Exception as e:
            self.logger.error(f"市场状况检查出错: {e}")
        
        return anomalies
    
    def process_anomalies(self, anomalies: List[MarketAnomalyEvent]) -> Dict[str, Any]:
        """处理检测到的异常"""
        if not anomalies:
            return {"status": "normal", "actions": []}
        
        # 按严重程度排序
        anomalies.sort(key=lambda x: x.severity.value, reverse=True)
        
        all_actions = []
        highest_severity = AlertLevel.NORMAL
        
        for anomaly in anomalies:
            # 激活对应的防御策略
            defense_actions = self.defense_strategy.activate_defense(anomaly)
            all_actions.append({
                "anomaly": anomaly,
                "actions": defense_actions
            })
            
            # 更新最高严重级别
            if anomaly.severity.value > highest_severity.value:
                highest_severity = anomaly.severity
        
        # 根据最高严重级别决定是否触发熔断
        if highest_severity in [AlertLevel.CRITICAL, AlertLevel.EMERGENCY]:
            self.trigger_circuit_breaker(anomalies[0])
        
        return {
            "status": "anomaly_detected",
            "highest_severity": highest_severity.value,
            "anomaly_count": len(anomalies),
            "actions": all_actions
        }
    
    def trigger_circuit_breaker(self, trigger_anomaly: MarketAnomalyEvent) -> None:
        """触发熔断机制"""
        with self._lock:
            if self.circuit_breaker_triggered:
                return
            
            self.circuit_breaker_triggered = True
            self.last_trigger_time = datetime.now()
            
            self.logger.critical(
                f"熔断机制触发! 触发事件: {trigger_anomaly.event_type} - {trigger_anomaly.description}"
            )
            
            # 发送紧急通知
            self._send_emergency_alert(trigger_anomaly)
    
    def reset_circuit_breaker(self) -> bool:
        """重置熔断机制"""
        with self._lock:
            if not self.circuit_breaker_triggered:
                return False
            
            # 检查冷却时间
            if self.last_trigger_time:
                cooldown_elapsed = (datetime.now() - self.last_trigger_time).seconds
                if cooldown_elapsed < self.config.circuit_breaker_cooldown:
                    remaining = self.config.circuit_breaker_cooldown - cooldown_elapsed
                    self.logger.warning(f"熔断冷却中，剩余{remaining}秒")
                    return False
            
            self.circuit_breaker_triggered = False
            self.last_trigger_time = None
            
            # 清除所有防御策略
            self.defense_strategy.active_defenses.clear()
            
            self.logger.info("熔断机制已重置")
            return True
    
    def check_consecutive_losses(self, daily_pnl: float) -> Optional[MarketAnomalyEvent]:
        """检查连续亏损情况"""
        self.daily_pnl_history.append({
            "date": datetime.now().date(),
            "pnl": daily_pnl
        })
        
        # 只保留最近30天的记录
        if len(self.daily_pnl_history) > 30:
            self.daily_pnl_history = self.daily_pnl_history[-30:]
        
        # 检查连续亏损天数
        consecutive_losses = 0
        for record in reversed(self.daily_pnl_history):
            if record["pnl"] < self.config.daily_loss_threshold:
                consecutive_losses += 1
            else:
                break
        
        self.consecutive_loss_days = consecutive_losses
        
        if consecutive_losses >= self.config.consecutive_loss_days:
            return MarketAnomalyEvent(
                event_type="CONSECUTIVE_LOSSES",
                severity=AlertLevel.DANGER,
                timestamp=datetime.now(),
                description=f"连续{consecutive_losses}天亏损",
                metrics={"consecutive_days": consecutive_losses, "daily_pnl": daily_pnl},
                suggested_actions=["REDUCE_FREQUENCY", "REDUCE_POSITION_SIZE"]
            )
        
        return None
    
    def _send_emergency_alert(self, anomaly: MarketAnomalyEvent) -> None:
        """发送紧急警报"""
        alert_message = {
            "type": "EMERGENCY_ALERT",
            "timestamp": datetime.now().isoformat(),
            "anomaly_type": anomaly.event_type,
            "severity": anomaly.severity.value,
            "description": anomaly.description,
            "metrics": anomaly.metrics,
            "suggested_actions": anomaly.suggested_actions
        }
        
        # 这里可以集成邮件、短信、钉钉等通知方式
        self.logger.critical(f"紧急警报: {json.dumps(alert_message, ensure_ascii=False)}")
    
    def get_status(self) -> Dict[str, Any]:
        """获取熔断器状态"""
        return {
            "is_active": self.is_active,
            "circuit_breaker_triggered": self.circuit_breaker_triggered,
            "last_trigger_time": self.last_trigger_time.isoformat() if self.last_trigger_time else None,
            "consecutive_loss_days": self.consecutive_loss_days,
            "active_defenses": list(self.defense_strategy.get_active_defenses().keys()),
            "config": {
                "single_stock_drop_threshold": self.config.single_stock_drop_threshold,
                "market_drop_threshold": self.config.market_drop_threshold,
                "consecutive_loss_days": self.config.consecutive_loss_days
            }
        }