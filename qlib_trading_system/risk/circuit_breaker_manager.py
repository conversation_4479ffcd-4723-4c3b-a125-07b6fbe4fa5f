"""
熔断机制管理器
负责协调熔断机制的各个组件，提供统一的接口
"""

import asyncio
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from concurrent.futures import ThreadPoolExecutor
import json

from .circuit_breaker import (
    CircuitBreaker, CircuitBreakerConfig, MarketAnomalyEvent, 
    AlertLevel, MarketCondition
)
from ..utils.logging.logger import get_logger
from ..utils.config.manager import ConfigManager


class CircuitBreakerManager:
    """熔断机制管理器"""
    
    def __init__(self, config: Optional[CircuitBreakerConfig] = None):
        self.config = config or CircuitBreakerConfig()
        self.logger = get_logger(__name__)
        
        # 初始化熔断器
        self.circuit_breaker = CircuitBreaker(self.config)
        
        # 状态管理
        self.is_running = False
        self.monitoring_thread = None
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        # 回调函数注册
        self.callbacks = {
            "on_anomaly_detected": [],
            "on_circuit_breaker_triggered": [],
            "on_emergency_exit": [],
            "on_defense_activated": []
        }
        
        # 数据缓存
        self.market_data_cache = {}
        self.position_data_cache = {}
        self.last_check_time = None
        
        self.logger.info("熔断机制管理器初始化完成")
    
    def start_monitoring(self) -> None:
        """启动监控"""
        if self.is_running:
            self.logger.warning("监控已在运行中")
            return
        
        self.is_running = True
        self.monitoring_thread = threading.Thread(
            target=self._monitoring_loop,
            daemon=True
        )
        self.monitoring_thread.start()
        
        self.logger.info("熔断机制监控已启动")
    
    def stop_monitoring(self) -> None:
        """停止监控"""
        if not self.is_running:
            return
        
        self.is_running = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        
        self.executor.shutdown(wait=True)
        self.logger.info("熔断机制监控已停止")
    
    def _monitoring_loop(self) -> None:
        """监控主循环"""
        while self.is_running:
            try:
                # 检查市场状况
                if self.market_data_cache:
                    anomalies = self.circuit_breaker.check_market_conditions(
                        self.market_data_cache
                    )
                    
                    if anomalies:
                        # 处理异常
                        result = self.circuit_breaker.process_anomalies(anomalies)
                        
                        # 触发回调
                        self._trigger_callbacks("on_anomaly_detected", {
                            "anomalies": anomalies,
                            "result": result
                        })
                        
                        # 如果触发了熔断，执行相应动作
                        if self.circuit_breaker.circuit_breaker_triggered:
                            self._handle_circuit_breaker_triggered(anomalies[0])
                
                # 检查连续亏损
                self._check_daily_performance()
                
                # 更新检查时间
                self.last_check_time = datetime.now()
                
                # 休眠一段时间
                time.sleep(1)  # 每秒检查一次
                
            except Exception as e:
                self.logger.error(f"监控循环出错: {e}")
                time.sleep(5)  # 出错后等待5秒再继续
    
    def update_market_data(self, market_data: Dict[str, Any]) -> None:
        """更新市场数据"""
        self.market_data_cache = market_data.copy()
        
        # 立即检查一次（如果正在监控）
        if self.is_running:
            self.executor.submit(self._immediate_check)
    
    def update_position_data(self, position_data: Dict[str, Any]) -> None:
        """更新持仓数据"""
        self.position_data_cache = position_data.copy()
    
    def _immediate_check(self) -> None:
        """立即检查市场状况"""
        try:
            anomalies = self.circuit_breaker.check_market_conditions(
                self.market_data_cache
            )
            
            if anomalies:
                result = self.circuit_breaker.process_anomalies(anomalies)
                
                # 触发回调
                self._trigger_callbacks("on_anomaly_detected", {
                    "anomalies": anomalies,
                    "result": result
                })
                
        except Exception as e:
            self.logger.error(f"立即检查出错: {e}")
    
    def _check_daily_performance(self) -> None:
        """检查日度表现"""
        if not self.position_data_cache:
            return
        
        # 计算当日PnL
        daily_pnl = self.position_data_cache.get("daily_pnl", 0.0)
        
        # 检查连续亏损
        consecutive_loss_anomaly = self.circuit_breaker.check_consecutive_losses(daily_pnl)
        
        if consecutive_loss_anomaly:
            # 处理连续亏损异常
            result = self.circuit_breaker.process_anomalies([consecutive_loss_anomaly])
            
            self._trigger_callbacks("on_anomaly_detected", {
                "anomalies": [consecutive_loss_anomaly],
                "result": result
            })
    
    def _handle_circuit_breaker_triggered(self, trigger_anomaly: MarketAnomalyEvent) -> None:
        """处理熔断触发"""
        self.logger.critical(f"处理熔断触发: {trigger_anomaly.event_type}")
        
        # 触发熔断回调
        self._trigger_callbacks("on_circuit_breaker_triggered", {
            "trigger_anomaly": trigger_anomaly,
            "timestamp": datetime.now()
        })
        
        # 根据异常类型执行相应动作
        if trigger_anomaly.severity == AlertLevel.EMERGENCY:
            self._execute_emergency_actions(trigger_anomaly)
    
    def _execute_emergency_actions(self, anomaly: MarketAnomalyEvent) -> None:
        """执行紧急动作"""
        self.logger.critical("执行紧急动作")
        
        # 强制清算所有持仓
        if self.position_data_cache:
            liquidation_orders = self.circuit_breaker.emergency_stop_loss.force_liquidation(
                self.position_data_cache.get("positions", {})
            )
            
            # 触发紧急退出回调
            self._trigger_callbacks("on_emergency_exit", {
                "anomaly": anomaly,
                "liquidation_orders": liquidation_orders
            })
    
    def register_callback(self, event_type: str, callback: Callable) -> None:
        """注册回调函数"""
        if event_type in self.callbacks:
            self.callbacks[event_type].append(callback)
            self.logger.info(f"注册回调函数: {event_type}")
        else:
            self.logger.warning(f"未知的事件类型: {event_type}")
    
    def _trigger_callbacks(self, event_type: str, data: Dict[str, Any]) -> None:
        """触发回调函数"""
        for callback in self.callbacks.get(event_type, []):
            try:
                self.executor.submit(callback, data)
            except Exception as e:
                self.logger.error(f"回调函数执行出错 {event_type}: {e}")
    
    def manual_trigger_circuit_breaker(self, reason: str) -> bool:
        """手动触发熔断"""
        manual_anomaly = MarketAnomalyEvent(
            event_type="MANUAL_TRIGGER",
            severity=AlertLevel.CRITICAL,
            timestamp=datetime.now(),
            description=f"手动触发熔断: {reason}",
            suggested_actions=["STOP_TRADING", "REVIEW_POSITIONS"]
        )
        
        self.circuit_breaker.trigger_circuit_breaker(manual_anomaly)
        self._handle_circuit_breaker_triggered(manual_anomaly)
        
        self.logger.warning(f"手动触发熔断: {reason}")
        return True
    
    def reset_circuit_breaker(self) -> bool:
        """重置熔断机制"""
        success = self.circuit_breaker.reset_circuit_breaker()
        if success:
            self.logger.info("熔断机制已重置")
        return success
    
    def get_current_status(self) -> Dict[str, Any]:
        """获取当前状态"""
        circuit_breaker_status = self.circuit_breaker.get_status()
        
        return {
            "manager_status": {
                "is_running": self.is_running,
                "last_check_time": self.last_check_time.isoformat() if self.last_check_time else None,
                "market_data_available": bool(self.market_data_cache),
                "position_data_available": bool(self.position_data_cache)
            },
            "circuit_breaker_status": circuit_breaker_status,
            "active_callbacks": {
                event_type: len(callbacks) 
                for event_type, callbacks in self.callbacks.items()
            }
        }
    
    def get_defense_recommendations(self, symbol: str) -> Dict[str, Any]:
        """获取防御建议"""
        active_defenses = self.circuit_breaker.defense_strategy.get_active_defenses()
        
        recommendations = {
            "symbol": symbol,
            "timestamp": datetime.now().isoformat(),
            "active_defenses": [],
            "suggested_actions": [],
            "risk_level": "LOW"
        }
        
        max_severity = AlertLevel.NORMAL
        
        for defense_type, defense_info in active_defenses.items():
            anomaly = defense_info["anomaly"]
            actions = defense_info["actions"]
            
            if anomaly.symbol == symbol or anomaly.symbol is None:
                recommendations["active_defenses"].append({
                    "type": defense_type,
                    "severity": anomaly.severity.value,
                    "description": anomaly.description,
                    "actions": actions
                })
                
                recommendations["suggested_actions"].extend(anomaly.suggested_actions)
                
                if anomaly.severity.value > max_severity.value:
                    max_severity = anomaly.severity
        
        # 设置风险级别
        if max_severity == AlertLevel.EMERGENCY:
            recommendations["risk_level"] = "CRITICAL"
        elif max_severity == AlertLevel.CRITICAL:
            recommendations["risk_level"] = "HIGH"
        elif max_severity == AlertLevel.DANGER:
            recommendations["risk_level"] = "MEDIUM"
        elif max_severity == AlertLevel.WARNING:
            recommendations["risk_level"] = "LOW"
        
        # 去重建议动作
        recommendations["suggested_actions"] = list(set(recommendations["suggested_actions"]))
        
        return recommendations
    
    def simulate_market_crash(self) -> Dict[str, Any]:
        """模拟市场崩盘（用于测试）"""
        crash_data = {
            "market": {
                "index_change": -0.08,  # 8%跌幅
                "volume_ratio": 2.5
            },
            "stocks": {
                "TEST001": {
                    "current_price": 9.2,
                    "reference_price": 10.0,
                    "volume": 1000000,
                    "avg_volume": 500000
                }
            },
            "news": ["市场出现暴跌", "投资者恐慌情绪蔓延", "流动性危机"]
        }
        
        self.update_market_data(crash_data)
        
        # 等待处理
        time.sleep(2)
        
        return self.get_current_status()


# 全局熔断管理器实例
_circuit_breaker_manager = None


def get_circuit_breaker_manager(config: Optional[CircuitBreakerConfig] = None) -> CircuitBreakerManager:
    """获取全局熔断管理器实例"""
    global _circuit_breaker_manager
    
    if _circuit_breaker_manager is None:
        _circuit_breaker_manager = CircuitBreakerManager(config)
    
    return _circuit_breaker_manager


def initialize_circuit_breaker(config: Optional[CircuitBreakerConfig] = None) -> CircuitBreakerManager:
    """初始化熔断机制"""
    manager = get_circuit_breaker_manager(config)
    manager.start_monitoring()
    return manager


def shutdown_circuit_breaker() -> None:
    """关闭熔断机制"""
    global _circuit_breaker_manager
    
    if _circuit_breaker_manager:
        _circuit_breaker_manager.stop_monitoring()
        _circuit_breaker_manager = None