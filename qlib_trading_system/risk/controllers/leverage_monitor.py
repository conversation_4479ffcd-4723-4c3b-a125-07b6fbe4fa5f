"""
杠杆监控器
实时监控杠杆使用情况，提供预警和自动调整功能
"""

import logging
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
from queue import Queue
import json

try:
    from .leverage_controller import DynamicLeverageController, AccountInfo, LeverageDecision, CapitalTier
except ImportError:
    from leverage_controller import DynamicLeverageController, AccountInfo, LeverageDecision, CapitalTier

# 配置日志
logger = logging.getLogger(__name__)

@dataclass
class LeverageAlert:
    """杠杆预警"""
    timestamp: datetime
    alert_type: str  # 'WARNING', 'CRITICAL', 'VIOLATION'
    symbol: str
    current_leverage: float
    limit_leverage: float
    message: str
    action_required: bool = False

class LeverageMonitor:
    """杠杆监控器"""
    
    def __init__(self, leverage_controller: DynamicLeverageController, 
                 monitoring_interval: int = 30):
        """
        初始化杠杆监控器
        
        Args:
            leverage_controller: 杠杆控制器
            monitoring_interval: 监控间隔（秒）
        """
        self.leverage_controller = leverage_controller
        self.monitoring_interval = monitoring_interval
        self.is_monitoring = False
        self.monitor_thread = None
        self.alert_queue = Queue()
        self.alert_callbacks: List[Callable] = []
        
        # 监控统计
        self.monitoring_stats = {
            'start_time': None,
            'alerts_generated': 0,
            'violations_detected': 0,
            'auto_adjustments': 0
        }
        
        logger.info("杠杆监控器初始化完成")
    
    def start_monitoring(self):
        """开始监控"""
        if self.is_monitoring:
            logger.warning("杠杆监控已在运行")
            return
        
        self.is_monitoring = True
        self.monitoring_stats['start_time'] = datetime.now()
        
        self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitor_thread.start()
        
        logger.info("杠杆监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        
        logger.info("杠杆监控已停止")
    
    def _monitoring_loop(self):
        """监控循环"""
        logger.info("杠杆监控循环开始")
        
        while self.is_monitoring:
            try:
                # 执行监控检查
                self._perform_monitoring_check()
                
                # 处理预警队列
                self._process_alerts()
                
                # 等待下一次检查
                time.sleep(self.monitoring_interval)
                
            except Exception as e:
                logger.error(f"监控循环异常: {e}")
                time.sleep(self.monitoring_interval)
        
        logger.info("杠杆监控循环结束")
    
    def _perform_monitoring_check(self):
        """执行监控检查"""
        try:
            # 这里应该从实际的持仓管理系统获取数据
            # 为了演示，我们使用模拟数据
            current_positions = self._get_current_positions()
            
            if not current_positions:
                return
            
            # 监控整体杠杆使用情况
            monitoring_result = self.leverage_controller.monitor_leverage_usage(current_positions)
            
            # 检查是否有风险警告
            if monitoring_result.get('risk_warnings'):
                for warning in monitoring_result['risk_warnings']:
                    alert = LeverageAlert(
                        timestamp=datetime.now(),
                        alert_type='WARNING',
                        symbol='PORTFOLIO',
                        current_leverage=monitoring_result.get('overall_leverage', 0),
                        limit_leverage=2.5,
                        message=warning,
                        action_required=True
                    )
                    self.alert_queue.put(alert)
                    self.monitoring_stats['alerts_generated'] += 1
            
            # 检查单个持仓杠杆
            for position in current_positions:
                self._check_position_leverage(position)
            
        except Exception as e:
            logger.error(f"监控检查失败: {e}")
    
    def _get_current_positions(self) -> List[Dict]:
        """获取当前持仓（模拟数据）"""
        # 在实际应用中，这里应该从持仓管理系统获取真实数据
        return [
            {
                'symbol': '000001.SZ',
                'shares': 1000,
                'current_price': 15.50,
                'leverage': 2.0,
                'cost_basis': 15.00
            },
            {
                'symbol': '000002.SZ', 
                'shares': 500,
                'current_price': 25.80,
                'leverage': 1.8,
                'cost_basis': 26.00
            }
        ]
    
    def _check_position_leverage(self, position: Dict):
        """检查单个持仓杠杆"""
        try:
            symbol = position.get('symbol', '')
            current_leverage = position.get('leverage', 1.0)
            
            # 模拟账户信息
            account = AccountInfo(
                total_capital=100000,
                available_capital=20000,
                used_capital=80000,
                current_drawdown=0.05,
                recent_win_rate=0.65,
                capital_tier=CapitalTier.SMALL
            )
            
            # 检查合规性
            compliance = self.leverage_controller.check_leverage_compliance(current_leverage, account)
            
            # 生成预警
            if not compliance['is_compliant']:
                alert = LeverageAlert(
                    timestamp=datetime.now(),
                    alert_type='VIOLATION',
                    symbol=symbol,
                    current_leverage=current_leverage,
                    limit_leverage=compliance['limits']['max'],
                    message=f"{symbol} 杠杆违规: {current_leverage}x > {compliance['limits']['max']}x",
                    action_required=True
                )
                self.alert_queue.put(alert)
                self.monitoring_stats['violations_detected'] += 1
                
            elif compliance.get('warnings'):
                alert = LeverageAlert(
                    timestamp=datetime.now(),
                    alert_type='WARNING',
                    symbol=symbol,
                    current_leverage=current_leverage,
                    limit_leverage=compliance['limits']['warning'],
                    message=f"{symbol} 杠杆警告: {current_leverage}x > {compliance['limits']['warning']}x",
                    action_required=False
                )
                self.alert_queue.put(alert)
                self.monitoring_stats['alerts_generated'] += 1
                
        except Exception as e:
            logger.error(f"检查持仓杠杆失败 {position}: {e}")
    
    def _process_alerts(self):
        """处理预警队列"""
        while not self.alert_queue.empty():
            try:
                alert = self.alert_queue.get_nowait()
                
                # 记录预警
                self._log_alert(alert)
                
                # 触发回调函数
                for callback in self.alert_callbacks:
                    try:
                        callback(alert)
                    except Exception as e:
                        logger.error(f"预警回调执行失败: {e}")
                
                # 如果需要自动调整
                if alert.action_required and alert.alert_type == 'VIOLATION':
                    self._auto_adjust_leverage(alert)
                
            except Exception as e:
                logger.error(f"处理预警失败: {e}")
    
    def _log_alert(self, alert: LeverageAlert):
        """记录预警日志"""
        log_message = (
            f"杠杆预警 [{alert.alert_type}] {alert.symbol}: "
            f"{alert.current_leverage}x (限制: {alert.limit_leverage}x) - {alert.message}"
        )
        
        if alert.alert_type == 'VIOLATION':
            logger.error(log_message)
        elif alert.alert_type == 'CRITICAL':
            logger.critical(log_message)
        else:
            logger.warning(log_message)
    
    def _auto_adjust_leverage(self, alert: LeverageAlert):
        """自动调整杠杆"""
        try:
            if alert.alert_type != 'VIOLATION':
                return
            
            # 计算目标杠杆（降至安全水平）
            target_leverage = alert.limit_leverage * 0.9  # 降至限制的90%
            
            logger.info(f"自动调整杠杆: {alert.symbol} {alert.current_leverage}x -> {target_leverage}x")
            
            # 这里应该调用实际的持仓调整接口
            # 为了演示，我们只记录调整动作
            adjustment_record = {
                'timestamp': datetime.now().isoformat(),
                'symbol': alert.symbol,
                'from_leverage': alert.current_leverage,
                'to_leverage': target_leverage,
                'reason': 'auto_adjustment_violation',
                'alert_id': id(alert)
            }
            
            logger.info(f"杠杆自动调整记录: {adjustment_record}")
            self.monitoring_stats['auto_adjustments'] += 1
            
        except Exception as e:
            logger.error(f"自动调整杠杆失败: {e}")
    
    def add_alert_callback(self, callback: Callable[[LeverageAlert], None]):
        """添加预警回调函数"""
        self.alert_callbacks.append(callback)
        logger.info("已添加杠杆预警回调函数")
    
    def remove_alert_callback(self, callback: Callable[[LeverageAlert], None]):
        """移除预警回调函数"""
        if callback in self.alert_callbacks:
            self.alert_callbacks.remove(callback)
            logger.info("已移除杠杆预警回调函数")
    
    def get_monitoring_status(self) -> Dict[str, Any]:
        """获取监控状态"""
        status = {
            'is_monitoring': self.is_monitoring,
            'monitoring_interval': self.monitoring_interval,
            'stats': self.monitoring_stats.copy(),
            'alert_queue_size': self.alert_queue.qsize(),
            'callback_count': len(self.alert_callbacks)
        }
        
        if self.monitoring_stats['start_time']:
            runtime = datetime.now() - self.monitoring_stats['start_time']
            status['runtime_seconds'] = runtime.total_seconds()
        
        return status
    
    def get_recent_alerts(self, hours: int = 24) -> List[Dict]:
        """获取最近的预警记录"""
        try:
            # 这里应该从数据库或日志文件中获取历史预警
            # 为了演示，返回模拟数据
            recent_alerts = [
                {
                    'timestamp': (datetime.now() - timedelta(hours=2)).isoformat(),
                    'alert_type': 'WARNING',
                    'symbol': '000001.SZ',
                    'current_leverage': 2.3,
                    'limit_leverage': 2.0,
                    'message': '杠杆接近警告线'
                },
                {
                    'timestamp': (datetime.now() - timedelta(hours=5)).isoformat(),
                    'alert_type': 'VIOLATION',
                    'symbol': '000002.SZ',
                    'current_leverage': 3.2,
                    'limit_leverage': 3.0,
                    'message': '杠杆超过最大限制'
                }
            ]
            
            return recent_alerts
            
        except Exception as e:
            logger.error(f"获取历史预警失败: {e}")
            return []
    
    def force_leverage_check(self, positions: Optional[List[Dict]] = None) -> Dict[str, Any]:
        """强制执行杠杆检查"""
        try:
            logger.info("执行强制杠杆检查")
            
            if positions is None:
                positions = self._get_current_positions()
            
            check_result = {
                'timestamp': datetime.now().isoformat(),
                'positions_checked': len(positions),
                'alerts_generated': 0,
                'violations_found': 0,
                'details': []
            }
            
            for position in positions:
                symbol = position.get('symbol', '')
                current_leverage = position.get('leverage', 1.0)
                
                # 模拟账户信息
                account = AccountInfo(
                    total_capital=100000,
                    available_capital=20000,
                    used_capital=80000,
                    current_drawdown=0.05,
                    recent_win_rate=0.65,
                    capital_tier=CapitalTier.SMALL
                )
                
                compliance = self.leverage_controller.check_leverage_compliance(current_leverage, account)
                
                position_result = {
                    'symbol': symbol,
                    'current_leverage': current_leverage,
                    'is_compliant': compliance['is_compliant'],
                    'warnings': compliance.get('warnings', []),
                    'violations': compliance.get('violations', [])
                }
                
                if not compliance['is_compliant']:
                    check_result['violations_found'] += 1
                    
                if compliance.get('warnings'):
                    check_result['alerts_generated'] += 1
                
                check_result['details'].append(position_result)
            
            logger.info(f"强制杠杆检查完成: {check_result['violations_found']} 违规, {check_result['alerts_generated']} 预警")
            
            return check_result
            
        except Exception as e:
            logger.error(f"强制杠杆检查失败: {e}")
            return {
                'timestamp': datetime.now().isoformat(),
                'error': str(e)
            }
    
    def set_monitoring_interval(self, interval: int):
        """设置监控间隔"""
        if interval < 10:
            logger.warning("监控间隔不能小于10秒，已设置为10秒")
            interval = 10
        
        self.monitoring_interval = interval
        logger.info(f"监控间隔已设置为 {interval} 秒")
    
    def get_leverage_statistics(self) -> Dict[str, Any]:
        """获取杠杆使用统计"""
        try:
            positions = self._get_current_positions()
            
            if not positions:
                return {
                    'timestamp': datetime.now().isoformat(),
                    'message': '暂无持仓数据'
                }
            
            leverages = [pos.get('leverage', 1.0) for pos in positions]
            market_values = [pos.get('shares', 0) * pos.get('current_price', 0) for pos in positions]
            
            total_market_value = sum(market_values)
            weighted_avg_leverage = sum(lev * mv for lev, mv in zip(leverages, market_values)) / total_market_value if total_market_value > 0 else 1.0
            
            statistics = {
                'timestamp': datetime.now().isoformat(),
                'position_count': len(positions),
                'total_market_value': total_market_value,
                'leverage_stats': {
                    'min': min(leverages),
                    'max': max(leverages),
                    'avg': sum(leverages) / len(leverages),
                    'weighted_avg': weighted_avg_leverage
                },
                'risk_distribution': {
                    'low_risk': len([l for l in leverages if l <= 1.5]),
                    'medium_risk': len([l for l in leverages if 1.5 < l <= 2.5]),
                    'high_risk': len([l for l in leverages if l > 2.5])
                }
            }
            
            return statistics
            
        except Exception as e:
            logger.error(f"获取杠杆统计失败: {e}")
            return {
                'timestamp': datetime.now().isoformat(),
                'error': str(e)
            }

class LeverageAlertHandler:
    """杠杆预警处理器"""
    
    def __init__(self):
        self.handlers = {
            'WARNING': self._handle_warning,
            'CRITICAL': self._handle_critical,
            'VIOLATION': self._handle_violation
        }
        
        logger.info("杠杆预警处理器初始化完成")
    
    def handle_alert(self, alert: LeverageAlert):
        """处理预警"""
        try:
            handler = self.handlers.get(alert.alert_type, self._handle_default)
            handler(alert)
            
        except Exception as e:
            logger.error(f"处理预警失败: {e}")
    
    def _handle_warning(self, alert: LeverageAlert):
        """处理警告级预警"""
        logger.warning(f"杠杆警告: {alert.symbol} {alert.message}")
        
        # 可以在这里添加邮件通知、短信通知等
        self._send_notification(alert, "WARNING")
    
    def _handle_critical(self, alert: LeverageAlert):
        """处理严重预警"""
        logger.critical(f"杠杆严重预警: {alert.symbol} {alert.message}")
        
        # 严重预警需要立即通知
        self._send_urgent_notification(alert)
    
    def _handle_violation(self, alert: LeverageAlert):
        """处理违规预警"""
        logger.error(f"杠杆违规: {alert.symbol} {alert.message}")
        
        # 违规需要立即处理
        self._send_urgent_notification(alert)
        self._trigger_emergency_action(alert)
    
    def _handle_default(self, alert: LeverageAlert):
        """默认处理"""
        logger.info(f"杠杆预警: {alert.symbol} {alert.message}")
    
    def _send_notification(self, alert: LeverageAlert, level: str):
        """发送通知"""
        # 这里可以集成邮件、短信、钉钉等通知方式
        notification_message = f"[{level}] 杠杆预警: {alert.symbol} - {alert.message}"
        logger.info(f"发送通知: {notification_message}")
    
    def _send_urgent_notification(self, alert: LeverageAlert):
        """发送紧急通知"""
        urgent_message = f"[紧急] 杠杆预警: {alert.symbol} - {alert.message}"
        logger.critical(f"发送紧急通知: {urgent_message}")
        
        # 这里可以集成紧急通知渠道
    
    def _trigger_emergency_action(self, alert: LeverageAlert):
        """触发紧急处理动作"""
        logger.critical(f"触发紧急处理: {alert.symbol} 杠杆违规")
        
        # 这里可以触发自动减仓、强制平仓等紧急动作