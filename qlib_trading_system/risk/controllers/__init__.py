"""
风险控制器模块
"""

from .leverage_controller import (
    DynamicLeverageController,
    LeverageConfig,
    AccountInfo,
    LeverageDecision,
    LeverageRecord,
    CapitalTier,
    MarketTrend
)

from .leverage_monitor import (
    LeverageMonitor,
    LeverageAlert,
    LeverageAlertHandler
)

from .leverage_analyzer import (
    LeverageAnalyzer,
    LeverageAnalysisResult
)

# 基础风险控制器类（其他风险控制器的基类）
class BaseRiskController:
    """基础风险控制器"""
    
    def __init__(self):
        self.is_active = True
        self.risk_level = "NORMAL"
    
    def activate(self):
        """激活风险控制器"""
        self.is_active = True
    
    def deactivate(self):
        """停用风险控制器"""
        self.is_active = False
    
    def get_status(self):
        """获取状态"""
        return {
            'is_active': self.is_active,
            'risk_level': self.risk_level
        }

# 通用风险控制器（占位类，后续任务实现）
class RiskController(BaseRiskController):
    """通用风险控制器占位类"""
    pass

class CircuitBreaker(BaseRiskController):
    """熔断机制占位类"""
    pass

class EmergencyRiskControl(BaseRiskController):
    """紧急风险控制占位类"""
    pass

class BlackSwanDefender(BaseRiskController):
    """黑天鹅防御占位类"""
    pass

__all__ = [
    # 基础类
    'BaseRiskController',
    'RiskController',
    'CircuitBreaker',
    'EmergencyRiskControl',
    'BlackSwanDefender',
    
    # 杠杆控制系统
    'DynamicLeverageController',
    'LeverageMonitor',
    'LeverageAnalyzer',
    
    # 数据类
    'LeverageConfig',
    'AccountInfo',
    'LeverageDecision',
    'LeverageRecord',
    'LeverageAlert',
    'LeverageAnalysisResult',
    'LeverageAlertHandler',
    
    # 枚举类
    'CapitalTier',
    'MarketTrend'
]