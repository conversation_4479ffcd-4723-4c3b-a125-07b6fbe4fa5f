"""
杠杆控制系统测试
测试动态杠杆控制、监控、分析等功能
"""

import unittest
import logging
import time
import tempfile
import os
from datetime import datetime, timedelta
from pathlib import Path

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from leverage_controller import (
    DynamicLeverageController,
    LeverageConfig,
    AccountInfo,
    CapitalTier,
    MarketTrend
)
from leverage_monitor import LeverageMonitor, LeverageAlert
from leverage_analyzer import LeverageAnalyzer
from leverage_management_system import LeverageManagementSystem, LeverageSystemConfig

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TestLeverageController(unittest.TestCase):
    """测试杠杆控制器"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        
        self.config = LeverageConfig()
        self.controller = DynamicLeverageController(self.config, self.temp_db.name)
        
        # 模拟账户信息
        self.account = AccountInfo(
            total_capital=100000,
            available_capital=20000,
            used_capital=80000,
            current_drawdown=0.05,
            recent_win_rate=0.65,
            capital_tier=CapitalTier.SMALL
        )
    
    def tearDown(self):
        """测试后清理"""
        try:
            os.unlink(self.temp_db.name)
        except:
            pass
    
    def test_calculate_dynamic_leverage(self):
        """测试动态杠杆计算"""
        logger.info("测试动态杠杆计算")
        
        # 测试不同波动率下的杠杆计算
        test_cases = [
            {'volatility': 0.1, 'expected_min': 1.0, 'expected_max': 2.0},
            {'volatility': 0.3, 'expected_min': 1.5, 'expected_max': 3.0},
            {'volatility': 0.5, 'expected_min': 2.0, 'expected_max': 3.0}
        ]
        
        for case in test_cases:
            with self.subTest(volatility=case['volatility']):
                decision = self.controller.calculate_dynamic_leverage(
                    self.account, 
                    case['volatility']
                )
                
                self.assertIsNotNone(decision)
                self.assertGreaterEqual(decision.recommended_leverage, case['expected_min'])
                self.assertLessEqual(decision.recommended_leverage, case['expected_max'])
                self.assertIn(decision.risk_level, ['LOW', 'MEDIUM', 'HIGH'])
                
                logger.info(f"波动率 {case['volatility']}: 杠杆 {decision.recommended_leverage}x, "
                          f"风险 {decision.risk_level}")
    
    def test_leverage_limits(self):
        """测试杠杆限制"""
        logger.info("测试杠杆限制")
        
        limits = self.controller.get_leverage_limits(self.account)
        
        self.assertIn('max', limits)
        self.assertIn('warning', limits)
        self.assertIn('normal', limits)
        
        self.assertGreater(limits['max'], limits['warning'])
        self.assertGreater(limits['warning'], limits['normal'])
        
        logger.info(f"杠杆限制: {limits}")
    
    def test_leverage_compliance(self):
        """测试杠杆合规检查"""
        logger.info("测试杠杆合规检查")
        
        # 测试合规情况
        compliance_normal = self.controller.check_leverage_compliance(1.5, self.account)
        self.assertTrue(compliance_normal['is_compliant'])
        
        # 测试违规情况
        compliance_violation = self.controller.check_leverage_compliance(5.0, self.account)
        self.assertFalse(compliance_violation['is_compliant'])
        self.assertGreater(len(compliance_violation['violations']), 0)
        
        logger.info(f"正常杠杆合规: {compliance_normal['is_compliant']}")
        logger.info(f"违规杠杆合规: {compliance_violation['is_compliant']}")
    
    def test_leverage_adjustment(self):
        """测试杠杆调整"""
        logger.info("测试杠杆调整")
        
        # 模拟风险评估
        risk_assessment = {
            'risk_level': 'HIGH',
            'overall_risk_score': 0.8
        }
        
        new_leverage, reason = self.controller.adjust_leverage(2.5, risk_assessment, -3.0)
        
        self.assertLess(new_leverage, 2.5)  # 应该降低杠杆
        self.assertIsInstance(reason, str)
        self.assertGreater(len(reason), 0)
        
        logger.info(f"杠杆调整: 2.5x -> {new_leverage}x, 原因: {reason}")
    
    def test_record_leverage_usage(self):
        """测试杠杆使用记录"""
        logger.info("测试杠杆使用记录")
        
        # 记录杠杆使用
        self.controller.record_leverage_usage(
            symbol='000001.SZ',
            leverage_ratio=2.0,
            position_size=1000,
            market_value=15000,
            risk_level='MEDIUM',
            pnl=500
        )
        
        # 验证记录是否保存
        self.assertEqual(len(self.controller.leverage_history), 1)
        
        record = self.controller.leverage_history[0]
        self.assertEqual(record.symbol, '000001.SZ')
        self.assertEqual(record.leverage_ratio, 2.0)
        self.assertEqual(record.pnl, 500)
        
        logger.info(f"杠杆使用记录: {record.symbol} {record.leverage_ratio}x")

class TestLeverageMonitor(unittest.TestCase):
    """测试杠杆监控器"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        
        self.controller = DynamicLeverageController(db_path=self.temp_db.name)
        self.monitor = LeverageMonitor(self.controller, monitoring_interval=1)  # 1秒间隔用于测试
        
        self.alerts_received = []
        
        def alert_callback(alert):
            self.alerts_received.append(alert)
        
        self.monitor.add_alert_callback(alert_callback)
    
    def tearDown(self):
        """测试后清理"""
        self.monitor.stop_monitoring()
        try:
            os.unlink(self.temp_db.name)
        except:
            pass
    
    def test_monitoring_lifecycle(self):
        """测试监控生命周期"""
        logger.info("测试监控生命周期")
        
        # 测试启动监控
        self.assertFalse(self.monitor.is_monitoring)
        self.monitor.start_monitoring()
        self.assertTrue(self.monitor.is_monitoring)
        
        # 等待一段时间让监控运行
        time.sleep(2)
        
        # 测试停止监控
        self.monitor.stop_monitoring()
        self.assertFalse(self.monitor.is_monitoring)
        
        logger.info("监控生命周期测试完成")
    
    def test_force_leverage_check(self):
        """测试强制杠杆检查"""
        logger.info("测试强制杠杆检查")
        
        # 模拟持仓数据
        positions = [
            {
                'symbol': '000001.SZ',
                'shares': 1000,
                'current_price': 15.50,
                'leverage': 2.8,  # 高杠杆
                'cost_basis': 15.00
            },
            {
                'symbol': '000002.SZ',
                'shares': 500,
                'current_price': 25.80,
                'leverage': 1.5,  # 正常杠杆
                'cost_basis': 26.00
            }
        ]
        
        check_result = self.monitor.force_leverage_check(positions)
        
        self.assertIn('timestamp', check_result)
        self.assertIn('positions_checked', check_result)
        self.assertIn('details', check_result)
        
        self.assertEqual(check_result['positions_checked'], 2)
        
        logger.info(f"强制检查结果: {check_result['violations_found']}个违规, "
                   f"{check_result['alerts_generated']}个预警")
    
    def test_leverage_statistics(self):
        """测试杠杆统计"""
        logger.info("测试杠杆统计")
        
        stats = self.monitor.get_leverage_statistics()
        
        self.assertIn('timestamp', stats)
        
        # 如果有持仓数据，检查统计结果
        if 'leverage_stats' in stats:
            self.assertIn('min', stats['leverage_stats'])
            self.assertIn('max', stats['leverage_stats'])
            self.assertIn('avg', stats['leverage_stats'])
        
        logger.info(f"杠杆统计: {stats}")

class TestLeverageAnalyzer(unittest.TestCase):
    """测试杠杆分析器"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        
        self.analyzer = LeverageAnalyzer(self.temp_db.name)
        
        # 创建一些测试数据
        self._create_test_data()
    
    def tearDown(self):
        """测试后清理"""
        try:
            os.unlink(self.temp_db.name)
        except:
            pass
    
    def _create_test_data(self):
        """创建测试数据"""
        import sqlite3
        import json
        
        conn = sqlite3.connect(self.temp_db.name)
        cursor = conn.cursor()
        
        # 创建表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS leverage_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                symbol TEXT NOT NULL,
                leverage_ratio REAL NOT NULL,
                position_size REAL NOT NULL,
                market_value REAL NOT NULL,
                risk_level TEXT NOT NULL,
                pnl REAL DEFAULT 0.0,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS leverage_decisions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                recommended_leverage REAL NOT NULL,
                max_allowed_leverage REAL NOT NULL,
                risk_level TEXT NOT NULL,
                factors TEXT NOT NULL,
                reason TEXT NOT NULL,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 插入测试数据
        test_records = [
            ('2024-01-01 10:00:00', '000001.SZ', 2.0, 1000, 15000, 'MEDIUM', 300),
            ('2024-01-01 11:00:00', '000002.SZ', 1.5, 500, 12500, 'LOW', 150),
            ('2024-01-01 14:00:00', '000001.SZ', 2.5, 1200, 18000, 'HIGH', -200),
            ('2024-01-02 10:00:00', '000003.SZ', 1.8, 800, 14400, 'MEDIUM', 400),
            ('2024-01-02 15:00:00', '000002.SZ', 2.2, 600, 15000, 'HIGH', 250)
        ]
        
        for record in test_records:
            cursor.execute('''
                INSERT INTO leverage_history 
                (timestamp, symbol, leverage_ratio, position_size, market_value, risk_level, pnl)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', record)
        
        # 插入决策数据
        test_decisions = [
            ('2024-01-01 09:00:00', 2.0, 2.5, 'MEDIUM', '{"volatility": 1.2, "risk": 0.8}', '推荐杠杆基于多因子分析'),
            ('2024-01-02 09:00:00', 1.8, 2.2, 'LOW', '{"volatility": 1.0, "risk": 0.9}', '市场波动降低，调整杠杆')
        ]
        
        for decision in test_decisions:
            cursor.execute('''
                INSERT INTO leverage_decisions 
                (timestamp, recommended_leverage, max_allowed_leverage, risk_level, factors, reason)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', decision)
        
        conn.commit()
        conn.close()
    
    def test_comprehensive_analysis(self):
        """测试综合分析"""
        logger.info("测试综合分析")
        
        analysis_result = self.analyzer.comprehensive_analysis(30)
        
        self.assertIsNotNone(analysis_result)
        self.assertGreater(analysis_result.total_trades, 0)
        self.assertGreaterEqual(analysis_result.win_rate, 0)
        self.assertLessEqual(analysis_result.win_rate, 1)
        self.assertGreater(analysis_result.avg_leverage, 0)
        
        logger.info(f"分析结果: {analysis_result.total_trades}笔交易, "
                   f"胜率{analysis_result.win_rate:.1%}, "
                   f"平均杠杆{analysis_result.avg_leverage:.2f}x")
    
    def test_generate_analysis_report(self):
        """测试生成分析报告"""
        logger.info("测试生成分析报告")
        
        analysis_result = self.analyzer.comprehensive_analysis(30)
        report = self.analyzer.generate_analysis_report(analysis_result)
        
        self.assertIsInstance(report, str)
        self.assertIn('杠杆使用历史分析报告', report)
        self.assertIn('基础统计', report)
        self.assertIn('收益分析', report)
        self.assertIn('优化建议', report)
        
        logger.info("分析报告生成成功")
        logger.info(f"报告长度: {len(report)}字符")

class TestLeverageManagementSystem(unittest.TestCase):
    """测试杠杆管理系统"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        
        config = LeverageSystemConfig(
            leverage_config=LeverageConfig(),
            monitoring_interval=1,  # 1秒间隔用于测试
            auto_adjustment=True
        )
        
        self.system = LeverageManagementSystem(config, self.temp_db.name)
    
    def tearDown(self):
        """测试后清理"""
        self.system.stop_system()
        try:
            os.unlink(self.temp_db.name)
        except:
            pass
    
    def test_system_lifecycle(self):
        """测试系统生命周期"""
        logger.info("测试系统生命周期")
        
        # 测试启动系统
        self.assertFalse(self.system.is_running)
        self.system.start_system()
        self.assertTrue(self.system.is_running)
        
        # 等待系统运行
        time.sleep(2)
        
        # 测试停止系统
        self.system.stop_system()
        self.assertFalse(self.system.is_running)
        
        logger.info("系统生命周期测试完成")
    
    def test_calculate_leverage_for_position(self):
        """测试为持仓计算杠杆"""
        logger.info("测试为持仓计算杠杆")
        
        account = AccountInfo(
            total_capital=100000,
            available_capital=20000,
            used_capital=80000,
            current_drawdown=0.05,
            recent_win_rate=0.65,
            capital_tier=CapitalTier.SMALL
        )
        
        decision = self.system.calculate_leverage_for_position(
            symbol='000001.SZ',
            account=account,
            stock_volatility=0.25
        )
        
        self.assertIsNotNone(decision)
        self.assertGreater(decision.recommended_leverage, 0)
        self.assertLessEqual(decision.recommended_leverage, self.system.config.max_system_leverage)
        
        logger.info(f"持仓杠杆计算: 000001.SZ {decision.recommended_leverage}x")
    
    def test_monitor_portfolio_leverage(self):
        """测试监控组合杠杆"""
        logger.info("测试监控组合杠杆")
        
        positions = [
            {
                'symbol': '000001.SZ',
                'shares': 1000,
                'current_price': 15.50,
                'leverage': 2.0,
                'cost_basis': 15.00
            },
            {
                'symbol': '000002.SZ',
                'shares': 500,
                'current_price': 25.80,
                'leverage': 1.8,
                'cost_basis': 26.00
            }
        ]
        
        monitoring_result = self.system.monitor_portfolio_leverage(positions)
        
        self.assertIn('timestamp', monitoring_result)
        self.assertIn('total_market_value', monitoring_result)
        self.assertIn('overall_leverage', monitoring_result)
        
        logger.info(f"组合监控结果: 整体杠杆{monitoring_result.get('overall_leverage', 0):.2f}x")
    
    def test_generate_system_report(self):
        """测试生成系统报告"""
        logger.info("测试生成系统报告")
        
        report = self.system.generate_system_report()
        
        self.assertIn('timestamp', report)
        self.assertIn('system_status', report)
        self.assertIn('system_stats', report)
        
        logger.info("系统报告生成成功")
    
    def test_force_system_check(self):
        """测试强制系统检查"""
        logger.info("测试强制系统检查")
        
        check_result = self.system.force_system_check()
        
        self.assertIn('timestamp', check_result)
        self.assertIn('overall_status', check_result)
        self.assertIn(check_result['overall_status'], ['HEALTHY', 'WARNING', 'ERROR'])
        
        logger.info(f"系统检查结果: {check_result['overall_status']}")

def run_integration_test():
    """运行集成测试"""
    logger.info("=" * 60)
    logger.info("开始杠杆控制系统集成测试")
    logger.info("=" * 60)
    
    try:
        # 创建临时数据库
        temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        temp_db.close()
        
        # 创建系统配置
        config = LeverageSystemConfig(
            leverage_config=LeverageConfig(max_leverage=3.0),
            monitoring_interval=2,
            auto_adjustment=True,
            enable_analysis=True
        )
        
        # 创建杠杆管理系统
        system = LeverageManagementSystem(config, temp_db.name)
        
        logger.info("1. 启动杠杆管理系统")
        system.start_system()
        
        # 模拟账户信息
        account = AccountInfo(
            total_capital=100000,
            available_capital=20000,
            used_capital=80000,
            current_drawdown=0.08,
            recent_win_rate=0.62,
            capital_tier=CapitalTier.SMALL
        )
        
        logger.info("2. 测试杠杆计算")
        decision = system.calculate_leverage_for_position(
            symbol='000001.SZ',
            account=account,
            stock_volatility=0.28,
            market_data={'index_change_5d': 3.2, 'volume_ratio': 1.15}
        )
        
        logger.info(f"   杠杆决策: {decision.recommended_leverage}x, 风险等级: {decision.risk_level}")
        logger.info(f"   决策原因: {decision.reason}")
        
        logger.info("3. 记录杠杆使用")
        system.leverage_controller.record_leverage_usage(
            symbol='000001.SZ',
            leverage_ratio=decision.recommended_leverage,
            position_size=1000,
            market_value=15500,
            risk_level=decision.risk_level,
            pnl=320
        )
        
        logger.info("4. 测试组合监控")
        positions = [
            {
                'symbol': '000001.SZ',
                'shares': 1000,
                'current_price': 15.50,
                'leverage': decision.recommended_leverage,
                'cost_basis': 15.00
            },
            {
                'symbol': '000002.SZ',
                'shares': 800,
                'current_price': 22.30,
                'leverage': 1.8,
                'cost_basis': 22.00
            }
        ]
        
        monitoring_result = system.monitor_portfolio_leverage(positions)
        logger.info(f"   组合杠杆: {monitoring_result.get('overall_leverage', 0):.2f}x")
        logger.info(f"   风险警告: {len(monitoring_result.get('risk_warnings', []))}个")
        
        logger.info("5. 等待监控运行")
        time.sleep(5)
        
        logger.info("6. 生成系统报告")
        report = system.generate_system_report()
        logger.info(f"   系统状态: {'运行中' if report['system_status']['is_running'] else '已停止'}")
        logger.info(f"   总决策数: {report['system_stats']['total_decisions']}")
        logger.info(f"   总预警数: {report['system_stats']['total_alerts']}")
        
        logger.info("7. 执行系统检查")
        check_result = system.force_system_check()
        logger.info(f"   系统健康状态: {check_result['overall_status']}")
        
        logger.info("8. 参数优化建议")
        optimization = system.optimize_system_parameters()
        if 'optimization_suggestions' in optimization:
            for key, value in optimization['optimization_suggestions'].items():
                if key != 'reason':
                    logger.info(f"   建议调整 {key}: {value}")
        
        logger.info("9. 停止系统")
        system.stop_system()
        
        # 清理
        os.unlink(temp_db.name)
        
        logger.info("=" * 60)
        logger.info("杠杆控制系统集成测试完成 - 所有功能正常")
        logger.info("=" * 60)
        
        return True
        
    except Exception as e:
        logger.error(f"集成测试失败: {e}")
        return False

if __name__ == '__main__':
    # 运行单元测试
    logger.info("开始运行杠杆控制系统单元测试")
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试用例
    test_suite.addTest(unittest.makeSuite(TestLeverageController))
    test_suite.addTest(unittest.makeSuite(TestLeverageMonitor))
    test_suite.addTest(unittest.makeSuite(TestLeverageAnalyzer))
    test_suite.addTest(unittest.makeSuite(TestLeverageManagementSystem))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 运行集成测试
    if result.wasSuccessful():
        logger.info("\n单元测试全部通过，开始集成测试...")
        integration_success = run_integration_test()
        
        if integration_success:
            logger.info("\n🎉 杠杆控制系统测试全部通过！")
            exit(0)
        else:
            logger.error("\n❌ 集成测试失败")
            exit(1)
    else:
        logger.error(f"\n❌ 单元测试失败: {len(result.failures)}个失败, {len(result.errors)}个错误")
        exit(1)