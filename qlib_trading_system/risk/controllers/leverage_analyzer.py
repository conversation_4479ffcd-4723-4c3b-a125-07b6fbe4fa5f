"""
杠杆历史分析器
深度分析杠杆使用历史，提供优化建议和策略改进
"""

import logging
import numpy as np
import pandas as pd
# import matplotlib.pyplot as plt
# import seaborn as sns
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import sqlite3
import json
from pathlib import Path

# 配置日志
logger = logging.getLogger(__name__)

@dataclass
class LeverageAnalysisResult:
    """杠杆分析结果"""
    analysis_period: str
    total_trades: int
    avg_leverage: float
    max_leverage: float
    min_leverage: float
    total_pnl: float
    win_rate: float
    sharpe_ratio: float
    max_drawdown: float
    leverage_efficiency: Dict[str, Any]
    risk_metrics: Dict[str, float]
    recommendations: List[str]
    timestamp: datetime

class LeverageAnalyzer:
    """杠杆历史分析器"""
    
    def __init__(self, db_path: str = "data/leverage_history.db"):
        """
        初始化杠杆分析器
        
        Args:
            db_path: 数据库路径
        """
        self.db_path = db_path
        self.analysis_cache = {}
        
        # 确保数据库存在
        if not Path(db_path).exists():
            logger.warning(f"数据库文件不存在: {db_path}")
        
        logger.info("杠杆历史分析器初始化完成")
    
    def comprehensive_analysis(self, days: int = 90) -> LeverageAnalysisResult:
        """
        综合分析杠杆使用历史
        
        Args:
            days: 分析天数
            
        Returns:
            LeverageAnalysisResult: 分析结果
        """
        try:
            logger.info(f"开始综合杠杆分析: {days}天")
            
            # 获取历史数据
            leverage_data, decision_data = self._load_historical_data(days)
            
            if leverage_data.empty:
                return self._create_empty_result(days)
            
            # 基础统计分析
            basic_stats = self._calculate_basic_statistics(leverage_data)
            
            # 收益分析
            return_analysis = self._analyze_returns(leverage_data)
            
            # 风险分析
            risk_analysis = self._analyze_risks(leverage_data)
            
            # 杠杆效率分析
            efficiency_analysis = self._analyze_leverage_efficiency(leverage_data)
            
            # 时间序列分析
            time_series_analysis = self._analyze_time_series(leverage_data)
            
            # 相关性分析
            correlation_analysis = self._analyze_correlations(leverage_data, decision_data)
            
            # 生成优化建议
            recommendations = self._generate_optimization_recommendations(
                basic_stats, return_analysis, risk_analysis, efficiency_analysis
            )
            
            # 构建分析结果
            result = LeverageAnalysisResult(
                analysis_period=f"{days}天",
                total_trades=len(leverage_data),
                avg_leverage=basic_stats['avg_leverage'],
                max_leverage=basic_stats['max_leverage'],
                min_leverage=basic_stats['min_leverage'],
                total_pnl=return_analysis['total_pnl'],
                win_rate=return_analysis['win_rate'],
                sharpe_ratio=return_analysis['sharpe_ratio'],
                max_drawdown=risk_analysis['max_drawdown'],
                leverage_efficiency=efficiency_analysis,
                risk_metrics=risk_analysis,
                recommendations=recommendations,
                timestamp=datetime.now()
            )
            
            # 缓存结果
            self.analysis_cache[f"{days}d"] = result
            
            logger.info(f"综合杠杆分析完成: {len(leverage_data)}笔交易, 胜率{return_analysis['win_rate']:.1%}")
            
            return result
            
        except Exception as e:
            logger.error(f"综合杠杆分析失败: {e}")
            return self._create_empty_result(days, error=str(e))
    
    def _load_historical_data(self, days: int) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """加载历史数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            start_date = (datetime.now() - timedelta(days=days)).isoformat()
            
            # 加载杠杆使用历史
            leverage_query = '''
                SELECT * FROM leverage_history 
                WHERE timestamp >= ? 
                ORDER BY timestamp ASC
            '''
            leverage_df = pd.read_sql_query(leverage_query, conn, params=[start_date])
            
            # 加载杠杆决策历史
            decision_query = '''
                SELECT * FROM leverage_decisions 
                WHERE timestamp >= ? 
                ORDER BY timestamp ASC
            '''
            decision_df = pd.read_sql_query(decision_query, conn, params=[start_date])
            
            conn.close()
            
            # 数据预处理
            if not leverage_df.empty:
                leverage_df['timestamp'] = pd.to_datetime(leverage_df['timestamp'])
                leverage_df['date'] = leverage_df['timestamp'].dt.date
                
            if not decision_df.empty:
                decision_df['timestamp'] = pd.to_datetime(decision_df['timestamp'])
                decision_df['factors'] = decision_df['factors'].apply(
                    lambda x: json.loads(x) if x else {}
                )
            
            return leverage_df, decision_df
            
        except Exception as e:
            logger.error(f"加载历史数据失败: {e}")
            return pd.DataFrame(), pd.DataFrame()
    
    def _calculate_basic_statistics(self, data: pd.DataFrame) -> Dict[str, float]:
        """计算基础统计指标"""
        try:
            stats = {
                'total_trades': len(data),
                'avg_leverage': data['leverage_ratio'].mean(),
                'max_leverage': data['leverage_ratio'].max(),
                'min_leverage': data['leverage_ratio'].min(),
                'std_leverage': data['leverage_ratio'].std(),
                'median_leverage': data['leverage_ratio'].median(),
                'avg_position_size': data['position_size'].mean(),
                'avg_market_value': data['market_value'].mean(),
                'total_market_value': data['market_value'].sum()
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"计算基础统计失败: {e}")
            return {}
    
    def _analyze_returns(self, data: pd.DataFrame) -> Dict[str, float]:
        """分析收益情况"""
        try:
            # 基础收益指标
            total_pnl = data['pnl'].sum()
            profitable_trades = len(data[data['pnl'] > 0])
            win_rate = profitable_trades / len(data) if len(data) > 0 else 0
            
            # 计算日收益率
            daily_returns = data.groupby('date')['pnl'].sum()
            
            # 夏普比率计算
            if len(daily_returns) > 1 and daily_returns.std() > 0:
                sharpe_ratio = daily_returns.mean() / daily_returns.std() * np.sqrt(252)
            else:
                sharpe_ratio = 0
            
            # 最大单日收益和亏损
            max_daily_profit = daily_returns.max() if not daily_returns.empty else 0
            max_daily_loss = daily_returns.min() if not daily_returns.empty else 0
            
            # 平均收益
            avg_profit_per_trade = data[data['pnl'] > 0]['pnl'].mean() if profitable_trades > 0 else 0
            avg_loss_per_trade = data[data['pnl'] < 0]['pnl'].mean() if len(data) - profitable_trades > 0 else 0
            
            # 盈亏比
            profit_loss_ratio = abs(avg_profit_per_trade / avg_loss_per_trade) if avg_loss_per_trade != 0 else 0
            
            return {
                'total_pnl': total_pnl,
                'win_rate': win_rate,
                'sharpe_ratio': sharpe_ratio,
                'max_daily_profit': max_daily_profit,
                'max_daily_loss': max_daily_loss,
                'avg_profit_per_trade': avg_profit_per_trade,
                'avg_loss_per_trade': avg_loss_per_trade,
                'profit_loss_ratio': profit_loss_ratio,
                'profitable_trades': profitable_trades,
                'losing_trades': len(data) - profitable_trades
            }
            
        except Exception as e:
            logger.error(f"收益分析失败: {e}")
            return {}
    
    def _analyze_risks(self, data: pd.DataFrame) -> Dict[str, float]:
        """分析风险指标"""
        try:
            # 计算累计收益曲线
            data_sorted = data.sort_values('timestamp')
            cumulative_pnl = data_sorted['pnl'].cumsum()
            
            # 最大回撤
            running_max = cumulative_pnl.expanding().max()
            drawdown = (cumulative_pnl - running_max) / running_max.abs()
            max_drawdown = drawdown.min() if not drawdown.empty else 0
            
            # VaR计算（95%置信度）
            daily_returns = data.groupby('date')['pnl'].sum()
            var_95 = daily_returns.quantile(0.05) if not daily_returns.empty else 0
            
            # 波动率
            volatility = daily_returns.std() if len(daily_returns) > 1 else 0
            
            # 杠杆风险指标
            high_leverage_trades = len(data[data['leverage_ratio'] > 2.5])
            high_leverage_ratio = high_leverage_trades / len(data) if len(data) > 0 else 0
            
            # 集中度风险
            symbol_concentration = data['symbol'].value_counts()
            max_symbol_ratio = symbol_concentration.iloc[0] / len(data) if not symbol_concentration.empty else 0
            
            # 风险调整收益
            total_return = data['pnl'].sum()
            risk_adjusted_return = total_return / volatility if volatility > 0 else 0
            
            return {
                'max_drawdown': abs(max_drawdown),
                'var_95': var_95,
                'volatility': volatility,
                'high_leverage_ratio': high_leverage_ratio,
                'max_symbol_concentration': max_symbol_ratio,
                'risk_adjusted_return': risk_adjusted_return,
                'downside_deviation': daily_returns[daily_returns < 0].std() if not daily_returns.empty else 0
            }
            
        except Exception as e:
            logger.error(f"风险分析失败: {e}")
            return {}
    
    def _analyze_leverage_efficiency(self, data: pd.DataFrame) -> Dict[str, Any]:
        """分析杠杆效率"""
        try:
            efficiency_data = {}
            
            # 按杠杆水平分组分析
            leverage_bins = [1.0, 1.5, 2.0, 2.5, 3.0, float('inf')]
            leverage_labels = ['1.0-1.5x', '1.5-2.0x', '2.0-2.5x', '2.5-3.0x', '3.0x+']
            
            data['leverage_group'] = pd.cut(data['leverage_ratio'], bins=leverage_bins, labels=leverage_labels, right=False)
            
            for group in leverage_labels:
                group_data = data[data['leverage_group'] == group]
                
                if not group_data.empty:
                    group_stats = {
                        'trade_count': len(group_data),
                        'total_pnl': group_data['pnl'].sum(),
                        'avg_pnl': group_data['pnl'].mean(),
                        'win_rate': len(group_data[group_data['pnl'] > 0]) / len(group_data),
                        'avg_leverage': group_data['leverage_ratio'].mean(),
                        'risk_score': len(group_data[group_data['risk_level'] == 'HIGH']) / len(group_data)
                    }
                    
                    # 计算效率指标（收益/风险）
                    group_stats['efficiency_score'] = (
                        group_stats['avg_pnl'] * group_stats['win_rate'] / 
                        (group_stats['avg_leverage'] * (1 + group_stats['risk_score']))
                    )
                    
                    efficiency_data[group] = group_stats
            
            # 找出最优杠杆区间
            best_efficiency = -float('inf')
            best_leverage_group = None
            
            for group, stats in efficiency_data.items():
                if stats['trade_count'] >= 5 and stats['efficiency_score'] > best_efficiency:
                    best_efficiency = stats['efficiency_score']
                    best_leverage_group = group
            
            efficiency_data['optimal_leverage_range'] = best_leverage_group
            efficiency_data['optimal_efficiency_score'] = best_efficiency
            
            return efficiency_data
            
        except Exception as e:
            logger.error(f"杠杆效率分析失败: {e}")
            return {}
    
    def _analyze_time_series(self, data: pd.DataFrame) -> Dict[str, Any]:
        """时间序列分析"""
        try:
            # 按日期聚合
            daily_stats = data.groupby('date').agg({
                'leverage_ratio': 'mean',
                'pnl': 'sum',
                'market_value': 'sum',
                'position_size': 'sum'
            }).reset_index()
            
            # 趋势分析
            if len(daily_stats) > 1:
                # 杠杆使用趋势
                leverage_trend = np.polyfit(range(len(daily_stats)), daily_stats['leverage_ratio'], 1)[0]
                
                # 收益趋势
                pnl_trend = np.polyfit(range(len(daily_stats)), daily_stats['pnl'], 1)[0]
                
                # 波动性趋势
                rolling_volatility = daily_stats['pnl'].rolling(window=7).std()
                volatility_trend = np.polyfit(
                    range(len(rolling_volatility.dropna())), 
                    rolling_volatility.dropna(), 1
                )[0] if len(rolling_volatility.dropna()) > 1 else 0
            else:
                leverage_trend = 0
                pnl_trend = 0
                volatility_trend = 0
            
            # 周期性分析
            if not data.empty:
                data['weekday'] = data['timestamp'].dt.dayofweek
                weekday_performance = data.groupby('weekday')['pnl'].mean().to_dict()
                
                data['hour'] = data['timestamp'].dt.hour
                hourly_performance = data.groupby('hour')['pnl'].mean().to_dict()
            else:
                weekday_performance = {}
                hourly_performance = {}
            
            return {
                'daily_stats': daily_stats.to_dict('records'),
                'leverage_trend': leverage_trend,
                'pnl_trend': pnl_trend,
                'volatility_trend': volatility_trend,
                'weekday_performance': weekday_performance,
                'hourly_performance': hourly_performance
            }
            
        except Exception as e:
            logger.error(f"时间序列分析失败: {e}")
            return {}
    
    def _analyze_correlations(self, leverage_data: pd.DataFrame, decision_data: pd.DataFrame) -> Dict[str, Any]:
        """相关性分析"""
        try:
            correlations = {}
            
            if not leverage_data.empty:
                # 杠杆与收益的相关性
                leverage_pnl_corr = leverage_data['leverage_ratio'].corr(leverage_data['pnl'])
                correlations['leverage_pnl'] = leverage_pnl_corr
                
                # 杠杆与风险的相关性
                risk_scores = leverage_data['risk_level'].map({'LOW': 1, 'MEDIUM': 2, 'HIGH': 3})
                leverage_risk_corr = leverage_data['leverage_ratio'].corr(risk_scores)
                correlations['leverage_risk'] = leverage_risk_corr
                
                # 持仓规模与杠杆的相关性
                size_leverage_corr = leverage_data['position_size'].corr(leverage_data['leverage_ratio'])
                correlations['size_leverage'] = size_leverage_corr
            
            if not decision_data.empty:
                # 决策因子分析
                factor_analysis = {}
                
                for _, row in decision_data.iterrows():
                    factors = row['factors']
                    if isinstance(factors, dict):
                        for factor_name, factor_value in factors.items():
                            if factor_name not in factor_analysis:
                                factor_analysis[factor_name] = []
                            factor_analysis[factor_name].append(factor_value)
                
                # 计算各因子的统计特征
                factor_stats = {}
                for factor_name, values in factor_analysis.items():
                    if values:
                        factor_stats[factor_name] = {
                            'mean': np.mean(values),
                            'std': np.std(values),
                            'min': np.min(values),
                            'max': np.max(values)
                        }
                
                correlations['factor_analysis'] = factor_stats
            
            return correlations
            
        except Exception as e:
            logger.error(f"相关性分析失败: {e}")
            return {}
    
    def _generate_optimization_recommendations(self, basic_stats: Dict, return_analysis: Dict, 
                                             risk_analysis: Dict, efficiency_analysis: Dict) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        try:
            # 基于胜率的建议
            win_rate = return_analysis.get('win_rate', 0)
            if win_rate < 0.5:
                recommendations.append("胜率偏低，建议优化选股策略或降低杠杆水平")
            elif win_rate > 0.7:
                recommendations.append("胜率较高，可适度提升杠杆以增加收益")
            
            # 基于杠杆水平的建议
            avg_leverage = basic_stats.get('avg_leverage', 1.0)
            if avg_leverage > 2.5:
                recommendations.append("平均杠杆较高，建议加强风险控制")
            elif avg_leverage < 1.3:
                recommendations.append("杠杆使用保守，可适度提升以增加收益潜力")
            
            # 基于风险指标的建议
            max_drawdown = risk_analysis.get('max_drawdown', 0)
            if max_drawdown > 0.2:
                recommendations.append("最大回撤过大，建议降低杠杆或优化止损策略")
            
            high_leverage_ratio = risk_analysis.get('high_leverage_ratio', 0)
            if high_leverage_ratio > 0.3:
                recommendations.append("高杠杆交易比例过高，建议控制风险敞口")
            
            # 基于效率分析的建议
            optimal_range = efficiency_analysis.get('optimal_leverage_range')
            if optimal_range:
                recommendations.append(f"历史数据显示{optimal_range}杠杆区间效率最高，建议重点使用")
            
            # 基于夏普比率的建议
            sharpe_ratio = return_analysis.get('sharpe_ratio', 0)
            if sharpe_ratio < 1.0:
                recommendations.append("夏普比率偏低，建议优化风险收益比")
            
            # 基于盈亏比的建议
            profit_loss_ratio = return_analysis.get('profit_loss_ratio', 0)
            if profit_loss_ratio < 1.5:
                recommendations.append("盈亏比偏低，建议优化止盈止损策略")
            
            if not recommendations:
                recommendations.append("当前杠杆策略表现良好，建议保持现有策略")
            
        except Exception as e:
            logger.error(f"生成优化建议失败: {e}")
            recommendations.append("分析过程中出现异常，建议人工审查杠杆策略")
        
        return recommendations
    
    def _create_empty_result(self, days: int, error: str = None) -> LeverageAnalysisResult:
        """创建空的分析结果"""
        return LeverageAnalysisResult(
            analysis_period=f"{days}天",
            total_trades=0,
            avg_leverage=1.0,
            max_leverage=1.0,
            min_leverage=1.0,
            total_pnl=0.0,
            win_rate=0.0,
            sharpe_ratio=0.0,
            max_drawdown=0.0,
            leverage_efficiency={},
            risk_metrics={},
            recommendations=["暂无历史数据，无法生成分析报告"] if not error else [f"分析失败: {error}"],
            timestamp=datetime.now()
        )
    
    def generate_analysis_report(self, analysis_result: LeverageAnalysisResult, 
                               save_path: Optional[str] = None) -> str:
        """
        生成分析报告
        
        Args:
            analysis_result: 分析结果
            save_path: 保存路径
            
        Returns:
            str: 报告内容
        """
        try:
            report_lines = [
                "=" * 60,
                f"杠杆使用历史分析报告",
                "=" * 60,
                f"分析时间: {analysis_result.timestamp.strftime('%Y-%m-%d %H:%M:%S')}",
                f"分析周期: {analysis_result.analysis_period}",
                "",
                "基础统计:",
                f"  总交易次数: {analysis_result.total_trades}",
                f"  平均杠杆: {analysis_result.avg_leverage:.2f}x",
                f"  最大杠杆: {analysis_result.max_leverage:.2f}x",
                f"  最小杠杆: {analysis_result.min_leverage:.2f}x",
                "",
                "收益分析:",
                f"  总盈亏: {analysis_result.total_pnl:.2f}",
                f"  胜率: {analysis_result.win_rate:.1%}",
                f"  夏普比率: {analysis_result.sharpe_ratio:.3f}",
                "",
                "风险分析:",
                f"  最大回撤: {analysis_result.max_drawdown:.1%}",
                "",
                "杠杆效率分析:"
            ]
            
            # 添加效率分析详情
            if analysis_result.leverage_efficiency:
                for level, stats in analysis_result.leverage_efficiency.items():
                    if isinstance(stats, dict) and 'trade_count' in stats:
                        report_lines.append(
                            f"  {level}: {stats['trade_count']}笔交易, "
                            f"胜率{stats['win_rate']:.1%}, "
                            f"平均收益{stats['avg_pnl']:.2f}"
                        )
            
            report_lines.extend([
                "",
                "优化建议:"
            ])
            
            for i, recommendation in enumerate(analysis_result.recommendations, 1):
                report_lines.append(f"  {i}. {recommendation}")
            
            report_lines.extend([
                "",
                "=" * 60
            ])
            
            report_content = "\n".join(report_lines)
            
            # 保存报告
            if save_path:
                Path(save_path).parent.mkdir(parents=True, exist_ok=True)
                with open(save_path, 'w', encoding='utf-8') as f:
                    f.write(report_content)
                logger.info(f"分析报告已保存: {save_path}")
            
            return report_content
            
        except Exception as e:
            logger.error(f"生成分析报告失败: {e}")
            return f"报告生成失败: {e}"
    
    def compare_periods(self, period1_days: int, period2_days: int) -> Dict[str, Any]:
        """
        比较不同时期的杠杆使用表现
        
        Args:
            period1_days: 第一个时期天数
            period2_days: 第二个时期天数
            
        Returns:
            Dict: 比较结果
        """
        try:
            logger.info(f"比较杠杆表现: {period1_days}天 vs {period2_days}天")
            
            # 获取两个时期的分析结果
            result1 = self.comprehensive_analysis(period1_days)
            result2 = self.comprehensive_analysis(period2_days)
            
            # 计算变化
            comparison = {
                'period1': f"{period1_days}天",
                'period2': f"{period2_days}天",
                'timestamp': datetime.now().isoformat(),
                'metrics_comparison': {
                    'total_trades': {
                        'period1': result1.total_trades,
                        'period2': result2.total_trades,
                        'change': result2.total_trades - result1.total_trades
                    },
                    'avg_leverage': {
                        'period1': result1.avg_leverage,
                        'period2': result2.avg_leverage,
                        'change': result2.avg_leverage - result1.avg_leverage
                    },
                    'win_rate': {
                        'period1': result1.win_rate,
                        'period2': result2.win_rate,
                        'change': result2.win_rate - result1.win_rate
                    },
                    'sharpe_ratio': {
                        'period1': result1.sharpe_ratio,
                        'period2': result2.sharpe_ratio,
                        'change': result2.sharpe_ratio - result1.sharpe_ratio
                    },
                    'max_drawdown': {
                        'period1': result1.max_drawdown,
                        'period2': result2.max_drawdown,
                        'change': result2.max_drawdown - result1.max_drawdown
                    }
                }
            }
            
            # 生成比较结论
            conclusions = []
            
            if comparison['metrics_comparison']['win_rate']['change'] > 0.05:
                conclusions.append("近期胜率显著提升")
            elif comparison['metrics_comparison']['win_rate']['change'] < -0.05:
                conclusions.append("近期胜率有所下降")
            
            if comparison['metrics_comparison']['avg_leverage']['change'] > 0.2:
                conclusions.append("近期杠杆使用更加激进")
            elif comparison['metrics_comparison']['avg_leverage']['change'] < -0.2:
                conclusions.append("近期杠杆使用更加保守")
            
            if comparison['metrics_comparison']['sharpe_ratio']['change'] > 0.2:
                conclusions.append("近期风险调整收益改善")
            elif comparison['metrics_comparison']['sharpe_ratio']['change'] < -0.2:
                conclusions.append("近期风险调整收益恶化")
            
            comparison['conclusions'] = conclusions
            
            logger.info(f"杠杆表现比较完成: {len(conclusions)}个结论")
            
            return comparison
            
        except Exception as e:
            logger.error(f"杠杆表现比较失败: {e}")
            return {
                'timestamp': datetime.now().isoformat(),
                'error': str(e)
            }
    
    def get_cached_analysis(self, days: int) -> Optional[LeverageAnalysisResult]:
        """获取缓存的分析结果"""
        cache_key = f"{days}d"
        return self.analysis_cache.get(cache_key)
    
    def clear_cache(self):
        """清空分析缓存"""
        self.analysis_cache.clear()
        logger.info("分析缓存已清空")