"""
杠杆管理系统
整合杠杆控制、监控、分析等功能的综合管理系统
"""

import logging
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
import json

try:
    from .leverage_controller import (
        DynamicLeverageController, 
        LeverageConfig, 
        AccountInfo, 
        LeverageDecision,
        CapitalTier
    )
    from .leverage_monitor import LeverageMonitor, LeverageAlert, LeverageAlertHandler
    from .leverage_analyzer import LeverageAnalyzer, LeverageAnalysisResult
except ImportError:
    from leverage_controller import (
        DynamicLeverageController, 
        LeverageConfig, 
        AccountInfo, 
        LeverageDecision,
        CapitalTier
    )
    from leverage_monitor import LeverageMonitor, LeverageAlert, LeverageAlertHandler
    from leverage_analyzer import LeverageAnalyzer, LeverageAnalysisResult

# 配置日志
logger = logging.getLogger(__name__)

@dataclass
class LeverageSystemConfig:
    """杠杆系统配置"""
    leverage_config: LeverageConfig
    monitoring_interval: int = 30
    auto_adjustment: bool = True
    alert_threshold: float = 2.5
    max_system_leverage: float = 3.0
    enable_analysis: bool = True
    analysis_period: int = 30

class LeverageManagementSystem:
    """杠杆管理系统"""
    
    def __init__(self, config: Optional[LeverageSystemConfig] = None, 
                 db_path: str = "data/leverage_history.db"):
        """
        初始化杠杆管理系统
        
        Args:
            config: 系统配置
            db_path: 数据库路径
        """
        self.config = config or LeverageSystemConfig(LeverageConfig())
        self.db_path = db_path
        
        # 初始化各个组件
        self.leverage_controller = DynamicLeverageController(
            self.config.leverage_config, 
            db_path
        )
        
        self.leverage_monitor = LeverageMonitor(
            self.leverage_controller,
            self.config.monitoring_interval
        )
        
        self.leverage_analyzer = LeverageAnalyzer(db_path)
        
        self.alert_handler = LeverageAlertHandler()
        
        # 系统状态
        self.is_running = False
        self.system_stats = {
            'start_time': None,
            'total_decisions': 0,
            'total_adjustments': 0,
            'total_alerts': 0,
            'last_analysis': None
        }
        
        # 注册预警处理回调
        self.leverage_monitor.add_alert_callback(self._handle_system_alert)
        
        logger.info("杠杆管理系统初始化完成")
    
    def start_system(self):
        """启动杠杆管理系统"""
        if self.is_running:
            logger.warning("杠杆管理系统已在运行")
            return
        
        try:
            self.is_running = True
            self.system_stats['start_time'] = datetime.now()
            
            # 启动监控
            self.leverage_monitor.start_monitoring()
            
            logger.info("杠杆管理系统已启动")
            
        except Exception as e:
            logger.error(f"启动杠杆管理系统失败: {e}")
            self.is_running = False
            raise
    
    def stop_system(self):
        """停止杠杆管理系统"""
        if not self.is_running:
            logger.warning("杠杆管理系统未在运行")
            return
        
        try:
            # 停止监控
            self.leverage_monitor.stop_monitoring()
            
            self.is_running = False
            
            logger.info("杠杆管理系统已停止")
            
        except Exception as e:
            logger.error(f"停止杠杆管理系统失败: {e}")
    
    def calculate_leverage_for_position(self, symbol: str, account: AccountInfo, 
                                      stock_volatility: float, 
                                      market_data: Optional[Dict] = None) -> LeverageDecision:
        """
        为特定持仓计算杠杆
        
        Args:
            symbol: 股票代码
            account: 账户信息
            stock_volatility: 股票波动率
            market_data: 市场数据
            
        Returns:
            LeverageDecision: 杠杆决策
        """
        try:
            logger.info(f"计算杠杆: {symbol}")
            
            # 使用杠杆控制器计算
            decision = self.leverage_controller.calculate_dynamic_leverage(
                account, stock_volatility, market_data
            )
            
            # 系统级别检查
            if decision.recommended_leverage > self.config.max_system_leverage:
                logger.warning(f"杠杆超过系统限制，调整至 {self.config.max_system_leverage}x")
                decision.recommended_leverage = self.config.max_system_leverage
                decision.reason += f"; 系统限制调整至{self.config.max_system_leverage}x"
            
            # 记录决策
            self.system_stats['total_decisions'] += 1
            
            logger.info(f"杠杆计算完成: {symbol} {decision.recommended_leverage}x")
            
            return decision
            
        except Exception as e:
            logger.error(f"计算杠杆失败 {symbol}: {e}")
            # 返回保守杠杆
            return LeverageDecision(
                recommended_leverage=1.0,
                max_allowed_leverage=1.0,
                risk_level="LOW",
                factors={},
                reason=f"计算失败，使用保守杠杆: {e}"
            )
    
    def monitor_portfolio_leverage(self, positions: List[Dict]) -> Dict[str, Any]:
        """
        监控组合杠杆
        
        Args:
            positions: 持仓列表
            
        Returns:
            Dict: 监控结果
        """
        try:
            logger.info(f"监控组合杠杆: {len(positions)}个持仓")
            
            # 使用监控器进行监控
            monitoring_result = self.leverage_controller.monitor_leverage_usage(positions)
            
            # 检查是否需要调整
            if monitoring_result.get('risk_warnings'):
                logger.warning(f"发现风险警告: {monitoring_result['risk_warnings']}")
                
                # 如果启用自动调整
                if self.config.auto_adjustment:
                    self._auto_adjust_portfolio(positions, monitoring_result)
            
            return monitoring_result
            
        except Exception as e:
            logger.error(f"监控组合杠杆失败: {e}")
            return {
                'timestamp': datetime.now().isoformat(),
                'error': str(e)
            }
    
    def _auto_adjust_portfolio(self, positions: List[Dict], monitoring_result: Dict):
        """自动调整组合杠杆"""
        try:
            logger.info("执行自动杠杆调整")
            
            overall_leverage = monitoring_result.get('overall_leverage', 1.0)
            
            if overall_leverage > self.config.alert_threshold:
                # 计算调整比例
                adjustment_ratio = self.config.alert_threshold / overall_leverage
                
                for position in positions:
                    symbol = position.get('symbol', '')
                    current_leverage = position.get('leverage', 1.0)
                    
                    if current_leverage > 1.5:  # 只调整高杠杆持仓
                        new_leverage = current_leverage * adjustment_ratio
                        new_leverage = max(new_leverage, 1.0)  # 不低于1倍
                        
                        logger.info(f"调整杠杆: {symbol} {current_leverage}x -> {new_leverage}x")
                        
                        # 这里应该调用实际的持仓调整接口
                        # 为了演示，我们只记录调整动作
                        self._record_adjustment(symbol, current_leverage, new_leverage, "auto_portfolio_adjustment")
            
            self.system_stats['total_adjustments'] += 1
            
        except Exception as e:
            logger.error(f"自动调整组合杠杆失败: {e}")
    
    def _record_adjustment(self, symbol: str, from_leverage: float, 
                          to_leverage: float, reason: str):
        """记录杠杆调整"""
        try:
            adjustment_record = {
                'timestamp': datetime.now().isoformat(),
                'symbol': symbol,
                'from_leverage': from_leverage,
                'to_leverage': to_leverage,
                'reason': reason
            }
            
            logger.info(f"杠杆调整记录: {adjustment_record}")
            
            # 这里可以保存到数据库或文件
            
        except Exception as e:
            logger.error(f"记录杠杆调整失败: {e}")
    
    def _handle_system_alert(self, alert: LeverageAlert):
        """处理系统预警"""
        try:
            logger.info(f"处理系统预警: {alert.alert_type} - {alert.message}")
            
            # 使用预警处理器处理
            self.alert_handler.handle_alert(alert)
            
            # 更新统计
            self.system_stats['total_alerts'] += 1
            
            # 如果是严重预警，可能需要额外处理
            if alert.alert_type in ['CRITICAL', 'VIOLATION']:
                self._handle_critical_alert(alert)
            
        except Exception as e:
            logger.error(f"处理系统预警失败: {e}")
    
    def _handle_critical_alert(self, alert: LeverageAlert):
        """处理严重预警"""
        try:
            logger.critical(f"处理严重预警: {alert.symbol} - {alert.message}")
            
            # 严重预警的特殊处理逻辑
            if alert.alert_type == 'VIOLATION':
                # 违规情况下强制调整
                if self.config.auto_adjustment:
                    target_leverage = alert.limit_leverage * 0.9
                    self._record_adjustment(
                        alert.symbol, 
                        alert.current_leverage, 
                        target_leverage,
                        "critical_violation_adjustment"
                    )
            
        except Exception as e:
            logger.error(f"处理严重预警失败: {e}")
    
    def analyze_leverage_performance(self, days: int = 30) -> LeverageAnalysisResult:
        """
        分析杠杆表现
        
        Args:
            days: 分析天数
            
        Returns:
            LeverageAnalysisResult: 分析结果
        """
        try:
            logger.info(f"分析杠杆表现: {days}天")
            
            # 使用分析器进行分析
            analysis_result = self.leverage_analyzer.comprehensive_analysis(days)
            
            # 更新系统统计
            self.system_stats['last_analysis'] = datetime.now()
            
            logger.info(f"杠杆表现分析完成: 胜率{analysis_result.win_rate:.1%}")
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"分析杠杆表现失败: {e}")
            return self.leverage_analyzer._create_empty_result(days, str(e))
    
    def generate_system_report(self) -> Dict[str, Any]:
        """生成系统报告"""
        try:
            # 获取各组件状态
            controller_status = {
                'config': {
                    'max_leverage': self.config.leverage_config.max_leverage,
                    'min_leverage': self.config.leverage_config.min_leverage,
                    'auto_adjustment': self.config.auto_adjustment
                }
            }
            
            monitor_status = self.leverage_monitor.get_monitoring_status()
            
            # 获取最近分析结果
            recent_analysis = None
            if self.config.enable_analysis:
                try:
                    recent_analysis = self.analyze_leverage_performance(7)  # 最近7天
                except:
                    pass
            
            system_report = {
                'timestamp': datetime.now().isoformat(),
                'system_status': {
                    'is_running': self.is_running,
                    'uptime_seconds': (datetime.now() - self.system_stats['start_time']).total_seconds() 
                                    if self.system_stats['start_time'] else 0
                },
                'system_stats': self.system_stats,
                'controller_status': controller_status,
                'monitor_status': monitor_status,
                'recent_analysis': {
                    'total_trades': recent_analysis.total_trades if recent_analysis else 0,
                    'win_rate': recent_analysis.win_rate if recent_analysis else 0,
                    'avg_leverage': recent_analysis.avg_leverage if recent_analysis else 1.0
                } if recent_analysis else None
            }
            
            return system_report
            
        except Exception as e:
            logger.error(f"生成系统报告失败: {e}")
            return {
                'timestamp': datetime.now().isoformat(),
                'error': str(e)
            }
    
    def optimize_system_parameters(self) -> Dict[str, Any]:
        """优化系统参数"""
        try:
            logger.info("开始系统参数优化")
            
            # 分析历史表现
            analysis_result = self.analyze_leverage_performance(60)  # 分析60天
            
            optimization_suggestions = {}
            
            # 基于分析结果优化参数
            if analysis_result.win_rate < 0.5:
                optimization_suggestions['max_leverage'] = min(
                    self.config.leverage_config.max_leverage * 0.8, 2.5
                )
                optimization_suggestions['reason'] = "胜率偏低，建议降低最大杠杆"
            
            elif analysis_result.win_rate > 0.7 and analysis_result.max_drawdown < 0.15:
                optimization_suggestions['max_leverage'] = min(
                    self.config.leverage_config.max_leverage * 1.1, 3.5
                )
                optimization_suggestions['reason'] = "表现良好，可适度提升杠杆上限"
            
            # 监控间隔优化
            if self.system_stats['total_alerts'] > 100:  # 预警过多
                optimization_suggestions['monitoring_interval'] = max(
                    self.config.monitoring_interval * 1.5, 60
                )
                optimization_suggestions['monitoring_reason'] = "预警频繁，建议延长监控间隔"
            
            optimization_result = {
                'timestamp': datetime.now().isoformat(),
                'current_parameters': {
                    'max_leverage': self.config.leverage_config.max_leverage,
                    'monitoring_interval': self.config.monitoring_interval,
                    'auto_adjustment': self.config.auto_adjustment
                },
                'optimization_suggestions': optimization_suggestions,
                'analysis_basis': {
                    'win_rate': analysis_result.win_rate,
                    'max_drawdown': analysis_result.max_drawdown,
                    'total_alerts': self.system_stats['total_alerts']
                }
            }
            
            logger.info("系统参数优化完成")
            
            return optimization_result
            
        except Exception as e:
            logger.error(f"系统参数优化失败: {e}")
            return {
                'timestamp': datetime.now().isoformat(),
                'error': str(e)
            }
    
    def update_system_config(self, new_config: Dict[str, Any]):
        """更新系统配置"""
        try:
            logger.info("更新系统配置")
            
            # 更新杠杆配置
            if 'max_leverage' in new_config:
                self.config.leverage_config.max_leverage = new_config['max_leverage']
                logger.info(f"更新最大杠杆: {new_config['max_leverage']}")
            
            if 'min_leverage' in new_config:
                self.config.leverage_config.min_leverage = new_config['min_leverage']
                logger.info(f"更新最小杠杆: {new_config['min_leverage']}")
            
            # 更新监控配置
            if 'monitoring_interval' in new_config:
                self.config.monitoring_interval = new_config['monitoring_interval']
                self.leverage_monitor.set_monitoring_interval(new_config['monitoring_interval'])
                logger.info(f"更新监控间隔: {new_config['monitoring_interval']}秒")
            
            # 更新自动调整配置
            if 'auto_adjustment' in new_config:
                self.config.auto_adjustment = new_config['auto_adjustment']
                logger.info(f"更新自动调整: {new_config['auto_adjustment']}")
            
            logger.info("系统配置更新完成")
            
        except Exception as e:
            logger.error(f"更新系统配置失败: {e}")
            raise
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            'is_running': self.is_running,
            'system_stats': self.system_stats,
            'config': {
                'max_leverage': self.config.leverage_config.max_leverage,
                'monitoring_interval': self.config.monitoring_interval,
                'auto_adjustment': self.config.auto_adjustment
            },
            'components': {
                'controller': 'active',
                'monitor': 'active' if self.leverage_monitor.is_monitoring else 'inactive',
                'analyzer': 'active'
            }
        }
    
    def force_system_check(self) -> Dict[str, Any]:
        """强制系统检查"""
        try:
            logger.info("执行强制系统检查")
            
            # 强制杠杆检查
            leverage_check = self.leverage_monitor.force_leverage_check()
            
            # 获取系统统计
            system_stats = self.get_system_status()
            
            # 检查系统健康状态
            health_check = {
                'database_accessible': self._check_database_health(),
                'monitoring_active': self.leverage_monitor.is_monitoring,
                'recent_errors': self._get_recent_errors()
            }
            
            check_result = {
                'timestamp': datetime.now().isoformat(),
                'leverage_check': leverage_check,
                'system_stats': system_stats,
                'health_check': health_check,
                'overall_status': 'HEALTHY' if all(health_check.values()) else 'WARNING'
            }
            
            logger.info(f"强制系统检查完成: {check_result['overall_status']}")
            
            return check_result
            
        except Exception as e:
            logger.error(f"强制系统检查失败: {e}")
            return {
                'timestamp': datetime.now().isoformat(),
                'error': str(e),
                'overall_status': 'ERROR'
            }
    
    def _check_database_health(self) -> bool:
        """检查数据库健康状态"""
        try:
            import sqlite3
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM leverage_history LIMIT 1")
            conn.close()
            return True
        except:
            return False
    
    def _get_recent_errors(self) -> List[str]:
        """获取最近的错误（简化实现）"""
        # 这里应该从日志系统获取最近的错误
        # 为了演示，返回空列表
        return []