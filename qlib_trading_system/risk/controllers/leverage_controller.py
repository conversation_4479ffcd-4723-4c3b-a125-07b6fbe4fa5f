"""
动态杠杆控制系统
实现基于多因子的杠杆计算、监控、风险评估和历史分析
"""

import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from enum import Enum
import json
import sqlite3
from pathlib import Path

# 配置日志
logger = logging.getLogger(__name__)

class CapitalTier(Enum):
    """资金规模等级"""
    SMALL = "small"      # <50万
    MEDIUM = "medium"    # 50-500万
    LARGE = "large"      # >500万

class MarketTrend(Enum):
    """市场趋势"""
    BULL = "bull"        # 牛市
    BEAR = "bear"        # 熊市
    SIDEWAYS = "sideways" # 震荡市

@dataclass
class LeverageConfig:
    """杠杆配置"""
    base_leverage_map: Dict[str, float] = field(default_factory=lambda: {
        'small': 1.5,    # 小资金基础杠杆
        'medium': 1.3,   # 中等资金基础杠杆
        'large': 1.1     # 大资金基础杠杆
    })
    max_leverage: float = 3.0           # 最大杠杆倍数
    min_leverage: float = 1.0           # 最小杠杆倍数
    volatility_threshold: float = 0.3   # 波动率阈值
    drawdown_threshold: float = 0.3     # 回撤阈值
    win_rate_threshold: float = 0.5     # 胜率阈值
    
@dataclass
class AccountInfo:
    """账户信息"""
    total_capital: float                # 总资金
    available_capital: float            # 可用资金
    used_capital: float                 # 已用资金
    current_drawdown: float             # 当前回撤
    recent_win_rate: float              # 近期胜率
    capital_tier: CapitalTier           # 资金等级
    leverage_usage: float = 0.0         # 当前杠杆使用率
    
@dataclass
class LeverageDecision:
    """杠杆决策"""
    recommended_leverage: float         # 推荐杠杆倍数
    max_allowed_leverage: float         # 最大允许杠杆
    risk_level: str                     # 风险等级
    factors: Dict[str, float]           # 各因子贡献
    reason: str                         # 决策原因
    timestamp: datetime = field(default_factory=datetime.now)

@dataclass
class LeverageRecord:
    """杠杆使用记录"""
    timestamp: datetime
    symbol: str
    leverage_ratio: float
    position_size: float
    market_value: float
    risk_level: str
    pnl: float = 0.0
    
class DynamicLeverageController:
    """动态杠杆控制系统"""
    
    def __init__(self, config: Optional[LeverageConfig] = None, db_path: str = "data/leverage_history.db"):
        """
        初始化动态杠杆控制器
        
        Args:
            config: 杠杆配置
            db_path: 数据库路径
        """
        self.config = config or LeverageConfig()
        self.db_path = db_path
        self.leverage_history: List[LeverageRecord] = []
        self.current_market_trend = MarketTrend.SIDEWAYS
        
        # 初始化数据库
        self._init_database()
        
        logger.info("动态杠杆控制系统初始化完成")
    
    def _init_database(self):
        """初始化数据库"""
        try:
            # 确保数据目录存在
            Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建杠杆历史表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS leverage_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    symbol TEXT NOT NULL,
                    leverage_ratio REAL NOT NULL,
                    position_size REAL NOT NULL,
                    market_value REAL NOT NULL,
                    risk_level TEXT NOT NULL,
                    pnl REAL DEFAULT 0.0,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建杠杆决策表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS leverage_decisions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    recommended_leverage REAL NOT NULL,
                    max_allowed_leverage REAL NOT NULL,
                    risk_level TEXT NOT NULL,
                    factors TEXT NOT NULL,
                    reason TEXT NOT NULL,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            conn.close()
            
            logger.info(f"杠杆控制数据库初始化完成: {self.db_path}")
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise
    
    def calculate_dynamic_leverage(self, account: AccountInfo, stock_volatility: float, 
                                 market_data: Optional[Dict] = None) -> LeverageDecision:
        """
        动态计算杠杆倍数
        
        Args:
            account: 账户信息
            stock_volatility: 股票波动率
            market_data: 市场数据
            
        Returns:
            LeverageDecision: 杠杆决策
        """
        try:
            # 基础杠杆系数
            base_leverage = self.config.base_leverage_map.get(account.capital_tier.value, 1.0)
            
            # 计算各因子
            factors = {}
            
            # 1. 波动率因子（波动越大杠杆越高，追求高收益）
            volatility_factor = 1 + min(stock_volatility * 2, 0.8)
            factors['volatility'] = volatility_factor
            
            # 2. 账户风险因子
            risk_factor = max(1 - (account.current_drawdown / self.config.drawdown_threshold), 0.5)
            factors['risk'] = risk_factor
            
            # 3. 市场环境因子
            market_factor = self._get_market_environment_factor(market_data)
            factors['market'] = market_factor
            
            # 4. 胜率因子
            win_rate_factor = 1 + (account.recent_win_rate - self.config.win_rate_threshold) * 0.5
            factors['win_rate'] = win_rate_factor
            
            # 5. 资金使用率因子
            capital_usage_factor = 1 - (account.used_capital / account.total_capital) * 0.2
            factors['capital_usage'] = capital_usage_factor
            
            # 计算最终杠杆
            final_leverage = (base_leverage * volatility_factor * risk_factor * 
                            market_factor * win_rate_factor * capital_usage_factor)
            
            # 限制杠杆范围
            recommended_leverage = round(
                min(max(final_leverage, self.config.min_leverage), self.config.max_leverage), 2
            )
            
            # 计算最大允许杠杆（考虑风险限制）
            max_allowed_leverage = min(recommended_leverage * 1.2, self.config.max_leverage)
            
            # 确定风险等级
            risk_level = self._determine_risk_level(recommended_leverage, account)
            
            # 生成决策原因
            reason = self._generate_leverage_reason(factors, recommended_leverage, risk_level)
            
            decision = LeverageDecision(
                recommended_leverage=recommended_leverage,
                max_allowed_leverage=max_allowed_leverage,
                risk_level=risk_level,
                factors=factors,
                reason=reason
            )
            
            # 保存决策记录
            self._save_leverage_decision(decision)
            
            logger.info(f"动态杠杆计算完成: {recommended_leverage}x, 风险等级: {risk_level}")
            
            return decision
            
        except Exception as e:
            logger.error(f"动态杠杆计算失败: {e}")
            # 返回保守杠杆
            return LeverageDecision(
                recommended_leverage=1.0,
                max_allowed_leverage=1.0,
                risk_level="LOW",
                factors={},
                reason=f"计算失败，使用保守杠杆: {e}"
            )
    
    def _get_market_environment_factor(self, market_data: Optional[Dict] = None) -> float:
        """获取市场环境因子"""
        if not market_data:
            return 1.0
        
        try:
            # 分析市场趋势
            market_trend = self._analyze_market_trend(market_data)
            self.current_market_trend = market_trend
            
            if market_trend == MarketTrend.BULL:
                return 1.2  # 牛市提高杠杆
            elif market_trend == MarketTrend.BEAR:
                return 0.8  # 熊市降低杠杆
            else:
                return 1.0  # 震荡市保持中性
                
        except Exception as e:
            logger.warning(f"市场环境分析失败: {e}")
            return 1.0
    
    def _analyze_market_trend(self, market_data: Dict) -> MarketTrend:
        """分析市场趋势"""
        try:
            # 简化的趋势判断逻辑
            index_change = market_data.get('index_change_5d', 0)
            volume_ratio = market_data.get('volume_ratio', 1.0)
            
            if index_change > 5 and volume_ratio > 1.2:
                return MarketTrend.BULL
            elif index_change < -5 and volume_ratio > 1.1:
                return MarketTrend.BEAR
            else:
                return MarketTrend.SIDEWAYS
                
        except Exception as e:
            logger.warning(f"市场趋势分析失败: {e}")
            return MarketTrend.SIDEWAYS
    
    def _determine_risk_level(self, leverage: float, account: AccountInfo) -> str:
        """确定风险等级"""
        if leverage >= 2.5 or account.current_drawdown > 0.2:
            return "HIGH"
        elif leverage >= 1.8 or account.current_drawdown > 0.1:
            return "MEDIUM"
        else:
            return "LOW"
    
    def _generate_leverage_reason(self, factors: Dict[str, float], leverage: float, risk_level: str) -> str:
        """生成杠杆决策原因"""
        reason_parts = [f"推荐杠杆: {leverage}x ({risk_level}风险)"]
        
        for factor_name, factor_value in factors.items():
            if factor_name == 'volatility' and factor_value > 1.3:
                reason_parts.append("高波动率支持提升杠杆")
            elif factor_name == 'risk' and factor_value < 0.8:
                reason_parts.append("账户回撤较大，降低杠杆")
            elif factor_name == 'market' and factor_value > 1.1:
                reason_parts.append("市场环境良好，适度提升杠杆")
            elif factor_name == 'win_rate' and factor_value > 1.2:
                reason_parts.append("胜率较高，支持提升杠杆")
        
        return "; ".join(reason_parts)
    
    def monitor_leverage_usage(self, positions: List[Dict]) -> Dict[str, Any]:
        """
        监控杠杆使用情况
        
        Args:
            positions: 持仓列表
            
        Returns:
            Dict: 监控结果
        """
        try:
            total_market_value = 0
            total_leverage_exposure = 0
            position_details = []
            
            for position in positions:
                symbol = position.get('symbol', '')
                shares = position.get('shares', 0)
                price = position.get('current_price', 0)
                leverage = position.get('leverage', 1.0)
                
                market_value = shares * price
                leverage_exposure = market_value * leverage
                
                total_market_value += market_value
                total_leverage_exposure += leverage_exposure
                
                position_details.append({
                    'symbol': symbol,
                    'market_value': market_value,
                    'leverage': leverage,
                    'leverage_exposure': leverage_exposure
                })
            
            # 计算整体杠杆率
            overall_leverage = total_leverage_exposure / total_market_value if total_market_value > 0 else 1.0
            
            # 风险评估
            risk_warnings = []
            if overall_leverage > 2.5:
                risk_warnings.append("整体杠杆过高")
            
            # 检查单个持仓杠杆
            for detail in position_details:
                if detail['leverage'] > 3.0:
                    risk_warnings.append(f"{detail['symbol']} 杠杆过高: {detail['leverage']}x")
            
            monitoring_result = {
                'timestamp': datetime.now().isoformat(),
                'total_market_value': total_market_value,
                'total_leverage_exposure': total_leverage_exposure,
                'overall_leverage': round(overall_leverage, 2),
                'position_count': len(positions),
                'position_details': position_details,
                'risk_warnings': risk_warnings,
                'risk_level': 'HIGH' if risk_warnings else 'NORMAL'
            }
            
            logger.info(f"杠杆监控完成: 整体杠杆 {overall_leverage:.2f}x, 风险等级 {monitoring_result['risk_level']}")
            
            return monitoring_result
            
        except Exception as e:
            logger.error(f"杠杆监控失败: {e}")
            return {
                'timestamp': datetime.now().isoformat(),
                'error': str(e),
                'risk_level': 'ERROR'
            }
    
    def assess_leverage_risk(self, leverage_decision: LeverageDecision, 
                           account: AccountInfo, market_conditions: Dict) -> Dict[str, Any]:
        """
        评估杠杆风险
        
        Args:
            leverage_decision: 杠杆决策
            account: 账户信息
            market_conditions: 市场条件
            
        Returns:
            Dict: 风险评估结果
        """
        try:
            risk_metrics = {}
            
            # 1. 杠杆风险评分
            leverage_risk_score = min(leverage_decision.recommended_leverage / self.config.max_leverage, 1.0)
            risk_metrics['leverage_risk_score'] = leverage_risk_score
            
            # 2. 账户风险评分
            account_risk_score = account.current_drawdown / self.config.drawdown_threshold
            risk_metrics['account_risk_score'] = account_risk_score
            
            # 3. 市场风险评分
            market_volatility = market_conditions.get('volatility', 0.2)
            market_risk_score = min(market_volatility / self.config.volatility_threshold, 1.0)
            risk_metrics['market_risk_score'] = market_risk_score
            
            # 4. 流动性风险评分
            liquidity_score = market_conditions.get('liquidity_score', 0.8)
            liquidity_risk_score = 1 - liquidity_score
            risk_metrics['liquidity_risk_score'] = liquidity_risk_score
            
            # 综合风险评分
            overall_risk_score = (
                leverage_risk_score * 0.3 +
                account_risk_score * 0.3 +
                market_risk_score * 0.2 +
                liquidity_risk_score * 0.2
            )
            risk_metrics['overall_risk_score'] = overall_risk_score
            
            # 风险等级
            if overall_risk_score > 0.7:
                risk_level = "HIGH"
                recommendations = ["建议降低杠杆", "增加风险监控频率", "准备应急预案"]
            elif overall_risk_score > 0.4:
                risk_level = "MEDIUM"
                recommendations = ["适度控制杠杆", "密切关注市场变化"]
            else:
                risk_level = "LOW"
                recommendations = ["当前风险可控", "可适度使用杠杆"]
            
            # 计算最大可承受损失
            max_acceptable_loss = account.total_capital * 0.1  # 10%最大损失
            leverage_adjusted_loss = max_acceptable_loss / leverage_decision.recommended_leverage
            
            risk_assessment = {
                'timestamp': datetime.now().isoformat(),
                'risk_level': risk_level,
                'overall_risk_score': round(overall_risk_score, 3),
                'risk_metrics': risk_metrics,
                'recommendations': recommendations,
                'max_acceptable_loss': max_acceptable_loss,
                'leverage_adjusted_loss': leverage_adjusted_loss,
                'should_reduce_leverage': overall_risk_score > 0.6
            }
            
            logger.info(f"杠杆风险评估完成: {risk_level} 风险, 评分 {overall_risk_score:.3f}")
            
            return risk_assessment
            
        except Exception as e:
            logger.error(f"杠杆风险评估失败: {e}")
            return {
                'timestamp': datetime.now().isoformat(),
                'risk_level': 'ERROR',
                'error': str(e)
            }
    
    def adjust_leverage(self, current_leverage: float, risk_assessment: Dict, 
                       market_change: float = 0.0) -> Tuple[float, str]:
        """
        调整杠杆倍数
        
        Args:
            current_leverage: 当前杠杆
            risk_assessment: 风险评估结果
            market_change: 市场变化幅度
            
        Returns:
            Tuple[float, str]: (调整后杠杆, 调整原因)
        """
        try:
            adjustment_reason = []
            new_leverage = current_leverage
            
            # 基于风险评估调整
            risk_level = risk_assessment.get('risk_level', 'MEDIUM')
            if risk_level == 'HIGH':
                new_leverage *= 0.7  # 高风险降低30%
                adjustment_reason.append("高风险环境降杠杆")
            elif risk_level == 'LOW':
                new_leverage *= 1.1  # 低风险适度提升
                adjustment_reason.append("低风险环境提升杠杆")
            
            # 基于市场变化调整
            if market_change < -5:  # 市场大跌
                new_leverage *= 0.8
                adjustment_reason.append("市场下跌降杠杆")
            elif market_change > 5:  # 市场大涨
                new_leverage *= 1.05
                adjustment_reason.append("市场上涨微调杠杆")
            
            # 限制杠杆范围
            new_leverage = min(max(new_leverage, self.config.min_leverage), self.config.max_leverage)
            new_leverage = round(new_leverage, 2)
            
            # 如果变化很小，保持不变
            if abs(new_leverage - current_leverage) < 0.05:
                return current_leverage, "杠杆无需调整"
            
            reason = f"杠杆从 {current_leverage}x 调整至 {new_leverage}x: " + "; ".join(adjustment_reason)
            
            logger.info(f"杠杆调整: {current_leverage}x -> {new_leverage}x")
            
            return new_leverage, reason
            
        except Exception as e:
            logger.error(f"杠杆调整失败: {e}")
            return current_leverage, f"调整失败: {e}"
    
    def record_leverage_usage(self, symbol: str, leverage_ratio: float, 
                            position_size: float, market_value: float, 
                            risk_level: str, pnl: float = 0.0):
        """
        记录杠杆使用情况
        
        Args:
            symbol: 股票代码
            leverage_ratio: 杠杆比率
            position_size: 持仓数量
            market_value: 市值
            risk_level: 风险等级
            pnl: 盈亏
        """
        try:
            record = LeverageRecord(
                timestamp=datetime.now(),
                symbol=symbol,
                leverage_ratio=leverage_ratio,
                position_size=position_size,
                market_value=market_value,
                risk_level=risk_level,
                pnl=pnl
            )
            
            self.leverage_history.append(record)
            
            # 保存到数据库
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO leverage_history 
                (timestamp, symbol, leverage_ratio, position_size, market_value, risk_level, pnl)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                record.timestamp.isoformat(),
                record.symbol,
                record.leverage_ratio,
                record.position_size,
                record.market_value,
                record.risk_level,
                record.pnl
            ))
            
            conn.commit()
            conn.close()
            
            logger.info(f"杠杆使用记录已保存: {symbol} {leverage_ratio}x")
            
        except Exception as e:
            logger.error(f"记录杠杆使用失败: {e}")
    
    def _save_leverage_decision(self, decision: LeverageDecision):
        """保存杠杆决策记录"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO leverage_decisions 
                (timestamp, recommended_leverage, max_allowed_leverage, risk_level, factors, reason)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                decision.timestamp.isoformat(),
                decision.recommended_leverage,
                decision.max_allowed_leverage,
                decision.risk_level,
                json.dumps(decision.factors),
                decision.reason
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"保存杠杆决策失败: {e}")
    
    def analyze_leverage_performance(self, days: int = 30) -> Dict[str, Any]:
        """
        分析杠杆使用历史表现
        
        Args:
            days: 分析天数
            
        Returns:
            Dict: 分析结果
        """
        try:
            # 从数据库获取历史数据
            conn = sqlite3.connect(self.db_path)
            
            # 获取杠杆使用历史
            start_date = (datetime.now() - timedelta(days=days)).isoformat()
            
            leverage_df = pd.read_sql_query('''
                SELECT * FROM leverage_history 
                WHERE timestamp >= ? 
                ORDER BY timestamp DESC
            ''', conn, params=[start_date])
            
            # 获取杠杆决策历史
            decisions_df = pd.read_sql_query('''
                SELECT * FROM leverage_decisions 
                WHERE timestamp >= ? 
                ORDER BY timestamp DESC
            ''', conn, params=[start_date])
            
            conn.close()
            
            if leverage_df.empty:
                return {
                    'period': f'{days}天',
                    'message': '暂无杠杆使用历史数据'
                }
            
            # 计算统计指标
            total_trades = len(leverage_df)
            avg_leverage = leverage_df['leverage_ratio'].mean()
            max_leverage = leverage_df['leverage_ratio'].max()
            min_leverage = leverage_df['leverage_ratio'].min()
            
            # 盈亏分析
            total_pnl = leverage_df['pnl'].sum()
            profitable_trades = len(leverage_df[leverage_df['pnl'] > 0])
            win_rate = profitable_trades / total_trades if total_trades > 0 else 0
            
            # 风险分析
            high_risk_trades = len(leverage_df[leverage_df['risk_level'] == 'HIGH'])
            high_risk_ratio = high_risk_trades / total_trades if total_trades > 0 else 0
            
            # 杠杆效率分析
            leverage_efficiency = {}
            for leverage_level in [1.0, 1.5, 2.0, 2.5, 3.0]:
                level_trades = leverage_df[
                    (leverage_df['leverage_ratio'] >= leverage_level - 0.25) & 
                    (leverage_df['leverage_ratio'] < leverage_level + 0.25)
                ]
                if not level_trades.empty:
                    level_pnl = level_trades['pnl'].sum()
                    level_count = len(level_trades)
                    level_win_rate = len(level_trades[level_trades['pnl'] > 0]) / level_count
                    leverage_efficiency[f'{leverage_level}x'] = {
                        'trades': level_count,
                        'total_pnl': level_pnl,
                        'avg_pnl': level_pnl / level_count,
                        'win_rate': level_win_rate
                    }
            
            # 时间序列分析
            leverage_df['date'] = pd.to_datetime(leverage_df['timestamp']).dt.date
            daily_stats = leverage_df.groupby('date').agg({
                'leverage_ratio': 'mean',
                'pnl': 'sum',
                'market_value': 'sum'
            }).reset_index()
            
            analysis_result = {
                'analysis_period': f'{days}天',
                'timestamp': datetime.now().isoformat(),
                'summary': {
                    'total_trades': total_trades,
                    'avg_leverage': round(avg_leverage, 2),
                    'max_leverage': max_leverage,
                    'min_leverage': min_leverage,
                    'total_pnl': round(total_pnl, 2),
                    'win_rate': round(win_rate, 3),
                    'high_risk_ratio': round(high_risk_ratio, 3)
                },
                'leverage_efficiency': leverage_efficiency,
                'daily_stats': daily_stats.to_dict('records'),
                'recommendations': self._generate_performance_recommendations(
                    avg_leverage, win_rate, high_risk_ratio, leverage_efficiency
                )
            }
            
            logger.info(f"杠杆历史分析完成: {days}天, {total_trades}笔交易, 胜率{win_rate:.1%}")
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"杠杆历史分析失败: {e}")
            return {
                'analysis_period': f'{days}天',
                'timestamp': datetime.now().isoformat(),
                'error': str(e)
            }
    
    def _generate_performance_recommendations(self, avg_leverage: float, win_rate: float, 
                                           high_risk_ratio: float, efficiency_data: Dict) -> List[str]:
        """生成性能优化建议"""
        recommendations = []
        
        if avg_leverage > 2.0:
            recommendations.append("平均杠杆较高，建议适度降低以控制风险")
        
        if win_rate < 0.5:
            recommendations.append("胜率偏低，建议优化选股策略或降低杠杆")
        
        if high_risk_ratio > 0.3:
            recommendations.append("高风险交易比例过高，建议加强风险控制")
        
        # 分析最优杠杆水平
        best_leverage = None
        best_efficiency = -float('inf')
        
        for level, data in efficiency_data.items():
            if data['trades'] >= 5:  # 至少5笔交易才有参考价值
                efficiency = data['avg_pnl'] * data['win_rate']
                if efficiency > best_efficiency:
                    best_efficiency = efficiency
                    best_leverage = level
        
        if best_leverage:
            recommendations.append(f"历史数据显示 {best_leverage} 杠杆效率最高，建议重点使用")
        
        return recommendations
    
    def optimize_leverage_strategy(self, historical_data: Dict) -> Dict[str, Any]:
        """
        优化杠杆策略
        
        Args:
            historical_data: 历史数据
            
        Returns:
            Dict: 优化建议
        """
        try:
            optimization_result = {
                'timestamp': datetime.now().isoformat(),
                'current_strategy': 'dynamic_multi_factor',
                'optimizations': []
            }
            
            # 分析历史表现
            performance_analysis = self.analyze_leverage_performance(30)
            
            if 'summary' in performance_analysis:
                summary = performance_analysis['summary']
                
                # 优化建议1: 杠杆水平调整
                if summary['avg_leverage'] > 2.5 and summary['win_rate'] < 0.6:
                    optimization_result['optimizations'].append({
                        'type': 'leverage_reduction',
                        'suggestion': '降低基础杠杆倍数',
                        'reason': '高杠杆但胜率不足',
                        'action': '将基础杠杆从当前水平降低20%'
                    })
                
                # 优化建议2: 风险控制强化
                if summary['high_risk_ratio'] > 0.4:
                    optimization_result['optimizations'].append({
                        'type': 'risk_control',
                        'suggestion': '强化风险控制机制',
                        'reason': '高风险交易比例过高',
                        'action': '提高风险评估阈值，降低高风险交易频率'
                    })
                
                # 优化建议3: 市场环境适应
                optimization_result['optimizations'].append({
                    'type': 'market_adaptation',
                    'suggestion': '增强市场环境识别',
                    'reason': '提高不同市场环境下的适应性',
                    'action': '优化市场趋势判断算法，动态调整杠杆策略'
                })
            
            # 参数优化建议
            param_optimizations = {
                'volatility_weight': '建议根据历史表现调整波动率因子权重',
                'risk_threshold': '建议根据实际风险承受能力调整风险阈值',
                'market_factor': '建议增强市场环境因子的敏感度'
            }
            
            optimization_result['parameter_suggestions'] = param_optimizations
            
            logger.info("杠杆策略优化分析完成")
            
            return optimization_result
            
        except Exception as e:
            logger.error(f"杠杆策略优化失败: {e}")
            return {
                'timestamp': datetime.now().isoformat(),
                'error': str(e)
            }
    
    def get_leverage_limits(self, account: AccountInfo) -> Dict[str, float]:
        """
        获取杠杆限制
        
        Args:
            account: 账户信息
            
        Returns:
            Dict: 杠杆限制
        """
        try:
            # 基于账户等级的基础限制
            base_limits = {
                CapitalTier.SMALL: {'max': 3.0, 'warning': 2.5, 'normal': 2.0},
                CapitalTier.MEDIUM: {'max': 2.5, 'warning': 2.0, 'normal': 1.5},
                CapitalTier.LARGE: {'max': 2.0, 'warning': 1.5, 'normal': 1.2}
            }
            
            limits = base_limits.get(account.capital_tier, base_limits[CapitalTier.MEDIUM])
            
            # 基于当前风险状况调整
            if account.current_drawdown > 0.15:
                limits = {k: v * 0.8 for k, v in limits.items()}
            elif account.recent_win_rate > 0.7:
                limits = {k: v * 1.1 for k, v in limits.items()}
            
            # 确保不超过系统最大限制
            for key in limits:
                limits[key] = min(limits[key], self.config.max_leverage)
            
            return limits
            
        except Exception as e:
            logger.error(f"获取杠杆限制失败: {e}")
            return {'max': 1.0, 'warning': 1.0, 'normal': 1.0}
    
    def check_leverage_compliance(self, current_leverage: float, account: AccountInfo) -> Dict[str, Any]:
        """
        检查杠杆合规性
        
        Args:
            current_leverage: 当前杠杆
            account: 账户信息
            
        Returns:
            Dict: 合规检查结果
        """
        try:
            limits = self.get_leverage_limits(account)
            
            compliance_result = {
                'timestamp': datetime.now().isoformat(),
                'current_leverage': current_leverage,
                'limits': limits,
                'is_compliant': True,
                'warnings': [],
                'violations': []
            }
            
            # 检查违规
            if current_leverage > limits['max']:
                compliance_result['is_compliant'] = False
                compliance_result['violations'].append(
                    f"杠杆 {current_leverage}x 超过最大限制 {limits['max']}x"
                )
            
            # 检查警告
            elif current_leverage > limits['warning']:
                compliance_result['warnings'].append(
                    f"杠杆 {current_leverage}x 超过警告线 {limits['warning']}x"
                )
            
            # 建议操作
            if not compliance_result['is_compliant']:
                compliance_result['required_action'] = f"必须将杠杆降至 {limits['max']}x 以下"
            elif compliance_result['warnings']:
                compliance_result['suggested_action'] = f"建议将杠杆控制在 {limits['normal']}x 附近"
            
            return compliance_result
            
        except Exception as e:
            logger.error(f"杠杆合规检查失败: {e}")
            return {
                'timestamp': datetime.now().isoformat(),
                'error': str(e),
                'is_compliant': False
            }