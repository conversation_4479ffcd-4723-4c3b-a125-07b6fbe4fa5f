# -*- coding: utf-8 -*-
"""
调试API测试

专门测试失败的API调用
"""

import sys
import time
import requests
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def debug_api_calls():
    """调试API调用"""
    print("开始调试API调用...")
    
    # 启动Web服务器
    try:
        from qlib_trading_system.web.app import app
        import threading
        import uvicorn
        
        def run_server():
            uvicorn.run(app, host="127.0.0.1", port=8002, log_level="error")
        
        server_thread = threading.Thread(target=run_server, daemon=True)
        server_thread.start()
        
        print("Web服务器启动成功")
        time.sleep(3)  # 等待服务器启动
        
    except Exception as e:
        print(f"启动Web服务器失败: {e}")
        return
    
    base_url = "http://127.0.0.1:8002"
    session = requests.Session()
    
    try:
        # 登录
        print("登录管理员账户...")
        login_data = {"username": "admin", "password": "admin123"}
        response = session.post(f"{base_url}/auth/login", data=login_data)
        
        if response.status_code != 200:
            print(f"登录失败: {response.status_code}")
            print(f"响应: {response.text}")
            return
        
        print("登录成功")
        
        # 测试下单API
        print("\n测试下单API...")
        order_data = {
            "symbol": "000001.SZ",
            "side": "buy", 
            "quantity": 100,
            "order_type": "market"
        }
        
        response = session.post(f"{base_url}/api/trading/orders", json=order_data)
        print(f"下单API状态码: {response.status_code}")
        print(f"下单API响应: {response.text}")
        
        # 测试添加告警API
        print("\n测试添加告警API...")
        alert_data = {
            "level": "INFO",
            "message": "测试告警",
            "source": "debug_test"
        }
        
        response = session.post(f"{base_url}/api/monitoring/alerts", json=alert_data)
        print(f"告警API状态码: {response.status_code}")
        print(f"告警API响应: {response.text}")
        
        # 测试API文档
        print("\n获取API文档...")
        response = session.get(f"{base_url}/api/docs")
        print(f"API文档状态码: {response.status_code}")
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
    finally:
        session.close()

if __name__ == "__main__":
    debug_api_calls()