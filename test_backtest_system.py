# -*- coding: utf-8 -*-
"""
回测系统集成测试

测试历史数据回放、回测引擎、性能分析和并行回测功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json

from qlib_trading_system.backtest import (
    HistoricalDataReplay, 
    BacktestEngine, 
    PerformanceAnalyzer,
    ParallelBacktestFramework
)
from qlib_trading_system.backtest.data_replay import ReplayConfig, DataFrequency
from qlib_trading_system.backtest.backtest_engine import BacktestConfig, Strategy, Order, OrderSide, OrderType
from qlib_trading_system.backtest.parallel_backtest import StrategyConfig, ParallelBacktestConfig

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('backtest_system_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


class TestMAStrategy(Strategy):
    """测试用移动平均策略"""
    
    def __init__(self, strategy_id: str = "test_ma", short_window: int = 5, long_window: int = 20):
        super().__init__(strategy_id)
        self.short_window = short_window
        self.long_window = long_window
        self.price_history = {}
        self.order_counter = 0
        
        logger.info(f"初始化测试MA策略：{strategy_id}, 短期={short_window}, 长期={long_window}")
    
    def on_data(self, timestamp: datetime, data) -> list:
        orders = []
        
        for symbol, market_data in data.items():
            # 更新价格历史
            if symbol not in self.price_history:
                self.price_history[symbol] = []
            
            self.price_history[symbol].append(market_data.close)
            
            # 保持历史数据长度
            if len(self.price_history[symbol]) > self.long_window:
                self.price_history[symbol] = self.price_history[symbol][-self.long_window:]
            
            # 计算移动平均
            if len(self.price_history[symbol]) >= self.long_window:
                short_ma = np.mean(self.price_history[symbol][-self.short_window:])
                long_ma = np.mean(self.price_history[symbol][-self.long_window:])
                prev_short_ma = np.mean(self.price_history[symbol][-self.short_window-1:-1])
                prev_long_ma = np.mean(self.price_history[symbol][-self.long_window-1:-1])
                
                # 金叉买入信号
                if short_ma > long_ma and prev_short_ma <= prev_long_ma:
                    if symbol not in self.positions or self.positions[symbol].quantity == 0:
                        self.order_counter += 1
                        order = Order(
                            order_id=f"{self.strategy_id}_{self.order_counter}",
                            symbol=symbol,
                            side=OrderSide.BUY,
                            order_type=OrderType.MARKET,
                            quantity=1000,
                            strategy_id=self.strategy_id,
                            signal_info={
                                'signal': 'golden_cross',
                                'short_ma': short_ma,
                                'long_ma': long_ma
                            }
                        )
                        orders.append(order)
                        logger.debug(f"生成买入信号：{symbol} @{market_data.close:.2f}")
                
                # 死叉卖出信号
                elif short_ma < long_ma and prev_short_ma >= prev_long_ma:
                    if symbol in self.positions and self.positions[symbol].quantity > 0:
                        self.order_counter += 1
                        order = Order(
                            order_id=f"{self.strategy_id}_{self.order_counter}",
                            symbol=symbol,
                            side=OrderSide.SELL,
                            order_type=OrderType.MARKET,
                            quantity=self.positions[symbol].quantity,
                            strategy_id=self.strategy_id,
                            signal_info={
                                'signal': 'death_cross',
                                'short_ma': short_ma,
                                'long_ma': long_ma
                            }
                        )
                        orders.append(order)
                        logger.debug(f"生成卖出信号：{symbol} @{market_data.close:.2f}")
        
        return orders
    
    def on_order_filled(self, order):
        logger.info(f"策略 {self.strategy_id} 订单成交：{order.symbol} {order.side.value} {order.filled_quantity}股 @{order.filled_price:.2f}")


class TestBuyHoldStrategy(Strategy):
    """测试用买入持有策略"""
    
    def __init__(self, strategy_id: str = "test_buy_hold", target_symbols: list = None):
        super().__init__(strategy_id)
        self.target_symbols = target_symbols or []
        self.has_bought = set()
        self.order_counter = 0
        
        logger.info(f"初始化测试买入持有策略：{strategy_id}, 目标股票={target_symbols}")
    
    def on_data(self, timestamp: datetime, data) -> list:
        orders = []
        
        # 只在第一次看到数据时买入
        for symbol in self.target_symbols:
            if symbol in data and symbol not in self.has_bought:
                self.order_counter += 1
                order = Order(
                    order_id=f"{self.strategy_id}_{self.order_counter}",
                    symbol=symbol,
                    side=OrderSide.BUY,
                    order_type=OrderType.MARKET,
                    quantity=2000,
                    strategy_id=self.strategy_id,
                    signal_info={'signal': 'buy_and_hold'}
                )
                orders.append(order)
                self.has_bought.add(symbol)
                logger.info(f"买入持有策略买入：{symbol}")
        
        return orders
    
    def on_order_filled(self, order):
        logger.info(f"买入持有策略订单成交：{order.symbol} {order.side.value} {order.filled_quantity}股")


def test_data_replay():
    """测试历史数据回放功能"""
    logger.info("=" * 50)
    logger.info("开始测试历史数据回放功能")
    
    try:
        # 创建回放配置
        config = ReplayConfig(
            start_date=datetime(2024, 1, 1),
            end_date=datetime(2024, 1, 10),
            symbols=['000001.SZ', '000002.SZ', '000858.SZ'],
            frequency=DataFrequency.DAILY,
            include_indicators=True,
            trading_hours_only=True
        )
        
        # 创建回放系统
        replay = HistoricalDataReplay(config)
        
        # 加载数据
        data = replay.load_historical_data()
        logger.info(f"成功加载 {len(data)} 只股票的数据")
        
        # 测试数据回放
        count = 0
        for timestamp, market_data in replay.start_replay():
            logger.info(f"时间：{timestamp.strftime('%Y-%m-%d %H:%M:%S')}")
            for symbol, data_point in market_data.items():
                logger.info(f"  {symbol}: 开={data_point.open:.2f}, 高={data_point.high:.2f}, "
                          f"低={data_point.low:.2f}, 收={data_point.close:.2f}, 量={data_point.volume}")
                
                # 检查技术指标
                if data_point.indicators:
                    indicators_str = ", ".join([f"{k}={v:.2f}" for k, v in list(data_point.indicators.items())[:3]])
                    logger.info(f"    技术指标: {indicators_str}")
            
            count += 1
            if count >= 5:  # 只显示前5个数据点
                break
        
        # 获取数据摘要
        summary = replay.get_data_summary()
        logger.info(f"数据摘要：{json.dumps(summary, indent=2, ensure_ascii=False, default=str)}")
        
        logger.info("历史数据回放功能测试通过 ✓")
        return True
        
    except Exception as e:
        logger.error(f"历史数据回放功能测试失败：{e}")
        return False


def test_backtest_engine():
    """测试回测引擎功能"""
    logger.info("=" * 50)
    logger.info("开始测试回测引擎功能")
    
    try:
        # 创建回测配置
        backtest_config = BacktestConfig(
            initial_capital=1000000,
            commission_rate=0.0003,
            slippage_rate=0.001,
            max_position_size=0.3,
            stop_loss_pct=0.1
        )
        
        # 创建数据回放配置
        replay_config = ReplayConfig(
            start_date=datetime(2024, 1, 1),
            end_date=datetime(2024, 1, 31),
            symbols=['000001.SZ', '000002.SZ'],
            frequency=DataFrequency.DAILY,
            include_indicators=True
        )
        
        # 创建数据回放器
        data_replay = HistoricalDataReplay(replay_config)
        
        # 创建回测引擎
        engine = BacktestEngine(backtest_config)
        engine.set_data_replay(data_replay)
        
        # 添加策略
        strategy = TestMAStrategy(strategy_id="test_ma_5_20", short_window=5, long_window=20)
        engine.add_strategy(strategy)
        
        # 运行回测
        logger.info("开始运行回测...")
        results = engine.run_backtest()
        
        # 检查结果
        basic_stats = results.get('basic_stats', {})
        trading_stats = results.get('trading_stats', {})
        
        logger.info("回测结果:")
        logger.info(f"  初始资金: {basic_stats.get('initial_capital', 0):,.2f}")
        logger.info(f"  最终价值: {basic_stats.get('final_value', 0):,.2f}")
        logger.info(f"  总收益率: {basic_stats.get('total_return', 0):.2%}")
        logger.info(f"  年化收益率: {basic_stats.get('annual_return', 0):.2%}")
        logger.info(f"  夏普比率: {basic_stats.get('sharpe_ratio', 0):.3f}")
        logger.info(f"  最大回撤: {basic_stats.get('max_drawdown', 0):.2%}")
        logger.info(f"  总交易次数: {trading_stats.get('total_trades', 0)}")
        logger.info(f"  胜率: {trading_stats.get('win_rate', 0):.2%}")
        
        # 显示业绩摘要
        summary = engine.get_performance_summary()
        logger.info("业绩摘要:")
        logger.info(summary)
        
        logger.info("回测引擎功能测试通过 ✓")
        return True, results
        
    except Exception as e:
        logger.error(f"回测引擎功能测试失败：{e}")
        return False, None


def test_performance_analyzer(backtest_results):
    """测试性能分析功能"""
    logger.info("=" * 50)
    logger.info("开始测试性能分析功能")
    
    try:
        if not backtest_results:
            logger.warning("没有回测结果，跳过性能分析测试")
            return False
        
        # 创建性能分析器
        analyzer = PerformanceAnalyzer(backtest_results)
        
        # 计算综合指标
        metrics = analyzer.calculate_comprehensive_metrics()
        
        logger.info("综合性能指标:")
        logger.info(f"  总收益率: {metrics.total_return:.2%}")
        logger.info(f"  年化收益率: {metrics.annual_return:.2%}")
        logger.info(f"  年化波动率: {metrics.volatility:.2%}")
        logger.info(f"  夏普比率: {metrics.sharpe_ratio:.3f}")
        logger.info(f"  索提诺比率: {metrics.sortino_ratio:.3f}")
        logger.info(f"  最大回撤: {metrics.max_drawdown:.2%}")
        logger.info(f"  95% VaR: {metrics.var_95:.4%}")
        logger.info(f"  胜率: {metrics.win_rate:.2%}")
        logger.info(f"  盈亏比: {metrics.profit_loss_ratio:.2f}")
        
        # 生成详细报告
        report = analyzer.generate_performance_report()
        logger.info("生成详细报告成功")
        
        # 保存报告到文件
        with open('test_performance_report.md', 'w', encoding='utf-8') as f:
            f.write(report)
        logger.info("报告已保存到 test_performance_report.md")
        
        # 导出Excel
        analyzer.export_results_to_excel('test_backtest_results.xlsx')
        logger.info("结果已导出到 test_backtest_results.xlsx")
        
        # 绘制图表（可选，需要matplotlib）
        try:
            charts = analyzer.plot_performance_charts(save_dir='.')
            logger.info(f"生成图表: {list(charts.keys())}")
        except Exception as e:
            logger.warning(f"绘制图表失败（可能缺少matplotlib）：{e}")
        
        logger.info("性能分析功能测试通过 ✓")
        return True
        
    except Exception as e:
        logger.error(f"性能分析功能测试失败：{e}")
        return False


def test_parallel_backtest():
    """测试并行回测功能"""
    logger.info("=" * 50)
    logger.info("开始测试并行回测功能")
    
    try:
        # 创建数据配置
        data_config = ReplayConfig(
            start_date=datetime(2024, 1, 1),
            end_date=datetime(2024, 1, 31),
            symbols=['000001.SZ', '000002.SZ', '000858.SZ'],
            frequency=DataFrequency.DAILY,
            include_indicators=True
        )
        
        # 创建回测配置
        backtest_config = BacktestConfig(
            initial_capital=1000000,
            commission_rate=0.0003,
            slippage_rate=0.001
        )
        
        # 创建策略配置
        strategies = [
            StrategyConfig(
                strategy_class=TestBuyHoldStrategy,
                strategy_params={'target_symbols': ['000001.SZ', '000002.SZ']},
                strategy_id='buy_hold_test',
                description='测试买入持有策略'
            ),
            StrategyConfig(
                strategy_class=TestMAStrategy,
                strategy_params={'short_window': 5, 'long_window': 10},
                strategy_id='ma_5_10_test',
                description='测试MA(5,10)策略'
            ),
            StrategyConfig(
                strategy_class=TestMAStrategy,
                strategy_params={'short_window': 5, 'long_window': 20},
                strategy_id='ma_5_20_test',
                description='测试MA(5,20)策略'
            )
        ]
        
        # 创建并行回测配置
        parallel_config = ParallelBacktestConfig(
            data_config=data_config,
            backtest_config=backtest_config,
            strategies=strategies,
            max_workers=2,
            use_multiprocessing=False,  # 测试时使用线程模式避免序列化问题
            save_results=True,
            results_dir='test_parallel_results'
        )
        
        # 创建并行回测框架
        framework = ParallelBacktestFramework(parallel_config)
        
        # 运行并行回测
        logger.info("开始运行并行回测...")
        results = framework.run_parallel_backtest()
        
        logger.info(f"并行回测完成，共 {len(results)} 个策略")
        
        # 显示每个策略的结果
        for strategy_id, result in results.items():
            if 'error' not in result:
                basic_stats = result.get('basic_stats', {})
                logger.info(f"策略 {strategy_id}:")
                logger.info(f"  总收益率: {basic_stats.get('total_return', 0):.2%}")
                logger.info(f"  年化收益率: {basic_stats.get('annual_return', 0):.2%}")
                logger.info(f"  夏普比率: {basic_stats.get('sharpe_ratio', 0):.3f}")
            else:
                logger.error(f"策略 {strategy_id} 执行失败: {result['error']}")
        
        # 获取最佳策略
        best_strategies = framework.get_best_strategies(top_n=2)
        logger.info("\n最佳策略排名:")
        for i, (strategy_id, metrics) in enumerate(best_strategies):
            logger.info(f"{i+1}. {strategy_id}: 年化收益率={metrics.annual_return:.2%}, "
                       f"夏普比率={metrics.sharpe_ratio:.3f}")
        
        # 获取策略对比矩阵
        comparison_matrix = framework.get_strategy_comparison_matrix()
        if not comparison_matrix.empty:
            logger.info("\n策略对比矩阵:")
            logger.info(comparison_matrix.to_string(index=False))
        
        logger.info("并行回测功能测试通过 ✓")
        return True
        
    except Exception as e:
        logger.error(f"并行回测功能测试失败：{e}")
        return False


def main():
    """主测试函数"""
    logger.info("开始回测系统集成测试")
    logger.info("=" * 80)
    
    test_results = {}
    
    # 1. 测试历史数据回放
    test_results['data_replay'] = test_data_replay()
    
    # 2. 测试回测引擎
    engine_success, backtest_results = test_backtest_engine()
    test_results['backtest_engine'] = engine_success
    
    # 3. 测试性能分析
    test_results['performance_analyzer'] = test_performance_analyzer(backtest_results)
    
    # 4. 测试并行回测
    test_results['parallel_backtest'] = test_parallel_backtest()
    
    # 汇总测试结果
    logger.info("=" * 80)
    logger.info("回测系统集成测试结果汇总:")
    
    all_passed = True
    for test_name, result in test_results.items():
        status = "通过 ✓" if result else "失败 ✗"
        logger.info(f"  {test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        logger.info("\n🎉 所有测试通过！回测系统功能正常")
        logger.info("\n系统功能说明:")
        logger.info("1. ✓ 历史数据回放系统 - 支持多种频率的历史数据回放")
        logger.info("2. ✓ 策略回测执行引擎 - 完整的订单执行和仓位管理")
        logger.info("3. ✓ 回测结果分析算法 - 全面的业绩指标计算和报告生成")
        logger.info("4. ✓ 多策略并行回测框架 - 支持多策略同时回测和对比分析")
        
        logger.info("\n生成的文件:")
        logger.info("- backtest_system_test.log: 测试日志")
        logger.info("- test_performance_report.md: 性能分析报告")
        logger.info("- test_backtest_results.xlsx: 回测结果Excel文件")
        logger.info("- test_parallel_results/: 并行回测结果目录")
        
    else:
        logger.error("\n❌ 部分测试失败，请检查错误日志")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)