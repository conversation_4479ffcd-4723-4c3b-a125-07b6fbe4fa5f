#!/usr/bin/env python3
"""
风险监控仪表板演示
Risk Monitoring Dashboard Demo

启动风险监控系统并展示Web仪表板功能
"""

import asyncio
import time
from datetime import datetime
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(__file__))

from qlib_trading_system.risk.monitors.integrated_risk_system import RiskSystemFactory


async def run_dashboard_demo():
    """运行仪表板演示"""
    print("🛡️ 风险监控仪表板演示")
    print("=" * 50)
    
    # 创建系统配置
    config = {
        'monitor': {
            'max_drawdown': 0.25,
            'daily_loss_limit': 0.12,
            'var_limit': 0.08,
            'position_concentration': 0.60,
            'leverage_limit': 2.0,
            'update_interval': 2.0  # 2秒更新一次
        },
        'notification': {
            'email_enabled': False,
            'dingtalk_enabled': False,
            'wechat_enabled': False,
            'sms_enabled': False,
            'min_notification_interval': 30
        },
        'dashboard': {
            'port': 8080
        }
    }
    
    # 创建系统
    print("1. 创建风险监控系统...")
    system = RiskSystemFactory.create_system(config_dict=config)
    
    # 添加回调函数
    def alert_callback(alert, metrics):
        print(f"🚨 [{alert.severity}] {alert.alert_type}: {alert.message}")
    
    def metrics_callback(metrics):
        print(f"📊 风险等级: {metrics.risk_level} | "
              f"总PnL: {metrics.total_pnl:.0f} | "
              f"回撤: {metrics.current_drawdown:.1%} | "
              f"集中度: {metrics.position_concentration:.1%}")
    
    system.add_alert_callback(alert_callback)
    system.add_metrics_callback(metrics_callback)
    
    try:
        # 启动系统
        print("2. 启动风险监控系统...")
        await system.start_system()
        
        status = system.get_system_status()
        print(f"✅ 系统启动成功！")
        print(f"🌐 仪表板地址: {status['dashboard_url']}")
        print(f"📊 请在浏览器中访问上述地址查看实时仪表板")
        
        # 模拟交易场景
        print("\n3. 开始模拟交易场景...")
        
        # 场景1: 正常交易
        print("\n📈 场景1: 正常盈利交易")
        positions_data = {
            'AAPL': {
                'quantity': 100,
                'avg_cost': 150.0,
                'current_price': 155.0,
                'beta': 1.2,
                'volatility': 0.25
            },
            'MSFT': {
                'quantity': 50,
                'avg_cost': 300.0,
                'current_price': 310.0,
                'beta': 1.0,
                'volatility': 0.20
            }
        }
        
        account_data = {
            'total_capital': 100000.0,
            'available_cash': 30000.0,
            'total_value': 106000.0,
            'initial_capital': 100000.0
        }
        
        system.update_positions(positions_data)
        system.update_account_info(account_data)
        
        print("等待10秒观察正常状态...")
        await asyncio.sleep(10)
        
        # 场景2: 价格波动
        print("\n📉 场景2: 价格下跌，触发预警")
        positions_data['AAPL']['current_price'] = 140.0  # 下跌
        positions_data['MSFT']['current_price'] = 290.0  # 下跌
        account_data['total_value'] = 96500.0
        
        system.update_positions(positions_data)
        system.update_account_info(account_data)
        
        print("等待10秒观察预警状态...")
        await asyncio.sleep(10)
        
        # 场景3: 严重亏损
        print("\n🚨 场景3: 严重亏损，多重预警")
        positions_data['AAPL']['current_price'] = 120.0  # 大幅下跌
        positions_data['MSFT']['current_price'] = 250.0  # 大幅下跌
        account_data['total_value'] = 83500.0  # 严重亏损
        
        system.update_positions(positions_data)
        system.update_account_info(account_data)
        
        print("等待10秒观察严重预警状态...")
        await asyncio.sleep(10)
        
        # 场景4: 集中持仓
        print("\n⚠️ 场景4: 集中持仓风险")
        positions_data = {
            'TSLA': {
                'quantity': 200,
                'avg_cost': 400.0,
                'current_price': 380.0,
                'beta': 1.8,
                'volatility': 0.45
            }
        }
        account_data['total_value'] = 76000.0
        
        system.update_positions(positions_data)
        system.update_account_info(account_data)
        
        print("等待10秒观察集中持仓风险...")
        await asyncio.sleep(10)
        
        # 场景5: 恢复正常
        print("\n💚 场景5: 价格恢复，风险缓解")
        positions_data['TSLA']['current_price'] = 420.0  # 价格恢复
        account_data['total_value'] = 84000.0
        
        system.update_positions(positions_data)
        system.update_account_info(account_data)
        
        print("等待10秒观察恢复状态...")
        await asyncio.sleep(10)
        
        # 导出最终报告
        print("\n4. 导出演示报告...")
        report_file = f"dashboard_demo_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        system.export_system_report(report_file)
        print(f"📄 演示报告已导出: {report_file}")
        
        # 保持运行
        print(f"\n🚀 演示完成！系统将继续运行...")
        print(f"🌐 仪表板地址: {status['dashboard_url']}")
        print("📊 您可以继续在浏览器中观察实时数据")
        print("⌨️ 按 Ctrl+C 停止演示")
        
        # 持续运行直到用户中断
        while True:
            await asyncio.sleep(5)
            
            # 模拟小幅价格波动
            import random
            for symbol in positions_data:
                current_price = positions_data[symbol]['current_price']
                # 随机波动 ±2%
                change_pct = random.uniform(-0.02, 0.02)
                new_price = current_price * (1 + change_pct)
                positions_data[symbol]['current_price'] = new_price
            
            # 更新总资产
            total_position_value = sum(
                pos['quantity'] * pos['current_price'] 
                for pos in positions_data.values()
            )
            account_data['total_value'] = account_data['available_cash'] + total_position_value
            
            system.update_positions(positions_data)
            system.update_account_info(account_data)
        
    except KeyboardInterrupt:
        print("\n\n⏹️ 用户中断演示")
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 停止系统
        print("\n5. 停止风险监控系统...")
        await system.stop_system()
        print("✅ 演示结束")


def main():
    """主函数"""
    print("🛡️ 风险监控仪表板演示程序")
    print("=" * 60)
    print("本演示将启动完整的风险监控系统，包括:")
    print("  • 实时风险指标计算")
    print("  • 动态预警系统")
    print("  • Web仪表板界面")
    print("  • 多种交易场景模拟")
    print()
    
    try:
        response = input("是否开始演示？(y/n): ").lower().strip()
        if response not in ['y', 'yes', '是']:
            print("演示已取消")
            return
        
        print("\n🚀 开始演示...")
        asyncio.run(run_dashboard_demo())
        
    except KeyboardInterrupt:
        print("\n演示已取消")
    except Exception as e:
        print(f"启动演示失败: {e}")


if __name__ == "__main__":
    main()