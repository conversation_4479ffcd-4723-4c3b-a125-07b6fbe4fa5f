---
inclusion: fileMatch
fileMatchPattern: '**/test_*.py'
---

# 测试标准和最佳实践

## 测试架构设计

### 1. 测试金字塔结构

```
                    E2E Tests (端到端测试)
                   /                    \
              Integration Tests (集成测试)
             /                            \
        Unit Tests (单元测试)
       /                                    \
  Component Tests (组件测试)
```

### 2. 测试分类和覆盖率要求

| 测试类型 | 覆盖率要求 | 执行频率 | 目标 |
|----------|------------|----------|------|
| 单元测试 | ≥90% | 每次提交 | 验证单个函数/方法 |
| 组件测试 | ≥80% | 每次提交 | 验证单个模块 |
| 集成测试 | ≥70% | 每日构建 | 验证模块间协作 |
| 端到端测试 | ≥60% | 发布前 | 验证完整工作流 |

### 3. 测试文件命名规范

```
测试文件结构:
├── test_unit/              # 单元测试
│   ├── test_models/
│   ├── test_data/
│   └── test_utils/
├── test_integration/       # 集成测试
│   ├── test_data_pipeline/
│   ├── test_model_pipeline/
│   └── test_trading_system/
├── test_e2e/              # 端到端测试
│   ├── test_complete_workflow/
│   └── test_user_scenarios/
└── test_performance/       # 性能测试
    ├── test_benchmarks/
    └── test_load/
```

## 单元测试标准

### 1. 测试类结构模板

```python
import unittest
import numpy as np
import pandas as pd
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
import tempfile
import shutil
from pathlib import Path

class TestModelComponent(unittest.TestCase):
    """模型组件单元测试"""
    
    @classmethod
    def setUpClass(cls):
        """类级别的测试准备（一次性）"""
        cls.test_config = {
            'model_type': 'lightgbm',
            'n_estimators': 10,  # 测试用小参数
            'learning_rate': 0.1
        }
    
    def setUp(self):
        """每个测试方法前的准备"""
        # 创建临时目录
        self.temp_dir = tempfile.mkdtemp()
        
        # 生成测试数据
        np.random.seed(42)  # 确保可重现性
        self.X_test = np.random.randn(100, 10)
        self.y_test = np.random.randn(100)
        
        # 初始化被测试对象
        self.model = ModelComponent(self.test_config)
    
    def tearDown(self):
        """每个测试方法后的清理"""
        # 清理临时目录
        if hasattr(self, 'temp_dir'):
            shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_model_initialization(self):
        """测试模型初始化"""
        # 测试正常初始化
        self.assertIsNotNone(self.model)
        self.assertEqual(self.model.config['model_type'], 'lightgbm')
        self.assertFalse(self.model.is_trained)
        
        # 测试异常配置
        with self.assertRaises(ValueError):
            ModelComponent({'invalid_config': True})
    
    def test_model_training(self):
        """测试模型训练"""
        # 测试正常训练
        result = self.model.train(self.X_test, self.y_test)
        
        self.assertTrue(self.model.is_trained)
        self.assertIn('train_score', result)
        self.assertIsInstance(result['train_score'], (int, float))
        
        # 测试异常输入
        with self.assertRaises(ValueError):
            self.model.train(np.array([]), np.array([]))
    
    def test_model_prediction(self):
        """测试模型预测"""
        # 先训练模型
        self.model.train(self.X_test, self.y_test)
        
        # 测试正常预测
        X_pred = np.random.randn(20, 10)
        predictions = self.model.predict(X_pred)
        
        self.assertEqual(len(predictions), 20)
        self.assertFalse(np.isnan(predictions).any())
        
        # 测试未训练模型预测
        untrained_model = ModelComponent(self.test_config)
        with self.assertRaises(ValueError):
            untrained_model.predict(X_pred)
    
    @patch('qlib_trading_system.utils.logging.logger')
    def test_error_handling(self, mock_logger):
        """测试错误处理"""
        # 模拟训练失败
        with patch.object(self.model, '_internal_train', side_effect=Exception("Training failed")):
            with self.assertRaises(Exception):
                self.model.train(self.X_test, self.y_test)
            
            # 验证错误日志
            mock_logger.error.assert_called()
    
    def test_model_persistence(self):
        """测试模型保存和加载"""
        # 训练模型
        self.model.train(self.X_test, self.y_test)
        
        # 保存模型
        model_path = Path(self.temp_dir) / "test_model.pkl"
        success = self.model.save_model(str(model_path))
        
        self.assertTrue(success)
        self.assertTrue(model_path.exists())
        
        # 加载模型
        new_model = ModelComponent(self.test_config)
        success = new_model.load_model(str(model_path))
        
        self.assertTrue(success)
        self.assertTrue(new_model.is_trained)
        
        # 验证预测一致性
        X_pred = np.random.randn(10, 10)
        pred1 = self.model.predict(X_pred)
        pred2 = new_model.predict(X_pred)
        
        np.testing.assert_array_almost_equal(pred1, pred2, decimal=6)
```

### 2. 数据相关测试

```python
class TestDataProcessor(unittest.TestCase):
    """数据处理器测试"""
    
    def setUp(self):
        """准备测试数据"""
        # 创建标准测试数据集
        self.sample_data = pd.DataFrame({
            'symbol': ['000001', '000002', '000001', '000002'],
            'date': pd.to_datetime(['2023-01-01', '2023-01-01', '2023-01-02', '2023-01-02']),
            'open': [10.0, 20.0, 10.5, 20.5],
            'high': [10.5, 21.0, 11.0, 21.5],
            'low': [9.8, 19.5, 10.2, 20.0],
            'close': [10.2, 20.8, 10.8, 21.2],
            'volume': [1000000, 2000000, 1100000, 2100000]
        })
        
        # 创建包含异常值的数据
        self.data_with_outliers = self.sample_data.copy()
        self.data_with_outliers.loc[0, 'high'] = 1000.0  # 异常高价
        
        # 创建包含缺失值的数据
        self.data_with_missing = self.sample_data.copy()
        self.data_with_missing.loc[1, 'close'] = np.nan
    
    def test_data_validation(self):
        """测试数据验证"""
        processor = DataProcessor()
        
        # 测试正常数据
        self.assertTrue(processor.validate_data(self.sample_data))
        
        # 测试空数据
        self.assertFalse(processor.validate_data(pd.DataFrame()))
        
        # 测试缺少必需字段的数据
        incomplete_data = self.sample_data.drop('close', axis=1)
        self.assertFalse(processor.validate_data(incomplete_data))
    
    def test_missing_value_handling(self):
        """测试缺失值处理"""
        processor = DataProcessor()
        
        # 测试前向填充
        result = processor.handle_missing_values(
            self.data_with_missing, 
            method='forward_fill'
        )
        
        self.assertFalse(result.isnull().any().any())
        
        # 验证填充值正确性
        expected_value = self.data_with_missing.loc[0, 'close']
        actual_value = result.loc[1, 'close']
        self.assertEqual(actual_value, expected_value)
    
    def test_outlier_detection(self):
        """测试异常值检测"""
        processor = DataProcessor()
        
        outliers = processor.detect_outliers(self.data_with_outliers)
        
        # 验证检测到异常值
        self.assertIn('high', outliers)
        self.assertIn(0, outliers['high'])  # 第0行包含异常值
    
    def test_data_transformation(self):
        """测试数据转换"""
        processor = DataProcessor()
        
        # 测试标准化
        normalized_data = processor.normalize_data(self.sample_data)
        
        # 验证数值列被标准化
        numeric_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in numeric_columns:
            if col in normalized_data.columns:
                # 标准化后均值应接近0，标准差应接近1
                self.assertAlmostEqual(normalized_data[col].mean(), 0, places=1)
                self.assertAlmostEqual(normalized_data[col].std(), 1, places=1)
```

### 3. Mock和Patch使用

```python
class TestDataCollector(unittest.TestCase):
    """数据收集器测试"""
    
    @patch('qlib_trading_system.data.collectors.itick_adapter.requests.get')
    def test_api_call_success(self, mock_get):
        """测试API调用成功"""
        # 模拟API响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'data': [
                {'symbol': '000001', 'price': 10.0, 'volume': 1000}
            ]
        }
        mock_get.return_value = mock_response
        
        # 执行测试
        collector = DataCollector()
        result = collector.fetch_data('000001')
        
        # 验证结果
        self.assertIsNotNone(result)
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0]['symbol'], '000001')
        
        # 验证API调用
        mock_get.assert_called_once()
    
    @patch('qlib_trading_system.data.collectors.itick_adapter.requests.get')
    def test_api_call_failure(self, mock_get):
        """测试API调用失败"""
        # 模拟API失败
        mock_get.side_effect = requests.exceptions.RequestException("Network error")
        
        collector = DataCollector()
        
        # 验证异常处理
        with self.assertRaises(DataCollectionError):
            collector.fetch_data('000001')
    
    @patch('qlib_trading_system.data.collectors.base.time.sleep')
    def test_retry_mechanism(self, mock_sleep):
        """测试重试机制"""
        collector = DataCollector(max_retries=3)
        
        with patch.object(collector, '_make_request') as mock_request:
            # 前两次失败，第三次成功
            mock_request.side_effect = [
                requests.exceptions.RequestException("Error 1"),
                requests.exceptions.RequestException("Error 2"),
                {'data': []}
            ]
            
            result = collector.fetch_data('000001')
            
            # 验证重试次数
            self.assertEqual(mock_request.call_count, 3)
            self.assertEqual(mock_sleep.call_count, 2)  # 重试前的等待
```

## 集成测试标准

### 1. 数据流集成测试

```python
class TestDataPipelineIntegration(unittest.TestCase):
    """数据管道集成测试"""
    
    def setUp(self):
        """准备集成测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        
        # 创建测试配置
        self.config = {
            'data_sources': ['test_source'],
            'storage_path': self.temp_dir,
            'batch_size': 100
        }
        
        # 初始化组件
        self.data_collector = DataCollector(self.config)
        self.data_processor = DataProcessor(self.config)
        self.data_storage = DataStorage(self.config)
    
    def test_end_to_end_data_flow(self):
        """测试端到端数据流"""
        # 1. 数据收集
        with patch.object(self.data_collector, 'fetch_data') as mock_fetch:
            mock_fetch.return_value = self._create_sample_data()
            
            raw_data = self.data_collector.collect_daily_data('2023-01-01')
            self.assertIsNotNone(raw_data)
            self.assertGreater(len(raw_data), 0)
        
        # 2. 数据处理
        processed_data = self.data_processor.process(raw_data)
        
        # 验证处理结果
        self.assertIsNotNone(processed_data)
        self.assertFalse(processed_data.isnull().any().any())  # 无缺失值
        
        # 3. 数据存储
        success = self.data_storage.save_data(processed_data, '2023-01-01')
        self.assertTrue(success)
        
        # 4. 数据读取验证
        loaded_data = self.data_storage.load_data('2023-01-01')
        pd.testing.assert_frame_equal(processed_data, loaded_data)
    
    def test_error_propagation(self):
        """测试错误传播"""
        # 模拟数据收集失败
        with patch.object(self.data_collector, 'fetch_data', 
                         side_effect=Exception("Collection failed")):
            
            with self.assertRaises(Exception):
                self.data_collector.collect_daily_data('2023-01-01')
    
    def _create_sample_data(self):
        """创建样本数据"""
        return pd.DataFrame({
            'symbol': ['000001', '000002'],
            'date': ['2023-01-01', '2023-01-01'],
            'open': [10.0, 20.0],
            'high': [10.5, 21.0],
            'low': [9.8, 19.5],
            'close': [10.2, 20.8],
            'volume': [1000000, 2000000]
        })
```

### 2. 模型训练集成测试

```python
class TestModelTrainingIntegration(unittest.TestCase):
    """模型训练集成测试"""
    
    def setUp(self):
        """准备模型训练环境"""
        self.temp_dir = tempfile.mkdtemp()
        
        # 创建完整的训练配置
        self.config = TrainingConfig(
            incremental_config=IncrementalConfig(
                batch_size=100,
                min_samples_for_update=50
            ),
            enable_ab_testing=False,  # 简化测试
            enable_monitoring=True
        )
        
        # 初始化训练系统
        self.training_system = ModelTrainingSystem(
            self.config, 
            str(Path(self.temp_dir) / "training")
        )
    
    def test_complete_training_workflow(self):
        """测试完整训练工作流"""
        # 1. 准备训练数据
        X_train, y_train = self._generate_training_data(1000, 20)
        X_val, y_val = self._generate_training_data(200, 20)
        
        # 2. 初始化基础模型
        base_version = self.training_system.initialize_base_model(
            X_train, y_train, X_val, y_val,
            model_name="集成测试模型"
        )
        
        self.assertIsNotNone(base_version)
        
        # 3. 验证系统状态
        status = self.training_system.get_system_status()
        self.assertEqual(status['active_model_version'], base_version)
        self.assertTrue(status['incremental_trainer_info']['model_exists'])
        
        # 4. 增量训练
        X_new, y_new = self._generate_training_data(100, 20)
        self.training_system.add_training_data(X_new, y_new)
        
        update_result = self.training_system.trigger_incremental_update()
        self.assertIn('status', update_result)
        
        # 5. 预测测试
        X_test, _ = self._generate_training_data(50, 20)
        predictions = self.training_system.predict_with_monitoring(X_test)
        
        self.assertEqual(len(predictions), 50)
        self.assertFalse(np.isnan(predictions).any())
    
    def test_model_version_management(self):
        """测试模型版本管理"""
        # 创建多个模型版本
        versions = []
        
        for i in range(3):
            X_train, y_train = self._generate_training_data(500, 15)
            
            if i == 0:
                # 第一个版本：初始化基础模型
                version = self.training_system.initialize_base_model(
                    X_train, y_train, model_name=f"测试模型v{i+1}"
                )
            else:
                # 后续版本：增量更新
                self.training_system.add_training_data(X_train, y_train)
                result = self.training_system.trigger_incremental_update(force=True)
                version = result.get('new_version')
            
            if version:
                versions.append(version)
        
        # 验证版本管理
        version_list = self.training_system.version_manager.list_versions()
        self.assertGreaterEqual(len(version_list), 1)
        
        # 测试版本回滚
        if len(versions) >= 2:
            success = self.training_system.rollback_model(
                versions[0], "集成测试回滚"
            )
            self.assertTrue(success)
            
            active_version = self.training_system.version_manager.get_active_version()
            self.assertEqual(active_version, versions[0])
    
    def _generate_training_data(self, n_samples: int, n_features: int):
        """生成训练数据"""
        np.random.seed(42)
        X = np.random.randn(n_samples, n_features)
        y = np.random.randn(n_samples)
        return X, y
```

## 性能测试标准

### 1. 基准测试

```python
class TestPerformanceBenchmarks(unittest.TestCase):
    """性能基准测试"""
    
    def setUp(self):
        """准备性能测试环境"""
        self.large_dataset_size = 10000
        self.feature_count = 100
        
        # 生成大规模测试数据
        np.random.seed(42)
        self.X_large = np.random.randn(self.large_dataset_size, self.feature_count)
        self.y_large = np.random.randn(self.large_dataset_size)
    
    def test_model_training_performance(self):
        """测试模型训练性能"""
        model = StockSelectionModel({'model_type': 'lightgbm'})
        
        start_time = time.time()
        model.train(self.X_large, self.y_large)
        training_time = time.time() - start_time
        
        # 性能要求：大规模数据训练时间不超过60秒
        self.assertLess(training_time, 60.0, 
                       f"训练时间过长: {training_time:.2f}秒")
        
        logger.info(f"模型训练性能: {training_time:.2f}秒")
    
    def test_prediction_latency(self):
        """测试预测延迟"""
        model = StockSelectionModel({'model_type': 'lightgbm'})
        model.train(self.X_large[:5000], self.y_large[:5000])  # 使用部分数据训练
        
        # 测试单次预测延迟
        X_single = self.X_large[0:1]
        
        latencies = []
        for _ in range(100):  # 多次测试取平均
            start_time = time.time()
            model.predict(X_single)
            latency = (time.time() - start_time) * 1000  # 转换为毫秒
            latencies.append(latency)
        
        avg_latency = np.mean(latencies)
        p95_latency = np.percentile(latencies, 95)
        
        # 性能要求：平均延迟不超过100ms，95%分位数不超过200ms
        self.assertLess(avg_latency, 100.0, 
                       f"平均预测延迟过高: {avg_latency:.2f}ms")
        self.assertLess(p95_latency, 200.0, 
                       f"P95预测延迟过高: {p95_latency:.2f}ms")
        
        logger.info(f"预测延迟 - 平均: {avg_latency:.2f}ms, P95: {p95_latency:.2f}ms")
    
    def test_memory_usage(self):
        """测试内存使用"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 执行内存密集操作
        model = StockSelectionModel({'model_type': 'lightgbm'})
        model.train(self.X_large, self.y_large)
        
        peak_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = peak_memory - initial_memory
        
        # 性能要求：内存增长不超过2GB
        self.assertLess(memory_increase, 2048, 
                       f"内存使用过多: {memory_increase:.2f}MB")
        
        logger.info(f"内存使用增长: {memory_increase:.2f}MB")
```

### 2. 负载测试

```python
import concurrent.futures
import threading

class TestLoadTesting(unittest.TestCase):
    """负载测试"""
    
    def setUp(self):
        """准备负载测试环境"""
        self.model = StockSelectionModel({'model_type': 'lightgbm'})
        
        # 训练模型
        X_train = np.random.randn(1000, 50)
        y_train = np.random.randn(1000)
        self.model.train(X_train, y_train)
        
        self.test_data = np.random.randn(100, 50)
    
    def test_concurrent_predictions(self):
        """测试并发预测"""
        num_threads = 10
        predictions_per_thread = 50
        
        def predict_worker():
            """预测工作线程"""
            results = []
            for _ in range(predictions_per_thread):
                try:
                    prediction = self.model.predict(self.test_data[:1])
                    results.append(prediction[0])
                except Exception as e:
                    results.append(None)
                    logger.error(f"并发预测失败: {e}")
            return results
        
        # 启动并发测试
        start_time = time.time()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = [executor.submit(predict_worker) for _ in range(num_threads)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        end_time = time.time()
        
        # 验证结果
        total_predictions = sum(len(result) for result in results)
        successful_predictions = sum(
            len([r for r in result if r is not None]) for result in results
        )
        
        success_rate = successful_predictions / total_predictions
        throughput = total_predictions / (end_time - start_time)
        
        # 性能要求：成功率不低于95%，吞吐量不低于100预测/秒
        self.assertGreaterEqual(success_rate, 0.95, 
                               f"并发预测成功率过低: {success_rate:.2%}")
        self.assertGreaterEqual(throughput, 100, 
                               f"并发预测吞吐量过低: {throughput:.2f}预测/秒")
        
        logger.info(f"并发测试结果 - 成功率: {success_rate:.2%}, 吞吐量: {throughput:.2f}预测/秒")
```

## 测试数据管理

### 1. 测试数据生成

```python
class TestDataGenerator:
    """测试数据生成器"""
    
    @staticmethod
    def generate_stock_data(
        symbols: List[str],
        start_date: str,
        end_date: str,
        frequency: str = 'D'
    ) -> pd.DataFrame:
        """生成股票数据"""
        date_range = pd.date_range(start_date, end_date, freq=frequency)
        
        data = []
        for symbol in symbols:
            for date in date_range:
                # 生成随机但合理的OHLCV数据
                base_price = np.random.uniform(10, 100)
                volatility = np.random.uniform(0.01, 0.05)
                
                open_price = base_price * (1 + np.random.normal(0, volatility))
                close_price = open_price * (1 + np.random.normal(0, volatility))
                high_price = max(open_price, close_price) * (1 + abs(np.random.normal(0, volatility/2)))
                low_price = min(open_price, close_price) * (1 - abs(np.random.normal(0, volatility/2)))
                volume = int(np.random.uniform(100000, 10000000))
                
                data.append({
                    'symbol': symbol,
                    'date': date,
                    'open': round(open_price, 2),
                    'high': round(high_price, 2),
                    'low': round(low_price, 2),
                    'close': round(close_price, 2),
                    'volume': volume
                })
        
        return pd.DataFrame(data)
    
    @staticmethod
    def generate_feature_data(n_samples: int, n_features: int, 
                            feature_names: List[str] = None) -> pd.DataFrame:
        """生成特征数据"""
        if feature_names is None:
            feature_names = [f'feature_{i}' for i in range(n_features)]
        
        # 生成不同类型的特征
        data = {}
        
        for i, name in enumerate(feature_names):
            if 'price' in name.lower():
                # 价格类特征：正值，对数正态分布
                data[name] = np.random.lognormal(mean=2, sigma=0.5, size=n_samples)
            elif 'ratio' in name.lower() or 'rate' in name.lower():
                # 比率类特征：可能为负值
                data[name] = np.random.normal(0, 0.1, size=n_samples)
            elif 'volume' in name.lower():
                # 成交量类特征：正整数
                data[name] = np.random.poisson(lam=1000000, size=n_samples)
            else:
                # 默认：标准正态分布
                data[name] = np.random.normal(0, 1, size=n_samples)
        
        return pd.DataFrame(data)
```

### 2. 测试数据验证

```python
class TestDataValidator:
    """测试数据验证器"""
    
    @staticmethod
    def validate_stock_data(data: pd.DataFrame) -> Dict[str, bool]:
        """验证股票数据"""
        checks = {}
        
        # 检查必需字段
        required_fields = ['symbol', 'date', 'open', 'high', 'low', 'close', 'volume']
        checks['has_required_fields'] = all(field in data.columns for field in required_fields)
        
        # 检查OHLC逻辑
        if checks['has_required_fields']:
            checks['ohlc_logic'] = (
                (data['high'] >= data[['open', 'low', 'close']].max(axis=1)).all() and
                (data['low'] <= data[['open', 'high', 'close']].min(axis=1)).all()
            )
        
        # 检查数据类型
        checks['correct_dtypes'] = (
            pd.api.types.is_numeric_dtype(data['open']) and
            pd.api.types.is_numeric_dtype(data['high']) and
            pd.api.types.is_numeric_dtype(data['low']) and
            pd.api.types.is_numeric_dtype(data['close']) and
            pd.api.types.is_numeric_dtype(data['volume'])
        )
        
        # 检查正值
        numeric_fields = ['open', 'high', 'low', 'close', 'volume']
        checks['positive_values'] = all(
            (data[field] > 0).all() for field in numeric_fields if field in data.columns
        )
        
        return checks
```

## 持续集成测试

### 1. 测试自动化脚本

```python
#!/usr/bin/env python3
"""
自动化测试脚本
"""

import sys
import subprocess
import argparse
from pathlib import Path

def run_tests(test_type: str = 'all', coverage: bool = True) -> bool:
    """运行测试"""
    
    test_commands = {
        'unit': ['python', '-m', 'pytest', 'test_unit/', '-v'],
        'integration': ['python', '-m', 'pytest', 'test_integration/', '-v'],
        'e2e': ['python', '-m', 'pytest', 'test_e2e/', '-v'],
        'performance': ['python', '-m', 'pytest', 'test_performance/', '-v']
    }
    
    if coverage:
        # 添加覆盖率参数
        for commands in test_commands.values():
            commands.extend(['--cov=qlib_trading_system', '--cov-report=html'])
    
    success = True
    
    if test_type == 'all':
        for test_name, command in test_commands.items():
            print(f"\n{'='*50}")
            print(f"运行 {test_name} 测试")
            print(f"{'='*50}")
            
            result = subprocess.run(command, capture_output=True, text=True)
            
            if result.returncode != 0:
                print(f"❌ {test_name} 测试失败")
                print(result.stdout)
                print(result.stderr)
                success = False
            else:
                print(f"✅ {test_name} 测试通过")
    else:
        if test_type in test_commands:
            result = subprocess.run(test_commands[test_type], capture_output=True, text=True)
            success = result.returncode == 0
            
            if not success:
                print(result.stdout)
                print(result.stderr)
    
    return success

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='运行测试套件')
    parser.add_argument('--type', choices=['all', 'unit', 'integration', 'e2e', 'performance'], 
                       default='all', help='测试类型')
    parser.add_argument('--no-coverage', action='store_true', help='不生成覆盖率报告')
    
    args = parser.parse_args()
    
    success = run_tests(args.type, not args.no_coverage)
    sys.exit(0 if success else 1)
```

### 2. 测试报告生成

```python
class TestReportGenerator:
    """测试报告生成器"""
    
    def __init__(self, output_dir: str = "test_reports"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
    
    def generate_summary_report(self, test_results: Dict) -> str:
        """生成测试摘要报告"""
        report_path = self.output_dir / "test_summary.html"
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>测试报告摘要</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ text-align: center; color: #333; }}
                .summary {{ display: flex; justify-content: space-around; margin: 20px 0; }}
                .metric {{ border: 1px solid #ddd; padding: 15px; border-radius: 5px; text-align: center; }}
                .passed {{ background-color: #d4edda; }}
                .failed {{ background-color: #f8d7da; }}
                .details {{ margin: 20px 0; }}
                table {{ width: 100%; border-collapse: collapse; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Qlib交易系统测试报告</h1>
                <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>
            
            <div class="summary">
                <div class="metric passed">
                    <h3>通过测试</h3>
                    <p>{test_results.get('passed', 0)}</p>
                </div>
                <div class="metric failed">
                    <h3>失败测试</h3>
                    <p>{test_results.get('failed', 0)}</p>
                </div>
                <div class="metric">
                    <h3>总覆盖率</h3>
                    <p>{test_results.get('coverage', 0):.1f}%</p>
                </div>
            </div>
            
            <div class="details">
                <h2>详细结果</h2>
                <table>
                    <tr>
                        <th>测试类型</th>
                        <th>通过</th>
                        <th>失败</th>
                        <th>覆盖率</th>
                        <th>状态</th>
                    </tr>
        """
        
        for test_type, results in test_results.get('details', {}).items():
            status = "✅" if results['failed'] == 0 else "❌"
            html_content += f"""
                    <tr>
                        <td>{test_type}</td>
                        <td>{results['passed']}</td>
                        <td>{results['failed']}</td>
                        <td>{results['coverage']:.1f}%</td>
                        <td>{status}</td>
                    </tr>
            """
        
        html_content += """
                </table>
            </div>
        </body>
        </html>
        """
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        return str(report_path)
```

## 测试最佳实践总结

### 1. 测试设计原则
- **独立性**: 测试之间不应相互依赖
- **可重复性**: 测试结果应该一致可重现
- **快速执行**: 单元测试应该快速执行
- **清晰断言**: 使用明确的断言和错误消息

### 2. 测试数据管理
- 使用固定的随机种子确保可重现性
- 创建专用的测试数据生成器
- 避免使用生产数据进行测试
- 及时清理测试产生的临时文件

### 3. Mock和Stub使用
- 对外部依赖使用Mock
- 保持Mock的简单性
- 验证Mock的调用
- 避免过度Mock导致测试失去意义

### 4. 性能测试注意事项
- 设置合理的性能基准
- 考虑测试环境的影响
- 多次运行取平均值
- 监控资源使用情况