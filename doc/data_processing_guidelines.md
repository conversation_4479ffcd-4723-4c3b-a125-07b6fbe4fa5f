---
inclusion: fileMatch
fileMatchPattern: 'qlib_trading_system/data/**/*.py'
---

# 数据处理指导原则

## 数据架构设计

### 1. 数据分层架构

```
原始数据层 (Raw Data Layer)
├── 市场数据 (Market Data)
├── 基本面数据 (Fundamental Data)
└── 新闻情感数据 (News Sentiment Data)

清洗数据层 (Cleaned Data Layer)
├── 标准化数据 (Standardized Data)
├── 质量检查数据 (Quality Checked Data)
└── 缺失值处理数据 (Missing Value Handled Data)

特征数据层 (Feature Data Layer)
├── 技术指标 (Technical Indicators)
├── 基本面指标 (Fundamental Indicators)
└── 衍生特征 (Derived Features)

模型数据层 (Model Data Layer)
├── 训练数据集 (Training Dataset)
├── 验证数据集 (Validation Dataset)
└── 测试数据集 (Test Dataset)
```

### 2. 数据源管理

#### 支持的数据源
- **iTick**: 主要数据提供商（推荐，性价比高）
- **JoinQuant**: 备用基本面数据
- **RiceQuant**: 替代数据源
- **AkShare**: 中国金融数据集成

#### 数据源适配器模式
```python
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Union
import pandas as pd

class DataSourceAdapter(ABC):
    """数据源适配器基类"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.connection = None
        self.is_connected = False
    
    @abstractmethod
    def connect(self) -> bool:
        """连接数据源"""
        pass
    
    @abstractmethod
    def disconnect(self) -> bool:
        """断开连接"""
        pass
    
    @abstractmethod
    def get_stock_data(self, 
                      symbols: List[str], 
                      start_date: str, 
                      end_date: str,
                      fields: Optional[List[str]] = None) -> pd.DataFrame:
        """获取股票数据"""
        pass
    
    @abstractmethod
    def get_market_data(self, 
                       date: str,
                       market: str = 'A') -> pd.DataFrame:
        """获取市场数据"""
        pass
    
    def validate_data(self, data: pd.DataFrame) -> bool:
        """验证数据质量"""
        if data.empty:
            logger.warning("数据为空")
            return False
        
        # 检查必需字段
        required_fields = ['symbol', 'date', 'open', 'high', 'low', 'close', 'volume']
        missing_fields = [field for field in required_fields if field not in data.columns]
        
        if missing_fields:
            logger.error(f"缺少必需字段: {missing_fields}")
            return False
        
        # 检查数据类型
        numeric_fields = ['open', 'high', 'low', 'close', 'volume']
        for field in numeric_fields:
            if not pd.api.types.is_numeric_dtype(data[field]):
                logger.error(f"字段 {field} 不是数值类型")
                return False
        
        return True
```

### 3. 数据质量管理

#### 数据质量检查
```python
class DataQualityChecker:
    """数据质量检查器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.quality_rules = self._load_quality_rules()
    
    def check_data_quality(self, data: pd.DataFrame) -> Dict:
        """检查数据质量"""
        quality_report = {
            'total_records': len(data),
            'quality_score': 0.0,
            'issues': [],
            'warnings': [],
            'passed_checks': []
        }
        
        # 1. 缺失值检查
        missing_check = self._check_missing_values(data)
        quality_report['issues'].extend(missing_check['issues'])
        quality_report['warnings'].extend(missing_check['warnings'])
        
        # 2. 异常值检查
        outlier_check = self._check_outliers(data)
        quality_report['issues'].extend(outlier_check['issues'])
        quality_report['warnings'].extend(outlier_check['warnings'])
        
        # 3. 数据一致性检查
        consistency_check = self._check_consistency(data)
        quality_report['issues'].extend(consistency_check['issues'])
        
        # 4. 时间序列完整性检查
        if 'date' in data.columns:
            time_check = self._check_time_series_completeness(data)
            quality_report['issues'].extend(time_check['issues'])
        
        # 计算质量分数
        total_checks = 4
        failed_checks = len(quality_report['issues'])
        quality_report['quality_score'] = max(0, (total_checks - failed_checks) / total_checks)
        
        return quality_report
    
    def _check_missing_values(self, data: pd.DataFrame) -> Dict:
        """检查缺失值"""
        result = {'issues': [], 'warnings': []}
        
        missing_ratio = data.isnull().sum() / len(data)
        
        for column, ratio in missing_ratio.items():
            if ratio > 0.5:  # 超过50%缺失
                result['issues'].append(f"列 {column} 缺失值过多: {ratio:.2%}")
            elif ratio > 0.1:  # 超过10%缺失
                result['warnings'].append(f"列 {column} 缺失值较多: {ratio:.2%}")
        
        return result
    
    def _check_outliers(self, data: pd.DataFrame) -> Dict:
        """检查异常值"""
        result = {'issues': [], 'warnings': []}
        
        numeric_columns = data.select_dtypes(include=[np.number]).columns
        
        for column in numeric_columns:
            # 使用IQR方法检测异常值
            Q1 = data[column].quantile(0.25)
            Q3 = data[column].quantile(0.75)
            IQR = Q3 - Q1
            
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            outliers = data[(data[column] < lower_bound) | (data[column] > upper_bound)]
            outlier_ratio = len(outliers) / len(data)
            
            if outlier_ratio > 0.05:  # 超过5%异常值
                result['warnings'].append(f"列 {column} 异常值较多: {outlier_ratio:.2%}")
        
        return result
    
    def _check_consistency(self, data: pd.DataFrame) -> Dict:
        """检查数据一致性"""
        result = {'issues': []}
        
        # 检查OHLC数据一致性
        if all(col in data.columns for col in ['open', 'high', 'low', 'close']):
            # High应该是最高价
            invalid_high = data[data['high'] < data[['open', 'low', 'close']].max(axis=1)]
            if len(invalid_high) > 0:
                result['issues'].append(f"发现 {len(invalid_high)} 条记录的最高价不正确")
            
            # Low应该是最低价
            invalid_low = data[data['low'] > data[['open', 'high', 'close']].min(axis=1)]
            if len(invalid_low) > 0:
                result['issues'].append(f"发现 {len(invalid_low)} 条记录的最低价不正确")
        
        # 检查价格为负值
        price_columns = ['open', 'high', 'low', 'close']
        for col in price_columns:
            if col in data.columns:
                negative_prices = data[data[col] <= 0]
                if len(negative_prices) > 0:
                    result['issues'].append(f"发现 {len(negative_prices)} 条记录的 {col} 价格为负值或零")
        
        return result
```

### 4. 数据预处理标准

#### 缺失值处理策略
```python
class MissingValueHandler:
    """缺失值处理器"""
    
    def __init__(self, strategy: str = 'auto'):
        self.strategy = strategy
        self.fill_values = {}
    
    def handle_missing_values(self, data: pd.DataFrame) -> pd.DataFrame:
        """处理缺失值"""
        data_cleaned = data.copy()
        
        for column in data.columns:
            missing_ratio = data[column].isnull().sum() / len(data)
            
            if missing_ratio == 0:
                continue
            elif missing_ratio > 0.5:
                # 缺失值过多，考虑删除列
                logger.warning(f"列 {column} 缺失值过多 ({missing_ratio:.2%})，建议删除")
                continue
            
            # 根据数据类型选择填充策略
            if pd.api.types.is_numeric_dtype(data[column]):
                data_cleaned[column] = self._handle_numeric_missing(data[column])
            elif pd.api.types.is_categorical_dtype(data[column]) or data[column].dtype == 'object':
                data_cleaned[column] = self._handle_categorical_missing(data[column])
            elif pd.api.types.is_datetime64_any_dtype(data[column]):
                data_cleaned[column] = self._handle_datetime_missing(data[column])
        
        return data_cleaned
    
    def _handle_numeric_missing(self, series: pd.Series) -> pd.Series:
        """处理数值型缺失值"""
        if self.strategy == 'forward_fill':
            return series.fillna(method='ffill')
        elif self.strategy == 'backward_fill':
            return series.fillna(method='bfill')
        elif self.strategy == 'interpolate':
            return series.interpolate()
        else:  # 默认使用中位数填充
            return series.fillna(series.median())
    
    def _handle_categorical_missing(self, series: pd.Series) -> pd.Series:
        """处理分类型缺失值"""
        # 使用众数填充
        mode_value = series.mode()
        if len(mode_value) > 0:
            return series.fillna(mode_value[0])
        else:
            return series.fillna('Unknown')
    
    def _handle_datetime_missing(self, series: pd.Series) -> pd.Series:
        """处理日期时间缺失值"""
        return series.fillna(method='ffill')
```

#### 异常值处理
```python
class OutlierHandler:
    """异常值处理器"""
    
    def __init__(self, method: str = 'iqr', threshold: float = 1.5):
        self.method = method
        self.threshold = threshold
    
    def detect_outliers(self, data: pd.DataFrame) -> Dict[str, pd.Index]:
        """检测异常值"""
        outliers = {}
        numeric_columns = data.select_dtypes(include=[np.number]).columns
        
        for column in numeric_columns:
            if self.method == 'iqr':
                outlier_indices = self._detect_iqr_outliers(data[column])
            elif self.method == 'zscore':
                outlier_indices = self._detect_zscore_outliers(data[column])
            elif self.method == 'isolation_forest':
                outlier_indices = self._detect_isolation_forest_outliers(data[column])
            else:
                continue
            
            if len(outlier_indices) > 0:
                outliers[column] = outlier_indices
        
        return outliers
    
    def handle_outliers(self, data: pd.DataFrame, action: str = 'clip') -> pd.DataFrame:
        """处理异常值"""
        data_processed = data.copy()
        outliers = self.detect_outliers(data)
        
        for column, outlier_indices in outliers.items():
            if action == 'remove':
                # 删除异常值行
                data_processed = data_processed.drop(outlier_indices)
            elif action == 'clip':
                # 截断异常值
                Q1 = data[column].quantile(0.25)
                Q3 = data[column].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - self.threshold * IQR
                upper_bound = Q3 + self.threshold * IQR
                
                data_processed[column] = data_processed[column].clip(lower_bound, upper_bound)
            elif action == 'transform':
                # 使用对数变换
                if (data[column] > 0).all():
                    data_processed[column] = np.log1p(data[column])
        
        return data_processed
```

### 5. 数据存储标准

#### 存储格式选择
- **时序数据**: 使用ClickHouse，支持高效的时间范围查询
- **配置数据**: 使用MongoDB，支持灵活的文档结构
- **缓存数据**: 使用Redis，支持快速读写
- **文件存储**: 使用Parquet格式，支持列式存储和压缩

#### 数据分区策略
```python
class DataPartitioner:
    """数据分区器"""
    
    def __init__(self, partition_strategy: str = 'date'):
        self.partition_strategy = partition_strategy
    
    def partition_data(self, data: pd.DataFrame, partition_column: str) -> Dict[str, pd.DataFrame]:
        """数据分区"""
        partitions = {}
        
        if self.partition_strategy == 'date':
            # 按日期分区
            data['partition_key'] = pd.to_datetime(data[partition_column]).dt.strftime('%Y-%m-%d')
        elif self.partition_strategy == 'month':
            # 按月分区
            data['partition_key'] = pd.to_datetime(data[partition_column]).dt.strftime('%Y-%m')
        elif self.partition_strategy == 'year':
            # 按年分区
            data['partition_key'] = pd.to_datetime(data[partition_column]).dt.strftime('%Y')
        
        for partition_key, group in data.groupby('partition_key'):
            partitions[partition_key] = group.drop('partition_key', axis=1)
        
        return partitions
```

### 6. 数据管道设计

#### ETL流水线
```python
class DataPipeline:
    """数据处理流水线"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.processors = []
        self.quality_checker = DataQualityChecker(config)
    
    def add_processor(self, processor):
        """添加处理器"""
        self.processors.append(processor)
    
    def process(self, data: pd.DataFrame) -> pd.DataFrame:
        """执行数据处理流水线"""
        processed_data = data.copy()
        
        # 数据质量检查
        quality_report = self.quality_checker.check_data_quality(processed_data)
        if quality_report['quality_score'] < 0.7:
            logger.warning(f"数据质量较低: {quality_report['quality_score']:.2f}")
        
        # 依次执行处理器
        for i, processor in enumerate(self.processors):
            try:
                logger.info(f"执行处理器 {i+1}: {processor.__class__.__name__}")
                processed_data = processor.process(processed_data)
                
                # 验证处理结果
                if processed_data.empty:
                    raise ValueError(f"处理器 {processor.__class__.__name__} 返回空数据")
                
            except Exception as e:
                logger.error(f"处理器 {processor.__class__.__name__} 执行失败: {e}")
                raise
        
        return processed_data
```

### 7. 实时数据处理

#### 流式数据处理
```python
import asyncio
from typing import AsyncGenerator

class StreamDataProcessor:
    """流式数据处理器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.buffer_size = config.get('buffer_size', 1000)
        self.batch_timeout = config.get('batch_timeout', 5.0)
        self.data_buffer = []
    
    async def process_stream(self, data_stream: AsyncGenerator) -> AsyncGenerator:
        """处理数据流"""
        async for data_batch in self._batch_data(data_stream):
            try:
                # 处理批次数据
                processed_batch = self._process_batch(data_batch)
                yield processed_batch
                
            except Exception as e:
                logger.error(f"批次数据处理失败: {e}")
                continue
    
    async def _batch_data(self, data_stream: AsyncGenerator) -> AsyncGenerator:
        """数据批处理"""
        buffer = []
        last_batch_time = asyncio.get_event_loop().time()
        
        async for data_point in data_stream:
            buffer.append(data_point)
            current_time = asyncio.get_event_loop().time()
            
            # 检查是否需要输出批次
            if (len(buffer) >= self.buffer_size or 
                current_time - last_batch_time >= self.batch_timeout):
                
                if buffer:
                    yield buffer
                    buffer = []
                    last_batch_time = current_time
        
        # 输出剩余数据
        if buffer:
            yield buffer
```

### 8. 数据监控和报警

#### 数据监控指标
- **数据新鲜度**: 最新数据的时间戳
- **数据完整性**: 缺失数据的比例
- **数据质量**: 异常值和错误数据的比例
- **处理延迟**: 数据处理的时间延迟
- **吞吐量**: 单位时间处理的数据量

#### 报警规则
```python
class DataMonitoringAlert:
    """数据监控报警"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.alert_rules = self._load_alert_rules()
    
    def check_data_freshness(self, latest_timestamp: datetime) -> bool:
        """检查数据新鲜度"""
        max_delay = timedelta(minutes=self.config.get('max_data_delay_minutes', 30))
        current_time = datetime.now()
        
        if current_time - latest_timestamp > max_delay:
            self._send_alert(
                level='WARNING',
                message=f"数据延迟过长: {current_time - latest_timestamp}"
            )
            return False
        
        return True
    
    def check_data_completeness(self, expected_count: int, actual_count: int) -> bool:
        """检查数据完整性"""
        completeness_ratio = actual_count / expected_count if expected_count > 0 else 0
        min_completeness = self.config.get('min_completeness_ratio', 0.95)
        
        if completeness_ratio < min_completeness:
            self._send_alert(
                level='ERROR',
                message=f"数据完整性不足: {completeness_ratio:.2%}"
            )
            return False
        
        return True
```

## 性能优化建议

### 1. 数据读取优化
- 使用列式存储格式（Parquet）
- 实现数据分区和索引
- 批量读取减少I/O次数

### 2. 内存管理
- 使用数据类型优化（如category类型）
- 及时释放不需要的数据
- 使用内存映射文件

### 3. 并行处理
- 使用多进程处理大数据集
- 实现异步I/O操作
- 利用向量化操作