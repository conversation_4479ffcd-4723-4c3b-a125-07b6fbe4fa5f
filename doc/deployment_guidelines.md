---
inclusion: fileMatch
fileMatchPattern: '**/deploy*.py'
---

# 部署指导原则

## 部署架构设计

### 1. 环境分层

```
生产环境 (Production)
├── 负载均衡器 (Load Balancer)
├── 应用服务器集群 (App Server Cluster)
├── 数据库集群 (Database Cluster)
└── 监控系统 (Monitoring System)

预生产环境 (Staging)
├── 应用服务器 (App Server)
├── 数据库 (Database)
└── 测试工具 (Testing Tools)

开发环境 (Development)
├── 本地开发服务器 (Local Dev Server)
├── 开发数据库 (Dev Database)
└── 调试工具 (Debug Tools)
```

### 2. 部署策略

#### 蓝绿部署 (Blue-Green Deployment)
```python
class BlueGreenDeployment:
    """蓝绿部署管理器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.current_env = 'blue'  # 当前活跃环境
        self.environments = {
            'blue': BlueEnvironment(config),
            'green': GreenEnvironment(config)
        }
    
    def deploy_new_version(self, version: str, model_path: str) -> bool:
        """部署新版本"""
        try:
            # 确定目标环境
            target_env = 'green' if self.current_env == 'blue' else 'blue'
            
            logger.info(f"开始部署版本 {version} 到 {target_env} 环境")
            
            # 1. 部署到目标环境
            success = self.environments[target_env].deploy(version, model_path)
            if not success:
                raise DeploymentError(f"部署到 {target_env} 环境失败")
            
            # 2. 健康检查
            if not self._health_check(target_env):
                raise DeploymentError(f"{target_env} 环境健康检查失败")
            
            # 3. 流量切换
            self._switch_traffic(target_env)
            
            # 4. 更新当前环境标记
            self.current_env = target_env
            
            logger.info(f"版本 {version} 部署成功，当前环境: {target_env}")
            return True
            
        except Exception as e:
            logger.error(f"部署失败: {e}")
            # 回滚操作
            self._rollback()
            return False
    
    def _health_check(self, env_name: str) -> bool:
        """健康检查"""
        env = self.environments[env_name]
        
        # 检查服务状态
        if not env.is_service_running():
            return False
        
        # 检查模型加载状态
        if not env.is_model_loaded():
            return False
        
        # 检查预测功能
        test_data = self._generate_test_data()
        try:
            predictions = env.predict(test_data)
            if predictions is None or len(predictions) == 0:
                return False
        except Exception:
            return False
        
        return True
    
    def _switch_traffic(self, target_env: str):
        """切换流量"""
        # 更新负载均衡器配置
        load_balancer = LoadBalancer(self.config)
        load_balancer.switch_traffic_to(target_env)
        
        # 等待流量切换完成
        time.sleep(self.config.get('traffic_switch_delay', 30))
    
    def _rollback(self):
        """回滚操作"""
        logger.warning("执行回滚操作")
        # 确保流量指向稳定环境
        self._switch_traffic(self.current_env)
```

#### 金丝雀部署 (Canary Deployment)
```python
class CanaryDeployment:
    """金丝雀部署管理器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.canary_traffic_percentage = config.get('canary_traffic_percentage', 5)
        self.monitoring_window = config.get('monitoring_window_minutes', 30)
    
    def deploy_canary(self, version: str, model_path: str) -> bool:
        """部署金丝雀版本"""
        try:
            # 1. 部署金丝雀实例
            canary_instance = self._deploy_canary_instance(version, model_path)
            
            # 2. 配置流量分配
            self._configure_traffic_split(self.canary_traffic_percentage)
            
            # 3. 监控金丝雀性能
            monitoring_result = self._monitor_canary_performance()
            
            # 4. 根据监控结果决定是否全量部署
            if monitoring_result['is_healthy']:
                return self._promote_canary_to_production()
            else:
                self._rollback_canary()
                return False
                
        except Exception as e:
            logger.error(f"金丝雀部署失败: {e}")
            self._rollback_canary()
            return False
    
    def _monitor_canary_performance(self) -> Dict:
        """监控金丝雀性能"""
        start_time = datetime.now()
        end_time = start_time + timedelta(minutes=self.monitoring_window)
        
        metrics = {
            'error_rate': [],
            'response_time': [],
            'prediction_accuracy': []
        }
        
        while datetime.now() < end_time:
            # 收集性能指标
            current_metrics = self._collect_canary_metrics()
            
            for metric_name, value in current_metrics.items():
                if metric_name in metrics:
                    metrics[metric_name].append(value)
            
            time.sleep(60)  # 每分钟收集一次
        
        # 分析监控结果
        return self._analyze_canary_metrics(metrics)
    
    def _analyze_canary_metrics(self, metrics: Dict) -> Dict:
        """分析金丝雀指标"""
        result = {'is_healthy': True, 'issues': []}
        
        # 检查错误率
        if metrics['error_rate']:
            avg_error_rate = np.mean(metrics['error_rate'])
            if avg_error_rate > self.config.get('max_error_rate', 0.01):
                result['is_healthy'] = False
                result['issues'].append(f"错误率过高: {avg_error_rate:.2%}")
        
        # 检查响应时间
        if metrics['response_time']:
            avg_response_time = np.mean(metrics['response_time'])
            if avg_response_time > self.config.get('max_response_time', 1000):
                result['is_healthy'] = False
                result['issues'].append(f"响应时间过长: {avg_response_time:.2f}ms")
        
        # 检查预测准确率
        if metrics['prediction_accuracy']:
            avg_accuracy = np.mean(metrics['prediction_accuracy'])
            if avg_accuracy < self.config.get('min_accuracy', 0.7):
                result['is_healthy'] = False
                result['issues'].append(f"预测准确率过低: {avg_accuracy:.2%}")
        
        return result
```

### 3. 容器化部署

#### Docker配置
```dockerfile
# Dockerfile
FROM python:3.8-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY qlib_trading_system/ ./qlib_trading_system/
COPY config/ ./config/
COPY main.py .

# 创建非root用户
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD python -c "import requests; requests.get('http://localhost:8000/health')"

# 启动命令
CMD ["python", "main.py", "--mode", "production"]
```

#### Docker Compose配置
```yaml
# docker-compose.yml
version: '3.8'

services:
  qlib-trading-system:
    build: .
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=production
      - LOG_LEVEL=INFO
      - DATABASE_URL=**********************************/qlib_db
      - REDIS_URL=redis://redis:6379/0
    volumes:
      - ./models:/app/models
      - ./logs:/app/logs
    depends_on:
      - db
      - redis
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'

  db:
    image: postgres:13
    environment:
      - POSTGRES_DB=qlib_db
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  redis:
    image: redis:6-alpine
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - qlib-trading-system
    restart: unless-stopped

volumes:
  postgres_data:
```

### 4. 配置管理

#### 环境配置
```python
class DeploymentConfig:
    """部署配置管理"""
    
    def __init__(self, environment: str):
        self.environment = environment
        self.config = self._load_environment_config()
    
    def _load_environment_config(self) -> Dict:
        """加载环境配置"""
        base_config = {
            'app_name': 'qlib-trading-system',
            'version': os.getenv('APP_VERSION', '1.0.0'),
            'log_level': 'INFO'
        }
        
        env_configs = {
            'development': {
                'debug': True,
                'database_url': 'sqlite:///dev.db',
                'redis_url': 'redis://localhost:6379/0',
                'model_update_interval': 3600,  # 1小时
                'enable_monitoring': False
            },
            'staging': {
                'debug': False,
                'database_url': os.getenv('STAGING_DATABASE_URL'),
                'redis_url': os.getenv('STAGING_REDIS_URL'),
                'model_update_interval': 1800,  # 30分钟
                'enable_monitoring': True
            },
            'production': {
                'debug': False,
                'database_url': os.getenv('PRODUCTION_DATABASE_URL'),
                'redis_url': os.getenv('PRODUCTION_REDIS_URL'),
                'model_update_interval': 900,  # 15分钟
                'enable_monitoring': True,
                'enable_alerting': True,
                'max_workers': 4,
                'request_timeout': 30
            }
        }
        
        config = base_config.copy()
        config.update(env_configs.get(self.environment, {}))
        
        return config
    
    def validate_config(self) -> List[str]:
        """验证配置"""
        errors = []
        
        # 检查必需的环境变量
        required_vars = {
            'production': [
                'PRODUCTION_DATABASE_URL',
                'PRODUCTION_REDIS_URL',
                'SECRET_KEY',
                'API_KEY'
            ],
            'staging': [
                'STAGING_DATABASE_URL',
                'STAGING_REDIS_URL'
            ]
        }
        
        for var in required_vars.get(self.environment, []):
            if not os.getenv(var):
                errors.append(f"缺少环境变量: {var}")
        
        # 检查数据库连接
        try:
            self._test_database_connection()
        except Exception as e:
            errors.append(f"数据库连接失败: {e}")
        
        # 检查Redis连接
        try:
            self._test_redis_connection()
        except Exception as e:
            errors.append(f"Redis连接失败: {e}")
        
        return errors
```

### 5. 健康检查和监控

#### 健康检查端点
```python
from flask import Flask, jsonify
import psutil
import time

app = Flask(__name__)

class HealthChecker:
    """健康检查器"""
    
    def __init__(self, model_manager):
        self.model_manager = model_manager
        self.start_time = time.time()
    
    @app.route('/health')
    def health_check(self):
        """基础健康检查"""
        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'uptime': time.time() - self.start_time
        })
    
    @app.route('/health/detailed')
    def detailed_health_check(self):
        """详细健康检查"""
        checks = {
            'database': self._check_database(),
            'redis': self._check_redis(),
            'model': self._check_model(),
            'memory': self._check_memory(),
            'disk': self._check_disk()
        }
        
        overall_status = 'healthy' if all(
            check['status'] == 'healthy' for check in checks.values()
        ) else 'unhealthy'
        
        return jsonify({
            'status': overall_status,
            'timestamp': datetime.now().isoformat(),
            'checks': checks
        })
    
    def _check_database(self) -> Dict:
        """检查数据库连接"""
        try:
            # 执行简单查询
            with get_db_connection() as conn:
                conn.execute("SELECT 1")
            
            return {'status': 'healthy', 'message': '数据库连接正常'}
        except Exception as e:
            return {'status': 'unhealthy', 'message': f'数据库连接失败: {e}'}
    
    def _check_redis(self) -> Dict:
        """检查Redis连接"""
        try:
            redis_client = get_redis_client()
            redis_client.ping()
            
            return {'status': 'healthy', 'message': 'Redis连接正常'}
        except Exception as e:
            return {'status': 'unhealthy', 'message': f'Redis连接失败: {e}'}
    
    def _check_model(self) -> Dict:
        """检查模型状态"""
        try:
            if not self.model_manager.is_model_loaded():
                return {'status': 'unhealthy', 'message': '模型未加载'}
            
            # 执行测试预测
            test_data = np.random.randn(1, 10)
            prediction = self.model_manager.predict(test_data)
            
            if prediction is None or np.isnan(prediction).any():
                return {'status': 'unhealthy', 'message': '模型预测异常'}
            
            return {'status': 'healthy', 'message': '模型运行正常'}
        except Exception as e:
            return {'status': 'unhealthy', 'message': f'模型检查失败: {e}'}
    
    def _check_memory(self) -> Dict:
        """检查内存使用"""
        memory = psutil.virtual_memory()
        memory_usage = memory.percent
        
        if memory_usage > 90:
            return {'status': 'unhealthy', 'message': f'内存使用过高: {memory_usage:.1f}%'}
        elif memory_usage > 80:
            return {'status': 'warning', 'message': f'内存使用较高: {memory_usage:.1f}%'}
        else:
            return {'status': 'healthy', 'message': f'内存使用正常: {memory_usage:.1f}%'}
    
    def _check_disk(self) -> Dict:
        """检查磁盘使用"""
        disk = psutil.disk_usage('/')
        disk_usage = (disk.used / disk.total) * 100
        
        if disk_usage > 90:
            return {'status': 'unhealthy', 'message': f'磁盘使用过高: {disk_usage:.1f}%'}
        elif disk_usage > 80:
            return {'status': 'warning', 'message': f'磁盘使用较高: {disk_usage:.1f}%'}
        else:
            return {'status': 'healthy', 'message': f'磁盘使用正常: {disk_usage:.1f}%'}
```

### 6. 日志和监控

#### 结构化日志
```python
import structlog
import json
from datetime import datetime

class StructuredLogger:
    """结构化日志记录器"""
    
    def __init__(self, service_name: str, environment: str):
        self.service_name = service_name
        self.environment = environment
        
        # 配置structlog
        structlog.configure(
            processors=[
                structlog.stdlib.filter_by_level,
                structlog.stdlib.add_logger_name,
                structlog.stdlib.add_log_level,
                structlog.stdlib.PositionalArgumentsFormatter(),
                structlog.processors.TimeStamper(fmt="iso"),
                structlog.processors.StackInfoRenderer(),
                structlog.processors.format_exc_info,
                self._add_service_info,
                structlog.processors.JSONRenderer()
            ],
            context_class=dict,
            logger_factory=structlog.stdlib.LoggerFactory(),
            wrapper_class=structlog.stdlib.BoundLogger,
            cache_logger_on_first_use=True,
        )
        
        self.logger = structlog.get_logger()
    
    def _add_service_info(self, logger, method_name, event_dict):
        """添加服务信息"""
        event_dict['service'] = self.service_name
        event_dict['environment'] = self.environment
        return event_dict
    
    def log_request(self, request_id: str, method: str, path: str, 
                   user_id: str = None, duration: float = None):
        """记录请求日志"""
        self.logger.info(
            "api_request",
            request_id=request_id,
            method=method,
            path=path,
            user_id=user_id,
            duration_ms=duration * 1000 if duration else None
        )
    
    def log_model_prediction(self, model_version: str, prediction_id: str,
                           input_features: int, prediction_value: float,
                           confidence: float = None, duration: float = None):
        """记录模型预测日志"""
        self.logger.info(
            "model_prediction",
            model_version=model_version,
            prediction_id=prediction_id,
            input_features=input_features,
            prediction_value=prediction_value,
            confidence=confidence,
            duration_ms=duration * 1000 if duration else None
        )
    
    def log_error(self, error_type: str, error_message: str, 
                 context: Dict = None, stack_trace: str = None):
        """记录错误日志"""
        self.logger.error(
            "application_error",
            error_type=error_type,
            error_message=error_message,
            context=context or {},
            stack_trace=stack_trace
        )
```

#### 指标收集
```python
from prometheus_client import Counter, Histogram, Gauge, start_http_server

class MetricsCollector:
    """指标收集器"""
    
    def __init__(self):
        # 请求计数器
        self.request_count = Counter(
            'http_requests_total',
            'Total HTTP requests',
            ['method', 'endpoint', 'status']
        )
        
        # 请求延迟直方图
        self.request_duration = Histogram(
            'http_request_duration_seconds',
            'HTTP request duration',
            ['method', 'endpoint']
        )
        
        # 模型预测计数器
        self.prediction_count = Counter(
            'model_predictions_total',
            'Total model predictions',
            ['model_version', 'status']
        )
        
        # 模型预测延迟
        self.prediction_duration = Histogram(
            'model_prediction_duration_seconds',
            'Model prediction duration',
            ['model_version']
        )
        
        # 系统资源指标
        self.memory_usage = Gauge(
            'system_memory_usage_percent',
            'System memory usage percentage'
        )
        
        self.cpu_usage = Gauge(
            'system_cpu_usage_percent',
            'System CPU usage percentage'
        )
        
        # 启动指标服务器
        start_http_server(8001)
    
    def record_request(self, method: str, endpoint: str, status: int, duration: float):
        """记录请求指标"""
        self.request_count.labels(method=method, endpoint=endpoint, status=status).inc()
        self.request_duration.labels(method=method, endpoint=endpoint).observe(duration)
    
    def record_prediction(self, model_version: str, status: str, duration: float):
        """记录预测指标"""
        self.prediction_count.labels(model_version=model_version, status=status).inc()
        self.prediction_duration.labels(model_version=model_version).observe(duration)
    
    def update_system_metrics(self):
        """更新系统指标"""
        import psutil
        
        # 更新内存使用率
        memory = psutil.virtual_memory()
        self.memory_usage.set(memory.percent)
        
        # 更新CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        self.cpu_usage.set(cpu_percent)
```

### 7. 自动化部署脚本

#### 部署脚本
```bash
#!/bin/bash
# deploy.sh - 自动化部署脚本

set -e  # 遇到错误立即退出

# 配置变量
APP_NAME="qlib-trading-system"
ENVIRONMENT=${1:-staging}
VERSION=${2:-latest}
DOCKER_REGISTRY="your-registry.com"
NAMESPACE="qlib-system"

echo "开始部署 $APP_NAME 到 $ENVIRONMENT 环境，版本: $VERSION"

# 1. 验证环境
validate_environment() {
    echo "验证部署环境..."
    
    # 检查kubectl连接
    if ! kubectl cluster-info > /dev/null 2>&1; then
        echo "错误: 无法连接到Kubernetes集群"
        exit 1
    fi
    
    # 检查命名空间
    if ! kubectl get namespace $NAMESPACE > /dev/null 2>&1; then
        echo "创建命名空间: $NAMESPACE"
        kubectl create namespace $NAMESPACE
    fi
    
    echo "环境验证完成"
}

# 2. 构建和推送镜像
build_and_push_image() {
    echo "构建Docker镜像..."
    
    IMAGE_TAG="$DOCKER_REGISTRY/$APP_NAME:$VERSION"
    
    # 构建镜像
    docker build -t $IMAGE_TAG .
    
    # 推送镜像
    docker push $IMAGE_TAG
    
    echo "镜像构建和推送完成: $IMAGE_TAG"
}

# 3. 更新Kubernetes配置
update_k8s_config() {
    echo "更新Kubernetes配置..."
    
    # 替换配置文件中的变量
    sed -e "s/{{IMAGE_TAG}}/$DOCKER_REGISTRY\/$APP_NAME:$VERSION/g" \
        -e "s/{{ENVIRONMENT}}/$ENVIRONMENT/g" \
        -e "s/{{NAMESPACE}}/$NAMESPACE/g" \
        k8s/deployment-template.yaml > k8s/deployment.yaml
    
    # 应用配置
    kubectl apply -f k8s/deployment.yaml -n $NAMESPACE
    
    echo "Kubernetes配置更新完成"
}

# 4. 等待部署完成
wait_for_deployment() {
    echo "等待部署完成..."
    
    kubectl rollout status deployment/$APP_NAME -n $NAMESPACE --timeout=300s
    
    if [ $? -eq 0 ]; then
        echo "部署成功完成"
    else
        echo "部署超时或失败"
        exit 1
    fi
}

# 5. 健康检查
health_check() {
    echo "执行健康检查..."
    
    # 获取服务端点
    SERVICE_IP=$(kubectl get service $APP_NAME -n $NAMESPACE -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
    
    if [ -z "$SERVICE_IP" ]; then
        SERVICE_IP=$(kubectl get service $APP_NAME -n $NAMESPACE -o jsonpath='{.spec.clusterIP}')
    fi
    
    # 健康检查
    for i in {1..10}; do
        if curl -f http://$SERVICE_IP:8000/health > /dev/null 2>&1; then
            echo "健康检查通过"
            return 0
        fi
        
        echo "健康检查失败，重试 $i/10..."
        sleep 10
    done
    
    echo "健康检查失败，部署可能有问题"
    exit 1
}

# 6. 回滚函数
rollback() {
    echo "执行回滚..."
    kubectl rollout undo deployment/$APP_NAME -n $NAMESPACE
    kubectl rollout status deployment/$APP_NAME -n $NAMESPACE
    echo "回滚完成"
}

# 主执行流程
main() {
    validate_environment
    build_and_push_image
    update_k8s_config
    wait_for_deployment
    health_check
    
    echo "部署完成！"
    echo "应用版本: $VERSION"
    echo "环境: $ENVIRONMENT"
    echo "命名空间: $NAMESPACE"
}

# 错误处理
trap 'echo "部署失败，执行清理..."; rollback' ERR

# 执行主流程
main
```

#### Kubernetes部署配置
```yaml
# k8s/deployment-template.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: qlib-trading-system
  namespace: {{NAMESPACE}}
  labels:
    app: qlib-trading-system
    environment: {{ENVIRONMENT}}
spec:
  replicas: 3
  selector:
    matchLabels:
      app: qlib-trading-system
  template:
    metadata:
      labels:
        app: qlib-trading-system
        environment: {{ENVIRONMENT}}
    spec:
      containers:
      - name: qlib-trading-system
        image: {{IMAGE_TAG}}
        ports:
        - containerPort: 8000
        - containerPort: 8001  # Metrics port
        env:
        - name: ENVIRONMENT
          value: {{ENVIRONMENT}}
        - name: LOG_LEVEL
          value: INFO
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: qlib-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: qlib-secrets
              key: redis-url
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        volumeMounts:
        - name: model-storage
          mountPath: /app/models
        - name: log-storage
          mountPath: /app/logs
      volumes:
      - name: model-storage
        persistentVolumeClaim:
          claimName: model-pvc
      - name: log-storage
        persistentVolumeClaim:
          claimName: log-pvc

---
apiVersion: v1
kind: Service
metadata:
  name: qlib-trading-system
  namespace: {{NAMESPACE}}
spec:
  selector:
    app: qlib-trading-system
  ports:
  - name: http
    port: 80
    targetPort: 8000
  - name: metrics
    port: 8001
    targetPort: 8001
  type: LoadBalancer

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: qlib-trading-system
  namespace: {{NAMESPACE}}
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  tls:
  - hosts:
    - api.qlib-trading.com
    secretName: qlib-tls
  rules:
  - host: api.qlib-trading.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: qlib-trading-system
            port:
              number: 80
```

### 8. 安全配置

#### 安全检查清单
```python
class SecurityChecker:
    """安全检查器"""
    
    def __init__(self):
        self.security_checks = [
            self._check_environment_variables,
            self._check_file_permissions,
            self._check_network_security,
            self._check_authentication,
            self._check_data_encryption
        ]
    
    def run_security_audit(self) -> Dict:
        """运行安全审计"""
        results = {
            'passed': 0,
            'failed': 0,
            'warnings': 0,
            'details': []
        }
        
        for check in self.security_checks:
            try:
                check_result = check()
                results['details'].append(check_result)
                
                if check_result['status'] == 'pass':
                    results['passed'] += 1
                elif check_result['status'] == 'fail':
                    results['failed'] += 1
                else:
                    results['warnings'] += 1
                    
            except Exception as e:
                results['details'].append({
                    'check': check.__name__,
                    'status': 'error',
                    'message': str(e)
                })
                results['failed'] += 1
        
        return results
    
    def _check_environment_variables(self) -> Dict:
        """检查环境变量安全性"""
        sensitive_vars = ['DATABASE_PASSWORD', 'API_KEY', 'SECRET_KEY']
        issues = []
        
        for var in sensitive_vars:
            if var in os.environ:
                # 检查是否使用默认值
                value = os.environ[var]
                if value in ['password', 'secret', 'key', '123456']:
                    issues.append(f"{var} 使用了不安全的默认值")
        
        return {
            'check': 'environment_variables',
            'status': 'fail' if issues else 'pass',
            'issues': issues
        }
    
    def _check_file_permissions(self) -> Dict:
        """检查文件权限"""
        sensitive_files = [
            'config/production.json',
            'ssl/private.key',
            '.env'
        ]
        
        issues = []
        
        for file_path in sensitive_files:
            if os.path.exists(file_path):
                stat_info = os.stat(file_path)
                permissions = oct(stat_info.st_mode)[-3:]
                
                # 检查是否对其他用户可读
                if permissions[2] != '0':
                    issues.append(f"{file_path} 对其他用户可读")
        
        return {
            'check': 'file_permissions',
            'status': 'fail' if issues else 'pass',
            'issues': issues
        }
```

## 部署最佳实践

### 1. 部署前检查
- 代码审查和测试通过
- 安全扫描无高危漏洞
- 性能测试满足要求
- 配置文件正确性验证

### 2. 部署过程监控
- 实时监控部署进度
- 自动化健康检查
- 性能指标监控
- 错误日志监控

### 3. 回滚策略
- 快速回滚机制
- 数据库迁移回滚
- 配置回滚
- 流量切换回滚

### 4. 部署后验证
- 功能测试验证
- 性能基准测试
- 安全扫描
- 用户体验验证