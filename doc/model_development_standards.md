---
inclusion: fileMatch
fileMatchPattern: 'qlib_trading_system/models/**/*.py'
---

# 模型开发标准和最佳实践

## 模型开发生命周期

### 1. 模型设计阶段

#### 设计原则
- **单一职责**: 每个模型专注于特定任务
- **可扩展性**: 支持新特征和算法的添加
- **可解释性**: 提供模型决策的解释能力
- **鲁棒性**: 处理异常数据和边界情况

#### 必需组件
```python
class BaseModel:
    """所有模型的基类"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.model = None
        self.is_trained = False
        self.feature_names = []
        self.performance_metrics = {}
    
    def train(self, X: np.ndarray, y: np.ndarray) -> Dict:
        """训练模型"""
        raise NotImplementedError
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        """预测"""
        raise NotImplementedError
    
    def evaluate(self, X: np.ndarray, y: np.ndarray) -> Dict:
        """评估模型性能"""
        raise NotImplementedError
    
    def save_model(self, path: str) -> bool:
        """保存模型"""
        raise NotImplementedError
    
    def load_model(self, path: str) -> bool:
        """加载模型"""
        raise NotImplementedError
```

### 2. 特征工程标准

#### 特征命名规范
- **基础特征**: `{source}_{indicator}_{period}` (如: `price_ma_20`)
- **技术指标**: `{indicator}_{param}` (如: `rsi_14`)
- **基本面特征**: `{category}_{metric}` (如: `financial_pe_ratio`)
- **衍生特征**: `{base_feature}_derived_{operation}` (如: `price_ma_20_derived_slope`)

#### 特征处理流程
```python
class FeatureProcessor:
    """特征处理器基类"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.feature_names = []
        self.scalers = {}
    
    def extract_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """提取特征"""
        raise NotImplementedError
    
    def validate_features(self, features: pd.DataFrame) -> bool:
        """验证特征质量"""
        # 检查缺失值
        missing_ratio = features.isnull().sum() / len(features)
        if missing_ratio.max() > 0.1:
            logger.warning(f"特征缺失值过多: {missing_ratio.max():.2%}")
        
        # 检查特征方差
        numeric_features = features.select_dtypes(include=[np.number])
        low_variance = numeric_features.var() < 1e-6
        if low_variance.any():
            logger.warning(f"低方差特征: {low_variance[low_variance].index.tolist()}")
        
        return True
    
    def preprocess_features(self, features: pd.DataFrame) -> pd.DataFrame:
        """预处理特征"""
        # 处理缺失值
        features = self._handle_missing_values(features)
        
        # 特征缩放
        features = self._scale_features(features)
        
        # 异常值处理
        features = self._handle_outliers(features)
        
        return features
```

### 3. 模型训练标准

#### 训练流程
1. **数据准备**: 数据清洗、特征工程、数据分割
2. **模型初始化**: 参数设置、模型创建
3. **训练执行**: 模型训练、验证
4. **性能评估**: 多指标评估、交叉验证
5. **模型保存**: 模型序列化、元数据记录

#### 必需的性能指标
```python
def calculate_model_metrics(y_true: np.ndarray, y_pred: np.ndarray) -> Dict:
    """计算模型性能指标"""
    metrics = {}
    
    # 回归指标
    if is_regression_task(y_true):
        metrics.update({
            'mse': mean_squared_error(y_true, y_pred),
            'mae': mean_absolute_error(y_true, y_pred),
            'rmse': np.sqrt(mean_squared_error(y_true, y_pred)),
            'r2': r2_score(y_true, y_pred),
            'ic': calculate_ic(y_true, y_pred),  # 信息系数
            'rank_ic': calculate_rank_ic(y_true, y_pred)  # 排序信息系数
        })
    
    # 分类指标
    else:
        metrics.update({
            'accuracy': accuracy_score(y_true, y_pred),
            'precision': precision_score(y_true, y_pred, average='weighted'),
            'recall': recall_score(y_true, y_pred, average='weighted'),
            'f1': f1_score(y_true, y_pred, average='weighted')
        })
    
    return metrics
```

### 4. 模型验证标准

#### 时间序列交叉验证
```python
class TimeSeriesCV:
    """时间序列交叉验证"""
    
    def __init__(self, n_splits: int = 5, test_size: int = 252):
        self.n_splits = n_splits
        self.test_size = test_size
    
    def split(self, X: pd.DataFrame) -> Iterator[Tuple[np.ndarray, np.ndarray]]:
        """生成训练/测试索引"""
        n_samples = len(X)
        
        for i in range(self.n_splits):
            # 计算测试集起始位置
            test_start = n_samples - (self.n_splits - i) * self.test_size
            test_end = test_start + self.test_size
            
            # 训练集使用测试集之前的所有数据
            train_indices = np.arange(0, test_start)
            test_indices = np.arange(test_start, min(test_end, n_samples))
            
            yield train_indices, test_indices
```

#### 回测验证
```python
def backtest_model(model, data: pd.DataFrame, start_date: str, end_date: str) -> Dict:
    """模型回测验证"""
    results = {
        'returns': [],
        'positions': [],
        'trades': [],
        'metrics': {}
    }
    
    # 按日期遍历
    for date in pd.date_range(start_date, end_date, freq='D'):
        if date not in data.index:
            continue
        
        # 获取当日数据
        daily_data = data.loc[date]
        
        # 模型预测
        predictions = model.predict(daily_data)
        
        # 生成交易信号
        signals = generate_trading_signals(predictions)
        
        # 计算收益
        returns = calculate_returns(signals, daily_data)
        results['returns'].append(returns)
    
    # 计算回测指标
    results['metrics'] = calculate_backtest_metrics(results['returns'])
    
    return results
```

### 5. 模型部署标准

#### 模型版本管理
- 使用语义化版本号 (如: v1.2.3)
- 记录模型训练时间、数据版本、参数配置
- 支持模型回滚和A/B测试

#### 模型监控
```python
class ModelMonitor:
    """模型监控器"""
    
    def __init__(self, model_version: str):
        self.model_version = model_version
        self.metrics_buffer = []
        self.alert_rules = []
    
    def log_prediction(self, features: np.ndarray, prediction: float, 
                      actual: float = None, timestamp: datetime = None):
        """记录预测结果"""
        record = {
            'timestamp': timestamp or datetime.now(),
            'model_version': self.model_version,
            'prediction': prediction,
            'actual': actual,
            'features_hash': hash(features.tobytes())
        }
        
        self.metrics_buffer.append(record)
        
        # 检查报警条件
        self._check_alerts(record)
    
    def calculate_drift_score(self, window_size: int = 1000) -> float:
        """计算模型漂移分数"""
        if len(self.metrics_buffer) < window_size * 2:
            return 0.0
        
        # 比较最近和历史预测分布
        recent_predictions = [r['prediction'] for r in self.metrics_buffer[-window_size:]]
        historical_predictions = [r['prediction'] for r in self.metrics_buffer[-window_size*2:-window_size]]
        
        # 使用KS检验计算漂移
        from scipy.stats import ks_2samp
        statistic, p_value = ks_2samp(recent_predictions, historical_predictions)
        
        return 1 - p_value  # 漂移分数
```

### 6. 代码质量要求

#### 类型注解
```python
from typing import Dict, List, Optional, Tuple, Union
import numpy as np
import pandas as pd

def train_model(
    X_train: np.ndarray,
    y_train: np.ndarray,
    config: Dict[str, Union[int, float, str]],
    validation_data: Optional[Tuple[np.ndarray, np.ndarray]] = None
) -> Dict[str, float]:
    """训练模型函数示例"""
    pass
```

#### 错误处理
```python
class ModelTrainingError(Exception):
    """模型训练异常"""
    pass

class ModelPredictionError(Exception):
    """模型预测异常"""
    pass

def safe_predict(model, features: np.ndarray) -> np.ndarray:
    """安全预测函数"""
    try:
        # 输入验证
        if features.shape[1] != model.n_features_:
            raise ModelPredictionError(f"特征维度不匹配: 期望{model.n_features_}, 实际{features.shape[1]}")
        
        # 执行预测
        predictions = model.predict(features)
        
        # 输出验证
        if np.isnan(predictions).any():
            raise ModelPredictionError("预测结果包含NaN值")
        
        return predictions
        
    except Exception as e:
        logger.error(f"模型预测失败: {e}")
        raise ModelPredictionError(f"预测失败: {e}")
```

#### 单元测试
```python
import unittest
import numpy as np
from unittest.mock import Mock, patch

class TestStockSelectionModel(unittest.TestCase):
    """股票筛选模型测试"""
    
    def setUp(self):
        """测试准备"""
        self.config = {
            'model_type': 'lightgbm',
            'n_estimators': 100,
            'learning_rate': 0.1
        }
        self.model = StockSelectionModel(self.config)
    
    def test_model_initialization(self):
        """测试模型初始化"""
        self.assertIsNotNone(self.model)
        self.assertEqual(self.model.config['model_type'], 'lightgbm')
    
    def test_model_training(self):
        """测试模型训练"""
        X_train = np.random.randn(100, 10)
        y_train = np.random.randn(100)
        
        result = self.model.train(X_train, y_train)
        
        self.assertTrue(self.model.is_trained)
        self.assertIn('train_score', result)
    
    def test_model_prediction(self):
        """测试模型预测"""
        # 先训练模型
        X_train = np.random.randn(100, 10)
        y_train = np.random.randn(100)
        self.model.train(X_train, y_train)
        
        # 测试预测
        X_test = np.random.randn(20, 10)
        predictions = self.model.predict(X_test)
        
        self.assertEqual(len(predictions), 20)
        self.assertFalse(np.isnan(predictions).any())
```

## 性能优化指导

### 1. 计算优化
- 使用向量化操作替代循环
- 合理使用缓存机制
- 并行计算加速

### 2. 内存优化
- 及时释放不需要的变量
- 使用生成器处理大数据
- 分批处理避免内存溢出

### 3. I/O优化
- 批量读写数据
- 使用合适的数据格式
- 异步I/O操作

## 文档要求

### 1. 模型文档
- 模型原理和算法说明
- 特征定义和计算方法
- 参数配置说明
- 性能基准和限制

### 2. API文档
- 函数签名和参数说明
- 返回值格式
- 使用示例
- 异常处理说明