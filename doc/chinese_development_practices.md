---
inclusion: always
---

# 中文开发实践指导

## 代码注释和文档规范

### 1. 双语注释标准

所有代码必须包含中英文双语注释，优先使用中文：

```python
class StockSelector:
    """
    股票筛选器 - 基于多维度特征筛选爆发股
    Stock Selector - Multi-dimensional feature-based explosive stock screening
    """
    
    def __init__(self, config: Dict):
        """
        初始化股票筛选器
        Initialize stock selector
        
        Args:
            config: 配置参数字典 / Configuration parameters dictionary
        """
        self.config = config
        self.model = None  # 模型实例 / Model instance
        self.features = []  # 特征列表 / Feature list
    
    def select_stocks(self, market_data: pd.DataFrame) -> List[str]:
        """
        筛选爆发股票
        Select explosive stocks
        
        Args:
            market_data: 市场数据 / Market data
            
        Returns:
            股票代码列表 / List of stock symbols
        """
        # 特征提取 / Feature extraction
        features = self._extract_features(market_data)
        
        # 模型预测 / Model prediction
        predictions = self.model.predict(features)
        
        # 筛选结果 / Filter results
        selected_stocks = self._filter_by_threshold(predictions)
        
        return selected_stocks
```

### 2. 变量命名规范

使用英文命名，但在注释中提供中文说明：

```python
# 好的命名方式
explosive_stocks = []  # 爆发股列表
profit_threshold = 0.05  # 盈利阈值
risk_level = 'medium'  # 风险等级

# 避免的命名方式
baofa_stocks = []  # 避免拼音
stocks_list = []  # 避免冗余后缀
x = 0.05  # 避免无意义命名
```

## 中国股市特定实现

### 1. 交易时间处理

```python
class TradingTimeManager:
    """交易时间管理器"""
    
    # 中国股市交易时间
    TRADING_SESSIONS = {
        'morning': ('09:30', '11:30'),  # 上午交易时段
        'afternoon': ('13:00', '15:00')  # 下午交易时段
    }
    
    # 节假日列表（需要定期更新）
    HOLIDAYS = [
        '2024-01-01',  # 元旦
        '2024-02-10',  # 春节
        # ... 其他节假日
    ]
    
    def is_trading_time(self, timestamp: datetime) -> bool:
        """
        判断是否为交易时间
        Check if it's trading time
        """
        # 检查是否为工作日
        if timestamp.weekday() >= 5:  # 周末
            return False
        
        # 检查是否为节假日
        date_str = timestamp.strftime('%Y-%m-%d')
        if date_str in self.HOLIDAYS:
            return False
        
        # 检查具体时间
        current_time = timestamp.strftime('%H:%M')
        
        for session_name, (start, end) in self.TRADING_SESSIONS.items():
            if start <= current_time <= end:
                return True
        
        return False
```

### 2. 股票代码处理

```python
class StockCodeHandler:
    """股票代码处理器"""
    
    # 市场代码映射
    MARKET_MAPPING = {
        '000': '深圳主板',  # Shenzhen Main Board
        '001': '深圳主板',
        '002': '深圳中小板',  # SME Board
        '003': '深圳主板',
        '300': '创业板',  # ChiNext
        '600': '上海主板',  # Shanghai Main Board
        '601': '上海主板',
        '603': '上海主板',
        '688': '科创板',  # STAR Market
    }
    
    def get_market_info(self, stock_code: str) -> Dict:
        """
        获取股票市场信息
        Get stock market information
        """
        prefix = stock_code[:3]
        market_name = self.MARKET_MAPPING.get(prefix, '未知市场')
        
        return {
            'code': stock_code,
            'market': market_name,
            'exchange': 'SZSE' if prefix in ['000', '001', '002', '003', '300'] else 'SSE'
        }
```

## 业务逻辑中文化

### 1. 错误消息中文化

```python
class TradingError(Exception):
    """交易异常基类"""
    
    def __init__(self, message: str, error_code: str = None):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)

class InsufficientFundsError(TradingError):
    """资金不足异常"""
    
    def __init__(self, required: float, available: float):
        message = f"资金不足：需要 {required:.2f} 元，可用 {available:.2f} 元"
        super().__init__(message, "INSUFFICIENT_FUNDS")

class InvalidStockCodeError(TradingError):
    """无效股票代码异常"""
    
    def __init__(self, stock_code: str):
        message = f"无效的股票代码：{stock_code}"
        super().__init__(message, "INVALID_STOCK_CODE")
```

### 2. 日志消息中文化

```python
import logging

# 配置中文日志格式
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('trading_system.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger('qlib_trading_system')

# 使用中文日志消息
logger.info("系统启动成功")
logger.warning(f"股票 {stock_code} 价格异常波动")
logger.error(f"模型预测失败：{error_message}")
```

## 配置文件中文化

### 1. 配置项中文说明

```json
{
  "model_config": {
    "_comment": "模型配置 - Model Configuration",
    "model_type": "lightgbm",
    "n_estimators": 100,
    "_n_estimators_comment": "树的数量 - Number of trees",
    "learning_rate": 0.1,
    "_learning_rate_comment": "学习率 - Learning rate",
    "max_depth": 6,
    "_max_depth_comment": "最大深度 - Maximum depth"
  },
  "trading_config": {
    "_comment": "交易配置 - Trading Configuration", 
    "max_position_size": 0.1,
    "_max_position_size_comment": "最大仓位比例 - Maximum position size ratio",
    "stop_loss_ratio": 0.05,
    "_stop_loss_ratio_comment": "止损比例 - Stop loss ratio",
    "take_profit_ratio": 0.15,
    "_take_profit_ratio_comment": "止盈比例 - Take profit ratio"
  }
}
```

### 2. 用户界面中文化

```python
class UserInterface:
    """用户界面类"""
    
    # 界面文本字典
    UI_TEXTS = {
        'welcome': '欢迎使用Qlib量化交易系统',
        'login': '登录',
        'logout': '退出',
        'portfolio': '投资组合',
        'trading': '交易',
        'analysis': '分析',
        'settings': '设置',
        'help': '帮助'
    }
    
    # 状态消息
    STATUS_MESSAGES = {
        'connected': '已连接到交易服务器',
        'disconnected': '与交易服务器断开连接',
        'order_submitted': '订单已提交',
        'order_filled': '订单已成交',
        'order_cancelled': '订单已取消'
    }
    
    def show_message(self, message_key: str, **kwargs) -> str:
        """显示本地化消息"""
        template = self.STATUS_MESSAGES.get(message_key, message_key)
        return template.format(**kwargs)
```

## 数据本地化

### 1. 中文股票名称处理

```python
class StockNameManager:
    """股票名称管理器"""
    
    def __init__(self):
        # 股票代码到中文名称的映射
        self.stock_names = {
            '000001': '平安银行',
            '000002': '万科A',
            '600000': '浦发银行',
            '600036': '招商银行',
            # ... 更多股票
        }
    
    def get_chinese_name(self, stock_code: str) -> str:
        """获取股票中文名称"""
        return self.stock_names.get(stock_code, f"未知股票({stock_code})")
    
    def search_by_name(self, name_pattern: str) -> List[str]:
        """根据中文名称搜索股票代码"""
        results = []
        for code, name in self.stock_names.items():
            if name_pattern in name:
                results.append(code)
        return results
```

### 2. 行业分类中文化

```python
class IndustryClassifier:
    """行业分类器"""
    
    # 申万行业分类
    SW_INDUSTRIES = {
        '银行': ['000001', '600000', '600036'],
        '房地产': ['000002', '600048'],
        '医药生物': ['000858', '600276'],
        '电子': ['000725', '002415'],
        '计算机': ['000977', '002230']
    }
    
    def get_industry(self, stock_code: str) -> str:
        """获取股票所属行业"""
        for industry, stocks in self.SW_INDUSTRIES.items():
            if stock_code in stocks:
                return industry
        return '其他'
    
    def get_industry_stocks(self, industry: str) -> List[str]:
        """获取行业内所有股票"""
        return self.SW_INDUSTRIES.get(industry, [])
```

## 测试用例中文化

```python
class TestStockSelector(unittest.TestCase):
    """股票筛选器测试类"""
    
    def setUp(self):
        """测试准备"""
        self.selector = StockSelector({
            'model_type': 'lightgbm',
            'threshold': 0.8
        })
    
    def test_股票筛选功能(self):
        """测试股票筛选基本功能"""
        # 准备测试数据
        test_data = pd.DataFrame({
            'symbol': ['000001', '000002', '600000'],
            'pe_ratio': [10.5, 15.2, 8.9],
            'pb_ratio': [1.2, 2.1, 0.9]
        })
        
        # 执行筛选
        selected = self.selector.select_stocks(test_data)
        
        # 验证结果
        self.assertIsInstance(selected, list)
        self.assertGreater(len(selected), 0)
        
        # 验证返回的都是有效股票代码
        for stock_code in selected:
            self.assertRegex(stock_code, r'^\d{6}$')
    
    def test_异常情况处理(self):
        """测试异常情况处理"""
        # 测试空数据
        empty_data = pd.DataFrame()
        with self.assertRaises(ValueError) as context:
            self.selector.select_stocks(empty_data)
        
        self.assertIn('数据不能为空', str(context.exception))
```

## 文档规范

### 1. README文件中文化

```markdown
# Qlib量化交易系统

## 项目简介

本项目是基于Qlib框架开发的量化交易系统，专门针对中国A股市场设计。

## 主要功能

- 🎯 **智能选股**: 基于多维度特征的爆发股筛选
- 📈 **策略回测**: 完整的策略回测和性能分析
- 🔄 **实时交易**: 支持实时数据接入和自动交易
- 📊 **风险管理**: 多层次风险控制和监控

## 快速开始

### 环境要求

- Python 3.8+
- Qlib >= 0.0.2.dev20
- 其他依赖见 requirements.txt

### 安装步骤

1. 克隆项目
```bash
git clone https://github.com/your-repo/qlib-trading-system.git
cd qlib-trading-system
```

2. 安装依赖
```bash
pip install -r requirements.txt
```

3. 初始化系统
```bash
python main.py --init
```

## 使用说明

详细使用说明请参考 [用户手册](docs/user_manual_zh.md)
```

### 2. API文档中文化

```python
def calculate_technical_indicators(data: pd.DataFrame, indicators: List[str]) -> pd.DataFrame:
    """
    计算技术指标
    Calculate technical indicators
    
    本函数用于计算各种技术分析指标，支持常用的技术指标如MA、RSI、MACD等。
    This function calculates various technical analysis indicators.
    
    参数说明 / Parameters:
        data (pd.DataFrame): 股票价格数据，必须包含OHLCV列
                           Stock price data with OHLCV columns
        indicators (List[str]): 需要计算的指标列表
                              List of indicators to calculate
                              可选值 / Options: ['MA', 'RSI', 'MACD', 'BOLL']
    
    返回值 / Returns:
        pd.DataFrame: 包含原始数据和技术指标的数据框
                     DataFrame with original data and technical indicators
    
    异常 / Raises:
        ValueError: 当输入数据格式不正确时抛出
                   Raised when input data format is incorrect
        KeyError: 当请求的指标不支持时抛出
                 Raised when requested indicator is not supported
    
    使用示例 / Example:
        >>> data = get_stock_data('000001')
        >>> indicators = calculate_technical_indicators(data, ['MA', 'RSI'])
        >>> print(indicators.head())
    """
    pass
```

## 本地化最佳实践

### 1. 字符编码
- 所有文件使用UTF-8编码
- 数据库连接指定utf8mb4字符集
- 日志文件使用UTF-8编码

### 2. 时区处理
```python
import pytz

# 使用中国时区
CHINA_TZ = pytz.timezone('Asia/Shanghai')

def get_china_time() -> datetime:
    """获取中国时间"""
    return datetime.now(CHINA_TZ)

def convert_to_china_time(utc_time: datetime) -> datetime:
    """将UTC时间转换为中国时间"""
    return utc_time.replace(tzinfo=pytz.UTC).astimezone(CHINA_TZ)
```

### 3. 数值格式化
```python
def format_currency(amount: float) -> str:
    """格式化货币金额"""
    return f"¥{amount:,.2f}"

def format_percentage(ratio: float) -> str:
    """格式化百分比"""
    return f"{ratio:.2%}"

def format_large_number(number: float) -> str:
    """格式化大数字（万、亿）"""
    if number >= 100000000:  # 亿
        return f"{number/100000000:.2f}亿"
    elif number >= 10000:  # 万
        return f"{number/10000:.2f}万"
    else:
        return f"{number:.2f}"
```