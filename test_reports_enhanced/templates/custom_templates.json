[{"template_id": "custom_20250731_112214", "template_name": "测试自定义模板", "template_type": "custom", "sections": ["数据概览", "图表分析", "总结"], "charts": ["performance_trend", "risk_analysis"], "export_formats": ["html", "pdf", "excel"], "template_content": "\n<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>{{ template_name }}</title>\n    <style>\n        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }\n        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n        .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #007acc; padding-bottom: 20px; }\n        .header h1 { color: #007acc; margin: 0; font-size: 28px; }\n        .section { margin: 30px 0; }\n        .section h2 { color: #007acc; border-bottom: 2px solid #e9ecef; padding-bottom: 10px; }\n        .metrics-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }\n        .metric-card { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #007acc; }\n        .chart-container { margin: 20px 0; text-align: center; }\n        .chart-container img { max-width: 100%; height: auto; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }\n        .footer { text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #dee2e6; color: #6c757d; }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <div class=\"header\">\n            <h1>{{ template_name }}</h1>\n            <p>生成时间: {{ generation_time }}</p>\n        </div>\n        \n        <div class=\"section\">\n            <h2>📊 数据概览</h2>\n            <div class=\"metrics-grid\">\n                {% for key, value in data.items() %}\n                <div class=\"metric-card\">\n                    <h3>{{ key }}</h3>\n                    <div class=\"metric-value\">{{ value }}</div>\n                </div>\n                {% endfor %}\n            </div>\n        </div>\n        \n        {% if charts %}\n        <div class=\"section\">\n            <h2>📈 图表分析</h2>\n            {% for chart_name, chart_path in charts.items() %}\n            <div class=\"chart-container\">\n                <h3>{{ chart_name }}</h3>\n                <img src=\"{{ chart_path }}\" alt=\"{{ chart_name }}\">\n            </div>\n            {% endfor %}\n        </div>\n        {% endif %}\n        \n        <div class=\"footer\">\n            <p>本报告由Qlib交易系统自动生成 | 生成时间: {{ generation_time }}</p>\n        </div>\n    </div>\n</body>\n</html>\n", "variables": {"custom_var": "测试变量"}, "created_time": "2025-07-31T11:22:14.586237", "updated_time": "2025-07-31T11:22:14.586237"}, {"template_id": "custom_20250731_112406", "template_name": "测试自定义模板", "template_type": "custom", "sections": ["数据概览", "图表分析", "总结"], "charts": ["performance_trend", "risk_analysis"], "export_formats": ["html", "pdf", "excel"], "template_content": "\n<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>{{ template_name }}</title>\n    <style>\n        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }\n        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n        .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #007acc; padding-bottom: 20px; }\n        .header h1 { color: #007acc; margin: 0; font-size: 28px; }\n        .section { margin: 30px 0; }\n        .section h2 { color: #007acc; border-bottom: 2px solid #e9ecef; padding-bottom: 10px; }\n        .metrics-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }\n        .metric-card { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #007acc; }\n        .chart-container { margin: 20px 0; text-align: center; }\n        .chart-container img { max-width: 100%; height: auto; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }\n        .footer { text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #dee2e6; color: #6c757d; }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <div class=\"header\">\n            <h1>{{ template_name }}</h1>\n            <p>生成时间: {{ generation_time }}</p>\n        </div>\n        \n        <div class=\"section\">\n            <h2>📊 数据概览</h2>\n            <div class=\"metrics-grid\">\n                {% for key, value in data.items() %}\n                <div class=\"metric-card\">\n                    <h3>{{ key }}</h3>\n                    <div class=\"metric-value\">{{ value }}</div>\n                </div>\n                {% endfor %}\n            </div>\n        </div>\n        \n        {% if charts %}\n        <div class=\"section\">\n            <h2>📈 图表分析</h2>\n            {% for chart_name, chart_path in charts.items() %}\n            <div class=\"chart-container\">\n                <h3>{{ chart_name }}</h3>\n                <img src=\"{{ chart_path }}\" alt=\"{{ chart_name }}\">\n            </div>\n            {% endfor %}\n        </div>\n        {% endif %}\n        \n        <div class=\"footer\">\n            <p>本报告由Qlib交易系统自动生成 | 生成时间: {{ generation_time }}</p>\n        </div>\n    </div>\n</body>\n</html>\n", "variables": {"custom_var": "测试变量"}, "created_time": "2025-07-31T11:24:06.158723", "updated_time": "2025-07-31T11:24:06.158723"}, {"template_id": "custom_20250731_113046", "template_name": "日度交易总结报告 (更新版)", "template_type": "daily", "sections": ["基础指标", "交易统计", "持仓信息", "风险分析", "做T统计", "系统状态"], "charts": ["performance_trend", "risk_analysis", "trading_volume"], "export_formats": ["html", "pdf", "excel"], "template_content": "\n<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>{{ template_name }}</title>\n    <style>\n        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }\n        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n        .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #007acc; padding-bottom: 20px; }\n        .header h1 { color: #007acc; margin: 0; font-size: 28px; }\n        .section { margin: 30px 0; }\n        .section h2 { color: #007acc; border-bottom: 2px solid #e9ecef; padding-bottom: 10px; }\n        .metrics-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }\n        .metric-card { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #007acc; }\n        .chart-container { margin: 20px 0; text-align: center; }\n        .chart-container img { max-width: 100%; height: auto; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }\n        .footer { text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #dee2e6; color: #6c757d; }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <div class=\"header\">\n            <h1>{{ template_name }}</h1>\n            <p>生成时间: {{ generation_time }}</p>\n        </div>\n        \n        <div class=\"section\">\n            <h2>📊 数据概览</h2>\n            <div class=\"metrics-grid\">\n                {% for key, value in data.items() %}\n                <div class=\"metric-card\">\n                    <h3>{{ key }}</h3>\n                    <div class=\"metric-value\">{{ value }}</div>\n                </div>\n                {% endfor %}\n            </div>\n        </div>\n        \n        {% if charts %}\n        <div class=\"section\">\n            <h2>📈 图表分析</h2>\n            {% for chart_name, chart_path in charts.items() %}\n            <div class=\"chart-container\">\n                <h3>{{ chart_name }}</h3>\n                <img src=\"{{ chart_path }}\" alt=\"{{ chart_name }}\">\n            </div>\n            {% endfor %}\n        </div>\n        {% endif %}\n        \n        <div class=\"footer\">\n            <p>本报告由Qlib交易系统自动生成 | 生成时间: {{ generation_time }}</p>\n        </div>\n    </div>\n</body>\n</html>\n", "variables": {"report_version": "v1.0", "author": "Qlib交易系统"}, "created_time": "2025-07-31T11:30:46.823381", "updated_time": "2025-07-31T11:30:49.597168"}, {"template_id": "custom_20250731_113933", "template_name": "测试自定义模板", "template_type": "custom", "sections": ["数据概览", "图表分析", "总结"], "charts": ["performance_trend", "risk_analysis"], "export_formats": ["html", "pdf", "excel"], "template_content": "\n<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>{{ template_name }}</title>\n    <style>\n        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }\n        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n        .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #007acc; padding-bottom: 20px; }\n        .header h1 { color: #007acc; margin: 0; font-size: 28px; }\n        .section { margin: 30px 0; }\n        .section h2 { color: #007acc; border-bottom: 2px solid #e9ecef; padding-bottom: 10px; }\n        .metrics-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }\n        .metric-card { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #007acc; }\n        .chart-container { margin: 20px 0; text-align: center; }\n        .chart-container img { max-width: 100%; height: auto; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }\n        .footer { text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #dee2e6; color: #6c757d; }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <div class=\"header\">\n            <h1>{{ template_name }}</h1>\n            <p>生成时间: {{ generation_time }}</p>\n        </div>\n        \n        <div class=\"section\">\n            <h2>📊 数据概览</h2>\n            <div class=\"metrics-grid\">\n                {% for key, value in data.items() %}\n                <div class=\"metric-card\">\n                    <h3>{{ key }}</h3>\n                    <div class=\"metric-value\">{{ value }}</div>\n                </div>\n                {% endfor %}\n            </div>\n        </div>\n        \n        {% if charts %}\n        <div class=\"section\">\n            <h2>📈 图表分析</h2>\n            {% for chart_name, chart_path in charts.items() %}\n            <div class=\"chart-container\">\n                <h3>{{ chart_name }}</h3>\n                <img src=\"{{ chart_path }}\" alt=\"{{ chart_name }}\">\n            </div>\n            {% endfor %}\n        </div>\n        {% endif %}\n        \n        <div class=\"footer\">\n            <p>本报告由Qlib交易系统自动生成 | 生成时间: {{ generation_time }}</p>\n        </div>\n    </div>\n</body>\n</html>\n", "variables": {"custom_var": "测试变量"}, "created_time": "2025-07-31T11:39:33.349155", "updated_time": "2025-07-31T11:39:33.349155"}]