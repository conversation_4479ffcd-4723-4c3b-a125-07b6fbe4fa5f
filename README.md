# Qlib 双AI交易系统

基于qlib框架构建的双AI交易系统，专门针对中国股市的短线交易。系统包含两个核心AI模型：股票筛选模型（识别3个月内爆发力最强的股票）和日内交易模型（执行T+0策略）。

## 系统特点

- **双AI架构**: 股票筛选AI + 日内交易AI
- **高收益导向**: 专注爆发股识别和T+0策略
- **小资金优化**: 支持全仓单股+做T策略
- **实时处理**: 秒级tick数据处理和信号生成
- **风险可控**: 多层次风险管理和熔断机制

## 项目结构

```
qlib_trading_system/
├── data/                   # 数据层
│   ├── collectors/         # 数据采集器
│   ├── processors/         # 数据处理器
│   └── storage/           # 数据存储
├── models/                # 模型层
│   ├── stock_selection/   # 股票筛选AI
│   └── intraday_trading/  # 日内交易AI
├── trading/               # 交易层
│   ├── execution/         # 交易执行
│   ├── orders/           # 订单管理
│   └── positions/        # 仓位管理
├── risk/                 # 风控层
│   ├── controllers/      # 风险控制器
│   └── monitors/         # 风险监控
└── utils/                # 工具模块
    ├── config/           # 配置管理
    └── logging/          # 日志系统
```

## 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd qlib-trading-system

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安装依赖
python main.py --install-deps
```

### 2. 配置系统

```bash
# 复制配置文件
cp .env.example .env

# 编辑配置文件，填入实际配置
vim .env
```

### 3. 运行系统

```bash
# 启动系统
python main.py
```

## 配置说明

### 资金配置
- `CAPITAL_MODE`: 资金模式 (small/medium/large)
- `TOTAL_CAPITAL`: 总资金
- `BASE_POSITION_RATIO`: 底仓比例 (0.75)
- `T_POSITION_RATIO`: 做T仓位比例 (0.20)

### 风险控制
- `MAX_SINGLE_LOSS`: 单股最大亏损 (0.02)
- `MAX_DAILY_LOSS`: 日最大亏损 (0.01)
- `MAX_DRAWDOWN`: 最大回撤 (0.30)

### 数据源配置
- `PRIMARY_DATA_SOURCE`: 主要数据源 (iTick)
- `ITICK_API_KEY`: iTick API密钥
- `JOINQUANT_USERNAME`: 聚宽用户名

## 数据源推荐

### 主要数据源：iTick
- **优势**: 价格便宜（月费几百元），数据质量好
- **覆盖**: A股实时行情、历史数据、Level-2数据
- **适用**: 个人和小团队，性价比极高

### 备用数据源
- **聚宽**: 免费额度较高，基本面数据全面
- **米筐**: 数据质量高，支持期货和股票
- **免费补充**: 新浪财经、腾讯财经、东方财富

## 系统架构

### 股票筛选AI
- **特征工程**: 六维度特征体系（基本面、估值、技术、情绪、风险、大盘）
- **模型架构**: LightGBM + LSTM混合模型
- **预测目标**: 3个月内爆发潜力评分
- **更新频率**: 每日收盘后重训练

### 日内交易AI
- **输入数据**: 秒级tick数据 + Level-2行情
- **模型架构**: Transformer + CNN + 传统技术分析
- **预测目标**: 1-30分钟价格方向和强度
- **执行策略**: 基于底仓的T+0操作

### 风险管理
- **实时监控**: PnL、回撤、仓位风险
- **动态杠杆**: 基于波动率和胜率的杠杆调整
- **熔断机制**: 极端行情自动防御
- **黑天鹅应对**: 异常事件检测和紧急退出

## 开发指南

### 添加新的数据源
1. 在 `qlib_trading_system/data/collectors/` 创建新的采集器
2. 实现 `DataSource` 接口
3. 在配置文件中添加相关配置

### 扩展特征工程
1. 在 `qlib_trading_system/models/` 添加特征计算模块
2. 更新特征配置
3. 重新训练模型

### 自定义交易策略
1. 继承基础策略类
2. 实现信号生成逻辑
3. 配置风险参数

## 注意事项

1. **数据质量**: 确保数据源稳定可靠
2. **风险控制**: 严格遵守风险限制
3. **资金管理**: 根据资金量选择合适模式
4. **监控告警**: 及时关注系统运行状态
5. **合规要求**: 遵守相关法律法规

## 许可证

MIT License

## 联系方式

如有问题请提交 Issue 或联系开发团队。