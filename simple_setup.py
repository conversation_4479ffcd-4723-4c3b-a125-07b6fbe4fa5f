#!/usr/bin/env python3
"""
Qlib双AI交易系统简化安装脚本
"""
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from qlib_trading_system.utils.simple_init import simple_initializer


def main():
    """主函数"""
    print("=" * 60)
    print("Qlib双AI交易系统 - 简化安装脚本")
    print("=" * 60)
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print(f"❌ Python版本过低: {sys.version}")
        print("需要Python 3.8或更高版本")
        return False
    
    print(f"✅ Python版本: {sys.version}")
    
    # 运行简化初始化
    try:
        success = simple_initializer.run_simple_initialization()
        
        if success:
            print("\n🎉 系统基础环境设置完成！")
            print("\n📋 接下来的步骤:")
            print("1. 安装依赖包: pip install pandas numpy loguru pydantic-settings python-dotenv")
            print("2. 修改.env文件配置")
            print("3. 运行完整初始化: python main.py --init")
            return True
        else:
            print("\n❌ 初始化失败，请检查错误信息")
            return False
            
    except Exception as e:
        print(f"\n❌ 初始化异常: {str(e)}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)