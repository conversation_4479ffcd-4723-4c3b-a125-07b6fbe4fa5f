# -*- coding: utf-8 -*-
"""
高级性能指标分析系统测试

测试爆发股识别准确率分析、做T效率分析、策略归因分析等功能
"""

import sys
import os
import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
import json

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from qlib_trading_system.backtest.advanced_performance_analyzer import (
    AdvancedPerformanceAnalyzer,
    ExplosiveStockMetrics,
    TradingEfficiencyMetrics,
    AttributionAnalysis
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('advanced_performance_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


def create_test_data():
    """创建测试数据"""
    logger.info("创建测试数据...")
    
    # 时间范围
    start_date = datetime(2024, 1, 1)
    end_date = datetime(2024, 6, 30)
    dates = pd.date_range(start_date, end_date, freq='D')
    
    # 设置随机种子
    np.random.seed(42)
    
    # 1. 创建权益曲线数据
    logger.info("创建权益曲线数据...")
    initial_value = 1000000  # 100万初始资金
    returns = np.random.normal(0.002, 0.025, len(dates))  # 日收益率
    
    values = [initial_value]
    for ret in returns[1:]:
        values.append(values[-1] * (1 + ret))
    
    equity_curve = []
    for i, (date, value) in enumerate(zip(dates, values)):
        equity_curve.append({
            'timestamp': date,
            'total_value': value,
            'cash': value * np.random.uniform(0.05, 0.15),  # 5-15%现金
            'positions_value': value * np.random.uniform(0.85, 0.95),  # 85-95%持仓
            'unrealized_pnl': (value - initial_value) * np.random.uniform(0.7, 0.9),
            'realized_pnl': (value - initial_value) * np.random.uniform(0.1, 0.3)
        })
    
    # 2. 创建交易记录数据（包含做T交易）
    logger.info("创建交易记录数据...")
    trades = []
    symbols = [f'00000{i:01d}.SZ' for i in range(1, 11)]  # 10只股票
    
    trade_id = 0
    for i in range(200):  # 200笔交易
        symbol = np.random.choice(symbols)
        trade_date = pd.Timestamp(np.random.choice(dates))
        
        # 随机决定是否为做T交易
        is_t_trade = np.random.random() < 0.3  # 30%概率为做T交易
        
        if is_t_trade and i < 190:  # 确保有配对交易
            # 创建做T交易对
            base_price = 10 + np.random.random() * 20
            buy_time = trade_date + timedelta(hours=np.random.randint(9, 12))
            sell_time = buy_time + timedelta(minutes=np.random.randint(30, 240))
            
            quantity = np.random.randint(1000, 5000)
            
            # 买入交易
            trades.append({
                'timestamp': buy_time,
                'symbol': symbol,
                'side': 'buy',
                'quantity': quantity,
                'price': base_price,
                'commission': quantity * base_price * 0.0003,  # 万三手续费
                'strategy_id': 'intraday_trading'
            })
            
            # 卖出交易（价格有一定波动）
            sell_price = base_price * (1 + np.random.normal(0.01, 0.03))  # 平均1%收益
            trades.append({
                'timestamp': sell_time,
                'symbol': symbol,
                'side': 'sell',
                'quantity': quantity,
                'price': max(sell_price, base_price * 0.95),  # 最大亏损5%
                'commission': quantity * sell_price * 0.0003,
                'strategy_id': 'intraday_trading'
            })
            
            trade_id += 2
        else:
            # 普通交易
            trades.append({
                'timestamp': trade_date + timedelta(hours=np.random.randint(9, 15)),
                'symbol': symbol,
                'side': np.random.choice(['buy', 'sell']),
                'quantity': np.random.randint(1000, 10000),
                'price': 10 + np.random.random() * 30,
                'commission': np.random.uniform(15, 50),
                'strategy_id': 'stock_selection'
            })
            
            trade_id += 1
    
    # 3. 创建股票预测数据
    logger.info("创建股票预测数据...")
    stock_predictions = []
    
    for i in range(100):  # 100个预测
        pred_date = pd.Timestamp(np.random.choice(dates[:150]))  # 前150天的预测
        symbol = np.random.choice(symbols)
        
        # 预测分数和实际收益有一定相关性
        predicted_score = np.random.random()
        
        # 模拟实际收益（与预测分数有正相关）
        base_return = (predicted_score - 0.5) * 2  # -1到1的基础收益
        noise = np.random.normal(0, 0.5)  # 噪声
        actual_return = base_return + noise
        
        stock_predictions.append({
            'date': pred_date,
            'symbol': symbol,
            'predicted_score': predicted_score,
            'predicted_return': predicted_score * 2 - 0.5,  # 预测收益
            'actual_return': actual_return  # 实际收益（用于测试）
        })
    
    # 4. 创建市场数据
    logger.info("创建市场数据...")
    market_data = []
    
    for symbol in symbols:
        base_price = 10 + np.random.random() * 20
        
        for date in dates:
            # 模拟价格走势
            daily_return = np.random.normal(0.001, 0.03)
            base_price *= (1 + daily_return)
            
            market_data.append({
                'date': date,
                'symbol': symbol,
                'open': base_price * (1 + np.random.uniform(-0.02, 0.02)),
                'high': base_price * (1 + np.random.uniform(0, 0.05)),
                'low': base_price * (1 + np.random.uniform(-0.05, 0)),
                'close': base_price,
                'volume': np.random.randint(100000, 1000000)
            })
    
    # 5. 创建基准数据
    logger.info("创建基准数据...")
    benchmark_data = []
    benchmark_value = 3000  # 基准指数初始值
    
    for date in dates:
        benchmark_return = np.random.normal(0.0005, 0.015)  # 基准收益率
        benchmark_value *= (1 + benchmark_return)
        
        benchmark_data.append({
            'date': date,
            'close': benchmark_value,
            'return': benchmark_return
        })
    
    # 组装回测结果
    results = {
        'equity_curve': equity_curve,
        'trades': trades,
        'start_date': start_date.isoformat(),
        'end_date': end_date.isoformat(),
        'initial_capital': initial_value,
        'final_value': values[-1],
        'total_return': (values[-1] - initial_value) / initial_value
    }
    
    logger.info(f"测试数据创建完成 - 权益曲线: {len(equity_curve)}条, 交易记录: {len(trades)}笔, 预测数据: {len(stock_predictions)}条")
    
    return (results, 
            pd.DataFrame(stock_predictions),
            pd.DataFrame(market_data),
            pd.DataFrame(benchmark_data))


def test_explosive_stock_analysis(analyzer):
    """测试爆发股识别准确率分析"""
    logger.info("=" * 50)
    logger.info("测试爆发股识别准确率分析")
    logger.info("=" * 50)
    
    try:
        # 分析爆发股识别准确率
        explosive_metrics = analyzer.analyze_explosive_stock_accuracy(
            explosive_threshold=0.5,  # 50%收益阈值
            time_window=90  # 90天时间窗口
        )
        
        logger.info("爆发股识别分析结果:")
        logger.info(f"  总预测数量: {explosive_metrics.total_predictions}")
        logger.info(f"  实际爆发股数量: {explosive_metrics.actual_explosive}")
        logger.info(f"  预测爆发股数量: {explosive_metrics.predicted_explosive}")
        logger.info(f"  正确识别数量: {explosive_metrics.true_positives}")
        logger.info(f"  准确率: {explosive_metrics.accuracy:.2%}")
        logger.info(f"  精确率: {explosive_metrics.precision:.2%}")
        logger.info(f"  召回率: {explosive_metrics.recall:.2%}")
        logger.info(f"  F1分数: {explosive_metrics.f1_score:.3f}")
        logger.info(f"  爆发股平均收益: {explosive_metrics.avg_explosive_return:.2%}")
        logger.info(f"  预测股票平均收益: {explosive_metrics.avg_predicted_return:.2%}")
        logger.info(f"  命中率加权收益: {explosive_metrics.hit_rate_return:.2%}")
        
        # 验证指标合理性
        assert 0 <= explosive_metrics.accuracy <= 1, "准确率应在0-1之间"
        assert 0 <= explosive_metrics.precision <= 1, "精确率应在0-1之间"
        assert 0 <= explosive_metrics.recall <= 1, "召回率应在0-1之间"
        assert 0 <= explosive_metrics.f1_score <= 1, "F1分数应在0-1之间"
        
        logger.info("✅ 爆发股识别准确率分析测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 爆发股识别准确率分析测试失败: {e}")
        return False


def test_t_trading_efficiency(analyzer):
    """测试做T效率分析"""
    logger.info("=" * 50)
    logger.info("测试做T交易效率分析")
    logger.info("=" * 50)
    
    try:
        # 分析做T交易效率
        t_metrics = analyzer.analyze_t_trading_efficiency()
        
        logger.info("做T交易效率分析结果:")
        logger.info(f"  总做T次数: {t_metrics.total_t_trades}")
        logger.info(f"  成功做T次数: {t_metrics.successful_t_trades}")
        logger.info(f"  做T成功率: {t_metrics.t_success_rate:.2%}")
        logger.info(f"  总成本降低: {t_metrics.total_cost_reduction:.2f} 元")
        logger.info(f"  单次平均成本降低: {t_metrics.avg_cost_reduction_per_trade:.2f} 元")
        logger.info(f"  成本降低率: {t_metrics.cost_reduction_rate:.2%}")
        logger.info(f"  总做T收益: {t_metrics.total_t_profit:.2f} 元")
        logger.info(f"  单次平均做T收益: {t_metrics.avg_t_profit_per_trade:.2f} 元")
        logger.info(f"  做T收益率: {t_metrics.t_profit_rate:.2%}")
        logger.info(f"  平均持仓时间: {t_metrics.avg_holding_time:.1f} 分钟")
        logger.info(f"  每分钟平均收益: {t_metrics.avg_profit_per_minute:.4f} 元")
        logger.info(f"  最大单次亏损: {t_metrics.max_t_loss:.2f} 元")
        logger.info(f"  做T夏普比率: {t_metrics.t_sharpe_ratio:.3f}")
        logger.info(f"  做T盈亏比: {t_metrics.t_win_loss_ratio:.2f}")
        
        # 验证指标合理性
        assert t_metrics.total_t_trades >= 0, "做T次数不能为负"
        assert 0 <= t_metrics.t_success_rate <= 1, "成功率应在0-1之间"
        assert t_metrics.avg_holding_time >= 0, "持仓时间不能为负"
        
        logger.info("✅ 做T交易效率分析测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 做T交易效率分析测试失败: {e}")
        return False


def test_attribution_analysis(analyzer):
    """测试策略归因分析"""
    logger.info("=" * 50)
    logger.info("测试策略归因分析")
    logger.info("=" * 50)
    
    try:
        # 执行策略归因分析
        attribution = analyzer.perform_attribution_analysis()
        
        logger.info("策略归因分析结果:")
        logger.info(f"  选股贡献: {attribution.stock_selection_contribution:.2%}")
        logger.info(f"  择时贡献: {attribution.timing_contribution:.2%}")
        logger.info(f"  交互效应: {attribution.interaction_effect:.2%}")
        logger.info(f"  系统性风险: {attribution.systematic_risk:.4f}")
        logger.info(f"  特定风险: {attribution.specific_risk:.4f}")
        
        logger.info("  因子暴露度:")
        for factor, exposure in attribution.factor_exposures.items():
            logger.info(f"    {factor}: {exposure:.3f}")
        
        logger.info("  因子收益贡献:")
        for factor, contribution in attribution.factor_returns.items():
            logger.info(f"    {factor}: {contribution:.2%}")
        
        # 验证指标合理性
        assert isinstance(attribution.factor_exposures, dict), "因子暴露应为字典"
        assert isinstance(attribution.factor_returns, dict), "因子收益应为字典"
        assert attribution.systematic_risk >= 0, "系统性风险不能为负"
        assert attribution.specific_risk >= 0, "特定风险不能为负"
        
        logger.info("✅ 策略归因分析测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 策略归因分析测试失败: {e}")
        return False


def test_comprehensive_report(analyzer):
    """测试综合报告生成"""
    logger.info("=" * 50)
    logger.info("测试综合报告生成")
    logger.info("=" * 50)
    
    try:
        # 生成综合报告
        report_path = "test_comprehensive_performance_report.md"
        report_content = analyzer.generate_comprehensive_report(report_path)
        
        # 验证报告内容
        assert len(report_content) > 1000, "报告内容应足够详细"
        assert "爆发股识别准确率分析" in report_content, "报告应包含爆发股分析"
        assert "做T交易效率分析" in report_content, "报告应包含做T分析"
        assert "策略归因分析" in report_content, "报告应包含归因分析"
        
        # 验证文件是否生成
        assert os.path.exists(report_path), "报告文件应该被创建"
        
        logger.info(f"✅ 综合报告生成测试通过，报告已保存到: {report_path}")
        logger.info(f"报告长度: {len(report_content)} 字符")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 综合报告生成测试失败: {e}")
        return False


def test_interactive_dashboard(analyzer):
    """测试交互式仪表板创建"""
    logger.info("=" * 50)
    logger.info("测试交互式仪表板创建")
    logger.info("=" * 50)
    
    try:
        # 创建交互式仪表板
        dashboard_path = analyzer.create_interactive_dashboard("test_performance_dashboard.html")
        
        # 验证文件是否生成
        assert os.path.exists(dashboard_path), "仪表板文件应该被创建"
        
        # 验证文件大小（HTML文件应该有一定大小）
        file_size = os.path.getsize(dashboard_path)
        assert file_size > 10000, "仪表板文件应该有足够的内容"
        
        logger.info(f"✅ 交互式仪表板创建测试通过，文件已保存到: {dashboard_path}")
        logger.info(f"文件大小: {file_size} 字节")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 交互式仪表板创建测试失败: {e}")
        return False


def test_json_export(analyzer):
    """测试JSON导出功能"""
    logger.info("=" * 50)
    logger.info("测试JSON导出功能")
    logger.info("=" * 50)
    
    try:
        # 导出指标到JSON
        json_path = "test_performance_metrics.json"
        analyzer.export_metrics_to_json(json_path)
        
        # 验证文件是否生成
        assert os.path.exists(json_path), "JSON文件应该被创建"
        
        # 验证JSON内容
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        assert 'explosive_stock_metrics' in data, "应包含爆发股指标"
        assert 't_trading_metrics' in data, "应包含做T指标"
        assert 'attribution_analysis' in data, "应包含归因分析"
        assert 'export_time' in data, "应包含导出时间"
        
        logger.info(f"✅ JSON导出功能测试通过，文件已保存到: {json_path}")
        logger.info(f"导出的指标类型: {list(data.keys())}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ JSON导出功能测试失败: {e}")
        return False


def run_integration_test():
    """运行集成测试"""
    logger.info("🚀 开始高级性能指标分析系统集成测试")
    logger.info("=" * 80)
    
    try:
        # 1. 创建测试数据
        results, stock_predictions, market_data, benchmark_data = create_test_data()
        
        # 2. 创建高级分析器
        logger.info("创建高级性能分析器...")
        analyzer = AdvancedPerformanceAnalyzer(
            results=results,
            stock_predictions=stock_predictions,
            market_data=market_data,
            benchmark_data=benchmark_data
        )
        
        # 3. 运行各项测试
        test_results = []
        
        test_results.append(("爆发股识别分析", test_explosive_stock_analysis(analyzer)))
        test_results.append(("做T效率分析", test_t_trading_efficiency(analyzer)))
        test_results.append(("策略归因分析", test_attribution_analysis(analyzer)))
        test_results.append(("综合报告生成", test_comprehensive_report(analyzer)))
        test_results.append(("交互式仪表板", test_interactive_dashboard(analyzer)))
        test_results.append(("JSON导出功能", test_json_export(analyzer)))
        
        # 4. 汇总测试结果
        logger.info("=" * 80)
        logger.info("📊 测试结果汇总")
        logger.info("=" * 80)
        
        passed_tests = 0
        total_tests = len(test_results)
        
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            logger.info(f"{test_name}: {status}")
            if result:
                passed_tests += 1
        
        logger.info("=" * 80)
        logger.info(f"测试完成: {passed_tests}/{total_tests} 通过")
        
        if passed_tests == total_tests:
            logger.info("🎉 所有测试通过！高级性能指标分析系统运行正常")
            return True
        else:
            logger.error(f"⚠️ 有 {total_tests - passed_tests} 个测试失败")
            return False
            
    except Exception as e:
        logger.error(f"💥 集成测试过程中发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


if __name__ == "__main__":
    # 运行集成测试
    success = run_integration_test()
    
    if success:
        print("\n" + "=" * 80)
        print("🎉 高级性能指标分析系统测试全部通过！")
        print("=" * 80)
        print("\n生成的文件:")
        print("- test_comprehensive_performance_report.md (综合报告)")
        print("- test_performance_dashboard.html (交互式仪表板)")
        print("- test_performance_metrics.json (性能指标JSON)")
        print("- advanced_performance_test.log (测试日志)")
        
        exit(0)
    else:
        print("\n" + "=" * 80)
        print("❌ 部分测试失败，请查看日志了解详情")
        print("=" * 80)
        exit(1)