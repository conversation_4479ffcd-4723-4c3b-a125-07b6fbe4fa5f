"""
FastAPI应用启动脚本
FastAPI Application Startup Script

启动符合架构要求的FastAPI应用
"""

import os
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('fastapi_app.log')
    ]
)

logger = logging.getLogger(__name__)


def main():
    """主函数"""
    try:
        logger.info("启动FastAPI应用...")
        
        # 导入FastAPI应用
        from qlib_trading_system.api.main import create_app, run_app
        
        # 创建应用配置
        config = {
            'debug': True,
            'cors_origins': [
                'http://localhost:3000',
                'http://localhost:8080',
                'http://localhost:8000',
                'http://127.0.0.1:3000',
                'http://127.0.0.1:8080',
                'http://127.0.0.1:8000'
            ],
            'trusted_hosts': ['localhost', '127.0.0.1', '*.trading-system.com'],
            'rate_limit': {
                'requests_per_minute': 1000,  # 开发环境放宽限制
                'burst_size': 50
            },
            'jwt_secret': os.environ.get('JWT_SECRET_KEY', 'dev-jwt-secret-key-change-in-production'),
            'rabbitmq_url': os.environ.get('RABBITMQ_URL', 'amqp://guest:guest@localhost:5672/'),
            'redis_url': os.environ.get('REDIS_URL', 'redis://localhost:6379/0')
        }
        
        # 创建应用
        app = create_app(config)
        
        # 启动应用
        logger.info("FastAPI应用配置完成，开始启动服务器...")
        logger.info("访问地址:")
        logger.info("  - API文档: http://localhost:8000/docs")
        logger.info("  - ReDoc文档: http://localhost:8000/redoc")
        logger.info("  - 健康检查: http://localhost:8000/health")
        logger.info("  - API信息: http://localhost:8000/info")
        logger.info("  - WebSocket: ws://localhost:8000/ws")
        
        run_app(
            app=app,
            host="0.0.0.0",
            port=8000,
            workers=1,
            reload=True  # 开发环境启用热重载
        )
        
    except ImportError as e:
        logger.error(f"导入错误: {e}")
        logger.error("请确保已安装所有依赖包:")
        logger.error("pip install fastapi uvicorn websockets aio-pika redis passlib[bcrypt] python-multipart email-validator")
        sys.exit(1)
        
    except Exception as e:
        logger.error(f"启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
