"""
杠杆控制系统演示
展示动态杠杆控制系统的完整功能
"""

import logging
import time
import json
from datetime import datetime, timedelta
from pathlib import Path

# 导入杠杆控制系统组件
from qlib_trading_system.risk.controllers.leverage_controller import (
    DynamicLeverageController,
    LeverageConfig,
    AccountInfo,
    CapitalTier,
    MarketTrend
)
from qlib_trading_system.risk.controllers.leverage_monitor import LeverageMonitor
from qlib_trading_system.risk.controllers.leverage_analyzer import LeverageAnalyzer
from qlib_trading_system.risk.controllers.leverage_management_system import (
    LeverageManagementSystem,
    LeverageSystemConfig
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/leverage_system_demo.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def create_demo_account(capital_amount: float = 100000) -> AccountInfo:
    """创建演示账户"""
    if capital_amount < 50000:
        tier = CapitalTier.SMALL
    elif capital_amount < 500000:
        tier = CapitalTier.MEDIUM
    else:
        tier = CapitalTier.LARGE
    
    return AccountInfo(
        total_capital=capital_amount,
        available_capital=capital_amount * 0.2,
        used_capital=capital_amount * 0.8,
        current_drawdown=0.05,
        recent_win_rate=0.65,
        capital_tier=tier
    )

def simulate_market_data() -> dict:
    """模拟市场数据"""
    return {
        'index_change_5d': 2.5,
        'volume_ratio': 1.2,
        'volatility': 0.25,
        'liquidity_score': 0.8
    }

def simulate_positions() -> list:
    """模拟持仓数据"""
    return [
        {
            'symbol': '000001.SZ',
            'name': '平安银行',
            'shares': 1000,
            'current_price': 15.50,
            'leverage': 2.0,
            'cost_basis': 15.00,
            'market_value': 15500
        },
        {
            'symbol': '000002.SZ',
            'name': '万科A',
            'shares': 800,
            'current_price': 22.30,
            'leverage': 1.8,
            'cost_basis': 22.00,
            'market_value': 17840
        },
        {
            'symbol': '000858.SZ',
            'name': '五粮液',
            'shares': 100,
            'current_price': 180.50,
            'leverage': 2.5,
            'cost_basis': 175.00,
            'market_value': 18050
        }
    ]

def demo_basic_leverage_calculation():
    """演示基础杠杆计算功能"""
    logger.info("=" * 60)
    logger.info("演示1: 基础杠杆计算功能")
    logger.info("=" * 60)
    
    # 创建杠杆控制器
    config = LeverageConfig(
        max_leverage=3.0,
        min_leverage=1.0,
        volatility_threshold=0.3
    )
    controller = DynamicLeverageController(config, "data/demo_leverage.db")
    
    # 创建不同规模的账户
    accounts = [
        ("小资金账户", create_demo_account(50000)),
        ("中等资金账户", create_demo_account(200000)),
        ("大资金账户", create_demo_account(1000000))
    ]
    
    # 测试不同波动率的股票
    volatilities = [0.15, 0.25, 0.35, 0.45]
    market_data = simulate_market_data()
    
    for account_name, account in accounts:
        logger.info(f"\n{account_name} (资金: {account.total_capital:,.0f}元)")
        logger.info(f"  资金等级: {account.capital_tier.value}")
        logger.info(f"  当前回撤: {account.current_drawdown:.1%}")
        logger.info(f"  近期胜率: {account.recent_win_rate:.1%}")
        
        for volatility in volatilities:
            decision = controller.calculate_dynamic_leverage(
                account, volatility, market_data
            )
            
            logger.info(f"  波动率 {volatility:.1%}: 推荐杠杆 {decision.recommended_leverage:.2f}x "
                       f"(风险: {decision.risk_level})")
    
    logger.info("\n基础杠杆计算演示完成")

def demo_leverage_monitoring():
    """演示杠杆监控功能"""
    logger.info("=" * 60)
    logger.info("演示2: 杠杆监控功能")
    logger.info("=" * 60)
    
    # 创建杠杆控制器和监控器
    controller = DynamicLeverageController(db_path="data/demo_leverage.db")
    monitor = LeverageMonitor(controller, monitoring_interval=2)
    
    # 设置预警回调
    alerts_received = []
    
    def alert_callback(alert):
        alerts_received.append(alert)
        logger.warning(f"收到预警: {alert.alert_type} - {alert.symbol} - {alert.message}")
    
    monitor.add_alert_callback(alert_callback)
    
    # 启动监控
    logger.info("启动杠杆监控...")
    monitor.start_monitoring()
    
    # 模拟持仓数据
    positions = simulate_positions()
    
    # 执行强制检查
    logger.info("\n执行强制杠杆检查...")
    check_result = monitor.force_leverage_check(positions)
    
    logger.info(f"检查结果:")
    logger.info(f"  检查持仓数: {check_result['positions_checked']}")
    logger.info(f"  发现违规: {check_result['violations_found']}")
    logger.info(f"  产生预警: {check_result['alerts_generated']}")
    
    # 显示持仓详情
    for detail in check_result.get('details', []):
        logger.info(f"  {detail['symbol']}: 杠杆 {detail['current_leverage']:.2f}x, "
                   f"合规: {'是' if detail['is_compliant'] else '否'}")
    
    # 获取杠杆统计
    logger.info("\n获取杠杆统计...")
    stats = monitor.get_leverage_statistics()
    
    if 'leverage_stats' in stats:
        logger.info(f"杠杆统计:")
        logger.info(f"  平均杠杆: {stats['leverage_stats']['avg']:.2f}x")
        logger.info(f"  最大杠杆: {stats['leverage_stats']['max']:.2f}x")
        logger.info(f"  最小杠杆: {stats['leverage_stats']['min']:.2f}x")
        logger.info(f"  加权平均: {stats['leverage_stats']['weighted_avg']:.2f}x")
    
    # 等待监控运行
    logger.info("\n监控运行中，等待5秒...")
    time.sleep(5)
    
    # 停止监控
    monitor.stop_monitoring()
    logger.info(f"监控已停止，共收到 {len(alerts_received)} 个预警")
    
    logger.info("\n杠杆监控演示完成")

def demo_leverage_analysis():
    """演示杠杆分析功能"""
    logger.info("=" * 60)
    logger.info("演示3: 杠杆分析功能")
    logger.info("=" * 60)
    
    # 创建分析器
    analyzer = LeverageAnalyzer("data/demo_leverage.db")
    
    # 首先创建一些历史数据用于分析
    controller = DynamicLeverageController(db_path="data/demo_leverage.db")
    
    logger.info("创建历史数据用于分析...")
    
    # 模拟一些历史交易记录
    test_records = [
        ('000001.SZ', 2.0, 1000, 15000, 'MEDIUM', 300),
        ('000002.SZ', 1.5, 800, 17600, 'LOW', 250),
        ('000858.SZ', 2.5, 100, 17500, 'HIGH', -150),
        ('000001.SZ', 1.8, 1200, 18600, 'MEDIUM', 400),
        ('000002.SZ', 2.2, 600, 13200, 'HIGH', 180),
        ('000858.SZ', 2.0, 150, 26250, 'MEDIUM', 520),
        ('000001.SZ', 2.3, 900, 13950, 'HIGH', -80),
        ('000002.SZ', 1.6, 1000, 22000, 'LOW', 320)
    ]
    
    for symbol, leverage, size, value, risk, pnl in test_records:
        controller.record_leverage_usage(symbol, leverage, size, value, risk, pnl)
    
    # 执行综合分析
    logger.info("\n执行综合杠杆分析...")
    analysis_result = analyzer.comprehensive_analysis(30)
    
    logger.info(f"分析结果:")
    logger.info(f"  分析周期: {analysis_result.analysis_period}")
    logger.info(f"  总交易数: {analysis_result.total_trades}")
    logger.info(f"  平均杠杆: {analysis_result.avg_leverage:.2f}x")
    logger.info(f"  最大杠杆: {analysis_result.max_leverage:.2f}x")
    logger.info(f"  总盈亏: {analysis_result.total_pnl:.2f}元")
    logger.info(f"  胜率: {analysis_result.win_rate:.1%}")
    logger.info(f"  夏普比率: {analysis_result.sharpe_ratio:.3f}")
    logger.info(f"  最大回撤: {analysis_result.max_drawdown:.1%}")
    
    # 显示杠杆效率分析
    logger.info(f"\n杠杆效率分析:")
    for level, stats in analysis_result.leverage_efficiency.items():
        if isinstance(stats, dict) and 'trade_count' in stats:
            logger.info(f"  {level}: {stats['trade_count']}笔交易, "
                       f"胜率{stats['win_rate']:.1%}, "
                       f"平均收益{stats['avg_pnl']:.2f}元")
    
    # 显示优化建议
    logger.info(f"\n优化建议:")
    for i, recommendation in enumerate(analysis_result.recommendations, 1):
        logger.info(f"  {i}. {recommendation}")
    
    # 生成分析报告
    logger.info("\n生成详细分析报告...")
    report = analyzer.generate_analysis_report(analysis_result, "reports/leverage_analysis_report.txt")
    logger.info(f"分析报告已保存到: reports/leverage_analysis_report.txt")
    
    logger.info("\n杠杆分析演示完成")

def demo_integrated_system():
    """演示集成杠杆管理系统"""
    logger.info("=" * 60)
    logger.info("演示4: 集成杠杆管理系统")
    logger.info("=" * 60)
    
    # 创建系统配置
    config = LeverageSystemConfig(
        leverage_config=LeverageConfig(max_leverage=3.0),
        monitoring_interval=3,
        auto_adjustment=True,
        enable_analysis=True
    )
    
    # 创建杠杆管理系统
    system = LeverageManagementSystem(config, "data/demo_leverage.db")
    
    logger.info("启动杠杆管理系统...")
    system.start_system()
    
    # 模拟账户和市场数据
    account = create_demo_account(150000)
    market_data = simulate_market_data()
    positions = simulate_positions()
    
    logger.info(f"\n账户信息:")
    logger.info(f"  总资金: {account.total_capital:,.0f}元")
    logger.info(f"  资金等级: {account.capital_tier.value}")
    logger.info(f"  当前回撤: {account.current_drawdown:.1%}")
    logger.info(f"  近期胜率: {account.recent_win_rate:.1%}")
    
    # 为每个持仓计算杠杆
    logger.info(f"\n为持仓计算推荐杠杆:")
    for position in positions:
        symbol = position['symbol']
        volatility = 0.2 + hash(symbol) % 20 / 100  # 模拟不同的波动率
        
        decision = system.calculate_leverage_for_position(
            symbol, account, volatility, market_data
        )
        
        logger.info(f"  {symbol} ({position['name']}):")
        logger.info(f"    当前杠杆: {position['leverage']:.2f}x")
        logger.info(f"    推荐杠杆: {decision.recommended_leverage:.2f}x")
        logger.info(f"    风险等级: {decision.risk_level}")
        logger.info(f"    决策原因: {decision.reason}")
    
    # 监控组合杠杆
    logger.info(f"\n监控组合杠杆:")
    monitoring_result = system.monitor_portfolio_leverage(positions)
    
    logger.info(f"  组合总市值: {monitoring_result['total_market_value']:,.0f}元")
    logger.info(f"  整体杠杆: {monitoring_result['overall_leverage']:.2f}x")
    logger.info(f"  持仓数量: {monitoring_result['position_count']}")
    logger.info(f"  风险等级: {monitoring_result['risk_level']}")
    
    if monitoring_result['risk_warnings']:
        logger.warning(f"  风险警告:")
        for warning in monitoring_result['risk_warnings']:
            logger.warning(f"    - {warning}")
    
    # 等待系统运行
    logger.info(f"\n系统运行中，等待8秒...")
    time.sleep(8)
    
    # 生成系统报告
    logger.info(f"\n生成系统报告:")
    report = system.generate_system_report()
    
    logger.info(f"  系统状态: {'运行中' if report['system_status']['is_running'] else '已停止'}")
    logger.info(f"  运行时间: {report['system_status']['uptime_seconds']:.0f}秒")
    logger.info(f"  总决策数: {report['system_stats']['total_decisions']}")
    logger.info(f"  总调整数: {report['system_stats']['total_adjustments']}")
    logger.info(f"  总预警数: {report['system_stats']['total_alerts']}")
    
    # 执行系统检查
    logger.info(f"\n执行系统健康检查:")
    check_result = system.force_system_check()
    
    logger.info(f"  整体状态: {check_result['overall_status']}")
    logger.info(f"  数据库可访问: {check_result['health_check']['database_accessible']}")
    logger.info(f"  监控活跃: {check_result['health_check']['monitoring_active']}")
    
    # 获取优化建议
    logger.info(f"\n获取系统优化建议:")
    optimization = system.optimize_system_parameters()
    
    if 'optimization_suggestions' in optimization:
        logger.info(f"  优化建议:")
        for key, value in optimization['optimization_suggestions'].items():
            if key not in ['reason', 'monitoring_reason']:
                logger.info(f"    {key}: {value}")
        
        if 'reason' in optimization['optimization_suggestions']:
            logger.info(f"  建议原因: {optimization['optimization_suggestions']['reason']}")
    
    # 停止系统
    logger.info(f"\n停止杠杆管理系统...")
    system.stop_system()
    
    logger.info("\n集成杠杆管理系统演示完成")

def main():
    """主函数"""
    logger.info("🚀 杠杆控制系统完整演示开始")
    logger.info(f"演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 确保必要的目录存在
    Path("data").mkdir(exist_ok=True)
    Path("logs").mkdir(exist_ok=True)
    Path("reports").mkdir(exist_ok=True)
    
    try:
        # 演示1: 基础杠杆计算
        demo_basic_leverage_calculation()
        
        # 演示2: 杠杆监控
        demo_leverage_monitoring()
        
        # 演示3: 杠杆分析
        demo_leverage_analysis()
        
        # 演示4: 集成系统
        demo_integrated_system()
        
        logger.info("=" * 60)
        logger.info("🎉 杠杆控制系统演示全部完成！")
        logger.info("=" * 60)
        
        # 显示演示总结
        logger.info("演示总结:")
        logger.info("✅ 1. 动态杠杆计算 - 基于多因子模型计算最优杠杆")
        logger.info("✅ 2. 实时杠杆监控 - 持续监控杠杆使用情况")
        logger.info("✅ 3. 历史数据分析 - 深度分析杠杆使用效果")
        logger.info("✅ 4. 集成管理系统 - 统一管理所有杠杆功能")
        logger.info("✅ 5. 风险控制机制 - 多层次风险预警和控制")
        logger.info("✅ 6. 自动调整功能 - 智能调整杠杆参数")
        logger.info("✅ 7. 性能优化建议 - 基于历史数据优化策略")
        
        logger.info("\n系统特点:")
        logger.info("🔹 支持小资金、中等资金、大资金不同模式")
        logger.info("🔹 基于波动率、胜率、回撤等多因子动态计算")
        logger.info("🔹 实时监控预警，支持自动调整")
        logger.info("🔹 完整的历史分析和优化建议")
        logger.info("🔹 高度可配置，支持个性化设置")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 演示过程中发生错误: {e}")
        return False

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)