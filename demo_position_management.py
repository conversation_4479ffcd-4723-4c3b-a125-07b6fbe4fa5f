#!/usr/bin/env python3
"""
仓位管理系统演示脚本
Position Management System Demo
"""

import sys
import os
import time
import json
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

from qlib_trading_system.trading.positions import (
    PositionManager, PositionRiskMonitor, PositionOptimizer, PositionAnalyzer,
    PositionType, RiskConfig, OptimizationConfig, OptimizationObjective
)
from qlib_trading_system.trading.orders.models import Order, OrderSide, OrderType, OrderStatus


def main():
    """主演示函数"""
    print("=" * 80)
    print("🚀 Qlib交易系统 - 仓位管理系统演示")
    print("=" * 80)
    
    # 1. 初始化系统组件
    print("\n📋 步骤1: 初始化系统组件")
    
    # 仓位管理器
    position_manager = PositionManager({
        'max_position_count': 5,
        'price_update_interval': 1.0,
        'position_sync_interval': 5.0
    })
    print("✓ 仓位管理器初始化完成")
    
    # 风险监控器（小资金高风险模式）
    risk_config = RiskConfig(
        max_position_loss_pct=0.10,  # 10%单仓位止损
        max_portfolio_loss_pct=0.05,  # 5%组合止损
        max_portfolio_drawdown_pct=0.30,  # 30%最大回撤
        max_single_position_pct=1.0,  # 允许100%单仓位（小资金模式）
        max_sector_concentration_pct=1.0  # 允许100%行业集中
    )
    risk_monitor = PositionRiskMonitor(position_manager, risk_config)
    print("✓ 风险监控器初始化完成（小资金高风险模式）")
    
    # 仓位优化器（小资金专用配置）
    opt_config = OptimizationConfig(
        objective=OptimizationObjective.MAX_RETURN,
        max_positions=1,  # 最多1只股票
        max_position_weight=1.0,  # 允许100%仓位
        small_capital_mode=True,
        single_stock_focus=True,
        t_plus_zero_enabled=True
    )
    optimizer = PositionOptimizer(position_manager, opt_config)
    print("✓ 仓位优化器初始化完成（小资金专用配置）")
    
    # 仓位分析器
    analyzer = PositionAnalyzer(position_manager)
    print("✓ 仓位分析器初始化完成")
    
    # 2. 模拟小资金全仓单股+做T策略
    print("\n💰 步骤2: 模拟小资金全仓单股+做T策略")
    
    initial_capital = 100000  # 10万资金
    target_symbol = '000001.SZ'  # 平安银行
    base_price = 15.00
    
    # 建立底仓（75%资金）
    base_shares = int(initial_capital * 0.75 / base_price / 100) * 100  # 整手
    base_position = position_manager.create_position(
        symbol=target_symbol,
        position_type=PositionType.BASE,
        initial_quantity=base_shares,
        initial_price=base_price,
        reason="小资金全仓策略-底仓"
    )
    print(f"✓ 建立底仓: {target_symbol} {base_shares}股 @{base_price}")
    
    # 建立T+0仓位（20%资金）
    t_shares = int(initial_capital * 0.20 / base_price / 100) * 100
    t_position = position_manager.create_position(
        symbol=target_symbol,
        position_type=PositionType.T_PLUS_ZERO,
        initial_quantity=t_shares,
        initial_price=base_price,
        reason="T+0操作仓位"
    )
    print(f"✓ 建立T仓: {target_symbol} {t_shares}股 @{base_price}")
    
    # 3. 模拟价格波动和T+0操作
    print("\n📈 步骤3: 模拟价格波动和T+0操作")
    
    # 模拟一天的价格波动
    price_sequence = [
        (15.00, "开盘"),
        (15.10, "小幅上涨"),
        (15.05, "回调"),
        (15.20, "突破上涨"),
        (15.15, "高位震荡"),
        (15.30, "继续上涨"),
        (15.25, "获利回吐"),
        (15.35, "再次上涨"),
        (15.30, "收盘前整理")
    ]
    
    t_trades = []  # 记录T+0交易
    
    for i, (price, desc) in enumerate(price_sequence):
        print(f"  {desc}: {price}")
        
        # 更新价格
        position_manager.update_price(target_symbol, price)
        
        # T+0操作逻辑
        t_pos = position_manager.get_position(f'{target_symbol}_T_PLUS_ZERO')
        if t_pos:
            # 简单的T+0策略：低买高卖
            if price <= 15.05 and t_pos.quantity < t_shares * 1.5:  # 低点加仓
                add_qty = 200
                t_pos.add_quantity(add_qty, price, reason=f"T+0低点买入@{price}")
                t_trades.append(f"买入 {add_qty}股 @{price}")
                print(f"    → T+0买入: {add_qty}股 @{price}")
                
            elif price >= 15.25 and t_pos.quantity > 0:  # 高点减仓
                reduce_qty = min(300, t_pos.available_quantity)
                if reduce_qty > 0:
                    t_pos.reduce_quantity(reduce_qty, price, reason=f"T+0高点卖出@{price}")
                    t_trades.append(f"卖出 {reduce_qty}股 @{price}")
                    print(f"    → T+0卖出: {reduce_qty}股 @{price}")
        
        time.sleep(0.5)  # 模拟时间间隔
    
    # 4. 风险监控检查
    print("\n⚠️  步骤4: 风险监控检查")
    
    # 获取当前风险状况
    risk_summary = risk_monitor.get_risk_summary()
    print(f"✓ 当前风险评分: {risk_summary['risk_score']}/100")
    
    active_alerts = risk_monitor.get_active_alerts()
    if active_alerts:
        print(f"⚠️  活跃风险警报: {len(active_alerts)}个")
        for alert in active_alerts[:3]:  # 显示前3个
            print(f"  - {alert.alert_type.value}: {alert.message}")
    else:
        print("✅ 无活跃风险警报")
    
    # 5. 仓位优化分析
    print("\n🎯 步骤5: 仓位优化分析")
    
    # 添加历史价格数据（模拟）
    for j in range(30):  # 30天历史
        hist_price = base_price * (1 + (j - 15) * 0.005)  # 模拟波动
        optimizer.update_price_history(target_symbol, hist_price)
    
    # 执行优化
    current_positions = position_manager.get_all_positions()
    total_value = sum(p.market_value for p in current_positions)
    current_weights = {p.symbol: p.market_value / total_value for p in current_positions}
    
    optimization_result = optimizer.optimize_portfolio_weights([target_symbol], current_weights)
    print(f"✓ 优化完成:")
    print(f"  目标权重: {optimization_result.target_weights}")
    print(f"  预期收益率: {optimization_result.expected_return:.2%}")
    print(f"  夏普比率: {optimization_result.sharpe_ratio:.2f}")
    
    # 6. 绩效分析
    print("\n📊 步骤6: 绩效分析")
    
    # 模拟历史组合价值
    base_date = datetime.now() - timedelta(days=10)
    for k in range(10):
        date = base_date + timedelta(days=k)
        portfolio_value = initial_capital * (1 + k * 0.01)  # 模拟增长
        analyzer.update_daily_portfolio_value(date, portfolio_value)
    
    # 计算绩效指标
    performance = analyzer.calculate_portfolio_performance()
    print(f"✓ 绩效指标:")
    print(f"  总收益率: {performance.total_return:.2%}")
    print(f"  年化收益率: {performance.annualized_return:.2%}")
    print(f"  夏普比率: {performance.sharpe_ratio:.2f}")
    print(f"  最大回撤: {performance.max_drawdown:.2%}")
    print(f"  胜率: {performance.win_rate:.1%}")
    
    # 7. 系统状态汇总
    print("\n📋 步骤7: 系统状态汇总")
    
    portfolio_summary = position_manager.get_portfolio_summary()
    print(f"✓ 投资组合状态:")
    print(f"  活跃仓位数: {portfolio_summary.active_position_count}")
    print(f"  总市值: ¥{portfolio_summary.total_market_value:,.2f}")
    print(f"  总成本: ¥{portfolio_summary.total_cost:,.2f}")
    print(f"  未实现盈亏: ¥{portfolio_summary.total_unrealized_pnl:,.2f}")
    print(f"  已实现盈亏: ¥{portfolio_summary.total_realized_pnl:,.2f}")
    print(f"  总盈亏: ¥{portfolio_summary.total_pnl:,.2f} ({portfolio_summary.total_pnl_pct:.2%})")
    print(f"  最大回撤: ¥{portfolio_summary.max_drawdown:,.2f} ({portfolio_summary.max_drawdown_pct:.2%})")
    
    # 显示各仓位详情
    positions = position_manager.get_all_positions()
    print(f"\n✓ 仓位详情:")
    for pos in positions:
        print(f"  {pos.symbol} ({pos.position_type.value}):")
        print(f"    数量: {pos.quantity:,}股")
        print(f"    成本: ¥{pos.avg_cost:.2f}/股")
        print(f"    当前价: ¥{pos.current_price:.2f}/股")
        print(f"    市值: ¥{pos.market_value:,.2f}")
        print(f"    盈亏: ¥{pos.unrealized_pnl:,.2f} ({pos.unrealized_pnl_pct:.2%})")
        print(f"    交易次数: {pos.total_trades}")
    
    # T+0交易汇总
    if t_trades:
        print(f"\n✓ T+0交易记录:")
        for trade in t_trades:
            print(f"  {trade}")
    
    # 8. 小资金策略验证
    print("\n🎯 步骤8: 小资金策略验证")
    
    base_positions = [p for p in positions if p.position_type == PositionType.BASE]
    t_positions = [p for p in positions if p.position_type == PositionType.T_PLUS_ZERO]
    
    print(f"✓ 策略特征验证:")
    print(f"  底仓数量: {len(base_positions)} (目标: 1)")
    print(f"  T仓数量: {len(t_positions)} (目标: ≤1)")
    
    if portfolio_summary.total_market_value > 0:
        max_position_value = max(p.market_value for p in positions)
        concentration = max_position_value / portfolio_summary.total_market_value
        print(f"  仓位集中度: {concentration:.1%} (目标: >70%)")
        
        if concentration > 0.7:
            print("✅ 符合小资金高集中度策略")
        else:
            print("⚠️  集中度偏低，建议调整")
    
    # 9. 生成详细报告
    print("\n📄 步骤9: 生成详细报告")
    
    report = analyzer.generate_performance_report()
    
    # 保存报告到文件
    report_filename = f"position_management_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_filename, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"✓ 详细报告已保存到: {report_filename}")
    
    # 10. 清理资源
    print("\n🧹 步骤10: 清理资源")
    
    position_manager.stop()
    risk_monitor.stop()
    
    print("✓ 系统资源清理完成")
    
    print("\n" + "=" * 80)
    print("🎉 仓位管理系统演示完成！")
    print("=" * 80)
    
    # 最终总结
    print(f"\n📈 演示总结:")
    print(f"  初始资金: ¥{initial_capital:,}")
    print(f"  最终市值: ¥{portfolio_summary.total_market_value:,.2f}")
    print(f"  总收益: ¥{portfolio_summary.total_pnl:,.2f}")
    print(f"  收益率: {portfolio_summary.total_pnl_pct:.2%}")
    print(f"  T+0交易: {len(t_trades)}笔")
    print(f"  风险评分: {risk_summary['risk_score']}/100")
    
    if portfolio_summary.total_pnl > 0:
        print("🎊 策略盈利！")
    else:
        print("📉 策略亏损，需要优化")


if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️  演示被用户中断")
    except Exception as e:
        print(f"\n\n❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()