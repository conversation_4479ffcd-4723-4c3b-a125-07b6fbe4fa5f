#!/usr/bin/env python3
"""
实时风险监控引擎简化测试
Simplified Test for Real-time Risk Monitoring Engine
"""

import asyncio
import json
import time
from datetime import datetime
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(__file__))

from qlib_trading_system.risk.monitors.realtime_risk_monitor import RealtimeRiskMonitor
from qlib_trading_system.risk.monitors.integrated_risk_system import RiskSystemFactory

def test_basic_functionality():
    """测试基本功能"""
    print("🛡️ 实时风险监控引擎 - 基本功能测试")
    print("=" * 50)
    
    # 1. 测试监控器创建
    print("1. 创建风险监控器...")
    config = {
        'max_drawdown': 0.20,
        'daily_loss_limit': 0.10,
        'var_limit': 0.08,
        'position_concentration': 0.40,
        'leverage_limit': 1.8,
        'update_interval': 1.0
    }
    
    monitor = RealtimeRiskMonitor(config)
    print("✓ 风险监控器创建成功")
    
    # 2. 测试持仓数据更新
    print("\n2. 更新持仓数据...")
    positions_data = {
        'AAPL': {
            'quantity': 100,
            'avg_cost': 150.0,
            'current_price': 155.0,
            'beta': 1.2,
            'volatility': 0.25
        },
        'GOOGL': {
            'quantity': 50,
            'avg_cost': 2800.0,
            'current_price': 2750.0,
            'beta': 1.1,
            'volatility': 0.30
        }
    }
    
    monitor.update_positions(positions_data)
    print(f"✓ 更新了 {len(monitor.positions)} 个持仓")
    
    # 3. 测试账户信息更新
    print("\n3. 更新账户信息...")
    account_data = {
        'total_capital': 100000.0,
        'available_cash': 20000.0,
        'total_value': 95000.0,
        'initial_capital': 100000.0
    }
    
    monitor.update_account_info(account_data)
    print("✓ 账户信息更新成功")
    
    # 4. 测试风险指标计算
    print("\n4. 计算风险指标...")
    monitor.update_risk_metrics()
    metrics = monitor.get_current_metrics()
    
    print(f"✓ 总盈亏: {metrics.total_pnl:.2f} 元")
    print(f"✓ 未实现盈亏: {metrics.unrealized_pnl:.2f} 元")
    print(f"✓ 当前回撤: {metrics.current_drawdown:.2%}")
    print(f"✓ 持仓集中度: {metrics.position_concentration:.2%}")
    print(f"✓ 风险等级: {metrics.risk_level}")
    
    # 5. 测试预警检查
    print("\n5. 检查风险预警...")
    monitor.check_risk_alerts()
    active_alerts = monitor.get_active_alerts()
    
    if active_alerts:
        print(f"✓ 发现 {len(active_alerts)} 个预警:")
        for alert in active_alerts:
            print(f"  - {alert.alert_type}: {alert.message}")
    else:
        print("✓ 当前无风险预警")
    
    # 6. 测试监控循环
    print("\n6. 测试监控循环...")
    monitor.start_monitoring()
    print("✓ 监控循环已启动")
    
    # 运行3秒
    time.sleep(3)
    
    monitor.stop_monitoring()
    print("✓ 监控循环已停止")
    
    # 7. 测试报告导出
    print("\n7. 导出风险报告...")
    report_file = f"risk_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    report = monitor.export_risk_report(report_file)
    
    if report and report != "{}":
        print(f"✓ 风险报告已导出: {report_file}")
        print(f"  报告大小: {len(report)} 字符")
    else:
        print("⚠ 风险报告为空")
    
    print("\n✅ 基本功能测试完成！")
    return True


async def test_integrated_system():
    """测试集成系统"""
    print("\n🚀 集成风险监控系统测试")
    print("=" * 50)
    
    try:
        # 1. 创建集成系统
        print("1. 创建集成系统...")
        system = RiskSystemFactory.create_system()
        print("✓ 集成系统创建成功")
        
        # 2. 添加回调函数
        print("\n2. 添加回调函数...")
        alert_count = 0
        metrics_count = 0
        
        def alert_callback(alert, metrics):
            nonlocal alert_count
            alert_count += 1
            print(f"  🚨 预警回调 #{alert_count}: {alert.alert_type} - {alert.message}")
        
        def metrics_callback(metrics):
            nonlocal metrics_count
            metrics_count += 1
            if metrics_count % 5 == 0:  # 每5次打印一次
                print(f"  📊 指标回调 #{metrics_count}: 风险等级={metrics.risk_level}")
        
        system.add_alert_callback(alert_callback)
        system.add_metrics_callback(metrics_callback)
        print("✓ 回调函数添加成功")
        
        # 3. 启动系统
        print("\n3. 启动集成系统...")
        await system.start_system()
        print("✓ 集成系统启动成功")
        
        # 4. 模拟数据更新
        print("\n4. 模拟交易数据...")
        positions_data = {
            'AAPL': {
                'quantity': 200,
                'avg_cost': 150.0,
                'current_price': 155.0,
                'beta': 1.2,
                'volatility': 0.25
            }
        }
        
        account_data = {
            'total_capital': 100000.0,
            'available_cash': 30000.0,
            'total_value': 101000.0,
            'initial_capital': 100000.0
        }
        
        system.update_positions(positions_data)
        system.update_account_info(account_data)
        print("✓ 交易数据更新成功")
        
        # 5. 运行系统
        print("\n5. 系统运行中...")
        await asyncio.sleep(5)
        
        # 6. 模拟价格下跌
        print("\n6. 模拟价格下跌...")
        positions_data['AAPL']['current_price'] = 120.0  # 大幅下跌
        account_data['total_value'] = 80000.0  # 总资产下降
        
        system.update_positions(positions_data)
        system.update_account_info(account_data)
        
        await asyncio.sleep(3)
        
        # 7. 获取系统状态
        print("\n7. 获取系统状态...")
        status = system.get_system_status()
        print(f"✓ 系统状态: {status['system_status']}")
        print(f"✓ 运行时间: {status['uptime_seconds']:.1f}秒")
        print(f"✓ 仪表板地址: {status['dashboard_url']}")
        
        # 8. 导出系统报告
        print("\n8. 导出系统报告...")
        report_file = f"system_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        report = system.export_system_report(report_file)
        
        if report and report != "{}":
            print(f"✓ 系统报告已导出: {report_file}")
        else:
            print("⚠ 系统报告为空")
        
        print(f"\n📊 回调统计: 预警回调 {alert_count} 次, 指标回调 {metrics_count} 次")
        
    except Exception as e:
        print(f"❌ 集成系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 停止系统
        print("\n9. 停止集成系统...")
        await system.stop_system()
        print("✓ 集成系统已停止")
    
    print("\n✅ 集成系统测试完成！")
    return True


def main():
    """主函数"""
    print("🛡️ 实时风险监控引擎 - 完整功能验证")
    print("=" * 60)
    
    success_count = 0
    total_tests = 2
    
    try:
        # 测试基本功能
        if test_basic_functionality():
            success_count += 1
        
        # 测试集成系统
        if asyncio.run(test_integrated_system()):
            success_count += 1
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    print(f"✅ 成功: {success_count}/{total_tests}")
    print(f"📈 成功率: {(success_count/total_tests*100):.1f}%")
    
    if success_count == total_tests:
        print("\n🎉 所有测试通过！实时风险监控引擎实现完成！")
        print("\n📋 实现的功能:")
        print("  ✓ 实时PnL计算和监控系统")
        print("  ✓ 动态风险指标计算（VaR、回撤等）")
        print("  ✓ 风险预警和报警机制")
        print("  ✓ 风险仪表板和可视化界面")
        print("  ✓ 集成风险监控系统")
        print("  ✓ 多渠道预警通知系统")
        print("  ✓ 完整的测试套件")
        
        print("\n🚀 使用方法:")
        print("  1. 导入: from qlib_trading_system.risk.monitors import IntegratedRiskSystem")
        print("  2. 创建: system = RiskSystemFactory.create_system()")
        print("  3. 启动: await system.start_system()")
        print("  4. 访问: http://localhost:8080 查看仪表板")
        
        return True
    else:
        print(f"\n❌ {total_tests - success_count} 个测试失败，请检查错误信息")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)