"""
API接口系统集成测试
API Interface System Integration Test

测试RESTful API接口、认证、监控和文档功能
"""

import json
import time
import requests
import threading
from datetime import datetime
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 导入API系统
from qlib_trading_system.api.main import create_app


class APISystemTest:
    """API系统测试类"""
    
    def __init__(self):
        """初始化测试环境"""
        # 创建Flask应用
        self.app = create_app({
            'TESTING': True,
            'DEBUG': True,
            'SECRET_KEY': 'test-secret-key',
            'JWT_SECRET_KEY': 'test-jwt-secret-key'
        })
        
        # 创建测试客户端
        self.client = self.app.test_client()
        self.app_context = self.app.app_context()
        self.app_context.push()
        
        # 测试结果
        self.test_results = {
            "total_tests": 0,
            "passed_tests": 0,
            "failed_tests": 0,
            "test_details": []
        }
        
        # 认证Token
        self.auth_token = None
        self.api_key = None
    
    def run_all_tests(self):
        """运行所有测试"""
        logger.info("开始API接口系统集成测试")
        
        try:
            # 1. 基础功能测试
            self.test_basic_endpoints()
            
            # 2. 认证系统测试
            self.test_authentication_system()
            
            # 3. 权限控制测试
            self.test_permission_control()
            
            # 4. API监控测试
            self.test_api_monitoring()
            
            # 5. 速率限制测试
            self.test_rate_limiting()
            
            # 6. 文档生成测试
            self.test_documentation_generation()
            
            # 7. 错误处理测试
            self.test_error_handling()
            
            # 8. 性能测试
            self.test_performance()
            
        except Exception as e:
            logger.error(f"测试执行异常: {e}")
            self._record_test_result("测试执行", False, str(e))
        
        finally:
            # 生成测试报告
            self._generate_test_report()
            
            # 清理测试环境
            self._cleanup()
    
    def test_basic_endpoints(self):
        """测试基础端点"""
        logger.info("测试基础端点")
        
        try:
            # 测试健康检查
            response = self.client.get('/health')
            assert response.status_code == 200, f"健康检查失败: {response.status_code}"
            
            data = response.get_json()
            assert data['status'] == 'healthy', "健康状态不正确"
            
            # 测试API信息
            response = self.client.get('/info')
            assert response.status_code == 200, f"API信息获取失败: {response.status_code}"
            
            data = response.get_json()
            assert 'title' in data, "API信息缺少标题"
            assert 'version' in data, "API信息缺少版本"
            
            self._record_test_result("基础端点", True)
            
        except Exception as e:
            self._record_test_result("基础端点", False, str(e))
    
    def test_authentication_system(self):
        """测试认证系统"""
        logger.info("测试认证系统")
        
        try:
            # 测试登录
            login_data = {
                "username": "admin",
                "password": "admin123"
            }
            
            response = self.client.post(
                '/api/v1/auth/login',
                data=json.dumps(login_data),
                content_type='application/json'
            )
            
            assert response.status_code == 200, f"登录失败: {response.status_code}"
            
            data = response.get_json()
            assert 'token' in data, "登录响应缺少token"
            assert 'user' in data, "登录响应缺少用户信息"
            
            # 保存认证token
            self.auth_token = data['token']
            
            # 测试获取用户信息
            headers = {'Authorization': f'Bearer {self.auth_token}'}
            response = self.client.get('/api/v1/auth/profile', headers=headers)
            
            assert response.status_code == 200, f"获取用户信息失败: {response.status_code}"
            
            data = response.get_json()
            assert data['username'] == 'admin', "用户信息不正确"
            
            # 测试生成API密钥
            response = self.client.post('/api/v1/auth/api-key', headers=headers)
            assert response.status_code == 200, f"生成API密钥失败: {response.status_code}"
            
            data = response.get_json()
            assert 'api_key' in data, "API密钥生成响应缺少密钥"
            
            # 保存API密钥
            self.api_key = data['api_key']
            
            self._record_test_result("认证系统", True)
            
        except Exception as e:
            self._record_test_result("认证系统", False, str(e))
    
    def test_permission_control(self):
        """测试权限控制"""
        logger.info("测试权限控制")
        
        try:
            # 测试需要认证的端点（无token）
            response = self.client.get('/api/v1/auth/profile')
            assert response.status_code == 401, "未认证请求应该返回401"
            
            # 测试需要特定权限的端点
            headers = {'Authorization': f'Bearer {self.auth_token}'}
            response = self.client.get('/api/v1/auth/users', headers=headers)
            
            # 管理员用户应该有权限
            assert response.status_code == 200, f"管理员用户应该有权限: {response.status_code}"
            
            # 测试API密钥认证
            api_headers = {'X-API-Key': self.api_key}
            response = self.client.get('/api/v1/config/global/default', headers=api_headers)
            
            # API密钥认证应该工作（即使返回404也说明认证通过了）
            assert response.status_code in [200, 404, 500], f"API密钥认证失败: {response.status_code}"
            
            self._record_test_result("权限控制", True)
            
        except Exception as e:
            self._record_test_result("权限控制", False, str(e))
    
    def test_api_monitoring(self):
        """测试API监控"""
        logger.info("测试API监控")
        
        try:
            headers = {'Authorization': f'Bearer {self.auth_token}'}
            
            # 发送一些请求以生成监控数据
            for i in range(5):
                self.client.get('/api/v1/auth/profile', headers=headers)
                time.sleep(0.1)
            
            # 测试获取指标
            response = self.client.get('/api/v1/monitoring/metrics', headers=headers)
            assert response.status_code == 200, f"获取指标失败: {response.status_code}"
            
            data = response.get_json()
            assert 'total_requests' in data, "指标数据缺少总请求数"
            assert data['total_requests'] > 0, "总请求数应该大于0"
            
            # 测试获取端点统计
            response = self.client.get('/api/v1/monitoring/endpoints', headers=headers)
            assert response.status_code == 200, f"获取端点统计失败: {response.status_code}"
            
            data = response.get_json()
            assert isinstance(data, dict), "端点统计应该是字典类型"
            
            self._record_test_result("API监控", True)
            
        except Exception as e:
            self._record_test_result("API监控", False, str(e))
    
    def test_rate_limiting(self):
        """测试速率限制"""
        logger.info("测试速率限制")
        
        try:
            headers = {'Authorization': f'Bearer {self.auth_token}'}
            
            # 快速发送大量请求
            success_count = 0
            rate_limited_count = 0
            
            for i in range(10):
                response = self.client.get('/api/v1/auth/profile', headers=headers)
                if response.status_code == 200:
                    success_count += 1
                elif response.status_code == 429:
                    rate_limited_count += 1
            
            # 应该有一些成功的请求
            assert success_count > 0, "应该有成功的请求"
            
            logger.info(f"成功请求: {success_count}, 被限制请求: {rate_limited_count}")
            
            self._record_test_result("速率限制", True)
            
        except Exception as e:
            self._record_test_result("速率限制", False, str(e))
    
    def test_documentation_generation(self):
        """测试文档生成"""
        logger.info("测试文档生成")
        
        try:
            # 测试OpenAPI规范
            response = self.client.get('/api/v1/openapi.json')
            assert response.status_code == 200, f"获取OpenAPI规范失败: {response.status_code}"
            
            data = response.get_json()
            assert 'openapi' in data, "OpenAPI规范缺少版本信息"
            assert 'paths' in data, "OpenAPI规范缺少路径信息"
            
            # 测试Postman集合
            response = self.client.get('/api/v1/postman.json')
            assert response.status_code == 200, f"获取Postman集合失败: {response.status_code}"
            
            data = response.get_json()
            assert 'info' in data, "Postman集合缺少信息"
            assert 'item' in data, "Postman集合缺少请求项"
            
            # 测试客户端代码生成
            response = self.client.get('/api/v1/client-code/python')
            assert response.status_code == 200, f"获取Python客户端代码失败: {response.status_code}"
            
            data = response.get_json()
            assert 'code' in data, "客户端代码响应缺少代码"
            assert 'class TradingSystemAPIClient' in data['code'], "Python代码缺少客户端类"
            
            # 测试cURL示例
            response = self.client.get('/api/v1/curl-examples')
            assert response.status_code == 200, f"获取cURL示例失败: {response.status_code}"
            
            data = response.get_json()
            assert isinstance(data, dict), "cURL示例应该是字典类型"
            
            self._record_test_result("文档生成", True)
            
        except Exception as e:
            self._record_test_result("文档生成", False, str(e))
    
    def test_error_handling(self):
        """测试错误处理"""
        logger.info("测试错误处理")
        
        try:
            # 测试404错误
            response = self.client.get('/api/v1/nonexistent')
            assert response.status_code == 404, "不存在的端点应该返回404"
            
            data = response.get_json()
            assert 'error' in data, "错误响应应该包含error字段"
            
            # 测试400错误（无效JSON）
            response = self.client.post(
                '/api/v1/auth/login',
                data='invalid json',
                content_type='application/json'
            )
            assert response.status_code == 400, "无效JSON应该返回400"
            
            # 测试401错误（无效token）
            headers = {'Authorization': 'Bearer invalid_token'}
            response = self.client.get('/api/v1/auth/profile', headers=headers)
            assert response.status_code == 401, "无效token应该返回401"
            
            self._record_test_result("错误处理", True)
            
        except Exception as e:
            self._record_test_result("错误处理", False, str(e))
    
    def test_performance(self):
        """测试性能"""
        logger.info("测试性能")
        
        try:
            headers = {'Authorization': f'Bearer {self.auth_token}'}
            
            # 测试并发请求
            def make_request():
                return self.client.get('/api/v1/auth/profile', headers=headers)
            
            # 创建多个线程并发请求
            threads = []
            results = []
            
            start_time = time.time()
            
            for i in range(10):
                thread = threading.Thread(target=lambda: results.append(make_request()))
                threads.append(thread)
                thread.start()
            
            # 等待所有线程完成
            for thread in threads:
                thread.join()
            
            end_time = time.time()
            duration = end_time - start_time
            
            # 检查结果
            success_count = sum(1 for r in results if r and r.status_code == 200)
            
            assert success_count > 0, "并发请求应该有成功的"
            assert duration < 5.0, f"并发请求耗时过长: {duration}秒"
            
            logger.info(f"并发测试: {success_count}/{len(results)} 成功, 耗时: {duration:.2f}秒")
            
            self._record_test_result("性能测试", True)
            
        except Exception as e:
            self._record_test_result("性能测试", False, str(e))
    
    def _record_test_result(self, test_name: str, passed: bool, error_msg: str = ""):
        """记录测试结果"""
        self.test_results["total_tests"] += 1
        
        if passed:
            self.test_results["passed_tests"] += 1
            logger.info(f"✅ {test_name} - 通过")
        else:
            self.test_results["failed_tests"] += 1
            logger.error(f"❌ {test_name} - 失败: {error_msg}")
        
        self.test_results["test_details"].append({
            "test_name": test_name,
            "passed": passed,
            "error_message": error_msg,
            "timestamp": datetime.now().isoformat()
        })
    
    def _generate_test_report(self):
        """生成测试报告"""
        logger.info("生成测试报告")
        
        # 计算通过率
        total = self.test_results["total_tests"]
        passed = self.test_results["passed_tests"]
        pass_rate = (passed / total * 100) if total > 0 else 0
        
        # 生成报告
        report = {
            "test_summary": {
                "total_tests": total,
                "passed_tests": passed,
                "failed_tests": self.test_results["failed_tests"],
                "pass_rate": f"{pass_rate:.1f}%"
            },
            "test_details": self.test_results["test_details"],
            "test_time": datetime.now().isoformat()
        }
        
        # 保存报告
        with open("api_system_test_report.json", 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        # 打印摘要
        logger.info("=" * 60)
        logger.info("API接口系统测试报告")
        logger.info("=" * 60)
        logger.info(f"总测试数: {total}")
        logger.info(f"通过测试: {passed}")
        logger.info(f"失败测试: {self.test_results['failed_tests']}")
        logger.info(f"通过率: {pass_rate:.1f}%")
        logger.info(f"测试报告: api_system_test_report.json")
        logger.info("=" * 60)
    
    def _cleanup(self):
        """清理测试环境"""
        try:
            if self.app_context:
                self.app_context.pop()
            
            logger.info("测试环境清理完成")
            
        except Exception as e:
            logger.error(f"清理测试环境失败: {e}")


def main():
    """主函数"""
    test = APISystemTest()
    test.run_all_tests()


if __name__ == "__main__":
    main()
