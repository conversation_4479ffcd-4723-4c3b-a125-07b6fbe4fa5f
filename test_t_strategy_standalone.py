"""
T+0策略执行引擎独立测试
测试完整的T+0交易策略功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
import unittest
from datetime import datetime, timedelta
import time
import logging
from typing import Dict, List

from qlib_trading_system.trading.t_plus_zero_engine import TTimingAnalyzer, TCostOptimizer, TPosition, TPositionType
from qlib_trading_system.trading.t_risk_controller import TRisk<PERSON>ontroller, RiskLevel
from qlib_trading_system.trading.t_strategy_executor import TStrategyExecutor, ExecutionConfig, ExecutionStatus

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class TestTStrategyIntegration(unittest.TestCase):
    """T+0策略集成测试"""
    
    def setUp(self):
        """测试初始化"""
        self.symbol = "000001.SZ"
        self.base_shares = 1000
        self.base_cost = 10.0
        
        # 创建执行配置
        self.config = ExecutionConfig(
            symbol=self.symbol,
            base_shares=self.base_shares,
            base_cost=self.base_cost,
            max_t_ratio=0.25,
            min_profit_threshold=0.005,
            analysis_interval=1,  # 测试时使用1秒间隔
            stop_loss_pct=0.015,
            max_holding_time=60,  # 测试时使用60分钟
            max_daily_trades=10
        )
        
        # 创建策略执行器
        self.executor = TStrategyExecutor(self.config)
        
        # 创建测试数据
        self.test_data = self._create_test_data()
        
        logger.info("测试初始化完成")
    
    def tearDown(self):
        """测试清理"""
        if hasattr(self, 'executor'):
            self.executor.stop()
        logger.info("测试清理完成")
    
    def _create_test_data(self) -> pd.DataFrame:
        """创建测试数据"""
        # 生成100个数据点的模拟行情数据
        np.random.seed(42)  # 固定随机种子确保测试可重复
        
        dates = pd.date_range(start='2024-01-01 09:30:00', periods=100, freq='1min')
        
        # 基础价格走势
        base_price = 10.0
        price_changes = np.random.normal(0, 0.01, 100)  # 1%的标准差
        prices = [base_price]
        
        for change in price_changes[1:]:
            new_price = prices[-1] * (1 + change)
            prices.append(max(new_price, 0.1))  # 确保价格为正
        
        # 生成OHLCV数据
        data = []
        for i, (date, price) in enumerate(zip(dates, prices)):
            # 生成高低开收
            volatility = 0.005  # 0.5%的日内波动
            high = price * (1 + np.random.uniform(0, volatility))
            low = price * (1 - np.random.uniform(0, volatility))
            
            if i == 0:
                open_price = price
            else:
                open_price = prices[i-1]
            
            close_price = price
            
            # 确保OHLC逻辑正确
            high = max(high, open_price, close_price)
            low = min(low, open_price, close_price)
            
            # 生成成交量
            volume = np.random.randint(100000, 1000000)
            
            data.append({
                'timestamp': date,
                'open': round(open_price, 2),
                'high': round(high, 2),
                'low': round(low, 2),
                'close': round(close_price, 2),
                'volume': volume
            })
        
        df = pd.DataFrame(data)
        logger.info(f"创建测试数据: {len(df)}行")
        return df
    
    def test_timing_analyzer(self):
        """测试时机分析器"""
        logger.info("开始测试时机分析器")
        
        # 创建时机分析器
        analyzer = TTimingAnalyzer()
        
        # 创建底仓
        base_position = TPosition(
            symbol=self.symbol,
            position_type=TPositionType.BASE,
            shares=self.base_shares,
            avg_cost=self.base_cost,
            current_price=self.base_cost,
            unrealized_pnl=0.0,
            unrealized_pnl_pct=0.0,
            entry_time=datetime.now(),
            holding_minutes=0
        )
        
        # 测试分析功能
        signal = analyzer.analyze_t_timing(self.test_data, base_position)
        
        # 验证信号
        self.assertIsNotNone(signal)
        self.assertEqual(signal.symbol, self.symbol)
        self.assertIn(signal.signal_type.value, ['BUY_T', 'SELL_T', 'HOLD_T'])
        self.assertGreaterEqual(signal.strength, 0.0)
        self.assertLessEqual(signal.strength, 1.0)
        self.assertGreaterEqual(signal.confidence, 0.0)
        self.assertLessEqual(signal.confidence, 1.0)
        
        logger.info(f"时机分析结果: {signal.signal_type.value}, 强度: {signal.strength:.3f}, 置信度: {signal.confidence:.3f}")
        logger.info("时机分析器测试通过")
    
    def test_cost_optimizer(self):
        """测试成本优化器"""
        logger.info("开始测试成本优化器")
        
        # 创建成本优化器
        optimizer = TCostOptimizer()
        
        # 创建底仓
        base_position = TPosition(
            symbol=self.symbol,
            position_type=TPositionType.BASE,
            shares=self.base_shares,
            avg_cost=self.base_cost,
            current_price=self.test_data['close'].iloc[-1],
            unrealized_pnl=0.0,
            unrealized_pnl_pct=0.0,
            entry_time=datetime.now(),
            holding_minutes=0
        )
        
        # 测试优化功能
        current_price = self.test_data['close'].iloc[-1]
        optimization_result = optimizer.optimize_t_strategy(base_position, current_price, self.test_data)
        
        # 验证结果
        self.assertIsInstance(optimization_result, dict)
        self.assertIn('recommended_action', optimization_result)
        self.assertIn('optimal_shares', optimization_result)
        self.assertIn('expected_cost_reduction', optimization_result)
        self.assertIn('confidence', optimization_result)
        
        logger.info(f"成本优化结果: {optimization_result['recommended_action']}, 预期成本降低: {optimization_result['expected_cost_reduction']:.4f}")
        logger.info("成本优化器测试通过")
    
    def test_risk_controller(self):
        """测试风险控制器"""
        logger.info("开始测试风险控制器")
        
        # 创建风险控制器
        risk_controller = TRiskController()
        
        # 创建底仓
        base_position = TPosition(
            symbol=self.symbol,
            position_type=TPositionType.BASE,
            shares=self.base_shares,
            avg_cost=self.base_cost,
            current_price=self.test_data['close'].iloc[-1],
            unrealized_pnl=0.0,
            unrealized_pnl_pct=0.0,
            entry_time=datetime.now(),
            holding_minutes=0
        )
        
        # 创建测试信号
        from qlib_trading_system.trading.t_plus_zero_engine import TSignal, TSignalType
        test_signal = TSignal(
            symbol=self.symbol,
            signal_type=TSignalType.BUY_T,
            strength=0.8,
            confidence=0.7,
            expected_return=0.01,
            suggested_shares=200,
            entry_price=self.test_data['close'].iloc[-1]
        )
        
        # 测试风险检查
        allow_trade, alerts = risk_controller.check_pre_trade_risk(test_signal, base_position, self.test_data)
        
        # 验证结果
        self.assertIsInstance(allow_trade, bool)
        self.assertIsInstance(alerts, list)
        
        logger.info(f"风险检查结果: 允许交易={allow_trade}, 警报数量={len(alerts)}")
        
        # 测试持仓监控
        positions = {self.symbol: base_position}
        market_data = {self.symbol: self.test_data}
        monitor_alerts = risk_controller.monitor_position_risk(positions, market_data)
        
        self.assertIsInstance(monitor_alerts, list)
        
        logger.info(f"持仓监控结果: 警报数量={len(monitor_alerts)}")
        logger.info("风险控制器测试通过")
    
    def test_strategy_executor_basic(self):
        """测试策略执行器基本功能"""
        logger.info("开始测试策略执行器基本功能")
        
        # 验证初始化状态
        self.assertEqual(self.executor.config.symbol, self.symbol)
        self.assertEqual(self.executor.config.base_shares, self.base_shares)
        self.assertEqual(self.executor.config.base_cost, self.base_cost)
        
        # 验证底仓初始化
        self.assertIsNotNone(self.executor.state.base_position)
        self.assertEqual(self.executor.state.base_position.symbol, self.symbol)
        self.assertEqual(self.executor.state.base_position.shares, self.base_shares)
        self.assertEqual(self.executor.state.base_position.avg_cost, self.base_cost)
        
        # 测试市场数据更新
        self.executor.update_market_data(self.test_data)
        self.assertGreater(len(self.executor.market_data_cache), 0)
        
        # 验证底仓价格更新
        latest_price = self.test_data['close'].iloc[-1]
        self.assertEqual(self.executor.state.base_position.current_price, latest_price)
        
        # 测试状态报告
        status = self.executor.get_status()
        self.assertIsInstance(status, dict)
        self.assertEqual(status['symbol'], self.symbol)
        
        logger.info("策略执行器基本功能测试通过")


def create_sample_data_for_manual_test():
    """创建用于手动测试的样本数据"""
    logger.info("创建手动测试样本数据")
    
    # 创建更真实的股票数据
    np.random.seed(123)
    
    # 模拟一天的分钟级数据
    start_time = datetime.now().replace(hour=9, minute=30, second=0, microsecond=0)
    periods = 120  # 2小时的数据用于快速测试
    
    dates = pd.date_range(start=start_time, periods=periods, freq='1min')
    
    # 创建有趋势的价格数据
    base_price = 15.0
    trend = 0.0001  # 轻微上涨趋势
    volatility = 0.008  # 0.8%波动率
    
    prices = [base_price]
    for i in range(1, periods):
        # 添加趋势和随机波动
        change = trend + np.random.normal(0, volatility)
        new_price = prices[-1] * (1 + change)
        prices.append(max(new_price, 1.0))  # 确保价格为正
    
    # 生成完整的OHLCV数据
    data = []
    for i, (date, close_price) in enumerate(zip(dates, prices)):
        if i == 0:
            open_price = close_price
        else:
            open_price = prices[i-1]
        
        # 生成日内高低点
        intraday_range = close_price * 0.003  # 0.3%的日内波动
        high = max(open_price, close_price) + np.random.uniform(0, intraday_range)
        low = min(open_price, close_price) - np.random.uniform(0, intraday_range)
        
        # 生成成交量
        volume = int(500000 * (1 + np.random.uniform(-0.3, 0.5)))
        
        data.append({
            'timestamp': date,
            'open': round(open_price, 2),
            'high': round(high, 2),
            'low': round(low, 2),
            'close': round(close_price, 2),
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    logger.info(f"创建样本数据: {len(df)}行, 价格范围: {df['close'].min():.2f} - {df['close'].max():.2f}")
    
    return df


def run_simple_test():
    """运行简单测试"""
    logger.info("开始简单功能测试")
    
    try:
        # 测试时机分析器
        logger.info("测试时机分析器...")
        analyzer = TTimingAnalyzer()
        
        # 创建测试数据
        test_data = create_sample_data_for_manual_test()
        
        # 创建底仓
        base_position = TPosition(
            symbol="000001.SZ",
            position_type=TPositionType.BASE,
            shares=1000,
            avg_cost=15.0,
            current_price=15.0,
            unrealized_pnl=0.0,
            unrealized_pnl_pct=0.0,
            entry_time=datetime.now(),
            holding_minutes=0
        )
        
        # 分析信号
        signal = analyzer.analyze_t_timing(test_data, base_position)
        logger.info(f"时机分析结果: {signal.signal_type.value}, 强度: {signal.strength:.3f}")
        
        # 测试成本优化器
        logger.info("测试成本优化器...")
        optimizer = TCostOptimizer()
        current_price = test_data['close'].iloc[-1]
        optimization_result = optimizer.optimize_t_strategy(base_position, current_price, test_data)
        logger.info(f"成本优化结果: {optimization_result['recommended_action']}")
        
        # 测试风险控制器
        logger.info("测试风险控制器...")
        risk_controller = TRiskController()
        allow_trade, alerts = risk_controller.check_pre_trade_risk(signal, base_position, test_data)
        logger.info(f"风险检查结果: 允许交易={allow_trade}, 警报数量={len(alerts)}")
        
        # 测试策略执行器
        logger.info("测试策略执行器...")
        config = ExecutionConfig(
            symbol="000001.SZ",
            base_shares=1000,
            base_cost=15.0,
            max_t_ratio=0.2,
            analysis_interval=1
        )
        
        executor = TStrategyExecutor(config)
        executor.update_market_data(test_data)
        
        status = executor.get_status()
        logger.info(f"执行器状态: {status['status']}")
        
        executor.stop()
        
        logger.info("所有组件测试通过！")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    print("="*60)
    print("T+0策略执行引擎测试")
    print("="*60)
    
    # 运行简单测试
    run_simple_test()
    
    print("\n" + "="*60)
    print("运行单元测试...")
    
    # 运行单元测试
    unittest.main(argv=[''], exit=False, verbosity=2)