"""
熔断机制演示程序
展示如何使用极端行情熔断机制
"""

import time
from datetime import datetime
from test_circuit_breaker_simple import CircuitBreaker, CircuitBreakerConfig, AlertLevel


def demo_circuit_breaker():
    """演示熔断机制的使用"""
    
    print("=" * 60)
    print("极端行情熔断机制演示")
    print("=" * 60)
    
    # 创建熔断器实例
    config = CircuitBreakerConfig(
        single_stock_drop_threshold=-0.05,    # 单股跌幅5%警告
        single_stock_crash_threshold=-0.10,   # 单股跌幅10%熔断
        market_drop_threshold=-0.03,          # 大盘跌幅3%警告
        market_crash_threshold=-0.05,         # 大盘跌幅5%熔断
        consecutive_loss_days=3               # 连续3天亏损
    )
    
    circuit_breaker = CircuitBreaker(config)
    
    print(f"熔断器初始化完成，当前状态: {'激活' if circuit_breaker.is_active else '未激活'}")
    
    # 演示场景1：正常市场情况
    print("\n场景1：正常市场情况")
    normal_data = {
        "market": {"index_change": -0.01},  # 1%跌幅
        "stocks": {
            "000001": {"current_price": 9.8, "reference_price": 10.0},  # 2%跌幅
            "000002": {"current_price": 10.1, "reference_price": 10.0}  # 1%涨幅
        }
    }
    
    anomalies = circuit_breaker.check_market_conditions(normal_data)
    result = circuit_breaker.process_anomalies(anomalies)
    
    print(f"检测结果: {result['status']}")
    if anomalies:
        for anomaly in anomalies:
            print(f"  异常: {anomaly.event_type} - {anomaly.description}")
    else:
        print("  未检测到异常")
    
    # 演示场景2：个股暴跌
    print("\n场景2：个股暴跌情况")
    crash_data = {
        "market": {"index_change": -0.02},
        "stocks": {
            "000001": {"current_price": 8.5, "reference_price": 10.0},  # 15%跌幅
        }
    }
    
    anomalies = circuit_breaker.check_market_conditions(crash_data)
    result = circuit_breaker.process_anomalies(anomalies)
    
    print(f"检测结果: {result['status']}")
    print(f"熔断状态: {'已触发' if circuit_breaker.circuit_breaker_triggered else '未触发'}")
    
    if anomalies:
        for anomaly in anomalies:
            print(f"  异常: {anomaly.event_type} - {anomaly.description}")
            print(f"  严重程度: {anomaly.severity.value}")
            print(f"  建议动作: {', '.join(anomaly.suggested_actions)}")
    
    # 演示场景3：市场崩盘
    print("\n场景3：市场崩盘情况")
    
    # 重置熔断器
    time.sleep(6)  # 等待冷却时间
    circuit_breaker.reset_circuit_breaker()
    
    market_crash_data = {
        "market": {"index_change": -0.08},  # 8%跌幅
        "stocks": {
            "000001": {"current_price": 9.0, "reference_price": 10.0},
            "000002": {"current_price": 8.8, "reference_price": 10.0}
        }
    }
    
    anomalies = circuit_breaker.check_market_conditions(market_crash_data)
    result = circuit_breaker.process_anomalies(anomalies)
    
    print(f"检测结果: {result['status']}")
    print(f"熔断状态: {'已触发' if circuit_breaker.circuit_breaker_triggered else '未触发'}")
    
    if anomalies:
        emergency_anomalies = [a for a in anomalies if a.severity == AlertLevel.EMERGENCY]
        if emergency_anomalies:
            print("🚨 紧急情况！建议立即执行以下动作：")
            for anomaly in emergency_anomalies:
                print(f"  - {anomaly.description}")
                for action in anomaly.suggested_actions:
                    print(f"    → {action}")
    
    # 演示场景4：黑天鹅事件
    print("\n场景4：黑天鹅事件")
    
    # 重置熔断器
    time.sleep(6)
    circuit_breaker.reset_circuit_breaker()
    
    black_swan_data = {
        "market": {"index_change": -0.02},
        "stocks": {
            "000001": {"current_price": 9.5, "reference_price": 10.0}
        },
        "news": [
            "重大上市公司被立案调查",
            "监管部门发布严厉处罚决定", 
            "市场出现恐慌性抛售",
            "多家机构下调评级",
            "流动性危机担忧加剧"
        ]
    }
    
    anomalies = circuit_breaker.check_market_conditions(black_swan_data)
    result = circuit_breaker.process_anomalies(anomalies)
    
    print(f"检测结果: {result['status']}")
    print(f"熔断状态: {'已触发' if circuit_breaker.circuit_breaker_triggered else '未触发'}")
    
    if anomalies:
        news_anomalies = [a for a in anomalies if "NEWS" in a.event_type]
        if news_anomalies:
            print("📰 检测到重大新闻事件：")
            for anomaly in news_anomalies:
                print(f"  风险评分: {anomaly.metrics.get('risk_score', 0)}")
                print(f"  建议动作: {', '.join(anomaly.suggested_actions)}")
    
    # 演示场景5：连续亏损检测
    print("\n场景5：连续亏损检测")
    
    # 重置熔断器
    time.sleep(6)
    circuit_breaker.reset_circuit_breaker()
    
    print("模拟连续3天亏损...")
    for day in range(3):
        daily_pnl = -0.015  # 每天亏损1.5%
        anomaly = circuit_breaker.check_consecutive_losses(daily_pnl)
        
        print(f"第{day+1}天: 亏损{daily_pnl:.1%}, 连续亏损天数: {circuit_breaker.consecutive_loss_days}")
        
        if anomaly:
            print(f"  触发连续亏损警报: {anomaly.description}")
            print(f"  建议动作: {', '.join(anomaly.suggested_actions)}")
            break
    
    # 总结
    print("\n" + "=" * 60)
    print("演示总结")
    print("=" * 60)
    print("熔断机制能够有效检测以下异常情况：")
    print("✓ 个股价格异常波动")
    print("✓ 市场整体崩盘")
    print("✓ 黑天鹅新闻事件")
    print("✓ 连续亏损情况")
    print("\n系统会根据异常严重程度自动触发相应的防御策略。")
    print("=" * 60)


if __name__ == "__main__":
    demo_circuit_breaker()