#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Qlib交易系统Web管理界面启动脚本

运行Web管理界面服务器
"""

import os
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from qlib_trading_system.web.app import run_web_app

def main():
    """主函数"""
    # 创建必要的目录
    os.makedirs("logs", exist_ok=True)
    os.makedirs("config", exist_ok=True)
    os.makedirs("qlib_trading_system/web/static", exist_ok=True)
    os.makedirs("qlib_trading_system/web/templates", exist_ok=True)
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('logs/web_app.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info("启动Qlib交易系统Web管理界面")
    
    try:
        # 启动Web应用
        run_web_app(
            host="127.0.0.1",
            port=8000,
            debug=True
        )
    except KeyboardInterrupt:
        logger.info("Web应用被用户中断")
    except Exception as e:
        logger.error(f"Web应用启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()