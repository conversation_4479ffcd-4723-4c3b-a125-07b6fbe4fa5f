# qlib交易系统快速启动指南

## 概述

本指南提供了在Windows 11和Linux环境下快速启动qlib交易系统的方法，支持单机模式和微服务模式。

## 环境要求

### 通用要求
- Python 3.9+
- Git (可选，用于版本控制)
- 至少4GB可用内存
- 至少10GB可用磁盘空间

### Windows 11 特定要求
- Windows 11 操作系统
- PowerShell 5.1+ 或 PowerShell Core 7+
- Windows Terminal (推荐)

### Linux 特定要求
- Ubuntu 18.04+, CentOS 7+, 或其他主流发行版
- curl (用于健康检查)
- 系统包管理器权限 (用于安装依赖)

## 快速启动方法

### 方法1: 使用自动化脚本 (推荐)

#### Windows 11
```powershell
# 1. 打开PowerShell (以管理员身份运行)
# 2. 导航到项目目录
cd E:\work\test\ai_2

# 3. 设置执行策略 (如果需要)
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# 4. 运行启动脚本
.\deployment\scripts\run_standalone_windows.ps1

# 其他选项:
.\deployment\scripts\run_standalone_windows.ps1 -InstallDeps    # 安装依赖
.\deployment\scripts\run_standalone_windows.ps1 -SetupDB       # 初始化数据库
.\deployment\scripts\run_standalone_windows.ps1 -Mode production -Port 8080  # 生产模式
.\deployment\scripts\run_standalone_windows.ps1 -Stop          # 停止服务
.\deployment\scripts\run_standalone_windows.ps1 -Status        # 查看状态
.\deployment\scripts\run_standalone_windows.ps1 -Help          # 查看帮助
```

#### Linux
```bash
# 1. 打开终端
# 2. 导航到项目目录
cd /path/to/qlib-trading-system

# 3. 给脚本执行权限
chmod +x deployment/scripts/run_standalone_linux.sh

# 4. 运行启动脚本
./deployment/scripts/run_standalone_linux.sh

# 其他选项:
./deployment/scripts/run_standalone_linux.sh --install-deps    # 安装依赖
./deployment/scripts/run_standalone_linux.sh --setup-db       # 初始化数据库
./deployment/scripts/run_standalone_linux.sh -m production -p 8080  # 生产模式
./deployment/scripts/run_standalone_linux.sh --stop           # 停止服务
./deployment/scripts/run_standalone_linux.sh --status         # 查看状态
./deployment/scripts/run_standalone_linux.sh --help           # 查看帮助
```

### 方法2: 使用Python直接启动

#### 完整系统启动
```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 初始化系统 (首次运行)
python main.py --init

# 3. 启动完整系统
python main.py
```

#### 微服务模式启动
```bash
# 启动API服务
python main.py --service api 0.0.0.0 8000

# 启动数据处理服务
python main.py --service data-processor 0.0.0.0 8001

# 启动模型服务
python main.py --service model-server 0.0.0.0 8002

# 启动风险监控服务
python main.py --service risk-monitor 0.0.0.0 8003
```

### 方法3: 使用Docker (高级用户)

#### 单机Docker运行
```bash
# 1. 构建镜像
docker build -t qlib-trading-system .

# 2. 运行容器
docker run -p 8000:8000 qlib-trading-system
```

#### Docker Compose运行
```bash
# 1. 生成docker-compose配置
python -c "from qlib_trading_system.deployment import DockerManager; DockerManager().create_docker_compose()"

# 2. 启动所有服务
docker-compose -f deployment/docker-compose.yml up -d

# 3. 查看服务状态
docker-compose -f deployment/docker-compose.yml ps

# 4. 停止所有服务
docker-compose -f deployment/docker-compose.yml down
```

## 验证安装

### 1. 健康检查
```bash
# 检查API服务
curl http://localhost:8000/health

# 预期响应
{
  "status": "healthy",
  "service": "qlib-trading-system",
  "version": "1.0.0",
  "timestamp": **********.123
}
```

### 2. 访问Web界面
- 主页: http://localhost:8000
- API文档: http://localhost:8000/docs
- 监控面板: http://localhost:8000/monitoring

### 3. 查看日志
```bash
# Windows
type logs\qlib_trading.log

# Linux
tail -f logs/qlib_trading.log
```

## 常见问题解决

### 1. 端口被占用
```bash
# Windows - 查找占用端口的进程
netstat -ano | findstr :8000

# Linux - 查找占用端口的进程
lsof -i :8000

# 杀死进程 (替换PID)
# Windows
taskkill /PID <PID> /F

# Linux
kill -9 <PID>
```

### 2. Python依赖问题
```bash
# 升级pip
python -m pip install --upgrade pip

# 清理缓存重新安装
pip cache purge
pip install -r requirements.txt --force-reinstall
```

### 3. 权限问题 (Linux)
```bash
# 给脚本执行权限
chmod +x deployment/scripts/run_standalone_linux.sh

# 如果需要sudo权限安装系统包
sudo apt update
sudo apt install python3-dev python3-pip
```

### 4. 内存不足
```bash
# 检查系统内存
# Windows
wmic OS get TotalVisibleMemorySize,FreePhysicalMemory

# Linux
free -h

# 如果内存不足，可以调整配置
export PYTHONHASHSEED=0
export OMP_NUM_THREADS=1
```

## 配置说明

### 环境变量配置 (.env文件)
```bash
# 运行环境
ENVIRONMENT=development  # development, staging, production

# 服务配置
HOST=0.0.0.0
PORT=8000
LOG_LEVEL=INFO

# 数据库配置
DATABASE_URL=sqlite:///data/qlib_trading.db

# Redis配置 (可选)
REDIS_URL=redis://localhost:6379/0

# 日志配置
LOG_DIR=logs

# 数据目录
DATA_DIR=data
```

### 服务端口分配
- API服务: 8000
- 数据处理服务: 8001
- 模型服务: 8002
- 风险监控服务: 8003
- Nginx负载均衡: 80/443

## 性能优化建议

### 1. 系统级优化
```bash
# 增加文件描述符限制 (Linux)
ulimit -n 65536

# 设置Python优化
export PYTHONOPTIMIZE=1
export PYTHONDONTWRITEBYTECODE=1
```

### 2. 应用级优化
- 使用生产模式: `ENVIRONMENT=production`
- 启用日志轮转: 设置合适的日志级别
- 配置数据库连接池
- 启用Redis缓存

### 3. 硬件建议
- **开发环境**: 4GB RAM, 2 CPU核心
- **测试环境**: 8GB RAM, 4 CPU核心
- **生产环境**: 16GB+ RAM, 8+ CPU核心

## 监控和维护

### 1. 系统监控
```bash
# 查看系统资源使用
# Windows
Get-Process python | Select-Object CPU,WorkingSet,ProcessName

# Linux
top -p $(pgrep -f "python.*main.py")
```

### 2. 日志监控
```bash
# 实时查看日志
# Windows
Get-Content logs\qlib_trading.log -Wait

# Linux
tail -f logs/qlib_trading.log
```

### 3. 定期维护
- 定期清理日志文件
- 更新Python依赖包
- 备份重要数据
- 监控磁盘空间使用

## 故障排除

### 1. 服务无法启动
1. 检查Python版本: `python --version`
2. 检查依赖安装: `pip list`
3. 检查端口占用: `netstat -an | grep 8000`
4. 查看错误日志: `cat logs/qlib_trading.log`

### 2. 性能问题
1. 检查系统资源: CPU、内存、磁盘
2. 优化数据库查询
3. 调整并发参数
4. 启用缓存机制

### 3. 网络连接问题
1. 检查防火墙设置
2. 验证网络连通性
3. 检查DNS解析
4. 测试端口可达性

## 技术支持

如果遇到问题，请按以下顺序排查：

1. **查看日志**: 检查 `logs/qlib_trading.log` 文件
2. **检查配置**: 验证 `.env` 文件和配置参数
3. **测试网络**: 确认端口和网络连接正常
4. **重启服务**: 尝试重启服务解决临时问题
5. **查看文档**: 参考本指南和API文档

---

**版本**: 1.0.0  
**更新时间**: 2025-07-31  
**适用系统**: Windows 11, Linux (Ubuntu 18.04+, CentOS 7+)  
**Python版本**: 3.9+
