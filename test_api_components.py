"""
API组件直接测试
Direct API Components Test

直接测试API组件而不通过导入
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import json
import time
from datetime import datetime
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_auth_manager():
    """测试认证管理器"""
    logger.info("测试认证管理器")
    
    try:
        # 直接导入独立模块
        import qlib_trading_system.api.auth_standalone as auth_module
        
        # 创建认证管理器
        auth_manager = auth_module.AuthManager("test-secret-key")
        
        # 测试默认管理员用户
        admin_user = auth_manager.authenticate_user("admin", "admin123")
        assert admin_user is not None, "默认管理员用户认证失败"
        assert admin_user.username == "admin", "管理员用户名不正确"
        assert admin_user.role == auth_module.UserRole.ADMIN, "管理员角色不正确"
        
        # 测试生成Token
        token = auth_manager.generate_token(admin_user)
        assert token is not None, "Token生成失败"
        assert isinstance(token, str), "Token应该是字符串类型"
        
        # 测试验证Token
        payload = auth_manager.verify_token(token)
        assert payload is not None, "Token验证失败"
        assert payload['username'] == 'admin', "Token载荷用户名不正确"
        
        # 测试创建用户
        user_id = auth_manager.create_user(
            username="test_user",
            email="<EMAIL>",
            password="test123",
            role=auth_module.UserRole.TRADER,
            created_by="admin"
        )
        assert user_id is not None, "创建用户失败"
        
        # 测试新用户认证
        test_user = auth_manager.authenticate_user("test_user", "test123")
        assert test_user is not None, "新用户认证失败"
        assert test_user.role == auth_module.UserRole.TRADER, "新用户角色不正确"
        
        # 测试生成API密钥
        api_key = auth_manager.generate_api_key(user_id)
        assert api_key is not None, "API密钥生成失败"
        assert api_key.startswith("ak_"), "API密钥格式不正确"
        
        # 测试验证API密钥
        verified_user = auth_manager.verify_api_key(api_key)
        assert verified_user is not None, "API密钥验证失败"
        assert verified_user.user_id == user_id, "API密钥对应用户不正确"
        
        # 测试权限检查
        has_permission = auth_manager.has_permission(admin_user, auth_module.Permission.SYSTEM_ADMIN)
        assert has_permission, "管理员应该有系统管理权限"
        
        has_permission = auth_manager.has_permission(test_user, auth_module.Permission.SYSTEM_ADMIN)
        assert not has_permission, "普通用户不应该有系统管理权限"
        
        logger.info("✅ 认证管理器测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 认证管理器测试失败: {e}")
        return False


def test_api_monitor():
    """测试API监控器"""
    logger.info("测试API监控器")
    
    try:
        # 直接导入独立模块
        import qlib_trading_system.api.monitoring_standalone as monitor_module
        
        # 创建API监控器
        api_monitor = monitor_module.APIMonitor()
        
        # 测试速率限制器
        rate_limiter = monitor_module.RateLimiter(max_requests=5, window_seconds=10)
        
        # 测试允许请求
        user_id = "test_user"
        for i in range(5):
            allowed = rate_limiter.is_allowed(user_id)
            assert allowed, f"第{i+1}个请求应该被允许"
        
        # 测试超出限制
        allowed = rate_limiter.is_allowed(user_id)
        assert not allowed, "第6个请求应该被拒绝"
        
        # 测试剩余请求数
        remaining = rate_limiter.get_remaining_requests(user_id)
        assert remaining == 0, "剩余请求数应该为0"
        
        # 测试请求监控
        request_id = api_monitor.start_request("/test", "GET", "test_user")
        assert request_id is not None, "开始请求监控失败"
        
        time.sleep(0.01)  # 模拟请求处理时间
        
        api_monitor.end_request(request_id, 200, 100)
        
        # 测试获取指标
        metrics = api_monitor.get_metrics(time_range_minutes=60)
        assert metrics is not None, "获取指标失败"
        assert hasattr(metrics, 'total_requests'), "指标缺少总请求数"
        
        # 测试端点统计
        endpoint_stats = api_monitor.get_endpoint_stats()
        assert isinstance(endpoint_stats, dict), "端点统计应该是字典类型"
        
        # 测试用户统计
        user_stats = api_monitor.get_user_stats()
        assert isinstance(user_stats, dict), "用户统计应该是字典类型"
        
        logger.info("✅ API监控器测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ API监控器测试失败: {e}")
        return False


def test_doc_generator():
    """测试文档生成器"""
    logger.info("测试文档生成器")
    
    try:
        # 直接导入独立模块
        import qlib_trading_system.api.documentation_standalone as doc_module
        
        # 创建文档生成器
        doc_generator = doc_module.APIDocGenerator()
        
        # 测试注册端点
        doc_generator.register_endpoint(
            path="/test/endpoint",
            method=doc_module.HTTPMethod.GET,
            summary="测试端点",
            description="这是一个测试端点",
            tags=["测试"]
        )
        
        # 测试注册模型
        doc_generator.register_model("TestModel", {
            "type": "object",
            "properties": {
                "id": {"type": "integer"},
                "name": {"type": "string"}
            }
        })
        
        # 测试生成OpenAPI规范
        openapi_spec = doc_generator.generate_openapi_spec()
        assert isinstance(openapi_spec, dict), "OpenAPI规范应该是字典类型"
        assert "openapi" in openapi_spec, "OpenAPI规范缺少版本信息"
        assert "paths" in openapi_spec, "OpenAPI规范缺少路径信息"
        assert "/test/endpoint" in openapi_spec["paths"], "测试端点未在规范中"
        
        # 测试生成Postman集合
        postman_collection = doc_generator.generate_postman_collection()
        assert isinstance(postman_collection, dict), "Postman集合应该是字典类型"
        assert "info" in postman_collection, "Postman集合缺少信息"
        assert "item" in postman_collection, "Postman集合缺少请求项"
        
        # 测试生成cURL示例
        curl_examples = doc_generator.generate_curl_examples()
        assert isinstance(curl_examples, dict), "cURL示例应该是字典类型"
        
        # 测试生成客户端代码
        python_code = doc_generator.create_test_client_code("python")
        assert isinstance(python_code, str), "Python客户端代码应该是字符串"
        assert "class TradingSystemAPIClient" in python_code, "Python代码缺少客户端类"
        
        javascript_code = doc_generator.create_test_client_code("javascript")
        assert isinstance(javascript_code, str), "JavaScript客户端代码应该是字符串"
        assert "class TradingSystemAPIClient" in javascript_code, "JavaScript代码缺少客户端类"
        
        # 测试导出文档
        json_doc = doc_generator.export_documentation("json")
        assert isinstance(json_doc, str), "JSON文档应该是字符串"
        
        postman_doc = doc_generator.export_documentation("postman")
        assert isinstance(postman_doc, str), "Postman文档应该是字符串"
        
        logger.info("✅ 文档生成器测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 文档生成器测试失败: {e}")
        return False


def main():
    """主函数"""
    logger.info("开始API组件直接测试")
    
    results = []
    
    # 运行各个测试
    results.append(("认证管理器", test_auth_manager()))
    results.append(("API监控器", test_api_monitor()))
    results.append(("文档生成器", test_doc_generator()))
    
    # 统计结果
    total_tests = len(results)
    passed_tests = sum(1 for _, passed in results if passed)
    failed_tests = total_tests - passed_tests
    pass_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
    
    # 生成报告
    report = {
        "test_summary": {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": failed_tests,
            "pass_rate": f"{pass_rate:.1f}%"
        },
        "test_details": [
            {
                "test_name": name,
                "passed": passed,
                "timestamp": datetime.now().isoformat()
            }
            for name, passed in results
        ],
        "test_time": datetime.now().isoformat()
    }
    
    # 保存报告
    with open("api_components_test_report.json", 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    # 打印摘要
    logger.info("=" * 60)
    logger.info("API组件测试报告")
    logger.info("=" * 60)
    logger.info(f"总测试数: {total_tests}")
    logger.info(f"通过测试: {passed_tests}")
    logger.info(f"失败测试: {failed_tests}")
    logger.info(f"通过率: {pass_rate:.1f}%")
    logger.info(f"测试报告: api_components_test_report.json")
    logger.info("=" * 60)


if __name__ == "__main__":
    main()
