2025-07-28 12:32:03 | INFO     | qlib_trading_system.utils.system_init:full_initialization:227 | ============================================================
2025-07-28 12:32:03 | INFO     | qlib_trading_system.utils.system_init:full_initialization:228 | 开始Qlib双AI交易系统完整初始化
2025-07-28 12:32:03 | INFO     | qlib_trading_system.utils.system_init:full_initialization:229 | ============================================================
2025-07-28 12:32:03 | INFO     | qlib_trading_system.utils.system_init:full_initialization:241 | 执行步骤: 设置目录结构
2025-07-28 12:32:03 | INFO     | qlib_trading_system.utils.system_init:setup_directories:65 | 创建项目目录结构...
2025-07-28 12:32:03 | INFO     | qlib_trading_system.utils.system_init:setup_directories:107 | 目录结构创建完成
2025-07-28 12:32:03 | INFO     | qlib_trading_system.utils.system_init:full_initialization:248 | 步骤完成: 设置目录结构
2025-07-28 12:32:03 | INFO     | qlib_trading_system.utils.system_init:full_initialization:241 | 执行步骤: 设置环境文件
2025-07-28 12:32:03 | INFO     | qlib_trading_system.utils.system_init:setup_environment_file:120 | .env文件已存在，跳过创建
2025-07-28 12:32:03 | INFO     | qlib_trading_system.utils.system_init:full_initialization:248 | 步骤完成: 设置环境文件
2025-07-28 12:32:03 | INFO     | qlib_trading_system.utils.system_init:full_initialization:241 | 执行步骤: 安装依赖包
2025-07-28 12:32:03 | INFO     | qlib_trading_system.utils.system_init:install_dependencies:28 | 开始安装依赖包...
2025-07-28 12:42:03 | ERROR    | qlib_trading_system.utils.system_init:install_dependencies:57 | 依赖包安装超时
2025-07-28 12:42:03 | ERROR    | qlib_trading_system.utils.system_init:full_initialization:245 | 步骤失败: 安装依赖包
2025-07-28 12:42:05 | INFO     | qlib_trading_system.utils.simple_init:run_simple_initialization:284 | ============================================================
2025-07-28 12:42:05 | INFO     | qlib_trading_system.utils.simple_init:run_simple_initialization:285 | 开始Qlib双AI交易系统简化初始化
2025-07-28 12:42:05 | INFO     | qlib_trading_system.utils.simple_init:run_simple_initialization:286 | ============================================================
2025-07-28 12:42:05 | INFO     | qlib_trading_system.utils.simple_init:run_simple_initialization:298 | 执行步骤: 创建项目目录结构
2025-07-28 12:42:05 | INFO     | qlib_trading_system.utils.simple_init:create_project_structure:22 | 创建项目目录结构...
2025-07-28 12:42:05 | INFO     | qlib_trading_system.utils.simple_init:create_project_structure:64 | 项目目录结构创建完成
2025-07-28 12:42:05 | INFO     | qlib_trading_system.utils.simple_init:run_simple_initialization:305 | 步骤完成: 创建项目目录结构
2025-07-28 12:42:05 | INFO     | qlib_trading_system.utils.simple_init:run_simple_initialization:298 | 执行步骤: 设置基础配置文件
2025-07-28 12:42:05 | INFO     | qlib_trading_system.utils.simple_init:setup_basic_configs:73 | 设置基础配置文件...
2025-07-28 12:42:05 | INFO     | qlib_trading_system.utils.simple_init:setup_basic_configs:128 | 基础配置文件创建完成
2025-07-28 12:42:05 | INFO     | qlib_trading_system.utils.simple_init:run_simple_initialization:305 | 步骤完成: 设置基础配置文件
2025-07-28 12:42:05 | INFO     | qlib_trading_system.utils.simple_init:run_simple_initialization:298 | 执行步骤: 创建示例数据文件
2025-07-28 12:42:05 | INFO     | qlib_trading_system.utils.simple_init:create_sample_data_files:137 | 创建示例数据文件...
2025-07-28 12:42:05 | INFO     | qlib_trading_system.utils.simple_init:create_sample_data_files:182 | 示例数据文件创建完成
2025-07-28 12:42:05 | INFO     | qlib_trading_system.utils.simple_init:run_simple_initialization:305 | 步骤完成: 创建示例数据文件
2025-07-28 12:42:05 | INFO     | qlib_trading_system.utils.simple_init:run_simple_initialization:298 | 执行步骤: 设置日志配置
2025-07-28 12:42:05 | INFO     | qlib_trading_system.utils.simple_init:setup_logging_config:191 | 设置日志配置...
2025-07-28 12:42:05 | INFO     | qlib_trading_system.utils.simple_init:setup_logging_config:240 | 日志配置文件创建完成
2025-07-28 12:42:05 | INFO     | qlib_trading_system.utils.simple_init:run_simple_initialization:305 | 步骤完成: 设置日志配置
2025-07-28 12:42:05 | INFO     | qlib_trading_system.utils.simple_init:run_simple_initialization:298 | 执行步骤: 设置基础Qlib环境
2025-07-28 12:42:05 | INFO     | qlib_trading_system.utils.basic_qlib_init:setup_basic_environment:27 | Qlib数据目录已创建: C:\Users\<USER>\.qlib\qlib_data\cn_data
2025-07-28 12:42:05 | INFO     | qlib_trading_system.utils.simple_init:run_simple_initialization:305 | 步骤完成: 设置基础Qlib环境
2025-07-28 12:42:05 | INFO     | qlib_trading_system.utils.simple_init:run_simple_initialization:298 | 执行步骤: 创建模拟数据结构
2025-07-28 12:42:05 | INFO     | qlib_trading_system.utils.basic_qlib_init:create_mock_data_structure:69 | 模拟数据结构创建完成
2025-07-28 12:42:05 | INFO     | qlib_trading_system.utils.simple_init:run_simple_initialization:305 | 步骤完成: 创建模拟数据结构
2025-07-28 12:42:05 | INFO     | qlib_trading_system.utils.simple_init:run_simple_initialization:312 | 验证环境配置...
2025-07-28 12:42:05 | INFO     | qlib_trading_system.utils.simple_init:run_simple_initialization:317 | ✓ python_version 验证通过
2025-07-28 12:42:05 | INFO     | qlib_trading_system.utils.simple_init:run_simple_initialization:317 | ✓ directories 验证通过
2025-07-28 12:42:05 | INFO     | qlib_trading_system.utils.simple_init:run_simple_initialization:317 | ✓ config_files 验证通过
2025-07-28 12:42:05 | INFO     | qlib_trading_system.utils.simple_init:run_simple_initialization:317 | ✓ env_file 验证通过
2025-07-28 12:42:05 | INFO     | qlib_trading_system.utils.simple_init:run_simple_initialization:322 | ============================================================
2025-07-28 12:42:05 | INFO     | qlib_trading_system.utils.simple_init:run_simple_initialization:323 | 初始化完成！后续步骤:
2025-07-28 12:42:05 | INFO     | qlib_trading_system.utils.simple_init:run_simple_initialization:324 | 1. 修改.env文件中的配置参数
2025-07-28 12:42:05 | INFO     | qlib_trading_system.utils.simple_init:run_simple_initialization:325 | 2. 配置数据源API密钥
2025-07-28 12:42:05 | INFO     | qlib_trading_system.utils.simple_init:run_simple_initialization:326 | 3. 安装完整依赖包: pip install -r requirements.txt
2025-07-28 12:42:05 | INFO     | qlib_trading_system.utils.simple_init:run_simple_initialization:327 | 4. 下载Qlib数据:
2025-07-28 12:42:05 | INFO     | qlib_trading_system.utils.simple_init:run_simple_initialization:328 |    python -m qlib.run.get_data qlib_data --target_dir ~/.qlib/qlib_data/cn_data --region cn
2025-07-28 12:42:05 | INFO     | qlib_trading_system.utils.simple_init:run_simple_initialization:329 | 5. 启动系统: python main.py
2025-07-28 12:42:05 | INFO     | qlib_trading_system.utils.simple_init:run_simple_initialization:330 | ============================================================
2025-07-28 12:51:23 | INFO     | qlib_trading_system.utils.basic_qlib_init:setup_basic_environment:27 | Qlib数据目录已创建: C:\Users\<USER>\.qlib\qlib_data\cn_data
2025-07-31 09:18:07 | INFO     | __main__:__init__:89 | 监控仪表板测试初始化完成
2025-07-31 09:18:07 | INFO     | __main__:run_comprehensive_test:388 | 🚀 开始监控仪表板综合测试
2025-07-31 09:18:07 | INFO     | __main__:test_metrics_collector:93 | === 测试指标收集器 ===
2025-07-31 09:18:07 | INFO     | qlib_trading_system.monitoring.metrics_collector:__init__:101 | 指标收集器初始化完成
2025-07-31 09:18:07 | INFO     | __main__:test_metrics_collector:100 | 测试获取当前指标...
2025-07-31 09:18:07 | INFO     | __main__:test_metrics_collector:102 | 当前指标时间戳: 2025-07-31 09:18:07.155516
2025-07-31 09:18:07 | INFO     | __main__:test_metrics_collector:105 | 启动指标收集...
2025-07-31 09:18:07 | INFO     | qlib_trading_system.monitoring.metrics_collector:start_collection:112 | 指标收集器已启动
2025-07-31 09:18:09 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 18.04%
2025-07-31 09:18:10 | INFO     | __main__:test_metrics_collector:113 | 收集到 5 个历史数据点
2025-07-31 09:18:10 | INFO     | __main__:test_metrics_collector:117 | 生成了 1 个报警事件
2025-07-31 09:18:10 | INFO     | __main__:test_metrics_collector:121 | 汇总统计: {'avg_pnl': np.float64(1042.0959148706886), 'max_pnl': np.float64(1117.8533670629884), 'min_pnl': np.float64(916.1407230563653), 'avg_position_value': np.float64(51228.8580182036), 'total_trades_today': 45, 'alert_count': 1}
2025-07-31 09:18:10 | INFO     | __main__:test_metrics_collector:123 | ✅ 指标收集器测试通过
2025-07-31 09:18:10 | INFO     | __main__:test_alert_manager:132 | === 测试报警管理器 ===
2025-07-31 09:18:10 | INFO     | qlib_trading_system.monitoring.alert_manager:_setup_notification_channels:75 | 添加通知渠道: test_webhook (webhook)
2025-07-31 09:18:10 | INFO     | qlib_trading_system.monitoring.alert_manager:__init__:60 | 报警管理器初始化完成
2025-07-31 09:18:10 | ERROR    | __main__:test_alert_manager:163 | ❌ 报警管理器测试失败: name 'AlertEvent' is not defined
2025-07-31 09:18:10 | INFO     | __main__:run_comprehensive_test:406 | 启动仪表板服务器...
2025-07-31 09:18:10 | INFO     | qlib_trading_system.monitoring.metrics_collector:__init__:101 | 指标收集器初始化完成
2025-07-31 09:18:10 | INFO     | __main__:run_comprehensive_test:412 | 启动移动端API服务器...
2025-07-31 09:18:10 | INFO     | qlib_trading_system.monitoring.dashboard:__init__:155 | 实时监控仪表板初始化完成
2025-07-31 09:18:10 | INFO     | qlib_trading_system.monitoring.mobile_api:__init__:94 | 移动端API初始化完成
2025-07-31 09:18:10 | INFO     | qlib_trading_system.monitoring.dashboard:__init__:363 | 仪表板管理器初始化完成
2025-07-31 09:18:10 | INFO     | qlib_trading_system.monitoring.mobile_api:__init__:503 | 移动端API管理器初始化完成
2025-07-31 09:18:10 | INFO     | qlib_trading_system.monitoring.mobile_api:start_server:509 | 启动移动端API服务器: http://127.0.0.1:8081
2025-07-31 09:18:10 | INFO     | qlib_trading_system.monitoring.metrics_collector:start_collection:112 | 指标收集器已启动
2025-07-31 09:18:10 | INFO     | qlib_trading_system.monitoring.dashboard:start_server:312 | 启动仪表板服务器: http://127.0.0.1:8080
2025-07-31 09:18:10 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 18.04%
2025-07-31 09:18:10 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 18.04%
2025-07-31 09:18:11 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 15.19%
2025-07-31 09:18:11 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 18.04%
2025-07-31 09:18:12 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 16.07%
2025-07-31 09:18:12 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 18.04%
2025-07-31 09:18:12 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 16.07%
2025-07-31 09:18:12 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 18.04%
2025-07-31 09:18:13 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 16.07%
2025-07-31 09:18:13 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 26.19%
2025-07-31 09:18:13 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 16.07%
2025-07-31 09:18:14 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 26.19%
2025-07-31 09:18:14 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 16.07%
2025-07-31 09:18:14 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 26.19%
2025-07-31 09:18:15 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 16.07%
2025-07-31 09:18:15 | INFO     | __main__:test_dashboard_api:168 | === 测试仪表板API ===
2025-07-31 09:18:15 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 26.19%
2025-07-31 09:18:15 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 16.07%
2025-07-31 09:18:15 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 26.19%
2025-07-31 09:18:16 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 16.07%
2025-07-31 09:18:16 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 26.19%
2025-07-31 09:18:16 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 16.07%
2025-07-31 09:18:17 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 26.19%
2025-07-31 09:18:17 | INFO     | __main__:test_dashboard_api:177 | 测试健康检查...
2025-07-31 09:18:17 | INFO     | __main__:test_dashboard_api:180 | ✅ 健康检查通过
2025-07-31 09:18:17 | INFO     | __main__:test_dashboard_api:186 | 测试获取当前指标...
2025-07-31 09:18:17 | ERROR    | qlib_trading_system.monitoring.dashboard:get_current_metrics:172 | 获取当前指标失败: Object of type datetime is not JSON serializable
2025-07-31 09:18:17 | ERROR    | __main__:test_dashboard_api:192 | ❌ 获取当前指标失败: 500
2025-07-31 09:18:17 | INFO     | __main__:test_mobile_api:234 | === 测试移动端API ===
2025-07-31 09:18:17 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 16.07%
2025-07-31 09:18:17 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 26.19%
2025-07-31 09:18:18 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 16.07%
2025-07-31 09:18:18 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 26.19%
2025-07-31 09:18:18 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 16.07%
2025-07-31 09:18:18 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 26.19%
2025-07-31 09:18:19 | INFO     | __main__:test_mobile_api:243 | 测试移动端健康检查...
2025-07-31 09:18:19 | INFO     | __main__:test_mobile_api:246 | ✅ 移动端健康检查通过
2025-07-31 09:18:19 | INFO     | __main__:test_mobile_api:252 | 测试用户登录...
2025-07-31 09:18:19 | ERROR    | __main__:test_mobile_api:263 | ❌ 用户登录失败: 401
2025-07-31 09:18:19 | INFO     | __main__:test_websocket_connection:308 | === 测试WebSocket连接 ===
2025-07-31 09:18:19 | INFO     | qlib_trading_system.monitoring.dashboard:connect:84 | WebSocket连接已建立，当前连接数: 1
2025-07-31 09:18:19 | INFO     | __main__:on_open:329 | WebSocket连接已建立
2025-07-31 09:18:19 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 16.07%
2025-07-31 09:18:19 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 26.19%
2025-07-31 09:18:19 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 16.07%
2025-07-31 09:18:20 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 26.19%
2025-07-31 09:18:20 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 16.58%
2025-07-31 09:18:20 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 26.19%
2025-07-31 09:18:21 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 16.62%
2025-07-31 09:18:21 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 26.19%
2025-07-31 09:18:21 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 21.28%
2025-07-31 09:18:21 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 26.19%
2025-07-31 09:18:22 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 21.28%
2025-07-31 09:18:22 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 26.19%
2025-07-31 09:18:23 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 21.28%
2025-07-31 09:18:23 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 26.19%
2025-07-31 09:18:23 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 21.28%
2025-07-31 09:18:23 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 28.83%
2025-07-31 09:18:24 | INFO     | __main__:on_close:326 | WebSocket连接已关闭
2025-07-31 09:18:24 | INFO     | qlib_trading_system.monitoring.dashboard:disconnect:90 | WebSocket连接已断开，当前连接数: 0
2025-07-31 09:18:24 | WARNING  | __main__:test_websocket_connection:357 | ⚠️ WebSocket连接成功但未收到消息
2025-07-31 09:18:24 | INFO     | __main__:run_comprehensive_test:433 | 
==================================================
2025-07-31 09:18:24 | INFO     | __main__:run_comprehensive_test:434 | 📊 测试结果汇总
2025-07-31 09:18:24 | INFO     | __main__:run_comprehensive_test:435 | ==================================================
2025-07-31 09:18:24 | INFO     | __main__:run_comprehensive_test:442 | 指标收集器: ✅ 通过
2025-07-31 09:18:24 | INFO     | __main__:run_comprehensive_test:442 | 报警管理器: ❌ 失败
2025-07-31 09:18:24 | INFO     | __main__:run_comprehensive_test:442 | 仪表板API: ❌ 失败
2025-07-31 09:18:24 | INFO     | __main__:run_comprehensive_test:442 | 移动端API: ❌ 失败
2025-07-31 09:18:24 | INFO     | __main__:run_comprehensive_test:442 | WebSocket连接: ✅ 通过
2025-07-31 09:18:24 | INFO     | __main__:run_comprehensive_test:446 | ==================================================
2025-07-31 09:18:24 | INFO     | __main__:run_comprehensive_test:447 | 总计: 2/5 个测试通过
2025-07-31 09:18:24 | ERROR    | __main__:run_comprehensive_test:460 | ❌ 3 个测试失败
2025-07-31 09:18:24 | INFO     | __main__:cleanup:473 | 清理测试资源...
2025-07-31 09:18:24 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 21.28%
2025-07-31 09:18:24 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 28.83%
2025-07-31 09:18:24 | INFO     | qlib_trading_system.monitoring.metrics_collector:stop_collection:119 | 指标收集器已停止
2025-07-31 09:18:24 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 21.28%
2025-07-31 09:18:25 | INFO     | qlib_trading_system.monitoring.metrics_collector:stop_collection:119 | 指标收集器已停止
2025-07-31 09:18:25 | INFO     | qlib_trading_system.monitoring.dashboard:stop_server:341 | 仪表板服务器已停止
2025-07-31 09:18:25 | INFO     | qlib_trading_system.monitoring.dashboard:stop:402 | 监控系统已停止
2025-07-31 09:18:25 | INFO     | __main__:cleanup:473 | 清理测试资源...
2025-07-31 09:18:25 | INFO     | qlib_trading_system.monitoring.metrics_collector:stop_collection:119 | 指标收集器已停止
2025-07-31 09:18:25 | INFO     | qlib_trading_system.monitoring.metrics_collector:stop_collection:119 | 指标收集器已停止
2025-07-31 09:18:25 | INFO     | qlib_trading_system.monitoring.dashboard:stop_server:341 | 仪表板服务器已停止
2025-07-31 09:18:25 | INFO     | qlib_trading_system.monitoring.dashboard:stop:402 | 监控系统已停止
2025-07-31 09:19:33 | INFO     | __main__:__init__:89 | 监控仪表板测试初始化完成
2025-07-31 09:19:33 | INFO     | __main__:run_comprehensive_test:389 | 🚀 开始监控仪表板综合测试
2025-07-31 09:19:33 | INFO     | __main__:test_metrics_collector:93 | === 测试指标收集器 ===
2025-07-31 09:19:33 | INFO     | qlib_trading_system.monitoring.metrics_collector:__init__:101 | 指标收集器初始化完成
2025-07-31 09:19:33 | INFO     | __main__:test_metrics_collector:100 | 测试获取当前指标...
2025-07-31 09:19:33 | INFO     | __main__:test_metrics_collector:102 | 当前指标时间戳: 2025-07-31 09:19:33.510034
2025-07-31 09:19:33 | INFO     | __main__:test_metrics_collector:105 | 启动指标收集...
2025-07-31 09:19:33 | INFO     | qlib_trading_system.monitoring.metrics_collector:start_collection:112 | 指标收集器已启动
2025-07-31 09:19:34 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 14.69%
2025-07-31 09:19:35 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 14.69%
2025-07-31 09:19:36 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 14.69%
2025-07-31 09:19:36 | INFO     | __main__:test_metrics_collector:113 | 收集到 5 个历史数据点
2025-07-31 09:19:36 | INFO     | __main__:test_metrics_collector:117 | 生成了 3 个报警事件
2025-07-31 09:19:36 | INFO     | __main__:test_metrics_collector:121 | 汇总统计: {'avg_pnl': np.float64(992.1032815868364), 'max_pnl': np.float64(1086.5668948170344), 'min_pnl': np.float64(841.2792746612122), 'avg_position_value': np.float64(47284.62807900531), 'total_trades_today': 81, 'alert_count': 3}
2025-07-31 09:19:36 | INFO     | __main__:test_metrics_collector:123 | ✅ 指标收集器测试通过
2025-07-31 09:19:36 | INFO     | __main__:test_alert_manager:132 | === 测试报警管理器 ===
2025-07-31 09:19:36 | INFO     | qlib_trading_system.monitoring.alert_manager:_setup_notification_channels:75 | 添加通知渠道: test_webhook (webhook)
2025-07-31 09:19:36 | INFO     | qlib_trading_system.monitoring.alert_manager:__init__:60 | 报警管理器初始化完成
2025-07-31 09:19:36 | INFO     | __main__:test_alert_manager:149 | 处理测试报警...
2025-07-31 09:19:36 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 22.57%
2025-07-31 09:19:37 | INFO     | qlib_trading_system.monitoring.alert_manager:_send_webhook_notification:269 | Webhook通知发送成功: test_webhook
2025-07-31 09:19:37 | INFO     | qlib_trading_system.monitoring.alert_manager:process_alert:117 | 处理报警事件: [WARNING] TRADING - 测试报警消息
2025-07-31 09:19:37 | INFO     | __main__:test_alert_manager:154 | 报警统计: {'total_alerts': 1, 'level_distribution': {'WARNING': 1}, 'category_distribution': {'TRADING': 1}, 'time_range_hours': 1, 'active_channels': 1}
2025-07-31 09:19:37 | INFO     | __main__:test_alert_manager:157 | 测试通知渠道...
2025-07-31 09:19:37 | INFO     | qlib_trading_system.monitoring.alert_manager:test_notification_channels:403 | 测试通知渠道: test_webhook
2025-07-31 09:19:37 | INFO     | __main__:test_alert_manager:160 | ✅ 报警管理器测试通过
2025-07-31 09:19:37 | INFO     | __main__:run_comprehensive_test:407 | 启动仪表板服务器...
2025-07-31 09:19:37 | INFO     | qlib_trading_system.monitoring.metrics_collector:__init__:101 | 指标收集器初始化完成
2025-07-31 09:19:37 | INFO     | __main__:run_comprehensive_test:413 | 启动移动端API服务器...
2025-07-31 09:19:37 | INFO     | qlib_trading_system.monitoring.dashboard:__init__:155 | 实时监控仪表板初始化完成
2025-07-31 09:19:37 | INFO     | qlib_trading_system.monitoring.mobile_api:__init__:99 | 移动端API初始化完成
2025-07-31 09:19:37 | INFO     | qlib_trading_system.monitoring.dashboard:__init__:367 | 仪表板管理器初始化完成
2025-07-31 09:19:37 | INFO     | qlib_trading_system.monitoring.mobile_api:__init__:508 | 移动端API管理器初始化完成
2025-07-31 09:19:37 | INFO     | qlib_trading_system.monitoring.mobile_api:start_server:514 | 启动移动端API服务器: http://127.0.0.1:8081
2025-07-31 09:19:37 | INFO     | qlib_trading_system.monitoring.metrics_collector:start_collection:112 | 指标收集器已启动
2025-07-31 09:19:37 | INFO     | qlib_trading_system.monitoring.dashboard:start_server:316 | 启动仪表板服务器: http://127.0.0.1:8080
2025-07-31 09:19:37 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 22.57%
2025-07-31 09:19:37 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:19:37 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 22.57%
2025-07-31 09:19:37 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:19:38 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 22.57%
2025-07-31 09:19:38 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 15.51%
2025-07-31 09:19:38 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:19:39 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 22.57%
2025-07-31 09:19:39 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 25.74%
2025-07-31 09:19:39 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:19:39 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 22.57%
2025-07-31 09:19:39 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 25.74%
2025-07-31 09:19:39 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:19:40 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 22.57%
2025-07-31 09:19:40 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 25.74%
2025-07-31 09:19:40 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:19:40 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 22.57%
2025-07-31 09:19:40 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 25.74%
2025-07-31 09:19:40 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:19:41 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 22.57%
2025-07-31 09:19:41 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 25.74%
2025-07-31 09:19:41 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:19:42 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 22.57%
2025-07-31 09:19:42 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 25.74%
2025-07-31 09:19:42 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:19:42 | INFO     | __main__:test_dashboard_api:169 | === 测试仪表板API ===
2025-07-31 09:19:42 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 22.57%
2025-07-31 09:19:42 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 25.74%
2025-07-31 09:19:42 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:19:43 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 22.57%
2025-07-31 09:19:43 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 25.74%
2025-07-31 09:19:43 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:19:43 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 22.57%
2025-07-31 09:19:43 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 25.74%
2025-07-31 09:19:43 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:19:44 | INFO     | __main__:test_dashboard_api:178 | 测试健康检查...
2025-07-31 09:19:44 | INFO     | __main__:test_dashboard_api:181 | ✅ 健康检查通过
2025-07-31 09:19:44 | INFO     | __main__:test_dashboard_api:187 | 测试获取当前指标...
2025-07-31 09:19:44 | INFO     | __main__:test_dashboard_api:191 | ✅ 获取当前指标成功: 2025-07-31T09:19:43.867091
2025-07-31 09:19:44 | INFO     | __main__:test_dashboard_api:197 | 测试获取历史指标...
2025-07-31 09:19:44 | ERROR    | qlib_trading_system.monitoring.dashboard:get_metrics_history:186 | 获取历史指标失败: Object of type datetime is not JSON serializable
2025-07-31 09:19:44 | ERROR    | __main__:test_dashboard_api:203 | ❌ 获取历史指标失败: 500
2025-07-31 09:19:44 | INFO     | __main__:test_mobile_api:235 | === 测试移动端API ===
2025-07-31 09:19:44 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 22.57%
2025-07-31 09:19:44 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 25.74%
2025-07-31 09:19:44 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:19:45 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 22.57%
2025-07-31 09:19:45 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 25.74%
2025-07-31 09:19:45 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:19:45 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 22.57%
2025-07-31 09:19:45 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 25.74%
2025-07-31 09:19:45 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:19:46 | INFO     | __main__:test_mobile_api:244 | 测试移动端健康检查...
2025-07-31 09:19:46 | INFO     | __main__:test_mobile_api:247 | ✅ 移动端健康检查通过
2025-07-31 09:19:46 | INFO     | __main__:test_mobile_api:253 | 测试用户登录...
2025-07-31 09:19:46 | ERROR    | __main__:test_mobile_api:264 | ❌ 用户登录失败: 401
2025-07-31 09:19:46 | INFO     | __main__:test_websocket_connection:309 | === 测试WebSocket连接 ===
2025-07-31 09:19:46 | INFO     | qlib_trading_system.monitoring.dashboard:connect:84 | WebSocket连接已建立，当前连接数: 1
2025-07-31 09:19:46 | INFO     | __main__:on_open:330 | WebSocket连接已建立
2025-07-31 09:19:46 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 22.57%
2025-07-31 09:19:46 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 34.91%
2025-07-31 09:19:46 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:19:46 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 22.57%
2025-07-31 09:19:47 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 34.91%
2025-07-31 09:19:47 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:19:47 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 22.57%
2025-07-31 09:19:47 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 34.91%
2025-07-31 09:19:47 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:19:48 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 22.57%
2025-07-31 09:19:48 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 34.91%
2025-07-31 09:19:48 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:19:48 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 22.57%
2025-07-31 09:19:48 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 34.91%
2025-07-31 09:19:48 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:19:49 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.45%
2025-07-31 09:19:49 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 34.91%
2025-07-31 09:19:49 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:19:50 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.45%
2025-07-31 09:19:50 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 34.91%
2025-07-31 09:19:50 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:19:50 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.45%
2025-07-31 09:19:50 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 34.91%
2025-07-31 09:19:50 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:19:51 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.45%
2025-07-31 09:19:51 | INFO     | __main__:on_close:327 | WebSocket连接已关闭
2025-07-31 09:19:51 | WARNING  | __main__:test_websocket_connection:358 | ⚠️ WebSocket连接成功但未收到消息
2025-07-31 09:19:51 | INFO     | qlib_trading_system.monitoring.dashboard:disconnect:90 | WebSocket连接已断开，当前连接数: 0
2025-07-31 09:19:51 | INFO     | __main__:run_comprehensive_test:434 | 
==================================================
2025-07-31 09:19:51 | INFO     | __main__:run_comprehensive_test:435 | 📊 测试结果汇总
2025-07-31 09:19:51 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 34.91%
2025-07-31 09:19:51 | INFO     | __main__:run_comprehensive_test:436 | ==================================================
2025-07-31 09:19:51 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:19:51 | INFO     | __main__:run_comprehensive_test:443 | 指标收集器: ✅ 通过
2025-07-31 09:19:51 | INFO     | __main__:run_comprehensive_test:443 | 报警管理器: ✅ 通过
2025-07-31 09:19:51 | INFO     | __main__:run_comprehensive_test:443 | 仪表板API: ❌ 失败
2025-07-31 09:19:51 | INFO     | __main__:run_comprehensive_test:443 | 移动端API: ❌ 失败
2025-07-31 09:19:51 | INFO     | __main__:run_comprehensive_test:443 | WebSocket连接: ✅ 通过
2025-07-31 09:19:51 | INFO     | __main__:run_comprehensive_test:447 | ==================================================
2025-07-31 09:19:51 | INFO     | __main__:run_comprehensive_test:448 | 总计: 3/5 个测试通过
2025-07-31 09:19:51 | ERROR    | __main__:run_comprehensive_test:461 | ❌ 2 个测试失败
2025-07-31 09:19:51 | INFO     | __main__:cleanup:474 | 清理测试资源...
2025-07-31 09:19:51 | INFO     | qlib_trading_system.monitoring.metrics_collector:stop_collection:119 | 指标收集器已停止
2025-07-31 09:19:51 | INFO     | qlib_trading_system.monitoring.metrics_collector:stop_collection:119 | 指标收集器已停止
2025-07-31 09:19:51 | INFO     | qlib_trading_system.monitoring.dashboard:stop_server:345 | 仪表板服务器已停止
2025-07-31 09:19:51 | INFO     | qlib_trading_system.monitoring.dashboard:stop:406 | 监控系统已停止
2025-07-31 09:19:51 | INFO     | __main__:cleanup:474 | 清理测试资源...
2025-07-31 09:19:51 | INFO     | qlib_trading_system.monitoring.metrics_collector:stop_collection:119 | 指标收集器已停止
2025-07-31 09:19:51 | INFO     | qlib_trading_system.monitoring.metrics_collector:stop_collection:119 | 指标收集器已停止
2025-07-31 09:19:51 | INFO     | qlib_trading_system.monitoring.dashboard:stop_server:345 | 仪表板服务器已停止
2025-07-31 09:19:51 | INFO     | qlib_trading_system.monitoring.dashboard:stop:406 | 监控系统已停止
2025-07-31 09:21:09 | INFO     | __main__:__init__:89 | 监控仪表板测试初始化完成
2025-07-31 09:21:09 | INFO     | __main__:run_comprehensive_test:389 | 🚀 开始监控仪表板综合测试
2025-07-31 09:21:09 | INFO     | __main__:test_metrics_collector:93 | === 测试指标收集器 ===
2025-07-31 09:21:09 | INFO     | qlib_trading_system.monitoring.metrics_collector:__init__:101 | 指标收集器初始化完成
2025-07-31 09:21:09 | INFO     | __main__:test_metrics_collector:100 | 测试获取当前指标...
2025-07-31 09:21:09 | INFO     | __main__:test_metrics_collector:102 | 当前指标时间戳: 2025-07-31 09:21:09.008770
2025-07-31 09:21:09 | INFO     | __main__:test_metrics_collector:105 | 启动指标收集...
2025-07-31 09:21:09 | INFO     | qlib_trading_system.monitoring.metrics_collector:start_collection:112 | 指标收集器已启动
2025-07-31 09:21:10 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 16.31%
2025-07-31 09:21:10 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 19.38%
2025-07-31 09:21:11 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 19.38%
2025-07-31 09:21:12 | INFO     | __main__:test_metrics_collector:113 | 收集到 5 个历史数据点
2025-07-31 09:21:12 | INFO     | __main__:test_metrics_collector:117 | 生成了 3 个报警事件
2025-07-31 09:21:12 | INFO     | __main__:test_metrics_collector:121 | 汇总统计: {'avg_pnl': np.float64(1040.8820441340013), 'max_pnl': np.float64(1159.5814224944645), 'min_pnl': np.float64(930.7193834124025), 'avg_position_value': np.float64(47687.76711136794), 'total_trades_today': 73, 'alert_count': 3}
2025-07-31 09:21:12 | INFO     | __main__:test_metrics_collector:123 | ✅ 指标收集器测试通过
2025-07-31 09:21:12 | INFO     | __main__:test_alert_manager:132 | === 测试报警管理器 ===
2025-07-31 09:21:12 | INFO     | qlib_trading_system.monitoring.alert_manager:_setup_notification_channels:75 | 添加通知渠道: test_webhook (webhook)
2025-07-31 09:21:12 | INFO     | qlib_trading_system.monitoring.alert_manager:__init__:60 | 报警管理器初始化完成
2025-07-31 09:21:12 | INFO     | __main__:test_alert_manager:149 | 处理测试报警...
2025-07-31 09:21:12 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 19.38%
2025-07-31 09:21:12 | INFO     | qlib_trading_system.monitoring.alert_manager:_send_webhook_notification:269 | Webhook通知发送成功: test_webhook
2025-07-31 09:21:12 | INFO     | qlib_trading_system.monitoring.alert_manager:process_alert:117 | 处理报警事件: [WARNING] TRADING - 测试报警消息
2025-07-31 09:21:12 | INFO     | __main__:test_alert_manager:154 | 报警统计: {'total_alerts': 1, 'level_distribution': {'WARNING': 1}, 'category_distribution': {'TRADING': 1}, 'time_range_hours': 1, 'active_channels': 1}
2025-07-31 09:21:12 | INFO     | __main__:test_alert_manager:157 | 测试通知渠道...
2025-07-31 09:21:12 | INFO     | qlib_trading_system.monitoring.alert_manager:test_notification_channels:403 | 测试通知渠道: test_webhook
2025-07-31 09:21:12 | INFO     | __main__:test_alert_manager:160 | ✅ 报警管理器测试通过
2025-07-31 09:21:12 | INFO     | __main__:run_comprehensive_test:407 | 启动仪表板服务器...
2025-07-31 09:21:12 | INFO     | qlib_trading_system.monitoring.metrics_collector:__init__:101 | 指标收集器初始化完成
2025-07-31 09:21:12 | INFO     | __main__:run_comprehensive_test:413 | 启动移动端API服务器...
2025-07-31 09:21:12 | INFO     | qlib_trading_system.monitoring.dashboard:__init__:155 | 实时监控仪表板初始化完成
2025-07-31 09:21:12 | INFO     | qlib_trading_system.monitoring.mobile_api:__init__:99 | 移动端API初始化完成
2025-07-31 09:21:12 | INFO     | qlib_trading_system.monitoring.dashboard:__init__:374 | 仪表板管理器初始化完成
2025-07-31 09:21:12 | INFO     | qlib_trading_system.monitoring.mobile_api:__init__:508 | 移动端API管理器初始化完成
2025-07-31 09:21:12 | INFO     | qlib_trading_system.monitoring.mobile_api:start_server:514 | 启动移动端API服务器: http://127.0.0.1:8081
2025-07-31 09:21:12 | INFO     | qlib_trading_system.monitoring.metrics_collector:start_collection:112 | 指标收集器已启动
2025-07-31 09:21:12 | INFO     | qlib_trading_system.monitoring.dashboard:start_server:323 | 启动仪表板服务器: http://127.0.0.1:8080
2025-07-31 09:21:12 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 28.54%
2025-07-31 09:21:12 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:13 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 28.54%
2025-07-31 09:21:13 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:13 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 28.54%
2025-07-31 09:21:14 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:14 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 28.54%
2025-07-31 09:21:14 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 20.07%
2025-07-31 09:21:14 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:15 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 28.54%
2025-07-31 09:21:15 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 20.07%
2025-07-31 09:21:15 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:15 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 28.54%
2025-07-31 09:21:15 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 20.30%
2025-07-31 09:21:15 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:16 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 28.54%
2025-07-31 09:21:16 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 20.30%
2025-07-31 09:21:16 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:17 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 28.54%
2025-07-31 09:21:17 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 20.30%
2025-07-31 09:21:17 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:17 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:17 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 20.30%
2025-07-31 09:21:17 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:17 | INFO     | __main__:test_dashboard_api:169 | === 测试仪表板API ===
2025-07-31 09:21:18 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:18 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 20.30%
2025-07-31 09:21:18 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:18 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:18 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 20.30%
2025-07-31 09:21:18 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:19 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:19 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 20.30%
2025-07-31 09:21:19 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:19 | INFO     | __main__:test_dashboard_api:178 | 测试健康检查...
2025-07-31 09:21:19 | INFO     | __main__:test_dashboard_api:181 | ✅ 健康检查通过
2025-07-31 09:21:19 | INFO     | __main__:test_dashboard_api:187 | 测试获取当前指标...
2025-07-31 09:21:19 | INFO     | __main__:test_dashboard_api:191 | ✅ 获取当前指标成功: 2025-07-31T09:21:19.415090
2025-07-31 09:21:19 | INFO     | __main__:test_dashboard_api:197 | 测试获取历史指标...
2025-07-31 09:21:19 | INFO     | __main__:test_dashboard_api:201 | ✅ 获取历史指标成功: 12 个数据点
2025-07-31 09:21:19 | INFO     | __main__:test_dashboard_api:207 | 测试获取报警信息...
2025-07-31 09:21:19 | INFO     | __main__:test_dashboard_api:211 | ✅ 获取报警信息成功: 9 个报警
2025-07-31 09:21:19 | INFO     | __main__:test_dashboard_api:217 | 测试获取汇总信息...
2025-07-31 09:21:19 | INFO     | __main__:test_dashboard_api:221 | ✅ 获取汇总信息成功
2025-07-31 09:21:19 | INFO     | __main__:test_dashboard_api:226 | ✅ 仪表板API测试通过
2025-07-31 09:21:19 | INFO     | __main__:test_mobile_api:235 | === 测试移动端API ===
2025-07-31 09:21:20 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:20 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 20.30%
2025-07-31 09:21:20 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:20 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:20 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 20.30%
2025-07-31 09:21:20 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:21 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:21 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 20.30%
2025-07-31 09:21:21 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:21 | INFO     | __main__:test_mobile_api:244 | 测试移动端健康检查...
2025-07-31 09:21:21 | INFO     | __main__:test_mobile_api:247 | ✅ 移动端健康检查通过
2025-07-31 09:21:21 | INFO     | __main__:test_mobile_api:253 | 测试用户登录...
2025-07-31 09:21:21 | INFO     | qlib_trading_system.monitoring.mobile_api:login:167 | 用户登录成功: test_admin
2025-07-31 09:21:21 | INFO     | __main__:test_mobile_api:262 | ✅ 用户登录成功
2025-07-31 09:21:21 | INFO     | __main__:test_mobile_api:271 | 测试移动端获取当前指标...
2025-07-31 09:21:21 | INFO     | __main__:test_mobile_api:275 | ✅ 移动端获取当前指标成功
2025-07-31 09:21:21 | INFO     | __main__:test_mobile_api:281 | 测试移动端获取汇总信息...
2025-07-31 09:21:21 | INFO     | __main__:test_mobile_api:285 | ✅ 移动端获取汇总信息成功: 状态=healthy
2025-07-31 09:21:21 | INFO     | __main__:test_mobile_api:291 | 测试移动端获取报警信息...
2025-07-31 09:21:21 | INFO     | __main__:test_mobile_api:295 | ✅ 移动端获取报警信息成功: 19 个报警
2025-07-31 09:21:21 | INFO     | __main__:test_mobile_api:300 | ✅ 移动端API测试通过
2025-07-31 09:21:21 | INFO     | __main__:test_websocket_connection:309 | === 测试WebSocket连接 ===
2025-07-31 09:21:21 | INFO     | qlib_trading_system.monitoring.dashboard:connect:84 | WebSocket连接已建立，当前连接数: 1
2025-07-31 09:21:21 | INFO     | __main__:on_open:330 | WebSocket连接已建立
2025-07-31 09:21:21 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:21 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 20.30%
2025-07-31 09:21:21 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:22 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:22 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 20.30%
2025-07-31 09:21:22 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:23 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:23 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 20.30%
2025-07-31 09:21:23 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:23 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:23 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 20.30%
2025-07-31 09:21:23 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:24 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:24 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 20.30%
2025-07-31 09:21:24 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:24 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:24 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 20.30%
2025-07-31 09:21:24 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:25 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:25 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 20.30%
2025-07-31 09:21:25 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:26 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:26 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 20.30%
2025-07-31 09:21:26 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:26 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:26 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 20.30%
2025-07-31 09:21:26 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:26 | INFO     | __main__:on_close:327 | WebSocket连接已关闭
2025-07-31 09:21:26 | WARNING  | __main__:test_websocket_connection:358 | ⚠️ WebSocket连接成功但未收到消息
2025-07-31 09:21:26 | INFO     | qlib_trading_system.monitoring.dashboard:disconnect:90 | WebSocket连接已断开，当前连接数: 0
2025-07-31 09:21:26 | INFO     | __main__:run_comprehensive_test:434 | 
==================================================
2025-07-31 09:21:26 | INFO     | __main__:run_comprehensive_test:435 | 📊 测试结果汇总
2025-07-31 09:21:26 | INFO     | __main__:run_comprehensive_test:436 | ==================================================
2025-07-31 09:21:26 | INFO     | __main__:run_comprehensive_test:443 | 指标收集器: ✅ 通过
2025-07-31 09:21:26 | INFO     | __main__:run_comprehensive_test:443 | 报警管理器: ✅ 通过
2025-07-31 09:21:26 | INFO     | __main__:run_comprehensive_test:443 | 仪表板API: ✅ 通过
2025-07-31 09:21:26 | INFO     | __main__:run_comprehensive_test:443 | 移动端API: ✅ 通过
2025-07-31 09:21:26 | INFO     | __main__:run_comprehensive_test:443 | WebSocket连接: ✅ 通过
2025-07-31 09:21:26 | INFO     | __main__:run_comprehensive_test:447 | ==================================================
2025-07-31 09:21:26 | INFO     | __main__:run_comprehensive_test:448 | 总计: 5/5 个测试通过
2025-07-31 09:21:26 | INFO     | __main__:run_comprehensive_test:451 | 🎉 所有测试通过！监控仪表板系统运行正常
2025-07-31 09:21:26 | INFO     | __main__:run_comprehensive_test:454 | 服务器将继续运行60秒供手动测试...
2025-07-31 09:21:26 | INFO     | __main__:run_comprehensive_test:455 | 仪表板地址: http://127.0.0.1:8080
2025-07-31 09:21:26 | INFO     | __main__:run_comprehensive_test:456 | 移动端API地址: http://127.0.0.1:8081
2025-07-31 09:21:27 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:27 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 24.81%
2025-07-31 09:21:27 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:27 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:28 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 24.81%
2025-07-31 09:21:28 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:28 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:28 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 24.81%
2025-07-31 09:21:28 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:29 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:29 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.78%
2025-07-31 09:21:29 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:29 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:29 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.78%
2025-07-31 09:21:29 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:30 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:30 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.78%
2025-07-31 09:21:30 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:30 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:31 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 32.85%
2025-07-31 09:21:31 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:31 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:31 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 32.85%
2025-07-31 09:21:31 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:32 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:32 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 32.85%
2025-07-31 09:21:32 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:32 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:32 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 32.85%
2025-07-31 09:21:32 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:33 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:33 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 32.85%
2025-07-31 09:21:33 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:33 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:34 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 32.85%
2025-07-31 09:21:34 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:34 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:34 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 32.85%
2025-07-31 09:21:34 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:35 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:35 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 32.85%
2025-07-31 09:21:35 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:35 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:35 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 32.85%
2025-07-31 09:21:35 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:36 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:36 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 32.85%
2025-07-31 09:21:36 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] SYSTEM - CPU使用率过高: 75.0%
2025-07-31 09:21:36 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:37 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:37 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] SYSTEM - CPU使用率过高: 93.9%
2025-07-31 09:21:37 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 32.85%
2025-07-31 09:21:37 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:37 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:37 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 32.85%
2025-07-31 09:21:37 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:38 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:38 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 32.85%
2025-07-31 09:21:38 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:38 | INFO     | qlib_trading_system.monitoring.dashboard:connect:84 | WebSocket连接已建立，当前连接数: 1
2025-07-31 09:21:38 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:38 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 32.85%
2025-07-31 09:21:38 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:39 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:39 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 32.85%
2025-07-31 09:21:39 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:40 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:40 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 32.85%
2025-07-31 09:21:40 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:40 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:40 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 32.85%
2025-07-31 09:21:40 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:41 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:41 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 32.85%
2025-07-31 09:21:41 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:41 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:42 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 32.85%
2025-07-31 09:21:42 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:42 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:42 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 32.85%
2025-07-31 09:21:42 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:43 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:43 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 32.85%
2025-07-31 09:21:43 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:43 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:43 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 34.50%
2025-07-31 09:21:43 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:44 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:44 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 34.50%
2025-07-31 09:21:44 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:44 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:45 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 34.50%
2025-07-31 09:21:45 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:45 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:45 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 34.50%
2025-07-31 09:21:45 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:46 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:46 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 34.50%
2025-07-31 09:21:46 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:46 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:46 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 34.50%
2025-07-31 09:21:46 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:47 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:47 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 34.50%
2025-07-31 09:21:47 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:47 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:48 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.37%
2025-07-31 09:21:48 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:48 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:48 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.37%
2025-07-31 09:21:48 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:49 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:49 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.37%
2025-07-31 09:21:49 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:49 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:49 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.37%
2025-07-31 09:21:49 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:50 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:50 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.37%
2025-07-31 09:21:50 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] SYSTEM - CPU使用率过高: 71.9%
2025-07-31 09:21:50 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:50 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:51 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.37%
2025-07-31 09:21:51 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:51 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:51 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 39.70%
2025-07-31 09:21:51 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:52 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:52 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 39.70%
2025-07-31 09:21:52 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:52 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:52 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 39.70%
2025-07-31 09:21:52 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:53 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:53 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 39.70%
2025-07-31 09:21:53 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:54 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:54 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 39.70%
2025-07-31 09:21:54 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:54 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:54 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 39.70%
2025-07-31 09:21:54 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:55 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:55 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 52.48%
2025-07-31 09:21:55 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:55 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:55 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 52.48%
2025-07-31 09:21:55 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:56 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:56 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 52.48%
2025-07-31 09:21:56 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:57 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:57 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 52.48%
2025-07-31 09:21:57 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:57 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:57 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 52.48%
2025-07-31 09:21:57 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:58 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:58 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 52.48%
2025-07-31 09:21:58 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:58 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:59 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 52.48%
2025-07-31 09:21:59 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:21:59 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:21:59 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 52.48%
2025-07-31 09:21:59 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:22:00 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:22:00 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 52.48%
2025-07-31 09:22:00 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:22:00 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:22:00 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 52.48%
2025-07-31 09:22:00 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:22:01 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:22:01 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 52.48%
2025-07-31 09:22:01 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:22:01 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:22:02 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 52.48%
2025-07-31 09:22:02 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:22:02 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:22:02 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 52.48%
2025-07-31 09:22:02 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:22:03 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:22:03 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 52.48%
2025-07-31 09:22:03 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:22:03 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:22:03 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 52.48%
2025-07-31 09:22:03 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:22:04 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:22:04 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 52.48%
2025-07-31 09:22:04 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:22:04 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:22:05 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 52.48%
2025-07-31 09:22:05 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] SYSTEM - CPU使用率过高: 81.0%
2025-07-31 09:22:05 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:22:05 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:22:05 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 52.48%
2025-07-31 09:22:05 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:22:06 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:22:06 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 52.48%
2025-07-31 09:22:06 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:22:06 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:22:06 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 52.48%
2025-07-31 09:22:06 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:22:07 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:22:07 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 52.48%
2025-07-31 09:22:07 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:22:07 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:22:08 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 52.48%
2025-07-31 09:22:08 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:22:08 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:22:08 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 52.48%
2025-07-31 09:22:08 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:22:09 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:22:09 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 52.48%
2025-07-31 09:22:09 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:22:09 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:22:09 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 52.48%
2025-07-31 09:22:09 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:22:10 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:22:10 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 52.48%
2025-07-31 09:22:10 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:22:11 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:22:11 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 52.48%
2025-07-31 09:22:11 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:22:11 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:22:11 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 52.48%
2025-07-31 09:22:11 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:22:12 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:22:12 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 52.48%
2025-07-31 09:22:12 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:22:12 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:22:13 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 52.48%
2025-07-31 09:22:13 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:22:13 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:22:13 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 52.48%
2025-07-31 09:22:13 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:22:14 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:22:14 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 52.48%
2025-07-31 09:22:14 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:22:14 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 38.09%
2025-07-31 09:22:14 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 52.48%
2025-07-31 09:22:14 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:22:15 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 36.84%
2025-07-31 09:22:15 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 52.48%
2025-07-31 09:22:15 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:22:15 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 36.84%
2025-07-31 09:22:16 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 52.48%
2025-07-31 09:22:16 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:22:16 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 36.84%
2025-07-31 09:22:16 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 52.48%
2025-07-31 09:22:16 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:22:17 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 36.84%
2025-07-31 09:22:17 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 52.48%
2025-07-31 09:22:17 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:22:17 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 36.84%
2025-07-31 09:22:17 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 52.48%
2025-07-31 09:22:17 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:22:18 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 36.84%
2025-07-31 09:22:18 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 52.48%
2025-07-31 09:22:18 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:22:18 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 36.84%
2025-07-31 09:22:19 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 52.48%
2025-07-31 09:22:19 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:22:19 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 36.36%
2025-07-31 09:22:19 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 52.48%
2025-07-31 09:22:19 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:22:20 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 36.36%
2025-07-31 09:22:20 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 52.48%
2025-07-31 09:22:20 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:22:20 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 36.36%
2025-07-31 09:22:20 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 52.48%
2025-07-31 09:22:20 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:22:21 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 39.72%
2025-07-31 09:22:21 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 52.48%
2025-07-31 09:22:21 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:22:21 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 39.72%
2025-07-31 09:22:22 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 52.48%
2025-07-31 09:22:22 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:22:22 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 39.72%
2025-07-31 09:22:22 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 52.48%
2025-07-31 09:22:22 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:22:22 | INFO     | __main__:cleanup:474 | 清理测试资源...
2025-07-31 09:22:23 | INFO     | qlib_trading_system.monitoring.metrics_collector:stop_collection:119 | 指标收集器已停止
2025-07-31 09:22:23 | INFO     | qlib_trading_system.monitoring.metrics_collector:stop_collection:119 | 指标收集器已停止
2025-07-31 09:22:23 | INFO     | qlib_trading_system.monitoring.dashboard:stop_server:352 | 仪表板服务器已停止
2025-07-31 09:22:23 | INFO     | qlib_trading_system.monitoring.dashboard:stop:413 | 监控系统已停止
2025-07-31 09:22:23 | INFO     | __main__:cleanup:474 | 清理测试资源...
2025-07-31 09:22:23 | INFO     | qlib_trading_system.monitoring.metrics_collector:stop_collection:119 | 指标收集器已停止
2025-07-31 09:22:23 | INFO     | qlib_trading_system.monitoring.metrics_collector:stop_collection:119 | 指标收集器已停止
2025-07-31 09:22:23 | INFO     | qlib_trading_system.monitoring.dashboard:stop_server:352 | 仪表板服务器已停止
2025-07-31 09:22:23 | INFO     | qlib_trading_system.monitoring.dashboard:stop:413 | 监控系统已停止
2025-07-31 09:28:15 | INFO     | qlib_trading_system.monitoring.metrics_collector:__init__:101 | 指标收集器初始化完成
2025-07-31 09:28:15 | INFO     | qlib_trading_system.monitoring.dashboard:__init__:155 | 实时监控仪表板初始化完成
2025-07-31 09:28:15 | INFO     | qlib_trading_system.monitoring.dashboard:__init__:374 | 仪表板管理器初始化完成
2025-07-31 09:28:15 | INFO     | qlib_trading_system.monitoring.metrics_collector:start_collection:112 | 指标收集器已启动
2025-07-31 09:28:15 | INFO     | qlib_trading_system.monitoring.dashboard:start_server:323 | 启动仪表板服务器: http://127.0.0.1:8080
2025-07-31 09:28:15 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:28:16 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:28:17 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:28:19 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 5.23%
2025-07-31 09:28:19 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:28:20 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 23.54%
2025-07-31 09:28:20 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:28:21 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 23.54%
2025-07-31 09:28:21 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:28:22 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 23.54%
2025-07-31 09:28:22 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:28:23 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 26.81%
2025-07-31 09:28:23 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:28:24 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 26.81%
2025-07-31 09:28:24 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:28:25 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 26.81%
2025-07-31 09:28:25 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:28:26 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 33.62%
2025-07-31 09:28:26 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:28:27 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 33.62%
2025-07-31 09:28:27 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:28:29 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 33.62%
2025-07-31 09:28:29 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:28:30 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 33.62%
2025-07-31 09:28:30 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:28:31 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 33.62%
2025-07-31 09:28:31 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:28:32 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 33.62%
2025-07-31 09:28:32 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:28:33 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 33.62%
2025-07-31 09:28:33 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:28:34 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 33.62%
2025-07-31 09:28:34 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:28:35 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 33.62%
2025-07-31 09:28:35 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:28:36 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 33.62%
2025-07-31 09:28:36 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:28:37 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 33.62%
2025-07-31 09:28:37 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:28:38 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 33.62%
2025-07-31 09:28:38 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:28:40 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 33.62%
2025-07-31 09:28:40 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:28:41 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 33.62%
2025-07-31 09:28:41 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:28:42 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 33.62%
2025-07-31 09:28:42 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:28:43 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 33.62%
2025-07-31 09:28:43 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:28:44 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 33.62%
2025-07-31 09:28:44 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:28:45 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 33.62%
2025-07-31 09:28:45 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:28:46 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 33.62%
2025-07-31 09:28:46 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:28:47 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 33.62%
2025-07-31 09:28:47 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:28:48 | INFO     | qlib_trading_system.monitoring.metrics_collector:stop_collection:119 | 指标收集器已停止
2025-07-31 09:28:48 | INFO     | qlib_trading_system.monitoring.dashboard:stop_server:352 | 仪表板服务器已停止
2025-07-31 09:28:48 | INFO     | qlib_trading_system.monitoring.dashboard:stop:413 | 监控系统已停止
2025-07-31 09:38:05 | INFO     | qlib_trading_system.monitoring.metrics_collector:__init__:101 | 指标收集器初始化完成
2025-07-31 09:38:05 | INFO     | qlib_trading_system.monitoring.dashboard:__init__:155 | 实时监控仪表板初始化完成
2025-07-31 09:38:05 | INFO     | qlib_trading_system.monitoring.dashboard:__init__:374 | 仪表板管理器初始化完成
2025-07-31 09:38:05 | INFO     | qlib_trading_system.monitoring.metrics_collector:start_collection:112 | 指标收集器已启动
2025-07-31 09:38:05 | INFO     | qlib_trading_system.monitoring.dashboard:start_server:323 | 启动仪表板服务器: http://127.0.0.1:8080
2025-07-31 09:38:05 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:38:06 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:38:07 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 5.22%
2025-07-31 09:38:07 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:38:08 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 5.22%
2025-07-31 09:38:08 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:38:09 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 12.11%
2025-07-31 09:38:09 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:38:10 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 13.79%
2025-07-31 09:38:10 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:38:12 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 25.16%
2025-07-31 09:38:12 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:38:13 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 25.16%
2025-07-31 09:38:13 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:38:14 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 25.16%
2025-07-31 09:38:14 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:38:15 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 25.16%
2025-07-31 09:38:15 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:38:16 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 25.16%
2025-07-31 09:38:16 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:38:17 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 25.16%
2025-07-31 09:38:17 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:38:18 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 25.16%
2025-07-31 09:38:18 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:38:19 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 25.16%
2025-07-31 09:38:19 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:38:20 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 25.16%
2025-07-31 09:38:20 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:38:21 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 25.16%
2025-07-31 09:38:21 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:38:23 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 25.16%
2025-07-31 09:38:23 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:38:24 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 25.16%
2025-07-31 09:38:24 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:38:25 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 25.16%
2025-07-31 09:38:25 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:38:26 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 25.16%
2025-07-31 09:38:26 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:38:27 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 25.16%
2025-07-31 09:38:27 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:38:28 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.01%
2025-07-31 09:38:28 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:38:29 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.01%
2025-07-31 09:38:29 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:38:30 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.01%
2025-07-31 09:38:30 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:38:31 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.01%
2025-07-31 09:38:31 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:38:33 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.01%
2025-07-31 09:38:33 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:38:34 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.01%
2025-07-31 09:38:34 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:38:35 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.01%
2025-07-31 09:38:35 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:38:36 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.01%
2025-07-31 09:38:36 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:38:37 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.01%
2025-07-31 09:38:37 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:38:38 | INFO     | qlib_trading_system.monitoring.metrics_collector:stop_collection:119 | 指标收集器已停止
2025-07-31 09:38:38 | INFO     | qlib_trading_system.monitoring.dashboard:stop_server:352 | 仪表板服务器已停止
2025-07-31 09:38:38 | INFO     | qlib_trading_system.monitoring.dashboard:stop:413 | 监控系统已停止
2025-07-31 09:40:01 | INFO     | qlib_trading_system.monitoring.metrics_collector:__init__:101 | 指标收集器初始化完成
2025-07-31 09:40:01 | INFO     | qlib_trading_system.monitoring.dashboard:__init__:155 | 实时监控仪表板初始化完成
2025-07-31 09:40:01 | INFO     | qlib_trading_system.monitoring.dashboard:__init__:374 | 仪表板管理器初始化完成
2025-07-31 09:40:01 | INFO     | qlib_trading_system.monitoring.metrics_collector:start_collection:112 | 指标收集器已启动
2025-07-31 09:40:01 | INFO     | qlib_trading_system.monitoring.dashboard:start_server:323 | 启动仪表板服务器: http://127.0.0.1:8080
2025-07-31 09:40:01 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:03 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:04 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:05 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:06 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 9.46%
2025-07-31 09:40:06 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:07 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 9.46%
2025-07-31 09:40:07 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:07 | INFO     | qlib_trading_system.monitoring.dashboard:connect:84 | WebSocket连接已建立，当前连接数: 1
2025-07-31 09:40:08 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 9.46%
2025-07-31 09:40:08 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] SYSTEM - CPU使用率过高: 95.9%
2025-07-31 09:40:08 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:09 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 10.69%
2025-07-31 09:40:09 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:10 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 22.37%
2025-07-31 09:40:10 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:11 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 22.76%
2025-07-31 09:40:11 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:13 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 22.76%
2025-07-31 09:40:13 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:14 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 23.37%
2025-07-31 09:40:14 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:15 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 26.40%
2025-07-31 09:40:15 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:16 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 26.40%
2025-07-31 09:40:16 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:17 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 26.40%
2025-07-31 09:40:17 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:18 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 26.40%
2025-07-31 09:40:18 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:19 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 26.40%
2025-07-31 09:40:19 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:20 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 26.40%
2025-07-31 09:40:20 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:21 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 26.40%
2025-07-31 09:40:21 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:22 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 26.40%
2025-07-31 09:40:22 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:24 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 26.40%
2025-07-31 09:40:24 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:25 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 26.40%
2025-07-31 09:40:25 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:26 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 26.40%
2025-07-31 09:40:26 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:27 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 26.40%
2025-07-31 09:40:27 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:28 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 29.64%
2025-07-31 09:40:28 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:29 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 29.64%
2025-07-31 09:40:29 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:30 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.18%
2025-07-31 09:40:30 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:31 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.18%
2025-07-31 09:40:31 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:32 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.18%
2025-07-31 09:40:32 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:34 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.18%
2025-07-31 09:40:34 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:35 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.18%
2025-07-31 09:40:35 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:36 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.18%
2025-07-31 09:40:36 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:37 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.18%
2025-07-31 09:40:37 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:38 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.18%
2025-07-31 09:40:38 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:39 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.18%
2025-07-31 09:40:39 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:40 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.18%
2025-07-31 09:40:40 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:41 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.18%
2025-07-31 09:40:41 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:42 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.18%
2025-07-31 09:40:42 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:44 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.18%
2025-07-31 09:40:44 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:45 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.18%
2025-07-31 09:40:45 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:46 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.18%
2025-07-31 09:40:46 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:47 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.18%
2025-07-31 09:40:47 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:48 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.18%
2025-07-31 09:40:48 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:49 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.18%
2025-07-31 09:40:49 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:50 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.18%
2025-07-31 09:40:50 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:51 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.18%
2025-07-31 09:40:51 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:51 | INFO     | qlib_trading_system.monitoring.dashboard:disconnect:90 | WebSocket连接已断开，当前连接数: 0
2025-07-31 09:40:52 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.18%
2025-07-31 09:40:52 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:53 | INFO     | qlib_trading_system.monitoring.dashboard:connect:84 | WebSocket连接已建立，当前连接数: 1
2025-07-31 09:40:54 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.18%
2025-07-31 09:40:54 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] SYSTEM - CPU使用率过高: 100.0%
2025-07-31 09:40:54 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:55 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.18%
2025-07-31 09:40:55 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:56 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.18%
2025-07-31 09:40:56 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:57 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.18%
2025-07-31 09:40:57 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:58 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.18%
2025-07-31 09:40:58 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:40:59 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.18%
2025-07-31 09:40:59 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:41:00 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.18%
2025-07-31 09:41:00 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:41:01 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.18%
2025-07-31 09:41:01 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:41:02 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.18%
2025-07-31 09:41:02 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:41:03 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.18%
2025-07-31 09:41:03 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:41:05 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 30.18%
2025-07-31 09:41:05 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:41:06 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 31.40%
2025-07-31 09:41:06 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:41:07 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 31.40%
2025-07-31 09:41:07 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:41:08 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_create_alert:398 | 报警事件: [WARNING] RISK - 最大回撤超过阈值: 31.40%
2025-07-31 09:41:08 | WARNING  | qlib_trading_system.monitoring.metrics_collector:_notify_subscribers:427 | 跳过异步回调，因为没有运行的事件循环
2025-07-31 09:41:09 | INFO     | qlib_trading_system.monitoring.metrics_collector:stop_collection:119 | 指标收集器已停止
2025-07-31 09:41:09 | INFO     | qlib_trading_system.monitoring.dashboard:stop_server:352 | 仪表板服务器已停止
2025-07-31 09:41:09 | INFO     | qlib_trading_system.monitoring.dashboard:stop:413 | 监控系统已停止
2025-07-31 10:47:56 | INFO     | qlib_trading_system.utils.logging.compliance_checker:add_rule:164 | 添加合规规则: 单日交易频率限制 (FREQ_001)
2025-07-31 10:47:56 | INFO     | qlib_trading_system.utils.logging.compliance_checker:add_rule:164 | 添加合规规则: 单笔交易金额限制 (AMOUNT_001)
2025-07-31 10:47:56 | INFO     | qlib_trading_system.utils.logging.compliance_checker:add_rule:164 | 添加合规规则: 交易时间限制 (TIME_001)
2025-07-31 10:47:56 | INFO     | qlib_trading_system.utils.logging.compliance_checker:add_rule:164 | 添加合规规则: 持仓集中度限制 (POS_001)
2025-07-31 10:47:56 | INFO     | qlib_trading_system.utils.logging.compliance_checker:add_rule:164 | 添加合规规则: 日内最大亏损限制 (RISK_001)
2025-07-31 10:47:56 | INFO     | qlib_trading_system.utils.logging.compliance_checker:add_rule:164 | 添加合规规则: 异常交易模式检测 (PATTERN_001)
2025-07-31 10:48:21 | INFO     | qlib_trading_system.utils.logging.compliance_checker:add_rule:164 | 添加合规规则: 单日交易频率限制 (FREQ_001)
2025-07-31 10:48:21 | INFO     | qlib_trading_system.utils.logging.compliance_checker:add_rule:164 | 添加合规规则: 单笔交易金额限制 (AMOUNT_001)
2025-07-31 10:48:21 | INFO     | qlib_trading_system.utils.logging.compliance_checker:add_rule:164 | 添加合规规则: 交易时间限制 (TIME_001)
2025-07-31 10:48:21 | INFO     | qlib_trading_system.utils.logging.compliance_checker:add_rule:164 | 添加合规规则: 持仓集中度限制 (POS_001)
2025-07-31 10:48:21 | INFO     | qlib_trading_system.utils.logging.compliance_checker:add_rule:164 | 添加合规规则: 日内最大亏损限制 (RISK_001)
2025-07-31 10:48:21 | INFO     | qlib_trading_system.utils.logging.compliance_checker:add_rule:164 | 添加合规规则: 异常交易模式检测 (PATTERN_001)
2025-07-31 10:48:59 | INFO     | qlib_trading_system.utils.logging.compliance_checker:add_rule:164 | 添加合规规则: 单日交易频率限制 (FREQ_001)
2025-07-31 10:48:59 | INFO     | qlib_trading_system.utils.logging.compliance_checker:add_rule:164 | 添加合规规则: 单笔交易金额限制 (AMOUNT_001)
2025-07-31 10:48:59 | INFO     | qlib_trading_system.utils.logging.compliance_checker:add_rule:164 | 添加合规规则: 交易时间限制 (TIME_001)
2025-07-31 10:48:59 | INFO     | qlib_trading_system.utils.logging.compliance_checker:add_rule:164 | 添加合规规则: 持仓集中度限制 (POS_001)
2025-07-31 10:48:59 | INFO     | qlib_trading_system.utils.logging.compliance_checker:add_rule:164 | 添加合规规则: 日内最大亏损限制 (RISK_001)
2025-07-31 10:48:59 | INFO     | qlib_trading_system.utils.logging.compliance_checker:add_rule:164 | 添加合规规则: 异常交易模式检测 (PATTERN_001)
2025-07-31 10:50:30 | INFO     | qlib_trading_system.utils.logging.compliance_checker:add_rule:164 | 添加合规规则: 单日交易频率限制 (FREQ_001)
2025-07-31 10:50:30 | INFO     | qlib_trading_system.utils.logging.compliance_checker:add_rule:164 | 添加合规规则: 单笔交易金额限制 (AMOUNT_001)
2025-07-31 10:50:30 | INFO     | qlib_trading_system.utils.logging.compliance_checker:add_rule:164 | 添加合规规则: 交易时间限制 (TIME_001)
2025-07-31 10:50:30 | INFO     | qlib_trading_system.utils.logging.compliance_checker:add_rule:164 | 添加合规规则: 持仓集中度限制 (POS_001)
2025-07-31 10:50:30 | INFO     | qlib_trading_system.utils.logging.compliance_checker:add_rule:164 | 添加合规规则: 日内最大亏损限制 (RISK_001)
2025-07-31 10:50:30 | INFO     | qlib_trading_system.utils.logging.compliance_checker:add_rule:164 | 添加合规规则: 异常交易模式检测 (PATTERN_001)
2025-07-31 10:50:30 | INFO     | qlib_trading_system.utils.logging.log_backup:_save_config:169 | 保存备份配置成功
2025-07-31 10:50:30 | INFO     | qlib_trading_system.utils.logging.log_backup:add_backup_config:235 | 添加备份配置: trading_daily
2025-07-31 10:50:30 | INFO     | qlib_trading_system.utils.logging.log_backup:_save_config:169 | 保存备份配置成功
2025-07-31 10:50:30 | INFO     | qlib_trading_system.utils.logging.log_backup:add_backup_config:235 | 添加备份配置: audit_daily
2025-07-31 10:50:30 | INFO     | qlib_trading_system.utils.logging.log_backup:_save_config:169 | 保存备份配置成功
2025-07-31 10:50:30 | INFO     | qlib_trading_system.utils.logging.log_backup:add_backup_config:235 | 添加备份配置: system_weekly
2025-07-31 10:50:30 | INFO     | qlib_trading_system.utils.logging.log_backup:_save_config:169 | 保存备份配置成功
2025-07-31 10:50:30 | INFO     | qlib_trading_system.utils.logging.log_backup:add_backup_config:235 | 添加备份配置: risk_monthly
2025-07-31 10:50:30 | ERROR    | qlib_trading_system.utils.logging.log_manager:_initialize_components:42 | 日志管理系统初始化失败: 'Job' object has no attribute 'month'
2025-07-31 10:50:30 | INFO     | qlib_trading_system.utils.logging.log_manager:start_monitoring:358 | 日志监控已启动
2025-07-31 10:51:18 | INFO     | qlib_trading_system.utils.logging.compliance_checker:add_rule:164 | 添加合规规则: 单日交易频率限制 (FREQ_001)
2025-07-31 10:51:18 | INFO     | qlib_trading_system.utils.logging.compliance_checker:add_rule:164 | 添加合规规则: 单笔交易金额限制 (AMOUNT_001)
2025-07-31 10:51:18 | INFO     | qlib_trading_system.utils.logging.compliance_checker:add_rule:164 | 添加合规规则: 交易时间限制 (TIME_001)
2025-07-31 10:51:18 | INFO     | qlib_trading_system.utils.logging.compliance_checker:add_rule:164 | 添加合规规则: 持仓集中度限制 (POS_001)
2025-07-31 10:51:18 | INFO     | qlib_trading_system.utils.logging.compliance_checker:add_rule:164 | 添加合规规则: 日内最大亏损限制 (RISK_001)
2025-07-31 10:51:18 | INFO     | qlib_trading_system.utils.logging.compliance_checker:add_rule:164 | 添加合规规则: 异常交易模式检测 (PATTERN_001)
2025-07-31 10:51:18 | INFO     | qlib_trading_system.utils.logging.log_backup:_load_config:155 | 加载备份配置: 4个配置
2025-07-31 10:51:18 | INFO     | qlib_trading_system.utils.logging.log_backup:_save_config:169 | 保存备份配置成功
2025-07-31 10:51:18 | INFO     | qlib_trading_system.utils.logging.log_backup:add_backup_config:235 | 添加备份配置: trading_daily
2025-07-31 10:51:18 | INFO     | qlib_trading_system.utils.logging.log_backup:_save_config:169 | 保存备份配置成功
2025-07-31 10:51:18 | INFO     | qlib_trading_system.utils.logging.log_backup:add_backup_config:235 | 添加备份配置: audit_daily
2025-07-31 10:51:18 | INFO     | qlib_trading_system.utils.logging.log_backup:_save_config:169 | 保存备份配置成功
2025-07-31 10:51:18 | INFO     | qlib_trading_system.utils.logging.log_backup:add_backup_config:235 | 添加备份配置: system_weekly
2025-07-31 10:51:18 | INFO     | qlib_trading_system.utils.logging.log_backup:_save_config:169 | 保存备份配置成功
2025-07-31 10:51:18 | INFO     | qlib_trading_system.utils.logging.log_backup:add_backup_config:235 | 添加备份配置: risk_monthly
2025-07-31 10:51:18 | INFO     | qlib_trading_system.utils.logging.log_backup:start_scheduler:477 | 日志备份调度器已启动
2025-07-31 10:51:18 | INFO     | qlib_trading_system.utils.logging.log_manager:_initialize_components:40 | 日志管理系统初始化完成
2025-07-31 10:51:18 | INFO     | qlib_trading_system.utils.logging.log_manager:start_monitoring:358 | 日志监控已启动
2025-07-31 10:51:18 | INFO     | qlib_trading_system.utils.logging.audit_logger:log_event:199 | {
  "event_id": "53f1ba6c-356f-4318-84d7-cd32cef9951c",
  "timestamp": "2025-07-31T02:51:18.920851+00:00",
  "event_type": "order_submit",
  "level": "INFO",
  "user_id": "user_001",
  "session_id": "session_001",
  "component": "TRADING",
  "action": "ORDER_SUBMIT",
  "details": {
    "symbol": "000001.SZ",
    "side": "BUY",
    "quantity": 1000,
    "price": 15.5,
    "amount": 15500,
    "total_assets": 100000,
    "order_id": "ORDER_20241231_001"
  },
  "before_state": null,
  "after_state": null,
  "ip_address": null,
  "result": "SUCCESS",
  "error_message": null
}
2025-07-31 10:51:18 | WARNING  | qlib_trading_system.utils.logging.audit_logger:log_event:201 | {
  "event_id": "1cd25199-ae17-43bc-8b0f-00479e782983",
  "timestamp": "2025-07-31T02:51:18.923921+00:00",
  "event_type": "risk_alert",
  "level": "WARNING",
  "user_id": null,
  "session_id": null,
  "component": "RISK",
  "action": "POSITION_LIMIT_WARNING",
  "details": {
    "symbol": "000001.SZ",
    "position_value": 80000,
    "total_assets": 100000,
    "position_ratio": 0.8,
    "limit_ratio": 0.8
  },
  "before_state": null,
  "after_state": null,
  "ip_address": null,
  "result": "DETECTED",
  "error_message": null
}
2025-07-31 10:51:18 | INFO     | qlib_trading_system.utils.logging.audit_logger:log_event:199 | {
  "event_id": "0e1785ac-4bac-4a65-8366-56eca9bffa67",
  "timestamp": "2025-07-31T02:51:18.926045+00:00",
  "event_type": "system_start",
  "level": "INFO",
  "user_id": null,
  "session_id": null,
  "component": "MAIN_SYSTEM",
  "action": "SYSTEM_START",
  "details": {
    "version": "1.0.0",
    "start_time": "2025-07-31T10:51:18.926045",
    "config_loaded": true
  },
  "before_state": null,
  "after_state": null,
  "ip_address": null,
  "result": "SUCCESS",
  "error_message": null
}
2025-07-31 10:51:18 | INFO     | qlib_trading_system.utils.logging.audit_logger:log_event:199 | {
  "event_id": "791aaf78-2cdc-4c6b-b3bd-f86baab6dd3b",
  "timestamp": "2025-07-31T02:51:18.929086+00:00",
  "event_type": "user_login",
  "level": "INFO",
  "user_id": "user_001",
  "session_id": "session_001",
  "component": "USER",
  "action": "LOGIN",
  "details": {
    "login_time": "2025-07-31T10:51:18.929086",
    "user_agent": "TradingClient/1.0",
    "login_method": "password"
  },
  "before_state": null,
  "after_state": null,
  "ip_address": "*************",
  "result": "SUCCESS",
  "error_message": null
}
2025-07-31 10:51:18 | INFO     | qlib_trading_system.utils.logging.audit_logger:log_event:199 | {
  "event_id": "ad2f03a0-9ea4-404b-ba4c-0b00071540d0",
  "timestamp": "2025-07-31T02:51:18.931643+00:00",
  "event_type": "order_submit",
  "level": "INFO",
  "user_id": "user_002",
  "session_id": "session_002",
  "component": "TRADING",
  "action": "ORDER_SUBMIT",
  "details": {
    "symbol": "000001.SZ",
    "side": "BUY",
    "quantity": 100,
    "price": 10.0,
    "amount": 1000,
    "total_assets": 100000,
    "order_id": "ORDER_FREQ_1"
  },
  "before_state": null,
  "after_state": null,
  "ip_address": null,
  "result": "SUCCESS",
  "error_message": null
}
2025-07-31 10:51:19 | INFO     | qlib_trading_system.utils.logging.audit_logger:log_event:199 | {
  "event_id": "1898c986-aa6d-4c02-bce1-3eb35d839ce0",
  "timestamp": "2025-07-31T02:51:19.034512+00:00",
  "event_type": "order_submit",
  "level": "INFO",
  "user_id": "user_002",
  "session_id": "session_002",
  "component": "TRADING",
  "action": "ORDER_SUBMIT",
  "details": {
    "symbol": "000002.SZ",
    "side": "BUY",
    "quantity": 100,
    "price": 10.0,
    "amount": 1000,
    "total_assets": 100000,
    "order_id": "ORDER_FREQ_2"
  },
  "before_state": null,
  "after_state": null,
  "ip_address": null,
  "result": "SUCCESS",
  "error_message": null
}
2025-07-31 10:51:19 | INFO     | qlib_trading_system.utils.logging.audit_logger:log_event:199 | {
  "event_id": "5d32a234-0866-49fc-8f6a-5093b6a21bf5",
  "timestamp": "2025-07-31T02:51:19.136705+00:00",
  "event_type": "order_submit",
  "level": "INFO",
  "user_id": "user_002",
  "session_id": "session_002",
  "component": "TRADING",
  "action": "ORDER_SUBMIT",
  "details": {
    "symbol": "000003.SZ",
    "side": "BUY",
    "quantity": 100,
    "price": 10.0,
    "amount": 1000,
    "total_assets": 100000,
    "order_id": "ORDER_FREQ_3"
  },
  "before_state": null,
  "after_state": null,
  "ip_address": null,
  "result": "SUCCESS",
  "error_message": null
}
2025-07-31 10:51:19 | INFO     | qlib_trading_system.utils.logging.audit_logger:log_event:199 | {
  "event_id": "c487d678-c09d-4f0e-83dc-62bec05ba06e",
  "timestamp": "2025-07-31T02:51:19.239148+00:00",
  "event_type": "order_submit",
  "level": "INFO",
  "user_id": "user_002",
  "session_id": "session_002",
  "component": "TRADING",
  "action": "ORDER_SUBMIT",
  "details": {
    "symbol": "000002.SZ",
    "side": "BUY",
    "quantity": 5000,
    "price": 12.0,
    "amount": 60000,
    "total_assets": 100000,
    "order_id": "ORDER_AMOUNT_001"
  },
  "before_state": null,
  "after_state": null,
  "ip_address": null,
  "result": "SUCCESS",
  "error_message": null
}
2025-07-31 10:51:19 | ERROR    | qlib_trading_system.utils.logging.audit_logger:log_event:203 | {
  "event_id": "d812fbd8-e36c-4f9c-a321-2f10ccf2047e",
  "timestamp": "2025-07-31T02:51:19.241595+00:00",
  "event_type": "system_start",
  "level": "ERROR",
  "user_id": null,
  "session_id": null,
  "component": "COMPLIANCE",
  "action": "COMPLIANCE_VIOLATION",
  "details": {
    "violation_id": "AMOUNT_VIOLATION_20250731_105119",
    "rule_id": "AMOUNT_001",
    "rule_name": "单笔交易金额限制",
    "violation_level": "HIGH",
    "description": "交易金额超限: 交易比例60.00%，超过限制50.00%",
    "details": {
      "trade_amount": 60000,
      "total_assets": 100000,
      "trade_ratio": 0.6,
      "max_allowed_ratio": 0.5
    }
  },
  "before_state": null,
  "after_state": null,
  "ip_address": null,
  "result": "SUCCESS",
  "error_message": null
}
2025-07-31 10:51:19 | ERROR    | qlib_trading_system.utils.logging.compliance_checker:_handle_violation:445 | 高级别合规违规: 交易金额超限: 交易比例60.00%，超过限制50.00%
2025-07-31 10:51:19 | WARNING  | qlib_trading_system.utils.logging.log_manager:log_trading_event:83 | 检测到合规违规: 1个
2025-07-31 10:51:19 | INFO     | qlib_trading_system.utils.logging.audit_logger:log_event:199 | {
  "event_id": "b6f718e0-d649-4b58-8a07-751316a063d2",
  "timestamp": "2025-07-31T02:51:19.247263+00:00",
  "event_type": "order_submit",
  "level": "INFO",
  "user_id": "user_002",
  "session_id": "session_002",
  "component": "TRADING",
  "action": "POSITION_CHANGE",
  "details": {
    "symbol": "000003.SZ",
    "position_value": 85000,
    "total_assets": 100000,
    "position_ratio": 0.85,
    "change_type": "INCREASE"
  },
  "before_state": null,
  "after_state": null,
  "ip_address": null,
  "result": "SUCCESS",
  "error_message": null
}
2025-07-31 10:51:19 | INFO     | qlib_trading_system.utils.logging.log_analyzer_simple:analyze_directory:72 | 分析日志文件: logs\test\test_system.log
2025-07-31 10:51:19 | INFO     | qlib_trading_system.utils.logging.log_manager:analyze_logs:212 | 日志分析完成: 异常1个, 违规1个
2025-07-31 10:51:19 | INFO     | qlib_trading_system.utils.logging.log_backup:_save_config:169 | 保存备份配置成功
2025-07-31 10:51:19 | INFO     | qlib_trading_system.utils.logging.log_backup:add_backup_config:235 | 添加备份配置: test_backup
2025-07-31 10:51:19 | INFO     | qlib_trading_system.utils.logging.log_backup:backup_logs:271 | 开始备份: test_backup
2025-07-31 10:51:19 | INFO     | qlib_trading_system.utils.logging.log_backup:backup_logs:312 | 备份完成: test_backup, 压缩比: 129.21%, 耗时: 0.01秒
2025-07-31 10:51:19 | INFO     | qlib_trading_system.utils.logging.log_manager:backup_logs:239 | 日志备份完成: 1个备份
2025-07-31 10:51:31 | INFO     | qlib_trading_system.utils.logging.log_backup:stop_scheduler:487 | 日志备份调度器已停止
2025-07-31 10:51:31 | INFO     | qlib_trading_system.utils.logging.log_manager:stop_monitoring:370 | 日志监控已停止
2025-07-31 16:59:01 | INFO     | __main__:start_single_service:214 | 启动单个服务: api
2025-07-31 16:59:01 | INFO     | __main__:start_single_service:215 | 主机: localhost, 端口: 8000
2025-07-31 16:59:01 | INFO     | __main__:start_single_service:245 | api 服务启动成功，监听 localhost:8000
2025-07-31 16:59:01 | INFO     | __main__:start_single_service:246 | 健康检查: http://localhost:8000/health
2025-07-31 16:59:01 | INFO     | __main__:start_single_service:247 | 按 Ctrl+C 停止服务
2025-07-31 17:00:27 | INFO     | __main__:log_message:242 | HTTP: "GET /health HTTP/1.1" 200 -
2025-07-31 17:00:35 | INFO     | __main__:<module>:285 | 收到中断信号，正在关闭服务...
