2025-07-28 12:42:03 | ERROR    | qlib_trading_system.utils.system_init:install_dependencies:57 | 依赖包安装超时
2025-07-28 12:42:03 | ERROR    | qlib_trading_system.utils.system_init:full_initialization:245 | 步骤失败: 安装依赖包
2025-07-31 09:18:10 | ERROR    | __main__:test_alert_manager:163 | ❌ 报警管理器测试失败: name 'AlertEvent' is not defined
2025-07-31 09:18:17 | ERROR    | qlib_trading_system.monitoring.dashboard:get_current_metrics:172 | 获取当前指标失败: Object of type datetime is not JSON serializable
2025-07-31 09:18:17 | ERROR    | __main__:test_dashboard_api:192 | ❌ 获取当前指标失败: 500
2025-07-31 09:18:19 | ERROR    | __main__:test_mobile_api:263 | ❌ 用户登录失败: 401
2025-07-31 09:18:24 | ERROR    | __main__:run_comprehensive_test:460 | ❌ 3 个测试失败
2025-07-31 09:19:44 | ERROR    | qlib_trading_system.monitoring.dashboard:get_metrics_history:186 | 获取历史指标失败: Object of type datetime is not JSON serializable
2025-07-31 09:19:44 | ERROR    | __main__:test_dashboard_api:203 | ❌ 获取历史指标失败: 500
2025-07-31 09:19:46 | ERROR    | __main__:test_mobile_api:264 | ❌ 用户登录失败: 401
2025-07-31 09:19:51 | ERROR    | __main__:run_comprehensive_test:461 | ❌ 2 个测试失败
2025-07-31 10:50:30 | ERROR    | qlib_trading_system.utils.logging.log_manager:_initialize_components:42 | 日志管理系统初始化失败: 'Job' object has no attribute 'month'
2025-07-31 10:51:19 | ERROR    | qlib_trading_system.utils.logging.audit_logger:log_event:203 | {
  "event_id": "d812fbd8-e36c-4f9c-a321-2f10ccf2047e",
  "timestamp": "2025-07-31T02:51:19.241595+00:00",
  "event_type": "system_start",
  "level": "ERROR",
  "user_id": null,
  "session_id": null,
  "component": "COMPLIANCE",
  "action": "COMPLIANCE_VIOLATION",
  "details": {
    "violation_id": "AMOUNT_VIOLATION_20250731_105119",
    "rule_id": "AMOUNT_001",
    "rule_name": "单笔交易金额限制",
    "violation_level": "HIGH",
    "description": "交易金额超限: 交易比例60.00%，超过限制50.00%",
    "details": {
      "trade_amount": 60000,
      "total_assets": 100000,
      "trade_ratio": 0.6,
      "max_allowed_ratio": 0.5
    }
  },
  "before_state": null,
  "after_state": null,
  "ip_address": null,
  "result": "SUCCESS",
  "error_message": null
}
2025-07-31 10:51:19 | ERROR    | qlib_trading_system.utils.logging.compliance_checker:_handle_violation:445 | 高级别合规违规: 交易金额超限: 交易比例60.00%，超过限制50.00%
