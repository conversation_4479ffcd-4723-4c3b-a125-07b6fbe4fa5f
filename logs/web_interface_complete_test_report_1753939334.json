{"test_time": "2025-07-31 13:22:14", "summary": {"auth_tests": {"total": 5, "passed": 2, "failed": 3, "errors": 0, "pass_rate": "40.0%"}, "config_tests": {"total": 6, "passed": 0, "failed": 6, "errors": 0, "pass_rate": "0.0%"}, "trading_tests": {"total": 5, "passed": 0, "failed": 5, "errors": 0, "pass_rate": "0.0%"}, "data_tests": {"total": 6, "passed": 0, "failed": 6, "errors": 0, "pass_rate": "0.0%"}, "monitoring_tests": {"total": 6, "passed": 0, "failed": 6, "errors": 0, "pass_rate": "0.0%"}, "overall": {"total": 28, "passed": 2, "failed": 26, "errors": 0, "pass_rate": "7.1%"}}, "details": {"auth_tests": [{"test": "管理员登录测试", "status": "FAIL", "message": "登录失败，状态码: 500"}, {"test": "交易员登录测试", "status": "FAIL", "message": "登录失败，状态码: 500"}, {"test": "分析师登录测试", "status": "FAIL", "message": "登录失败，状态码: 500"}, {"test": "错误密码登录测试", "status": "PASS", "message": "正确拒绝了错误的登录凭据"}, {"test": "用户登出测试", "status": "PASS", "message": "登出成功"}], "config_tests": [{"test": "获取所有配置", "status": "FAIL", "message": "请求失败，状态码: 401"}, {"test": "获取交易配置", "status": "FAIL", "message": "请求失败，状态码: 401"}, {"test": "获取数据源配置", "status": "FAIL", "message": "请求失败，状态码: 401"}, {"test": "获取模型配置", "status": "FAIL", "message": "请求失败，状态码: 401"}, {"test": "获取系统配置", "status": "FAIL", "message": "请求失败，状态码: 401"}, {"test": "交易配置更新测试", "status": "FAIL", "message": "配置更新失败，状态码: 401"}], "trading_tests": [{"test": "获取交易状态", "status": "FAIL", "message": "请求失败，状态码: 401"}, {"test": "获取持仓信息", "status": "FAIL", "message": "请求失败，状态码: 401"}, {"test": "获取订单列表", "status": "FAIL", "message": "请求失败，状态码: 401"}, {"test": "获取实时数据", "status": "FAIL", "message": "请求失败，状态码: 401"}, {"test": "下单功能测试", "status": "FAIL", "message": "下单失败，状态码: 401"}], "data_tests": [{"test": "股票搜索功能", "status": "FAIL", "message": "请求失败，状态码: 401"}, {"test": "获取股票信息", "status": "FAIL", "message": "请求失败，状态码: 401"}, {"test": "获取市场概览", "status": "FAIL", "message": "请求失败，状态码: 401"}, {"test": "获取热门股票", "status": "FAIL", "message": "请求失败，状态码: 401"}, {"test": "历史价格数据查询", "status": "FAIL", "message": "查询失败，状态码: 401"}, {"test": "技术指标数据查询", "status": "FAIL", "message": "查询失败，状态码: 401"}], "monitoring_tests": [{"test": "获取系统指标", "status": "FAIL", "message": "请求失败，状态码: 401"}, {"test": "获取交易指标", "status": "FAIL", "message": "请求失败，状态码: 401"}, {"test": "获取告警信息", "status": "FAIL", "message": "请求失败，状态码: 401"}, {"test": "获取系统日志", "status": "FAIL", "message": "请求失败，状态码: 401"}, {"test": "获取系统健康状态", "status": "FAIL", "message": "请求失败，状态码: 401"}, {"test": "添加告警功能", "status": "FAIL", "message": "告警添加失败，状态码: 401"}]}}