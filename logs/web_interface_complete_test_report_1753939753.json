{"test_time": "2025-07-31 13:29:13", "summary": {"auth_tests": {"total": 14, "passed": 13, "failed": 1, "errors": 0, "pass_rate": "92.9%"}, "config_tests": {"total": 6, "passed": 6, "failed": 0, "errors": 0, "pass_rate": "100.0%"}, "trading_tests": {"total": 5, "passed": 4, "failed": 1, "errors": 0, "pass_rate": "80.0%"}, "data_tests": {"total": 6, "passed": 0, "failed": 6, "errors": 0, "pass_rate": "0.0%"}, "monitoring_tests": {"total": 6, "passed": 5, "failed": 1, "errors": 0, "pass_rate": "83.3%"}, "overall": {"total": 37, "passed": 28, "failed": 9, "errors": 0, "pass_rate": "75.7%"}}, "details": {"auth_tests": [{"test": "管理员登录测试", "status": "PASS", "message": "登录成功，角色: administrator"}, {"test": "权限测试 - /api/config/all", "status": "FAIL", "message": "权限检查失败，状态码: 200"}, {"test": "权限测试 - /api/trading/status", "status": "PASS", "message": "角色 administrator 正确拥有权限"}, {"test": "权限测试 - /api/monitoring/system", "status": "PASS", "message": "角色 administrator 正确拥有权限"}, {"test": "交易员登录测试", "status": "PASS", "message": "登录成功，角色: trader"}, {"test": "权限测试 - /api/config/all", "status": "PASS", "message": "角色 trader 正确被拒绝访问"}, {"test": "权限测试 - /api/trading/status", "status": "PASS", "message": "角色 trader 正确拥有权限"}, {"test": "权限测试 - /api/monitoring/system", "status": "PASS", "message": "角色 trader 正确拥有权限"}, {"test": "分析师登录测试", "status": "PASS", "message": "登录成功，角色: analyst"}, {"test": "权限测试 - /api/config/all", "status": "PASS", "message": "角色 analyst 正确拥有权限"}, {"test": "权限测试 - /api/trading/status", "status": "PASS", "message": "角色 analyst 正确拥有权限"}, {"test": "权限测试 - /api/monitoring/system", "status": "PASS", "message": "角色 analyst 正确拥有权限"}, {"test": "错误密码登录测试", "status": "PASS", "message": "正确拒绝了错误的登录凭据"}, {"test": "用户登出测试", "status": "PASS", "message": "登出成功"}], "config_tests": [{"test": "获取所有配置", "status": "PASS", "message": "配置获取成功"}, {"test": "获取交易配置", "status": "PASS", "message": "配置获取成功"}, {"test": "获取数据源配置", "status": "PASS", "message": "配置获取成功"}, {"test": "获取模型配置", "status": "PASS", "message": "配置获取成功"}, {"test": "获取系统配置", "status": "PASS", "message": "配置获取成功"}, {"test": "交易配置更新测试", "status": "PASS", "message": "配置更新成功"}], "trading_tests": [{"test": "获取交易状态", "status": "PASS", "message": "数据获取成功"}, {"test": "获取持仓信息", "status": "PASS", "message": "数据获取成功"}, {"test": "获取订单列表", "status": "PASS", "message": "数据获取成功"}, {"test": "获取实时数据", "status": "PASS", "message": "数据获取成功"}, {"test": "下单功能测试", "status": "FAIL", "message": "下单失败，状态码: 422"}], "data_tests": [{"test": "股票搜索功能", "status": "FAIL", "message": "请求失败，状态码: 403"}, {"test": "获取股票信息", "status": "FAIL", "message": "请求失败，状态码: 403"}, {"test": "获取市场概览", "status": "FAIL", "message": "请求失败，状态码: 403"}, {"test": "获取热门股票", "status": "FAIL", "message": "请求失败，状态码: 403"}, {"test": "历史价格数据查询", "status": "FAIL", "message": "查询失败，状态码: 403"}, {"test": "技术指标数据查询", "status": "FAIL", "message": "查询失败，状态码: 403"}], "monitoring_tests": [{"test": "获取系统指标", "status": "PASS", "message": "监控数据获取成功"}, {"test": "获取交易指标", "status": "PASS", "message": "监控数据获取成功"}, {"test": "获取告警信息", "status": "PASS", "message": "监控数据获取成功"}, {"test": "获取系统日志", "status": "PASS", "message": "监控数据获取成功"}, {"test": "获取系统健康状态", "status": "PASS", "message": "监控数据获取成功"}, {"test": "添加告警功能", "status": "FAIL", "message": "告警添加失败，状态码: 403"}]}}