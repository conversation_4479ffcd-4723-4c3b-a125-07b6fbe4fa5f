2025-07-29 18:05:26,510 - qlib_trading_system.trading.execution.test_broker_integration - INFO - 启动券商接口适配层集成测试
2025-07-29 18:05:26,512 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 模拟券商 MockBroker1 初始化完成
2025-07-29 18:05:26,564 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 模拟券商 MockBroker1 连接成功
2025-07-29 18:05:26,564 - qlib_trading_system.trading.execution.broker_adapter - INFO - 券商 mock_broker_1 连接成功
2025-07-29 18:05:26,566 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 模拟券商 MockBroker2 初始化完成
2025-07-29 18:05:26,647 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 模拟券商 MockBroker2 连接成功
2025-07-29 18:05:26,647 - qlib_trading_system.trading.execution.broker_adapter - INFO - 券商 mock_broker_2 连接成功
2025-07-29 18:05:26,648 - qlib_trading_system.trading.execution.mock_broker.MockBroker3 - INFO - 模拟券商 MockBroker3 初始化完成
2025-07-29 18:05:26,750 - qlib_trading_system.trading.execution.mock_broker.MockBroker3 - INFO - 模拟券商 MockBroker3 连接成功
2025-07-29 18:05:26,750 - qlib_trading_system.trading.execution.broker_adapter - INFO - 券商 mock_broker_3 连接成功
2025-07-29 18:05:26,750 - qlib_trading_system.trading.execution.broker_adapter - INFO - 主要券商: mock_broker_1, 备用券商: ['mock_broker_2', 'mock_broker_3']
2025-07-29 18:05:26,751 - qlib_trading_system.trading.execution.broker_adapter - INFO - 健康检查监控线程已启动
2025-07-29 18:05:26,751 - qlib_trading_system.trading.execution.order_router - INFO - 负载监控线程已启动
2025-07-29 18:05:26,752 - qlib_trading_system.trading.execution.trading_monitor - INFO - 交易监控系统初始化完成
2025-07-29 18:05:26,752 - qlib_trading_system.trading.execution.test_broker_integration - INFO - 券商接口适配层集成测试初始化完成
2025-07-29 18:05:26,752 - qlib_trading_system.trading.execution.test_broker_integration - INFO - 开始运行券商接口适配层集成测试
2025-07-29 18:05:26,753 - qlib_trading_system.trading.execution.trading_monitor - INFO - 交易监控系统已启动
2025-07-29 18:05:26,753 - qlib_trading_system.trading.execution.test_broker_integration - INFO - 运行测试: 连接测试
2025-07-29 18:05:26,804 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 模拟券商 MockBroker1 连接成功
2025-07-29 18:05:26,804 - qlib_trading_system.trading.execution.broker_adapter - INFO - 券商 mock_broker_1 连接成功
2025-07-29 18:05:26,885 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 模拟券商 MockBroker2 连接成功
2025-07-29 18:05:26,885 - qlib_trading_system.trading.execution.broker_adapter - INFO - 券商 mock_broker_2 连接成功
2025-07-29 18:05:26,986 - qlib_trading_system.trading.execution.mock_broker.MockBroker3 - INFO - 模拟券商 MockBroker3 连接成功
2025-07-29 18:05:26,986 - qlib_trading_system.trading.execution.broker_adapter - INFO - 券商 mock_broker_3 连接成功
2025-07-29 18:05:26,987 - qlib_trading_system.trading.execution.test_broker_integration - INFO - 成功连接 3 个券商
2025-07-29 18:05:26,987 - qlib_trading_system.trading.execution.test_broker_integration - INFO - 市场开放状态: False
2025-07-29 18:05:27,009 - qlib_trading_system.trading.execution.test_broker_integration - INFO - 账户总资产: 1,000,000.00
2025-07-29 18:05:27,009 - qlib_trading_system.trading.execution.test_broker_integration - INFO - ✓ 连接测试 通过
2025-07-29 18:05:29,010 - qlib_trading_system.trading.execution.test_broker_integration - INFO - 运行测试: 基础交易测试
2025-07-29 18:05:29,031 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单提交成功: MOCK_301C9E79 000001.SZ BUY 100
2025-07-29 18:05:29,031 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 000001.SZ BUY 100 订单ID: MOCK_301C9E79
2025-07-29 18:05:29,032 - qlib_trading_system.trading.execution.test_broker_integration - INFO - 买入订单提交成功: MOCK_301C9E79
2025-07-29 18:05:29,332 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单成交: MOCK_301C9E79 价格: 50.05511744650494 手续费: 5.0
2025-07-29 18:05:29,916 - qlib_trading_system.trading.execution.trading_monitor - INFO - 新成交: TRADE_E3433E50 000001.SZ BUY 100@50.05511744650494
2025-07-29 18:05:29,917 - qlib_trading_system.trading.execution.trading_monitor - WARNING - [WARNING] 持仓集中度过高: 000001.SZ 持仓占比 100.00% 超过阈值 40.00%
2025-07-29 18:05:31,054 - qlib_trading_system.trading.execution.test_broker_integration - INFO - 订单状态: FILLED
2025-07-29 18:05:31,075 - qlib_trading_system.trading.execution.test_broker_integration - INFO - 持仓数量: 100
2025-07-29 18:05:31,095 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单提交成功: MOCK_A003E63C 000001.SZ SELL 50
2025-07-29 18:05:31,095 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 000001.SZ SELL 50 订单ID: MOCK_A003E63C
2025-07-29 18:05:31,096 - qlib_trading_system.trading.execution.test_broker_integration - INFO - 卖出订单提交成功: MOCK_A003E63C
2025-07-29 18:05:31,096 - qlib_trading_system.trading.execution.test_broker_integration - INFO - ✓ 基础交易测试 通过
2025-07-29 18:05:31,405 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单成交: MOCK_A003E63C 价格: 52.924105358949355 手续费: 5.0
2025-07-29 18:05:32,999 - qlib_trading_system.trading.execution.trading_monitor - INFO - 新成交: TRADE_7BA8C7DC 000001.SZ SELL 50@52.924105358949355
2025-07-29 18:05:32,999 - qlib_trading_system.trading.execution.trading_monitor - WARNING - [WARNING] 持仓集中度过高: 000001.SZ 持仓占比 100.00% 超过阈值 40.00%
2025-07-29 18:05:33,097 - qlib_trading_system.trading.execution.test_broker_integration - INFO - 运行测试: 订单路由测试
2025-07-29 18:05:33,118 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单提交成功: MOCK_D48D499E 600000.SH BUY 200
2025-07-29 18:05:33,119 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 600000.SH BUY 200 订单ID: MOCK_D48D499E
2025-07-29 18:05:33,119 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 600000.SH -> mock_broker_1
2025-07-29 18:05:33,119 - qlib_trading_system.trading.execution.test_broker_integration - INFO - 小订单路由结果: 1 个订单
2025-07-29 18:05:33,141 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单提交成功: MOCK_630AF2F6 000858.SZ BUY 5000
2025-07-29 18:05:33,141 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 000858.SZ BUY 5000 订单ID: MOCK_630AF2F6
2025-07-29 18:05:33,141 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 000858.SZ -> mock_broker_1
2025-07-29 18:05:33,142 - qlib_trading_system.trading.execution.test_broker_integration - INFO - 大订单路由结果: 1 个订单
2025-07-29 18:05:33,142 - qlib_trading_system.trading.execution.test_broker_integration - INFO - 订单分片数量: 0
2025-07-29 18:05:33,142 - qlib_trading_system.trading.execution.test_broker_integration - INFO - 路由统计: 总路由 2, 成功 2
2025-07-29 18:05:33,143 - qlib_trading_system.trading.execution.test_broker_integration - INFO - ✓ 订单路由测试 通过
2025-07-29 18:05:33,305 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单成交: MOCK_D48D499E 价格: 75.91692697189238 手续费: 5.0
2025-07-29 18:05:33,551 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单成交: MOCK_630AF2F6 价格: 19.00640477184885 手续费: 28.50960715777327
2025-07-29 18:05:35,144 - qlib_trading_system.trading.execution.test_broker_integration - INFO - 运行测试: 负载均衡测试
2025-07-29 18:05:35,165 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单提交成功: MOCK_D3077DA9 000001.SZ BUY 100
2025-07-29 18:05:35,166 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单提交成功: MOCK_0F28162E 000858.SZ BUY 300
2025-07-29 18:05:35,166 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单提交成功: MOCK_3C1752A0 600000.SH BUY 200
2025-07-29 18:05:35,166 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 000001.SZ BUY 100 订单ID: MOCK_D3077DA9
2025-07-29 18:05:35,166 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单提交成功: MOCK_C5E5FD46 300059.SZ BUY 500
2025-07-29 18:05:35,166 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单提交成功: MOCK_8305E647 002415.SZ BUY 400
2025-07-29 18:05:35,166 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 000858.SZ BUY 300 订单ID: MOCK_0F28162E
2025-07-29 18:05:35,168 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 600000.SH BUY 200 订单ID: MOCK_3C1752A0
2025-07-29 18:05:35,168 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 000001.SZ -> mock_broker_1
2025-07-29 18:05:35,168 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 300059.SZ BUY 500 订单ID: MOCK_C5E5FD46
2025-07-29 18:05:35,168 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 002415.SZ BUY 400 订单ID: MOCK_8305E647
2025-07-29 18:05:35,170 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 300059.SZ -> mock_broker_1
2025-07-29 18:05:35,170 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 002415.SZ -> mock_broker_1
2025-07-29 18:05:35,169 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 000858.SZ -> mock_broker_1
2025-07-29 18:05:35,169 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 600000.SH -> mock_broker_1
2025-07-29 18:05:35,172 - qlib_trading_system.trading.execution.test_broker_integration - INFO - 负载均衡测试: 5/5 成功
2025-07-29 18:05:35,173 - qlib_trading_system.trading.execution.test_broker_integration - INFO - 券商 mock_broker_1 负载: 待处理 0, 处理中 0, 响应时间 0.000s
2025-07-29 18:05:35,173 - qlib_trading_system.trading.execution.test_broker_integration - INFO - 券商 mock_broker_2 负载: 待处理 0, 处理中 0, 响应时间 0.000s
2025-07-29 18:05:35,174 - qlib_trading_system.trading.execution.test_broker_integration - INFO - 券商 mock_broker_3 负载: 待处理 0, 处理中 0, 响应时间 0.000s
2025-07-29 18:05:35,174 - qlib_trading_system.trading.execution.test_broker_integration - INFO - ✓ 负载均衡测试 通过
2025-07-29 18:05:35,380 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单成交: MOCK_3C1752A0 价格: 75.18981404538465 手续费: 5.0
2025-07-29 18:05:35,389 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单成交: MOCK_0F28162E 价格: 18.70661918419535 手续费: 5.0
2025-07-29 18:05:35,467 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单成交: MOCK_C5E5FD46 价格: 63.24715072931314 手续费: 9.487072609396972
2025-07-29 18:05:35,501 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单成交: MOCK_8305E647 价格: 15.090833352618569 手续费: 5.0
2025-07-29 18:05:35,543 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单成交: MOCK_D3077DA9 价格: 52.81382390540983 手续费: 5.0
2025-07-29 18:05:36,082 - qlib_trading_system.trading.execution.trading_monitor - INFO - 新成交: TRADE_EA74878A 000001.SZ BUY 100@52.81382390540983
2025-07-29 18:05:36,082 - qlib_trading_system.trading.execution.trading_monitor - INFO - 新成交: TRADE_791611A9 002415.SZ BUY 400@15.090833352618569
2025-07-29 18:05:36,082 - qlib_trading_system.trading.execution.trading_monitor - INFO - 新成交: TRADE_68E15B11 300059.SZ BUY 500@63.24715072931314
2025-07-29 18:05:36,082 - qlib_trading_system.trading.execution.trading_monitor - INFO - 新成交: TRADE_4537A12F 000858.SZ BUY 300@18.70661918419535
2025-07-29 18:05:36,083 - qlib_trading_system.trading.execution.trading_monitor - INFO - 新成交: TRADE_E68FDA4D 600000.SH BUY 200@75.18981404538465
2025-07-29 18:05:36,083 - qlib_trading_system.trading.execution.trading_monitor - INFO - 新成交: TRADE_4A23F9E7 000858.SZ BUY 5000@19.00640477184885
2025-07-29 18:05:36,083 - qlib_trading_system.trading.execution.trading_monitor - INFO - [INFO] 大额成交: 成交金额 95,032.02 元超过阈值
2025-07-29 18:05:36,083 - qlib_trading_system.trading.execution.trading_monitor - INFO - 新成交: TRADE_5BD11D5D 600000.SH BUY 200@75.91692697189238
2025-07-29 18:05:36,084 - qlib_trading_system.trading.execution.trading_monitor - WARNING - [WARNING] 持仓集中度过高: 000858.SZ 持仓占比 57.22% 超过阈值 40.00%
2025-07-29 18:05:37,175 - qlib_trading_system.trading.execution.test_broker_integration - INFO - 运行测试: 容错重试测试
2025-07-29 18:05:37,175 - qlib_trading_system.trading.execution.test_broker_integration - INFO - 重试操作成功，尝试次数: 1
2025-07-29 18:05:37,175 - qlib_trading_system.trading.execution.test_broker_integration - INFO - 熔断器状态: CLOSED
2025-07-29 18:05:37,176 - qlib_trading_system.trading.execution.test_broker_integration - INFO - 熔断器失败次数: 0
2025-07-29 18:05:37,176 - qlib_trading_system.trading.execution.test_broker_integration - INFO - 重试统计: {'total_operations': 1, 'successful_operations': 1, 'failed_operations': 0, 'total_retries': 0, 'circuit_breaker_trips': 0, 'success_rate': 1.0, 'avg_retries': 0.0, 'circuit_breakers': {'test_circuit': {'name': 'test_circuit', 'state': 'CLOSED', 'failure_count': 0, 'success_count': 0, 'total_requests': 1, 'total_failures': 0, 'failure_rate': 0.0, 'last_failure_time': None, 'state_change_time': '2025-07-29T18:05:37.175524', 'uptime_seconds': 0.001011}}}
2025-07-29 18:05:37,176 - qlib_trading_system.trading.execution.test_broker_integration - INFO - ✓ 容错重试测试 通过
2025-07-29 18:05:39,167 - qlib_trading_system.trading.execution.trading_monitor - WARNING - [WARNING] 持仓集中度过高: 000858.SZ 持仓占比 57.85% 超过阈值 40.00%
2025-07-29 18:05:39,177 - qlib_trading_system.trading.execution.test_broker_integration - INFO - 运行测试: 监控系统测试
2025-07-29 18:05:42,250 - qlib_trading_system.trading.execution.trading_monitor - WARNING - [WARNING] 持仓集中度过高: 000858.SZ 持仓占比 56.92% 超过阈值 40.00%
2025-07-29 18:05:44,179 - qlib_trading_system.trading.execution.test_broker_integration - INFO - 监控系统状态: True
2025-07-29 18:05:44,179 - qlib_trading_system.trading.execution.test_broker_integration - INFO - 最后同步时间: 2025-07-29T18:05:42.250125
2025-07-29 18:05:44,180 - qlib_trading_system.trading.execution.test_broker_integration - INFO - 当前指标:
2025-07-29 18:05:44,180 - qlib_trading_system.trading.execution.test_broker_integration - INFO -   总订单数: 9
2025-07-29 18:05:44,180 - qlib_trading_system.trading.execution.test_broker_integration - INFO -   成功订单数: 9
2025-07-29 18:05:44,181 - qlib_trading_system.trading.execution.test_broker_integration - INFO -   成功率: 100.00%
2025-07-29 18:05:44,181 - qlib_trading_system.trading.execution.test_broker_integration - INFO -   总成交数: 9
2025-07-29 18:05:44,181 - qlib_trading_system.trading.execution.test_broker_integration - INFO -   持仓数量: 5
2025-07-29 18:05:44,181 - qlib_trading_system.trading.execution.test_broker_integration - INFO - 告警数量: 6
2025-07-29 18:05:44,182 - qlib_trading_system.trading.execution.test_broker_integration - INFO - 告警: [WARNING] 持仓集中度过高
2025-07-29 18:05:44,182 - qlib_trading_system.trading.execution.test_broker_integration - INFO - 告警: [WARNING] 持仓集中度过高
2025-07-29 18:05:44,182 - qlib_trading_system.trading.execution.test_broker_integration - INFO - 告警: [WARNING] 持仓集中度过高
2025-07-29 18:05:44,183 - qlib_trading_system.trading.execution.test_broker_integration - INFO - 告警: [INFO] 大额成交
2025-07-29 18:05:44,183 - qlib_trading_system.trading.execution.test_broker_integration - INFO - 告警: [WARNING] 持仓集中度过高
2025-07-29 18:05:44,183 - qlib_trading_system.trading.execution.test_broker_integration - INFO - 缓存状态:
2025-07-29 18:05:44,184 - qlib_trading_system.trading.execution.test_broker_integration - INFO -   订单缓存: 9
2025-07-29 18:05:44,184 - qlib_trading_system.trading.execution.test_broker_integration - INFO -   成交缓存: 9
2025-07-29 18:05:44,184 - qlib_trading_system.trading.execution.test_broker_integration - INFO -   持仓缓存: 5
2025-07-29 18:05:44,184 - qlib_trading_system.trading.execution.test_broker_integration - INFO - ✓ 监控系统测试 通过
2025-07-29 18:05:45,332 - qlib_trading_system.trading.execution.trading_monitor - WARNING - [WARNING] 持仓集中度过高: 000858.SZ 持仓占比 57.90% 超过阈值 40.00%
2025-07-29 18:05:46,186 - qlib_trading_system.trading.execution.test_broker_integration - INFO - 运行测试: 性能压力测试
2025-07-29 18:05:46,186 - qlib_trading_system.trading.execution.test_broker_integration - INFO - 开始性能压力测试
2025-07-29 18:05:46,208 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单提交成功: MOCK_423D0153 000001.SZ BUY 100
2025-07-29 18:05:46,208 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单提交成功: MOCK_D421D3C3 000001.SZ BUY 100
2025-07-29 18:05:46,208 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单提交成功: MOCK_FEB6478A 000001.SZ BUY 100
2025-07-29 18:05:46,208 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单提交成功: MOCK_265E69D4 000001.SZ BUY 100
2025-07-29 18:05:46,208 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 000001.SZ BUY 100 订单ID: MOCK_423D0153
2025-07-29 18:05:46,208 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单提交成功: MOCK_ED043C3F 000001.SZ BUY 100
2025-07-29 18:05:46,208 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 000001.SZ BUY 100 订单ID: MOCK_D421D3C3
2025-07-29 18:05:46,210 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 000001.SZ BUY 100 订单ID: MOCK_FEB6478A
2025-07-29 18:05:46,211 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 000001.SZ BUY 100 订单ID: MOCK_265E69D4
2025-07-29 18:05:46,211 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 000001.SZ -> mock_broker_1
2025-07-29 18:05:46,211 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 000001.SZ BUY 100 订单ID: MOCK_ED043C3F
2025-07-29 18:05:46,211 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 000001.SZ -> mock_broker_1
2025-07-29 18:05:46,211 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 000001.SZ -> mock_broker_1
2025-07-29 18:05:46,212 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 000001.SZ -> mock_broker_1
2025-07-29 18:05:46,212 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 000001.SZ -> mock_broker_1
2025-07-29 18:05:46,249 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单提交成功: MOCK_C4A331A9 600000.SH BUY 100
2025-07-29 18:05:46,249 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单提交成功: MOCK_3E4FEF34 600000.SH BUY 100
2025-07-29 18:05:46,249 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单提交成功: MOCK_7FE84463 600000.SH BUY 100
2025-07-29 18:05:46,249 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 600000.SH BUY 100 订单ID: MOCK_C4A331A9
2025-07-29 18:05:46,249 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单提交成功: MOCK_6442961D 600000.SH BUY 100
2025-07-29 18:05:46,250 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单提交成功: MOCK_8D4270A5 600000.SH BUY 100
2025-07-29 18:05:46,250 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 600000.SH BUY 100 订单ID: MOCK_3E4FEF34
2025-07-29 18:05:46,251 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 600000.SH BUY 100 订单ID: MOCK_7FE84463
2025-07-29 18:05:46,252 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 600000.SH -> mock_broker_1
2025-07-29 18:05:46,252 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 600000.SH BUY 100 订单ID: MOCK_6442961D
2025-07-29 18:05:46,252 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 600000.SH BUY 100 订单ID: MOCK_8D4270A5
2025-07-29 18:05:46,253 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 600000.SH -> mock_broker_1
2025-07-29 18:05:46,253 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 600000.SH -> mock_broker_1
2025-07-29 18:05:46,254 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 600000.SH -> mock_broker_1
2025-07-29 18:05:46,254 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 600000.SH -> mock_broker_1
2025-07-29 18:05:46,274 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单提交成功: MOCK_C0F35EBD 000858.SZ BUY 100
2025-07-29 18:05:46,274 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 000858.SZ BUY 100 订单ID: MOCK_C0F35EBD
2025-07-29 18:05:46,276 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单提交成功: MOCK_6AEEE6DB 000858.SZ BUY 100
2025-07-29 18:05:46,276 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 000858.SZ -> mock_broker_1
2025-07-29 18:05:46,276 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单提交成功: MOCK_5F6C156F 000858.SZ BUY 100
2025-07-29 18:05:46,276 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单提交成功: MOCK_0339C5C4 000858.SZ BUY 100
2025-07-29 18:05:46,276 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 000858.SZ BUY 100 订单ID: MOCK_6AEEE6DB
2025-07-29 18:05:46,276 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单提交成功: MOCK_90BC97F5 000858.SZ BUY 100
2025-07-29 18:05:46,276 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 000858.SZ BUY 100 订单ID: MOCK_5F6C156F
2025-07-29 18:05:46,277 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 000858.SZ BUY 100 订单ID: MOCK_0339C5C4
2025-07-29 18:05:46,277 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 000858.SZ -> mock_broker_1
2025-07-29 18:05:46,277 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 000858.SZ BUY 100 订单ID: MOCK_90BC97F5
2025-07-29 18:05:46,277 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 000858.SZ -> mock_broker_1
2025-07-29 18:05:46,277 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 000858.SZ -> mock_broker_1
2025-07-29 18:05:46,279 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 000858.SZ -> mock_broker_1
2025-07-29 18:05:46,297 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单提交成功: MOCK_F87E4794 002415.SZ BUY 100
2025-07-29 18:05:46,299 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 002415.SZ BUY 100 订单ID: MOCK_F87E4794
2025-07-29 18:05:46,300 - qlib_trading_system.trading.execution.broker_adapter - WARNING - place_order 第 1 次尝试失败 (券商: mock_broker_1): 模拟订单提交失败
2025-07-29 18:05:46,300 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 002415.SZ -> mock_broker_1
2025-07-29 18:05:46,301 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单提交成功: MOCK_08A4D87F 002415.SZ BUY 100
2025-07-29 18:05:46,301 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单提交成功: MOCK_A9FAB2F7 002415.SZ BUY 100
2025-07-29 18:05:46,301 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单提交成功: MOCK_2E605F9D 002415.SZ BUY 100
2025-07-29 18:05:46,302 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 002415.SZ BUY 100 订单ID: MOCK_08A4D87F
2025-07-29 18:05:46,302 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 002415.SZ BUY 100 订单ID: MOCK_A9FAB2F7
2025-07-29 18:05:46,302 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 002415.SZ BUY 100 订单ID: MOCK_2E605F9D
2025-07-29 18:05:46,302 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 002415.SZ -> mock_broker_1
2025-07-29 18:05:46,302 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 002415.SZ -> mock_broker_1
2025-07-29 18:05:46,303 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 002415.SZ -> mock_broker_1
2025-07-29 18:05:46,333 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单提交成功: MOCK_59451790 300059.SZ BUY 100
2025-07-29 18:05:46,333 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 300059.SZ BUY 100 订单ID: MOCK_59451790
2025-07-29 18:05:46,335 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单提交成功: MOCK_67972785 300059.SZ BUY 100
2025-07-29 18:05:46,335 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 300059.SZ -> mock_broker_3
2025-07-29 18:05:46,335 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单提交成功: MOCK_83F27E05 300059.SZ BUY 100
2025-07-29 18:05:46,335 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 300059.SZ BUY 100 订单ID: MOCK_67972785
2025-07-29 18:05:46,335 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单提交成功: MOCK_411F894D 300059.SZ BUY 100
2025-07-29 18:05:46,336 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 300059.SZ BUY 100 订单ID: MOCK_83F27E05
2025-07-29 18:05:46,336 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 300059.SZ -> mock_broker_2
2025-07-29 18:05:46,336 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 300059.SZ BUY 100 订单ID: MOCK_411F894D
2025-07-29 18:05:46,336 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 300059.SZ -> mock_broker_2
2025-07-29 18:05:46,337 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 300059.SZ -> mock_broker_2
2025-07-29 18:05:46,367 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单提交成功: MOCK_69867C3D 000001.SZ BUY 100
2025-07-29 18:05:46,369 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 000001.SZ BUY 100 订单ID: MOCK_69867C3D
2025-07-29 18:05:46,369 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单提交成功: MOCK_C7D78143 000001.SZ BUY 100
2025-07-29 18:05:46,369 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单提交成功: MOCK_4B6A4C8C 000001.SZ BUY 100
2025-07-29 18:05:46,369 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单提交成功: MOCK_3D7FA6DA 000001.SZ BUY 100
2025-07-29 18:05:46,369 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 000001.SZ -> mock_broker_2
2025-07-29 18:05:46,370 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 000001.SZ BUY 100 订单ID: MOCK_C7D78143
2025-07-29 18:05:46,370 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 000001.SZ BUY 100 订单ID: MOCK_4B6A4C8C
2025-07-29 18:05:46,370 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 000001.SZ BUY 100 订单ID: MOCK_3D7FA6DA
2025-07-29 18:05:46,371 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 000001.SZ -> mock_broker_2
2025-07-29 18:05:46,371 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 000001.SZ -> mock_broker_2
2025-07-29 18:05:46,372 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 000001.SZ -> mock_broker_3
2025-07-29 18:05:46,373 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单成交: MOCK_FEB6478A 价格: 57.33296779632962 手续费: 5.0
2025-07-29 18:05:46,384 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单成交: MOCK_265E69D4 价格: 56.86425027677367 手续费: 5.0
2025-07-29 18:05:46,392 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单成交: MOCK_C0F35EBD 价格: 19.415459334417278 手续费: 5.0
2025-07-29 18:05:46,402 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单提交成功: MOCK_2F34A3A1 600000.SH BUY 100
2025-07-29 18:05:46,403 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单提交成功: MOCK_FE897007 600000.SH BUY 100
2025-07-29 18:05:46,403 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 600000.SH BUY 100 订单ID: MOCK_2F34A3A1
2025-07-29 18:05:46,403 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单提交成功: MOCK_BF9A8BF9 600000.SH BUY 100
2025-07-29 18:05:46,403 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单提交成功: MOCK_38122878 600000.SH BUY 100
2025-07-29 18:05:46,404 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 600000.SH BUY 100 订单ID: MOCK_FE897007
2025-07-29 18:05:46,404 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 600000.SH -> mock_broker_2
2025-07-29 18:05:46,404 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 600000.SH BUY 100 订单ID: MOCK_BF9A8BF9
2025-07-29 18:05:46,405 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 600000.SH BUY 100 订单ID: MOCK_38122878
2025-07-29 18:05:46,405 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 600000.SH -> mock_broker_3
2025-07-29 18:05:46,405 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 600000.SH -> mock_broker_3
2025-07-29 18:05:46,406 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 600000.SH -> mock_broker_3
2025-07-29 18:05:46,422 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单成交: MOCK_F87E4794 价格: 15.045405385968529 手续费: 5.0
2025-07-29 18:05:46,436 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单提交成功: MOCK_6BC1522E 000858.SZ BUY 100
2025-07-29 18:05:46,436 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 000858.SZ BUY 100 订单ID: MOCK_6BC1522E
2025-07-29 18:05:46,437 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 000858.SZ -> mock_broker_3
2025-07-29 18:05:46,437 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单提交成功: MOCK_06421DB0 000858.SZ BUY 100
2025-07-29 18:05:46,437 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单提交成功: MOCK_887FEC16 000858.SZ BUY 100
2025-07-29 18:05:46,437 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单提交成功: MOCK_41289A88 000858.SZ BUY 100
2025-07-29 18:05:46,438 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 000858.SZ BUY 100 订单ID: MOCK_06421DB0
2025-07-29 18:05:46,438 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 000858.SZ BUY 100 订单ID: MOCK_887FEC16
2025-07-29 18:05:46,438 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 000858.SZ BUY 100 订单ID: MOCK_41289A88
2025-07-29 18:05:46,438 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 000858.SZ -> mock_broker_2
2025-07-29 18:05:46,439 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 000858.SZ -> mock_broker_2
2025-07-29 18:05:46,439 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 000858.SZ -> mock_broker_3
2025-07-29 18:05:46,452 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单成交: MOCK_C4A331A9 价格: 73.22882962879261 手续费: 5.0
2025-07-29 18:05:46,453 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单成交: MOCK_A9FAB2F7 价格: 15.086207446817784 手续费: 5.0
2025-07-29 18:05:46,468 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单提交成功: MOCK_69C99239 002415.SZ BUY 100
2025-07-29 18:05:46,468 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 002415.SZ BUY 100 订单ID: MOCK_69C99239
2025-07-29 18:05:46,469 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 002415.SZ -> mock_broker_2
2025-07-29 18:05:46,470 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单提交成功: MOCK_868ADB16 002415.SZ BUY 100
2025-07-29 18:05:46,471 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 002415.SZ BUY 100 订单ID: MOCK_868ADB16
2025-07-29 18:05:46,471 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单提交成功: MOCK_5AC39281 002415.SZ BUY 100
2025-07-29 18:05:46,471 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单提交成功: MOCK_75C5462A 002415.SZ BUY 100
2025-07-29 18:05:46,471 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 002415.SZ -> mock_broker_2
2025-07-29 18:05:46,471 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 002415.SZ BUY 100 订单ID: MOCK_5AC39281
2025-07-29 18:05:46,472 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 002415.SZ BUY 100 订单ID: MOCK_75C5462A
2025-07-29 18:05:46,472 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 002415.SZ -> mock_broker_3
2025-07-29 18:05:46,472 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 002415.SZ -> mock_broker_2
2025-07-29 18:05:46,501 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单成交: MOCK_423D0153 价格: 57.823924207133246 手续费: 5.0
2025-07-29 18:05:46,501 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单提交成功: MOCK_41191AC5 300059.SZ BUY 100
2025-07-29 18:05:46,502 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 300059.SZ BUY 100 订单ID: MOCK_41191AC5
2025-07-29 18:05:46,502 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 300059.SZ -> mock_broker_2
2025-07-29 18:05:46,503 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单提交成功: MOCK_035E18C8 300059.SZ BUY 100
2025-07-29 18:05:46,503 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 300059.SZ BUY 100 订单ID: MOCK_035E18C8
2025-07-29 18:05:46,503 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单提交成功: MOCK_16676695 300059.SZ BUY 100
2025-07-29 18:05:46,503 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单提交成功: MOCK_DAEF5498 300059.SZ BUY 100
2025-07-29 18:05:46,503 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 300059.SZ -> mock_broker_2
2025-07-29 18:05:46,503 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 300059.SZ BUY 100 订单ID: MOCK_16676695
2025-07-29 18:05:46,504 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 300059.SZ BUY 100 订单ID: MOCK_DAEF5498
2025-07-29 18:05:46,504 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 300059.SZ -> mock_broker_2
2025-07-29 18:05:46,504 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 300059.SZ -> mock_broker_3
2025-07-29 18:05:46,522 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单成交: MOCK_6442961D 价格: 73.21718535445821 手续费: 5.0
2025-07-29 18:05:46,523 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单成交: MOCK_FE897007 价格: 51.101445954259944 手续费: 5.0
2025-07-29 18:05:46,524 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单成交: MOCK_2E605F9D 价格: 14.947890237961818 手续费: 5.0
2025-07-29 18:05:46,538 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单成交: MOCK_0339C5C4 价格: 19.21701505206288 手续费: 5.0
2025-07-29 18:05:46,550 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单成交: MOCK_59451790 价格: 62.62446337034104 手续费: 5.0
2025-07-29 18:05:46,567 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单成交: MOCK_8D4270A5 价格: 74.05657975898993 手续费: 5.0
2025-07-29 18:05:46,579 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单成交: MOCK_69C99239 价格: 51.58834024367307 手续费: 5.0
2025-07-29 18:05:46,599 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单成交: MOCK_ED043C3F 价格: 57.7904460860752 手续费: 5.0
2025-07-29 18:05:46,610 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单成交: MOCK_3E4FEF34 价格: 74.84618961247274 手续费: 5.0
2025-07-29 18:05:46,618 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单成交: MOCK_D421D3C3 价格: 56.90268580030754 手续费: 5.0
2025-07-29 18:05:46,629 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单成交: MOCK_08A4D87F 价格: 14.877559030370815 手续费: 5.0
2025-07-29 18:05:46,629 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单成交: MOCK_90BC97F5 价格: 19.278064465034237 手续费: 5.0
2025-07-29 18:05:46,632 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单成交: MOCK_06421DB0 价格: 78.01249911816889 手续费: 5.0
2025-07-29 18:05:46,657 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单成交: MOCK_16676695 价格: 63.65528648799181 手续费: 5.0
2025-07-29 18:05:46,665 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单成交: MOCK_5AC39281 价格: 52.38103808353379 手续费: 5.0
2025-07-29 18:05:46,685 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单成交: MOCK_83F27E05 价格: 63.309036544968116 手续费: 5.0
2025-07-29 18:05:46,686 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单成交: MOCK_5F6C156F 价格: 19.350588440703543 手续费: 5.0
2025-07-29 18:05:46,725 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单成交: MOCK_7FE84463 价格: 75.39254480135854 手续费: 5.0
2025-07-29 18:05:46,728 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单成交: MOCK_DAEF5498 价格: 63.46648633161164 手续费: 5.0
2025-07-29 18:05:46,739 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单成交: MOCK_2F34A3A1 价格: 50.204773396287464 手续费: 5.0
2025-07-29 18:05:46,739 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单成交: MOCK_67972785 价格: 62.78164693292362 手续费: 5.0
2025-07-29 18:05:46,753 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 订单成交: MOCK_6AEEE6DB 价格: 19.214194612264183 手续费: 5.0
2025-07-29 18:05:46,765 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单成交: MOCK_69867C3D 价格: 35.08052295432142 手续费: 5.0
2025-07-29 18:05:46,776 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单成交: MOCK_6BC1522E 价格: 76.81078116878781 手续费: 5.0
2025-07-29 18:05:46,784 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单成交: MOCK_C7D78143 价格: 35.66408795917183 手续费: 5.0
2025-07-29 18:05:46,797 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单成交: MOCK_4B6A4C8C 价格: 35.94011315985386 手续费: 5.0
2025-07-29 18:05:46,797 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单成交: MOCK_411F894D 价格: 62.28026582875397 手续费: 5.0
2025-07-29 18:05:46,812 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单成交: MOCK_868ADB16 价格: 52.5744784960756 手续费: 5.0
2025-07-29 18:05:46,829 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单成交: MOCK_BF9A8BF9 价格: 50.62131679517782 手续费: 5.0
2025-07-29 18:05:46,848 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单成交: MOCK_035E18C8 价格: 62.00641394715118 手续费: 5.0
2025-07-29 18:05:46,853 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单成交: MOCK_41289A88 价格: 75.62286708648251 手续费: 5.0
2025-07-29 18:05:46,854 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单成交: MOCK_38122878 价格: 51.02533528979965 手续费: 5.0
2025-07-29 18:05:46,860 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单成交: MOCK_3D7FA6DA 价格: 36.02842475910455 手续费: 5.0
2025-07-29 18:05:46,870 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单成交: MOCK_887FEC16 价格: 74.19104703651622 手续费: 5.0
2025-07-29 18:05:46,873 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单成交: MOCK_41191AC5 价格: 61.81862692340887 手续费: 5.0
2025-07-29 18:05:46,893 - qlib_trading_system.trading.execution.broker_adapter - INFO - 券商 mock_broker_1 状态变更为: 健康
2025-07-29 18:05:46,935 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单成交: MOCK_75C5462A 价格: 53.4148785675648 手续费: 5.0
2025-07-29 18:05:47,334 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单提交成功: MOCK_254C2BF7 002415.SZ BUY 100
2025-07-29 18:05:47,334 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 002415.SZ BUY 100 订单ID: MOCK_254C2BF7
2025-07-29 18:05:47,334 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 002415.SZ -> mock_broker_1
2025-07-29 18:05:47,356 - qlib_trading_system.trading.execution.broker_adapter - WARNING - place_order 第 1 次尝试失败 (券商: mock_broker_1): 模拟订单提交失败
2025-07-29 18:05:47,620 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单成交: MOCK_254C2BF7 价格: 53.58187902271825 手续费: 5.0
2025-07-29 18:05:48,387 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单提交成功: MOCK_58DA1521 300059.SZ BUY 100
2025-07-29 18:05:48,387 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 300059.SZ BUY 100 订单ID: MOCK_58DA1521
2025-07-29 18:05:48,387 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 300059.SZ -> mock_broker_1
2025-07-29 18:05:48,415 - qlib_trading_system.trading.execution.trading_monitor - INFO - 新成交: TRADE_190B0F7B 002415.SZ BUY 100@53.58187902271825
2025-07-29 18:05:48,415 - qlib_trading_system.trading.execution.trading_monitor - INFO - 新成交: TRADE_0BEC89FD 002415.SZ BUY 100@53.4148785675648
2025-07-29 18:05:48,415 - qlib_trading_system.trading.execution.trading_monitor - INFO - 新成交: TRADE_C7A57A17 300059.SZ BUY 100@61.81862692340887
2025-07-29 18:05:48,416 - qlib_trading_system.trading.execution.trading_monitor - INFO - 新成交: TRADE_2A71D861 000858.SZ BUY 100@74.19104703651622
2025-07-29 18:05:48,416 - qlib_trading_system.trading.execution.trading_monitor - INFO - 新成交: TRADE_2CCDF9FD 000001.SZ BUY 100@36.02842475910455
2025-07-29 18:05:48,416 - qlib_trading_system.trading.execution.trading_monitor - INFO - 新成交: TRADE_7EAB7FF0 600000.SH BUY 100@51.02533528979965
2025-07-29 18:05:48,417 - qlib_trading_system.trading.execution.trading_monitor - INFO - 新成交: TRADE_9C6C8B46 000858.SZ BUY 100@75.62286708648251
2025-07-29 18:05:48,418 - qlib_trading_system.trading.execution.trading_monitor - INFO - 新成交: TRADE_E628BC85 300059.SZ BUY 100@62.00641394715118
2025-07-29 18:05:48,418 - qlib_trading_system.trading.execution.trading_monitor - INFO - 新成交: TRADE_A60BB157 600000.SH BUY 100@50.62131679517782
2025-07-29 18:05:48,418 - qlib_trading_system.trading.execution.trading_monitor - INFO - 新成交: TRADE_F1CF4DF0 002415.SZ BUY 100@52.5744784960756
2025-07-29 18:05:48,419 - qlib_trading_system.trading.execution.trading_monitor - INFO - 新成交: TRADE_46153328 000001.SZ BUY 100@35.94011315985386
2025-07-29 18:05:48,420 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单提交成功: MOCK_09E3E9FB 000001.SZ BUY 100
2025-07-29 18:05:48,420 - qlib_trading_system.trading.execution.trading_monitor - INFO - 新成交: TRADE_6D1771EE 300059.SZ BUY 100@62.28026582875397
2025-07-29 18:05:48,420 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 000001.SZ BUY 100 订单ID: MOCK_09E3E9FB
2025-07-29 18:05:48,420 - qlib_trading_system.trading.execution.trading_monitor - INFO - 新成交: TRADE_B292BE0E 000001.SZ BUY 100@35.66408795917183
2025-07-29 18:05:48,421 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 000001.SZ -> mock_broker_3
2025-07-29 18:05:48,421 - qlib_trading_system.trading.execution.trading_monitor - INFO - 新成交: TRADE_8CC69602 000858.SZ BUY 100@76.81078116878781
2025-07-29 18:05:48,421 - qlib_trading_system.trading.execution.trading_monitor - INFO - 新成交: TRADE_D6646685 000001.SZ BUY 100@35.08052295432142
2025-07-29 18:05:48,421 - qlib_trading_system.trading.execution.trading_monitor - INFO - 新成交: TRADE_E9E51A0E 600000.SH BUY 100@50.204773396287464
2025-07-29 18:05:48,421 - qlib_trading_system.trading.execution.trading_monitor - INFO - 新成交: TRADE_6761E3CD 300059.SZ BUY 100@62.78164693292362
2025-07-29 18:05:48,422 - qlib_trading_system.trading.execution.trading_monitor - INFO - 新成交: TRADE_7F119AA0 300059.SZ BUY 100@63.46648633161164
2025-07-29 18:05:48,422 - qlib_trading_system.trading.execution.trading_monitor - INFO - 新成交: TRADE_F413010F 300059.SZ BUY 100@63.309036544968116
2025-07-29 18:05:48,422 - qlib_trading_system.trading.execution.trading_monitor - INFO - 新成交: TRADE_532567A3 002415.SZ BUY 100@52.38103808353379
2025-07-29 18:05:48,423 - qlib_trading_system.trading.execution.trading_monitor - INFO - 新成交: TRADE_E6BF7548 300059.SZ BUY 100@63.65528648799181
2025-07-29 18:05:48,424 - qlib_trading_system.trading.execution.trading_monitor - INFO - 新成交: TRADE_18E03D45 000858.SZ BUY 100@78.01249911816889
2025-07-29 18:05:48,424 - qlib_trading_system.trading.execution.trading_monitor - INFO - 新成交: TRADE_74CDC0A2 002415.SZ BUY 100@51.58834024367307
2025-07-29 18:05:48,424 - qlib_trading_system.trading.execution.trading_monitor - INFO - 新成交: TRADE_B1967C08 300059.SZ BUY 100@62.62446337034104
2025-07-29 18:05:48,425 - qlib_trading_system.trading.execution.trading_monitor - INFO - 新成交: TRADE_6F036049 600000.SH BUY 100@51.101445954259944
2025-07-29 18:05:48,452 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单提交成功: MOCK_4BF99045 600000.SH BUY 100
2025-07-29 18:05:48,453 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 600000.SH BUY 100 订单ID: MOCK_4BF99045
2025-07-29 18:05:48,453 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 600000.SH -> mock_broker_3
2025-07-29 18:05:48,484 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单提交成功: MOCK_DAF394D1 000858.SZ BUY 100
2025-07-29 18:05:48,486 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 000858.SZ BUY 100 订单ID: MOCK_DAF394D1
2025-07-29 18:05:48,486 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 000858.SZ -> mock_broker_2
2025-07-29 18:05:48,517 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单提交成功: MOCK_70F4643A 002415.SZ BUY 100
2025-07-29 18:05:48,518 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 002415.SZ BUY 100 订单ID: MOCK_70F4643A
2025-07-29 18:05:48,518 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 002415.SZ -> mock_broker_3
2025-07-29 18:05:48,544 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单成交: MOCK_09E3E9FB 价格: 35.91381345351873 手续费: 5.0
2025-07-29 18:05:48,550 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单提交成功: MOCK_62872508 300059.SZ BUY 100
2025-07-29 18:05:48,551 - qlib_trading_system.trading.execution.broker_adapter - INFO - 订单提交成功: 300059.SZ BUY 100 订单ID: MOCK_62872508
2025-07-29 18:05:48,551 - qlib_trading_system.trading.execution.order_router - INFO - 订单路由成功: 300059.SZ -> mock_broker_2
2025-07-29 18:05:48,552 - qlib_trading_system.trading.execution.test_broker_integration - INFO - 性能测试完成:
2025-07-29 18:05:48,552 - qlib_trading_system.trading.execution.test_broker_integration - INFO -   总耗时: 2.37秒
2025-07-29 18:05:48,552 - qlib_trading_system.trading.execution.test_broker_integration - INFO -   成功订单: 50/50
2025-07-29 18:05:48,554 - qlib_trading_system.trading.execution.test_broker_integration - INFO -   平均TPS: 21.13
2025-07-29 18:05:48,554 - qlib_trading_system.trading.execution.test_broker_integration - INFO - 券商统计: {'total_orders': 59, 'successful_orders': 59, 'failed_orders': 0, 'retry_count': 2, 'broker_switches': 0}
2025-07-29 18:05:48,554 - qlib_trading_system.trading.execution.test_broker_integration - INFO - 路由统计: 平均路由时间 0.066s
2025-07-29 18:05:48,555 - qlib_trading_system.trading.execution.test_broker_integration - INFO - ✓ 性能压力测试 通过
2025-07-29 18:05:48,751 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单成交: MOCK_58DA1521 价格: 60.923740354836546 手续费: 5.0
2025-07-29 18:05:48,829 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单成交: MOCK_DAF394D1 价格: 71.73592710943555 手续费: 5.0
2025-07-29 18:05:48,855 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单成交: MOCK_70F4643A 价格: 51.988467813748 手续费: 5.0
2025-07-29 18:05:48,924 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单成交: MOCK_4BF99045 价格: 49.89264192327998 手续费: 5.0
2025-07-29 18:05:48,940 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 订单成交: MOCK_62872508 价格: 60.651683895663915 手续费: 5.0
2025-07-29 18:05:50,557 - qlib_trading_system.trading.execution.test_broker_integration - INFO - 测试报告已生成: broker_integration_test_report.json
2025-07-29 18:05:50,558 - qlib_trading_system.trading.execution.test_broker_integration - INFO - 测试完成: 7/7 通过 (100.0%)
2025-07-29 18:05:56,431 - qlib_trading_system.trading.execution.trading_monitor - INFO - 交易监控系统已停止
2025-07-29 18:05:56,431 - qlib_trading_system.trading.execution.mock_broker.MockBroker1 - INFO - 模拟券商 MockBroker1 断开连接
2025-07-29 18:05:56,431 - qlib_trading_system.trading.execution.broker_adapter - INFO - 券商 mock_broker_1 断开连接成功
2025-07-29 18:05:56,431 - qlib_trading_system.trading.execution.mock_broker.MockBroker2 - INFO - 模拟券商 MockBroker2 断开连接
2025-07-29 18:05:56,431 - qlib_trading_system.trading.execution.broker_adapter - INFO - 券商 mock_broker_2 断开连接成功
2025-07-29 18:05:56,432 - qlib_trading_system.trading.execution.mock_broker.MockBroker3 - INFO - 模拟券商 MockBroker3 断开连接
2025-07-29 18:05:56,432 - qlib_trading_system.trading.execution.broker_adapter - INFO - 券商 mock_broker_3 断开连接成功
2025-07-29 18:05:56,432 - qlib_trading_system.trading.execution.test_broker_integration - INFO - 🎉 券商接口适配层集成测试全部通过！
