#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资金管理系统完整集成测试 - Complete Capital Management System Integration Test

测试所有组件的完整功能和协同工作

Author: Qlib Trading System
Date: 2025-01-30
"""

import sys
import os
import json
import logging
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from qlib_trading_system.trading.capital_manager_simple import (
    CapitalManager, CapitalConfig, CapitalMode, PositionType, TransactionType
)
from qlib_trading_system.trading.small_capital_config import (
    SmallCapitalConfigManager, SmallCapitalConfig, RiskLevel
)
from qlib_trading_system.trading.single_stock_risk_controller import (
    SingleStockRiskController, SingleStockRiskConfig, RiskEvent
)
from qlib_trading_system.trading.fund_flow_analyzer import (
    FundFlowAnalyzer, FlowType
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('capital_management_complete_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class CompleteCapitalManagementTest:
    """完整资金管理系统集成测试类"""
    
    def __init__(self):
        """初始化测试环境"""
        self.test_account_id = "COMPLETE_TEST_001"
        self.test_capital = 200000.0  # 20万测试资金
        self.test_symbol = "000001.SZ"
        
        # 初始化各个组件
        self.config_manager = None
        self.capital_manager = None
        self.risk_controller = None
        self.flow_analyzer = None
        
        logger.info("=== 完整资金管理系统集成测试开始 ===")
    
    def test_1_small_capital_config_management(self) -> bool:
        """测试1: 小资金专用配置管理"""
        try:
            logger.info("测试1: 小资金专用配置管理")
            
            # 创建配置管理器
            self.config_manager = SmallCapitalConfigManager("test_config/complete_test")
            
            # 创建小资金配置
            config = self.config_manager.create_config(
                account_id=self.test_account_id,
                total_capital=self.test_capital,
                risk_level=RiskLevel.AGGRESSIVE
            )
            
            logger.info(f"✓ 小资金配置创建成功")
            logger.info(f"  - 账户ID: {config.account_id}")
            logger.info(f"  - 总资金: {config.total_capital:,.2f}")
            logger.info(f"  - 风险等级: {config.risk_level.value}")
            logger.info(f"  - 底仓比例: {config.base_position_ratio:.1%}")
            logger.info(f"  - T仓比例: {config.t_position_ratio:.1%}")
            logger.info(f"  - 现金储备: {config.cash_reserve_ratio:.1%}")
            
            # 测试配置更新
            update_success = self.config_manager.update_config(
                self.test_account_id,
                max_daily_loss_ratio=0.025,
                max_t_times_per_day=6
            )
            
            if not update_success:
                logger.error("✗ 配置更新失败")
                return False
            
            logger.info("✓ 配置更新成功")
            
            # 测试配置列表
            config_list = self.config_manager.list_configs()
            logger.info(f"✓ 配置列表获取成功，共{len(config_list)}个配置")
            
            return True
            
        except Exception as e:
            logger.error(f"✗ 小资金配置管理测试失败: {e}")
            return False
    
    def test_2_capital_manager_operations(self) -> bool:
        """测试2: 资金管理器操作"""
        try:
            logger.info("测试2: 资金管理器操作")
            
            # 获取小资金配置
            small_config = self.config_manager.get_config(self.test_account_id)
            if not small_config:
                logger.error("✗ 无法获取小资金配置")
                return False
            
            # 创建资金管理器配置
            capital_config = CapitalConfig(
                total_capital=small_config.total_capital,
                mode=CapitalMode.SMALL,
                max_stocks=1,
                base_position_ratio=small_config.base_position_ratio,
                t_position_ratio=small_config.t_position_ratio,
                cash_reserve_ratio=small_config.cash_reserve_ratio,
                max_single_stock_ratio=1.0,
                max_daily_loss_ratio=small_config.max_daily_loss_ratio,
                max_drawdown_ratio=small_config.max_drawdown_ratio,
                leverage_ratio=1.0
            )
            
            # 创建资金管理器
            self.capital_manager = CapitalManager(capital_config)
            logger.info("✓ 资金管理器创建成功")
            
            # 测试资金分配
            allocation = self.capital_manager.get_capital_allocation()
            logger.info(f"✓ 资金分配:")
            logger.info(f"  - 总资金: {allocation['total_capital']:,.2f}")
            logger.info(f"  - 底仓分配: {allocation['base_allocation']:,.2f}")
            logger.info(f"  - T仓分配: {allocation['t_allocation']:,.2f}")
            logger.info(f"  - 现金分配: {allocation['cash_allocation']:,.2f}")
            
            # 测试买入底仓
            buy_price = 12.50
            buy_quantity = int(allocation['base_allocation'] / buy_price / 100) * 100
            
            success = self.capital_manager.execute_buy(
                self.test_symbol, buy_quantity, buy_price, PositionType.BASE, "建立底仓"
            )
            
            if not success:
                logger.error("✗ 底仓买入失败")
                return False
            
            logger.info(f"✓ 底仓买入成功: {buy_quantity}股 @{buy_price:.2f}")
            
            # 测试T+0操作序列
            t_operations = [
                ("buy", 1000, 12.45, "T+0买入1"),
                ("sell", 1000, 12.55, "T+0卖出1"),
                ("buy", 1500, 12.40, "T+0买入2"),
                ("sell", 1500, 12.60, "T+0卖出2"),
                ("buy", 800, 12.35, "T+0买入3")
            ]
            
            for operation, quantity, price, notes in t_operations:
                if operation == "buy":
                    success = self.capital_manager.execute_buy(
                        self.test_symbol, quantity, price, PositionType.T_POSITION, notes
                    )
                else:
                    success = self.capital_manager.execute_sell(
                        self.test_symbol, quantity, price, PositionType.T_POSITION, notes
                    )
                
                if success:
                    logger.info(f"✓ {notes}: {quantity}股 @{price:.2f}")
                else:
                    logger.warning(f"⚠ {notes}失败")
            
            # 更新价格并获取投资组合摘要
            current_price = 12.80
            self.capital_manager.update_positions_price({self.test_symbol: current_price})
            
            portfolio = self.capital_manager.get_portfolio_summary()
            logger.info(f"✓ 投资组合摘要:")
            logger.info(f"  - 投资组合价值: {portfolio['portfolio_value']:,.2f}")
            logger.info(f"  - 总收益率: {portfolio['total_return_pct']:.2f}%")
            logger.info(f"  - 持仓数量: {portfolio['position_count']}")
            
            return True
            
        except Exception as e:
            logger.error(f"✗ 资金管理器操作测试失败: {e}")
            return False
    
    def test_3_risk_controller_monitoring(self) -> bool:
        """测试3: 风险控制器监控"""
        try:
            logger.info("测试3: 风险控制器监控")
            
            # 创建风险控制配置
            risk_config = SingleStockRiskConfig(
                max_position_loss_pct=0.05,
                max_daily_loss_pct=0.03,
                max_drawdown_pct=0.15,
                stop_loss_pct=0.08,
                trailing_stop_pct=0.05
            )
            
            # 创建风险控制器
            self.risk_controller = SingleStockRiskController(risk_config)
            logger.info("✓ 风险控制器创建成功")
            
            # 获取当前持仓信息
            if self.test_symbol in self.capital_manager.positions:
                position = self.capital_manager.positions[self.test_symbol]
                
                # 模拟价格变动序列
                price_scenarios = [
                    (12.80, "当前价格"),
                    (13.20, "价格上涨"),
                    (13.50, "继续上涨"),
                    (12.90, "价格回调"),
                    (12.20, "价格下跌"),
                    (11.80, "继续下跌"),
                    (11.50, "深度下跌")
                ]
                
                for price, scenario in price_scenarios:
                    # 更新资金管理器价格
                    self.capital_manager.update_positions_price({self.test_symbol: price})
                    
                    # 更新风险控制器
                    updated_position = self.capital_manager.positions[self.test_symbol]
                    self.risk_controller.update_position(
                        symbol=self.test_symbol,
                        current_price=price,
                        position_value=updated_position.market_value,
                        cost_basis=updated_position.avg_cost,
                        daily_volume=100000000
                    )
                    
                    # 获取风险状态
                    risk_status = self.risk_controller.get_current_risk_status()
                    
                    logger.info(f"✓ {scenario} {price:.2f}:")
                    logger.info(f"  - 风险等级: {risk_status['overall_risk_level']}")
                    logger.info(f"  - 活跃警报: {risk_status['active_alerts_count']}")
                    logger.info(f"  - 当前回撤: {risk_status['current_drawdown']:.2%}")
                    logger.info(f"  - 持仓盈亏: {risk_status['position_pnl']:.2%}")
                
                # 获取风险建议
                suggestions = self.risk_controller.get_risk_suggestions()
                logger.info(f"✓ 风险管理建议 ({len(suggestions)}条):")
                for i, suggestion in enumerate(suggestions[:5], 1):
                    logger.info(f"  {i}. {suggestion}")
                
                return True
            else:
                logger.error("✗ 未找到持仓信息")
                return False
            
        except Exception as e:
            logger.error(f"✗ 风险控制器监控测试失败: {e}")
            return False
    
    def test_4_fund_flow_analysis(self) -> bool:
        """测试4: 资金流水分析"""
        try:
            logger.info("测试4: 资金流水分析")
            
            # 创建资金流水分析器
            self.flow_analyzer = FundFlowAnalyzer("test_data/complete_test")
            logger.info("✓ 资金流水分析器创建成功")
            
            # 基于资金管理器的交易记录添加流水记录
            for transaction in self.capital_manager.transactions:
                if transaction.transaction_type in [TransactionType.BUY_BASE, TransactionType.BUY_T]:
                    flow_type = FlowType.OUTFLOW
                    balance_before = transaction.balance_after + transaction.net_amount
                    balance_after = transaction.balance_after
                else:
                    flow_type = FlowType.INFLOW
                    balance_before = transaction.balance_after - transaction.net_amount
                    balance_after = transaction.balance_after
                
                self.flow_analyzer.add_flow_record(
                    symbol=transaction.symbol,
                    flow_type=flow_type,
                    amount=transaction.amount,
                    balance_before=balance_before,
                    balance_after=balance_after,
                    description=f"{transaction.transaction_type.value}: {transaction.notes}",
                    related_transaction_id=transaction.transaction_id
                )
            
            logger.info(f"✓ 添加了{len(self.capital_manager.transactions)}条流水记录")
            
            # 生成流水报告
            flow_report = self.flow_analyzer.generate_flow_report()
            logger.info(f"✓ 流水报告生成成功:")
            logger.info(f"  - 总记录数: {flow_report['summary']['total_records']}")
            logger.info(f"  - 总流入: {flow_report['summary']['total_inflow']:,.2f}")
            logger.info(f"  - 总流出: {flow_report['summary']['total_outflow']:,.2f}")
            logger.info(f"  - 净流量: {flow_report['summary']['net_flow']:,.2f}")
            
            # 计算成本分析
            cost_analysis = self.flow_analyzer.calculate_cost_analysis(self.test_symbol)
            logger.info(f"✓ 成本分析完成:")
            logger.info(f"  - 总成本: {cost_analysis.total_cost:.2f}")
            logger.info(f"  - 成本率: {cost_analysis.cost_ratio:.4%}")
            logger.info(f"  - 效率评分: {cost_analysis.cost_efficiency_score:.1f}")
            
            # 生成成本优化报告
            cost_report = self.flow_analyzer.generate_cost_optimization_report()
            logger.info(f"✓ 成本优化报告:")
            logger.info(f"  - 整体成本率: {cost_report['overall_metrics']['overall_cost_ratio']:.4%}")
            logger.info(f"  - 优化建议: {len(cost_report['optimization_suggestions'])}条")
            
            return True
            
        except Exception as e:
            logger.error(f"✗ 资金流水分析测试失败: {e}")
            return False
    
    def test_5_capital_efficiency_optimization(self) -> bool:
        """测试5: 资金使用效率优化"""
        try:
            logger.info("测试5: 资金使用效率优化")
            
            # 计算资金使用效率
            efficiency = self.capital_manager.calculate_capital_efficiency()
            logger.info(f"✓ 资金效率计算:")
            logger.info(f"  - 资金利用率: {efficiency['capital_utilization']:.1f}%")
            logger.info(f"  - 现金闲置率: {efficiency['cash_idle_rate']:.1f}%")
            logger.info(f"  - 持仓集中度: {efficiency['concentration_ratio']:.1f}%")
            logger.info(f"  - 换手率: {efficiency['turnover_rate']:.1f}%")
            logger.info(f"  - T+0效率: {efficiency['t_efficiency']:.2f}%")
            logger.info(f"  - 综合效率评分: {efficiency['efficiency_score']:.1f}")
            
            # 测试资金分配优化
            target_stocks = ["000001.SZ", "000002.SZ", "600000.SH", "600036.SH"]
            stock_scores = {
                "000001.SZ": 0.88,
                "000002.SZ": 0.82,
                "600000.SH": 0.75,
                "600036.SH": 0.70
            }
            
            optimization = self.capital_manager.optimize_capital_allocation(target_stocks, stock_scores)
            logger.info(f"✓ 资金分配优化:")
            logger.info(f"  - 策略: {optimization['strategy']}")
            logger.info(f"  - 预期效率: {optimization['expected_efficiency']:.1f}%")
            logger.info(f"  - 现金储备: {optimization['cash_reserve']:,.2f}")
            
            # 显示分配详情
            for symbol, allocation in optimization['allocations'].items():
                logger.info(f"  - {symbol}: 总额{allocation['total_amount']:,.2f}, "
                           f"底仓{allocation['base_amount']:,.2f}, "
                           f"T仓{allocation['t_amount']:,.2f}, "
                           f"评分{allocation['score']:.2f}")
            
            return True
            
        except Exception as e:
            logger.error(f"✗ 资金效率优化测试失败: {e}")
            return False
    
    def test_6_integrated_workflow_simulation(self) -> bool:
        """测试6: 集成工作流程模拟"""
        try:
            logger.info("测试6: 集成工作流程模拟")
            
            # 模拟一个完整的交易日
            logger.info("模拟完整交易日工作流程:")
            
            # 早盘开盘
            logger.info("09:30 - 开盘")
            morning_price = 11.95
            self.capital_manager.update_positions_price({self.test_symbol: morning_price})
            
            # 上午做T操作
            logger.info("10:30 - 上午做T")
            self.capital_manager.execute_buy(self.test_symbol, 1200, 11.90, PositionType.T_POSITION, "上午T买")
            self.capital_manager.execute_sell(self.test_symbol, 1200, 12.05, PositionType.T_POSITION, "上午T卖")
            
            # 中午休盘前
            logger.info("11:30 - 中午休盘前")
            noon_price = 12.10
            self.capital_manager.update_positions_price({self.test_symbol: noon_price})
            
            # 下午开盘
            logger.info("13:00 - 下午开盘")
            afternoon_price = 12.15
            self.capital_manager.update_positions_price({self.test_symbol: afternoon_price})
            
            # 下午做T操作
            logger.info("14:00 - 下午做T")
            self.capital_manager.execute_buy(self.test_symbol, 1000, 12.12, PositionType.T_POSITION, "下午T买")
            self.capital_manager.execute_sell(self.test_symbol, 1000, 12.25, PositionType.T_POSITION, "下午T卖")
            
            # 收盘
            logger.info("15:00 - 收盘")
            close_price = 12.30
            self.capital_manager.update_positions_price({self.test_symbol: close_price})
            
            # 更新风险控制器
            if self.test_symbol in self.capital_manager.positions:
                position = self.capital_manager.positions[self.test_symbol]
                self.risk_controller.update_position(
                    symbol=self.test_symbol,
                    current_price=close_price,
                    position_value=position.market_value,
                    cost_basis=position.avg_cost,
                    daily_volume=150000000
                )
            
            # 日终总结
            portfolio = self.capital_manager.get_portfolio_summary()
            risk_status = self.risk_controller.get_current_risk_status()
            efficiency = self.capital_manager.calculate_capital_efficiency()
            
            logger.info("✓ 交易日总结:")
            logger.info(f"  - 投资组合价值: {portfolio['portfolio_value']:,.2f}")
            logger.info(f"  - 当日收益率: {portfolio['total_return_pct']:.2f}%")
            logger.info(f"  - 风险等级: {risk_status['overall_risk_level']}")
            logger.info(f"  - 效率评分: {efficiency['efficiency_score']:.1f}")
            logger.info(f"  - 交易次数: {len(self.capital_manager.transactions)}")
            
            return True
            
        except Exception as e:
            logger.error(f"✗ 集成工作流程模拟测试失败: {e}")
            return False
    
    def generate_comprehensive_report(self) -> Dict:
        """生成综合测试报告"""
        try:
            logger.info("生成综合测试报告")
            
            # 收集各组件状态
            report = {
                "test_time": datetime.now().isoformat(),
                "test_account": self.test_account_id,
                "test_capital": self.test_capital,
                "test_symbol": self.test_symbol
            }
            
            # 小资金配置状态
            if self.config_manager:
                config = self.config_manager.get_config(self.test_account_id)
                if config:
                    report["small_capital_config"] = {
                        "account_id": config.account_id,
                        "total_capital": config.total_capital,
                        "risk_level": config.risk_level.value,
                        "base_position_ratio": config.base_position_ratio,
                        "t_position_ratio": config.t_position_ratio,
                        "cash_reserve_ratio": config.cash_reserve_ratio,
                        "max_daily_loss_ratio": config.max_daily_loss_ratio,
                        "max_t_times_per_day": config.max_t_times_per_day
                    }
            
            # 资金管理状态
            if self.capital_manager:
                portfolio = self.capital_manager.get_portfolio_summary()
                efficiency = self.capital_manager.calculate_capital_efficiency()
                allocation = self.capital_manager.get_capital_allocation()
                
                report["capital_management"] = {
                    "portfolio_value": portfolio["portfolio_value"],
                    "total_return": portfolio["total_return"],
                    "total_return_pct": portfolio["total_return_pct"],
                    "available_cash": portfolio["available_cash"],
                    "position_count": portfolio["position_count"],
                    "efficiency_score": efficiency["efficiency_score"],
                    "capital_utilization": efficiency["capital_utilization"],
                    "cash_idle_rate": efficiency["cash_idle_rate"],
                    "t_efficiency": efficiency["t_efficiency"],
                    "transaction_count": len(self.capital_manager.transactions)
                }
            
            # 风险控制状态
            if self.risk_controller:
                risk_status = self.risk_controller.get_current_risk_status()
                risk_report = self.risk_controller.generate_risk_report()
                
                report["risk_control"] = {
                    "overall_risk_level": risk_status["overall_risk_level"],
                    "active_alerts_count": risk_status["active_alerts_count"],
                    "total_alerts_count": risk_status["total_alerts_count"],
                    "current_drawdown": risk_status["current_drawdown"],
                    "max_drawdown": risk_status["max_drawdown"],
                    "position_pnl": risk_status["position_pnl"],
                    "holding_days": risk_status["holding_days"],
                    "performance_metrics": risk_report["performance_metrics"]
                }
            
            # 流水分析状态
            if self.flow_analyzer:
                statistics = self.flow_analyzer.get_flow_statistics()
                cost_analysis = self.flow_analyzer.calculate_cost_analysis(self.test_symbol)
                cost_report = self.flow_analyzer.generate_cost_optimization_report()
                
                report["flow_analysis"] = {
                    "total_records": statistics["total_records"],
                    "net_flow": statistics["flow_statistics"]["net_flow"],
                    "total_inflow": statistics["flow_statistics"]["total_inflow"],
                    "total_outflow": statistics["flow_statistics"]["total_outflow"],
                    "cost_efficiency_score": cost_analysis.cost_efficiency_score,
                    "total_cost_ratio": cost_analysis.cost_ratio,
                    "overall_cost_ratio": cost_report["overall_metrics"]["overall_cost_ratio"]
                }
            
            # 保存报告
            report_file = f"capital_management_complete_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            logger.info(f"✓ 综合测试报告已保存: {report_file}")
            return report
            
        except Exception as e:
            logger.error(f"✗ 生成综合测试报告失败: {e}")
            return {}
    
    def cleanup(self):
        """清理测试环境"""
        try:
            logger.info("清理测试环境")
            
            # 删除测试配置
            if self.config_manager:
                self.config_manager.delete_config(self.test_account_id)
                logger.info("✓ 测试配置已删除")
            
            logger.info("✓ 测试环境清理完成")
            
        except Exception as e:
            logger.error(f"✗ 清理测试环境失败: {e}")
    
    def run_complete_test(self) -> bool:
        """运行完整测试"""
        try:
            test_results = []
            
            # 执行各项测试
            test_results.append(("小资金配置管理", self.test_1_small_capital_config_management()))
            test_results.append(("资金管理器操作", self.test_2_capital_manager_operations()))
            test_results.append(("风险控制器监控", self.test_3_risk_controller_monitoring()))
            test_results.append(("资金流水分析", self.test_4_fund_flow_analysis()))
            test_results.append(("资金效率优化", self.test_5_capital_efficiency_optimization()))
            test_results.append(("集成工作流程", self.test_6_integrated_workflow_simulation()))
            
            # 统计测试结果
            passed_tests = sum(1 for _, result in test_results if result)
            total_tests = len(test_results)
            
            logger.info(f"\n=== 完整测试结果汇总 ===")
            for test_name, result in test_results:
                status = "✓ 通过" if result else "✗ 失败"
                logger.info(f"{test_name}: {status}")
            
            logger.info(f"\n总计: {passed_tests}/{total_tests} 项测试通过")
            
            # 生成综合测试报告
            report = self.generate_comprehensive_report()
            
            # 清理测试环境
            self.cleanup()
            
            success_rate = passed_tests / total_tests
            if success_rate >= 0.85:  # 85%以上通过率认为测试成功
                logger.info(f"🎉 完整资金管理系统集成测试成功! 通过率: {success_rate:.1%}")
                return True
            else:
                logger.error(f"❌ 完整资金管理系统集成测试失败! 通过率: {success_rate:.1%}")
                return False
                
        except Exception as e:
            logger.error(f"✗ 运行完整测试失败: {e}")
            return False


def main():
    """主函数"""
    try:
        # 创建测试实例
        test_system = CompleteCapitalManagementTest()
        
        # 运行完整测试
        success = test_system.run_complete_test()
        
        if success:
            print("\n🎉 完整资金管理系统集成测试全部通过!")
            return 0
        else:
            print("\n❌ 完整资金管理系统集成测试存在失败项!")
            return 1
            
    except Exception as e:
        print(f"\n💥 测试执行异常: {e}")
        return 1


if __name__ == "__main__":
    exit(main())