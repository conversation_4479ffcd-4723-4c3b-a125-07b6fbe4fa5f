"""
FastAPI系统测试
FastAPI System Test

测试新的FastAPI系统是否正常工作
"""

import asyncio
import json
import logging
from datetime import datetime
import httpx
import pytest

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class FastAPISystemTest:
    """FastAPI系统测试类"""
    
    def __init__(self):
        """初始化测试环境"""
        self.base_url = "http://localhost:8000"
        self.api_prefix = "/api/v1"
        
        # 测试结果
        self.test_results = {
            "total_tests": 0,
            "passed_tests": 0,
            "failed_tests": 0,
            "test_details": []
        }
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("开始FastAPI系统测试")
        
        try:
            # 1. 测试应用启动
            await self.test_app_startup()
            
            # 2. 测试健康检查
            await self.test_health_checks()
            
            # 3. 测试认证系统
            await self.test_authentication()
            
            # 4. 测试API端点
            await self.test_api_endpoints()
            
            # 5. 测试错误处理
            await self.test_error_handling()
            
        except Exception as e:
            logger.error(f"测试执行异常: {e}")
            self._record_test_result("测试执行", False, str(e))
        
        finally:
            # 生成测试报告
            self._generate_test_report()
    
    async def test_app_startup(self):
        """测试应用启动"""
        logger.info("测试应用启动")
        
        try:
            # 尝试导入FastAPI应用
            from qlib_trading_system.api.main import create_app
            
            # 创建应用实例
            app = create_app()
            assert app is not None, "FastAPI应用创建失败"
            assert hasattr(app, 'state'), "FastAPI应用缺少state属性"
            
            # 检查组件是否正确初始化
            assert hasattr(app.state, 'auth_manager'), "认证管理器未初始化"
            assert hasattr(app.state, 'api_monitor'), "API监控器未初始化"
            assert hasattr(app.state, 'websocket_manager'), "WebSocket管理器未初始化"
            assert hasattr(app.state, 'message_queue'), "消息队列未初始化"
            
            self._record_test_result("应用启动", True)
            
        except Exception as e:
            self._record_test_result("应用启动", False, str(e))
    
    async def test_health_checks(self):
        """测试健康检查"""
        logger.info("测试健康检查")
        
        try:
            async with httpx.AsyncClient() as client:
                # 测试基本健康检查
                response = await client.get(f"{self.base_url}/health")
                assert response.status_code == 200, f"健康检查失败: {response.status_code}"
                
                data = response.json()
                assert data["status"] == "healthy", "健康状态不正确"
                assert "timestamp" in data, "健康检查响应缺少时间戳"
                
                # 测试API信息端点
                response = await client.get(f"{self.base_url}/info")
                assert response.status_code == 200, f"API信息获取失败: {response.status_code}"
                
                info_data = response.json()
                assert "title" in info_data, "API信息缺少标题"
                assert "version" in info_data, "API信息缺少版本"
                assert "endpoints" in info_data, "API信息缺少端点信息"
                
            self._record_test_result("健康检查", True)
            
        except Exception as e:
            self._record_test_result("健康检查", False, str(e))
    
    async def test_authentication(self):
        """测试认证系统"""
        logger.info("测试认证系统")
        
        try:
            async with httpx.AsyncClient() as client:
                # 测试登录端点
                login_data = {
                    "username": "admin",
                    "password": "admin123"
                }
                
                response = await client.post(
                    f"{self.base_url}{self.api_prefix}/auth/login",
                    json=login_data
                )
                
                # 由于Redis可能未启动，我们检查是否返回了合理的错误
                if response.status_code == 200:
                    data = response.json()
                    assert "token" in data, "登录响应缺少token"
                    assert "user" in data, "登录响应缺少用户信息"
                    
                    # 测试token验证
                    token = data["token"]
                    headers = {"Authorization": f"Bearer {token}"}
                    
                    profile_response = await client.get(
                        f"{self.base_url}{self.api_prefix}/auth/profile",
                        headers=headers
                    )
                    assert profile_response.status_code == 200, "Token验证失败"
                    
                elif response.status_code == 500:
                    # Redis未启动的情况，这是可以接受的
                    logger.warning("认证系统测试跳过（Redis未启动）")
                else:
                    raise AssertionError(f"登录请求失败: {response.status_code}")
                
                # 测试权限端点
                permissions_response = await client.get(
                    f"{self.base_url}{self.api_prefix}/auth/permissions"
                )
                assert permissions_response.status_code == 200, "获取权限列表失败"
                
                permissions_data = permissions_response.json()
                assert "permissions" in permissions_data, "权限响应格式不正确"
                
            self._record_test_result("认证系统", True)
            
        except Exception as e:
            self._record_test_result("认证系统", False, str(e))
    
    async def test_api_endpoints(self):
        """测试API端点"""
        logger.info("测试API端点")
        
        try:
            async with httpx.AsyncClient() as client:
                # 测试各个模块的健康检查端点
                endpoints = [
                    f"{self.api_prefix}/config/health",
                    f"{self.api_prefix}/monitoring/health",
                    f"{self.api_prefix}/trading/health"
                ]
                
                for endpoint in endpoints:
                    response = await client.get(f"{self.base_url}{endpoint}")
                    assert response.status_code == 200, f"端点 {endpoint} 健康检查失败"
                    
                    data = response.json()
                    assert data["status"] == "healthy", f"端点 {endpoint} 状态不健康"
                
                # 测试OpenAPI文档端点
                openapi_response = await client.get(f"{self.base_url}/openapi.json")
                assert openapi_response.status_code == 200, "OpenAPI文档获取失败"
                
                openapi_data = openapi_response.json()
                assert "openapi" in openapi_data, "OpenAPI文档格式不正确"
                assert "paths" in openapi_data, "OpenAPI文档缺少路径信息"
                
            self._record_test_result("API端点", True)
            
        except Exception as e:
            self._record_test_result("API端点", False, str(e))
    
    async def test_error_handling(self):
        """测试错误处理"""
        logger.info("测试错误处理")
        
        try:
            async with httpx.AsyncClient() as client:
                # 测试404错误
                response = await client.get(f"{self.base_url}/nonexistent")
                assert response.status_code == 404, "404错误处理不正确"
                
                # 测试无效的API端点
                response = await client.get(f"{self.base_url}{self.api_prefix}/invalid")
                assert response.status_code == 404, "无效API端点错误处理不正确"
                
                # 测试方法不允许
                response = await client.post(f"{self.base_url}/health")
                assert response.status_code == 405, "方法不允许错误处理不正确"
                
            self._record_test_result("错误处理", True)
            
        except Exception as e:
            self._record_test_result("错误处理", False, str(e))
    
    def _record_test_result(self, test_name: str, passed: bool, error_msg: str = ""):
        """记录测试结果"""
        self.test_results["total_tests"] += 1
        
        if passed:
            self.test_results["passed_tests"] += 1
            logger.info(f"✅ {test_name} - 通过")
        else:
            self.test_results["failed_tests"] += 1
            logger.error(f"❌ {test_name} - 失败: {error_msg}")
        
        self.test_results["test_details"].append({
            "test_name": test_name,
            "passed": passed,
            "error_message": error_msg,
            "timestamp": datetime.now().isoformat()
        })
    
    def _generate_test_report(self):
        """生成测试报告"""
        logger.info("生成测试报告")
        
        # 计算通过率
        total = self.test_results["total_tests"]
        passed = self.test_results["passed_tests"]
        pass_rate = (passed / total * 100) if total > 0 else 0
        
        # 生成报告
        report = {
            "test_summary": {
                "total_tests": total,
                "passed_tests": passed,
                "failed_tests": self.test_results["failed_tests"],
                "pass_rate": f"{pass_rate:.1f}%"
            },
            "test_details": self.test_results["test_details"],
            "test_time": datetime.now().isoformat()
        }
        
        # 保存报告
        with open("fastapi_system_test_report.json", 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        # 打印摘要
        logger.info("=" * 60)
        logger.info("FastAPI系统测试报告")
        logger.info("=" * 60)
        logger.info(f"总测试数: {total}")
        logger.info(f"通过测试: {passed}")
        logger.info(f"失败测试: {self.test_results['failed_tests']}")
        logger.info(f"通过率: {pass_rate:.1f}%")
        logger.info(f"测试报告: fastapi_system_test_report.json")
        logger.info("=" * 60)


async def main():
    """主函数"""
    test = FastAPISystemTest()
    await test.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
