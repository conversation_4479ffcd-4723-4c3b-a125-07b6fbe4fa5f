"""
熔断机制简化测试
测试极端行情熔断机制的核心功能
"""

import sys
import os
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any
from dataclasses import dataclass, field
from enum import Enum
import statistics

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


class AlertLevel(Enum):
    """警报级别枚举"""
    NORMAL = "NORMAL"
    WARNING = "WARNING"
    DANGER = "DANGER"
    CRITICAL = "CRITICAL"
    EMERGENCY = "EMERGENCY"


@dataclass
class MarketAnomalyEvent:
    """市场异常事件数据类"""
    event_type: str
    severity: AlertLevel
    timestamp: datetime
    symbol: str = None
    description: str = ""
    metrics: Dict[str, float] = field(default_factory=dict)
    suggested_actions: List[str] = field(default_factory=list)


@dataclass
class CircuitBreakerConfig:
    """熔断机制配置"""
    single_stock_drop_threshold: float = -0.08
    single_stock_crash_threshold: float = -0.15
    market_drop_threshold: float = -0.03
    market_crash_threshold: float = -0.05
    volume_surge_threshold: float = 3.0
    volume_dry_threshold: float = 0.3
    consecutive_loss_days: int = 3
    daily_loss_threshold: float = -0.01


class MarketAnomalyDetector:
    """市场异常检测器"""
    
    def __init__(self, config: CircuitBreakerConfig):
        self.config = config
    
    def detect_price_anomaly(self, symbol: str, current_price: float, 
                           reference_price: float) -> MarketAnomalyEvent:
        """检测价格异常"""
        price_change = (current_price - reference_price) / reference_price
        
        if price_change <= self.config.single_stock_crash_threshold:
            return MarketAnomalyEvent(
                event_type="PRICE_CRASH",
                severity=AlertLevel.CRITICAL,
                timestamp=datetime.now(),
                symbol=symbol,
                description=f"股票{symbol}价格暴跌{price_change:.2%}",
                metrics={"price_change": price_change},
                suggested_actions=["EMERGENCY_EXIT", "STOP_TRADING"]
            )
        elif price_change <= self.config.single_stock_drop_threshold:
            return MarketAnomalyEvent(
                event_type="PRICE_DROP",
                severity=AlertLevel.WARNING,
                timestamp=datetime.now(),
                symbol=symbol,
                description=f"股票{symbol}价格下跌{price_change:.2%}",
                metrics={"price_change": price_change},
                suggested_actions=["REDUCE_POSITION", "MONITOR_CLOSELY"]
            )
        
        return None
    
    def detect_market_anomaly(self, market_data: Dict[str, float]) -> MarketAnomalyEvent:
        """检测市场整体异常"""
        index_change = market_data.get("index_change", 0.0)
        
        if index_change <= self.config.market_crash_threshold:
            return MarketAnomalyEvent(
                event_type="MARKET_CRASH",
                severity=AlertLevel.EMERGENCY,
                timestamp=datetime.now(),
                description=f"市场出现崩盘，指数下跌{index_change:.2%}",
                metrics={"index_change": index_change},
                suggested_actions=["FULL_DEFENSIVE_MODE", "EMERGENCY_LIQUIDATION"]
            )
        elif index_change <= self.config.market_drop_threshold:
            return MarketAnomalyEvent(
                event_type="MARKET_DROP",
                severity=AlertLevel.DANGER,
                timestamp=datetime.now(),
                description=f"市场大幅下跌{index_change:.2%}",
                metrics={"index_change": index_change},
                suggested_actions=["DEFENSIVE_MODE", "REDUCE_EXPOSURE"]
            )
        
        return None


class BlackSwanDetector:
    """黑天鹅事件检测器"""
    
    def __init__(self):
        self.news_keywords = {
            "negative": ["立案调查", "ST", "退市", "减持", "财务造假", "监管处罚"],
            "market_risk": ["熔断", "暴跌", "崩盘", "恐慌", "流动性危机"]
        }
    
    def detect_news_anomaly(self, news_data: List[str]) -> MarketAnomalyEvent:
        """检测新闻异常事件"""
        negative_score = 0
        market_risk_score = 0
        
        for news in news_data:
            for keyword in self.news_keywords["negative"]:
                if keyword in news:
                    negative_score += 1
            for keyword in self.news_keywords["market_risk"]:
                if keyword in news:
                    market_risk_score += 2
        
        total_risk_score = negative_score + market_risk_score
        
        if total_risk_score >= 5:
            return MarketAnomalyEvent(
                event_type="BLACK_SWAN_NEWS",
                severity=AlertLevel.EMERGENCY,
                timestamp=datetime.now(),
                description=f"检测到重大负面新闻，风险评分{total_risk_score}",
                metrics={"risk_score": total_risk_score},
                suggested_actions=["IMMEDIATE_REVIEW", "CONSIDER_EXIT"]
            )
        elif total_risk_score >= 3:
            return MarketAnomalyEvent(
                event_type="NEGATIVE_NEWS",
                severity=AlertLevel.DANGER,
                timestamp=datetime.now(),
                description=f"检测到负面新闻，风险评分{total_risk_score}",
                metrics={"risk_score": total_risk_score},
                suggested_actions=["MONITOR_CLOSELY", "PREPARE_EXIT"]
            )
        
        return None


class CircuitBreaker:
    """主熔断控制器"""
    
    def __init__(self, config: CircuitBreakerConfig = None):
        self.config = config or CircuitBreakerConfig()
        self.anomaly_detector = MarketAnomalyDetector(self.config)
        self.black_swan_detector = BlackSwanDetector()
        
        self.is_active = True
        self.circuit_breaker_triggered = False
        self.last_trigger_time = None
        self.consecutive_loss_days = 0
        self.daily_pnl_history = []
    
    def check_market_conditions(self, market_data: Dict[str, Any]) -> List[MarketAnomalyEvent]:
        """检查市场状况并识别异常"""
        anomalies = []
        
        try:
            # 检测价格异常
            for symbol, price_data in market_data.get("stocks", {}).items():
                current_price = price_data.get("current_price", 0)
                reference_price = price_data.get("reference_price", current_price)
                
                price_anomaly = self.anomaly_detector.detect_price_anomaly(
                    symbol, current_price, reference_price
                )
                if price_anomaly:
                    anomalies.append(price_anomaly)
            
            # 检测市场整体异常
            market_anomaly = self.anomaly_detector.detect_market_anomaly(
                market_data.get("market", {})
            )
            if market_anomaly:
                anomalies.append(market_anomaly)
            
            # 检测黑天鹅事件
            news_data = market_data.get("news", [])
            if news_data:
                news_anomaly = self.black_swan_detector.detect_news_anomaly(news_data)
                if news_anomaly:
                    anomalies.append(news_anomaly)
            
        except Exception as e:
            print(f"市场状况检查出错: {e}")
        
        return anomalies
    
    def process_anomalies(self, anomalies: List[MarketAnomalyEvent]) -> Dict[str, Any]:
        """处理检测到的异常"""
        if not anomalies:
            return {"status": "normal", "actions": []}
        
        # 按严重程度排序
        anomalies.sort(key=lambda x: x.severity.value, reverse=True)
        
        highest_severity = anomalies[0].severity
        
        # 根据最高严重级别决定是否触发熔断
        if highest_severity in [AlertLevel.CRITICAL, AlertLevel.EMERGENCY]:
            self.trigger_circuit_breaker(anomalies[0])
        
        return {
            "status": "anomaly_detected",
            "highest_severity": highest_severity.value,
            "anomaly_count": len(anomalies),
            "anomalies": anomalies
        }
    
    def trigger_circuit_breaker(self, trigger_anomaly: MarketAnomalyEvent) -> None:
        """触发熔断机制"""
        if self.circuit_breaker_triggered:
            return
        
        self.circuit_breaker_triggered = True
        self.last_trigger_time = datetime.now()
        
        print(f"🚨 熔断机制触发! 触发事件: {trigger_anomaly.event_type} - {trigger_anomaly.description}")
    
    def reset_circuit_breaker(self) -> bool:
        """重置熔断机制"""
        if not self.circuit_breaker_triggered:
            return False
        
        # 检查冷却时间（简化为5秒）
        if self.last_trigger_time:
            cooldown_elapsed = (datetime.now() - self.last_trigger_time).seconds
            if cooldown_elapsed < 5:
                print(f"熔断冷却中，剩余{5 - cooldown_elapsed}秒")
                return False
        
        self.circuit_breaker_triggered = False
        self.last_trigger_time = None
        
        print("熔断机制已重置")
        return True
    
    def check_consecutive_losses(self, daily_pnl: float) -> MarketAnomalyEvent:
        """检查连续亏损情况"""
        self.daily_pnl_history.append({
            "date": datetime.now().date(),
            "pnl": daily_pnl
        })
        
        # 只保留最近30天的记录
        if len(self.daily_pnl_history) > 30:
            self.daily_pnl_history = self.daily_pnl_history[-30:]
        
        # 检查连续亏损天数
        consecutive_losses = 0
        for record in reversed(self.daily_pnl_history):
            if record["pnl"] < self.config.daily_loss_threshold:
                consecutive_losses += 1
            else:
                break
        
        self.consecutive_loss_days = consecutive_losses
        
        if consecutive_losses >= self.config.consecutive_loss_days:
            return MarketAnomalyEvent(
                event_type="CONSECUTIVE_LOSSES",
                severity=AlertLevel.DANGER,
                timestamp=datetime.now(),
                description=f"连续{consecutive_losses}天亏损",
                metrics={"consecutive_days": consecutive_losses, "daily_pnl": daily_pnl},
                suggested_actions=["REDUCE_FREQUENCY", "REDUCE_POSITION_SIZE"]
            )
        
        return None


class CircuitBreakerTest:
    """熔断机制测试类"""
    
    def __init__(self):
        self.circuit_breaker = CircuitBreaker()
        self.test_results = []
        
        print("=" * 60)
        print("熔断机制测试开始")
        print("=" * 60)
    
    def test_price_anomaly_detection(self) -> bool:
        """测试价格异常检测"""
        print("\n1. 测试价格异常检测...")
        
        try:
            # 模拟价格暴跌数据（-15%触发严重异常）
            crash_data = {
                "market": {"index_change": -0.02},
                "stocks": {
                    "TEST001": {
                        "current_price": 8.5,  # -15%跌幅
                        "reference_price": 10.0
                    }
                }
            }
            
            anomalies = self.circuit_breaker.check_market_conditions(crash_data)
            result = self.circuit_breaker.process_anomalies(anomalies)
            
            if self.circuit_breaker.circuit_breaker_triggered:
                print("✓ 价格异常检测成功，熔断机制已触发")
                return True
            else:
                print("✗ 价格异常检测失败，熔断机制未触发")
                return False
                
        except Exception as e:
            print(f"✗ 价格异常检测测试出错: {e}")
            return False
    
    def test_market_crash_detection(self) -> bool:
        """测试市场崩盘检测"""
        print("\n2. 测试市场崩盘检测...")
        
        try:
            # 重置熔断器
            self.circuit_breaker.reset_circuit_breaker()
            time.sleep(1)
            
            # 模拟市场崩盘数据
            crash_data = {
                "market": {"index_change": -0.06},  # 6%跌幅
                "stocks": {
                    "TEST002": {
                        "current_price": 9.5,
                        "reference_price": 10.0
                    }
                }
            }
            
            anomalies = self.circuit_breaker.check_market_conditions(crash_data)
            result = self.circuit_breaker.process_anomalies(anomalies)
            
            if self.circuit_breaker.circuit_breaker_triggered:
                print("✓ 市场崩盘检测成功，熔断机制已触发")
                return True
            else:
                print("✗ 市场崩盘检测失败，熔断机制未触发")
                return False
                
        except Exception as e:
            print(f"✗ 市场崩盘检测测试出错: {e}")
            return False
    
    def test_black_swan_detection(self) -> bool:
        """测试黑天鹅事件检测"""
        print("\n3. 测试黑天鹅事件检测...")
        
        try:
            # 重置熔断器
            self.circuit_breaker.reset_circuit_breaker()
            time.sleep(1)
            
            # 模拟黑天鹅事件数据
            black_swan_data = {
                "market": {"index_change": -0.01},
                "stocks": {
                    "TEST003": {
                        "current_price": 9.5,
                        "reference_price": 10.0
                    }
                },
                "news": [
                    "某公司被立案调查",
                    "监管部门发布重大处罚决定",
                    "市场出现恐慌情绪",
                    "流动性危机爆发"
                ]
            }
            
            anomalies = self.circuit_breaker.check_market_conditions(black_swan_data)
            result = self.circuit_breaker.process_anomalies(anomalies)
            
            if self.circuit_breaker.circuit_breaker_triggered:
                print("✓ 黑天鹅事件检测成功，熔断机制已触发")
                return True
            else:
                print("✗ 黑天鹅事件检测失败，熔断机制未触发")
                return False
                
        except Exception as e:
            print(f"✗ 黑天鹅事件检测测试出错: {e}")
            return False
    
    def test_consecutive_loss_detection(self) -> bool:
        """测试连续亏损检测"""
        print("\n4. 测试连续亏损检测...")
        
        try:
            # 重置熔断器
            self.circuit_breaker.reset_circuit_breaker()
            time.sleep(1)
            
            # 模拟连续3天亏损
            for day in range(3):
                anomaly = self.circuit_breaker.check_consecutive_losses(-0.015)
                if anomaly:
                    result = self.circuit_breaker.process_anomalies([anomaly])
                    break
            
            if self.circuit_breaker.consecutive_loss_days >= 3:
                print("✓ 连续亏损检测成功，已记录连续亏损天数")
                return True
            else:
                print("✗ 连续亏损检测失败，未正确记录亏损天数")
                return False
                
        except Exception as e:
            print(f"✗ 连续亏损检测测试出错: {e}")
            return False
    
    def test_circuit_breaker_reset(self) -> bool:
        """测试熔断器重置功能"""
        print("\n5. 测试熔断器重置功能...")
        
        try:
            # 确保熔断器已触发
            if not self.circuit_breaker.circuit_breaker_triggered:
                # 手动触发熔断
                trigger_anomaly = MarketAnomalyEvent(
                    event_type="MANUAL_TRIGGER",
                    severity=AlertLevel.CRITICAL,
                    timestamp=datetime.now(),
                    description="手动触发测试"
                )
                self.circuit_breaker.trigger_circuit_breaker(trigger_anomaly)
            
            # 尝试立即重置（应该失败）
            reset_result = self.circuit_breaker.reset_circuit_breaker()
            
            if not reset_result:
                print("✓ 熔断器冷却机制正常工作")
                
                # 等待冷却时间
                time.sleep(6)
                
                # 再次尝试重置
                reset_result = self.circuit_breaker.reset_circuit_breaker()
                
                if reset_result:
                    print("✓ 熔断器重置功能测试成功")
                    return True
                else:
                    print("✗ 熔断器重置功能测试失败")
                    return False
            else:
                print("✗ 熔断器冷却机制异常")
                return False
                
        except Exception as e:
            print(f"✗ 熔断器重置功能测试出错: {e}")
            return False
    
    def test_defense_strategy_recommendations(self) -> bool:
        """测试防御策略建议"""
        print("\n6. 测试防御策略建议...")
        
        try:
            # 模拟不同严重程度的异常
            test_cases = [
                {
                    "name": "轻微下跌",
                    "data": {
                        "market": {"index_change": -0.035},  # 超过-3%阈值
                        "stocks": {"TEST004": {"current_price": 9.5, "reference_price": 10.0}}
                    },
                    "expected_severity": AlertLevel.DANGER
                },
                {
                    "name": "严重暴跌",
                    "data": {
                        "market": {"index_change": -0.08},
                        "stocks": {"TEST005": {"current_price": 8.0, "reference_price": 10.0}}
                    },
                    "expected_severity": AlertLevel.EMERGENCY
                }
            ]
            
            success_count = 0
            
            for test_case in test_cases:
                self.circuit_breaker.reset_circuit_breaker()
                time.sleep(1)
                
                anomalies = self.circuit_breaker.check_market_conditions(test_case["data"])
                
                if anomalies:
                    highest_severity = max(anomalies, key=lambda x: x.severity.value).severity
                    if highest_severity == test_case["expected_severity"]:
                        print(f"  ✓ {test_case['name']}: 检测到{highest_severity.value}级别异常")
                        success_count += 1
                    else:
                        print(f"  ✗ {test_case['name']}: 期望{test_case['expected_severity'].value}，实际{highest_severity.value}")
                else:
                    print(f"  ✗ {test_case['name']}: 未检测到异常")
            
            if success_count == len(test_cases):
                print("✓ 防御策略建议测试成功")
                return True
            else:
                print(f"✗ 防御策略建议测试失败，成功{success_count}/{len(test_cases)}")
                return False
                
        except Exception as e:
            print(f"✗ 防御策略建议测试出错: {e}")
            return False
    
    def run_all_tests(self) -> None:
        """运行所有测试"""
        test_methods = [
            ("价格异常检测", self.test_price_anomaly_detection),
            ("市场崩盘检测", self.test_market_crash_detection),
            ("黑天鹅事件检测", self.test_black_swan_detection),
            ("连续亏损检测", self.test_consecutive_loss_detection),
            ("熔断器重置功能", self.test_circuit_breaker_reset),
            ("防御策略建议", self.test_defense_strategy_recommendations)
        ]
        
        passed_tests = 0
        total_tests = len(test_methods)
        
        for test_name, test_method in test_methods:
            try:
                result = test_method()
                if result:
                    passed_tests += 1
                    self.test_results.append({"test": test_name, "result": "PASS"})
                else:
                    self.test_results.append({"test": test_name, "result": "FAIL"})
            except Exception as e:
                print(f"✗ {test_name}测试异常: {e}")
                self.test_results.append({"test": test_name, "result": "ERROR", "error": str(e)})
        
        # 输出测试结果
        self.print_test_summary(passed_tests, total_tests)
    
    def print_test_summary(self, passed_tests: int, total_tests: int) -> None:
        """打印测试摘要"""
        print("\n" + "=" * 60)
        print("测试结果摘要")
        print("=" * 60)
        
        for result in self.test_results:
            status_symbol = "✓" if result["result"] == "PASS" else "✗"
            print(f"{status_symbol} {result['test']}: {result['result']}")
            if "error" in result:
                print(f"  错误: {result['error']}")
        
        print(f"\n通过测试: {passed_tests}/{total_tests}")
        print(f"成功率: {passed_tests/total_tests*100:.1f}%")
        
        if passed_tests == total_tests:
            print("\n🎉 所有测试通过！熔断机制功能正常。")
        else:
            print(f"\n⚠️  有{total_tests - passed_tests}个测试失败，请检查相关功能。")
        
        print("=" * 60)


def main():
    """主函数"""
    try:
        # 创建并运行测试
        test_runner = CircuitBreakerTest()
        test_runner.run_all_tests()
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"测试运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()