"""
安装脚本
"""
from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="qlib-trading-system",
    version="1.0.0",
    author="Trading System Team",
    author_email="<EMAIL>",
    description="基于qlib框架的双AI交易系统",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/your-org/qlib-trading-system",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Financial and Insurance Industry",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Topic :: Office/Business :: Financial :: Investment",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    entry_points={
        "console_scripts": [
            "qlib-trading=main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "qlib_trading_system": ["*.yaml", "*.json", "*.txt"],
    },
)