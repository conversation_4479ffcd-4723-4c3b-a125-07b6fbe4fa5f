#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Qlib交易系统Web管理界面集成测试

完整测试Web界面的所有功能模块
"""

import os
import sys
import json
import logging
from pathlib import Path
from fastapi.testclient import TestClient

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/web_integration_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class WebIntegrationTest:
    """Web界面集成测试类"""
    
    def __init__(self):
        from qlib_trading_system.web.app import app
        self.client = TestClient(app)
        self.test_results = []
        self.session_cookies = None
    
    def test_user_authentication_flow(self):
        """测试完整的用户认证流程"""
        logger.info("测试用户认证流程...")
        
        try:
            # 1. 测试登录页面访问
            response = self.client.get("/login")
            if response.status_code != 200:
                self.test_results.append(("用户认证流程", "FAIL", "登录页面访问失败"))
                return False
            
            # 2. 测试用户登录
            login_data = {
                'username': 'admin',
                'password': 'admin123'
            }
            response = self.client.post("/auth/login", data=login_data)
            if response.status_code != 200:
                self.test_results.append(("用户认证流程", "FAIL", "用户登录失败"))
                return False
            
            result = response.json()
            if 'user' not in result or result['user']['username'] != 'admin':
                self.test_results.append(("用户认证流程", "FAIL", "登录返回数据错误"))
                return False
            
            # 保存会话cookies
            self.session_cookies = response.cookies
            
            # 3. 测试获取用户信息
            response = self.client.get("/auth/me", cookies=self.session_cookies)
            if response.status_code != 200:
                self.test_results.append(("用户认证流程", "FAIL", "获取用户信息失败"))
                return False
            
            # 4. 测试权限验证
            response = self.client.get("/auth/permissions", cookies=self.session_cookies)
            if response.status_code != 200:
                self.test_results.append(("用户认证流程", "FAIL", "权限验证失败"))
                return False
            
            logger.info("✓ 用户认证流程测试通过")
            self.test_results.append(("用户认证流程", "PASS", "完整认证流程正常"))
            return True
            
        except Exception as e:
            logger.error(f"✗ 用户认证流程测试异常: {e}")
            self.test_results.append(("用户认证流程", "ERROR", str(e)))
            return False
    
    def test_trading_api_endpoints(self):
        """测试交易API端点"""
        logger.info("测试交易API端点...")
        
        try:
            # 1. 测试获取交易状态
            response = self.client.get("/api/trading/status", cookies=self.session_cookies)
            if response.status_code != 200:
                self.test_results.append(("交易API端点", "FAIL", "获取交易状态失败"))
                return False
            
            # 2. 测试获取持仓信息
            response = self.client.get("/api/trading/positions", cookies=self.session_cookies)
            if response.status_code != 200:
                self.test_results.append(("交易API端点", "FAIL", "获取持仓信息失败"))
                return False
            
            # 3. 测试获取订单信息
            response = self.client.get("/api/trading/orders", cookies=self.session_cookies)
            if response.status_code != 200:
                self.test_results.append(("交易API端点", "FAIL", "获取订单信息失败"))
                return False
            
            # 4. 测试下单功能
            order_data = {
                "symbol": "000001.SZ",
                "side": "buy",
                "quantity": 100,
                "order_type": "market"
            }
            response = self.client.post("/api/trading/orders", 
                                      json=order_data, 
                                      cookies=self.session_cookies)
            if response.status_code != 200:
                self.test_results.append(("交易API端点", "FAIL", "下单功能失败"))
                return False
            
            # 5. 测试启动/停止交易
            response = self.client.post("/api/trading/start", cookies=self.session_cookies)
            if response.status_code != 200:
                self.test_results.append(("交易API端点", "FAIL", "启动交易失败"))
                return False
            
            response = self.client.post("/api/trading/s