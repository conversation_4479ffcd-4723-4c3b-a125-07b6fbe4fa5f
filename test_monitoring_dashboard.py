#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
实时监控仪表板测试
Real-time Monitoring Dashboard Test

测试监控系统的各个组件功能
"""

import asyncio
import time
import threading
import requests
import json
from datetime import datetime, timedelta
from qlib_trading_system.monitoring import (
    MetricsCollector, 
    RealTimeDashboard, 
    AlertManager,
    MobileAPI,
    DashboardManager,
    MobileAPIManager
)
from qlib_trading_system.monitoring.alert_manager import NotificationChannel
from qlib_trading_system.utils.logging.logger import get_logger

logger = get_logger(__name__)

class MonitoringDashboardTest:
    """监控仪表板测试类"""
    
    def __init__(self):
        """初始化测试"""
        self.test_config = {
            'metrics': {
                'collection_interval': 0.5,  # 测试时使用更短的间隔
                'alert_thresholds': {
                    'max_drawdown': 0.03,  # 3%
                    'daily_loss': 0.02,    # 2%
                    'system_cpu': 70.0,    # 70%
                    'system_memory': 80.0, # 80%
                    'network_latency': 80.0 # 80ms
                }
            },
            'dashboard': {
                'host': '127.0.0.1',
                'port': 8080,
                'update_interval': 0.5
            },
            'mobile_api': {
                'host': '127.0.0.1',
                'port': 8081,
                'jwt_secret': 'test-secret-key',
                'users': {
                    'test_admin': {
                        'password_hash': 'fcf730b6d95236ecd3c9fc2d92d7b6b2bb061514961aec041d6c7a7192f592e4',  # secret123
                        'role': 'admin',
                        'permissions': ['read', 'write']
                    }
                }
            },
            'alert_manager': {
                'notification_channels': [
                    {
                        'name': 'test_webhook',
                        'type': 'webhook',
                        'config': {
                            'url': 'http://httpbin.org/post',
                            'headers': {'Content-Type': 'application/json'}
                        },
                        'enabled': True,
                        'min_level': 'WARNING'
                    }
                ],
                'suppression': {
                    'same_alert_interval': 10,  # 测试时使用更短间隔
                    'max_alerts_per_hour': 50
                }
            }
        }
        
        # 创建组件
        self.metrics_collector = None
        self.dashboard_manager = None
        self.mobile_api_manager = None
        self.alert_manager = None
        
        logger.info("监控仪表板测试初始化完成")
    
    def test_metrics_collector(self):
        """测试指标收集器"""
        logger.info("=== 测试指标收集器 ===")
        
        try:
            # 创建指标收集器
            self.metrics_collector = MetricsCollector(self.test_config['metrics'])
            
            # 测试基本功能
            logger.info("测试获取当前指标...")
            current_metrics = self.metrics_collector.get_current_metrics()
            logger.info(f"当前指标时间戳: {current_metrics.timestamp}")
            
            # 启动收集
            logger.info("启动指标收集...")
            self.metrics_collector.start_collection()
            
            # 等待收集一些数据
            time.sleep(3)
            
            # 检查历史数据
            history = self.metrics_collector.get_metrics_history(1)  # 1分钟内
            logger.info(f"收集到 {len(history)} 个历史数据点")
            
            # 检查报警
            alerts = self.metrics_collector.get_recent_alerts(1)
            logger.info(f"生成了 {len(alerts)} 个报警事件")
            
            # 获取汇总统计
            summary = self.metrics_collector.get_summary_stats()
            logger.info(f"汇总统计: {summary}")
            
            logger.info("✅ 指标收集器测试通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 指标收集器测试失败: {e}")
            return False
    
    def test_alert_manager(self):
        """测试报警管理器"""
        logger.info("=== 测试报警管理器 ===")
        
        try:
            # 创建报警管理器
            self.alert_manager = AlertManager(self.test_config['alert_manager'])
            
            # 创建测试报警事件
            from qlib_trading_system.monitoring.metrics_collector import AlertEvent
            test_alert = AlertEvent(
                timestamp=datetime.now(),
                level='WARNING',
                category='TRADING',
                message='测试报警消息',
                details={'test_value': 123.45}
            )
            
            # 处理报警
            logger.info("处理测试报警...")
            self.alert_manager.process_alert(test_alert)
            
            # 获取报警统计
            stats = self.alert_manager.get_alert_statistics(1)
            logger.info(f"报警统计: {stats}")
            
            # 测试通知渠道
            logger.info("测试通知渠道...")
            self.alert_manager.test_notification_channels()
            
            logger.info("✅ 报警管理器测试通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 报警管理器测试失败: {e}")
            return False
    
    def test_dashboard_api(self):
        """测试仪表板API"""
        logger.info("=== 测试仪表板API ===")
        
        try:
            # 等待服务器启动
            time.sleep(2)
            
            base_url = f"http://{self.test_config['dashboard']['host']}:{self.test_config['dashboard']['port']}"
            
            # 测试健康检查
            logger.info("测试健康检查...")
            response = requests.get(f"{base_url}/health", timeout=5)
            if response.status_code == 200:
                logger.info("✅ 健康检查通过")
            else:
                logger.error(f"❌ 健康检查失败: {response.status_code}")
                return False
            
            # 测试获取当前指标
            logger.info("测试获取当前指标...")
            response = requests.get(f"{base_url}/api/metrics/current", timeout=5)
            if response.status_code == 200:
                data = response.json()
                logger.info(f"✅ 获取当前指标成功: {data.get('timestamp', 'N/A')}")
            else:
                logger.error(f"❌ 获取当前指标失败: {response.status_code}")
                return False
            
            # 测试获取历史指标
            logger.info("测试获取历史指标...")
            response = requests.get(f"{base_url}/api/metrics/history?minutes=5", timeout=5)
            if response.status_code == 200:
                data = response.json()
                logger.info(f"✅ 获取历史指标成功: {len(data)} 个数据点")
            else:
                logger.error(f"❌ 获取历史指标失败: {response.status_code}")
                return False
            
            # 测试获取报警信息
            logger.info("测试获取报警信息...")
            response = requests.get(f"{base_url}/api/alerts/recent?minutes=5", timeout=5)
            if response.status_code == 200:
                data = response.json()
                logger.info(f"✅ 获取报警信息成功: {len(data)} 个报警")
            else:
                logger.error(f"❌ 获取报警信息失败: {response.status_code}")
                return False
            
            # 测试获取汇总信息
            logger.info("测试获取汇总信息...")
            response = requests.get(f"{base_url}/api/summary", timeout=5)
            if response.status_code == 200:
                data = response.json()
                logger.info(f"✅ 获取汇总信息成功")
            else:
                logger.error(f"❌ 获取汇总信息失败: {response.status_code}")
                return False
            
            logger.info("✅ 仪表板API测试通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 仪表板API测试失败: {e}")
            return False
    
    def test_mobile_api(self):
        """测试移动端API"""
        logger.info("=== 测试移动端API ===")
        
        try:
            # 等待服务器启动
            time.sleep(2)
            
            base_url = f"http://{self.test_config['mobile_api']['host']}:{self.test_config['mobile_api']['port']}"
            
            # 测试健康检查
            logger.info("测试移动端健康检查...")
            response = requests.get(f"{base_url}/api/mobile/health", timeout=5)
            if response.status_code == 200:
                logger.info("✅ 移动端健康检查通过")
            else:
                logger.error(f"❌ 移动端健康检查失败: {response.status_code}")
                return False
            
            # 测试用户登录
            logger.info("测试用户登录...")
            login_data = {
                "username": "test_admin",
                "password": "secret123"
            }
            response = requests.post(f"{base_url}/api/mobile/auth/login", json=login_data, timeout=5)
            if response.status_code == 200:
                login_result = response.json()
                access_token = login_result['access_token']
                logger.info("✅ 用户登录成功")
            else:
                logger.error(f"❌ 用户登录失败: {response.status_code}")
                return False
            
            # 设置认证头
            headers = {'Authorization': f'Bearer {access_token}'}
            
            # 测试获取当前指标
            logger.info("测试移动端获取当前指标...")
            response = requests.get(f"{base_url}/api/mobile/metrics/current", headers=headers, timeout=5)
            if response.status_code == 200:
                data = response.json()
                logger.info(f"✅ 移动端获取当前指标成功")
            else:
                logger.error(f"❌ 移动端获取当前指标失败: {response.status_code}")
                return False
            
            # 测试获取汇总信息
            logger.info("测试移动端获取汇总信息...")
            response = requests.get(f"{base_url}/api/mobile/summary", headers=headers, timeout=5)
            if response.status_code == 200:
                data = response.json()
                logger.info(f"✅ 移动端获取汇总信息成功: 状态={data.get('status', 'N/A')}")
            else:
                logger.error(f"❌ 移动端获取汇总信息失败: {response.status_code}")
                return False
            
            # 测试获取报警信息
            logger.info("测试移动端获取报警信息...")
            response = requests.get(f"{base_url}/api/mobile/alerts", headers=headers, timeout=5)
            if response.status_code == 200:
                data = response.json()
                logger.info(f"✅ 移动端获取报警信息成功: {data.get('total_count', 0)} 个报警")
            else:
                logger.error(f"❌ 移动端获取报警信息失败: {response.status_code}")
                return False
            
            logger.info("✅ 移动端API测试通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 移动端API测试失败: {e}")
            return False
    
    def test_websocket_connection(self):
        """测试WebSocket连接"""
        logger.info("=== 测试WebSocket连接 ===")
        
        try:
            import websocket
            
            # WebSocket连接测试
            ws_url = f"ws://{self.test_config['dashboard']['host']}:{self.test_config['dashboard']['port']}/ws"
            
            received_messages = []
            
            def on_message(ws, message):
                received_messages.append(message)
                logger.info(f"收到WebSocket消息: {len(message)} 字符")
            
            def on_error(ws, error):
                logger.error(f"WebSocket错误: {error}")
            
            def on_close(ws, close_status_code, close_msg):
                logger.info("WebSocket连接已关闭")
            
            def on_open(ws):
                logger.info("WebSocket连接已建立")
                # 发送测试消息
                ws.send("test message")
            
            # 创建WebSocket连接
            ws = websocket.WebSocketApp(
                ws_url,
                on_open=on_open,
                on_message=on_message,
                on_error=on_error,
                on_close=on_close
            )
            
            # 在单独线程中运行WebSocket
            ws_thread = threading.Thread(target=ws.run_forever)
            ws_thread.daemon = True
            ws_thread.start()
            
            # 等待接收消息
            time.sleep(5)
            
            # 关闭连接
            ws.close()
            
            if len(received_messages) > 0:
                logger.info(f"✅ WebSocket测试通过，收到 {len(received_messages)} 条消息")
                return True
            else:
                logger.warning("⚠️ WebSocket连接成功但未收到消息")
                return True  # 连接成功就算通过
            
        except ImportError:
            logger.warning("⚠️ websocket-client未安装，跳过WebSocket测试")
            return True
        except Exception as e:
            logger.error(f"❌ WebSocket测试失败: {e}")
            return False
    
    def run_dashboard_server(self):
        """运行仪表板服务器"""
        try:
            self.dashboard_manager = DashboardManager(self.test_config)
            self.dashboard_manager.start()
        except Exception as e:
            logger.error(f"仪表板服务器启动失败: {e}")
    
    def run_mobile_api_server(self):
        """运行移动端API服务器"""
        try:
            self.mobile_api_manager = MobileAPIManager(
                self.metrics_collector, 
                self.test_config['mobile_api']
            )
            self.mobile_api_manager.start_server()
        except Exception as e:
            logger.error(f"移动端API服务器启动失败: {e}")
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        logger.info("🚀 开始监控仪表板综合测试")
        
        test_results = []
        
        try:
            # 1. 测试指标收集器
            result = self.test_metrics_collector()
            test_results.append(("指标收集器", result))
            
            if not result:
                logger.error("指标收集器测试失败，停止后续测试")
                return False
            
            # 2. 测试报警管理器
            result = self.test_alert_manager()
            test_results.append(("报警管理器", result))
            
            # 3. 启动仪表板服务器（在单独线程中）
            logger.info("启动仪表板服务器...")
            dashboard_thread = threading.Thread(target=self.run_dashboard_server)
            dashboard_thread.daemon = True
            dashboard_thread.start()
            
            # 4. 启动移动端API服务器（在单独线程中）
            logger.info("启动移动端API服务器...")
            mobile_api_thread = threading.Thread(target=self.run_mobile_api_server)
            mobile_api_thread.daemon = True
            mobile_api_thread.start()
            
            # 等待服务器启动
            time.sleep(5)
            
            # 5. 测试仪表板API
            result = self.test_dashboard_api()
            test_results.append(("仪表板API", result))
            
            # 6. 测试移动端API
            result = self.test_mobile_api()
            test_results.append(("移动端API", result))
            
            # 7. 测试WebSocket连接
            result = self.test_websocket_connection()
            test_results.append(("WebSocket连接", result))
            
            # 输出测试结果
            logger.info("\n" + "="*50)
            logger.info("📊 测试结果汇总")
            logger.info("="*50)
            
            passed = 0
            total = len(test_results)
            
            for test_name, result in test_results:
                status = "✅ 通过" if result else "❌ 失败"
                logger.info(f"{test_name}: {status}")
                if result:
                    passed += 1
            
            logger.info("="*50)
            logger.info(f"总计: {passed}/{total} 个测试通过")
            
            if passed == total:
                logger.info("🎉 所有测试通过！监控仪表板系统运行正常")
                
                # 保持服务器运行一段时间供手动测试
                logger.info("服务器将继续运行60秒供手动测试...")
                logger.info(f"仪表板地址: http://{self.test_config['dashboard']['host']}:{self.test_config['dashboard']['port']}")
                logger.info(f"移动端API地址: http://{self.test_config['mobile_api']['host']}:{self.test_config['mobile_api']['port']}")
                time.sleep(60)
                
                return True
            else:
                logger.error(f"❌ {total - passed} 个测试失败")
                return False
                
        except Exception as e:
            logger.error(f"综合测试过程中发生错误: {e}")
            return False
        
        finally:
            # 清理资源
            self.cleanup()
    
    def cleanup(self):
        """清理测试资源"""
        logger.info("清理测试资源...")
        
        try:
            if self.metrics_collector:
                self.metrics_collector.stop_collection()
            
            if self.dashboard_manager:
                self.dashboard_manager.stop()
                
        except Exception as e:
            logger.error(f"清理资源时发生错误: {e}")

def main():
    """主函数"""
    print("="*60)
    print("🚀 Qlib交易系统 - 实时监控仪表板测试")
    print("="*60)
    
    # 创建测试实例
    test = MonitoringDashboardTest()
    
    try:
        # 运行综合测试
        success = test.run_comprehensive_test()
        
        if success:
            print("\n✅ 监控仪表板测试完成 - 所有功能正常")
            return 0
        else:
            print("\n❌ 监控仪表板测试失败 - 存在问题需要修复")
            return 1
            
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        return 1
    except Exception as e:
        print(f"\n❌ 测试过程中发生未预期错误: {e}")
        return 1
    finally:
        test.cleanup()

if __name__ == "__main__":
    exit(main())