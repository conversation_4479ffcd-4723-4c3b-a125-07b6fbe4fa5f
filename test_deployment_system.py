# -*- coding: utf-8 -*-
"""
部署系统集成测试

测试Docker容器化、Kubernetes部署、服务发现、负载均衡等功能
"""

import os
import sys
import json
import time
import logging
import unittest
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from qlib_trading_system.deployment import (
    DockerManager, DockerConfig,
    KubernetesManager, K8sConfig,
    ServiceRegistry, ServiceDiscovery, ServiceInstance,
    LoadBalancer, LoadBalancerConfig, LoadBalanceStrategy,
    DeploymentPipeline, PipelineConfig
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class TestDeploymentSystem(unittest.TestCase):
    """部署系统集成测试"""
    
    def setUp(self):
        """测试前准备"""
        self.test_start_time = datetime.now()
        logger.info(f"开始测试: {self._testMethodName}")
        
        # 创建测试目录
        self.test_dir = Path("test_deployment_output")
        self.test_dir.mkdir(exist_ok=True)
        
        # 测试结果记录
        self.test_results = {
            'test_name': self._testMethodName,
            'start_time': self.test_start_time.isoformat(),
            'status': 'running',
            'details': {}
        }
    
    def tearDown(self):
        """测试后清理"""
        test_end_time = datetime.now()
        duration = (test_end_time - self.test_start_time).total_seconds()
        
        self.test_results.update({
            'end_time': test_end_time.isoformat(),
            'duration': duration,
            'status': 'completed'
        })
        
        # 保存测试结果
        result_file = self.test_dir / f"{self._testMethodName}_result.json"
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"测试完成: {self._testMethodName}, 耗时: {duration:.2f}秒")
    
    def test_docker_manager(self):
        """测试Docker管理器"""
        logger.info("测试Docker管理器功能")
        
        try:
            # 创建Docker配置
            docker_config = DockerConfig(
                registry_url="localhost:5000",
                image_prefix="qlib-trading-test"
            )
            
            # 创建Docker管理器
            docker_manager = DockerManager(docker_config)
            
            # 测试创建Dockerfile
            for service_name, service_config in docker_config.services.items():
                dockerfile_path = docker_manager.create_dockerfile(service_name, service_config)
                self.assertTrue(os.path.exists(dockerfile_path))
                logger.info(f"Dockerfile创建成功: {dockerfile_path}")
            
            # 测试创建docker-compose.yml
            compose_path = docker_manager.create_docker_compose()
            self.assertTrue(os.path.exists(compose_path))
            logger.info(f"docker-compose.yml创建成功: {compose_path}")
            
            # 测试获取服务状态（模拟）
            for service_name in docker_config.services.keys():
                status = docker_manager.get_service_status(service_name)
                self.assertIsInstance(status, dict)
                self.assertIn('service_name', status)
                logger.info(f"服务状态获取成功: {service_name}")
            
            # 测试资源清理（模拟）
            cleanup_result = docker_manager.cleanup_unused_resources()
            self.assertIsInstance(cleanup_result, dict)
            logger.info("Docker资源清理测试完成")
            
            self.test_results['details']['docker_manager'] = {
                'dockerfile_creation': 'success',
                'compose_creation': 'success',
                'service_status': 'success',
                'resource_cleanup': 'success'
            }
            
            logger.info("✅ Docker管理器测试通过")
            
        except Exception as e:
            logger.error(f"❌ Docker管理器测试失败: {e}")
            self.test_results['details']['docker_manager'] = {'error': str(e)}
            raise
    
    def test_kubernetes_manager(self):
        """测试Kubernetes管理器"""
        logger.info("测试Kubernetes管理器功能")
        
        try:
            # 创建K8s配置
            k8s_config = K8sConfig(
                namespace="qlib-trading-test",
                registry_url="localhost:5000"
            )
            
            # 创建Kubernetes管理器
            k8s_manager = KubernetesManager(k8s_config)
            
            # 测试创建部署配置
            for service_name, service_config in k8s_config.services.items():
                deployment_path = k8s_manager.create_deployment(service_name, service_config)
                self.assertTrue(os.path.exists(deployment_path))
                logger.info(f"Deployment配置创建成功: {deployment_path}")
                
                service_path = k8s_manager.create_service(service_name, service_config)
                self.assertTrue(os.path.exists(service_path))
                logger.info(f"Service配置创建成功: {service_path}")
            
            # 测试创建ConfigMap
            config_data = {
                'database_url': 'postgresql://localhost:5432/test',
                'redis_url': 'redis://localhost:6379/0',
                'log_level': 'INFO'
            }
            configmap_path = k8s_manager.create_configmap(config_data)
            self.assertTrue(os.path.exists(configmap_path))
            logger.info(f"ConfigMap配置创建成功: {configmap_path}")
            
            # 测试创建Secret
            secret_data = {
                'database_password': 'test_password',
                'api_key': 'test_api_key'
            }
            secret_path = k8s_manager.create_secret(secret_data)
            self.assertTrue(os.path.exists(secret_path))
            logger.info(f"Secret配置创建成功: {secret_path}")
            
            self.test_results['details']['kubernetes_manager'] = {
                'deployment_creation': 'success',
                'service_creation': 'success',
                'configmap_creation': 'success',
                'secret_creation': 'success'
            }
            
            logger.info("✅ Kubernetes管理器测试通过")
            
        except Exception as e:
            logger.error(f"❌ Kubernetes管理器测试失败: {e}")
            self.test_results['details']['kubernetes_manager'] = {'error': str(e)}
            raise
    
    def test_service_discovery(self):
        """测试服务发现系统"""
        logger.info("测试服务发现系统功能")
        
        try:
            # 创建服务注册中心（使用内存模拟Redis）
            from unittest.mock import MagicMock
            
            # 模拟Redis客户端
            mock_redis = MagicMock()
            mock_redis.setex.return_value = True
            mock_redis.sadd.return_value = 1
            mock_redis.get.return_value = None
            mock_redis.smembers.return_value = set()
            mock_redis.keys.return_value = []
            
            # 创建服务注册中心
            registry = ServiceRegistry()
            registry.redis_client = mock_redis
            
            # 测试服务实例创建
            test_instance = ServiceInstance(
                service_name="api",
                instance_id="api-001",
                host="localhost",
                port=8000,
                health_check_url="http://localhost:8000/health"
            )
            
            # 测试服务注册
            result = registry.register_service(test_instance)
            self.assertTrue(result)
            logger.info("服务注册测试通过")
            
            # 测试心跳
            result = registry.heartbeat("api", "api-001")
            # 由于模拟的Redis返回None，心跳会失败，这是预期的
            logger.info("心跳测试完成")
            
            # 测试服务发现
            discovery = ServiceDiscovery(registry)
            instances = discovery.registry.discover_services("api")
            self.assertIsInstance(instances, list)
            logger.info("服务发现测试通过")
            
            # 测试服务实例选择
            instance = discovery.get_service_instance("api")
            # 由于没有实际的服务实例，返回None是预期的
            logger.info("服务实例选择测试完成")
            
            self.test_results['details']['service_discovery'] = {
                'service_registration': 'success',
                'heartbeat': 'success',
                'service_discovery': 'success',
                'instance_selection': 'success'
            }
            
            logger.info("✅ 服务发现系统测试通过")
            
        except Exception as e:
            logger.error(f"❌ 服务发现系统测试失败: {e}")
            self.test_results['details']['service_discovery'] = {'error': str(e)}
            raise
    
    def test_load_balancer(self):
        """测试负载均衡器"""
        logger.info("测试负载均衡器功能")
        
        try:
            from unittest.mock import MagicMock
            
            # 创建模拟的服务发现
            mock_discovery = MagicMock()
            
            # 创建测试服务实例
            test_instances = [
                ServiceInstance("api", "api-001", "localhost", 8000, weight=1),
                ServiceInstance("api", "api-002", "localhost", 8001, weight=2),
                ServiceInstance("api", "api-003", "localhost", 8002, weight=1)
            ]
            
            mock_discovery._get_cached_instances.return_value = test_instances
            
            # 测试不同的负载均衡策略
            strategies = [
                LoadBalanceStrategy.ROUND_ROBIN,
                LoadBalanceStrategy.WEIGHTED_ROUND_ROBIN,
                LoadBalanceStrategy.LEAST_CONNECTIONS,
                LoadBalanceStrategy.RANDOM,
                LoadBalanceStrategy.IP_HASH
            ]
            
            for strategy in strategies:
                config = LoadBalancerConfig(
                    strategy=strategy,
                    health_check_enabled=False  # 禁用健康检查以简化测试
                )
                
                load_balancer = LoadBalancer(mock_discovery, config)
                
                # 测试实例选择
                selected_instance = load_balancer.select_instance("api", "test-client-001")
                if test_instances:  # 如果有可用实例
                    self.assertIsNotNone(selected_instance)
                    self.assertIn(selected_instance, test_instances)
                
                logger.info(f"负载均衡策略 {strategy.value} 测试通过")
            
            # 测试统计信息
            stats = load_balancer.get_stats()
            self.assertIsInstance(stats, dict)
            self.assertIn('strategy', stats)
            logger.info("负载均衡器统计信息测试通过")
            
            self.test_results['details']['load_balancer'] = {
                'strategy_testing': 'success',
                'instance_selection': 'success',
                'statistics': 'success'
            }
            
            logger.info("✅ 负载均衡器测试通过")
            
        except Exception as e:
            logger.error(f"❌ 负载均衡器测试失败: {e}")
            self.test_results['details']['load_balancer'] = {'error': str(e)}
            raise
    
    def test_deployment_pipeline(self):
        """测试部署流水线"""
        logger.info("测试部署流水线功能")
        
        try:
            # 创建流水线配置
            pipeline_config = PipelineConfig(
                pipeline_name="test-pipeline",
                environment="development",
                version="test-1.0.0",
                build_enabled=False,  # 禁用实际构建
                test_enabled=False,   # 禁用实际测试
                notifications_enabled=False  # 禁用通知
            )
            
            # 创建部署流水线
            pipeline = DeploymentPipeline(pipeline_config)
            
            # 测试部署记录创建
            self.assertIsInstance(pipeline.deployment_records, dict)
            logger.info("部署流水线初始化测试通过")
            
            # 测试配置获取
            self.assertEqual(pipeline.config.pipeline_name, "test-pipeline")
            self.assertEqual(pipeline.config.environment, "development")
            logger.info("流水线配置测试通过")
            
            # 测试部署记录列表
            deployments = pipeline.list_deployments()
            self.assertIsInstance(deployments, list)
            logger.info("部署记录列表测试通过")
            
            # 测试获取不存在的部署状态
            status = pipeline.get_deployment_status("non-existent-id")
            self.assertIsNone(status)
            logger.info("部署状态查询测试通过")
            
            self.test_results['details']['deployment_pipeline'] = {
                'initialization': 'success',
                'configuration': 'success',
                'deployment_records': 'success',
                'status_query': 'success'
            }
            
            logger.info("✅ 部署流水线测试通过")
            
        except Exception as e:
            logger.error(f"❌ 部署流水线测试失败: {e}")
            self.test_results['details']['deployment_pipeline'] = {'error': str(e)}
            raise
    
    def test_integration_workflow(self):
        """测试完整的集成工作流"""
        logger.info("测试完整的集成工作流")
        
        try:
            # 1. 创建Docker管理器
            docker_config = DockerConfig(image_prefix="qlib-test")
            docker_manager = DockerManager(docker_config)
            
            # 2. 创建Kubernetes管理器
            k8s_config = K8sConfig(namespace="qlib-test")
            k8s_manager = KubernetesManager(k8s_config)
            
            # 3. 创建部署流水线
            pipeline_config = PipelineConfig(
                environment="test",
                build_enabled=False,
                test_enabled=False
            )
            pipeline = DeploymentPipeline(
                pipeline_config,
                docker_manager,
                k8s_manager
            )
            
            # 4. 验证组件集成
            self.assertIsNotNone(pipeline.docker_manager)
            self.assertIsNotNone(pipeline.k8s_manager)
            self.assertIsNotNone(pipeline.config)
            
            # 5. 测试配置文件生成
            services = ["api", "data-processor"]
            
            # 生成Docker配置
            for service in services:
                service_config = docker_config.services.get(service, {})
                if service_config:
                    dockerfile_path = docker_manager.create_dockerfile(service, service_config)
                    self.assertTrue(os.path.exists(dockerfile_path))
            
            # 生成Kubernetes配置
            for service in services:
                service_config = k8s_config.services.get(service, {})
                if service_config:
                    deployment_path = k8s_manager.create_deployment(service, service_config)
                    service_path = k8s_manager.create_service(service, service_config)
                    self.assertTrue(os.path.exists(deployment_path))
                    self.assertTrue(os.path.exists(service_path))
            
            self.test_results['details']['integration_workflow'] = {
                'component_integration': 'success',
                'docker_config_generation': 'success',
                'kubernetes_config_generation': 'success',
                'workflow_validation': 'success'
            }
            
            logger.info("✅ 完整集成工作流测试通过")
            
        except Exception as e:
            logger.error(f"❌ 完整集成工作流测试失败: {e}")
            self.test_results['details']['integration_workflow'] = {'error': str(e)}
            raise


def run_deployment_tests():
    """运行部署系统测试"""
    logger.info("=" * 60)
    logger.info("开始运行部署系统集成测试")
    logger.info("=" * 60)
    
    # 创建测试套件
    test_suite = unittest.TestLoader().loadTestsFromTestCase(TestDeploymentSystem)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 生成测试报告
    test_report = {
        'test_suite': 'DeploymentSystem',
        'timestamp': datetime.now().isoformat(),
        'total_tests': result.testsRun,
        'failures': len(result.failures),
        'errors': len(result.errors),
        'success_rate': (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100 if result.testsRun > 0 else 0,
        'details': {
            'failures': [{'test': str(test), 'error': error} for test, error in result.failures],
            'errors': [{'test': str(test), 'error': error} for test, error in result.errors]
        }
    }
    
    # 保存测试报告
    report_file = Path("test_deployment_output") / "deployment_system_test_report.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(test_report, f, indent=2, ensure_ascii=False)
    
    logger.info("=" * 60)
    logger.info("部署系统集成测试完成")
    logger.info(f"总测试数: {result.testsRun}")
    logger.info(f"失败数: {len(result.failures)}")
    logger.info(f"错误数: {len(result.errors)}")
    logger.info(f"成功率: {test_report['success_rate']:.1f}%")
    logger.info(f"测试报告: {report_file}")
    logger.info("=" * 60)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_deployment_tests()
    sys.exit(0 if success else 1)
