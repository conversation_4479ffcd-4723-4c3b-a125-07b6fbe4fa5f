"""
文档页面测试
Documentation Page Test

测试自定义文档页面是否正常工作
"""

import asyncio
import httpx
import logging
from datetime import datetime

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_docs_page():
    """测试文档页面"""
    logger.info("测试文档页面")
    
    try:
        async with httpx.AsyncClient() as client:
            # 测试文档页面
            response = await client.get("http://localhost:8000/docs")
            
            assert response.status_code == 200, f"文档页面访问失败: {response.status_code}"
            
            content = response.text
            
            # 验证页面内容
            assert "Qlib Trading System API" in content, "页面标题不正确"
            assert "量化交易系统高性能异步API接口" in content, "页面描述不正确"
            assert "/health" in content, "缺少健康检查端点"
            assert "/api/v1/auth/login" in content, "缺少登录端点"
            assert "WebSocket" in content, "缺少WebSocket信息"
            assert "FastAPI + Uvicorn + WebSocket + RabbitMQ" in content, "缺少架构信息"
            
            logger.info("✅ 文档页面测试通过")
            return True
            
    except Exception as e:
        logger.error(f"❌ 文档页面测试失败: {e}")
        return False


async def test_openapi_spec():
    """测试OpenAPI规范"""
    logger.info("测试OpenAPI规范")
    
    try:
        async with httpx.AsyncClient() as client:
            # 测试OpenAPI规范
            response = await client.get("http://localhost:8000/openapi.json")
            
            assert response.status_code == 200, f"OpenAPI规范访问失败: {response.status_code}"
            
            spec = response.json()
            
            # 验证OpenAPI规范
            assert "openapi" in spec, "缺少OpenAPI版本信息"
            assert "info" in spec, "缺少API信息"
            assert "paths" in spec, "缺少路径信息"
            assert spec["info"]["title"] == "Qlib Trading System API", "API标题不正确"
            assert spec["info"]["version"] == "2.0.0", "API版本不正确"
            
            # 检查关键端点是否存在
            paths = spec["paths"]
            assert "/health" in paths, "缺少健康检查端点"
            assert "/api/v1/auth/login" in paths, "缺少登录端点"
            
            logger.info("✅ OpenAPI规范测试通过")
            return True
            
    except Exception as e:
        logger.error(f"❌ OpenAPI规范测试失败: {e}")
        return False


async def main():
    """主函数"""
    logger.info("开始文档页面测试")
    
    # 测试文档页面
    docs_result = await test_docs_page()
    
    # 测试OpenAPI规范
    openapi_result = await test_openapi_spec()
    
    # 生成测试报告
    report = {
        "docs_page_test": {
            "passed": docs_result,
            "timestamp": datetime.now().isoformat()
        },
        "openapi_spec_test": {
            "passed": openapi_result,
            "timestamp": datetime.now().isoformat()
        },
        "overall_result": {
            "all_passed": docs_result and openapi_result,
            "pass_rate": f"{(int(docs_result) + int(openapi_result)) / 2 * 100:.1f}%"
        }
    }
    
    with open("docs_page_test_report.json", 'w', encoding='utf-8') as f:
        import json
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    logger.info("=" * 50)
    logger.info("文档页面测试报告")
    logger.info("=" * 50)
    logger.info(f"文档页面测试: {'通过' if docs_result else '失败'}")
    logger.info(f"OpenAPI规范测试: {'通过' if openapi_result else '失败'}")
    logger.info(f"总体通过率: {report['overall_result']['pass_rate']}")
    logger.info("=" * 50)
    
    if docs_result and openapi_result:
        logger.info("🎉 所有文档相关测试都通过了！")
        logger.info("现在您可以访问 http://localhost:8000/docs 查看API文档")
    else:
        logger.error("❌ 部分测试失败，请检查服务器状态")


if __name__ == "__main__":
    asyncio.run(main())
