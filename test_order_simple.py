#!/usr/bin/env python3
"""
简化的订单管理系统测试
"""

import sys
import asyncio
import logging
from datetime import datetime, timedelta

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

# 导入订单管理系统组件
from qlib_trading_system.trading.orders.manager import OrderManager, OrderRequest
from qlib_trading_system.trading.orders.models import OrderType, OrderSide, OrderPriority, AlgoOrderConfig
from qlib_trading_system.trading.orders.validator import RiskLimits


class MockBrokerAdapter:
    """模拟券商接口"""
    
    def __init__(self):
        self.account_info = {
            'available_cash': 100000.0,
            'total_value': 500000.0,
            'positions': {'000001': 1000, '600000': 2000}
        }
        self.order_counter = 0
    
    async def get_account_info(self):
        return self.account_info.copy()
    
    async def place_market_order(self, symbol: str, quantity: int, side: str):
        await asyncio.sleep(0.1)  # 模拟延迟
        self.order_counter += 1
        return {
            'success': True,
            'broker_order_id': f'BROKER_{self.order_counter}',
            'message': '订单已提交'
        }
    
    async def place_limit_order(self, symbol: str, quantity: int, price: float, side: str):
        # 对于测试取消功能的订单，增加延迟
        if '测试订单取消' in str(side):
            await asyncio.sleep(2.0)  # 增加延迟用于测试取消
        else:
            await asyncio.sleep(0.1)  # 正常延迟
        self.order_counter += 1
        return {
            'success': True,
            'broker_order_id': f'BROKER_{self.order_counter}',
            'message': '订单已提交'
        }


async def mock_market_data_provider(symbol: str):
    """模拟市场数据提供者"""
    prices = {
        '000001': 10.50,
        '600000': 15.80,
        '000002': 8.30,
        '600036': 25.60
    }
    
    base_price = prices.get(symbol, 10.0)
    
    return {
        'symbol': symbol,
        'price': base_price,
        'volume': 1000000,
        'bid_price': base_price - 0.01,
        'ask_price': base_price + 0.01,
        'bid_volume': 10000,
        'ask_volume': 10000,
        'timestamp': datetime.now()
    }


async def test_basic_order_management():
    """测试基本订单管理功能"""
    logger.info("=" * 60)
    logger.info("开始订单管理系统基本功能测试")
    logger.info("=" * 60)
    
    # 初始化组件
    broker_adapter = MockBrokerAdapter()
    
    risk_limits = RiskLimits(
        max_position_value=50000.0,
        max_single_position_pct=0.2,
        max_daily_trades=50,
        max_order_value=20000.0,
        min_order_value=1000.0
    )
    
    # 创建订单管理器
    order_manager = OrderManager(
        broker_adapter=broker_adapter,
        market_data_provider=mock_market_data_provider,
        risk_limits=risk_limits
    )
    
    # 启用测试模式，绕过交易时间检查
    order_manager.validator.test_mode = True
    
    # 启动订单管理器
    order_manager.start()
    
    try:
        # 测试1: 基本市价单
        logger.info("\n测试1: 提交市价买入订单")
        request = OrderRequest(
            symbol='000001',
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=1000,
            reason='测试市价买入'
        )
        
        success, message, order_id = await order_manager.submit_order(request)
        
        if success:
            logger.info(f"✓ 订单提交成功: {order_id}")
            
            # 等待处理
            await asyncio.sleep(2)
            
            # 检查订单状态
            order = order_manager.get_order(order_id)
            if order:
                logger.info(f"  订单状态: {order.status.value}")
                logger.info(f"  成交数量: {order.filled_quantity}/{order.quantity}")
                logger.info(f"  平均成交价: {order.avg_fill_price:.2f}")
            else:
                logger.error("  未找到订单")
        else:
            logger.error(f"✗ 订单提交失败: {message}")
        
        # 测试2: 限价单
        logger.info("\n测试2: 提交限价卖出订单")
        request = OrderRequest(
            symbol='600000',
            side=OrderSide.SELL,
            order_type=OrderType.LIMIT,
            quantity=500,
            price=15.90,
            reason='测试限价卖出'
        )
        
        success, message, order_id = await order_manager.submit_order(request)
        
        if success:
            logger.info(f"✓ 限价单提交成功: {order_id}")
            
            # 等待处理
            await asyncio.sleep(2)
            
            # 检查订单状态
            order = order_manager.get_order(order_id)
            if order:
                logger.info(f"  订单状态: {order.status.value}")
                logger.info(f"  成交数量: {order.filled_quantity}/{order.quantity}")
        else:
            logger.error(f"✗ 限价单提交失败: {message}")
        
        # 测试3: TWAP算法订单
        logger.info("\n测试3: 提交TWAP算法订单")
        start_time = datetime.now() + timedelta(seconds=2)
        end_time = start_time + timedelta(seconds=8)
        
        algo_config = AlgoOrderConfig(
            algo_type='TWAP',
            start_time=start_time,
            end_time=end_time,
            participation_rate=0.2,
            max_slice_size=500,
            min_slice_size=100,
            urgency=0.5
        )
        
        request = OrderRequest(
            symbol='000001',
            side=OrderSide.BUY,
            order_type=OrderType.TWAP,
            quantity=1500,
            algo_config=algo_config,
            reason='测试TWAP算法'
        )
        
        success, message, order_id = await order_manager.submit_order(request)
        
        if success:
            logger.info(f"✓ TWAP订单提交成功: {order_id}")
            
            # 等待算法执行完成
            await asyncio.sleep(12)
            
            # 检查执行结果
            order = order_manager.get_order(order_id)
            if order:
                logger.info(f"  TWAP执行完成: {order.status.value}")
                logger.info(f"  成交数量: {order.filled_quantity}/{order.quantity}")
                logger.info(f"  成交比例: {order.fill_ratio:.2%}")
        else:
            logger.error(f"✗ TWAP订单提交失败: {message}")
        
        # 测试4: 风险检查
        logger.info("\n测试4: 测试风险检查功能")
        
        # 提交一个资金不足的订单
        request = OrderRequest(
            symbol='000001',
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=20000,  # 超大数量
            reason='测试资金不足检查'
        )
        
        success, message, order_id = await order_manager.submit_order(request)
        
        if not success:
            logger.info(f"✓ 风险检查正确拒绝: {message}")
        else:
            logger.warning(f"✗ 风险检查未生效，订单意外通过: {order_id}")
        
        # 测试5: 订单取消
        logger.info("\n测试5: 测试订单取消功能")
        
        request = OrderRequest(
            symbol='000002',
            side=OrderSide.BUY,
            order_type=OrderType.LIMIT,
            quantity=800,
            price=8.50,
            reason='测试订单取消'
        )
        
        success, message, order_id = await order_manager.submit_order(request)
        
        if success:
            logger.info(f"✓ 订单提交成功: {order_id}")
            
            # 等待一小段时间
            await asyncio.sleep(0.5)
            
            # 取消订单
            cancel_success = order_manager.cancel_order(order_id, "测试取消")
            
            if cancel_success:
                logger.info(f"✓ 订单取消成功")
                
                # 检查状态
                order = order_manager.get_order(order_id)
                if order:
                    logger.info(f"  订单状态: {order.status.value}")
            else:
                logger.error(f"✗ 订单取消失败")
        
        # 测试6: 监控和报告
        logger.info("\n测试6: 测试监控和报告功能")
        
        # 获取执行统计
        stats = order_manager.get_execution_stats()
        logger.info(f"执行统计:")
        for key, value in stats.items():
            logger.info(f"  {key}: {value}")
        
        # 生成日度报告
        today = datetime.now().strftime('%Y-%m-%d')
        report = order_manager.generate_daily_report(today)
        
        logger.info(f"日度报告:")
        logger.info(f"  日期: {report.date}")
        logger.info(f"  总订单数: {report.total_orders}")
        logger.info(f"  完成订单数: {report.completed_orders}")
        logger.info(f"  总成交量: {report.total_volume}")
        logger.info(f"  总成交额: {report.total_value:.2f}")
        
        # 导出报告
        json_file = order_manager.export_report(report, 'json')
        logger.info(f"✓ 报告导出成功: {json_file}")
        
        logger.info("\n" + "=" * 60)
        logger.info("所有基本功能测试完成")
        logger.info("=" * 60)
        
    except Exception as e:
        logger.error(f"测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 停止订单管理器
        order_manager.stop()


if __name__ == "__main__":
    asyncio.run(test_basic_order_management())