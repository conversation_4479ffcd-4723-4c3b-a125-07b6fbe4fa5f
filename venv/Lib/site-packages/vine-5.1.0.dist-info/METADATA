Metadata-Version: 2.1
Name: vine
Version: 5.1.0
Summary: Python promises.
Home-page: https://github.com/celery/vine
Author: Ask Solem
Author-email: <EMAIL>
License: BSD
Keywords: promise promises lazy future futures
Platform: any
Classifier: Development Status :: 5 - Production/Stable
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: License :: OSI Approved :: BSD License
Classifier: Intended Audience :: Developers
Classifier: Operating System :: OS Independent
Requires-Python: >=3.6
License-File: LICENSE

=====================================================================
 vine - Python Promises
=====================================================================

|build-status| |coverage| |license| |wheel| |pyversion| |pyimp|

:Version: 5.1.0
:Web: https://vine.readthedocs.io/
:Download: https://pypi.org/project/vine/
:Source: https://github.com/celery/vine/
:Keywords: promise, async, future

About
=====

This is a special implementation of promises in that it can be used both
for promise of a value and lazy evaluation. The biggest upside for this
is that everything in a promise can also be a promise, e.g. filters,
callbacks and errbacks can all be promises.

.. |build-status| image:: https://secure.travis-ci.org/celery/vine.png?branch=master
    :alt: Build status
    :target: https://travis-ci.org/celery/vine

.. |coverage| image:: https://codecov.io/github/celery/vine/coverage.svg?branch=master
    :target: https://codecov.io/github/celery/vine?branch=master

.. |license| image:: https://img.shields.io/pypi/l/vine.svg
    :alt: BSD License
    :target: https://opensource.org/licenses/BSD-3-Clause

.. |wheel| image:: https://img.shields.io/pypi/wheel/vine.svg
    :alt: Vine can be installed via wheel
    :target: https://pypi.org/project/vine/

.. |pyversion| image:: https://img.shields.io/pypi/pyversions/vine.svg
    :alt: Supported Python versions.
    :target: https://pypi.org/project/vine/

.. |pyimp| image:: https://img.shields.io/pypi/implementation/vine.svg
    :alt: Support Python implementations.
    :target: https://pypi.org/project/vine/



