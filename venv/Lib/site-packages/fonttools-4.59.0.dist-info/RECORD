../../Scripts/fonttools.exe,sha256=334RgCMyMRHlM1mAlA2hU38IOHBOJbXC6JPF57ZA28I,108396
../../Scripts/pyftmerge.exe,sha256=sFKeBimylP_FPnBMU0sr3v4eDmQ8WdJJRTAyrPCg5bM,108393
../../Scripts/pyftsubset.exe,sha256=PJOp6arVm1EZ9ytU2uyqpuOIwAlFVJcQSWke5XFhFqs,108394
../../Scripts/ttx.exe,sha256=EeE-b_Cpal2lZLPNB4kFnuAi8H_DFtalTsHUMg3XwsU,108391
../../share/man/man1/ttx.1,sha256=E71F9mRNWlttVpzlnP7w_fqkQygPkph5s-AtVa0Js50,5601
fontTools/__init__.py,sha256=5U6x_uZd11VYk1ucwvyYEj3wYSfJMdnXPt0twVOXaPQ,191
fontTools/__main__.py,sha256=T8Tg8xPKHOCVoYVG82p_zpQXfW7_ERRAphBkZVvhWN8,960
fontTools/__pycache__/__init__.cpython-311.pyc,,
fontTools/__pycache__/__main__.cpython-311.pyc,,
fontTools/__pycache__/afmLib.cpython-311.pyc,,
fontTools/__pycache__/agl.cpython-311.pyc,,
fontTools/__pycache__/fontBuilder.cpython-311.pyc,,
fontTools/__pycache__/help.cpython-311.pyc,,
fontTools/__pycache__/tfmLib.cpython-311.pyc,,
fontTools/__pycache__/ttx.cpython-311.pyc,,
fontTools/__pycache__/unicode.cpython-311.pyc,,
fontTools/afmLib.py,sha256=YbmmjT8Du6qFUhFHwnAhOdvsyfXszODVjSJtd18CCjY,13603
fontTools/agl.py,sha256=4aKwnbvSVUa39eV5Ka8e5ULwV-IEp4pcfwlMwEH_z3k,118208
fontTools/cffLib/CFF2ToCFF.py,sha256=5uPKDFwoJvH0KVDrCjpf3MdOpqbyvdZMe0jZ3emjdsQ,6291
fontTools/cffLib/CFFToCFF2.py,sha256=0dCYSSozptUC9BVUre49e6LgjSxJRtVyMl8vDB6i3r4,10424
fontTools/cffLib/__init__.py,sha256=E4wzLsJ1LxWO7CIR7fjZMHaYQJSVdqCO08fOVFowwpM,111580
fontTools/cffLib/__pycache__/CFF2ToCFF.cpython-311.pyc,,
fontTools/cffLib/__pycache__/CFFToCFF2.cpython-311.pyc,,
fontTools/cffLib/__pycache__/__init__.cpython-311.pyc,,
fontTools/cffLib/__pycache__/specializer.cpython-311.pyc,,
fontTools/cffLib/__pycache__/transforms.cpython-311.pyc,,
fontTools/cffLib/__pycache__/width.cpython-311.pyc,,
fontTools/cffLib/specializer.py,sha256=dznFa-7VrKZkx6D8klaixTaqEAnrnT6YLX9jzA6S0Cc,33536
fontTools/cffLib/transforms.py,sha256=8hffhsWRhBhVukNSL-7ieuygTVV5Ta3Cz9s4s8Awvgg,17861
fontTools/cffLib/width.py,sha256=3L9NWI0uQrJHvHF_IvC_tbW1cq94zgDEPSjubdug8qM,6284
fontTools/colorLib/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
fontTools/colorLib/__pycache__/__init__.cpython-311.pyc,,
fontTools/colorLib/__pycache__/builder.cpython-311.pyc,,
fontTools/colorLib/__pycache__/errors.cpython-311.pyc,,
fontTools/colorLib/__pycache__/geometry.cpython-311.pyc,,
fontTools/colorLib/__pycache__/table_builder.cpython-311.pyc,,
fontTools/colorLib/__pycache__/unbuilder.cpython-311.pyc,,
fontTools/colorLib/builder.py,sha256=S8z4Qzw2FAE-d1Zm1eHyqDBYh6FW4W_hQJWjVeVicOk,23672
fontTools/colorLib/errors.py,sha256=_3vbGsi6nlkRxxglt82uxK89K8tjURX59G3BBQIy5ps,43
fontTools/colorLib/geometry.py,sha256=RH7sl0oP9othrawGMeLVDAIocv8I2HrMd3aW857Xi_s,5661
fontTools/colorLib/table_builder.py,sha256=0k6SHt8JBwP6hy-nZ9k6VXnPywdPRBe91yZyGq3Mzb8,7692
fontTools/colorLib/unbuilder.py,sha256=nw8YKKiJiSsZAPcvPzRvXO-oZnvWmCWE7Y8nU1g75iE,2223
fontTools/config/__init__.py,sha256=qdbu1XOSFLOEZSe9Rfgj33Pkff8wEaaWJJExf_dzv0A,3244
fontTools/config/__pycache__/__init__.cpython-311.pyc,,
fontTools/cu2qu/__init__.py,sha256=OoM_nBJAleZal6kxeNJn1ESy1pNm5c3DG417yVIE0-Q,633
fontTools/cu2qu/__main__.py,sha256=6Vb8Ler3yqJ5w84UwlMJV6cS01uhV4PN10OlXQ6jlqo,98
fontTools/cu2qu/__pycache__/__init__.cpython-311.pyc,,
fontTools/cu2qu/__pycache__/__main__.cpython-311.pyc,,
fontTools/cu2qu/__pycache__/benchmark.cpython-311.pyc,,
fontTools/cu2qu/__pycache__/cli.cpython-311.pyc,,
fontTools/cu2qu/__pycache__/cu2qu.cpython-311.pyc,,
fontTools/cu2qu/__pycache__/errors.cpython-311.pyc,,
fontTools/cu2qu/__pycache__/ufo.cpython-311.pyc,,
fontTools/cu2qu/benchmark.py,sha256=FwdvNjKfWHo18_CX0CO8AY5c68XSBE4M4TJo_EkB4q8,1350
fontTools/cu2qu/cli.py,sha256=CvWzC5a6XF_v5o0yrS4vGI1JXiVVLzSJahTIqpJmiPk,6274
fontTools/cu2qu/cu2qu.c,sha256=vGZopOBC5bnIqTGmwPiW107oLG0YSrHSoyq1BFoqppI,644884
fontTools/cu2qu/cu2qu.cp311-win_amd64.pyd,sha256=IxqPz4PdMj90pwmxX9eI9ivkrWBgmvfmIyhBwK5agIw,98816
fontTools/cu2qu/cu2qu.py,sha256=XH2bnQ5aG9ic921ZWzQzU1-q3MQU6INCjLk4XjRj5_Y,16970
fontTools/cu2qu/errors.py,sha256=uYyPSs_x-EMJKO2S3cLGWyk_KlHoOoh_XEtdB_oKBp0,2518
fontTools/cu2qu/ufo.py,sha256=Mpd_7Be9jxNcOKFqkyRp8Oem3CS3R-ZYMMSD03LJL6o,12143
fontTools/designspaceLib/__init__.py,sha256=80fzbsWaoTMaXsPGMnevXAxR4eqvZeYCwV_GYpBvlkM,132601
fontTools/designspaceLib/__main__.py,sha256=QOn1SNf8xmw-zQ5EJN0JnrHllu9rbRm8kTpWF9b3jlo,109
fontTools/designspaceLib/__pycache__/__init__.cpython-311.pyc,,
fontTools/designspaceLib/__pycache__/__main__.cpython-311.pyc,,
fontTools/designspaceLib/__pycache__/split.cpython-311.pyc,,
fontTools/designspaceLib/__pycache__/statNames.cpython-311.pyc,,
fontTools/designspaceLib/__pycache__/types.cpython-311.pyc,,
fontTools/designspaceLib/split.py,sha256=MjgyVDfhLEdb844nioL3xIN6VinHqY4jcdOlwmvr03M,19714
fontTools/designspaceLib/statNames.py,sha256=RxxSLfkniuJ9I1aeXiLEdCS8uTL4w952_5D9DSfiRM4,9497
fontTools/designspaceLib/types.py,sha256=HtM5ibhj1FeoS5Yq2Q5YAlP8CL5WDI_W_0v-qJyKJww,5467
fontTools/encodings/MacRoman.py,sha256=rxWvh1yMTg_pY7_sSKpjfD6bYcA-BVHZL4S8JUH33fc,3834
fontTools/encodings/StandardEncoding.py,sha256=z0Uh0ZLnz5SsO6T2dxN0S646ZYRfpC_F6HtUIsidC94,3839
fontTools/encodings/__init__.py,sha256=QoK6HlOoqtVqX5gOyv0bJiTXsVBbBRreUifdccWNp2k,76
fontTools/encodings/__pycache__/MacRoman.cpython-311.pyc,,
fontTools/encodings/__pycache__/StandardEncoding.cpython-311.pyc,,
fontTools/encodings/__pycache__/__init__.cpython-311.pyc,,
fontTools/encodings/__pycache__/codecs.cpython-311.pyc,,
fontTools/encodings/codecs.py,sha256=bSpO6kuPbEIDsXSVHhzftqsm_FFUiXpLVfPSk410SqE,4856
fontTools/feaLib/__init__.py,sha256=RprjP6BKswq4pt0J-9L1XGuZfjIFAGD6HDly_haMAN4,217
fontTools/feaLib/__main__.py,sha256=niUAPkiYxeRAJMlJuvVJZism2VFufZrNaQtieA7sNLk,2318
fontTools/feaLib/__pycache__/__init__.cpython-311.pyc,,
fontTools/feaLib/__pycache__/__main__.cpython-311.pyc,,
fontTools/feaLib/__pycache__/ast.cpython-311.pyc,,
fontTools/feaLib/__pycache__/builder.cpython-311.pyc,,
fontTools/feaLib/__pycache__/error.cpython-311.pyc,,
fontTools/feaLib/__pycache__/lexer.cpython-311.pyc,,
fontTools/feaLib/__pycache__/location.cpython-311.pyc,,
fontTools/feaLib/__pycache__/lookupDebugInfo.cpython-311.pyc,,
fontTools/feaLib/__pycache__/parser.cpython-311.pyc,,
fontTools/feaLib/__pycache__/variableScalar.cpython-311.pyc,,
fontTools/feaLib/ast.py,sha256=q-UvEPZ97AAHpggVOzVHdgfTcE072kuOK08rdAYpCXU,76301
fontTools/feaLib/builder.py,sha256=9f7v9Vfo0HkC6Pqj-hP4B6xj5GDIoV_XLCeDajmbI2g,74962
fontTools/feaLib/error.py,sha256=pqi8F2tnH2h7pXVffxwzuBuWaSHMzZsXs5VckdQKQAI,670
fontTools/feaLib/lexer.c,sha256=PXU52K9r0MSS_E2tiD4R_ac1r-uXCeye8yHgRfcww44,770662
fontTools/feaLib/lexer.cp311-win_amd64.pyd,sha256=eERiHI8YMi8lBPt4S96ZWB2fTwjmkz6j9oeKCqXm7OQ,122880
fontTools/feaLib/lexer.py,sha256=7VZ3NPFH7V1mvRbym111BNKvbB4hLfGLTMS0VV_3Ipw,11408
fontTools/feaLib/location.py,sha256=teHrhjT8zzImcGBEJS1J43oaX9onCPu_pynxS8d-tUg,246
fontTools/feaLib/lookupDebugInfo.py,sha256=h4Ig8kmEk5WlGf1C9JJAbbOKQK5OwkFLdj8CT7fOkmU,316
fontTools/feaLib/parser.py,sha256=fdycJS5E1RtBvFifNx4rub360J6LmUyX5XpvfoEGxNI,101710
fontTools/feaLib/variableScalar.py,sha256=RiLHKQh2-wa-BZ015H2e7XkbshssTj2PjlapaMNJfAs,4182
fontTools/fontBuilder.py,sha256=qeUEUzacKdZXZ9dS_e2AuLE62c17eg_oHwnZgD04TaQ,35144
fontTools/help.py,sha256=8yn5iAonGPsijFSHmU6aLuuZtaLMhR5CIkSp9hVYL2c,1161
fontTools/merge/__init__.py,sha256=A6aQxwtbzcLUrE9UOdk3yCrMwqPOboTEr5ZyEu9RxRg,8504
fontTools/merge/__main__.py,sha256=3_u3dnyEOyh0O-SrLMLlkXxOfCFT-0SlwJpimosVJ-c,100
fontTools/merge/__pycache__/__init__.cpython-311.pyc,,
fontTools/merge/__pycache__/__main__.cpython-311.pyc,,
fontTools/merge/__pycache__/base.cpython-311.pyc,,
fontTools/merge/__pycache__/cmap.cpython-311.pyc,,
fontTools/merge/__pycache__/layout.cpython-311.pyc,,
fontTools/merge/__pycache__/options.cpython-311.pyc,,
fontTools/merge/__pycache__/tables.cpython-311.pyc,,
fontTools/merge/__pycache__/unicode.cpython-311.pyc,,
fontTools/merge/__pycache__/util.cpython-311.pyc,,
fontTools/merge/base.py,sha256=LPJKOwMiDwayLGzA1xH325CtYHPvahAA17lihvKjiPw,2470
fontTools/merge/cmap.py,sha256=zoOze0gVp4YQXGs-zFf5k7DgEPdFMs-A3sm5v-Rtz5M,6901
fontTools/merge/layout.py,sha256=S9j0FOUDOtXAzfO7_L6IrLBHplSLfxFqIi_IJUunXCg,16601
fontTools/merge/options.py,sha256=b-9GZ-nN7fh1VrpnEFhK_eRZPIIlRArtYOndOCetoUY,2586
fontTools/merge/tables.py,sha256=xjWt2uqgfxmrDvpLfo_ngsPr7aY8CTkDwwjYBToLnm0,11310
fontTools/merge/unicode.py,sha256=mgqRFhRugda62Xt0r28SduaN7YBzRfHxrpNprjLqoX8,4351
fontTools/merge/util.py,sha256=3alo4b7mhFNC6h8PjeqNU99dS7EuO8sdZkZpvRsEE6E,3521
fontTools/misc/__init__.py,sha256=QoK6HlOoqtVqX5gOyv0bJiTXsVBbBRreUifdccWNp2k,76
fontTools/misc/__pycache__/__init__.cpython-311.pyc,,
fontTools/misc/__pycache__/arrayTools.cpython-311.pyc,,
fontTools/misc/__pycache__/bezierTools.cpython-311.pyc,,
fontTools/misc/__pycache__/classifyTools.cpython-311.pyc,,
fontTools/misc/__pycache__/cliTools.cpython-311.pyc,,
fontTools/misc/__pycache__/configTools.cpython-311.pyc,,
fontTools/misc/__pycache__/cython.cpython-311.pyc,,
fontTools/misc/__pycache__/dictTools.cpython-311.pyc,,
fontTools/misc/__pycache__/eexec.cpython-311.pyc,,
fontTools/misc/__pycache__/encodingTools.cpython-311.pyc,,
fontTools/misc/__pycache__/etree.cpython-311.pyc,,
fontTools/misc/__pycache__/filenames.cpython-311.pyc,,
fontTools/misc/__pycache__/fixedTools.cpython-311.pyc,,
fontTools/misc/__pycache__/intTools.cpython-311.pyc,,
fontTools/misc/__pycache__/iterTools.cpython-311.pyc,,
fontTools/misc/__pycache__/lazyTools.cpython-311.pyc,,
fontTools/misc/__pycache__/loggingTools.cpython-311.pyc,,
fontTools/misc/__pycache__/macCreatorType.cpython-311.pyc,,
fontTools/misc/__pycache__/macRes.cpython-311.pyc,,
fontTools/misc/__pycache__/psCharStrings.cpython-311.pyc,,
fontTools/misc/__pycache__/psLib.cpython-311.pyc,,
fontTools/misc/__pycache__/psOperators.cpython-311.pyc,,
fontTools/misc/__pycache__/py23.cpython-311.pyc,,
fontTools/misc/__pycache__/roundTools.cpython-311.pyc,,
fontTools/misc/__pycache__/sstruct.cpython-311.pyc,,
fontTools/misc/__pycache__/symfont.cpython-311.pyc,,
fontTools/misc/__pycache__/testTools.cpython-311.pyc,,
fontTools/misc/__pycache__/textTools.cpython-311.pyc,,
fontTools/misc/__pycache__/timeTools.cpython-311.pyc,,
fontTools/misc/__pycache__/transform.cpython-311.pyc,,
fontTools/misc/__pycache__/treeTools.cpython-311.pyc,,
fontTools/misc/__pycache__/vector.cpython-311.pyc,,
fontTools/misc/__pycache__/visitor.cpython-311.pyc,,
fontTools/misc/__pycache__/xmlReader.cpython-311.pyc,,
fontTools/misc/__pycache__/xmlWriter.cpython-311.pyc,,
fontTools/misc/arrayTools.py,sha256=baENNALPvYRUhS4rdx_F3ltOmVIf1PV9G2EaMt7gAHM,11907
fontTools/misc/bezierTools.c,sha256=R4i5TUxLgiDFCwkoBZUpdBhGzRp5AI4GYDW497S1f54,1860910
fontTools/misc/bezierTools.cp311-win_amd64.pyd,sha256=rFtk7lMpRPueC2MAukc2tZ9OBgW0GNoi-MwjUQ1wmmM,343552
fontTools/misc/bezierTools.py,sha256=m4j14ckKYtrKy8NhFFFY_Uv3kuL8g-SWNdEKUzqGjRQ,46535
fontTools/misc/classifyTools.py,sha256=wLTjOhLiZaLiwwUTj2Ad5eZ5T_38W0Eo_uzRGWHWYvE,5783
fontTools/misc/cliTools.py,sha256=7zKOXczaCKRMW6Yv5jdCZYHco8y0-lfimhIWzQ2IL8A,1915
fontTools/misc/configTools.py,sha256=JNR7HqId8zudAlFcK4lwocHZkwgaTSH4u6BOyFLTujw,11537
fontTools/misc/cython.py,sha256=fZ9_mObkVzdJoK6sufiIU95k5GStjp6LWOk4AQ8zW_Q,709
fontTools/misc/dictTools.py,sha256=GZa83GxwQD4-kZYkbCCefW-ggH4WG8G6f5jCy0NcO6w,2500
fontTools/misc/eexec.py,sha256=eN9R1_67tWaeWn3ikEs0VwB1N7yr4vBbzs-aMbAUROw,3450
fontTools/misc/encodingTools.py,sha256=rlAZpxgcKXPzfpfHKk0BQW2Edz2JwTT8d0IIMRib3VE,2145
fontTools/misc/etree.py,sha256=M_4wKgaiaV7ALP3Uiv3HnK_KXFJmb37SUIK4tFZFVws,16760
fontTools/misc/filenames.py,sha256=IZuoPgh88KI2Rdo56FrHAtNSUoCeIaiWqrQk2VEeRoQ,8468
fontTools/misc/filesystem/__init__.py,sha256=wAfcTDA9TBfzZpEN-jnlhNgJ-7tsvsEpbEb3TP9B8Vs,2079
fontTools/misc/filesystem/__pycache__/__init__.cpython-311.pyc,,
fontTools/misc/filesystem/__pycache__/_base.cpython-311.pyc,,
fontTools/misc/filesystem/__pycache__/_copy.cpython-311.pyc,,
fontTools/misc/filesystem/__pycache__/_errors.cpython-311.pyc,,
fontTools/misc/filesystem/__pycache__/_info.cpython-311.pyc,,
fontTools/misc/filesystem/__pycache__/_osfs.cpython-311.pyc,,
fontTools/misc/filesystem/__pycache__/_path.cpython-311.pyc,,
fontTools/misc/filesystem/__pycache__/_subfs.cpython-311.pyc,,
fontTools/misc/filesystem/__pycache__/_tempfs.cpython-311.pyc,,
fontTools/misc/filesystem/__pycache__/_tools.cpython-311.pyc,,
fontTools/misc/filesystem/__pycache__/_walk.cpython-311.pyc,,
fontTools/misc/filesystem/__pycache__/_zipfs.cpython-311.pyc,,
fontTools/misc/filesystem/_base.py,sha256=7vVl3J-xRNjQMrtoXDfeCCXSolM61gOZuML0p7akqL4,4144
fontTools/misc/filesystem/_copy.py,sha256=ilLjWsY9smVAD6xfDQTf6SiQQbwYvBB1C1UTAi8y0oA,1406
fontTools/misc/filesystem/_errors.py,sha256=ydFO8gLDQg-19zNps5c25aFS2pn8KoG36isRqHf223w,695
fontTools/misc/filesystem/_info.py,sha256=84OTekpR5N_7sbnnOGznTeXgNrKauAECnl8Exr5iEUs,2088
fontTools/misc/filesystem/_osfs.py,sha256=rdUu0wz8EEJgyjSkh7sPW_mwNmTuOulbD-t2C3kQy1M,5901
fontTools/misc/filesystem/_path.py,sha256=uPk1ncLUelCM4ms1Onj_sQaOGZZSB4eK-4HYmoKwZNI,1812
fontTools/misc/filesystem/_subfs.py,sha256=WZ7OUO56PeF86AJB4lO1UXm94zC0BHkAwa1BVJc6aLk,3120
fontTools/misc/filesystem/_tempfs.py,sha256=VFmK5-W7VmAutZEYGSfrG562lgW0Jk2ZdxTz4w4B5II,958
fontTools/misc/filesystem/_tools.py,sha256=Po76YLo2pYODh13d_kjTMa9XCf8A6MUlRM7obLAMZOw,1006
fontTools/misc/filesystem/_walk.py,sha256=WU0SBKObrj_bCqB48z2uEsFWnNosSfekwBlAER5-HKE,1710
fontTools/misc/filesystem/_zipfs.py,sha256=IcIO3epWIGT85LSRcoWmofkGGyYVY47zQaWQHKmoIRs,6505
fontTools/misc/fixedTools.py,sha256=3HzMFAs57LqsGBnbslq2btQ3KJbKwxmxkJPvTvOi8sY,7900
fontTools/misc/intTools.py,sha256=kRNjD5_2jyTKo07C0sFT0jT3dcVnU5XGJEjbXCErm4E,611
fontTools/misc/iterTools.py,sha256=hyLQrAPuUOzDoQWKtKhFLjV8-Gx3jHd9SvBEwQRSeTE,402
fontTools/misc/lazyTools.py,sha256=LJ7QvDG65xOBw2AI43qGCLxVmfdbsf-PUECfrenbkAU,1062
fontTools/misc/loggingTools.py,sha256=27VatVrX8Yu-w5rFYSUjOnPLJIJ9Hx2R6hJ5YpP_djA,20476
fontTools/misc/macCreatorType.py,sha256=5JZKTsnkI_VBhC52lwMSrdmzqgUOhwC42jPvbGahsPo,1649
fontTools/misc/macRes.py,sha256=ewiYDKioxxBKW6JQcRmxpNYw5JgtJZIJyqWBG_KplUo,8840
fontTools/misc/plistlib/__init__.py,sha256=doPqlGry1mRywSup0ahnwuT7mNeClhYQ82y7kd86hWQ,21794
fontTools/misc/plistlib/__pycache__/__init__.cpython-311.pyc,,
fontTools/misc/plistlib/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
fontTools/misc/psCharStrings.py,sha256=e5kR55Gm3orJsDLo3eu6CxpoZ1pMNZh5Wm-Zj4m7wJs,44532
fontTools/misc/psLib.py,sha256=cqxG8yMZ7_5VTxgTUl2ARNhIhNu_iTzxLTEd1Egwugo,12497
fontTools/misc/psOperators.py,sha256=9nZ4ymbiiCApY9V8OARpYqvO73OEcJgGyTtCuGzD-rw,16272
fontTools/misc/py23.py,sha256=BhByQabxZis6fDvK3ZVeI-YRj_1rMQeBZCFzGWIac0U,2334
fontTools/misc/roundTools.py,sha256=2rmbuk73NYGPmJqP58FQCFioSLilvNffd0WbL5znKUg,3283
fontTools/misc/sstruct.py,sha256=7Lc9x2RV_e2JvSI4A_Rs0tM1mZpjYg_oAJKhP_F9zts,7236
fontTools/misc/symfont.py,sha256=KYAtw-ZnG5YReS8XkSDIvxc1bl0xzZl-Wx4J7k7u7LA,7219
fontTools/misc/testTools.py,sha256=SG48M4TJIQ4_cPpitUzGEITPnwL-o0yNZKXzWSQdwVE,7285
fontTools/misc/textTools.py,sha256=NIBmM6k9PXIs8DMpio-9ckHS35QxL2EMFwBXP6zG-8w,3531
fontTools/misc/timeTools.py,sha256=lmncKUKvxQKO4Kqx2k7UNFkYYpj2n5CwR1lPiLZv3tA,2322
fontTools/misc/transform.py,sha256=pCR0tbKzmhH6crB_rDT5hnAWySztW_XqL0efmKOVsCU,16314
fontTools/misc/treeTools.py,sha256=IMopMUcuhelvz8gNra50Zc1w8DSlWywnL6DFaz1ijQs,1314
fontTools/misc/vector.py,sha256=yaNixq5pXXpPCD_wRP-LsXYSLr4WPX_y92Po05FeLU0,4209
fontTools/misc/visitor.py,sha256=30EPyUKvNsYU81uiYKjp_9gFj2KSqF8W2y_ldyIBJYQ,5760
fontTools/misc/xmlReader.py,sha256=gqYg3qlDkrKsO55DPaJ-dU0i5rltqZgnKlrXmR2Z7dQ,6768
fontTools/misc/xmlWriter.py,sha256=rGn6BDiB2MPrhoNabDbY4vAHZC6-mEciwCJXtOL6hMA,7060
fontTools/mtiLib/__init__.py,sha256=izRpPCoQfLoDjrlgKqP6gAE6JF9LU73aqH2_qi0NpaM,48002
fontTools/mtiLib/__main__.py,sha256=MnVcMQ1TxmYged20wKcjrpZDIvetmkzfRVKHCb5dsUc,99
fontTools/mtiLib/__pycache__/__init__.cpython-311.pyc,,
fontTools/mtiLib/__pycache__/__main__.cpython-311.pyc,,
fontTools/otlLib/__init__.py,sha256=WhTONAtlItZxWAkHNit_EBW19pP32TFZSqIJ_GG6Peg,46
fontTools/otlLib/__pycache__/__init__.cpython-311.pyc,,
fontTools/otlLib/__pycache__/builder.cpython-311.pyc,,
fontTools/otlLib/__pycache__/error.cpython-311.pyc,,
fontTools/otlLib/__pycache__/maxContextCalc.cpython-311.pyc,,
fontTools/otlLib/builder.py,sha256=YTmrntgVrFbeSkAv44AZqBdIUBUUPyKcKPHj_kkh_pc,132192
fontTools/otlLib/error.py,sha256=0OQ2AuxKNEqvoHIkgouf47LDGDEmPUlhdZIW5DROL8k,346
fontTools/otlLib/maxContextCalc.py,sha256=sVU7LLwkjhV16ADcpjbUwCt5PZbWWdc8_yZo9Lv7HaI,3271
fontTools/otlLib/optimize/__init__.py,sha256=NKqA7fqHyzjkmuBL_ZVpc3u9OMbWxbKDtymC8CnVGNY,1583
fontTools/otlLib/optimize/__main__.py,sha256=ZZDwg21yVtdQi9GkNQe70w49hn9fPmObFEEDWGlCj3U,110
fontTools/otlLib/optimize/__pycache__/__init__.cpython-311.pyc,,
fontTools/otlLib/optimize/__pycache__/__main__.cpython-311.pyc,,
fontTools/otlLib/optimize/__pycache__/gpos.cpython-311.pyc,,
fontTools/otlLib/optimize/gpos.py,sha256=Rr9o9BJjQt_hLKxROqRFT41vY0eAcsSCogPhItPN3R8,18107
fontTools/pens/__init__.py,sha256=QoK6HlOoqtVqX5gOyv0bJiTXsVBbBRreUifdccWNp2k,76
fontTools/pens/__pycache__/__init__.cpython-311.pyc,,
fontTools/pens/__pycache__/areaPen.cpython-311.pyc,,
fontTools/pens/__pycache__/basePen.cpython-311.pyc,,
fontTools/pens/__pycache__/boundsPen.cpython-311.pyc,,
fontTools/pens/__pycache__/cairoPen.cpython-311.pyc,,
fontTools/pens/__pycache__/cocoaPen.cpython-311.pyc,,
fontTools/pens/__pycache__/cu2quPen.cpython-311.pyc,,
fontTools/pens/__pycache__/explicitClosingLinePen.cpython-311.pyc,,
fontTools/pens/__pycache__/filterPen.cpython-311.pyc,,
fontTools/pens/__pycache__/freetypePen.cpython-311.pyc,,
fontTools/pens/__pycache__/hashPointPen.cpython-311.pyc,,
fontTools/pens/__pycache__/momentsPen.cpython-311.pyc,,
fontTools/pens/__pycache__/perimeterPen.cpython-311.pyc,,
fontTools/pens/__pycache__/pointInsidePen.cpython-311.pyc,,
fontTools/pens/__pycache__/pointPen.cpython-311.pyc,,
fontTools/pens/__pycache__/qtPen.cpython-311.pyc,,
fontTools/pens/__pycache__/qu2cuPen.cpython-311.pyc,,
fontTools/pens/__pycache__/quartzPen.cpython-311.pyc,,
fontTools/pens/__pycache__/recordingPen.cpython-311.pyc,,
fontTools/pens/__pycache__/reportLabPen.cpython-311.pyc,,
fontTools/pens/__pycache__/reverseContourPen.cpython-311.pyc,,
fontTools/pens/__pycache__/roundingPen.cpython-311.pyc,,
fontTools/pens/__pycache__/statisticsPen.cpython-311.pyc,,
fontTools/pens/__pycache__/svgPathPen.cpython-311.pyc,,
fontTools/pens/__pycache__/t2CharStringPen.cpython-311.pyc,,
fontTools/pens/__pycache__/teePen.cpython-311.pyc,,
fontTools/pens/__pycache__/transformPen.cpython-311.pyc,,
fontTools/pens/__pycache__/ttGlyphPen.cpython-311.pyc,,
fontTools/pens/__pycache__/wxPen.cpython-311.pyc,,
fontTools/pens/areaPen.py,sha256=SJnD7HwRg6JL_p7HaAy5DB64G75So9sqIdmzCSRv1bI,1524
fontTools/pens/basePen.py,sha256=Wrd4xNl2apH4fdpkCPbV8z0QuNX7k46JHwylZer72G0,17548
fontTools/pens/boundsPen.py,sha256=JPqvmslPlv2kgdmhgjeJo-CTYbloxxkkaJD8wVTVpng,3227
fontTools/pens/cairoPen.py,sha256=jQL-9usqCU_FvfFpH4uaKjOcGd6jsarPpVM3vrhdyOU,618
fontTools/pens/cocoaPen.py,sha256=ReJkXzlgP8qe4zi_6X4oO_I6m0jQGATeB6ZHjJhNv_I,638
fontTools/pens/cu2quPen.py,sha256=w9xTNmhb96kvNZwcM5WT9q8FnRgA51AOISzVRpkiI3g,13332
fontTools/pens/explicitClosingLinePen.py,sha256=knCXcjSl2iPy6mLCDnsdDYx6J5rV7FH4S24OXFdINjg,3320
fontTools/pens/filterPen.py,sha256=tWhgklyaCTUt7oQRTBbFUcOlc702V0NfadCH3X93CYg,8031
fontTools/pens/freetypePen.py,sha256=NqNzXrOTDckoH4N6WLnj-KuxGcg6z7DlqSCfmpq8qAE,20370
fontTools/pens/hashPointPen.py,sha256=ZAU87uw5ge3Kb4i9kRV28a5VFeZ_TWSsJabyAzwAHrU,3662
fontTools/pens/momentsPen.c,sha256=JgH7cc_5ZKbJJUq0iiwppphdXhD4XL33KsNAIsBejx8,578346
fontTools/pens/momentsPen.cp311-win_amd64.pyd,sha256=0oXGE53C7ooYDe-ultjsCz8UGxQgcxIFMI9ukUv43jM,89600
fontTools/pens/momentsPen.py,sha256=Z-V5CjQBSj3qPxg3C_DBFKExqno89nOe3jWwHT9_xsM,26537
fontTools/pens/perimeterPen.py,sha256=Zy5F8QzaNJAkkQQSb2QJCp-wZTvDAjBn-B099t2ABds,2222
fontTools/pens/pointInsidePen.py,sha256=Hy48iR5NWV3x_wWoos-UC7GMtwvvUhd_q_ykiwaWdzQ,6547
fontTools/pens/pointPen.py,sha256=GV28cLEwSgpZZ4QGV7_eUw4Mdks_UqIpShQU0DBcvRs,23339
fontTools/pens/qtPen.py,sha256=KHHQggFQc6Gq-kPdn9X2_wBXTPWzvyzKTSUeq0mqvSM,663
fontTools/pens/qu2cuPen.py,sha256=VIqUzA_y_6xnRmTESKzlKkoByh7ZU5TnQwHnVAoy4no,4090
fontTools/pens/quartzPen.py,sha256=6DMDWPYfsOb374VDnLLpKLqcMJig4GCGbTsW1Jr0fgg,1330
fontTools/pens/recordingPen.py,sha256=hw393TStvhoF1XT7aidpVQ8glASbxZuARnUAyUyZAGM,12824
fontTools/pens/reportLabPen.py,sha256=vVRG044LvUvFtqrRFYRiMFS_USHAeAvz9y9-7__WbY4,2145
fontTools/pens/reverseContourPen.py,sha256=E_Ny86JfiMoQ04VfswMtdpaKCU37wNy9ifOccb0aWKQ,4118
fontTools/pens/roundingPen.py,sha256=e7rZdTmHfGX7vLHcqJ-ntc8qIhquLCDeaOhzYxQH998,4750
fontTools/pens/statisticsPen.py,sha256=F_JjbNtvYmJ0b3Fbv3BA3-LZhecodPr4tJEQZZd4Jxc,10120
fontTools/pens/svgPathPen.py,sha256=4aU4iTlnGuzzyXrBgfHvrjMOkC2rdSF8HOkJ_q8tZ38,8882
fontTools/pens/t2CharStringPen.py,sha256=g0lcaRhSAs4T2NuWvn89TODikC1t9x4KyBq0Dnkso-0,3019
fontTools/pens/teePen.py,sha256=19N3FEaFm4mGMTZrEn5Qg4YiXGGK61zcXjh2LcRxe_s,1345
fontTools/pens/transformPen.py,sha256=_Zvyxp0yQ7iFZ1_FYfr3KFWKWYOUY2eSxrRk41BRO2w,4171
fontTools/pens/ttGlyphPen.py,sha256=gAglwTL9DSsJGI8TUPVz-YBdPSMUcvd2S9jF-FzmckE,12205
fontTools/pens/wxPen.py,sha256=bolMLl06Q-TxsN8-SsSDbmJStTPGXMYJQZ7Vb67FhLw,709
fontTools/qu2cu/__init__.py,sha256=MpdE0XsHSDo9M3hyHLkPPLxB3FKr3aiT0dPW5qHCuSo,633
fontTools/qu2cu/__main__.py,sha256=leKpToUNNyHf0nobr1I19vus2ziA1pO7rRKkreat-Xw,100
fontTools/qu2cu/__pycache__/__init__.cpython-311.pyc,,
fontTools/qu2cu/__pycache__/__main__.cpython-311.pyc,,
fontTools/qu2cu/__pycache__/benchmark.cpython-311.pyc,,
fontTools/qu2cu/__pycache__/cli.cpython-311.pyc,,
fontTools/qu2cu/__pycache__/qu2cu.cpython-311.pyc,,
fontTools/qu2cu/benchmark.py,sha256=PFxx2Bfu7-KuNrzdOIBXHPZvyNphqqcTVy4CneaCo3M,1456
fontTools/qu2cu/cli.py,sha256=1QLBTSZW7e_VATJN9vjszRxIk_-Xjxu1KP53yX4T7q8,3839
fontTools/qu2cu/qu2cu.c,sha256=mPAVbeH6SgLkKCnPhergCFE2-evs0Z8E1WlvBhDxo5E,706100
fontTools/qu2cu/qu2cu.cp311-win_amd64.pyd,sha256=H8ipND75BSr6qaD1cKuWZSHEeL8HxyflCER20q0f9S8,106496
fontTools/qu2cu/qu2cu.py,sha256=dtp5Zqhcs_NePwA2U5fgG2LtWleRwmBilTurau8sLL0,12693
fontTools/subset/__init__.py,sha256=cMUbkLDSaj0f576WF5zhbZ9mYT4U9b_PPzFFBpg8VEI,141636
fontTools/subset/__main__.py,sha256=cEIC52EtGOJvFDfHXzi0M2EAYmyHAcI-ZZ0lb2y4r7s,101
fontTools/subset/__pycache__/__init__.cpython-311.pyc,,
fontTools/subset/__pycache__/__main__.cpython-311.pyc,,
fontTools/subset/__pycache__/cff.cpython-311.pyc,,
fontTools/subset/__pycache__/svg.cpython-311.pyc,,
fontTools/subset/__pycache__/util.cpython-311.pyc,,
fontTools/subset/cff.py,sha256=GSmxdsokxuFKvJJQVcAIOhd5hYQq8KkzxnXE_dgm8yo,6329
fontTools/subset/svg.py,sha256=y_yTZuAm3bjcoEOFu5likXoHuG5u1oNiv0mOni2Z9fQ,9637
fontTools/subset/util.py,sha256=gh2hkLaUmhHKRkdxxdLcFjz8clCmncLqdnDZm_2QNco,779
fontTools/svgLib/__init__.py,sha256=2igTH8FIxCzEp02sRijWni-ocuGqqwuPPPSpgjozrK0,78
fontTools/svgLib/__pycache__/__init__.cpython-311.pyc,,
fontTools/svgLib/path/__init__.py,sha256=xfTh9zD_JOjEq6EEDtDxYCtn73O33d5wCIaVEfsIb0U,2061
fontTools/svgLib/path/__pycache__/__init__.cpython-311.pyc,,
fontTools/svgLib/path/__pycache__/arc.cpython-311.pyc,,
fontTools/svgLib/path/__pycache__/parser.cpython-311.pyc,,
fontTools/svgLib/path/__pycache__/shapes.cpython-311.pyc,,
fontTools/svgLib/path/arc.py,sha256=-jU7F3gO_DdTO6MrDbOLxmFBZ_h5eb02Eq3Z_Ia35Nw,5966
fontTools/svgLib/path/parser.py,sha256=mMxmJjU1Z9beD0CqFrvBx9LkCutJ2LfKbTLgidLQvNw,11110
fontTools/svgLib/path/shapes.py,sha256=h3aOhsZ0pPUOLtNab2bj5cJuqPIlgdtOOOT4VYvnRww,5505
fontTools/t1Lib/__init__.py,sha256=eBp3X5XcHZIV4uurKxyakurcT2bfFdoTVpw4AOMx2TU,21513
fontTools/t1Lib/__pycache__/__init__.cpython-311.pyc,,
fontTools/tfmLib.py,sha256=-bv4iv2VhUSse5pA0oXdudf7o7ZuFWdWNsiHElO06dk,14730
fontTools/ttLib/__init__.py,sha256=2dJ9-KzN_5AwttwMEhmusrxR2IdFTZ73hJiPjeVwuwU,691
fontTools/ttLib/__main__.py,sha256=gSaKy1O2Hws3_1xGHGdLL-lEUVxw9q8ymNx9YlwIFXs,4881
fontTools/ttLib/__pycache__/__init__.cpython-311.pyc,,
fontTools/ttLib/__pycache__/__main__.cpython-311.pyc,,
fontTools/ttLib/__pycache__/macUtils.cpython-311.pyc,,
fontTools/ttLib/__pycache__/removeOverlaps.cpython-311.pyc,,
fontTools/ttLib/__pycache__/reorderGlyphs.cpython-311.pyc,,
fontTools/ttLib/__pycache__/scaleUpem.cpython-311.pyc,,
fontTools/ttLib/__pycache__/sfnt.cpython-311.pyc,,
fontTools/ttLib/__pycache__/standardGlyphOrder.cpython-311.pyc,,
fontTools/ttLib/__pycache__/ttCollection.cpython-311.pyc,,
fontTools/ttLib/__pycache__/ttFont.cpython-311.pyc,,
fontTools/ttLib/__pycache__/ttGlyphSet.cpython-311.pyc,,
fontTools/ttLib/__pycache__/ttVisitor.cpython-311.pyc,,
fontTools/ttLib/__pycache__/woff2.cpython-311.pyc,,
fontTools/ttLib/macUtils.py,sha256=B5UhZU8gQerJMXEG9-BGZsuv3aewFRAGQ5HCuZMzMkQ,1791
fontTools/ttLib/removeOverlaps.py,sha256=PTxICjLx89JxKfboLruoV_OwuwCIxcJ4feNcCCkrsTQ,13005
fontTools/ttLib/reorderGlyphs.py,sha256=PAHvoh4yN3u-_aDACH8H1ResVMCmVE7Kp5_mIKAG0TI,10656
fontTools/ttLib/scaleUpem.py,sha256=Qz-kS48q7a5GibgnPoUglyVk_qIVkYp5KZ-r1aMx_7Q,15054
fontTools/ttLib/sfnt.py,sha256=0VEhMx4T30H5X8_ZrpgXv8mxZkdLcGUETcN-QfmTiOM,23453
fontTools/ttLib/standardGlyphOrder.py,sha256=VG-8hW1VgQIro7cDJusSXThILIr4pQgmU37t85SQ65Y,6056
fontTools/ttLib/tables/B_A_S_E_.py,sha256=KpUf8_XEoFNEv3RcoQjfOaJUtBBaCxMzfifEcGtAydI,383
fontTools/ttLib/tables/BitmapGlyphMetrics.py,sha256=cQuhook-kYL6AoUS9vQIAr65Ls6xN-e15l_lCxDwM2w,1833
fontTools/ttLib/tables/C_B_D_T_.py,sha256=zg-Knjto2WgnEjl-_foLbeecNp0KHUOHg8ZgCmAyCqI,3759
fontTools/ttLib/tables/C_B_L_C_.py,sha256=b8FTbwHE4RinasyZ1ieLW7lo5gmmWQB3fO73g4DMVAE,539
fontTools/ttLib/tables/C_F_F_.py,sha256=Jo_pbWzq6im8Jh4N47RTl9E6-6YQbRiNI2mOY-JY8Js,2039
fontTools/ttLib/tables/C_F_F__2.py,sha256=q9Y6-yvA8JjrMjBKvb0jtg5T0Z1qKPBduTv8A5-WTZk,833
fontTools/ttLib/tables/C_O_L_R_.py,sha256=pq9xotYUq19Gq8GghKqUlru0nBqlmKdAcqrxa25wboM,6158
fontTools/ttLib/tables/C_P_A_L_.py,sha256=DEB9H9TXDg9uYhmrNEsNgXJ6cj9NMCVpVDJ-XMsBJzo,12247
fontTools/ttLib/tables/D_S_I_G_.py,sha256=ngvrE19I7s5t6N3QUsvoUONkTvdVtGC7vItIVDun0r4,5675
fontTools/ttLib/tables/D__e_b_g.py,sha256=Co-AyQ7kQQs4x7fhIjBt33E9I48mIoZNZR4Z8OUVXUU,1169
fontTools/ttLib/tables/DefaultTable.py,sha256=_pMaYi_MrvHzioY5s3NvKdzEFaueppMeJIpnfQDwWqg,1536
fontTools/ttLib/tables/E_B_D_T_.py,sha256=f96YN8zj5Qcp9kweWU0fmG1W-uUewNxLR8Ox3yzsnjo,33369
fontTools/ttLib/tables/E_B_L_C_.py,sha256=ED5j8COGRPBUXxoey7bXVoRNiZCC2rgx0cWls2YNp6g,30772
fontTools/ttLib/tables/F_F_T_M_.py,sha256=wpjIN0MfovCM0JEHdzC7ZCTRjBx9-mNjIMRRQGYstCA,1735
fontTools/ttLib/tables/F__e_a_t.py,sha256=JgTjN_z2Wo55Ul6hNGqluRu4zgegtnu2H8mvq1hNjfs,5632
fontTools/ttLib/tables/G_D_E_F_.py,sha256=AjrMHUMYg75zrZloeRbuMXW1VJkYei1iouYpIVZ_mgk,312
fontTools/ttLib/tables/G_M_A_P_.py,sha256=6_EJEwWdE4Jz6Y2BsRNLpGJPbcuozKMAUSMGhqVqWuc,4868
fontTools/ttLib/tables/G_P_K_G_.py,sha256=GBwAX4zOC5fAcK7m9bC2Cf_8kcVu-39tdFUaSYH0jFg,4779
fontTools/ttLib/tables/G_P_O_S_.py,sha256=TU0AI44SJonvGkfF9GO7vH3Ca0R8_DhHDSn5CDUbOfI,411
fontTools/ttLib/tables/G_S_U_B_.py,sha256=x09o8a8tcnGHdbW--RgA7tDao860uh3Lp183DkeMWpc,307
fontTools/ttLib/tables/G_V_A_R_.py,sha256=S1dGZnMDJ2kIndVo-I5FGDW05rXuMoIlsoFbjZvtPSM,99
fontTools/ttLib/tables/G__l_a_t.py,sha256=xmyj4nsf1cpYxBAXrvaZ9zY_G1gclGWpfG1m6qOzgw4,8880
fontTools/ttLib/tables/G__l_o_c.py,sha256=sTNUnvHMvFSiU1QOhLT9A8Fw0mTOOyUumhxAOEpB4So,2770
fontTools/ttLib/tables/H_V_A_R_.py,sha256=sVJ4MK33ZenyUq8Hg-tmqu_FlR7tJOsqZgpbUnIQL6E,326
fontTools/ttLib/tables/J_S_T_F_.py,sha256=Pp8tE_w6YNJaCCnzteYQ7B70pZ1_q1nGci4zfASt-4Q,328
fontTools/ttLib/tables/L_T_S_H_.py,sha256=pgoHEK-9iBRmAfjO9lYROT5cqOMxsQjO13MMXU0RXp4,2247
fontTools/ttLib/tables/M_A_T_H_.py,sha256=gU7yDMPDZ_XyA_pYZBYoA_p8rxzJtB65CrNV4Ta35tI,355
fontTools/ttLib/tables/M_E_T_A_.py,sha256=E_jO_lkeLBuGEtlYfH1sDbzIZhP0ZaJX4u3lgF3ZAMs,12341
fontTools/ttLib/tables/M_V_A_R_.py,sha256=sKe1GfahViCwY4kFXSU8t8WYH-FzUOwZdO_q6NnQZbM,321
fontTools/ttLib/tables/O_S_2f_2.py,sha256=HFVzQYVZDqETgRCTbRqa3NyyuwlTwmc6CNSngjt89ZY,28782
fontTools/ttLib/tables/S_I_N_G_.py,sha256=6deN-m2-k5C20NE8iTdajju8D2Mw_0tcPiHQln_RaMo,3416
fontTools/ttLib/tables/S_T_A_T_.py,sha256=clj8sbU60dzo16KApGXNp54CSS387GjIjxuiu5cU09c,513
fontTools/ttLib/tables/S_V_G_.py,sha256=O6Aik0j7t02ODsZRwI_tJUwNJQiZ3Dl3oxPqQhyRXH8,7899
fontTools/ttLib/tables/S__i_l_f.py,sha256=i-ufBKv10ykJ7-8rfYnA7RgcIdl3XaQmz8pNrLOsWCw,36025
fontTools/ttLib/tables/S__i_l_l.py,sha256=LeN6U0y4VLNgtIa7uCX_cpsZW1Ue_yKY8dsZJyS75ec,3316
fontTools/ttLib/tables/T_S_I_B_.py,sha256=zbtLbMfCSVRd9hc5qoxPoQ8j3tNYtrvofTy7Kl6TBwE,354
fontTools/ttLib/tables/T_S_I_C_.py,sha256=xpE9EYI3hFETe0CFG4RMe4G52_2aBsOs9kiCwXISKeo,395
fontTools/ttLib/tables/T_S_I_D_.py,sha256=wYcFELNUSziSax21UlqOnEBpVl4k4aDOXBYI9F3NwMk,354
fontTools/ttLib/tables/T_S_I_J_.py,sha256=3Q-tPCl04mggf5bIY6p6RvV2ZUmVMfCQ5WYyfJdPfPA,354
fontTools/ttLib/tables/T_S_I_P_.py,sha256=4h7p-ssF_gklUJHtkPy62eA8LvidV38K3HHmiJ2p0ek,354
fontTools/ttLib/tables/T_S_I_S_.py,sha256=eDzfFEZHN4-sawGAF5gtAIj8LKRAFXe8z1ve7aHmY9M,354
fontTools/ttLib/tables/T_S_I_V_.py,sha256=IX-V7mRFxXNmj-wtEfFvpDjkevGZE-OEo5-Dvd6jfgY,881
fontTools/ttLib/tables/T_S_I__0.py,sha256=-ahx5aDKeZAkoeZHXZRw95aZ85905sYAPhDlKGb_1_A,2575
fontTools/ttLib/tables/T_S_I__1.py,sha256=QXBeuKDikkWQME0T3e-NYK7qevsJClLl121Za5bXQsg,7134
fontTools/ttLib/tables/T_S_I__2.py,sha256=QU05Fvz1L-OE7bqXwsRuMjrhtGwi7WtI-UwS7lBS1jM,513
fontTools/ttLib/tables/T_S_I__3.py,sha256=bBo8nZ2bXDcKAmym1rRM--4nrjSXl7CvQhqe6h544SY,565
fontTools/ttLib/tables/T_S_I__5.py,sha256=QbEJddd97R46RGfaDYbkwwpd_hDtEddLoK3MkzkOZIo,1965
fontTools/ttLib/tables/T_T_F_A_.py,sha256=2lCDLx_UmkWTvNLrKkDm7T4MErKOvojLDvr6iQ9tXpM,406
fontTools/ttLib/tables/TupleVariation.py,sha256=m7TWmB4TBmJ6DD_onpYidGp5qq3ogTd4qYRXa8XHJ90,33119
fontTools/ttLib/tables/V_A_R_C_.py,sha256=6CgniBLKLlrLXRqDC-z6aYHQD1QzXZYF8w9DMof1PMc,301
fontTools/ttLib/tables/V_D_M_X_.py,sha256=gDeNfw0f1YzJqdad4NSuP5KzuoTbH5bP4GFQOKv58i0,10686
fontTools/ttLib/tables/V_O_R_G_.py,sha256=s9g03_qeTV3qoJAWpXxpRCmao0l1wj4WagR_YsTlyBQ,6130
fontTools/ttLib/tables/V_V_A_R_.py,sha256=PiwzLv95tfXH25hYQFAxL11mwgbLjeg4R1LvVH5m7lU,332
fontTools/ttLib/tables/__init__.py,sha256=pYmftKvp7RCNIaEJuUNjcZFaiIfOC0zzcApViNKUxkU,2749
fontTools/ttLib/tables/__pycache__/B_A_S_E_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/BitmapGlyphMetrics.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/C_B_D_T_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/C_B_L_C_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/C_F_F_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/C_F_F__2.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/C_O_L_R_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/C_P_A_L_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/D_S_I_G_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/D__e_b_g.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/DefaultTable.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/E_B_D_T_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/E_B_L_C_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/F_F_T_M_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/F__e_a_t.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/G_D_E_F_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/G_M_A_P_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/G_P_K_G_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/G_P_O_S_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/G_S_U_B_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/G_V_A_R_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/G__l_a_t.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/G__l_o_c.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/H_V_A_R_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/J_S_T_F_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/L_T_S_H_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/M_A_T_H_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/M_E_T_A_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/M_V_A_R_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/O_S_2f_2.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/S_I_N_G_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/S_T_A_T_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/S_V_G_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/S__i_l_f.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/S__i_l_l.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/T_S_I_B_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/T_S_I_C_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/T_S_I_D_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/T_S_I_J_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/T_S_I_P_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/T_S_I_S_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/T_S_I_V_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/T_S_I__0.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/T_S_I__1.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/T_S_I__2.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/T_S_I__3.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/T_S_I__5.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/T_T_F_A_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/TupleVariation.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/V_A_R_C_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/V_D_M_X_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/V_O_R_G_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/V_V_A_R_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/__init__.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_a_n_k_r.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_a_v_a_r.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_b_s_l_n.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_c_i_d_g.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_c_m_a_p.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_c_v_a_r.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_c_v_t.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_f_e_a_t.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_f_p_g_m.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_f_v_a_r.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_g_a_s_p.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_g_c_i_d.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_g_l_y_f.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_g_v_a_r.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_h_d_m_x.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_h_e_a_d.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_h_h_e_a.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_h_m_t_x.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_k_e_r_n.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_l_c_a_r.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_l_o_c_a.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_l_t_a_g.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_m_a_x_p.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_m_e_t_a.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_m_o_r_t.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_m_o_r_x.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_n_a_m_e.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_o_p_b_d.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_p_o_s_t.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_p_r_e_p.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_p_r_o_p.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_s_b_i_x.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_t_r_a_k.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_v_h_e_a.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_v_m_t_x.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/asciiTable.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/grUtils.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/otBase.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/otConverters.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/otData.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/otTables.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/otTraverse.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/sbixGlyph.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/sbixStrike.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/ttProgram.cpython-311.pyc,,
fontTools/ttLib/tables/_a_n_k_r.py,sha256=eiy6DKxPGw-H9QCLWIQBFveuTFQSKgcPItwgyBOghk8,498
fontTools/ttLib/tables/_a_v_a_r.py,sha256=cIqfWyyU5hNrJ-SIfVCKQcpyi2DUjobAgrmyE2In9FI,7307
fontTools/ttLib/tables/_b_s_l_n.py,sha256=iHLFy3sjFFoGa-pDGdcLCCudv4QFMt2VBL06gigGA_k,480
fontTools/ttLib/tables/_c_i_d_g.py,sha256=BPa6b0yrmT8OXPW3USRpn_H8DOLKFlDe9subtRDJBrc,937
fontTools/ttLib/tables/_c_m_a_p.py,sha256=dvtnrDf1LjXVf_DXW0tz67BqOlf7dINyG0_HF3aoDmw,63793
fontTools/ttLib/tables/_c_v_a_r.py,sha256=5LCuL07YXkqcqkSQeW4F6Vlx5XYtkjIIyiYfQ88Ydw0,3621
fontTools/ttLib/tables/_c_v_t.py,sha256=n1dc7Gyui5HxkimCYGMEdJ8V5X27UCDbydhv5-npNiI,1674
fontTools/ttLib/tables/_f_e_a_t.py,sha256=g-B57skN59uZa3dQ88gMVpY1RmQH92cCkttJq2T4TzY,484
fontTools/ttLib/tables/_f_p_g_m.py,sha256=eX1DSy29t_BFai90WQKEgHgCuMLqFp01Ff9a1txuuLI,1695
fontTools/ttLib/tables/_f_v_a_r.py,sha256=hpxU0-u7_pZYDmQdKpmd_7c6BjVpXHdoQ97jKk9Kz5U,9098
fontTools/ttLib/tables/_g_a_s_p.py,sha256=Pr4X2CEg3a_nYAZrKSWT0auQ5HU2WutP1Shxxg7ALPw,2266
fontTools/ttLib/tables/_g_c_i_d.py,sha256=diZlew4U8nFK5vimh-GMOjwHw8ccZtIEl9cPq0PrNdA,375
fontTools/ttLib/tables/_g_l_y_f.py,sha256=gnZ1femTeEaFeWn4inLiWL0h8U9_iJ0CzU_6PDtFryk,87895
fontTools/ttLib/tables/_g_v_a_r.py,sha256=bXLh4wXqtbL_hEmds5PXWJ5FTx58_1VojaqVvgesYH4,12412
fontTools/ttLib/tables/_h_d_m_x.py,sha256=1dRbNSzHQlznrXqG0GtFYbGp_SzzgDcYfJqhSoS1FL8,4379
fontTools/ttLib/tables/_h_e_a_d.py,sha256=ft9ghTA1NZsGBvB0yElFFCqVHecuCKGjT2m2GfYB3Yc,5056
fontTools/ttLib/tables/_h_h_e_a.py,sha256=pY92ZLt3o0jZ3KQVd_qtxYtk_tbP2DLzSWm_wVP8FNM,4914
fontTools/ttLib/tables/_h_m_t_x.py,sha256=p-9K-E3LcdJByagZ-0F0OA11pCVfNS9HtKRjbpvMM6I,6202
fontTools/ttLib/tables/_k_e_r_n.py,sha256=AjG5Fd6XaPAdXi5puDtLuMrfCsHUi9X7uFh76QGCMrc,11083
fontTools/ttLib/tables/_l_c_a_r.py,sha256=N-1I6OJHvnF_YfGktyfFTRAG5lrExV7q6HX-0ffSRyQ,403
fontTools/ttLib/tables/_l_o_c_a.py,sha256=MHRhxCcHbyK71lD3L91qNfYXD_rflSXsRa-YHDptCik,2250
fontTools/ttLib/tables/_l_t_a_g.py,sha256=rnf8P_C_RIb37HBNk0qDSxP7rK-N9j5CcQHgMrPSuxw,2624
fontTools/ttLib/tables/_m_a_x_p.py,sha256=9B6lvWo4y42dyLPIvG6CsVOlWCk7bs4DoVJDB8ViEew,5411
fontTools/ttLib/tables/_m_e_t_a.py,sha256=I8HaZgcIPQZcCxBiSX0rGrfrs-zXRGUfEbJ8eGvZ07A,4025
fontTools/ttLib/tables/_m_o_r_t.py,sha256=LU3D9PmV_nFs6hoccGmr1pfUzjJaeB_WRW2OIS0RwPc,501
fontTools/ttLib/tables/_m_o_r_x.py,sha256=vLyrtx_O__BwnPi7Qo3oT8WHaANRARtHcqHSdZ5ct0E,563
fontTools/ttLib/tables/_n_a_m_e.py,sha256=bvahvBMX21pC6G83D35WZap6F7BDd7Xi2AmhVMBZz00,42300
fontTools/ttLib/tables/_o_p_b_d.py,sha256=lfJi6kblt_nGmGmRSupwEaud3Ri_y6ftWNuyrCPpzQ0,462
fontTools/ttLib/tables/_p_o_s_t.py,sha256=HIsvathEMOAm8QFG7vcRfPQvBqXh-QmsVyOoDWsaRNc,12020
fontTools/ttLib/tables/_p_r_e_p.py,sha256=qWDjHiHvHaJCx2hYFmjJeMwpgwvD-cG5zkibMh9TWuk,443
fontTools/ttLib/tables/_p_r_o_p.py,sha256=ux5Z0FrE7uuKQrO-SCQwButVtKmEAsvfDE6mOP_SOnE,439
fontTools/ttLib/tables/_s_b_i_x.py,sha256=KF9acCLqBcYpg92h5vJBp5LsNT7c4MDKD4rocixRPKw,4994
fontTools/ttLib/tables/_t_r_a_k.py,sha256=7PLK_3VaZxxdgdn4wiPbMLvmUl0JZIWLWqgl-wvVvvQ,11711
fontTools/ttLib/tables/_v_h_e_a.py,sha256=ay73lNwQR72zZeyQ00ejfds2XmUp7sOLidnSzMvawUw,4598
fontTools/ttLib/tables/_v_m_t_x.py,sha256=933DMeQTI9JFfJ3TOjAFE6G8qHXJ7ZI2GukIKSQjaFU,519
fontTools/ttLib/tables/asciiTable.py,sha256=xJtOWy5lATZJILItU-A0dK4-jNXBByzyVWeO81FW8nc,657
fontTools/ttLib/tables/grUtils.py,sha256=T_WsEtpW60m9X6Rulko3bGI9aFdSC8Iyffwg_9ky0_I,2362
fontTools/ttLib/tables/otBase.py,sha256=CLtbHFqU-X0l07cj2jVE9BgZT28PYJXC1v_jBq7WUfw,54794
fontTools/ttLib/tables/otConverters.py,sha256=7_vqtb3-OV_iyHnVBrbQCWLg7lqkrX55F5PwRVDkck8,76270
fontTools/ttLib/tables/otData.py,sha256=i6KD2n1OqJvYgRlXV9-ya_QSmhYl8jcBQm61zCnBt90,203662
fontTools/ttLib/tables/otTables.py,sha256=Oe7YfSsfTBC_loA0KSwGtD7pZ5BSgInUSDvrPceRUY0,99690
fontTools/ttLib/tables/otTraverse.py,sha256=T1fnamNXqvFPUBspFm7aYsq_P0jUSJSy1ab9t48p_ZI,5681
fontTools/ttLib/tables/sbixGlyph.py,sha256=a-mCmO5EibN_He7QQohG06Qg-fCOHWiNFMAbCpxa25w,5945
fontTools/ttLib/tables/sbixStrike.py,sha256=Q1F4rFlj-SwUKFDJkcta1oz_b393dt9VYFOyxaFSc_o,6828
fontTools/ttLib/tables/table_API_readme.txt,sha256=E9lwGW1P_dGqy1FYBcYLVEDDmikbsqW4pUtpv1RKCJU,2839
fontTools/ttLib/tables/ttProgram.py,sha256=vkRtptH7QXD0Ng8LNzh-A_Ln27VPCxSJOXgW8878nSo,36482
fontTools/ttLib/ttCollection.py,sha256=1_wMr_ONgwPZh6wfbS_a7lNeE2IxUqd029TGObOsWs0,4088
fontTools/ttLib/ttFont.py,sha256=MkZX62eRAu_Qq7DFKLW5KBVbq3ZZJAGIiyUeRtFk_3g,41817
fontTools/ttLib/ttGlyphSet.py,sha256=JQZTE5TKXaVdceqYHZiC7gYanl_1mpHWjtW6GYjAtZ0,17966
fontTools/ttLib/ttVisitor.py,sha256=_Dkmz0tDs-5AFUR46kyg3Ku6BMPifrZzRU8-9UvXdz4,1057
fontTools/ttLib/woff2.py,sha256=-vdJKDAUUJlriaoOGnrtB8OwagiJ8argKeNhR4YzIHs,62601
fontTools/ttx.py,sha256=CpfOtEVTXAv79XM2jiWKrOFXHFtWSyAniIgC7b9tWf8,17756
fontTools/ufoLib/__init__.py,sha256=HbYdVqQUpL5szGEnkuBhRSOKgpWTQLuRADfy1te1Rv0,96811
fontTools/ufoLib/__pycache__/__init__.cpython-311.pyc,,
fontTools/ufoLib/__pycache__/converters.cpython-311.pyc,,
fontTools/ufoLib/__pycache__/errors.cpython-311.pyc,,
fontTools/ufoLib/__pycache__/etree.cpython-311.pyc,,
fontTools/ufoLib/__pycache__/filenames.cpython-311.pyc,,
fontTools/ufoLib/__pycache__/glifLib.cpython-311.pyc,,
fontTools/ufoLib/__pycache__/kerning.cpython-311.pyc,,
fontTools/ufoLib/__pycache__/plistlib.cpython-311.pyc,,
fontTools/ufoLib/__pycache__/pointPen.cpython-311.pyc,,
fontTools/ufoLib/__pycache__/utils.cpython-311.pyc,,
fontTools/ufoLib/__pycache__/validators.cpython-311.pyc,,
fontTools/ufoLib/converters.py,sha256=hPVFC0K1IPXG8tCGZQOIUXB4ILdiGPuzbgtqSiWHZn4,13442
fontTools/ufoLib/errors.py,sha256=pgJKS2A5RcsfQS2Z6Y_l3mIz62-VD_SrpIysKmywuYA,875
fontTools/ufoLib/etree.py,sha256=kTUP1EzN2wSXZ4jwAX8waNfKz52u7jc2qQ2LrqPYLBw,237
fontTools/ufoLib/filenames.py,sha256=wuXjT9VX3791TBJ0WL4lWhFQ6pInkcYML9Mgc2cdJsE,10764
fontTools/ufoLib/glifLib.py,sha256=lV42VMkIDuYL10L5lqLj6c4JNqSgvWGJZT05Kw2ddVI,74723
fontTools/ufoLib/kerning.py,sha256=VgE-xhGMAD2ipauleB-liNxwBuR_Ze5Jceb4XDi2Ob4,4354
fontTools/ufoLib/plistlib.py,sha256=GpWReRtO7S1JCv6gJnnuiYooo4Hwbgc2vagT041kFk8,1557
fontTools/ufoLib/pointPen.py,sha256=bU0-DLHrWKyutmwjw0tvhT-QPE-kmqs2Dqe0cflYgOk,250
fontTools/ufoLib/utils.py,sha256=3hKaFRkCBhmwP6NRst6H0d5ZKSXYTzKK8kNfGGwzEaw,2074
fontTools/ufoLib/validators.py,sha256=VtH8-SzQv16T6oVqYkRmdcwLBE8E0c9t0kiDgCMgqO4,31997
fontTools/unicode.py,sha256=a7460sU25TnVYGzrVl0uv0lI_pDbANZp8Jfmqx9tAag,1287
fontTools/unicodedata/Blocks.py,sha256=R0rSdM3NktyDMxtyLQJV4nvlTJylX9osWKkQQ_ZTEpQ,33216
fontTools/unicodedata/Mirrored.py,sha256=I6Fy7stp4cphy9JQ2zFZOynXvqIp3eKL6Clw7CTI8IU,9688
fontTools/unicodedata/OTTags.py,sha256=IAt8NXaZOhu5cuuks46DDX3E7Ovoqp-PMUQC-WJUPIs,1246
fontTools/unicodedata/ScriptExtensions.py,sha256=eIAXBnM9BbI5V_MWeA9I9Iv2rvgWi8mt8dCWN3cN1gY,29033
fontTools/unicodedata/Scripts.py,sha256=jCKY8wlKrSFmsFndzLegVS6vrhVGZ-S3T0dw2vO9Drg,133888
fontTools/unicodedata/__init__.py,sha256=dUWWA4Ga1Wud8XkCIWZ02NCHBLtX2XYYUJo4ZLHTd0U,9337
fontTools/unicodedata/__pycache__/Blocks.cpython-311.pyc,,
fontTools/unicodedata/__pycache__/Mirrored.cpython-311.pyc,,
fontTools/unicodedata/__pycache__/OTTags.cpython-311.pyc,,
fontTools/unicodedata/__pycache__/ScriptExtensions.cpython-311.pyc,,
fontTools/unicodedata/__pycache__/Scripts.cpython-311.pyc,,
fontTools/unicodedata/__pycache__/__init__.cpython-311.pyc,,
fontTools/varLib/__init__.py,sha256=E38iOYGLna0PhK-t7G33KNl36B11w_Lq7rd6KV5Pt8s,55753
fontTools/varLib/__main__.py,sha256=ykyZY5GG9IPDsPrUWiHgXEnsgKrQudZkneCTes6GUpU,101
fontTools/varLib/__pycache__/__init__.cpython-311.pyc,,
fontTools/varLib/__pycache__/__main__.cpython-311.pyc,,
fontTools/varLib/__pycache__/avar.cpython-311.pyc,,
fontTools/varLib/__pycache__/avarPlanner.cpython-311.pyc,,
fontTools/varLib/__pycache__/builder.cpython-311.pyc,,
fontTools/varLib/__pycache__/cff.cpython-311.pyc,,
fontTools/varLib/__pycache__/errors.cpython-311.pyc,,
fontTools/varLib/__pycache__/featureVars.cpython-311.pyc,,
fontTools/varLib/__pycache__/hvar.cpython-311.pyc,,
fontTools/varLib/__pycache__/interpolatable.cpython-311.pyc,,
fontTools/varLib/__pycache__/interpolatableHelpers.cpython-311.pyc,,
fontTools/varLib/__pycache__/interpolatablePlot.cpython-311.pyc,,
fontTools/varLib/__pycache__/interpolatableTestContourOrder.cpython-311.pyc,,
fontTools/varLib/__pycache__/interpolatableTestStartingPoint.cpython-311.pyc,,
fontTools/varLib/__pycache__/interpolate_layout.cpython-311.pyc,,
fontTools/varLib/__pycache__/iup.cpython-311.pyc,,
fontTools/varLib/__pycache__/merger.cpython-311.pyc,,
fontTools/varLib/__pycache__/models.cpython-311.pyc,,
fontTools/varLib/__pycache__/multiVarStore.cpython-311.pyc,,
fontTools/varLib/__pycache__/mutator.cpython-311.pyc,,
fontTools/varLib/__pycache__/mvar.cpython-311.pyc,,
fontTools/varLib/__pycache__/plot.cpython-311.pyc,,
fontTools/varLib/__pycache__/stat.cpython-311.pyc,,
fontTools/varLib/__pycache__/varStore.cpython-311.pyc,,
fontTools/varLib/avar.py,sha256=tRgKAUn_K5MTCSkB2MgPYYZ2U6Qo_Cg3jFQV0TDKFgc,9907
fontTools/varLib/avarPlanner.py,sha256=orjyFvg3YkC-slt7fgSEU1AGjLCkGgMEJ7hTRV6CqUA,28362
fontTools/varLib/builder.py,sha256=1k-N-rTwnZqQpzhNLBx2tqu2oYGG44sJSXKTCjAvIVM,6824
fontTools/varLib/cff.py,sha256=bl8rrPHHpwzUdZBY80_5JJLWYcXQOolhKKvTJiiU-Bs,23532
fontTools/varLib/errors.py,sha256=mXl-quT2Z75_t7Uwb6ug3VMhmbQjO841YNLeghwuY_s,7153
fontTools/varLib/featureVars.py,sha256=fBt7iJtohfsfqO7AULmYMD56hb3apCDXRgpR18pDoG8,26390
fontTools/varLib/hvar.py,sha256=Tm0ibxOtSVrBQeHiA5-idUQaJEjzOdUWxAQyKTGsOdQ,3808
fontTools/varLib/instancer/__init__.py,sha256=Dp4bxw_aOCdpECbQ4QPj8PujUzzpZAxQso4huWUx2J4,74220
fontTools/varLib/instancer/__main__.py,sha256=YN_tyJDdmLlH3umiLDS2ue0Zc3fSFexa9wCuk3Wuod0,109
fontTools/varLib/instancer/__pycache__/__init__.cpython-311.pyc,,
fontTools/varLib/instancer/__pycache__/__main__.cpython-311.pyc,,
fontTools/varLib/instancer/__pycache__/featureVars.cpython-311.pyc,,
fontTools/varLib/instancer/__pycache__/names.cpython-311.pyc,,
fontTools/varLib/instancer/__pycache__/solver.cpython-311.pyc,,
fontTools/varLib/instancer/featureVars.py,sha256=b3qtGCYVZ9fqkqcgFQUikYQBX_3_x0YgdrvvxIALbuU,7300
fontTools/varLib/instancer/names.py,sha256=vmHi7JZlh-N4amxKdaTJ-5DN9mDJ8Wnh_s9W1gJAQ4Y,15338
fontTools/varLib/instancer/solver.py,sha256=7noVYZ6gHrv4tV7kaXHn4iMKs_YP2YNssr4zgCHk4qI,11311
fontTools/varLib/interpolatable.py,sha256=8AXrhsnYY1z0hR6gskqYRYx8qcFsvUKmIIHZRpIOlAU,46430
fontTools/varLib/interpolatableHelpers.py,sha256=JnabttZY7sY9-QzdiqkgzQ_S5nG8k_O1TzLEmfNUvNo,11892
fontTools/varLib/interpolatablePlot.py,sha256=tUKFd8H9B2eD_GE6jV13J-dZkkIeLmk3ojAYrf-edsA,45644
fontTools/varLib/interpolatableTestContourOrder.py,sha256=Pbt0jW0LoVggIwrtADZ7HWK6Ftdoo1bjuWz0ost0HD0,3103
fontTools/varLib/interpolatableTestStartingPoint.py,sha256=f5MJ3mj8MctJCvDJwqmW1fIVOgovUMYAOela9HweaRU,4403
fontTools/varLib/interpolate_layout.py,sha256=tTPUes_K7MwooUO_wac9AeFEVgL1uGSz4ITYiOizaME,3813
fontTools/varLib/iup.c,sha256=Eld9QWThV5wgZlhHNvj15DILGRPArLAkU2071G1fSEY,846546
fontTools/varLib/iup.cp311-win_amd64.pyd,sha256=fuhlHSVoPAHTK_ULZPygMk9pdDDECCmITBCKoyVHS-M,129024
fontTools/varLib/iup.py,sha256=O_xPJOBECrNDbQqCC3e5xf9KsWXUd1i3BAp9Fl6Hv2Y,15474
fontTools/varLib/merger.py,sha256=V-B17poOYbbrRsfUYJbdqt46GtRfG833MKwtv9NOB3Q,62519
fontTools/varLib/models.py,sha256=ZqQb1Lapj5dCO8dwa3UTx1LsIpF0-GiDte32t_TMJJQ,23040
fontTools/varLib/multiVarStore.py,sha256=OvrrTaKrCZCXP40Rrv-2w416P-dNz3xE6gPOEyS3PrY,8558
fontTools/varLib/mutator.py,sha256=bUkUP27sxhEVkdljzbHNylHkj6Ob3FfQ9AoDYTRIwdo,19796
fontTools/varLib/mvar.py,sha256=Gf3q54ICH-E9oAwKYeIKUPLZabfjY0bUT4t220zLzYI,2489
fontTools/varLib/plot.py,sha256=BtozrcnKoEyCs0rGy7PZmrUvUNTmZT-5_sylW5PuJ28,7732
fontTools/varLib/stat.py,sha256=ScaVFIVpXTqA-F07umv_66GoxtcjaZ54MPLFvFK4s68,4960
fontTools/varLib/varStore.py,sha256=GWz-B1YcR-JnIh2aDmeQg621GDEBj9M4pKYcbZraA3w,24808
fontTools/voltLib/__init__.py,sha256=J7W0S2YED0GOqW9B_ZOhw-oL0-ltuRDYgAbrd8XHjqA,156
fontTools/voltLib/__main__.py,sha256=Ye6x5R_I9L1UuyWJsKyAajYMwV2B7OCdKsCRQ3leeJ4,6134
fontTools/voltLib/__pycache__/__init__.cpython-311.pyc,,
fontTools/voltLib/__pycache__/__main__.cpython-311.pyc,,
fontTools/voltLib/__pycache__/ast.cpython-311.pyc,,
fontTools/voltLib/__pycache__/error.cpython-311.pyc,,
fontTools/voltLib/__pycache__/lexer.cpython-311.pyc,,
fontTools/voltLib/__pycache__/parser.cpython-311.pyc,,
fontTools/voltLib/__pycache__/voltToFea.cpython-311.pyc,,
fontTools/voltLib/ast.py,sha256=DBxJygWUT5gE_tuxx0j2nmKFm3RvRSEF_rTXeKiisEo,13752
fontTools/voltLib/error.py,sha256=3TsaZBA82acFd2j5Beq3WUQTURTKM0zxOnUFGZovSNA,407
fontTools/voltLib/lexer.py,sha256=v9V4zdBO2VqVJG__IWrL8fv_CRURmh2eD_1UpbIJn9g,3467
fontTools/voltLib/parser.py,sha256=HS72gxtFzvcPSwEbUYj3E41CPK7ZqK9mSe0nLRxn-IY,26060
fontTools/voltLib/voltToFea.py,sha256=nS-OSlx_a-TngGICFNKyFxMhjqkV3OQLcvyzw4sQFyk,37460
fonttools-4.59.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
fonttools-4.59.0.dist-info/METADATA,sha256=d4GOxfoHGPMLqZHqeziF_YonUyYjeJuRcCWQgCZg81s,110052
fonttools-4.59.0.dist-info/RECORD,,
fonttools-4.59.0.dist-info/WHEEL,sha256=JLOMsP7F5qtkAkINx5UnzbFguf8CqZeraV8o04b0I8I,101
fonttools-4.59.0.dist-info/entry_points.txt,sha256=8kVHddxfFWA44FSD4mBpmC-4uCynQnkoz_9aNJb227Y,147
fonttools-4.59.0.dist-info/licenses/LICENSE,sha256=Ir74Bpfs-qF_l-YrmibfoSggvgVYPo3RKtFpskEnTJk,1093
fonttools-4.59.0.dist-info/licenses/LICENSE.external,sha256=sIKl-Gd1smQfAbzLi5yCkISB3l9QK7JUseE7_CqfMD0,20410
fonttools-4.59.0.dist-info/top_level.txt,sha256=rRgRylrXzekqWOsrhygzib12pQ7WILf7UGjqEwkIFDM,10
