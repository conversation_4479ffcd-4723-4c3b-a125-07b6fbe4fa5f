#!/usr/bin/env python3
"""
测试VWAP算法订单
"""

import sys
import asyncio
import logging
from datetime import datetime, timedelta

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

# 导入订单管理系统组件
from qlib_trading_system.trading.orders.manager import OrderManager, OrderRequest
from qlib_trading_system.trading.orders.models import OrderType, OrderSide, OrderPriority, AlgoOrderConfig
from qlib_trading_system.trading.orders.validator import RiskLimits


class MockBrokerAdapter:
    """模拟券商接口"""
    
    def __init__(self):
        self.account_info = {
            'available_cash': 100000.0,
            'total_value': 500000.0,
            'positions': {}
        }
        self.order_counter = 0
    
    async def get_account_info(self):
        return self.account_info.copy()
    
    async def place_market_order(self, symbol: str, quantity: int, side: str):
        await asyncio.sleep(0.1)
        self.order_counter += 1
        return {
            'success': True,
            'broker_order_id': f'BROKER_{self.order_counter}',
            'message': '订单已提交'
        }
    
    async def place_limit_order(self, symbol: str, quantity: int, price: float, side: str):
        await asyncio.sleep(0.1)
        self.order_counter += 1
        return {
            'success': True,
            'broker_order_id': f'BROKER_{self.order_counter}',
            'message': '订单已提交'
        }


async def mock_market_data_provider(symbol: str):
    """模拟市场数据提供者"""
    return {
        'symbol': symbol,
        'price': 12.50,
        'volume': 2000000,
        'bid_price': 12.49,
        'ask_price': 12.51,
        'bid_volume': 15000,
        'ask_volume': 15000,
        'timestamp': datetime.now()
    }


async def test_vwap_algorithm():
    """测试VWAP算法"""
    logger.info("=" * 60)
    logger.info("开始VWAP算法测试")
    logger.info("=" * 60)
    
    # 初始化组件
    broker_adapter = MockBrokerAdapter()
    
    risk_limits = RiskLimits(
        max_position_value=100000.0,
        max_single_position_pct=0.5,
        max_daily_trades=100,
        max_order_value=50000.0,
        min_order_value=1000.0
    )
    
    # 创建订单管理器
    order_manager = OrderManager(
        broker_adapter=broker_adapter,
        market_data_provider=mock_market_data_provider,
        risk_limits=risk_limits
    )
    
    # 启用测试模式
    order_manager.validator.test_mode = True
    
    # 启动订单管理器
    order_manager.start()
    
    try:
        # 测试VWAP算法订单
        logger.info("提交VWAP算法订单")
        start_time = datetime.now() + timedelta(seconds=3)
        end_time = start_time + timedelta(seconds=15)
        
        algo_config = AlgoOrderConfig(
            algo_type='VWAP',
            start_time=start_time,
            end_time=end_time,
            participation_rate=0.15,
            max_slice_size=600,
            min_slice_size=200,
            urgency=0.6,
            risk_aversion=0.4
        )
        
        request = OrderRequest(
            symbol='600000',
            side=OrderSide.BUY,
            order_type=OrderType.VWAP,
            quantity=2400,
            algo_config=algo_config,
            reason='测试VWAP算法'
        )
        
        success, message, order_id = await order_manager.submit_order(request)
        
        if success:
            logger.info(f"✓ VWAP订单提交成功: {order_id}")
            
            # 等待算法执行完成
            await asyncio.sleep(20)
            
            # 检查执行结果
            order = order_manager.get_order(order_id)
            if order:
                logger.info(f"VWAP执行结果:")
                logger.info(f"  订单状态: {order.status.value}")
                logger.info(f"  成交数量: {order.filled_quantity}/{order.quantity}")
                logger.info(f"  成交比例: {order.fill_ratio:.2%}")
                logger.info(f"  平均成交价: {order.avg_fill_price:.2f}")
                logger.info(f"  成交记录数: {len(order.fills)}")
                
                # 显示成交详情
                for i, fill in enumerate(order.fills):
                    logger.info(f"  成交{i+1}: 数量={fill.quantity}, 价格={fill.price:.2f}, 时间={fill.timestamp.strftime('%H:%M:%S')}")
            else:
                logger.error("未找到VWAP订单")
        else:
            logger.error(f"✗ VWAP订单提交失败: {message}")
        
        # 获取执行统计
        stats = order_manager.get_execution_stats()
        logger.info(f"\n执行统计:")
        for key, value in stats.items():
            logger.info(f"  {key}: {value}")
        
        # 生成报告
        today = datetime.now().strftime('%Y-%m-%d')
        report = order_manager.generate_daily_report(today)
        
        logger.info(f"\n日度报告:")
        logger.info(f"  总订单数: {report.total_orders}")
        logger.info(f"  完成订单数: {report.completed_orders}")
        logger.info(f"  总成交量: {report.total_volume}")
        logger.info(f"  总成交额: {report.total_value:.2f}")
        logger.info(f"  平均执行时间: {report.avg_execution_time:.2f}秒")
        
        # 导出报告
        json_file = order_manager.export_report(report, 'json', 'vwap_test_report.json')
        logger.info(f"✓ 报告导出成功: {json_file}")
        
        logger.info("\n" + "=" * 60)
        logger.info("VWAP算法测试完成")
        logger.info("=" * 60)
        
    except Exception as e:
        logger.error(f"测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 停止订单管理器
        order_manager.stop()


if __name__ == "__main__":
    asyncio.run(test_vwap_algorithm())