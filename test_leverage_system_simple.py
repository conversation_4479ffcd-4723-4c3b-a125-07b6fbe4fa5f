"""
杠杆控制系统简化测试
验证动态杠杆控制系统的核心功能
"""

import logging
import tempfile
import os
from datetime import datetime

# 导入杠杆控制系统组件
from qlib_trading_system.risk.controllers.leverage_controller import (
    DynamicLeverageController,
    LeverageConfig,
    AccountInfo,
    CapitalTier
)
from qlib_trading_system.risk.controllers.leverage_management_system import (
    LeverageManagementSystem,
    LeverageSystemConfig
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_leverage_controller():
    """测试杠杆控制器基本功能"""
    logger.info("=" * 50)
    logger.info("测试杠杆控制器基本功能")
    logger.info("=" * 50)
    
    # 创建临时数据库
    temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
    temp_db.close()
    
    try:
        # 创建杠杆控制器
        config = LeverageConfig(max_leverage=3.0, min_leverage=1.0)
        controller = DynamicLeverageController(config, temp_db.name)
        
        # 创建测试账户
        account = AccountInfo(
            total_capital=100000,
            available_capital=20000,
            used_capital=80000,
            current_drawdown=0.05,
            recent_win_rate=0.65,
            capital_tier=CapitalTier.SMALL
        )
        
        # 测试杠杆计算
        logger.info("1. 测试动态杠杆计算")
        decision = controller.calculate_dynamic_leverage(account, 0.25)
        logger.info(f"   推荐杠杆: {decision.recommended_leverage}x")
        logger.info(f"   风险等级: {decision.risk_level}")
        logger.info(f"   决策原因: {decision.reason}")
        
        # 测试杠杆限制
        logger.info("2. 测试杠杆限制获取")
        limits = controller.get_leverage_limits(account)
        logger.info(f"   最大杠杆: {limits['max']}x")
        logger.info(f"   警告杠杆: {limits['warning']}x")
        logger.info(f"   正常杠杆: {limits['normal']}x")
        
        # 测试合规检查
        logger.info("3. 测试杠杆合规检查")
        compliance = controller.check_leverage_compliance(2.0, account)
        logger.info(f"   2.0x杠杆合规: {compliance['is_compliant']}")
        
        compliance_violation = controller.check_leverage_compliance(5.0, account)
        logger.info(f"   5.0x杠杆合规: {compliance_violation['is_compliant']}")
        
        # 测试杠杆记录
        logger.info("4. 测试杠杆使用记录")
        controller.record_leverage_usage(
            symbol='000001.SZ',
            leverage_ratio=2.0,
            position_size=1000,
            market_value=15000,
            risk_level='MEDIUM',
            pnl=300
        )
        logger.info(f"   记录数量: {len(controller.leverage_history)}")
        
        # 测试杠杆调整
        logger.info("5. 测试杠杆调整")
        risk_assessment = {'risk_level': 'HIGH', 'overall_risk_score': 0.8}
        new_leverage, reason = controller.adjust_leverage(2.5, risk_assessment)
        logger.info(f"   调整结果: 2.5x -> {new_leverage}x")
        logger.info(f"   调整原因: {reason}")
        
        logger.info("✅ 杠杆控制器测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 杠杆控制器测试失败: {e}")
        return False
        
    finally:
        try:
            os.unlink(temp_db.name)
        except:
            pass

def test_leverage_management_system():
    """测试杠杆管理系统"""
    logger.info("=" * 50)
    logger.info("测试杠杆管理系统")
    logger.info("=" * 50)
    
    # 创建临时数据库
    temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
    temp_db.close()
    
    try:
        # 创建系统配置
        config = LeverageSystemConfig(
            leverage_config=LeverageConfig(max_leverage=3.0),
            monitoring_interval=5,
            auto_adjustment=True
        )
        
        # 创建杠杆管理系统
        system = LeverageManagementSystem(config, temp_db.name)
        
        # 创建测试账户
        account = AccountInfo(
            total_capital=150000,
            available_capital=30000,
            used_capital=120000,
            current_drawdown=0.08,
            recent_win_rate=0.62,
            capital_tier=CapitalTier.MEDIUM
        )
        
        # 测试系统启动
        logger.info("1. 测试系统启动")
        system.start_system()
        logger.info(f"   系统运行状态: {system.is_running}")
        
        # 测试杠杆计算
        logger.info("2. 测试持仓杠杆计算")
        decision = system.calculate_leverage_for_position(
            symbol='000001.SZ',
            account=account,
            stock_volatility=0.28
        )
        logger.info(f"   推荐杠杆: {decision.recommended_leverage}x")
        logger.info(f"   风险等级: {decision.risk_level}")
        
        # 测试组合监控
        logger.info("3. 测试组合杠杆监控")
        positions = [
            {
                'symbol': '000001.SZ',
                'shares': 1000,
                'current_price': 15.50,
                'leverage': 2.0,
                'cost_basis': 15.00
            },
            {
                'symbol': '000002.SZ',
                'shares': 800,
                'current_price': 22.30,
                'leverage': 1.8,
                'cost_basis': 22.00
            }
        ]
        
        monitoring_result = system.monitor_portfolio_leverage(positions)
        logger.info(f"   组合总市值: {monitoring_result['total_market_value']:,.0f}元")
        logger.info(f"   整体杠杆: {monitoring_result['overall_leverage']:.2f}x")
        logger.info(f"   风险等级: {monitoring_result['risk_level']}")
        
        # 测试系统报告
        logger.info("4. 测试系统报告生成")
        report = system.generate_system_report()
        logger.info(f"   系统状态: {'运行中' if report['system_status']['is_running'] else '已停止'}")
        logger.info(f"   总决策数: {report['system_stats']['total_decisions']}")
        
        # 测试系统检查
        logger.info("5. 测试系统健康检查")
        check_result = system.force_system_check()
        logger.info(f"   整体状态: {check_result['overall_status']}")
        
        # 停止系统
        logger.info("6. 测试系统停止")
        system.stop_system()
        logger.info(f"   系统运行状态: {system.is_running}")
        
        logger.info("✅ 杠杆管理系统测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 杠杆管理系统测试失败: {e}")
        return False
        
    finally:
        try:
            os.unlink(temp_db.name)
        except:
            pass

def test_leverage_scenarios():
    """测试不同杠杆场景"""
    logger.info("=" * 50)
    logger.info("测试不同杠杆场景")
    logger.info("=" * 50)
    
    # 创建临时数据库
    temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
    temp_db.close()
    
    try:
        controller = DynamicLeverageController(db_path=temp_db.name)
        
        # 测试场景1: 小资金高胜率
        logger.info("场景1: 小资金高胜率")
        account1 = AccountInfo(
            total_capital=50000,
            available_capital=10000,
            used_capital=40000,
            current_drawdown=0.02,
            recent_win_rate=0.8,
            capital_tier=CapitalTier.SMALL
        )
        
        decision1 = controller.calculate_dynamic_leverage(account1, 0.3)
        logger.info(f"   推荐杠杆: {decision1.recommended_leverage}x (预期较高)")
        
        # 测试场景2: 大资金低胜率
        logger.info("场景2: 大资金低胜率")
        account2 = AccountInfo(
            total_capital=1000000,
            available_capital=200000,
            used_capital=800000,
            current_drawdown=0.15,
            recent_win_rate=0.4,
            capital_tier=CapitalTier.LARGE
        )
        
        decision2 = controller.calculate_dynamic_leverage(account2, 0.2)
        logger.info(f"   推荐杠杆: {decision2.recommended_leverage}x (预期较低)")
        
        # 测试场景3: 高波动率股票
        logger.info("场景3: 高波动率股票")
        account3 = AccountInfo(
            total_capital=200000,
            available_capital=40000,
            used_capital=160000,
            current_drawdown=0.08,
            recent_win_rate=0.65,
            capital_tier=CapitalTier.MEDIUM
        )
        
        decision3 = controller.calculate_dynamic_leverage(account3, 0.5)  # 50%波动率
        logger.info(f"   推荐杠杆: {decision3.recommended_leverage}x (高波动率)")
        
        # 测试场景4: 低波动率股票
        logger.info("场景4: 低波动率股票")
        decision4 = controller.calculate_dynamic_leverage(account3, 0.1)  # 10%波动率
        logger.info(f"   推荐杠杆: {decision4.recommended_leverage}x (低波动率)")
        
        logger.info("✅ 杠杆场景测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 杠杆场景测试失败: {e}")
        return False
        
    finally:
        try:
            os.unlink(temp_db.name)
        except:
            pass

def main():
    """主测试函数"""
    logger.info("🚀 开始杠杆控制系统简化测试")
    logger.info(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("杠杆控制器", test_leverage_controller()))
    test_results.append(("杠杆管理系统", test_leverage_management_system()))
    test_results.append(("杠杆场景测试", test_leverage_scenarios()))
    
    # 汇总测试结果
    logger.info("=" * 50)
    logger.info("测试结果汇总")
    logger.info("=" * 50)
    
    passed = 0
    failed = 0
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    logger.info("=" * 50)
    logger.info(f"测试完成: {passed}个通过, {failed}个失败")
    
    if failed == 0:
        logger.info("🎉 所有测试通过！杠杆控制系统功能正常")
        logger.info("")
        logger.info("系统功能验证:")
        logger.info("✅ 动态杠杆计算 - 基于多因子模型")
        logger.info("✅ 杠杆限制管理 - 分级风险控制")
        logger.info("✅ 合规性检查 - 实时风险监控")
        logger.info("✅ 杠杆使用记录 - 历史数据管理")
        logger.info("✅ 智能杠杆调整 - 风险自适应")
        logger.info("✅ 组合杠杆监控 - 整体风险管理")
        logger.info("✅ 系统健康检查 - 运行状态监控")
        logger.info("✅ 多场景适配 - 不同资金规模和风险偏好")
        return True
    else:
        logger.error("❌ 部分测试失败，请检查系统配置")
        return False

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)