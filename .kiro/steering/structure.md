# Project Structure

## Root Level
```
qlib_trading_system/          # Main package
config/                       # Configuration files
.kiro/                       # Kiro IDE settings and specs
logs/                        # System logs (auto-generated)
main.py                      # System entry point
setup.py                     # Package installation
requirements.txt             # Dependencies
.env                         # Environment variables
```

## Core Package Structure
```
qlib_trading_system/
├── data/                    # Data layer
│   ├── collectors/          # Data source adapters
│   ├── processors/          # Real-time data processing
│   └── storage/             # Data storage management
├── models/                  # AI models layer
│   ├── stock_selection/     # Stock screening AI
│   ├── intraday_trading/    # T+0 trading AI
│   └── features/            # Feature engineering
├── trading/                 # Trading execution layer
│   ├── execution/           # Order execution
│   ├── orders/              # Order management
│   └── positions/           # Position management
├── risk/                    # Risk management layer
│   ├── controllers/         # Risk controllers
│   └── monitors/            # Risk monitoring
└── utils/                   # Utility modules
    ├── config/              # Configuration management
    └── logging/             # Logging system
```

## Key Architectural Patterns

### Layered Architecture
- **Data Layer**: Handles all data collection, processing, and storage
- **Model Layer**: Contains AI models and feature engineering
- **Trading Layer**: Manages order execution and position tracking
- **Risk Layer**: Implements risk controls and monitoring
- **Utils Layer**: Provides shared utilities and configuration

### Naming Conventions
- **Files**: Snake_case (e.g., `stock_selection.py`)
- **Classes**: PascalCase (e.g., `StockSelector`)
- **Functions/Variables**: Snake_case (e.g., `calculate_features`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `MAX_POSITION_SIZE`)

### Module Organization
- Each major component has its own directory with `__init__.py`
- Test files follow pattern `test_*.py` in same directory as source
- Configuration classes use Pydantic BaseSettings
- All modules should have proper docstrings in Chinese/English

### Data Flow
1. **Data Collection**: `data/collectors/` → Raw market data
2. **Data Processing**: `data/processors/` → Clean, structured data
3. **Feature Engineering**: `models/features/` → ML-ready features
4. **Model Inference**: `models/stock_selection|intraday_trading/` → Predictions
5. **Trading Execution**: `trading/execution/` → Market orders
6. **Risk Monitoring**: `risk/monitors/` → Continuous oversight

### Configuration Management
- Environment-specific settings in `.env`
- Structured configs in `config/` directory
- Runtime configuration via `utils/config/manager.py`
- Database connections centralized in `utils/database.py`