# Product Overview

## Qlib 双AI交易系统 (Dual AI Trading System)

A quantitative trading system built on the qlib framework, specifically designed for short-term trading in the Chinese stock market. The system employs a dual AI architecture with two core models:

1. **Stock Selection AI**: Identifies stocks with the strongest explosive potential within 3 months
2. **Intraday Trading AI**: Executes T+0 strategies for selected stocks

## Key Features

- **Dual AI Architecture**: Combines stock screening and intraday trading intelligence
- **High Return Focus**: Targets explosive stock identification and T+0 strategies
- **Small Capital Optimization**: Supports full position single stock + T+0 strategies
- **Real-time Processing**: Second-level tick data processing and signal generation
- **Risk Management**: Multi-layered risk control and circuit breaker mechanisms

## Target Market

- Chinese A-share market
- Short-term trading (intraday to 3-month horizons)
- Small to medium capital accounts (optimized for <500万 RMB)
- T+0 trading strategies with base position management

## Business Logic

The system operates on a two-stage approach:
1. Daily stock selection using multi-dimensional feature analysis
2. Intraday execution using real-time tick data and Level-2 market data
3. Risk-controlled position management with automatic stop-loss mechanisms