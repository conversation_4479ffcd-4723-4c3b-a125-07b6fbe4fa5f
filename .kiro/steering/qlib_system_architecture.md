---
inclusion: always
---

# Qlib交易系统架构指导原则

## 系统概述

Qlib交易系统是一个基于qlib框架的量化交易系统，专门为中国股票市场的短期交易设计。系统采用双AI架构，结合股票筛选和日内交易智能。

## 核心架构原则

### 1. 分层架构设计

```
应用层 (Application Layer)
├── 交易执行层 (Trading Execution)
├── 风险管理层 (Risk Management)
└── 用户接口层 (User Interface)

业务逻辑层 (Business Logic Layer)
├── 模型层 (Models Layer)
│   ├── 股票筛选AI (Stock Selection AI)
│   ├── 日内交易AI (Intraday Trading AI)
│   └── 特征工程 (Feature Engineering)
├── 策略层 (Strategy Layer)
└── 决策引擎 (Decision Engine)

数据层 (Data Layer)
├── 数据收集 (Data Collection)
├── 数据处理 (Data Processing)
└── 数据存储 (Data Storage)

基础设施层 (Infrastructure Layer)
├── 配置管理 (Configuration Management)
├── 日志系统 (Logging System)
└── 监控系统 (Monitoring System)
```

### 2. 模块命名规范

- **包名**: 使用snake_case，如`qlib_trading_system`
- **模块名**: 使用snake_case，如`stock_selection`
- **类名**: 使用PascalCase，如`StockSelector`
- **函数名**: 使用snake_case，如`calculate_features`
- **常量**: 使用UPPER_SNAKE_CASE，如`MAX_POSITION_SIZE`

### 3. 目录结构标准

```
qlib_trading_system/
├── data/                    # 数据层
│   ├── collectors/          # 数据收集器
│   ├── processors/          # 数据处理器
│   └── storage/             # 数据存储
├── models/                  # 模型层
│   ├── stock_selection/     # 股票筛选模型
│   ├── intraday_trading/    # 日内交易模型
│   └── features/            # 特征工程
├── trading/                 # 交易执行层
│   ├── execution/           # 订单执行
│   ├── orders/              # 订单管理
│   └── positions/           # 持仓管理
├── risk/                    # 风险管理层
│   ├── controllers/         # 风险控制器
│   └── monitors/            # 风险监控
└── utils/                   # 工具模块
    ├── config/              # 配置管理
    ├── logging/             # 日志系统
    └── database/            # 数据库工具
```

## 技术栈规范

### 核心框架
- **Qlib**: 主要量化交易框架 (>=0.0.2.dev20)
- **Python**: 3.8+ 版本要求

### 机器学习栈
- **LightGBM**: 主要梯度提升框架
- **PyTorch**: 深度学习模型
- **Scikit-learn**: 传统机器学习算法

### 数据处理
- **Pandas & NumPy**: 核心数据处理
- **TA-Lib**: 技术分析指标

### 数据库架构
- **ClickHouse**: 时序数据存储
- **Redis**: 实时缓存
- **MongoDB**: 文档存储

## 代码质量标准

### 1. 文档要求
- 所有公共类和函数必须有详细的docstring
- 使用中英文双语注释
- 复杂算法必须有实现说明

### 2. 错误处理
- 使用try-except进行异常处理
- 记录详细的错误日志
- 提供有意义的错误消息

### 3. 测试要求
- 每个模块必须有对应的测试文件
- 测试覆盖率不低于80%
- 包含单元测试和集成测试

### 4. 性能要求
- 预测延迟 < 100ms
- 内存使用 < 2GB
- 支持并发处理

## 配置管理原则

### 1. 配置分离
- 开发、测试、生产环境配置分离
- 敏感信息使用环境变量
- 配置文件使用JSON或YAML格式

### 2. 配置验证
- 启动时验证所有配置项
- 提供配置默认值
- 配置变更需要验证

## 日志和监控标准

### 1. 日志级别
- DEBUG: 详细调试信息
- INFO: 一般信息记录
- WARNING: 警告信息
- ERROR: 错误信息
- CRITICAL: 严重错误

### 2. 监控指标
- 系统性能指标
- 业务指标监控
- 错误率监控
- 响应时间监控

## 安全考虑

### 1. 数据安全
- 敏感数据加密存储
- API访问权限控制
- 数据传输加密

### 2. 系统安全
- 输入验证和清理
- SQL注入防护
- 访问日志记录