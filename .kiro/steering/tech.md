# Technology Stack

## Core Framework
- **Qlib**: Primary quantitative trading framework (>=0.0.2.dev20)
- **Python**: 3.8+ required for compatibility

## Machine Learning Stack
- **LightGBM**: Primary gradient boosting framework for stock selection
- **PyTorch**: Deep learning models for intraday trading
- **XGBoost & CatBoost**: Alternative boosting algorithms
- **Scikit-learn**: Traditional ML algorithms and preprocessing

## Data Processing
- **Pandas & NumPy**: Core data manipulation
- **TA-Lib**: Technical analysis indicators
- **AkShare**: Chinese financial data integration

## Database Architecture
- **ClickHouse**: Time-series data storage (tick data, OHLCV)
- **Redis**: Real-time caching and session management
- **MongoDB**: Document storage for configurations and logs

## Web & API Framework
- **FastAPI**: REST API endpoints
- **Uvicorn**: ASGI server
- **WebSockets**: Real-time data streaming

## Data Sources
- **iTick**: Primary data provider (recommended for cost-effectiveness)
- **JoinQuant**: Backup fundamental data
- **RiceQuant**: Alternative data source

## Common Commands

### System Initialization
```bash
# Full system setup
python main.py --init

# Install dependencies only
python main.py --install-deps

# Start system
python main.py
```

### Development
```bash
# Run tests
pytest qlib_trading_system/

# Code formatting
black qlib_trading_system/
flake8 qlib_trading_system/

# Validate setup
python validate_setup.py
```

### Database Management
```bash
# Test database connections
python -c "from qlib_trading_system.utils.database import db_manager; print(db_manager.test_connections())"
```

## Configuration Management
- Environment variables via `.env` file
- Pydantic-based configuration validation
- Separate configs for trading, data sources, models, and system settings