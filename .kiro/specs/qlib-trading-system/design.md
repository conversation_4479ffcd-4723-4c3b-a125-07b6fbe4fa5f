# 设计文档

## 概述

基于qlib框架的双AI交易系统，专门针对中国A股市场设计。系统采用微服务架构，将股票筛选和日内交易分离为两个独立的AI模型，通过消息队列进行通信。整体架构遵循qlib的设计理念，同时针对中国股市的T+1限制和交易时间特点进行了优化。

## 架构设计

### 整体架构

```mermaid
graph TB
    A[数据采集层] --> B[数据处理层]
    B --> C[AI模型层]
    C --> D[交易执行层]
    D --> E[风险管理层]
    E --> F[监控报告层]
    
    subgraph "AI模型层"
        C1[股票筛选AI]
        C2[日内交易AI]
    end
    
    subgraph "数据源"
        DS1[Wind/同花顺API]
        DS2[财经新闻]
        DS3[公告数据]
        DS4[技术指标]
    end
    
    DS1 --> A
    DS2 --> A
    DS3 --> A
    DS4 --> A
```

### 技术栈选择

- **核心框架**: Qlib (微软量化投资平台)
- **机器学习**: LightGBM, XGBoost, PyTorch
- **数据处理**: Pandas, NumPy, TA-Lib
- **数据库**: ClickHouse (时序数据), Redis (缓存)
- **消息队列**: RabbitMQ
- **Web框架**: FastAPI
- **前端**: React + Ant Design
- **部署**: Docker + Kubernetes

## 组件和接口设计

### 1. 数据采集层 (Data Collection Layer)

#### 数据源接口
```python
class DataSource:
    def get_stock_basic_info(self) -> pd.DataFrame
    def get_daily_price(self, symbols: List[str], start_date: str, end_date: str) -> pd.DataFrame
    def get_minute_price(self, symbol: str, date: str) -> pd.DataFrame
    def get_financial_data(self, symbols: List[str]) -> pd.DataFrame
    def get_news_sentiment(self, symbols: List[str]) -> pd.DataFrame
```

#### 数据预处理器
```python
class DataPreprocessor:
    def clean_price_data(self, data: pd.DataFrame) -> pd.DataFrame
    def calculate_technical_indicators(self, data: pd.DataFrame) -> pd.DataFrame
    def normalize_features(self, data: pd.DataFrame) -> pd.DataFrame
    def handle_missing_values(self, data: pd.DataFrame) -> pd.DataFrame
```

### 2. 股票筛选AI模型

#### 模型架构
- **特征工程**: 200+技术指标 + 基本面指标 + 情绪指标
- **模型类型**: LightGBM + LSTM混合模型
- **预测目标**: 未来3个月内实现100%+涨幅的概率（爆发股识别）
- **更新频率**: 每日收盘后重训练

#### 核心接口
```python
class StockSelectionModel:
    def __init__(self, config: Dict):
        self.feature_extractor = FeatureExtractor()
        self.model = LightGBMModel()
        self.lstm_model = LSTMModel()
        self.capital_mode = config.get('capital_mode', 'small')  # small/medium/large
    
    def extract_features(self, data: pd.DataFrame) -> pd.DataFrame
    def train(self, train_data: pd.DataFrame, labels: pd.Series) -> None
    def predict(self, data: pd.DataFrame) -> List[Tuple[str, float]]
    def get_best_stock_for_capital(self, capital: float) -> str  # 根据资金量返回最佳单股
    def get_explosive_stocks(self, n: int = 10) -> List[Tuple[str, float]]
```

#### 六维度特征工程体系（专注爆发股识别）

1. **基本面分析特征** (60个)
   - 业绩爆发指标: 业绩预增幅度、ROE加速度、净利润环比增速
   - 估值修复机会: PEG比率、相对估值折价、历史估值分位数
   - 成长性突破: 营收增长拐点、毛利率改善、现金流转正
   - 财务质量跃升: 资产负债率下降、应收账款周转改善

2. **估值分析特征** (40个)
   - 动态估值模型: DCF估值上修、PB-ROE模型、EV/EBITDA合理性
   - 相对估值优势: 行业估值分位数、同业对比折价率
   - 估值催化剂: 分拆上市预期、资产注入可能性、重估触发因子

3. **技术分析特征** (80个)
   - 爆发力技术指标: 价格加速度、成交量爆发系数、动量突破强度
   - 主力行为识别: 龙虎榜机构买入强度、北向资金持仓变化率、大宗交易溢价率
   - 突破形态确认: 多重阻力位突破、放量突破验证、技术形态完成度
   - 量价关系分析: 价量背离修复、异常成交量识别、资金流向集中度

4. **情绪分析特征** (50个)
   - 市场情绪指标: 恐慌指数VIX、投资者情绪指数、市场热度分布
   - 舆论发酵程度: 新闻热度爆发、社交媒体提及频率、搜索指数激增
   - 资金情绪表现: 换手率异常、成交额爆发、游资活跃度
   - 板块轮动信号: 概念股轮动强度、热点持续性、龙头效应强度

5. **风险管理评估** (30个)
   - 流动性风险: 日均成交额、买卖价差、冲击成本
   - 基本面风险: 财务造假概率、ST风险评分、退市风险指标
   - 技术面风险: 超买超卖程度、支撑阻力有效性、趋势稳定性
   - 系统性风险: Beta系数、相关性分析、行业集中度风险

6. **大盘走势预测** (40个)
   - 宏观经济指标: GDP增速、CPI走势、货币政策方向
   - 市场技术指标: 上证指数技术面、成交量变化、市场宽度指标
   - 资金流向分析: 北向资金净流入、融资融券余额、新发基金规模
   - 政策影响因子: 政策预期指数、监管态度、改革红利释放

### 3. 日内交易AI模型

#### 模型架构
- **输入**: 秒级tick数据 + 实时订单簿数据 + Level-2行情
- **模型类型**: Transformer + CNN + 传统技术分析三重融合架构
- **预测目标**: 未来1-30分钟价格方向和强度（多时间框架）
- **决策频率**: 秒级实时更新，支持高频做T

#### 核心接口
```python
class IntradayTradingModel:
    def __init__(self, config: Dict):
        self.transformer_model = TransformerModel()
        self.cnn_model = CNNModel()
        self.signal_generator = SignalGenerator()
        self.t_plus_zero_strategy = TPlusZeroStrategy()  # 做T策略
    
    def process_realtime_data(self, data: Dict) -> Dict
    def generate_t_signals(self, symbol: str, base_position: float) -> TradingSignal  # 基于底仓做T
    def calculate_t_size(self, signal: TradingSignal, base_position: float) -> float  # 做T数量
    def should_close_t_position(self, t_position: Position) -> bool  # AI判断T仓平仓时机
    def optimize_cost_basis(self, symbol: str, total_shares: int) -> float  # 成本优化
```

#### 信号生成策略
1. **多时间框架分析**
   - 1分钟: 入场时机
   - 5分钟: 趋势确认
   - 15分钟: 大方向判断

2. **技术信号**
   - 突破信号: 支撑阻力位突破
   - 反转信号: 背离、锤子线等
   - 趋势信号: 均线排列、MACD金叉死叉

3. **量价分析**
   - 异常成交量
   - 价量背离
   - 大单流向

### 4. 交易执行层

#### 订单管理系统
```python
class OrderManager:
    def __init__(self, broker_api: BrokerAPI):
        self.broker = broker_api
        self.order_queue = Queue()
        self.position_manager = PositionManager()
    
    def place_order(self, order: Order) -> str
    def cancel_order(self, order_id: str) -> bool
    def get_order_status(self, order_id: str) -> OrderStatus
    def update_positions(self) -> None
```

#### 券商接口适配
```python
class BrokerAdapter:
    def connect(self) -> bool
    def get_account_info(self) -> Account
    def get_positions(self) -> List[Position]
    def place_market_order(self, symbol: str, quantity: int, side: str) -> str
    def place_limit_order(self, symbol: str, quantity: int, price: float, side: str) -> str
```

### 5. 风险管理层

#### 风险控制器
```python
class RiskController:
    def __init__(self, config: RiskConfig):
        self.max_position_size = config.max_position_size
        self.max_daily_loss = config.max_daily_loss
        self.max_drawdown = config.max_drawdown
    
    def check_order_risk(self, order: Order, account: Account) -> bool
    def check_position_risk(self, positions: List[Position]) -> List[str]
    def calculate_var(self, positions: List[Position]) -> float
    def should_stop_trading(self, account: Account) -> bool
```

#### 风险指标监控（高风险高收益模式）
- 实时PnL监控
- 激进回撤控制（允许30%最大回撤）
- 集中持仓策略（单股最高30%仓位）
- 行业集中投资（允许80%资金投入热点行业）
- 波动率目标管理（追求高波动率标的）
- 杠杆使用监控（适度使用融资融券）

## 数据模型设计

### 股票基础信息
```python
@dataclass
class StockInfo:
    symbol: str
    name: str
    industry: str
    market_cap: float
    listing_date: datetime
    is_st: bool
    is_suspended: bool
```

### 交易信号
```python
@dataclass
class TradingSignal:
    symbol: str
    signal_type: str  # 'BUY', 'SELL', 'HOLD'
    confidence: float  # 0-1
    target_price: float
    stop_loss: float
    expected_holding_time: int  # minutes
    timestamp: datetime
```

### 订单模型
```python
@dataclass
class Order:
    order_id: str
    symbol: str
    side: str  # 'BUY', 'SELL'
    order_type: str  # 'MARKET', 'LIMIT'
    quantity: int
    price: Optional[float]
    status: str
    create_time: datetime
    fill_time: Optional[datetime]
```

## 错误处理设计

### 异常类型定义
```python
class TradingSystemException(Exception):
    pass

class DataException(TradingSystemException):
    pass

class ModelException(TradingSystemException):
    pass

class BrokerException(TradingSystemException):
    pass

class RiskException(TradingSystemException):
    pass
```

### 错误处理策略
1. **数据异常**: 使用历史数据填补，记录异常日志
2. **模型异常**: 切换到备用模型，发送警报
3. **交易异常**: 取消未成交订单，保护现有仓位
4. **网络异常**: 自动重连，缓存未发送请求

### 容错机制
- 多数据源冗余
- 模型热备份
- 断线重连
- 状态持久化

## 测试策略

### 单元测试
- 数据处理函数测试
- 模型预测准确性测试
- 风险控制逻辑测试
- API接口测试

### 集成测试
- 端到端交易流程测试
- 多组件协作测试
- 异常场景测试

### 回测框架
```python
class BacktestEngine:
    def __init__(self, start_date: str, end_date: str):
        self.start_date = start_date
        self.end_date = end_date
        self.portfolio = Portfolio()
        self.performance_analyzer = PerformanceAnalyzer()
    
    def run_backtest(self, strategy: Strategy) -> BacktestResult
    def calculate_metrics(self) -> Dict[str, float]
    def generate_report(self) -> str
```

### 性能指标（高收益导向）
- 年化收益率（目标1000%+）
- 最大单股收益率（目标200%+）
- 爆发股识别准确率（目标50%+）
- 最大回撤（可接受30%）
- 持仓集中度
- 热点捕捉速度
- 3个月滚动收益率

## 部署架构

### 微服务部署
```yaml
services:
  data-collector:
    image: trading-system/data-collector:latest
    replicas: 2
    
  stock-selector:
    image: trading-system/stock-selector:latest
    replicas: 1
    
  intraday-trader:
    image: trading-system/intraday-trader:latest
    replicas: 3
    
  risk-manager:
    image: trading-system/risk-manager:latest
    replicas: 2
    
  web-api:
    image: trading-system/web-api:latest
    replicas: 2
```

### 监控和日志
- Prometheus + Grafana 监控
- ELK Stack 日志分析
- 钉钉/微信告警通知
- 性能指标实时展示

这个设计充分考虑了中国股市的特点，包括T+1交易限制、涨跌停板制度、以及A股特有的市场行为模式。系统具备高可用性、可扩展性和容错能力。
#
# 高风险高收益策略优化

### 爆发股筛选策略
1. **多因子爆发模型**
   - 技术爆发因子权重: 40%
   - 基本面催化因子权重: 30% 
   - 市场情绪因子权重: 20%
   - 资金流向因子权重: 10%

2. **动态阈值调整**
   - 牛市: 降低筛选标准，增加候选股票
   - 熊市: 提高筛选标准，专注超强个股
   - 震荡市: 重点关注题材轮动机会

3. **极致集中投资策略（可配置）**
   - 小资金模式: 全仓单股（100%仓位）
   - 中等资金模式: 2-3只股票分仓（每股30-50%）
   - 大资金模式: 5-10只股票分散（每股10-30%）
   - 根据资金量自动调整仓位分配策略

### 小资金激进交易策略（10万资金模式）
1. **全仓单股+做T策略**
   - 基于股票筛选AI选出的最佳标的全仓买入作为底仓
   - 利用日内交易AI进行T+0操作降低成本
   - 底仓长期持有等待爆发，T仓快进快出
   - 每日做T收益用于复利增长

2. **做T操作细节**
   - 早盘低点买入T仓（10-30%资金）
   - 盘中高点卖出T仓获利
   - 如遇大跌可加仓摊低成本
   - 如遇大涨可减仓锁定利润

3. **资金管理策略**
   - 底仓: 70-80%资金长期持有
   - T仓: 20-30%资金日内操作
   - 现金: 保留少量现金应对极端情况
   - 盈利再投: 做T盈利全部用于加仓

4. **AI驱动的动态决策策略**
   - 底仓决策: 完全由股票筛选AI判断，当AI预测爆发力下降或出现更好标的时才清仓
   - T仓决策: 完全由日内交易AI判断最佳买卖时机，不设固定止盈止损点
   - 风险兜底: 仅在极端情况下（如停牌、ST等）启用强制止损
   - 持仓切换: 当AI发现更优标的时，可进行底仓切换

### 风险控制调整
1. **整体风险预算**
   - 年度最大回撤目标: 30%（vs传统10-15%）
   - 单月最大亏损: 15%
   - 连续亏损3个月时降低仓位

2. **集中度风险管理**
   - 虽然允许集中投资，但要分散到不同催化剂
   - 避免所有持股都依赖同一个题材或事件
   - 定期评估持股相关性

3. **流动性风险控制**
   - 重点关注日成交额>5亿的股票
   - 避免ST股票和退市风险股票
   - 保持20%现金仓位应对机会和风险
### 小资金
专用配置系统

#### 资金模式配置
```python
@dataclass
class CapitalConfig:
    total_capital: float
    mode: str  # 'small' (<50万), 'medium' (50-500万), 'large' (>500万)
    max_stocks: int  # 最大持股数量
    base_position_ratio: float  # 底仓比例
    t_position_ratio: float  # 做T仓位比例
    cash_reserve_ratio: float  # 现金储备比例

# 小资金配置示例
small_capital_config = CapitalConfig(
    total_capital=100000,  # 10万
    mode='small',
    max_stocks=1,  # 只持有1只股票
    base_position_ratio=0.75,  # 75%做底仓
    t_position_ratio=0.20,  # 20%做T
    cash_reserve_ratio=0.05  # 5%现金
)
```

#### 全仓单股策略实现
```python
class SingleStockStrategy:
    def __init__(self, config: CapitalConfig):
        self.config = config
        self.current_stock = None
        self.base_position = 0
        self.t_position = 0
    
    def select_target_stock(self) -> str:
        """选择目标股票（全仓投入）"""
        candidates = self.stock_selector.get_explosive_stocks(n=5)
        # 选择最具爆发潜力且适合做T的股票
        return self.evaluate_t_suitability(candidates)
    
    def execute_base_position(self, symbol: str) -> None:
        """建立底仓"""
        base_amount = self.config.total_capital * self.config.base_position_ratio
        self.place_order(symbol, base_amount, 'base')
    
    def execute_t_trading(self, symbol: str) -> None:
        """执行做T操作"""
        t_amount = self.config.total_capital * self.config.t_position_ratio
        signal = self.intraday_model.generate_t_signals(symbol, self.base_position)
        
        if signal.signal_type == 'BUY_T':
            self.place_order(symbol, t_amount, 't_buy')
        elif signal.signal_type == 'SELL_T':
            self.place_order(symbol, self.t_position, 't_sell')
    
    def evaluate_t_suitability(self, candidates: List[Tuple[str, float]]) -> str:
        """评估股票做T适合度"""
        # 考虑因素：日内波动率、成交量、价格区间等
        pass
```

#### 做T策略优化
```python
class TPlusZeroOptimizer:
    def __init__(self):
        self.daily_t_profit = 0
        self.t_success_rate = 0
        self.cost_basis = 0
    
    def calculate_optimal_t_timing(self, symbol: str) -> Dict:
        """计算最佳做T时机"""
        return {
            'buy_signals': self.identify_intraday_lows(symbol),
            'sell_signals': self.identify_intraday_highs(symbol),
            'volume_profile': self.analyze_volume_distribution(symbol)
        }
    
    def optimize_cost_reduction(self, base_cost: float, t_profits: List[float]) -> float:
        """通过做T优化持仓成本"""
        total_t_profit = sum(t_profits)
        shares = self.base_position
        return base_cost - (total_t_profit / shares)
```

这样调整后，系统完全适配你的10万资金全仓单股+做T的策略需求。#
## AI驱动的智能决策系统

#### 股票筛选AI决策逻辑
```python
class StockSelectionDecisionEngine:
    def __init__(self):
        self.current_stock_score = 0
        self.holding_days = 0
        self.score_threshold = 0.7  # 爆发力评分阈值
    
    def should_hold_current_stock(self, current_stock: str) -> bool:
        """AI判断是否继续持有当前股票"""
        current_score = self.model.predict_explosive_potential(current_stock)
        
        # 如果爆发力评分仍然很高，继续持有
        if current_score > self.score_threshold:
            return True
            
        # 如果基本面发生重大变化，考虑清仓
        if self.detect_fundamental_change(current_stock):
            return False
            
        return current_score > 0.5  # 中等评分继续观察
    
    def find_better_alternative(self, current_stock: str) -> Optional[str]:
        """寻找更好的替代标的"""
        current_score = self.get_current_score(current_stock)
        candidates = self.get_explosive_stocks(n=10)
        
        for stock, score in candidates:
            if stock != current_stock and score > current_score * 1.2:  # 新标的评分要显著更高
                return stock
        return None
    
    def execute_stock_switch(self, from_stock: str, to_stock: str) -> None:
        """执行股票切换"""
        # 清仓当前股票
        self.clear_position(from_stock)
        # 建仓新股票
        self.build_position(to_stock)
```

#### 日内交易AI决策逻辑
```python
class IntradayDecisionEngine:
    def __init__(self):
        self.model_confidence_threshold = 0.6
        self.min_profit_expectation = 0.01  # 最小预期收益1%
    
    def generate_t_decision(self, symbol: str, current_price: float) -> TradingDecision:
        """AI生成做T决策"""
        # 预测未来30分钟价格走势
        price_prediction = self.model.predict_price_movement(symbol, timeframe=30)
        confidence = price_prediction.confidence
        expected_return = price_prediction.expected_return
        
        if confidence < self.model_confidence_threshold:
            return TradingDecision('HOLD', 0, '模型信心不足')
        
        if expected_return > self.min_profit_expectation:
            return TradingDecision('BUY_T', self.calculate_t_size(), f'预期收益{expected_return:.2%}')
        elif expected_return < -self.min_profit_expectation:
            return TradingDecision('SELL_T', self.get_t_position_size(), f'预期下跌{abs(expected_return):.2%}')
        else:
            return TradingDecision('HOLD', 0, '预期收益不足')
    
    def should_close_t_position(self, t_position: Position) -> bool:
        """AI判断是否平仓T仓位"""
        current_profit = t_position.unrealized_pnl_pct
        
        # 预测继续持有的收益预期
        future_prediction = self.model.predict_price_movement(
            t_position.symbol, 
            timeframe=15
        )
        
        # 如果AI预测反转，立即平仓
        if future_prediction.direction != t_position.direction:
            return True
            
        # 如果已有盈利但AI预测收益递减，考虑平仓
        if current_profit > 0 and future_prediction.expected_return < 0.005:
            return True
            
        return False
```

#### 风险兜底机制
```python
class EmergencyRiskControl:
    def __init__(self):
        self.emergency_triggers = {
            'suspension': True,  # 停牌
            'st_risk': True,     # ST风险
            'limit_down_3_days': True,  # 连续3天跌停
            'volume_dry_up': True,      # 成交量枯竭
        }
    
    def check_emergency_exit(self, symbol: str) -> bool:
        """检查是否需要紧急退出"""
        if self.is_suspended(symbol):
            return True
        if self.has_st_risk(symbol):
            return True
        if self.consecutive_limit_down(symbol, days=3):
            return True
        if self.volume_too_low(symbol):
            return True
        return False
    
    def override_ai_decision(self, ai_decision: str, symbol: str) -> str:
        """在紧急情况下覆盖AI决策"""
        if self.check_emergency_exit(symbol):
            return 'FORCE_EXIT'
        return ai_decision
```

这样调整后，系统完全依赖AI的智能判断，只在极端风险情况下才会强制干预，更符合你的AI驱动交易理念。### 强化
特征工程实现

#### 主力行为识别模块
```python
class InstitutionalBehaviorAnalyzer:
    def __init__(self):
        self.dragon_tiger_analyzer = DragonTigerAnalyzer()
        self.north_bound_tracker = NorthBoundTracker()
        self.block_trade_monitor = BlockTradeMonitor()
    
    def add_institutional_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """增加主力行为识别特征"""
        # 龙虎榜机构买入强度
        data['institutional_buy_ratio'] = data['机构买入额'] / data['总成交额']
        data['institutional_buy_intensity'] = data['institutional_buy_ratio'].rolling(5).mean()
        
        # 北向资金持仓变化率
        data['north_bound_change'] = data['北向持仓'].pct_change(5)
        data['north_bound_acceleration'] = data['north_bound_change'].diff()
        
        # 大宗交易溢价率
        data['block_trade_premium'] = (data['大宗成交价'] - data['收盘价']) / data['收盘价']
        data['block_trade_volume_ratio'] = data['大宗成交量'] / data['总成交量']
        
        # 游资活跃度指标
        data['hot_money_activity'] = self.calculate_hot_money_activity(data)
        
        # 机构建仓信号
        data['institution_accumulation'] = self.detect_accumulation_pattern(data)
        
        return data
    
    def calculate_explosive_potential(self, features: pd.DataFrame) -> float:
        """计算爆发潜力综合评分"""
        weights = {
            'fundamental': 0.25,    # 基本面权重
            'valuation': 0.20,      # 估值权重  
            'technical': 0.25,      # 技术面权重
            'sentiment': 0.15,      # 情绪权重
            'risk': -0.10,          # 风险权重（负向）
            'market': 0.05          # 大盘权重
        }
        
        score = 0
        for dimension, weight in weights.items():
            dimension_score = self.calculate_dimension_score(features, dimension)
            score += dimension_score * weight
        
        return min(max(score, 0), 1)  # 限制在0-1范围内
```

#### 实时行情处理系统
```python
class RealtimeDataProcessor:
    def __init__(self):
        self.tick_buffer = deque(maxlen=3600)  # 保存1小时tick数据
        self.orderbook_analyzer = OrderBookAnalyzer()
        self.level2_processor = Level2Processor()
    
    def process_tick_data(self, tick: TickData) -> Dict:
        """处理秒级tick数据"""
        self.tick_buffer.append(tick)
        
        # 计算实时技术指标
        realtime_indicators = {
            'price_momentum': self.calculate_price_momentum(),
            'volume_surge': self.detect_volume_surge(),
            'bid_ask_pressure': self.analyze_bid_ask_pressure(),
            'large_order_flow': self.track_large_orders(),
        }
        
        return realtime_indicators
    
    def analyze_level2_data(self, level2: Level2Data) -> Dict:
        """分析Level-2行情数据"""
        return {
            'order_imbalance': self.calculate_order_imbalance(level2),
            'support_resistance': self.identify_dynamic_levels(level2),
            'institutional_footprint': self.detect_institutional_orders(level2),
            'liquidity_depth': self.measure_liquidity_depth(level2)
        }
```

#### 多模型信号融合系统
```python
class SignalFusionEngine:
    def __init__(self):
        self.transformer_model = TransformerModel()
        self.cnn_model = CNNModel() 
        self.ta_analyzer = TechnicalAnalyzer()
        self.confidence_tracker = ConfidenceTracker()
    
    def fuse_signals(self, symbol: str, timeframe: int) -> TradingSignal:
        """多模型信号融合"""
        # 获取各模型信号
        transformer_signal = self.transformer_model.predict(symbol, timeframe)
        cnn_signal = self.cnn_model.predict(symbol, timeframe)
        ta_signal = self.ta_analyzer.generate_signal(symbol)
        
        # 动态权重分配（基于模型置信度+市场波动率）
        volatility_factor = self.calculate_market_volatility()
        model_performances = self.confidence_tracker.get_recent_performance()
        
        weights = {
            'transformer': 0.5 * transformer_signal.confidence * model_performances['transformer'],
            'cnn': 0.3 * cnn_signal.confidence * model_performances['cnn'],
            'ta': 0.2 * (1 - volatility_factor) * model_performances['ta']
        }
        
        # 归一化权重
        total_weight = sum(weights.values())
        weights = {k: v/total_weight for k, v in weights.items()}
        
        # 生成综合信号
        fused_strength = (
            weights['transformer'] * transformer_signal.strength +
            weights['cnn'] * cnn_signal.strength +
            weights['ta'] * ta_signal.strength
        )
        
        # 信号过滤和确认
        if abs(fused_strength) < 0.6:  # 信号强度不足
            return TradingSignal('HOLD', 0, 0, '信号强度不足')
        
        direction = 1 if fused_strength > 0.7 else -1 if fused_strength < -0.7 else 0
        confidence = min(sum(s.confidence for s in [transformer_signal, cnn_signal, ta_signal]) / 3, 1.0)
        
        return TradingSignal(
            direction=direction,
            strength=abs(fused_strength),
            confidence=confidence,
            reason=f'融合信号: T={transformer_signal.strength:.2f}, C={cnn_signal.strength:.2f}, TA={ta_signal.strength:.2f}'
        )
```

#### 极端行情熔断机制
```python
class CircuitBreaker:
    def __init__(self):
        self.limit_down_count = 0
        self.volatility_index = 0
        self.market_stress_level = 0
        self.black_swan_detector = BlackSwanDetector()
    
    def check_market_condition(self, market_data: Dict) -> str:
        """监测市场极端状态"""
        # 大盘暴跌检测
        if market_data['index_drop'] > 5:
            self.market_stress_level = 3
            return 'RED_ALERT'
        
        # 个股连续跌停检测    
        if self.limit_down_count >= 3:
            return 'STOCK_ALERT'
        
        # 市场流动性枯竭检测
        if market_data['market_volume'] < market_data['avg_volume'] * 0.3:
            return 'LIQUIDITY_ALERT'
        
        # 黑天鹅事件检测
        if self.black_swan_detector.detect_anomaly(market_data):
            return 'BLACK_SWAN'
        
        return 'NORMAL'
    
    def activate_defense(self, alert_level: str) -> Dict:
        """激活防御策略"""
        defense_actions = {
            'RED_ALERT': {
                'allow_new_position': False,
                't_direction': 'SELL_ONLY',
                'reduce_position': 0.5,
                'max_single_trade': 0.05
            },
            'STOCK_ALERT': {
                'force_liquidation': True,
                'emergency_exit': True
            },
            'LIQUIDITY_ALERT': {
                'reduce_trade_size': 0.3,
                'increase_spread_tolerance': 2.0
            },
            'BLACK_SWAN': {
                'full_defensive_mode': True,
                'stop_all_trading': True
            }
        }
        
        return defense_actions.get(alert_level, {})
```

#### 动态杠杆控制系统
```python
class DynamicLeverageController:
    def __init__(self):
        self.base_leverage_map = {
            'small': 1.5,    # <50万
            'medium': 1.3,   # 50-500万  
            'large': 1.1     # >500万
        }
    
    def calculate_dynamic_leverage(self, account: Account, stock_volatility: float) -> float:
        """动态计算杠杆倍数"""
        # 基础杠杆系数
        base_leverage = self.base_leverage_map.get(account.capital_tier, 1.0)
        
        # 波动率调整（波动越大杠杆越高，追求高收益）
        volatility_factor = 1 + min(stock_volatility * 2, 0.8)
        
        # 账户风险调整
        risk_factor = max(1 - (account.current_drawdown / 30), 0.5)
        
        # 市场环境调整
        market_factor = self.get_market_environment_factor()
        
        # 胜率调整
        win_rate_factor = 1 + (account.recent_win_rate - 0.5) * 0.5
        
        final_leverage = base_leverage * volatility_factor * risk_factor * market_factor * win_rate_factor
        
        return round(min(max(final_leverage, 1.0), 3.0), 2)  # 限制在1-3倍之间
    
    def get_market_environment_factor(self) -> float:
        """获取市场环境因子"""
        market_trend = self.analyze_market_trend()
        if market_trend == 'BULL':
            return 1.2  # 牛市提高杠杆
        elif market_trend == 'BEAR':
            return 0.8  # 熊市降低杠杆
        else:
            return 1.0  # 震荡市保持中性
```

#### 黑天鹅事件应对模块
```python
class BlackSwanDefender:
    def __init__(self):
        self.news_analyzer = NewsAnalyzer()
        self.anomaly_detector = AnomalyDetector()
        self.emergency_protocols = EmergencyProtocols()
    
    def detect_anomalies(self, news_stream: List[str], price_movement: Dict) -> bool:
        """检测异常事件"""
        # NLP检测重大利空新闻
        negative_keywords = ['立案调查', 'ST', '退市', '减持', '财务造假', '监管处罚']
        news_risk_score = self.news_analyzer.calculate_risk_score(news_stream, negative_keywords)
        
        # 价格异常检测
        price_anomaly = self.detect_price_anomaly(price_movement)
        
        # 成交量异常检测
        volume_anomaly = self.detect_volume_anomaly(price_movement)
        
        # 综合判断
        total_risk_score = news_risk_score + price_anomaly + volume_anomaly
        
        return total_risk_score > 0.7
    
    def execute_defense(self, position: Position, anomaly_type: str) -> Dict:
        """执行防御策略"""
        if anomaly_type == 'NEWS_NEGATIVE':
            if position.unrealized_pnl > 0:
                return {'action': 'PARTIAL_CLOSE', 'ratio': 0.5}
            else:
                return {'action': 'FULL_LIQUIDATION'}
        
        elif anomaly_type == 'PRICE_CRASH':
            return {'action': 'EMERGENCY_EXIT', 'method': 'MARKET_ORDER'}
        
        elif anomaly_type == 'VOLUME_DRY_UP':
            return {'action': 'GRADUAL_EXIT', 'time_limit': 30}  # 30分钟内逐步退出
        
        return {'action': 'MONITOR'}
```

这个强化版设计完全基于你的建议，加入了六维度特征工程、实时秒级行情处理、多模型信号融合、极端行情熔断等核心功能，更适合追求高收益的激进交易策略。