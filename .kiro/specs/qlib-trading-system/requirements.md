# 需求文档

## 介绍

基于qlib框架构建的双AI交易系统，专门针对中国股市的短线交易。系统包含两个核心AI模型：股票筛选模型（识别3个月内爆发力最强的股票）和日内交易模型（执行T+0策略）。系统旨在通过AI驱动的量化策略实现稳定的短线收益。

## 需求

### 需求1：股票筛选AI模型

**用户故事：** 作为量化交易员，我希望有一个AI模型能够从A股市场中筛选出未来3个月内具有最强爆发潜力的股票，以便我能专注于最有价值的交易标的。

#### 验收标准

1. 当市场开盘前，系统应当分析全A股数据并输出排名前20的潜力股票
2. 当模型预测时，系统应当考虑技术指标、基本面数据、市场情绪和宏观经济因素
3. 当历史回测时，系统应当在3个月时间窗口内实现至少60%的预测准确率
4. 当数据更新时，系统应当每日重新训练模型以适应市场变化
5. 如果股票出现重大风险事件，系统应当自动从候选列表中剔除该股票

### 需求2：日内交易AI模型

**用户故事：** 作为短线交易者，我希望有一个AI模型能够在选定的股票上执行精准的T+0交易策略，以便在日内波动中获取收益。

#### 验收标准

1. 当接收到筛选股票列表时，系统应当对每只股票进行实时价格监控和交易信号生成
2. 当检测到买入信号时，系统应当在5秒内执行买入操作
3. 当检测到卖出信号时，系统应当在3秒内执行卖出操作
4. 当单日交易次数超过设定阈值时，系统应当停止该股票的交易以控制风险
5. 如果账户资金不足，系统应当暂停新的买入操作并发出警告

### 需求3：风险管理系统

**用户故事：** 作为投资者，我希望系统具备完善的风险控制机制，以便在追求收益的同时保护我的资金安全。

#### 验收标准

1. 当单只股票亏损达到2%时，系统应当强制止损
2. 当日总亏损达到账户资金的1%时，系统应当停止所有交易
3. 当检测到异常交易行为时，系统应当立即暂停交易并发送警报
4. 当市场出现极端波动时，系统应当自动切换到保守模式
5. 如果连续3天出现亏损，系统应当降低交易频率和仓位大小

### 需求4：数据管理和回测系统

**用户故事：** 作为策略开发者，我希望系统能够高效管理历史数据并提供完整的回测功能，以便我能够验证和优化交易策略。

#### 验收标准

1. 当系统启动时，应当自动从数据源获取最新的股票数据
2. 当进行回测时，系统应当支持自定义时间范围和参数设置
3. 当回测完成时，系统应当生成详细的性能报告包括收益率、夏普比率、最大回撤等指标
4. 当数据缺失时，系统应当使用合理的插值方法填补空缺
5. 如果数据质量异常，系统应当发出警告并提供数据清洗建议

### 需求5：实时监控和报告系统

**用户故事：** 作为交易监管者，我希望能够实时监控系统运行状态和交易表现，以便及时发现问题并做出调整。

#### 验收标准

1. 当系统运行时，应当提供实时的交易状态监控界面
2. 当交易完成时，系统应当记录详细的交易日志包括时间、价格、数量和原因
3. 当日交易结束时，系统应当生成当日交易总结报告
4. 当出现系统错误时，应当立即发送邮件或短信通知
5. 如果网络连接中断，系统应当自动重连并恢复交易状态

### 需求6：用户界面和配置管理

**用户故事：** 作为系统用户，我希望有一个直观易用的界面来配置参数、查看结果和管理系统，以便我能够高效地使用这个交易系统。

#### 验收标准

1. 当用户登录时，系统应当显示清晰的仪表板包含关键指标和状态信息
2. 当用户修改参数时，系统应当验证参数有效性并提供实时预览
3. 当用户查询历史数据时，系统应当在3秒内返回结果并支持图表展示
4. 当用户导出报告时，系统应当支持多种格式包括PDF、Excel和CSV
5. 如果用户权限不足，系统应当显示相应的错误信息并引导用户获取权限