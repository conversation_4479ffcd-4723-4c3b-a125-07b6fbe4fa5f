2025-07-30 12:33:28,718 - __main__ - INFO - 🚀 开始高级性能指标分析系统集成测试
2025-07-30 12:33:28,719 - __main__ - INFO - ================================================================================
2025-07-30 12:33:28,721 - __main__ - INFO - 创建测试数据...
2025-07-30 12:33:28,722 - __main__ - INFO - 创建权益曲线数据...
2025-07-30 12:33:28,727 - __main__ - INFO - 创建交易记录数据...
2025-07-30 12:33:28,728 - __main__ - ERROR - 💥 集成测试过程中发生错误: unsupported operand type(s) for +: 'int' and 'datetime.timedelta'
2025-07-30 12:33:28,730 - __main__ - ERROR - Traceback (most recent call last):
  File "E:\work\test\ai\test_advanced_performance_analyzer.py", line 425, in run_integration_test
    results, stock_predictions, market_data, benchmark_data = create_test_data()
                                                              ^^^^^^^^^^^^^^^^^^
  File "E:\work\test\ai\test_advanced_performance_analyzer.py", line 119, in create_test_data
    'timestamp': trade_date + timedelta(hours=np.random.randint(9, 15)),
                 ~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
TypeError: unsupported operand type(s) for +: 'int' and 'datetime.timedelta'

2025-07-30 12:34:40,196 - __main__ - INFO - 🚀 开始高级性能指标分析系统集成测试
2025-07-30 12:34:40,196 - __main__ - INFO - ================================================================================
2025-07-30 12:34:40,197 - __main__ - INFO - 创建测试数据...
2025-07-30 12:34:40,197 - __main__ - INFO - 创建权益曲线数据...
2025-07-30 12:34:40,200 - __main__ - INFO - 创建交易记录数据...
2025-07-30 12:34:40,223 - __main__ - INFO - 创建股票预测数据...
2025-07-30 12:34:40,234 - __main__ - INFO - 创建市场数据...
2025-07-30 12:34:40,277 - __main__ - INFO - 创建基准数据...
2025-07-30 12:34:40,278 - __main__ - INFO - 测试数据创建完成 - 权益曲线: 182条, 交易记录: 255笔, 预测数据: 100条
2025-07-30 12:34:40,286 - __main__ - INFO - 创建高级性能分析器...
2025-07-30 12:34:40,589 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 高级性能分析器初始化完成
2025-07-30 12:34:40,589 - __main__ - INFO - ==================================================
2025-07-30 12:34:40,590 - __main__ - INFO - 测试爆发股识别准确率分析
2025-07-30 12:34:40,590 - __main__ - INFO - ==================================================
2025-07-30 12:34:40,590 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 开始分析爆发股识别准确率，阈值: 50.0%, 时间窗口: 90天
2025-07-30 12:34:40,954 - __main__ - ERROR - ❌ 爆发股识别准确率分析测试失败: 'actual_return'
2025-07-30 12:34:40,955 - __main__ - INFO - ==================================================
2025-07-30 12:34:40,955 - __main__ - INFO - 测试做T交易效率分析
2025-07-30 12:34:40,956 - __main__ - INFO - ==================================================
2025-07-30 12:34:40,956 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 开始分析做T交易效率...
2025-07-30 12:34:41,105 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 做T效率分析完成 - 成功率: 68.42%, 平均收益: 628.35
2025-07-30 12:34:41,106 - __main__ - INFO - 做T交易效率分析结果:
2025-07-30 12:34:41,106 - __main__ - INFO -   总做T次数: 57
2025-07-30 12:34:41,106 - __main__ - INFO -   成功做T次数: 39
2025-07-30 12:34:41,107 - __main__ - INFO -   做T成功率: 68.42%
2025-07-30 12:34:41,107 - __main__ - INFO -   总成本降低: 112133.12 元
2025-07-30 12:34:41,108 - __main__ - INFO -   单次平均成本降低: 1967.25 元
2025-07-30 12:34:41,108 - __main__ - INFO -   成本降低率: 3.28%
2025-07-30 12:34:41,108 - __main__ - INFO -   总做T收益: 35816.17 元
2025-07-30 12:34:41,108 - __main__ - INFO -   单次平均做T收益: 628.35 元
2025-07-30 12:34:41,110 - __main__ - INFO -   做T收益率: 1.77%
2025-07-30 12:34:41,111 - __main__ - INFO -   平均持仓时间: 141.0 分钟
2025-07-30 12:34:41,111 - __main__ - INFO -   每分钟平均收益: 4.4575 元
2025-07-30 12:34:41,112 - __main__ - INFO -   最大单次亏损: -23344.94 元
2025-07-30 12:34:41,112 - __main__ - INFO -   做T夏普比率: 1.722
2025-07-30 12:34:41,113 - __main__ - INFO -   做T盈亏比: 0.68
2025-07-30 12:34:41,113 - __main__ - INFO - ✅ 做T交易效率分析测试通过
2025-07-30 12:34:41,113 - __main__ - INFO - ==================================================
2025-07-30 12:34:41,113 - __main__ - INFO - 测试策略归因分析
2025-07-30 12:34:41,114 - __main__ - INFO - ==================================================
2025-07-30 12:34:41,114 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 开始执行策略归因分析...
2025-07-30 12:34:41,177 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 策略归因分析完成
2025-07-30 12:34:41,178 - __main__ - INFO - 策略归因分析结果:
2025-07-30 12:34:41,178 - __main__ - INFO -   选股贡献: 6.68%
2025-07-30 12:34:41,179 - __main__ - INFO -   择时贡献: 0.62%
2025-07-30 12:34:41,179 - __main__ - INFO -   交互效应: 0.00%
2025-07-30 12:34:41,179 - __main__ - INFO -   系统性风险: 0.0000
2025-07-30 12:34:41,180 - __main__ - INFO -   特定风险: 0.0000
2025-07-30 12:34:41,180 - __main__ - INFO -   因子暴露度:
2025-07-30 12:34:41,180 - __main__ - INFO -     market: 0.800
2025-07-30 12:34:41,180 - __main__ - INFO -     size: -0.200
2025-07-30 12:34:41,181 - __main__ - INFO -     value: 0.100
2025-07-30 12:34:41,181 - __main__ - INFO -     momentum: 0.300
2025-07-30 12:34:41,181 - __main__ - INFO -     quality: 0.200
2025-07-30 12:34:41,181 - __main__ - INFO -   因子收益贡献:
2025-07-30 12:34:41,182 - __main__ - INFO -     market: 20.40%
2025-07-30 12:34:41,182 - __main__ - INFO -     size: 3.40%
2025-07-30 12:34:41,182 - __main__ - INFO -     value: 1.70%
2025-07-30 12:34:41,182 - __main__ - INFO -     momentum: 5.10%
2025-07-30 12:34:41,183 - __main__ - INFO -     quality: 3.40%
2025-07-30 12:34:41,183 - __main__ - INFO - ✅ 策略归因分析测试通过
2025-07-30 12:34:41,183 - __main__ - INFO - ==================================================
2025-07-30 12:34:41,184 - __main__ - INFO - 测试综合报告生成
2025-07-30 12:34:41,184 - __main__ - INFO - ==================================================
2025-07-30 12:34:41,185 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 生成综合性能分析报告...
2025-07-30 12:34:41,185 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 开始分析爆发股识别准确率，阈值: 100.0%, 时间窗口: 90天
2025-07-30 12:34:41,555 - __main__ - ERROR - ❌ 综合报告生成测试失败: 'actual_return'
2025-07-30 12:34:41,556 - __main__ - INFO - ==================================================
2025-07-30 12:34:41,556 - __main__ - INFO - 测试交互式仪表板创建
2025-07-30 12:34:41,556 - __main__ - INFO - ==================================================
2025-07-30 12:34:41,557 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 创建交互式性能分析仪表板...
2025-07-30 12:34:41,557 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 开始分析爆发股识别准确率，阈值: 100.0%, 时间窗口: 90天
2025-07-30 12:34:41,909 - __main__ - ERROR - ❌ 交互式仪表板创建测试失败: 'actual_return'
2025-07-30 12:34:41,910 - __main__ - INFO - ==================================================
2025-07-30 12:34:41,910 - __main__ - INFO - 测试JSON导出功能
2025-07-30 12:34:41,911 - __main__ - INFO - ==================================================
2025-07-30 12:34:41,911 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 导出性能指标到JSON：test_performance_metrics.json
2025-07-30 12:34:41,911 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 开始分析爆发股识别准确率，阈值: 100.0%, 时间窗口: 90天
2025-07-30 12:34:42,252 - __main__ - ERROR - ❌ JSON导出功能测试失败: 'actual_return'
2025-07-30 12:34:42,252 - __main__ - INFO - ================================================================================
2025-07-30 12:34:42,252 - __main__ - INFO - 📊 测试结果汇总
2025-07-30 12:34:42,253 - __main__ - INFO - ================================================================================
2025-07-30 12:34:42,253 - __main__ - INFO - 爆发股识别分析: ❌ 失败
2025-07-30 12:34:42,253 - __main__ - INFO - 做T效率分析: ✅ 通过
2025-07-30 12:34:42,255 - __main__ - INFO - 策略归因分析: ✅ 通过
2025-07-30 12:34:42,255 - __main__ - INFO - 综合报告生成: ❌ 失败
2025-07-30 12:34:42,255 - __main__ - INFO - 交互式仪表板: ❌ 失败
2025-07-30 12:34:42,256 - __main__ - INFO - JSON导出功能: ❌ 失败
2025-07-30 12:34:42,256 - __main__ - INFO - ================================================================================
2025-07-30 12:34:42,256 - __main__ - INFO - 测试完成: 2/6 通过
2025-07-30 12:34:42,256 - __main__ - ERROR - ⚠️ 有 4 个测试失败
2025-07-30 12:35:06,408 - __main__ - INFO - 🚀 开始高级性能指标分析系统集成测试
2025-07-30 12:35:06,408 - __main__ - INFO - ================================================================================
2025-07-30 12:35:06,408 - __main__ - INFO - 创建测试数据...
2025-07-30 12:35:06,410 - __main__ - INFO - 创建权益曲线数据...
2025-07-30 12:35:06,415 - __main__ - INFO - 创建交易记录数据...
2025-07-30 12:35:06,447 - __main__ - INFO - 创建股票预测数据...
2025-07-30 12:35:06,460 - __main__ - INFO - 创建市场数据...
2025-07-30 12:35:06,658 - __main__ - INFO - 创建基准数据...
2025-07-30 12:35:06,660 - __main__ - INFO - 测试数据创建完成 - 权益曲线: 182条, 交易记录: 255笔, 预测数据: 100条
2025-07-30 12:35:06,667 - __main__ - INFO - 创建高级性能分析器...
2025-07-30 12:35:07,089 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 高级性能分析器初始化完成
2025-07-30 12:35:07,089 - __main__ - INFO - ==================================================
2025-07-30 12:35:07,090 - __main__ - INFO - 测试爆发股识别准确率分析
2025-07-30 12:35:07,090 - __main__ - INFO - ==================================================
2025-07-30 12:35:07,091 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 开始分析爆发股识别准确率，阈值: 50.0%, 时间窗口: 90天
2025-07-30 12:35:07,450 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 爆发股识别分析完成 - 准确率: 90.00%, 精确率: 76.92%, 召回率: 83.33%
2025-07-30 12:35:07,450 - __main__ - INFO - 爆发股识别分析结果:
2025-07-30 12:35:07,451 - __main__ - INFO -   总预测数量: 100
2025-07-30 12:35:07,451 - __main__ - INFO -   实际爆发股数量: 24
2025-07-30 12:35:07,451 - __main__ - INFO -   预测爆发股数量: 26
2025-07-30 12:35:07,452 - __main__ - INFO -   正确识别数量: 20
2025-07-30 12:35:07,452 - __main__ - INFO -   准确率: 90.00%
2025-07-30 12:35:07,452 - __main__ - INFO -   精确率: 76.92%
2025-07-30 12:35:07,453 - __main__ - INFO -   召回率: 83.33%
2025-07-30 12:35:07,453 - __main__ - INFO -   F1分数: 0.800
2025-07-30 12:35:07,453 - __main__ - INFO -   爆发股平均收益: 92.19%
2025-07-30 12:35:07,454 - __main__ - INFO -   预测股票平均收益: 71.42%
2025-07-30 12:35:07,455 - __main__ - INFO -   命中率加权收益: 90.22%
2025-07-30 12:35:07,455 - __main__ - INFO - ✅ 爆发股识别准确率分析测试通过
2025-07-30 12:35:07,455 - __main__ - INFO - ==================================================
2025-07-30 12:35:07,457 - __main__ - INFO - 测试做T交易效率分析
2025-07-30 12:35:07,457 - __main__ - INFO - ==================================================
2025-07-30 12:35:07,457 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 开始分析做T交易效率...
2025-07-30 12:35:07,593 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 做T效率分析完成 - 成功率: 68.42%, 平均收益: 628.35
2025-07-30 12:35:07,593 - __main__ - INFO - 做T交易效率分析结果:
2025-07-30 12:35:07,594 - __main__ - INFO -   总做T次数: 57
2025-07-30 12:35:07,594 - __main__ - INFO -   成功做T次数: 39
2025-07-30 12:35:07,594 - __main__ - INFO -   做T成功率: 68.42%
2025-07-30 12:35:07,595 - __main__ - INFO -   总成本降低: 112133.12 元
2025-07-30 12:35:07,595 - __main__ - INFO -   单次平均成本降低: 1967.25 元
2025-07-30 12:35:07,595 - __main__ - INFO -   成本降低率: 3.28%
2025-07-30 12:35:07,595 - __main__ - INFO -   总做T收益: 35816.17 元
2025-07-30 12:35:07,595 - __main__ - INFO -   单次平均做T收益: 628.35 元
2025-07-30 12:35:07,596 - __main__ - INFO -   做T收益率: 1.77%
2025-07-30 12:35:07,596 - __main__ - INFO -   平均持仓时间: 141.0 分钟
2025-07-30 12:35:07,596 - __main__ - INFO -   每分钟平均收益: 4.4575 元
2025-07-30 12:35:07,597 - __main__ - INFO -   最大单次亏损: -23344.94 元
2025-07-30 12:35:07,597 - __main__ - INFO -   做T夏普比率: 1.722
2025-07-30 12:35:07,597 - __main__ - INFO -   做T盈亏比: 0.68
2025-07-30 12:35:07,597 - __main__ - INFO - ✅ 做T交易效率分析测试通过
2025-07-30 12:35:07,599 - __main__ - INFO - ==================================================
2025-07-30 12:35:07,599 - __main__ - INFO - 测试策略归因分析
2025-07-30 12:35:07,599 - __main__ - INFO - ==================================================
2025-07-30 12:35:07,600 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 开始执行策略归因分析...
2025-07-30 12:35:07,650 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 策略归因分析完成
2025-07-30 12:35:07,650 - __main__ - INFO - 策略归因分析结果:
2025-07-30 12:35:07,650 - __main__ - INFO -   选股贡献: 6.68%
2025-07-30 12:35:07,650 - __main__ - INFO -   择时贡献: 0.62%
2025-07-30 12:35:07,651 - __main__ - INFO -   交互效应: 0.00%
2025-07-30 12:35:07,651 - __main__ - INFO -   系统性风险: 0.0000
2025-07-30 12:35:07,651 - __main__ - INFO -   特定风险: 0.0000
2025-07-30 12:35:07,652 - __main__ - INFO -   因子暴露度:
2025-07-30 12:35:07,652 - __main__ - INFO -     market: 0.800
2025-07-30 12:35:07,652 - __main__ - INFO -     size: -0.200
2025-07-30 12:35:07,653 - __main__ - INFO -     value: 0.100
2025-07-30 12:35:07,653 - __main__ - INFO -     momentum: 0.300
2025-07-30 12:35:07,653 - __main__ - INFO -     quality: 0.200
2025-07-30 12:35:07,655 - __main__ - INFO -   因子收益贡献:
2025-07-30 12:35:07,656 - __main__ - INFO -     market: 20.40%
2025-07-30 12:35:07,657 - __main__ - INFO -     size: 3.40%
2025-07-30 12:35:07,657 - __main__ - INFO -     value: 1.70%
2025-07-30 12:35:07,657 - __main__ - INFO -     momentum: 5.10%
2025-07-30 12:35:07,657 - __main__ - INFO -     quality: 3.40%
2025-07-30 12:35:07,659 - __main__ - INFO - ✅ 策略归因分析测试通过
2025-07-30 12:35:07,659 - __main__ - INFO - ==================================================
2025-07-30 12:35:07,659 - __main__ - INFO - 测试综合报告生成
2025-07-30 12:35:07,659 - __main__ - INFO - ==================================================
2025-07-30 12:35:07,660 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 生成综合性能分析报告...
2025-07-30 12:35:07,660 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 开始分析爆发股识别准确率，阈值: 100.0%, 时间窗口: 90天
2025-07-30 12:35:08,028 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 爆发股识别分析完成 - 准确率: 77.00%, 精确率: 23.08%, 召回率: 66.67%
2025-07-30 12:35:08,028 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 开始分析做T交易效率...
2025-07-30 12:35:08,174 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 做T效率分析完成 - 成功率: 68.42%, 平均收益: 628.35
2025-07-30 12:35:08,175 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 开始执行策略归因分析...
2025-07-30 12:35:08,223 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 策略归因分析完成
2025-07-30 12:35:08,225 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 综合报告已保存到：test_comprehensive_performance_report.md
2025-07-30 12:35:08,226 - __main__ - INFO - ✅ 综合报告生成测试通过，报告已保存到: test_comprehensive_performance_report.md
2025-07-30 12:35:08,226 - __main__ - INFO - 报告长度: 1578 字符
2025-07-30 12:35:08,226 - __main__ - INFO - ==================================================
2025-07-30 12:35:08,226 - __main__ - INFO - 测试交互式仪表板创建
2025-07-30 12:35:08,227 - __main__ - INFO - ==================================================
2025-07-30 12:35:08,227 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 创建交互式性能分析仪表板...
2025-07-30 12:35:08,227 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 开始分析爆发股识别准确率，阈值: 100.0%, 时间窗口: 90天
2025-07-30 12:35:08,577 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 爆发股识别分析完成 - 准确率: 77.00%, 精确率: 23.08%, 召回率: 66.67%
2025-07-30 12:35:08,577 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 开始分析做T交易效率...
2025-07-30 12:35:08,719 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 做T效率分析完成 - 成功率: 68.42%, 平均收益: 628.35
2025-07-30 12:35:09,461 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 交互式仪表板已保存到：test_performance_dashboard.html
2025-07-30 12:35:09,462 - __main__ - INFO - ✅ 交互式仪表板创建测试通过，文件已保存到: test_performance_dashboard.html
2025-07-30 12:35:09,462 - __main__ - INFO - 文件大小: 4677864 字节
2025-07-30 12:35:09,463 - __main__ - INFO - ==================================================
2025-07-30 12:35:09,463 - __main__ - INFO - 测试JSON导出功能
2025-07-30 12:35:09,463 - __main__ - INFO - ==================================================
2025-07-30 12:35:09,463 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 导出性能指标到JSON：test_performance_metrics.json
2025-07-30 12:35:09,464 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 开始分析爆发股识别准确率，阈值: 100.0%, 时间窗口: 90天
2025-07-30 12:35:09,813 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 爆发股识别分析完成 - 准确率: 77.00%, 精确率: 23.08%, 召回率: 66.67%
2025-07-30 12:35:09,813 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 开始分析做T交易效率...
2025-07-30 12:35:09,958 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 做T效率分析完成 - 成功率: 68.42%, 平均收益: 628.35
2025-07-30 12:35:09,958 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 开始执行策略归因分析...
2025-07-30 12:35:10,003 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 策略归因分析完成
2025-07-30 12:35:10,004 - __main__ - ERROR - ❌ JSON导出功能测试失败: Object of type int64 is not JSON serializable
2025-07-30 12:35:10,005 - __main__ - INFO - ================================================================================
2025-07-30 12:35:10,005 - __main__ - INFO - 📊 测试结果汇总
2025-07-30 12:35:10,006 - __main__ - INFO - ================================================================================
2025-07-30 12:35:10,006 - __main__ - INFO - 爆发股识别分析: ✅ 通过
2025-07-30 12:35:10,007 - __main__ - INFO - 做T效率分析: ✅ 通过
2025-07-30 12:35:10,008 - __main__ - INFO - 策略归因分析: ✅ 通过
2025-07-30 12:35:10,008 - __main__ - INFO - 综合报告生成: ✅ 通过
2025-07-30 12:35:10,008 - __main__ - INFO - 交互式仪表板: ✅ 通过
2025-07-30 12:35:10,010 - __main__ - INFO - JSON导出功能: ❌ 失败
2025-07-30 12:35:10,010 - __main__ - INFO - ================================================================================
2025-07-30 12:35:10,010 - __main__ - INFO - 测试完成: 5/6 通过
2025-07-30 12:35:10,011 - __main__ - ERROR - ⚠️ 有 1 个测试失败
2025-07-30 12:36:03,128 - __main__ - INFO - 🚀 开始高级性能指标分析系统集成测试
2025-07-30 12:36:03,128 - __main__ - INFO - ================================================================================
2025-07-30 12:36:03,129 - __main__ - INFO - 创建测试数据...
2025-07-30 12:36:03,130 - __main__ - INFO - 创建权益曲线数据...
2025-07-30 12:36:03,134 - __main__ - INFO - 创建交易记录数据...
2025-07-30 12:36:03,162 - __main__ - INFO - 创建股票预测数据...
2025-07-30 12:36:03,173 - __main__ - INFO - 创建市场数据...
2025-07-30 12:36:03,214 - __main__ - INFO - 创建基准数据...
2025-07-30 12:36:03,215 - __main__ - INFO - 测试数据创建完成 - 权益曲线: 182条, 交易记录: 255笔, 预测数据: 100条
2025-07-30 12:36:03,224 - __main__ - INFO - 创建高级性能分析器...
2025-07-30 12:36:03,604 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 高级性能分析器初始化完成
2025-07-30 12:36:03,605 - __main__ - INFO - ==================================================
2025-07-30 12:36:03,605 - __main__ - INFO - 测试爆发股识别准确率分析
2025-07-30 12:36:03,605 - __main__ - INFO - ==================================================
2025-07-30 12:36:03,606 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 开始分析爆发股识别准确率，阈值: 50.0%, 时间窗口: 90天
2025-07-30 12:36:04,124 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 爆发股识别分析完成 - 准确率: 90.00%, 精确率: 76.92%, 召回率: 83.33%
2025-07-30 12:36:04,125 - __main__ - INFO - 爆发股识别分析结果:
2025-07-30 12:36:04,125 - __main__ - INFO -   总预测数量: 100
2025-07-30 12:36:04,125 - __main__ - INFO -   实际爆发股数量: 24
2025-07-30 12:36:04,126 - __main__ - INFO -   预测爆发股数量: 26
2025-07-30 12:36:04,126 - __main__ - INFO -   正确识别数量: 20
2025-07-30 12:36:04,126 - __main__ - INFO -   准确率: 90.00%
2025-07-30 12:36:04,127 - __main__ - INFO -   精确率: 76.92%
2025-07-30 12:36:04,127 - __main__ - INFO -   召回率: 83.33%
2025-07-30 12:36:04,127 - __main__ - INFO -   F1分数: 0.800
2025-07-30 12:36:04,128 - __main__ - INFO -   爆发股平均收益: 92.19%
2025-07-30 12:36:04,128 - __main__ - INFO -   预测股票平均收益: 71.42%
2025-07-30 12:36:04,128 - __main__ - INFO -   命中率加权收益: 90.22%
2025-07-30 12:36:04,129 - __main__ - INFO - ✅ 爆发股识别准确率分析测试通过
2025-07-30 12:36:04,129 - __main__ - INFO - ==================================================
2025-07-30 12:36:04,129 - __main__ - INFO - 测试做T交易效率分析
2025-07-30 12:36:04,131 - __main__ - INFO - ==================================================
2025-07-30 12:36:04,131 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 开始分析做T交易效率...
2025-07-30 12:36:04,283 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 做T效率分析完成 - 成功率: 68.42%, 平均收益: 628.35
2025-07-30 12:36:04,283 - __main__ - INFO - 做T交易效率分析结果:
2025-07-30 12:36:04,284 - __main__ - INFO -   总做T次数: 57
2025-07-30 12:36:04,284 - __main__ - INFO -   成功做T次数: 39
2025-07-30 12:36:04,284 - __main__ - INFO -   做T成功率: 68.42%
2025-07-30 12:36:04,284 - __main__ - INFO -   总成本降低: 112133.12 元
2025-07-30 12:36:04,285 - __main__ - INFO -   单次平均成本降低: 1967.25 元
2025-07-30 12:36:04,285 - __main__ - INFO -   成本降低率: 3.28%
2025-07-30 12:36:04,285 - __main__ - INFO -   总做T收益: 35816.17 元
2025-07-30 12:36:04,286 - __main__ - INFO -   单次平均做T收益: 628.35 元
2025-07-30 12:36:04,286 - __main__ - INFO -   做T收益率: 1.77%
2025-07-30 12:36:04,286 - __main__ - INFO -   平均持仓时间: 141.0 分钟
2025-07-30 12:36:04,287 - __main__ - INFO -   每分钟平均收益: 4.4575 元
2025-07-30 12:36:04,287 - __main__ - INFO -   最大单次亏损: -23344.94 元
2025-07-30 12:36:04,287 - __main__ - INFO -   做T夏普比率: 1.722
2025-07-30 12:36:04,287 - __main__ - INFO -   做T盈亏比: 0.68
2025-07-30 12:36:04,288 - __main__ - INFO - ✅ 做T交易效率分析测试通过
2025-07-30 12:36:04,288 - __main__ - INFO - ==================================================
2025-07-30 12:36:04,288 - __main__ - INFO - 测试策略归因分析
2025-07-30 12:36:04,288 - __main__ - INFO - ==================================================
2025-07-30 12:36:04,289 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 开始执行策略归因分析...
2025-07-30 12:36:04,342 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 策略归因分析完成
2025-07-30 12:36:04,343 - __main__ - INFO - 策略归因分析结果:
2025-07-30 12:36:04,343 - __main__ - INFO -   选股贡献: 6.68%
2025-07-30 12:36:04,343 - __main__ - INFO -   择时贡献: 0.62%
2025-07-30 12:36:04,344 - __main__ - INFO -   交互效应: 0.00%
2025-07-30 12:36:04,344 - __main__ - INFO -   系统性风险: 0.0000
2025-07-30 12:36:04,345 - __main__ - INFO -   特定风险: 0.0000
2025-07-30 12:36:04,345 - __main__ - INFO -   因子暴露度:
2025-07-30 12:36:04,345 - __main__ - INFO -     market: 0.800
2025-07-30 12:36:04,345 - __main__ - INFO -     size: -0.200
2025-07-30 12:36:04,346 - __main__ - INFO -     value: 0.100
2025-07-30 12:36:04,346 - __main__ - INFO -     momentum: 0.300
2025-07-30 12:36:04,346 - __main__ - INFO -     quality: 0.200
2025-07-30 12:36:04,347 - __main__ - INFO -   因子收益贡献:
2025-07-30 12:36:04,347 - __main__ - INFO -     market: 20.40%
2025-07-30 12:36:04,347 - __main__ - INFO -     size: 3.40%
2025-07-30 12:36:04,348 - __main__ - INFO -     value: 1.70%
2025-07-30 12:36:04,348 - __main__ - INFO -     momentum: 5.10%
2025-07-30 12:36:04,348 - __main__ - INFO -     quality: 3.40%
2025-07-30 12:36:04,349 - __main__ - INFO - ✅ 策略归因分析测试通过
2025-07-30 12:36:04,349 - __main__ - INFO - ==================================================
2025-07-30 12:36:04,349 - __main__ - INFO - 测试综合报告生成
2025-07-30 12:36:04,349 - __main__ - INFO - ==================================================
2025-07-30 12:36:04,350 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 生成综合性能分析报告...
2025-07-30 12:36:04,350 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 开始分析爆发股识别准确率，阈值: 100.0%, 时间窗口: 90天
2025-07-30 12:36:04,693 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 爆发股识别分析完成 - 准确率: 77.00%, 精确率: 23.08%, 召回率: 66.67%
2025-07-30 12:36:04,693 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 开始分析做T交易效率...
2025-07-30 12:36:04,830 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 做T效率分析完成 - 成功率: 68.42%, 平均收益: 628.35
2025-07-30 12:36:04,831 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 开始执行策略归因分析...
2025-07-30 12:36:04,880 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 策略归因分析完成
2025-07-30 12:36:04,882 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 综合报告已保存到：test_comprehensive_performance_report.md
2025-07-30 12:36:04,883 - __main__ - INFO - ✅ 综合报告生成测试通过，报告已保存到: test_comprehensive_performance_report.md
2025-07-30 12:36:04,883 - __main__ - INFO - 报告长度: 1578 字符
2025-07-30 12:36:04,883 - __main__ - INFO - ==================================================
2025-07-30 12:36:04,883 - __main__ - INFO - 测试交互式仪表板创建
2025-07-30 12:36:04,884 - __main__ - INFO - ==================================================
2025-07-30 12:36:04,884 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 创建交互式性能分析仪表板...
2025-07-30 12:36:04,884 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 开始分析爆发股识别准确率，阈值: 100.0%, 时间窗口: 90天
2025-07-30 12:36:05,213 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 爆发股识别分析完成 - 准确率: 77.00%, 精确率: 23.08%, 召回率: 66.67%
2025-07-30 12:36:05,213 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 开始分析做T交易效率...
2025-07-30 12:36:05,346 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 做T效率分析完成 - 成功率: 68.42%, 平均收益: 628.35
2025-07-30 12:36:05,910 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 交互式仪表板已保存到：test_performance_dashboard.html
2025-07-30 12:36:05,911 - __main__ - INFO - ✅ 交互式仪表板创建测试通过，文件已保存到: test_performance_dashboard.html
2025-07-30 12:36:05,912 - __main__ - INFO - 文件大小: 4677864 字节
2025-07-30 12:36:05,912 - __main__ - INFO - ==================================================
2025-07-30 12:36:05,912 - __main__ - INFO - 测试JSON导出功能
2025-07-30 12:36:05,913 - __main__ - INFO - ==================================================
2025-07-30 12:36:05,913 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 导出性能指标到JSON：test_performance_metrics.json
2025-07-30 12:36:05,913 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 开始分析爆发股识别准确率，阈值: 100.0%, 时间窗口: 90天
2025-07-30 12:36:06,242 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 爆发股识别分析完成 - 准确率: 77.00%, 精确率: 23.08%, 召回率: 66.67%
2025-07-30 12:36:06,243 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 开始分析做T交易效率...
2025-07-30 12:36:06,368 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 做T效率分析完成 - 成功率: 68.42%, 平均收益: 628.35
2025-07-30 12:36:06,369 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 开始执行策略归因分析...
2025-07-30 12:36:06,417 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - 策略归因分析完成
2025-07-30 12:36:06,419 - qlib_trading_system.backtest.advanced_performance_analyzer - INFO - JSON导出完成
2025-07-30 12:36:06,420 - __main__ - INFO - ✅ JSON导出功能测试通过，文件已保存到: test_performance_metrics.json
2025-07-30 12:36:06,420 - __main__ - INFO - 导出的指标类型: ['explosive_stock_metrics', 't_trading_metrics', 'attribution_analysis', 'export_time']
2025-07-30 12:36:06,421 - __main__ - INFO - ================================================================================
2025-07-30 12:36:06,421 - __main__ - INFO - 📊 测试结果汇总
2025-07-30 12:36:06,421 - __main__ - INFO - ================================================================================
2025-07-30 12:36:06,421 - __main__ - INFO - 爆发股识别分析: ✅ 通过
2025-07-30 12:36:06,422 - __main__ - INFO - 做T效率分析: ✅ 通过
2025-07-30 12:36:06,422 - __main__ - INFO - 策略归因分析: ✅ 通过
2025-07-30 12:36:06,422 - __main__ - INFO - 综合报告生成: ✅ 通过
2025-07-30 12:36:06,423 - __main__ - INFO - 交互式仪表板: ✅ 通过
2025-07-30 12:36:06,423 - __main__ - INFO - JSON导出功能: ✅ 通过
2025-07-30 12:36:06,423 - __main__ - INFO - ================================================================================
2025-07-30 12:36:06,424 - __main__ - INFO - 测试完成: 6/6 通过
2025-07-30 12:36:06,424 - __main__ - INFO - 🎉 所有测试通过！高级性能指标分析系统运行正常
