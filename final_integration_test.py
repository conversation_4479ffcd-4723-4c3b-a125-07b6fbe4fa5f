#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
任务 9.2 最终集成测试 - 验证所有功能完整性
Final Integration Test for Task 9.2 - Verify All Functionality
"""

import sys
import os
import json
import logging
from datetime import datetime, date, timedelta
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from qlib_trading_system.monitoring.enhanced_report_generator import EnhancedReportGenerator, DEFAULT_CUSTOM_TEMPLATE

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def run_final_integration_test():
    """运行最终集成测试"""
    print("🚀 任务 9.2 最终集成测试")
    print("="*60)
    
    test_results = []
    
    try:
        # 1. 初始化系统
        print("\n📋 1. 初始化报告生成系统...")
        generator = EnhancedReportGenerator({
            'report_dir': 'final_test_reports',
            'enable_charts': True,
            'enable_interactive_charts': False
        })
        test_results.append(("系统初始化", True))
        print("✅ 系统初始化成功")
        
        # 2. 测试日度交易总结报告生成
        print("\n📋 2. 测试日度交易总结报告生成...")
        daily_template_id = generator.create_custom_template(
            template_name="日度交易总结报告",
            template_type="daily",
            sections=["基础指标", "交易统计", "持仓信息", "风险指标", "做T统计"],
            charts=["performance_trend", "risk_analysis"],
            template_content=DEFAULT_CUSTOM_TEMPLATE
        )
        
        daily_data = {
            "日期": "2024-01-15",
            "总收益": "¥125,000",
            "日度收益": "¥15,800",
            "收益率": "18.5%",
            "交易次数": 52,
            "买入次数": 32,
            "卖出次数": 20,
            "持仓数量": 4,
            "做T次数": 15,
            "做T收益": "¥4,200",
            "做T成功率": "78.5%",
            "最大回撤": "5.2%",
            "波动率": "16.8%",
            "系统告警": 0
        }
        
        daily_reports = generator.generate_custom_report(
            template_id=daily_template_id,
            data=daily_data,
            output_filename="final_daily_report_20240115",
            export_formats=['html', 'excel', 'json']
        )
        
        daily_success = all(os.path.exists(path) for path in daily_reports.values())
        test_results.append(("日度交易总结报告生成", daily_success))
        print(f"{'✅' if daily_success else '❌'} 日度报告生成: {len(daily_reports)} 个文件")
        
        # 3. 测试周度和月度绩效分析报告
        print("\n📋 3. 测试周度和月度绩效分析报告...")
        
        # 周度报告
        weekly_template_id = generator.create_custom_template(
            template_name="周度绩效分析报告",
            template_type="weekly",
            sections=["收益表现", "交易统计", "策略表现", "风险管理"],
            charts=["performance_trend", "risk_analysis", "trading_volume"],
            template_content=DEFAULT_CUSTOM_TEMPLATE
        )
        
        weekly_data = {
            "周期": "2024-01-15 至 2024-01-21",
            "周度收益率": "4.8%",
            "累计收益率": "22.3%",
            "最佳单日收益": "2.1%",
            "最差单日收益": "-0.8%",
            "总交易次数": 125,
            "日均交易次数": 17.9,
            "胜率": "72.5%",
            "夏普比率": "1.85",
            "股票筛选准确率": "68.2%"
        }
        
        weekly_reports = generator.generate_custom_report(
            template_id=weekly_template_id,
            data=weekly_data,
            output_filename="final_weekly_report_20240115_20240121",
            export_formats=['html', 'excel', 'json']
        )
        
        # 月度报告
        monthly_template_id = generator.create_custom_template(
            template_name="月度绩效分析报告",
            template_type="monthly",
            sections=["月度收益表现", "策略分析", "风险调整收益", "收益归因分析"],
            charts=["performance_trend", "risk_analysis", "trading_volume"],
            template_content=DEFAULT_CUSTOM_TEMPLATE
        )
        
        monthly_data = {
            "月份": "2024年1月",
            "月度收益率": "18.5%",
            "累计收益率": "35.2%",
            "爆发股命中率": "52.8%",
            "平均爆发收益": "28.5%",
            "做T交易效率": "75.2%",
            "成本降低率": "15.8%",
            "夏普比率": "2.35",
            "索提诺比率": "2.85",
            "卡尔马比率": "1.95",
            "信息比率": "1.25",
            "股票筛选贡献": "65.2%",
            "择时贡献": "25.8%",
            "市场因子贡献": "9.0%"
        }
        
        monthly_reports = generator.generate_custom_report(
            template_id=monthly_template_id,
            data=monthly_data,
            output_filename="final_monthly_report_202401",
            export_formats=['html', 'excel', 'json']
        )
        
        weekly_success = all(os.path.exists(path) for path in weekly_reports.values())
        monthly_success = all(os.path.exists(path) for path in monthly_reports.values())
        
        test_results.append(("周度绩效分析报告", weekly_success))
        test_results.append(("月度绩效分析报告", monthly_success))
        print(f"{'✅' if weekly_success else '❌'} 周度报告生成: {len(weekly_reports)} 个文件")
        print(f"{'✅' if monthly_success else '❌'} 月度报告生成: {len(monthly_reports)} 个文件")
        
        # 4. 测试策略执行详情报告系统
        print("\n📋 4. 测试策略执行详情报告系统...")
        strategy_template_id = generator.create_custom_template(
            template_name="策略执行详情报告",
            template_type="strategy",
            sections=["收益表现", "交易统计", "执行详情", "风险指标"],
            charts=["performance_trend", "risk_analysis", "trading_volume"],
            template_content=DEFAULT_CUSTOM_TEMPLATE
        )
        
        strategy_data = {
            "策略ID": "explosive_stock_ai_v2",
            "执行周期": "2024-01-01 至 2024-01-31",
            "总收益率": "28.5%",
            "年化收益率": "42.8%",
            "最大回撤": "7.2%",
            "夏普比率": "2.65",
            "总交易次数": 185,
            "胜率": "68.9%",
            "盈利因子": "2.15",
            "平均交易收益": "0.85%",
            "最佳交易": "12.5%",
            "最差交易": "-4.2%",
            "平均持仓周期": "3.5天",
            "换手率": "4.2",
            "平均滑点": "0.06%",
            "平均手续费": "0.03%",
            "成交率": "98.5%",
            "平均延迟": "38ms",
            "VaR (95%)": "3.2%",
            "CVaR (95%)": "5.8%",
            "Beta系数": "1.05",
            "Alpha系数": "2.8%"
        }
        
        strategy_reports = generator.generate_custom_report(
            template_id=strategy_template_id,
            data=strategy_data,
            output_filename="final_strategy_report_explosive_stock_ai_v2",
            export_formats=['html', 'excel', 'json']
        )
        
        strategy_success = all(os.path.exists(path) for path in strategy_reports.values())
        test_results.append(("策略执行详情报告系统", strategy_success))
        print(f"{'✅' if strategy_success else '❌'} 策略执行报告生成: {len(strategy_reports)} 个文件")
        
        # 5. 测试自定义报告模板和导出功能
        print("\n📋 5. 测试自定义报告模板和导出功能...")
        
        # 创建导出配置
        export_config_id = generator.create_export_config(
            export_name="最终测试导出配置",
            export_format="zip",
            include_charts=True,
            include_raw_data=True,
            compression_level=6
        )
        
        # 收集所有报告路径
        all_report_paths = []
        all_report_paths.extend(daily_reports.values())
        all_report_paths.extend(weekly_reports.values())
        all_report_paths.extend(monthly_reports.values())
        all_report_paths.extend(strategy_reports.values())
        
        # 批量导出
        try:
            batch_export_path = generator.export_reports_batch(
                report_paths=all_report_paths,
                export_config_id=export_config_id,
                output_path="final_test_reports/exports/final_batch_export"
            )
            
            export_success = os.path.exists(batch_export_path)
            test_results.append(("自定义报告模板和导出功能", export_success))
            print(f"{'✅' if export_success else '❌'} 批量导出: {batch_export_path if export_success else '失败'}")
            
        except Exception as e:
            test_results.append(("自定义报告模板和导出功能", False))
            print(f"❌ 批量导出失败: {e}")
        
        # 6. 测试模板管理功能
        print("\n📋 6. 测试模板管理功能...")
        templates = generator.list_custom_templates()
        template_management_success = len(templates) >= 4  # 应该有至少4个模板
        test_results.append(("模板管理功能", template_management_success))
        print(f"{'✅' if template_management_success else '❌'} 模板管理: {len(templates)} 个模板")
        
        # 7. 生成报告汇总
        print("\n📋 7. 生成报告汇总...")
        summary = generator.generate_report_summary(
            start_date='2024-01-01',
            end_date='2024-01-31',
            report_types=['daily', 'weekly', 'monthly']
        )
        
        summary_path = f"final_test_reports/final_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2, default=str)
        
        summary_success = os.path.exists(summary_path)
        test_results.append(("报告汇总生成", summary_success))
        print(f"{'✅' if summary_success else '❌'} 报告汇总: {summary_path if summary_success else '失败'}")
        
        # 8. 验证文件完整性
        print("\n📋 8. 验证文件完整性...")
        all_files = []
        all_files.extend(daily_reports.values())
        all_files.extend(weekly_reports.values())
        all_files.extend(monthly_reports.values())
        all_files.extend(strategy_reports.values())
        all_files.append(summary_path)
        
        file_integrity_success = all(os.path.exists(f) and os.path.getsize(f) > 0 for f in all_files)
        test_results.append(("文件完整性验证", file_integrity_success))
        print(f"{'✅' if file_integrity_success else '❌'} 文件完整性: {len(all_files)} 个文件")
        
        # 汇总测试结果
        print("\n" + "="*60)
        print("📊 最终集成测试结果")
        print("="*60)
        
        passed_tests = sum(1 for _, result in test_results if result)
        total_tests = len(test_results)
        
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"   {test_name:<30} {status}")
        
        print(f"\n📈 测试结果: {passed_tests}/{total_tests} 项通过")
        
        if passed_tests == total_tests:
            print("\n🎉 任务 9.2 开发报告生成系统 - 最终集成测试全部通过！")
            print("\n✅ 功能验证完成:")
            print("   ✅ 日度交易总结报告生成 - 完整实现")
            print("   ✅ 周度和月度绩效分析报告 - 完整实现")
            print("   ✅ 策略执行详情报告系统 - 完整实现")
            print("   ✅ 自定义报告模板和导出功能 - 完整实现")
            print("   ✅ 需求 5.2 (实时监控和报告) - 已满足")
            print("   ✅ 需求 5.4 (报告和审计) - 已满足")
            
            print(f"\n📁 生成的报告文件:")
            print(f"   📄 HTML报告: {len([f for f in all_files if f.endswith('.html')])} 个")
            print(f"   📊 Excel报告: {len([f for f in all_files if f.endswith('.xlsx')])} 个")
            print(f"   📋 JSON报告: {len([f for f in all_files if f.endswith('.json')])} 个")
            print(f"   📈 图表文件: {len(list(Path('final_test_reports/charts').glob('*.png')))} 个")
            
            return True
        else:
            print(f"\n❌ 有 {total_tests - passed_tests} 项测试失败")
            return False
            
    except Exception as e:
        print(f"\n❌ 最终集成测试发生异常: {e}")
        logger.error(f"最终集成测试异常: {e}", exc_info=True)
        return False


if __name__ == "__main__":
    success = run_final_integration_test()
    sys.exit(0 if success else 1)