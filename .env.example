# 环境配置示例文件
# 复制此文件为 .env 并填入实际配置

# 系统配置
ENVIRONMENT=development
DEBUG=True
LOG_LEVEL=INFO

# 资金配置
CAPITAL_MODE=small
TOTAL_CAPITAL=100000
BASE_POSITION_RATIO=0.75
T_POSITION_RATIO=0.20
CASH_RESERVE_RATIO=0.05

# 风险控制
MAX_SINGLE_LOSS=0.02
MAX_DAILY_LOSS=0.01
MAX_DRAWDOWN=0.30

# 数据库配置
CLICKHOUSE_HOST=localhost
CLICKHOUSE_PORT=9000
CLICKHOUSE_USER=default
CLICKHOUSE_PASSWORD=
CLICKHOUSE_DATABASE=qlib_trading

REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

MONGODB_HOST=localhost
MONGODB_PORT=27017
MONGODB_USER=
MONGODB_PASSWORD=
MONGODB_DATABASE=qlib_trading

# 数据源配置
PRIMARY_DATA_SOURCE=iTick
ITICK_API_KEY=your_itick_api_key
ITICK_SECRET_KEY=your_itick_secret_key

JOINQUANT_USERNAME=your_joinquant_username
JOINQUANT_PASSWORD=your_joinquant_password

RICEQUANT_API_KEY=your_ricequant_api_key
RICEQUANT_SECRET_KEY=your_ricequant_secret_key

# API配置
API_HOST=0.0.0.0
API_PORT=8000

# 监控配置
ENABLE_MONITORING=True
PROMETHEUS_PORT=9090