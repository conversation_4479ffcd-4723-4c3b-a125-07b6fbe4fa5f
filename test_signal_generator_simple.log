2025-07-29 16:14:59,369 - __main__ - INFO - 开始信号生成和决策系统简化测试
2025-07-29 16:14:59,369 - __main__ - INFO - ============================================================
2025-07-29 16:14:59,370 - __main__ - INFO - 
==================== 技术分析器 ====================
2025-07-29 16:14:59,370 - __main__ - INFO - 开始测试技术分析器...
2025-07-29 16:15:03,781 - __main__ - ERROR - 技术分析器测试失败: [WinError 127] 找不到指定的程序。 Error loading "D:\app\pyenv-win-3.1.1\pyenv-win\versions\3.11.0b4\Lib\site-packages\torch\lib\torch_python.dll" or one of its dependencies.
2025-07-29 16:15:03,788 - __main__ - ERROR - 技术分析器 测试失败 ✗
2025-07-29 16:15:03,789 - __main__ - INFO - 
==================== 时间框架重采样 ====================
2025-07-29 16:15:03,789 - __main__ - INFO - 
开始测试时间框架重采样...
2025-07-29 16:15:03,800 - __main__ - ERROR - 时间框架重采样测试失败: [WinError 127] 找不到指定的程序。 Error loading "D:\app\pyenv-win-3.1.1\pyenv-win\versions\3.11.0b4\Lib\site-packages\torch\lib\torch_python.dll" or one of its dependencies.
2025-07-29 16:15:03,804 - __main__ - ERROR - 时间框架重采样 测试失败 ✗
2025-07-29 16:15:03,804 - __main__ - INFO - 
==================== 置信度计算 ====================
2025-07-29 16:15:03,804 - __main__ - INFO - 
开始测试置信度计算...
2025-07-29 16:15:03,816 - __main__ - ERROR - 置信度计算测试失败: [WinError 127] 找不到指定的程序。 Error loading "D:\app\pyenv-win-3.1.1\pyenv-win\versions\3.11.0b4\Lib\site-packages\torch\lib\torch_python.dll" or one of its dependencies.
2025-07-29 16:15:03,819 - __main__ - ERROR - 置信度计算 测试失败 ✗
2025-07-29 16:15:03,819 - __main__ - INFO - 
==================== 信号过滤 ====================
2025-07-29 16:15:03,819 - __main__ - INFO - 
开始测试信号过滤...
2025-07-29 16:15:03,820 - __main__ - INFO - === 信号过滤测试 ===
2025-07-29 16:15:03,820 - __main__ - INFO - 测试案例 1: 强度=0.8, 置信度=0.8, 一致性=0.8, 风险=0.3 -> 通过
2025-07-29 16:15:03,820 - __main__ - INFO - 测试案例 2: 强度=0.3, 置信度=0.8, 一致性=0.8, 风险=0.3 -> 被过滤
2025-07-29 16:15:03,820 - __main__ - INFO -   过滤原因: 信号强度不足: 0.300 < 0.5
2025-07-29 16:15:03,821 - __main__ - INFO - 测试案例 3: 强度=0.8, 置信度=0.3, 一致性=0.8, 风险=0.3 -> 被过滤
2025-07-29 16:15:03,822 - __main__ - INFO -   过滤原因: 置信度不足: 0.300 < 0.6
2025-07-29 16:15:03,822 - __main__ - INFO - 测试案例 4: 强度=0.8, 置信度=0.8, 一致性=0.3, 风险=0.3 -> 被过滤
2025-07-29 16:15:03,822 - __main__ - INFO -   过滤原因: 一致性不足: 0.300 < 0.7
2025-07-29 16:15:03,824 - __main__ - INFO - 测试案例 5: 强度=0.8, 置信度=0.8, 一致性=0.8, 风险=0.9 -> 被过滤
2025-07-29 16:15:03,824 - __main__ - INFO -   过滤原因: 风险过高: 0.900 > 0.8
2025-07-29 16:15:03,824 - __main__ - INFO - 
过滤测试结果: 5/5 通过
2025-07-29 16:15:03,824 - __main__ - INFO - 信号过滤测试完成 ✓
2025-07-29 16:15:03,825 - __main__ - INFO - 信号过滤 测试通过 ✓
2025-07-29 16:15:03,825 - __main__ - INFO - 
==================== 动态阈值调整 ====================
2025-07-29 16:15:03,825 - __main__ - INFO - 
开始测试动态阈值调整...
2025-07-29 16:15:03,825 - __main__ - INFO - === 动态阈值调整测试 ===
2025-07-29 16:15:03,825 - __main__ - INFO - 初始买入阈值: 0.600
2025-07-29 16:15:03,826 - __main__ - INFO - 初始卖出阈值: 0.600
2025-07-29 16:15:03,826 - __main__ - INFO - 第5次更新:
2025-07-29 16:15:03,828 - __main__ - INFO -   预测方向: 1, 实际收益: 0.047
2025-07-29 16:15:03,828 - __main__ - INFO -   当前准确率: 0.600
2025-07-29 16:15:03,828 - __main__ - INFO -   平均收益: 0.029
2025-07-29 16:15:03,828 - __main__ - INFO -   买入阈值: 0.629
2025-07-29 16:15:03,829 - __main__ - INFO -   卖出阈值: 0.629
2025-07-29 16:15:03,829 - __main__ - INFO - 第10次更新:
2025-07-29 16:15:03,830 - __main__ - INFO -   预测方向: 0, 实际收益: 0.030
2025-07-29 16:15:03,830 - __main__ - INFO -   当前准确率: 0.300
2025-07-29 16:15:03,830 - __main__ - INFO -   平均收益: 0.018
2025-07-29 16:15:03,831 - __main__ - INFO -   买入阈值: 0.803
2025-07-29 16:15:03,831 - __main__ - INFO -   卖出阈值: 0.803
2025-07-29 16:15:03,831 - __main__ - INFO - 第15次更新:
2025-07-29 16:15:03,833 - __main__ - INFO -   预测方向: -1, 实际收益: -0.029
2025-07-29 16:15:03,833 - __main__ - INFO -   当前准确率: 0.400
2025-07-29 16:15:03,833 - __main__ - INFO -   平均收益: 0.011
2025-07-29 16:15:03,834 - __main__ - INFO -   买入阈值: 0.900
2025-07-29 16:15:03,834 - __main__ - INFO -   卖出阈值: 0.900
2025-07-29 16:15:03,835 - __main__ - INFO - 第20次更新:
2025-07-29 16:15:03,836 - __main__ - INFO -   预测方向: 1, 实际收益: 0.009
2025-07-29 16:15:03,837 - __main__ - INFO -   当前准确率: 0.400
2025-07-29 16:15:03,837 - __main__ - INFO -   平均收益: 0.010
2025-07-29 16:15:03,837 - __main__ - INFO -   买入阈值: 0.900
2025-07-29 16:15:03,838 - __main__ - INFO -   卖出阈值: 0.900
2025-07-29 16:15:03,838 - __main__ - INFO - 
最终统计:
2025-07-29 16:15:03,838 - __main__ - INFO -   最终准确率: 0.400
2025-07-29 16:15:03,839 - __main__ - INFO -   最终平均收益: 0.010
2025-07-29 16:15:03,839 - __main__ - INFO -   最终买入阈值: 0.900
2025-07-29 16:15:03,839 - __main__ - INFO -   最终卖出阈值: 0.900
2025-07-29 16:15:03,839 - __main__ - INFO - 动态阈值调整测试完成 ✓
2025-07-29 16:15:03,840 - __main__ - INFO - 动态阈值调整 测试通过 ✓
2025-07-29 16:15:03,840 - __main__ - INFO - 
============================================================
2025-07-29 16:15:03,840 - __main__ - INFO - 测试结果汇总:
2025-07-29 16:15:03,841 - __main__ - INFO -   技术分析器: 失败 ✗
2025-07-29 16:15:03,842 - __main__ - INFO -   时间框架重采样: 失败 ✗
2025-07-29 16:15:03,842 - __main__ - INFO -   置信度计算: 失败 ✗
2025-07-29 16:15:03,843 - __main__ - INFO -   信号过滤: 通过 ✓
2025-07-29 16:15:03,843 - __main__ - INFO -   动态阈值调整: 通过 ✓
2025-07-29 16:15:03,844 - __main__ - INFO - 
总计: 2/5 项测试通过
2025-07-29 16:15:03,844 - __main__ - ERROR - ❌ 3 项测试失败，请检查实现。
