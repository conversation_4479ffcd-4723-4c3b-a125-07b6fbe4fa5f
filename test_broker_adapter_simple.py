"""
券商接口适配层简化测试
验证多券商API统一接口封装、订单路由、负载均衡、容错重试和监控系统
"""

import logging
import time
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from qlib_trading_system.trading.execution.broker_interface import (
    Order, OrderSide, OrderType, OrderStatus, Account, Position
)
from qlib_trading_system.trading.execution.mock_broker import MockBroker
from qlib_trading_system.trading.execution.broker_adapter import BrokerAdapter
from qlib_trading_system.trading.execution.order_router import OrderRouter
from qlib_trading_system.trading.execution.trading_monitor import TradingMonitor
from qlib_trading_system.trading.execution.retry_handler import RetryHandler
from datetime import datetime


def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def test_broker_adapter():
    """测试券商适配器功能"""
    print("=" * 60)
    print("测试券商接口适配层功能")
    print("=" * 60)
    
    # 创建测试配置
    config = {
        'brokers': {
            'mock_broker_1': {
                'class': 'qlib_trading_system.trading.execution.mock_broker.MockBroker',
                'name': 'MockBroker1',
                'primary': True,
                'weight': 1.0,
                'initial_cash': 1000000.0,
                'commission_rate': 0.0003,
                'connection_delay': 0.01,
                'order_delay': 0.01,
                'connection_failure_rate': 0.0,
                'order_failure_rate': 0.0
            },
            'mock_broker_2': {
                'class': 'qlib_trading_system.trading.execution.mock_broker.MockBroker',
                'name': 'MockBroker2',
                'primary': False,
                'weight': 0.8,
                'initial_cash': 800000.0,
                'commission_rate': 0.0005,
                'connection_delay': 0.02,
                'order_delay': 0.02,
                'connection_failure_rate': 0.0,
                'order_failure_rate': 0.0
            }
        },
        'max_retries': 3,
        'retry_delay': 0.5,
        'health_check_interval': 5
    }
    
    try:
        # 1. 测试券商适配器
        print("\n1. 测试券商适配器连接...")
        broker_adapter = BrokerAdapter(config)
        
        if broker_adapter.connect():
            print("√ 券商连接成功")
        else:
            print("✗ 券商连接失败")
            return False
        
        # 检查券商状态
        status = broker_adapter.get_broker_status()
        print(f"√ 主要券商: {status['primary_broker']}")
        print(f"√ 备用券商: {status['backup_brokers']}")
        print(f"√ 券商健康状态: {status['broker_health']}")
        
        # 2. 测试账户信息
        print("\n2. 测试账户信息...")
        account = broker_adapter.get_account_info()
        print(f"√ 账户ID: {account.account_id}")
        print(f"√ 总资产: {account.total_assets:,.2f}")
        print(f"√ 可用资金: {account.available_cash:,.2f}")
        
        # 3. 测试基础交易
        print("\n3. 测试基础交易...")
        
        # 创建买入订单
        buy_order = Order(
            order_id="",
            symbol="000001.SZ",
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=100,
            create_time=datetime.now()
        )
        
        order_id = broker_adapter.place_order(buy_order)
        print(f"√ 买入订单提交成功: {order_id}")
        
        # 等待订单处理
        time.sleep(1)
        
        # 查询订单状态
        order_status = broker_adapter.get_order_status(order_id)
        if order_status:
            print(f"√ 订单状态: {order_status.status.value}")
            print(f"√ 成交数量: {order_status.filled_quantity}")
            print(f"√ 成交价格: {order_status.avg_fill_price}")
        
        # 查询持仓
        positions = broker_adapter.get_positions()
        print(f"√ 持仓数量: {len(positions)}")
        for pos in positions:
            print(f"  - {pos.symbol}: {pos.quantity}股, 成本: {pos.avg_cost:.2f}")
        
        # 4. 测试订单路由
        print("\n4. 测试订单路由...")
        
        router_config = {
            'routing_strategy': 'WEIGHTED',
            'max_workers': 3,
            'load_update_interval': 5,
            'routing_rules': {
                'default': {
                    'max_fragments': 2,
                    'fragment_size': 500
                }
            }
        }
        
        order_router = OrderRouter(broker_adapter, router_config)
        
        # 测试大订单分片
        large_order = Order(
            order_id="ROUTE_TEST",
            symbol="600000.SH",
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=1000,  # 大订单
            create_time=datetime.now()
        )
        
        routed_order_ids = order_router.route_order(large_order)
        print(f"✓ 订单路由成功: {len(routed_order_ids)} 个子订单")
        
        # 检查分片信息
        fragments = order_router.get_order_fragments("ROUTE_TEST")
        print(f"✓ 订单分片数量: {len(fragments)}")
        for fragment in fragments:
            print(f"  - 分片: {fragment.fragment_id}, 券商: {fragment.broker_name}, 数量: {fragment.quantity}")
        
        # 获取路由统计
        routing_stats = order_router.get_routing_statistics()
        print(f"✓ 路由统计: 总路由 {routing_stats['total_routed']}, 成功 {routing_stats['successful_routes']}")
        
        # 5. 测试监控系统
        print("\n5. 测试监控系统...")
        
        monitor_config = {
            'sync_interval': 2,
            'metrics_interval': 5,
            'alert_retention_hours': 1,
            'anomaly_detection': {
                'max_order_failure_rate': 0.1,
                'max_response_time': 2.0,
                'min_success_rate': 0.9
            }
        }
        
        trading_monitor = TradingMonitor(broker_adapter, order_router, monitor_config)
        trading_monitor.start_monitoring()
        
        # 等待监控系统收集数据
        time.sleep(3)
        
        monitor_status = trading_monitor.get_current_status()
        print(f"✓ 监控系统运行状态: {monitor_status['is_monitoring']}")
        print(f"✓ 最后同步时间: {monitor_status['last_sync_time']}")
        
        current_metrics = monitor_status['current_metrics']
        print(f"✓ 当前指标:")
        print(f"  - 总订单数: {current_metrics['total_orders']}")
        print(f"  - 成功订单数: {current_metrics['successful_orders']}")
        print(f"  - 成功率: {current_metrics['success_rate']:.2%}")
        print(f"  - 总成交数: {current_metrics['total_trades']}")
        
        # 6. 测试重试机制
        print("\n6. 测试重试机制...")
        
        retry_config = {
            'max_attempts': 3,
            'base_delay': 0.1,
            'max_delay': 2.0,
            'strategy': 'EXPONENTIAL_BACKOFF'
        }
        
        retry_handler = RetryHandler(retry_config)
        
        # 模拟可能失败的操作
        def test_operation():
            import random
            if random.random() < 0.3:  # 30%失败率
                raise Exception("模拟操作失败")
            return "操作成功"
        
        result = retry_handler.execute_with_retry(test_operation)
        if result.success:
            print(f"✓ 重试操作成功，尝试次数: {result.attempts}")
        else:
            print(f"✗ 重试操作失败，尝试次数: {result.attempts}")
        
        retry_stats = retry_handler.get_statistics()
        print(f"✓ 重试统计: {retry_stats}")
        
        # 7. 清理资源
        print("\n7. 清理资源...")
        trading_monitor.stop_monitoring()
        broker_adapter.disconnect()
        print("✓ 资源清理完成")
        
        print("\n" + "=" * 60)
        print("🎉 券商接口适配层测试全部通过！")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    setup_logging()
    
    print("启动券商接口适配层测试")
    
    try:
        success = test_broker_adapter()
        
        if success:
            print("\n✅ 所有测试通过！")
            print("\n实现的功能包括:")
            print("1. ✓ 多券商API统一接口封装")
            print("2. ✓ 订单路由和负载均衡机制")
            print("3. ✓ 交易接口容错和重试逻辑")
            print("4. ✓ 交易状态同步和监控系统")
            print("5. ✓ 熔断器和故障恢复机制")
            print("6. ✓ 订单分片和智能路由")
            print("7. ✓ 实时监控和告警系统")
            print("8. ✓ 性能统计和分析")
            return 0
        else:
            print("\n❌ 测试失败")
            return 1
            
    except Exception as e:
        print(f"\n💥 测试运行异常: {e}")
        return 1


if __name__ == "__main__":
    exit(main())