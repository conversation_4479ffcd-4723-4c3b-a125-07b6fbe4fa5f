#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资金管理系统简化测试 - Capital Management System Simple Test

测试核心资金管理功能

Author: Qlib Trading System
Date: 2025-01-30
"""

import sys
import os
import json
import logging
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from qlib_trading_system.trading.capital_manager_simple import (
    CapitalManager, CapitalConfig, CapitalMode, PositionType, TransactionType
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_capital_management_core():
    """测试资金管理核心功能"""
    logger.info("=== 资金管理系统核心功能测试开始 ===")
    
    try:
        # 1. 创建资金配置
        logger.info("1. 创建资金配置")
        config = CapitalConfig(
            total_capital=100000.0,
            mode=CapitalMode.SMALL,
            max_stocks=1,
            base_position_ratio=0.75,
            t_position_ratio=0.20,
            cash_reserve_ratio=0.05,
            max_single_stock_ratio=1.0,
            max_daily_loss_ratio=0.03,
            max_drawdown_ratio=0.15,
            leverage_ratio=1.0
        )
        logger.info(f"✓ 资金配置创建成功: 总资金{config.total_capital:,.2f}, 模式{config.mode.value}")
        
        # 2. 创建资金管理器
        logger.info("2. 创建资金管理器")
        manager = CapitalManager(config)
        logger.info("✓ 资金管理器创建成功")
        
        # 3. 测试资金分配
        logger.info("3. 测试资金分配")
        allocation = manager.get_capital_allocation()
        logger.info(f"✓ 资金分配获取成功:")
        logger.info(f"  - 总资金: {allocation['total_capital']:,.2f}")
        logger.info(f"  - 底仓分配: {allocation['base_allocation']:,.2f}")
        logger.info(f"  - T仓分配: {allocation['t_allocation']:,.2f}")
        logger.info(f"  - 现金分配: {allocation['cash_allocation']:,.2f}")
        logger.info(f"  - 可用现金: {allocation['available_cash']:,.2f}")
        
        # 4. 测试买入操作
        logger.info("4. 测试买入操作")
        test_symbol = "000001.SZ"
        buy_price = 10.00
        buy_quantity = int(allocation['base_allocation'] / buy_price / 100) * 100  # 整手
        
        # 检查是否可以买入
        can_buy, reason = manager.can_buy(test_symbol, buy_quantity, buy_price, PositionType.BASE)
        logger.info(f"买入检查: {can_buy}, 原因: {reason}")
        
        if can_buy:
            # 执行买入
            success = manager.execute_buy(test_symbol, buy_quantity, buy_price, PositionType.BASE, "建立底仓")
            if success:
                logger.info(f"✓ 买入成功: {test_symbol} {buy_quantity}股 @{buy_price:.2f}")
            else:
                logger.error("✗ 买入失败")
                return False
        else:
            logger.error(f"✗ 无法买入: {reason}")
            return False
        
        # 5. 测试T+0操作
        logger.info("5. 测试T+0操作")
        t_quantity = 1000
        t_buy_price = 9.95
        
        # T+0买入
        t_buy_success = manager.execute_buy(test_symbol, t_quantity, t_buy_price, PositionType.T_POSITION, "T+0买入")
        if t_buy_success:
            logger.info(f"✓ T+0买入成功: {t_quantity}股 @{t_buy_price:.2f}")
        else:
            logger.error("✗ T+0买入失败")
        
        # 6. 测试价格更新
        logger.info("6. 测试价格更新")
        new_price = 10.20
        manager.update_positions_price({test_symbol: new_price})
        logger.info(f"✓ 价格更新成功: {test_symbol} -> {new_price:.2f}")
        
        # 7. 测试投资组合摘要
        logger.info("7. 测试投资组合摘要")
        portfolio = manager.get_portfolio_summary()
        logger.info(f"✓ 投资组合摘要:")
        logger.info(f"  - 投资组合价值: {portfolio['portfolio_value']:,.2f}")
        logger.info(f"  - 总市值: {portfolio['total_market_value']:,.2f}")
        logger.info(f"  - 可用现金: {portfolio['available_cash']:,.2f}")
        logger.info(f"  - 总收益率: {portfolio['total_return_pct']:.2f}%")
        logger.info(f"  - 持仓数量: {portfolio['position_count']}")
        
        # 8. 测试资金效率计算
        logger.info("8. 测试资金效率计算")
        efficiency = manager.calculate_capital_efficiency()
        logger.info(f"✓ 资金效率计算完成:")
        logger.info(f"  - 资金利用率: {efficiency['capital_utilization']:.1f}%")
        logger.info(f"  - 现金闲置率: {efficiency['cash_idle_rate']:.1f}%")
        logger.info(f"  - 持仓集中度: {efficiency['concentration_ratio']:.1f}%")
        logger.info(f"  - 换手率: {efficiency['turnover_rate']:.1f}%")
        logger.info(f"  - T+0效率: {efficiency['t_efficiency']:.2f}%")
        logger.info(f"  - 综合效率评分: {efficiency['efficiency_score']:.1f}")
        
        # 9. 测试资金分配优化
        logger.info("9. 测试资金分配优化")
        target_stocks = ["000001.SZ", "000002.SZ", "600000.SH"]
        stock_scores = {
            "000001.SZ": 0.85,
            "000002.SZ": 0.78,
            "600000.SH": 0.72
        }
        
        optimization = manager.optimize_capital_allocation(target_stocks, stock_scores)
        logger.info(f"✓ 资金分配优化完成:")
        logger.info(f"  - 策略: {optimization['strategy']}")
        logger.info(f"  - 预期效率: {optimization['expected_efficiency']:.1f}%")
        logger.info(f"  - 现金储备: {optimization['cash_reserve']:,.2f}")
        
        for symbol, allocation_detail in optimization['allocations'].items():
            logger.info(f"  - {symbol}: 总额{allocation_detail['total_amount']:,.2f}, "
                       f"底仓{allocation_detail['base_amount']:,.2f}, "
                       f"T仓{allocation_detail['t_amount']:,.2f}, "
                       f"评分{allocation_detail['score']:.2f}")
        
        # 10. 生成测试报告
        logger.info("10. 生成测试报告")
        test_report = {
            "test_time": datetime.now().isoformat(),
            "test_results": {
                "config_creation": True,
                "manager_creation": True,
                "capital_allocation": True,
                "buy_operation": True,
                "t_plus_zero_operation": t_buy_success,
                "price_update": True,
                "portfolio_summary": True,
                "efficiency_calculation": True,
                "allocation_optimization": True
            },
            "final_metrics": {
                "portfolio_value": portfolio["portfolio_value"],
                "total_return_pct": portfolio["total_return_pct"],
                "efficiency_score": efficiency["efficiency_score"],
                "capital_utilization": efficiency["capital_utilization"],
                "position_count": portfolio["position_count"]
            },
            "transactions_count": len(manager.transactions)
        }
        
        # 保存测试报告
        report_file = f"capital_management_core_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(test_report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"✓ 测试报告已保存: {report_file}")
        
        # 计算测试通过率
        passed_tests = sum(1 for result in test_report["test_results"].values() if result)
        total_tests = len(test_report["test_results"])
        pass_rate = passed_tests / total_tests
        
        logger.info(f"\n=== 测试结果汇总 ===")
        logger.info(f"通过测试: {passed_tests}/{total_tests}")
        logger.info(f"通过率: {pass_rate:.1%}")
        
        if pass_rate >= 0.9:  # 90%以上通过率
            logger.info("🎉 资金管理系统核心功能测试成功!")
            return True
        else:
            logger.error("❌ 资金管理系统核心功能测试失败!")
            return False
            
    except Exception as e:
        logger.error(f"✗ 测试执行异常: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    try:
        success = test_capital_management_core()
        
        if success:
            print("\n🎉 资金管理系统核心功能测试成功!")
            return 0
        else:
            print("\n❌ 资金管理系统核心功能测试失败!")
            return 1
            
    except Exception as e:
        print(f"\n💥 测试执行异常: {e}")
        return 1


if __name__ == "__main__":
    exit(main())