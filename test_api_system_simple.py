"""
API接口系统简化测试
API Interface System Simple Test

测试API系统的核心组件（不依赖Flask）
"""

import json
import time
from datetime import datetime
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SimpleAPISystemTest:
    """简化API系统测试类"""
    
    def __init__(self):
        """初始化测试环境"""
        # 测试结果
        self.test_results = {
            "total_tests": 0,
            "passed_tests": 0,
            "failed_tests": 0,
            "test_details": []
        }
    
    def run_all_tests(self):
        """运行所有测试"""
        logger.info("开始API接口系统简化测试")
        
        try:
            # 1. 认证系统测试
            self.test_auth_manager()
            
            # 2. API监控测试
            self.test_api_monitor()
            
            # 3. 文档生成测试
            self.test_doc_generator()
            
            # 4. 权限系统测试
            self.test_permission_system()
            
        except Exception as e:
            logger.error(f"测试执行异常: {e}")
            self._record_test_result("测试执行", False, str(e))
        
        finally:
            # 生成测试报告
            self._generate_test_report()
    
    def test_auth_manager(self):
        """测试认证管理器"""
        logger.info("测试认证管理器")
        
        try:
            from qlib_trading_system.api.auth_standalone import AuthManager, UserRole, Permission
            
            # 创建认证管理器
            auth_manager = AuthManager("test-secret-key")
            
            # 测试默认管理员用户
            admin_user = auth_manager.authenticate_user("admin", "admin123")
            assert admin_user is not None, "默认管理员用户认证失败"
            assert admin_user.username == "admin", "管理员用户名不正确"
            assert admin_user.role == UserRole.ADMIN, "管理员角色不正确"
            
            # 测试生成Token
            token = auth_manager.generate_token(admin_user)
            assert token is not None, "Token生成失败"
            assert isinstance(token, str), "Token应该是字符串类型"
            
            # 测试验证Token
            payload = auth_manager.verify_token(token)
            assert payload is not None, "Token验证失败"
            assert payload['username'] == 'admin', "Token载荷用户名不正确"
            
            # 测试创建用户
            user_id = auth_manager.create_user(
                username="test_user",
                email="<EMAIL>",
                password="test123",
                role=UserRole.TRADER,
                created_by="admin"
            )
            assert user_id is not None, "创建用户失败"
            
            # 测试新用户认证
            test_user = auth_manager.authenticate_user("test_user", "test123")
            assert test_user is not None, "新用户认证失败"
            assert test_user.role == UserRole.TRADER, "新用户角色不正确"
            
            # 测试生成API密钥
            api_key = auth_manager.generate_api_key(user_id)
            assert api_key is not None, "API密钥生成失败"
            assert api_key.startswith("ak_"), "API密钥格式不正确"
            
            # 测试验证API密钥
            verified_user = auth_manager.verify_api_key(api_key)
            assert verified_user is not None, "API密钥验证失败"
            assert verified_user.user_id == user_id, "API密钥对应用户不正确"
            
            # 测试权限检查
            has_permission = auth_manager.has_permission(admin_user, Permission.SYSTEM_ADMIN)
            assert has_permission, "管理员应该有系统管理权限"
            
            has_permission = auth_manager.has_permission(test_user, Permission.SYSTEM_ADMIN)
            assert not has_permission, "普通用户不应该有系统管理权限"
            
            self._record_test_result("认证管理器", True)
            
        except Exception as e:
            self._record_test_result("认证管理器", False, str(e))
    
    def test_api_monitor(self):
        """测试API监控器"""
        logger.info("测试API监控器")
        
        try:
            from qlib_trading_system.api.monitoring_standalone import APIMonitor, RateLimiter
            
            # 创建API监控器
            api_monitor = APIMonitor()
            
            # 测试速率限制器
            rate_limiter = RateLimiter(max_requests=5, window_seconds=10)
            
            # 测试允许请求
            user_id = "test_user"
            for i in range(5):
                allowed = rate_limiter.is_allowed(user_id)
                assert allowed, f"第{i+1}个请求应该被允许"
            
            # 测试超出限制
            allowed = rate_limiter.is_allowed(user_id)
            assert not allowed, "第6个请求应该被拒绝"
            
            # 测试剩余请求数
            remaining = rate_limiter.get_remaining_requests(user_id)
            assert remaining == 0, "剩余请求数应该为0"
            
            # 测试获取指标
            metrics = api_monitor.get_metrics(time_range_minutes=60)
            assert metrics is not None, "获取指标失败"
            assert hasattr(metrics, 'total_requests'), "指标缺少总请求数"
            
            # 测试端点统计
            endpoint_stats = api_monitor.get_endpoint_stats()
            assert isinstance(endpoint_stats, dict), "端点统计应该是字典类型"
            
            # 测试用户统计
            user_stats = api_monitor.get_user_stats()
            assert isinstance(user_stats, dict), "用户统计应该是字典类型"
            
            self._record_test_result("API监控器", True)
            
        except Exception as e:
            self._record_test_result("API监控器", False, str(e))
    
    def test_doc_generator(self):
        """测试文档生成器"""
        logger.info("测试文档生成器")
        
        try:
            from qlib_trading_system.api.documentation_standalone import APIDocGenerator, HTTPMethod
            
            # 创建文档生成器
            doc_generator = APIDocGenerator()
            
            # 测试注册端点
            doc_generator.register_endpoint(
                path="/test/endpoint",
                method=HTTPMethod.GET,
                summary="测试端点",
                description="这是一个测试端点",
                tags=["测试"]
            )
            
            # 测试注册模型
            doc_generator.register_model("TestModel", {
                "type": "object",
                "properties": {
                    "id": {"type": "integer"},
                    "name": {"type": "string"}
                }
            })
            
            # 测试生成OpenAPI规范
            openapi_spec = doc_generator.generate_openapi_spec()
            assert isinstance(openapi_spec, dict), "OpenAPI规范应该是字典类型"
            assert "openapi" in openapi_spec, "OpenAPI规范缺少版本信息"
            assert "paths" in openapi_spec, "OpenAPI规范缺少路径信息"
            assert "/test/endpoint" in openapi_spec["paths"], "测试端点未在规范中"
            
            # 测试生成Postman集合
            postman_collection = doc_generator.generate_postman_collection()
            assert isinstance(postman_collection, dict), "Postman集合应该是字典类型"
            assert "info" in postman_collection, "Postman集合缺少信息"
            assert "item" in postman_collection, "Postman集合缺少请求项"
            
            # 测试生成cURL示例
            curl_examples = doc_generator.generate_curl_examples()
            assert isinstance(curl_examples, dict), "cURL示例应该是字典类型"
            
            # 测试生成客户端代码
            python_code = doc_generator.create_test_client_code("python")
            assert isinstance(python_code, str), "Python客户端代码应该是字符串"
            assert "class TradingSystemAPIClient" in python_code, "Python代码缺少客户端类"
            
            javascript_code = doc_generator.create_test_client_code("javascript")
            assert isinstance(javascript_code, str), "JavaScript客户端代码应该是字符串"
            assert "class TradingSystemAPIClient" in javascript_code, "JavaScript代码缺少客户端类"
            
            # 测试导出文档
            json_doc = doc_generator.export_documentation("json")
            assert isinstance(json_doc, str), "JSON文档应该是字符串"
            
            postman_doc = doc_generator.export_documentation("postman")
            assert isinstance(postman_doc, str), "Postman文档应该是字符串"
            
            self._record_test_result("文档生成器", True)
            
        except Exception as e:
            self._record_test_result("文档生成器", False, str(e))
    
    def test_permission_system(self):
        """测试权限系统"""
        logger.info("测试权限系统")
        
        try:
            from qlib_trading_system.api.auth_standalone import AuthManager, UserRole, Permission
            
            # 创建认证管理器
            auth_manager = AuthManager("test-secret-key")
            
            # 测试角色权限映射
            admin_permissions = auth_manager.role_permissions[UserRole.ADMIN]
            assert Permission.SYSTEM_ADMIN in admin_permissions, "管理员应该有系统管理权限"
            assert Permission.MANAGE_CONFIG in admin_permissions, "管理员应该有配置管理权限"
            
            trader_permissions = auth_manager.role_permissions[UserRole.TRADER]
            assert Permission.EXECUTE_TRADE in trader_permissions, "交易员应该有交易执行权限"
            assert Permission.SYSTEM_ADMIN not in trader_permissions, "交易员不应该有系统管理权限"
            
            viewer_permissions = auth_manager.role_permissions[UserRole.VIEWER]
            assert Permission.VIEW_CONFIG in viewer_permissions, "查看者应该有配置查看权限"
            assert Permission.EXECUTE_TRADE not in viewer_permissions, "查看者不应该有交易执行权限"
            
            # 测试用户权限检查
            admin_user = auth_manager.authenticate_user("admin", "admin123")
            
            # 管理员权限测试
            assert auth_manager.has_permission(admin_user, Permission.SYSTEM_ADMIN), "管理员应该有系统管理权限"
            assert auth_manager.has_permission(admin_user, Permission.MANAGE_CONFIG), "管理员应该有配置管理权限"
            assert auth_manager.has_permission(admin_user, Permission.EXECUTE_TRADE), "管理员应该有交易执行权限"
            assert auth_manager.has_permission(admin_user, Permission.VIEW_CONFIG), "管理员应该有配置查看权限"
            
            # 创建不同角色的用户进行测试
            trader_id = auth_manager.create_user(
                username="trader_test",
                email="<EMAIL>",
                password="trader123",
                role=UserRole.TRADER,
                created_by="admin"
            )
            
            trader_user = auth_manager.get_user_by_id(trader_id)
            assert not auth_manager.has_permission(trader_user, Permission.SYSTEM_ADMIN), "交易员不应该有系统管理权限"
            assert auth_manager.has_permission(trader_user, Permission.EXECUTE_TRADE), "交易员应该有交易执行权限"
            
            self._record_test_result("权限系统", True)
            
        except Exception as e:
            self._record_test_result("权限系统", False, str(e))
    
    def _record_test_result(self, test_name: str, passed: bool, error_msg: str = ""):
        """记录测试结果"""
        self.test_results["total_tests"] += 1
        
        if passed:
            self.test_results["passed_tests"] += 1
            logger.info(f"✅ {test_name} - 通过")
        else:
            self.test_results["failed_tests"] += 1
            logger.error(f"❌ {test_name} - 失败: {error_msg}")
        
        self.test_results["test_details"].append({
            "test_name": test_name,
            "passed": passed,
            "error_message": error_msg,
            "timestamp": datetime.now().isoformat()
        })
    
    def _generate_test_report(self):
        """生成测试报告"""
        logger.info("生成测试报告")
        
        # 计算通过率
        total = self.test_results["total_tests"]
        passed = self.test_results["passed_tests"]
        pass_rate = (passed / total * 100) if total > 0 else 0
        
        # 生成报告
        report = {
            "test_summary": {
                "total_tests": total,
                "passed_tests": passed,
                "failed_tests": self.test_results["failed_tests"],
                "pass_rate": f"{pass_rate:.1f}%"
            },
            "test_details": self.test_results["test_details"],
            "test_time": datetime.now().isoformat()
        }
        
        # 保存报告
        with open("api_system_simple_test_report.json", 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        # 打印摘要
        logger.info("=" * 60)
        logger.info("API接口系统简化测试报告")
        logger.info("=" * 60)
        logger.info(f"总测试数: {total}")
        logger.info(f"通过测试: {passed}")
        logger.info(f"失败测试: {self.test_results['failed_tests']}")
        logger.info(f"通过率: {pass_rate:.1f}%")
        logger.info(f"测试报告: api_system_simple_test_report.json")
        logger.info("=" * 60)


def main():
    """主函数"""
    test = SimpleAPISystemTest()
    test.run_all_tests()


if __name__ == "__main__":
    main()
