"""
T+0策略执行引擎演示
展示完整的T+0交易策略功能，包括实时分析、交易执行和风险控制
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
import logging
from typing import Dict, List

from qlib_trading_system.trading.t_plus_zero_engine import TTimingAnalyzer, TCostOptimizer, TPosition, TPositionType
from qlib_trading_system.trading.t_risk_controller import TRiskController, RiskLevel
from qlib_trading_system.trading.t_strategy_executor import TStrategyExecutor, ExecutionConfig, ExecutionStatus

# 配置日志
logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('t_strategy_demo.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)


def create_realistic_market_data(symbol: str = "000001.SZ", periods: int = 240) -> pd.DataFrame:
    """
    创建更真实的市场数据
    
    Args:
        symbol: 股票代码
        periods: 数据点数量
        
    Returns:
        市场数据DataFrame
    """
    logger.info(f"创建{symbol}的市场数据，共{periods}个数据点")
    
    # 设置随机种子确保可重复
    np.random.seed(42)
    
    # 生成时间序列（模拟交易日的分钟数据）
    start_time = datetime.now().replace(hour=9, minute=30, second=0, microsecond=0)
    dates = pd.date_range(start=start_time, periods=periods, freq='1min')
    
    # 基础价格参数
    base_price = 12.50
    daily_volatility = 0.02  # 日波动率2%
    intraday_volatility = 0.005  # 分钟波动率0.5%
    
    # 生成价格走势（包含趋势和随机波动）
    prices = [base_price]
    trend_factor = 0.0001  # 轻微上涨趋势
    
    for i in range(1, periods):
        # 计算价格变化
        trend_change = trend_factor
        random_change = np.random.normal(0, intraday_volatility)
        
        # 添加一些特殊模式
        if i % 60 == 0:  # 每小时有一次较大波动
            random_change *= 2
        
        if 120 <= i <= 140:  # 模拟中午休市前的波动
            random_change *= 1.5
        
        total_change = trend_change + random_change
        new_price = prices[-1] * (1 + total_change)
        prices.append(max(new_price, 1.0))  # 确保价格为正
    
    # 生成完整的OHLCV数据
    data = []
    for i, (timestamp, close_price) in enumerate(zip(dates, prices)):
        # 开盘价
        if i == 0:
            open_price = close_price
        else:
            open_price = prices[i-1]
        
        # 生成高低价
        price_range = close_price * 0.002  # 0.2%的价格范围
        high = max(open_price, close_price) + np.random.uniform(0, price_range)
        low = min(open_price, close_price) - np.random.uniform(0, price_range)
        
        # 确保OHLC逻辑正确
        high = max(high, open_price, close_price)
        low = min(low, open_price, close_price)
        
        # 生成成交量（模拟不同时段的成交量特征）
        hour = timestamp.hour
        minute = timestamp.minute
        
        if hour == 9 and minute < 45:  # 开盘前45分钟成交量大
            volume_base = 1200000
        elif hour == 14 and minute > 45:  # 收盘前15分钟成交量大
            volume_base = 1000000
        elif hour in [11, 13]:  # 午休前后成交量小
            volume_base = 300000
        else:
            volume_base = 600000
        
        # 添加随机波动
        volume = int(volume_base * (1 + np.random.uniform(-0.4, 0.6)))
        
        data.append({
            'timestamp': timestamp,
            'open': round(open_price, 2),
            'high': round(high, 2),
            'low': round(low, 2),
            'close': round(close_price, 2),
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    logger.info(f"市场数据创建完成: 价格范围 {df['close'].min():.2f} - {df['close'].max():.2f}")
    
    return df


class TStrategyDemo:
    """T+0策略演示类"""
    
    def __init__(self):
        """初始化演示"""
        self.symbol = "000001.SZ"
        self.base_shares = 2000
        self.base_cost = 12.50
        
        # 创建市场数据
        self.market_data = create_realistic_market_data(self.symbol, 240)
        
        # 统计信息
        self.demo_stats = {
            'signals_generated': 0,
            'trades_executed': 0,
            'risk_alerts': 0,
            'total_profit': 0.0
        }
        
        logger.info("T+0策略演示初始化完成")
    
    def demo_timing_analyzer(self):
        """演示时机分析器"""
        logger.info("="*50)
        logger.info("演示时机分析器功能")
        logger.info("="*50)
        
        # 创建时机分析器
        analyzer = TTimingAnalyzer({
            'min_profit_threshold': 0.003,
            'max_holding_time': 90,
            'rsi_period': 14,
            'volume_threshold': 1.5
        })
        
        # 创建底仓
        base_position = TPosition(
            symbol=self.symbol,
            position_type=TPositionType.BASE,
            shares=self.base_shares,
            avg_cost=self.base_cost,
            current_price=self.base_cost,
            unrealized_pnl=0.0,
            unrealized_pnl_pct=0.0,
            entry_time=datetime.now(),
            holding_minutes=0
        )
        
        # 分析不同时段的信号
        analysis_points = [30, 60, 120, 180, 220]  # 选择几个分析点
        
        for point in analysis_points:
            if point < len(self.market_data):
                # 获取到当前时点的数据
                current_data = self.market_data.iloc[:point+1].copy()
                current_price = current_data['close'].iloc[-1]
                
                # 更新底仓价格
                base_position.update_price(current_price)
                
                # 分析信号
                signal = analyzer.analyze_t_timing(current_data, base_position)
                
                logger.info(f"时点 {point} ({current_data['timestamp'].iloc[-1].strftime('%H:%M')})")
                logger.info(f"  当前价格: {current_price:.2f}")
                logger.info(f"  信号类型: {signal.signal_type.value}")
                logger.info(f"  信号强度: {signal.strength:.3f}")
                logger.info(f"  置信度: {signal.confidence:.3f}")
                logger.info(f"  建议股数: {signal.suggested_shares}")
                logger.info(f"  预期收益: {signal.expected_return:.3%}")
                logger.info(f"  信号原因: {signal.reason}")
                logger.info("")
                
                self.demo_stats['signals_generated'] += 1
    
    def demo_cost_optimizer(self):
        """演示成本优化器"""
        logger.info("="*50)
        logger.info("演示成本优化器功能")
        logger.info("="*50)
        
        # 创建成本优化器
        optimizer = TCostOptimizer({
            'target_cost_reduction': 0.008,
            'max_t_ratio': 0.25,
            'commission_rate': 0.0003,
            'stamp_tax_rate': 0.001
        })
        
        # 创建底仓
        base_position = TPosition(
            symbol=self.symbol,
            position_type=TPositionType.BASE,
            shares=self.base_shares,
            avg_cost=self.base_cost,
            current_price=self.base_cost,
            unrealized_pnl=0.0,
            unrealized_pnl_pct=0.0,
            entry_time=datetime.now(),
            holding_minutes=0
        )
        
        # 分析不同价格点的优化建议
        price_points = [
            self.base_cost * 0.985,  # 低于成本1.5%
            self.base_cost * 0.995,  # 低于成本0.5%
            self.base_cost * 1.005,  # 高于成本0.5%
            self.base_cost * 1.015,  # 高于成本1.5%
        ]
        
        for i, price in enumerate(price_points):
            # 更新底仓价格
            base_position.update_price(price)
            
            # 创建对应的市场数据
            test_data = self.market_data.iloc[:100].copy()
            test_data.loc[test_data.index[-1], 'close'] = price
            
            # 优化分析
            optimization_result = optimizer.optimize_t_strategy(base_position, price, test_data)
            
            logger.info(f"价格情况 {i+1}: {price:.2f} (相对成本 {(price/self.base_cost-1)*100:+.1f}%)")
            logger.info(f"  推荐动作: {optimization_result['recommended_action']}")
            logger.info(f"  最优股数: {optimization_result['optimal_shares']}")
            logger.info(f"  预期成本降低: {optimization_result['expected_cost_reduction']:.4%}")
            logger.info(f"  风险收益比: {optimization_result['risk_reward_ratio']:.2f}")
            logger.info(f"  置信度: {optimization_result['confidence']:.3f}")
            logger.info("")
    
    def demo_risk_controller(self):
        """演示风险控制器"""
        logger.info("="*50)
        logger.info("演示风险控制器功能")
        logger.info("="*50)
        
        # 创建风险控制器
        risk_controller = TRiskController({
            'risk_limits': {
                'max_position_ratio': 0.25,
                'max_single_loss': 0.015,
                'max_daily_loss': 0.03,
                'stop_loss_pct': 0.012,
                'max_holding_time': 90
            }
        })
        
        # 创建底仓
        base_position = TPosition(
            symbol=self.symbol,
            position_type=TPositionType.BASE,
            shares=self.base_shares,
            avg_cost=self.base_cost,
            current_price=self.base_cost,
            unrealized_pnl=0.0,
            unrealized_pnl_pct=0.0,
            entry_time=datetime.now(),
            holding_minutes=0
        )
        
        # 测试不同风险场景
        from qlib_trading_system.trading.t_plus_zero_engine import TSignal, TSignalType
        
        test_scenarios = [
            {
                'name': '正常买入信号',
                'signal': TSignal(
                    symbol=self.symbol,
                    signal_type=TSignalType.BUY_T,
                    strength=0.7,
                    confidence=0.8,
                    expected_return=0.008,
                    suggested_shares=400,
                    entry_price=self.base_cost * 0.995
                )
            },
            {
                'name': '高风险买入信号',
                'signal': TSignal(
                    symbol=self.symbol,
                    signal_type=TSignalType.BUY_T,
                    strength=0.9,
                    confidence=0.6,
                    expected_return=0.015,
                    suggested_shares=800,  # 过大的仓位
                    entry_price=self.base_cost * 1.02
                )
            },
            {
                'name': '卖出信号',
                'signal': TSignal(
                    symbol=self.symbol,
                    signal_type=TSignalType.SELL_T,
                    strength=0.8,
                    confidence=0.7,
                    expected_return=0.01,
                    suggested_shares=300,
                    entry_price=self.base_cost * 1.01
                )
            }
        ]
        
        for scenario in test_scenarios:
            logger.info(f"测试场景: {scenario['name']}")
            signal = scenario['signal']
            
            # 风险检查
            allow_trade, alerts = risk_controller.check_pre_trade_risk(
                signal, base_position, self.market_data.iloc[:100]
            )
            
            logger.info(f"  信号: {signal.signal_type.value} {signal.suggested_shares}股 @{signal.entry_price:.2f}")
            logger.info(f"  允许交易: {allow_trade}")
            logger.info(f"  风险警报数量: {len(alerts)}")
            
            for alert in alerts:
                logger.info(f"    - {alert.alert_type}: {alert.message}")
            
            logger.info("")
            self.demo_stats['risk_alerts'] += len(alerts)
    
    def demo_full_strategy_execution(self):
        """演示完整策略执行"""
        logger.info("="*50)
        logger.info("演示完整策略执行")
        logger.info("="*50)
        
        # 创建执行配置
        config = ExecutionConfig(
            symbol=self.symbol,
            base_shares=self.base_shares,
            base_cost=self.base_cost,
            max_t_ratio=0.2,
            min_profit_threshold=0.005,
            analysis_interval=1,  # 1秒分析间隔用于演示
            stop_loss_pct=0.012,
            max_holding_time=60,
            max_daily_trades=10
        )
        
        # 设置回调函数
        def on_signal_generated(signal, allow_trade, alerts):
            logger.info(f"信号生成: {signal.signal_type.value} 强度:{signal.strength:.3f} 允许:{allow_trade}")
            if alerts:
                logger.warning(f"  风险警报: {len(alerts)}个")
            self.demo_stats['signals_generated'] += 1
        
        def on_trade_executed(trade_record):
            logger.info(f"交易执行: {trade_record.trade_type} {trade_record.shares}股 @{trade_record.price:.2f}")
            self.demo_stats['trades_executed'] += 1
            self.demo_stats['total_profit'] += trade_record.profit
        
        def on_risk_alert(alert):
            logger.warning(f"风险警报: {alert.alert_type} - {alert.message}")
            self.demo_stats['risk_alerts'] += 1
        
        config.on_signal_generated = on_signal_generated
        config.on_trade_executed = on_trade_executed
        config.on_risk_alert = on_risk_alert
        
        # 创建策略执行器
        executor = TStrategyExecutor(config)
        
        try:
            logger.info("启动策略执行器...")
            executor.start()
            
            # 模拟实时数据流
            batch_size = 20
            total_batches = len(self.market_data) // batch_size
            
            for i in range(0, len(self.market_data), batch_size):
                batch_data = self.market_data.iloc[i:i+batch_size]
                
                # 更新市场数据
                executor.update_market_data(batch_data)
                
                # 显示进度
                batch_num = i // batch_size + 1
                if batch_num % 3 == 0:  # 每3个批次显示一次状态
                    status = executor.get_status()
                    logger.info(f"进度: {batch_num}/{total_batches}, "
                              f"状态: {status['status']}, "
                              f"交易: {status['statistics']['total_trades']}笔, "
                              f"T仓: {status['t_positions']}个")
                
                # 模拟实时延迟
                time.sleep(0.2)
            
            # 等待处理完成
            time.sleep(3)
            
            # 获取最终结果
            final_status = executor.get_status()
            performance_report = executor.get_performance_report()
            
            logger.info("="*30)
            logger.info("策略执行结果")
            logger.info("="*30)
            logger.info(f"执行状态: {final_status['status']}")
            logger.info(f"底仓信息:")
            logger.info(f"  股数: {final_status['base_position']['shares']}")
            logger.info(f"  成本: {final_status['base_position']['avg_cost']:.2f}")
            logger.info(f"  现价: {final_status['base_position']['current_price']:.2f}")
            logger.info(f"  浮盈: {final_status['base_position']['unrealized_pnl']:.2f}")
            logger.info(f"  浮盈率: {final_status['base_position']['unrealized_pnl_pct']:.2%}")
            
            logger.info(f"交易统计:")
            logger.info(f"  总交易: {final_status['statistics']['total_trades']}笔")
            logger.info(f"  成功交易: {final_status['statistics']['successful_trades']}笔")
            logger.info(f"  成功率: {final_status['statistics']['success_rate']:.1%}")
            logger.info(f"  总盈利: {final_status['statistics']['total_profit']:.2f}")
            logger.info(f"  当日交易: {final_status['statistics']['daily_trades']}笔")
            
            logger.info(f"T仓位: {final_status['t_positions']}个")
            
            # 成本优化效果
            cost_effect = performance_report['cost_optimization']
            logger.info(f"成本优化:")
            logger.info(f"  成本降低: {cost_effect['cost_reduction_pct']:.4%}")
            logger.info(f"  新成本: {cost_effect['new_cost_basis']:.2f}")
            
        except Exception as e:
            logger.error(f"策略执行异常: {e}")
            import traceback
            traceback.print_exc()
        
        finally:
            executor.stop()
            logger.info("策略执行器已停止")
    
    def run_complete_demo(self):
        """运行完整演示"""
        logger.info("开始T+0策略完整演示")
        logger.info(f"股票代码: {self.symbol}")
        logger.info(f"底仓: {self.base_shares}股 @{self.base_cost:.2f}")
        logger.info(f"数据时间范围: {self.market_data['timestamp'].iloc[0]} - {self.market_data['timestamp'].iloc[-1]}")
        logger.info("")
        
        # 依次演示各个组件
        self.demo_timing_analyzer()
        time.sleep(1)
        
        self.demo_cost_optimizer()
        time.sleep(1)
        
        self.demo_risk_controller()
        time.sleep(1)
        
        self.demo_full_strategy_execution()
        
        # 显示演示统计
        logger.info("="*50)
        logger.info("演示统计")
        logger.info("="*50)
        logger.info(f"信号生成次数: {self.demo_stats['signals_generated']}")
        logger.info(f"交易执行次数: {self.demo_stats['trades_executed']}")
        logger.info(f"风险警报次数: {self.demo_stats['risk_alerts']}")
        logger.info(f"总盈利: {self.demo_stats['total_profit']:.2f}")
        
        logger.info("T+0策略演示完成！")


if __name__ == '__main__':
    print("T+0策略执行引擎完整演示")
    print("="*60)
    
    # 创建并运行演示
    demo = TStrategyDemo()
    demo.run_complete_demo()
    
    print("\n演示完成！详细日志请查看 t_strategy_demo.log 文件")