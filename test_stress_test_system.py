# -*- coding: utf-8 -*-
"""
压力测试系统测试文件

测试极端市场情况模拟、历史危机事件重现、蒙特卡洛风险模拟和压力测试报告生成功能
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import json
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from qlib_trading_system.backtest.stress_test_system import (
    StressTestSystem, StressScenario, StressTestType,
    ExtremeMarketSimulator, HistoricalCrisisReplicator, 
    MonteCarloRiskSimulator, StressTestReportGenerator
)
from qlib_trading_system.backtest.backtest_engine import SimpleMAStrategy

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('stress_test_system.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


def create_sample_data(num_days: int = 252) -> pd.DataFrame:
    """创建示例市场数据"""
    dates = pd.date_range('2024-01-01', periods=num_days, freq='D')
    
    # 模拟两只股票的价格数据
    np.random.seed(42)  # 确保结果可重现
    
    # 股票1：相对稳定
    stock1_returns = np.random.normal(0.0005, 0.02, num_days)
    stock1_prices = 10.0 * np.exp(np.cumsum(stock1_returns))
    stock1_volumes = np.random.randint(1000000, 5000000, num_days)
    
    # 股票2：波动较大
    stock2_returns = np.random.normal(0.0008, 0.03, num_days)
    stock2_prices = 15.0 * np.exp(np.cumsum(stock2_returns))
    stock2_volumes = np.random.randint(800000, 4000000, num_days)
    
    data = pd.DataFrame({
        'date': dates,
        '000001.SZ_close': stock1_prices,
        '000001.SZ_volume': stock1_volumes,
        '000001.SZ_open': stock1_prices * np.random.uniform(0.98, 1.02, num_days),
        '000001.SZ_high': stock1_prices * np.random.uniform(1.00, 1.05, num_days),
        '000001.SZ_low': stock1_prices * np.random.uniform(0.95, 1.00, num_days),
        '000002.SZ_close': stock2_prices,
        '000002.SZ_volume': stock2_volumes,
        '000002.SZ_open': stock2_prices * np.random.uniform(0.98, 1.02, num_days),
        '000002.SZ_high': stock2_prices * np.random.uniform(1.00, 1.05, num_days),
        '000002.SZ_low': stock2_prices * np.random.uniform(0.95, 1.00, num_days),
    })
    
    return data


def test_extreme_market_simulator():
    """测试极端市场情况模拟器"""
    logger.info("=== 测试极端市场情况模拟器 ===")
    
    simulator = ExtremeMarketSimulator()
    base_data = create_sample_data(100)
    
    # 测试市场崩盘场景
    logger.info("测试市场崩盘场景...")
    crash_data = simulator.create_market_crash_scenario(
        base_data, crash_magnitude=-0.25, crash_duration_days=5
    )
    
    # 验证崩盘效果
    original_avg = base_data['000001.SZ_close'].mean()
    crashed_avg = crash_data['000001.SZ_close'].mean()
    logger.info(f"原始平均价格: {original_avg:.2f}")
    logger.info(f"崩盘后平均价格: {crashed_avg:.2f}")
    logger.info(f"平均跌幅: {(crashed_avg - original_avg) / original_avg:.2%}")
    
    # 测试闪崩场景
    logger.info("测试闪崩场景...")
    flash_data = simulator.create_flash_crash_scenario(
        base_data, flash_magnitude=-0.15, recovery_ratio=0.6
    )
    
    # 测试高波动率场景
    logger.info("测试高波动率场景...")
    volatile_data = simulator.create_high_volatility_scenario(
        base_data, volatility_multiplier=3.0, duration_days=10
    )
    
    # 计算波动率变化
    original_vol = base_data['000001.SZ_close'].pct_change().std()
    volatile_vol = volatile_data['000001.SZ_close'].pct_change().std()
    logger.info(f"原始波动率: {original_vol:.4f}")
    logger.info(f"高波动率: {volatile_vol:.4f}")
    logger.info(f"波动率放大倍数: {volatile_vol / original_vol:.2f}")
    
    logger.info("✅ 极端市场情况模拟器测试完成")
    return True


def test_historical_crisis_replicator():
    """测试历史危机事件重现器"""
    logger.info("=== 测试历史危机事件重现器 ===")
    
    replicator = HistoricalCrisisReplicator()
    base_data = create_sample_data(200)
    
    # 测试各种历史危机
    crisis_types = ["2008_financial_crisis", "2015_china_crash", "2020_covid_crash"]
    
    for crisis_type in crisis_types:
        logger.info(f"测试 {crisis_type} 重现...")
        try:
            crisis_data = replicator.replicate_crisis(base_data, crisis_type)
            
            # 计算影响
            original_final = base_data['000001.SZ_close'].iloc[-1]
            crisis_final = crisis_data['000001.SZ_close'].iloc[-1]
            impact = (crisis_final - original_final) / original_final
            
            logger.info(f"{crisis_type} 总体影响: {impact:.2%}")
            
        except Exception as e:
            logger.error(f"测试 {crisis_type} 失败: {e}")
            return False
    
    logger.info("✅ 历史危机事件重现器测试完成")
    return True


def test_monte_carlo_simulator():
    """测试蒙特卡洛风险模拟器"""
    logger.info("=== 测试蒙特卡洛风险模拟器 ===")
    
    simulator = MonteCarloRiskSimulator(num_simulations=500)  # 减少模拟次数以加快测试
    
    # 测试价格路径模拟
    logger.info("测试价格路径模拟...")
    price_paths = simulator.simulate_price_paths(
        initial_price=100.0,
        num_days=252,
        annual_return=0.1,
        annual_volatility=0.3,
        num_paths=100
    )
    
    logger.info(f"生成价格路径形状: {price_paths.shape}")
    logger.info(f"最终价格范围: {price_paths[:, -1].min():.2f} - {price_paths[:, -1].max():.2f}")
    
    # 测试VaR和CVaR计算
    logger.info("测试VaR和CVaR计算...")
    returns = np.random.normal(0, 0.02, 10000)
    var_95, cvar_95 = simulator.calculate_var_cvar(returns, 0.95)
    
    logger.info(f"VaR (95%): {var_95:.4f}")
    logger.info(f"CVaR (95%): {cvar_95:.4f}")
    
    # 测试完整的蒙特卡洛模拟
    logger.info("测试完整蒙特卡洛模拟...")
    scenario = {
        "scenario_name": "测试场景",
        "initial_value": 1000000,
        "num_days": 252,
        "annual_return": 0.1,
        "annual_volatility": 0.3
    }
    
    results = simulator.run_monte_carlo_simulation(scenario)
    
    logger.info(f"模拟结果 - 平均收益率: {results['mean_return']:.2%}")
    logger.info(f"模拟结果 - VaR (95%): {results['var_95']:.4f}")
    logger.info(f"模拟结果 - 最大回撤: {results['mean_max_drawdown']:.2%}")
    logger.info(f"模拟结果 - 正收益概率: {results['prob_positive_return']:.2%}")
    
    logger.info("✅ 蒙特卡洛风险模拟器测试完成")
    return True


def test_stress_test_report_generator():
    """测试压力测试报告生成器"""
    logger.info("=== 测试压力测试报告生成器 ===")
    
    # 创建测试目录
    test_output_dir = "test_stress_reports"
    Path(test_output_dir).mkdir(exist_ok=True)
    
    generator = StressTestReportGenerator(output_dir=test_output_dir)
    
    # 创建模拟测试结果
    from qlib_trading_system.backtest.stress_test_system import StressTestResult
    
    test_results = []
    for i in range(3):
        result = StressTestResult(
            scenario_id=f"test_scenario_{i+1}",
            test_type=StressTestType.EXTREME_MARKET,
            start_time=datetime.now() - timedelta(hours=1),
            end_time=datetime.now(),
            duration_seconds=3600.0,
            
            # 财务指标
            initial_capital=1000000.0,
            final_value=900000.0 + i * 50000,
            total_return=-0.1 + i * 0.05,
            max_drawdown=-0.15 + i * 0.02,
            volatility=0.25 + i * 0.05,
            sharpe_ratio=0.5 + i * 0.2,
            var_95=-0.03 + i * 0.005,
            cvar_95=-0.05 + i * 0.008,
            
            # 交易统计
            total_trades=100 + i * 20,
            winning_trades=50 + i * 10,
            losing_trades=50 + i * 10,
            win_rate=0.5 + i * 0.05,
            avg_trade_return=0.001 + i * 0.0005,
            max_single_loss=-0.05 + i * 0.01,
            
            # 风险指标
            circuit_breaker_triggers=2 - i,
            emergency_exits=1 if i > 0 else 0,
            max_position_concentration=0.3 + i * 0.05,
            liquidity_risk_events=i,
            
            # 详细数据
            equity_curve=[],
            trade_history=[],
            risk_events=[],
            
            # 压力测试特有指标
            stress_metrics={
                "scenario_severity": i + 2,
                "stress_magnitude": -0.1 - i * 0.05,
                "stress_duration": 5 + i * 2
            }
        )
        test_results.append(result)
    
    # 创建蒙特卡洛结果
    monte_carlo_results = [
        {
            "scenario_name": "基准场景",
            "num_simulations": 1000,
            "simulation_days": 252,
            "mean_return": 0.08,
            "median_return": 0.07,
            "std_return": 0.15,
            "min_return": -0.45,
            "max_return": 0.65,
            "var_95": -0.025,
            "cvar_95": -0.045,
            "var_99": -0.040,
            "cvar_99": -0.065,
            "mean_max_drawdown": -0.12,
            "worst_drawdown": -0.35,
            "drawdown_std": 0.08,
            "prob_positive_return": 0.72,
            "prob_loss_gt_10pct": 0.15,
            "prob_loss_gt_20pct": 0.08,
            "prob_loss_gt_50pct": 0.02
        }
    ]
    
    # 生成报告
    logger.info("生成综合报告...")
    report_file = generator.generate_comprehensive_report(test_results, monte_carlo_results)
    
    # 验证报告文件是否生成
    if os.path.exists(report_file):
        logger.info(f"✅ 报告生成成功: {report_file}")
        
        # 读取并显示部分内容
        with open(report_file, 'r', encoding='utf-8') as f:
            content = f.read()
            logger.info(f"报告长度: {len(content)} 字符")
            logger.info("报告开头内容:")
            logger.info(content[:500] + "...")
        
        return True
    else:
        logger.error("❌ 报告生成失败")
        return False


def test_comprehensive_stress_test_system():
    """测试完整的压力测试系统"""
    logger.info("=== 测试完整压力测试系统 ===")
    
    # 创建压力测试系统
    config = {
        'monte_carlo_simulations': 200,  # 减少模拟次数以加快测试
        'output_dir': 'test_comprehensive_stress_reports'
    }
    
    stress_test_system = StressTestSystem(config)
    
    # 创建测试策略
    strategy = SimpleMAStrategy(strategy_id="test_strategy", short_window=5, long_window=20)
    
    # 创建测试数据
    base_data = create_sample_data(150)
    
    # 选择部分场景进行测试（减少测试时间）
    test_scenarios = [
        StressScenario(
            scenario_id="test_crash",
            scenario_name="测试市场崩盘",
            test_type=StressTestType.EXTREME_MARKET,
            description="测试15%市场下跌",
            parameters={"crash_magnitude": -0.15, "crash_duration_days": 3},
            severity_level=3
        ),
        StressScenario(
            scenario_id="test_crisis_2020",
            scenario_name="测试2020疫情重现",
            test_type=StressTestType.HISTORICAL_CRISIS,
            description="重现2020年疫情暴跌",
            parameters={"crisis_type": "2020_covid_crash"},
            severity_level=3
        ),
        StressScenario(
            scenario_id="test_monte_carlo",
            scenario_name="测试蒙特卡洛模拟",
            test_type=StressTestType.MONTE_CARLO,
            description="基准蒙特卡洛模拟",
            parameters={
                "initial_value": 1000000,
                "num_days": 252,
                "annual_return": 0.08,
                "annual_volatility": 0.25
            }
        )
    ]
    
    # 运行综合压力测试
    logger.info("开始运行综合压力测试...")
    try:
        results = stress_test_system.run_comprehensive_stress_test(
            strategy=strategy,
            base_data=base_data,
            scenarios=test_scenarios
        )
        
        # 验证结果
        logger.info("压力测试结果验证:")
        logger.info(f"总场景数: {results['summary']['total_scenarios']}")
        logger.info(f"压力测试场景数: {results['summary']['stress_test_scenarios']}")
        logger.info(f"蒙特卡洛场景数: {results['summary']['monte_carlo_scenarios']}")
        
        if 'avg_return' in results['summary']:
            logger.info(f"平均收益率: {results['summary']['avg_return']:.2%}")
            logger.info(f"最差收益率: {results['summary']['worst_return']:.2%}")
            logger.info(f"平均最大回撤: {results['summary']['avg_max_drawdown']:.2%}")
        
        if 'mc_avg_return' in results['summary']:
            logger.info(f"蒙特卡洛平均收益率: {results['summary']['mc_avg_return']:.2%}")
            logger.info(f"蒙特卡洛平均VaR: {results['summary']['mc_avg_var']:.4f}")
        
        logger.info(f"报告文件: {results['report_file']}")
        
        # 验证报告文件是否存在
        if os.path.exists(results['report_file']):
            logger.info("✅ 综合压力测试系统测试完成")
            return True
        else:
            logger.error("❌ 报告文件未生成")
            return False
            
    except Exception as e:
        logger.error(f"❌ 综合压力测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def main():
    """主测试函数"""
    logger.info("开始压力测试系统集成测试")
    
    test_results = []
    
    # 运行各个组件测试
    tests = [
        ("极端市场模拟器", test_extreme_market_simulator),
        ("历史危机重现器", test_historical_crisis_replicator),
        ("蒙特卡洛模拟器", test_monte_carlo_simulator),
        ("报告生成器", test_stress_test_report_generator),
        ("完整压力测试系统", test_comprehensive_stress_test_system)
    ]
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"开始测试: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            result = test_func()
            test_results.append((test_name, result))
            
            if result:
                logger.info(f"✅ {test_name} 测试通过")
            else:
                logger.error(f"❌ {test_name} 测试失败")
                
        except Exception as e:
            logger.error(f"❌ {test_name} 测试异常: {e}")
            import traceback
            logger.error(traceback.format_exc())
            test_results.append((test_name, False))
    
    # 输出测试总结
    logger.info(f"\n{'='*50}")
    logger.info("测试总结")
    logger.info(f"{'='*50}")
    
    passed_tests = sum(1 for _, result in test_results if result)
    total_tests = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\n总体结果: {passed_tests}/{total_tests} 测试通过")
    
    if passed_tests == total_tests:
        logger.info("🎉 所有测试通过！压力测试系统实现完成")
        return True
    else:
        logger.error("⚠️ 部分测试失败，需要检查和修复")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)