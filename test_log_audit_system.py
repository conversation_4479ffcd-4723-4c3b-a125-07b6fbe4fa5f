"""
测试日志和审计系统
验证全链路交易日志记录、操作审计、合规检查、日志分析和备份功能
"""
import json
import time
from datetime import datetime, timedelta
from pathlib import Path

from qlib_trading_system.utils.logging.log_manager import log_manager
from qlib_trading_system.utils.logging.audit_logger import AuditLevel, AuditEventType
from qlib_trading_system.utils.logging.compliance_checker import ComplianceViolationLevel
from qlib_trading_system.utils.logging.log_analyzer_simple import AnomalyLevel
from qlib_trading_system.utils.logging.log_backup import BackupType


def test_audit_logging():
    """测试审计日志功能"""
    print("=" * 60)
    print("测试审计日志功能")
    print("=" * 60)
    
    # 测试交易事件记录
    print("\n1. 测试交易事件记录")
    log_manager.log_trading_event(
        event_type="ORDER_SUBMIT",
        details={
            "symbol": "000001.SZ",
            "side": "BUY",
            "quantity": 1000,
            "price": 15.50,
            "amount": 15500,
            "total_assets": 100000,
            "order_id": "ORDER_20241231_001"
        },
        user_id="user_001",
        session_id="session_001",
        level=AuditLevel.INFO
    )
    print("✓ 交易事件记录完成")
    
    # 测试风险事件记录
    print("\n2. 测试风险事件记录")
    log_manager.log_risk_event(
        risk_type="POSITION_LIMIT_WARNING",
        details={
            "symbol": "000001.SZ",
            "position_value": 80000,
            "total_assets": 100000,
            "position_ratio": 0.8,
            "limit_ratio": 0.8
        },
        level=AuditLevel.WARNING
    )
    print("✓ 风险事件记录完成")
    
    # 测试系统事件记录
    print("\n3. 测试系统事件记录")
    log_manager.log_system_event(
        action="SYSTEM_START",
        details={
            "version": "1.0.0",
            "start_time": datetime.now().isoformat(),
            "config_loaded": True
        },
        component="MAIN_SYSTEM",
        level=AuditLevel.INFO
    )
    print("✓ 系统事件记录完成")
    
    # 测试用户操作记录
    print("\n4. 测试用户操作记录")
    log_manager.log_user_action(
        action="LOGIN",
        user_id="user_001",
        session_id="session_001",
        details={
            "login_time": datetime.now().isoformat(),
            "user_agent": "TradingClient/1.0",
            "login_method": "password"
        },
        ip_address="*************"
    )
    print("✓ 用户操作记录完成")


def test_compliance_checking():
    """测试合规检查功能"""
    print("\n" + "=" * 60)
    print("测试合规检查功能")
    print("=" * 60)
    
    # 测试交易频率限制
    print("\n1. 测试交易频率限制")
    for i in range(3):
        log_manager.log_trading_event(
            event_type="ORDER_SUBMIT",
            details={
                "symbol": f"00000{i+1}.SZ",
                "side": "BUY",
                "quantity": 100,
                "price": 10.0,
                "amount": 1000,
                "total_assets": 100000,
                "order_id": f"ORDER_FREQ_{i+1}"
            },
            user_id="user_002",
            session_id="session_002"
        )
        time.sleep(0.1)
    print("✓ 交易频率测试完成")
    
    # 测试交易金额限制
    print("\n2. 测试交易金额限制")
    log_manager.log_trading_event(
        event_type="ORDER_SUBMIT",
        details={
            "symbol": "000002.SZ",
            "side": "BUY",
            "quantity": 5000,
            "price": 12.0,
            "amount": 60000,  # 超过50%限制
            "total_assets": 100000,
            "order_id": "ORDER_AMOUNT_001"
        },
        user_id="user_002",
        session_id="session_002"
    )
    print("✓ 交易金额限制测试完成")
    
    # 测试持仓集中度限制
    print("\n3. 测试持仓集中度限制")
    log_manager.log_trading_event(
        event_type="POSITION_CHANGE",
        details={
            "symbol": "000003.SZ",
            "position_value": 85000,  # 超过80%限制
            "total_assets": 100000,
            "position_ratio": 0.85,
            "change_type": "INCREASE"
        },
        user_id="user_002",
        session_id="session_002"
    )
    print("✓ 持仓集中度限制测试完成")


def test_log_analysis():
    """测试日志分析功能"""
    print("\n" + "=" * 60)
    print("测试日志分析功能")
    print("=" * 60)
    
    # 创建测试日志文件
    print("\n1. 创建测试日志文件")
    test_log_dir = Path("logs/test")
    test_log_dir.mkdir(parents=True, exist_ok=True)
    
    test_log_file = test_log_dir / "test_system.log"
    with open(test_log_file, 'w', encoding='utf-8') as f:
        # 写入正常日志
        f.write("2024-12-31 10:00:00.000 | INFO | 系统启动成功\n")
        f.write("2024-12-31 10:01:00.000 | INFO | 数据加载完成\n")
        
        # 写入错误日志
        f.write("2024-12-31 10:02:00.000 | ERROR | 数据库连接错误: connection timeout\n")
        f.write("2024-12-31 10:02:30.000 | ERROR | 数据库连接错误: connection refused\n")
        f.write("2024-12-31 10:03:00.000 | ERROR | API调用失败: request timeout\n")
        
        # 写入性能问题日志
        f.write("2024-12-31 10:04:00.000 | WARNING | 响应时间过长: 5.2秒\n")
        f.write("2024-12-31 10:05:00.000 | WARNING | CPU使用率过高: 95%\n")
        
        # 写入安全相关日志
        f.write("2024-12-31 10:06:00.000 | WARNING | 登录失败: 用户user_003\n")
        f.write("2024-12-31 10:06:30.000 | WARNING | 登录失败: 用户user_003\n")
        f.write("2024-12-31 10:07:00.000 | ERROR | 权限违规: 访问被拒绝\n")
    
    print("✓ 测试日志文件创建完成")
    
    # 执行日志分析
    print("\n2. 执行日志分析")
    analysis_result = log_manager.analyze_logs(
        log_directory="logs/test",
        start_time=datetime.now() - timedelta(hours=1),
        end_time=datetime.now()
    )
    
    print(f"✓ 日志分析完成")
    print(f"  - 检测到异常: {analysis_result.get('anomalies', {}).get('total_count', 0)}个")
    print(f"  - 合规违规: {analysis_result.get('violations', {}).get('total_count', 0)}个")
    
    # 显示分析结果
    if analysis_result.get('anomalies', {}).get('critical_anomalies'):
        print(f"  - 严重异常: {analysis_result['anomalies']['critical_anomalies']}")


def test_log_backup():
    """测试日志备份功能"""
    print("\n" + "=" * 60)
    print("测试日志备份功能")
    print("=" * 60)
    
    # 创建测试日志目录和文件
    print("\n1. 创建测试日志")
    test_backup_dir = Path("logs/test_backup")
    test_backup_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建一些测试日志文件
    for i in range(3):
        log_file = test_backup_dir / f"test_log_{i+1}.log"
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write(f"测试日志文件 {i+1}\n")
            f.write(f"创建时间: {datetime.now().isoformat()}\n")
            f.write("这是一个测试日志文件的内容\n")
    
    print("✓ 测试日志文件创建完成")
    
    # 添加测试备份配置
    print("\n2. 添加测试备份配置")
    from qlib_trading_system.utils.logging.log_backup import log_backup_manager, BackupConfig, CompressionType, StorageType
    
    test_config = BackupConfig(
        backup_type=BackupType.DAILY,
        source_path="logs/test_backup",
        backup_path="logs/backup/test",
        compression=CompressionType.TAR_GZ,
        storage_type=StorageType.LOCAL,
        retention_days=7,
        enabled=True
    )
    
    log_backup_manager.add_backup_config("test_backup", test_config)
    print("✓ 测试备份配置添加完成")
    
    # 执行备份
    print("\n3. 执行备份")
    backup_records = log_manager.backup_logs("test_backup")
    
    if backup_records:
        record = backup_records[0]
        print(f"✓ 备份完成")
        print(f"  - 备份ID: {record.backup_id}")
        print(f"  - 原始大小: {record.file_size} 字节")
        print(f"  - 压缩后大小: {record.compressed_size} 字节")
        print(f"  - 压缩比: {record.compression_ratio:.2%}")
        print(f"  - 耗时: {record.duration:.2f} 秒")
        print(f"  - 状态: {record.status}")
    else:
        print("✗ 备份失败")


def test_system_health():
    """测试系统健康检查"""
    print("\n" + "=" * 60)
    print("测试系统健康检查")
    print("=" * 60)
    
    # 获取系统健康状态
    print("\n1. 获取系统健康状态")
    health_status = log_manager.get_system_health()
    
    print(f"✓ 系统健康检查完成")
    print(f"  - 健康分数: {health_status.get('health_score', 0)}")
    print(f"  - 健康状态: {health_status.get('status', 'UNKNOWN')}")
    
    recent_24h = health_status.get('recent_24h', {})
    print(f"  - 最近24小时异常: {recent_24h.get('anomalies', {}).get('total', 0)}个")
    print(f"  - 最近24小时违规: {recent_24h.get('violations', {}).get('total', 0)}个")
    print(f"  - 最近24小时备份: {recent_24h.get('backups', {}).get('total', 0)}个")
    
    recommendations = health_status.get('recommendations', [])
    if recommendations:
        print("  - 建议:")
        for rec in recommendations:
            print(f"    * {rec}")


def test_comprehensive_report():
    """测试综合报告生成"""
    print("\n" + "=" * 60)
    print("测试综合报告生成")
    print("=" * 60)
    
    # 生成综合报告
    print("\n1. 生成综合报告")
    report = log_manager.generate_comprehensive_report(
        start_time=datetime.now() - timedelta(hours=1),
        end_time=datetime.now()
    )
    
    print(f"✓ 综合报告生成完成")
    
    summary = report.get('summary', {})
    print(f"  - 总异常数: {summary.get('total_anomalies', 0)}")
    print(f"  - 总违规数: {summary.get('total_violations', 0)}")
    print(f"  - 总备份数: {summary.get('total_backups', 0)}")
    print(f"  - 健康分数: {summary.get('health_score', 0)}")
    
    # 保存报告到文件
    report_file = Path("logs/comprehensive_report.json")
    report_file.parent.mkdir(parents=True, exist_ok=True)
    
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"  - 报告已保存到: {report_file}")


def cleanup_test_files():
    """清理测试文件"""
    print("\n" + "=" * 60)
    print("清理测试文件")
    print("=" * 60)
    
    import shutil
    
    # 清理测试目录
    test_dirs = [
        "logs/test",
        "logs/test_backup",
        "logs/backup/test"
    ]
    
    for test_dir in test_dirs:
        test_path = Path(test_dir)
        if test_path.exists():
            shutil.rmtree(test_path)
            print(f"✓ 清理目录: {test_dir}")
    
    print("✓ 测试文件清理完成")


def main():
    """主测试函数"""
    print("开始测试日志和审计系统")
    print("测试时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    try:
        # 执行各项测试
        test_audit_logging()
        test_compliance_checking()
        test_log_analysis()
        test_log_backup()
        test_system_health()
        test_comprehensive_report()
        
        print("\n" + "=" * 60)
        print("所有测试完成!")
        print("=" * 60)
        
        # 等待一段时间让日志写入完成
        time.sleep(2)
        
        # 最终系统状态检查
        print("\n最终系统状态:")
        final_health = log_manager.get_system_health()
        print(f"健康分数: {final_health.get('health_score', 0)}")
        print(f"健康状态: {final_health.get('status', 'UNKNOWN')}")
        
    except Exception as e:
        print(f"\n测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试文件
        cleanup_test_files()
        
        # 停止监控
        log_manager.stop_monitoring()
        print("\n日志监控已停止")


if __name__ == "__main__":
    main()