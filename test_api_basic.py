"""
API基础功能测试
Basic API Functionality Test

测试API系统的基础功能（不依赖外部库）
"""

import json
import time
import hashlib
import secrets
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class UserRole(Enum):
    """用户角色枚举"""
    ADMIN = "admin"
    TRADER = "trader"
    ANALYST = "analyst"
    VIEWER = "viewer"
    API_USER = "api_user"


class Permission(Enum):
    """权限枚举"""
    MANAGE_CONFIG = "manage_config"
    VIEW_CONFIG = "view_config"
    EXECUTE_TRADE = "execute_trade"
    VIEW_POSITIONS = "view_positions"
    SYSTEM_ADMIN = "system_admin"
    API_ACCESS = "api_access"


@dataclass
class User:
    """用户信息"""
    user_id: str
    username: str
    email: str
    role: UserRole
    permissions: List[Permission]
    is_active: bool
    created_time: datetime
    api_key: Optional[str] = None


class SimpleAuthManager:
    """简化认证管理器"""
    
    def __init__(self):
        """初始化认证管理器"""
        self.users: Dict[str, User] = {}
        self.username_to_id: Dict[str, str] = {}
        self.api_keys: Dict[str, str] = {}
        self.user_passwords: Dict[str, str] = {}
        
        # 角色权限映射
        self.role_permissions = {
            UserRole.ADMIN: [
                Permission.SYSTEM_ADMIN,
                Permission.MANAGE_CONFIG,
                Permission.VIEW_CONFIG,
                Permission.EXECUTE_TRADE,
                Permission.VIEW_POSITIONS,
                Permission.API_ACCESS
            ],
            UserRole.TRADER: [
                Permission.VIEW_CONFIG,
                Permission.EXECUTE_TRADE,
                Permission.VIEW_POSITIONS,
                Permission.API_ACCESS
            ],
            UserRole.VIEWER: [
                Permission.VIEW_CONFIG,
                Permission.VIEW_POSITIONS,
                Permission.API_ACCESS
            ]
        }
        
        # 创建默认管理员用户
        self._create_default_admin()
    
    def _create_default_admin(self):
        """创建默认管理员用户"""
        admin_user = User(
            user_id="admin_001",
            username="admin",
            email="<EMAIL>",
            role=UserRole.ADMIN,
            permissions=self.role_permissions[UserRole.ADMIN],
            is_active=True,
            created_time=datetime.now()
        )
        
        self.users[admin_user.user_id] = admin_user
        self.username_to_id[admin_user.username] = admin_user.user_id
        self._set_user_password(admin_user.user_id, "admin123")
    
    def _hash_password(self, password: str) -> str:
        """密码哈希"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def _set_user_password(self, user_id: str, password: str):
        """设置用户密码"""
        self.user_passwords[user_id] = self._hash_password(password)
    
    def _verify_password(self, user_id: str, password: str) -> bool:
        """验证密码"""
        stored_hash = self.user_passwords.get(user_id)
        if not stored_hash:
            return False
        return stored_hash == self._hash_password(password)
    
    def create_user(self, username: str, email: str, password: str, role: UserRole) -> Optional[str]:
        """创建用户"""
        if username in self.username_to_id:
            return None
        
        user_id = f"{role.value}_{len(self.users) + 1:03d}"
        
        user = User(
            user_id=user_id,
            username=username,
            email=email,
            role=role,
            permissions=self.role_permissions.get(role, []),
            is_active=True,
            created_time=datetime.now()
        )
        
        self.users[user_id] = user
        self.username_to_id[username] = user_id
        self._set_user_password(user_id, password)
        
        return user_id
    
    def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """用户认证"""
        user_id = self.username_to_id.get(username)
        if not user_id:
            return None
        
        user = self.users.get(user_id)
        if not user or not user.is_active:
            return None
        
        if not self._verify_password(user_id, password):
            return None
        
        return user
    
    def generate_api_key(self, user_id: str) -> Optional[str]:
        """生成API密钥"""
        user = self.users.get(user_id)
        if not user:
            return None
        
        api_key = f"ak_{secrets.token_urlsafe(32)}"
        user.api_key = api_key
        self.api_keys[api_key] = user_id
        
        return api_key
    
    def verify_api_key(self, api_key: str) -> Optional[User]:
        """验证API密钥"""
        user_id = self.api_keys.get(api_key)
        if not user_id:
            return None
        
        user = self.users.get(user_id)
        if not user or not user.is_active:
            return None
        
        return user
    
    def has_permission(self, user: User, permission: Permission) -> bool:
        """检查用户权限"""
        return permission in user.permissions


class SimpleAPIMonitor:
    """简化API监控器"""
    
    def __init__(self):
        """初始化API监控器"""
        self.request_count = 0
        self.error_count = 0
        self.total_response_time = 0.0
        self.requests = []
    
    def record_request(self, endpoint: str, method: str, response_time: float, status_code: int):
        """记录请求"""
        self.request_count += 1
        self.total_response_time += response_time
        
        if status_code >= 400:
            self.error_count += 1
        
        self.requests.append({
            "endpoint": endpoint,
            "method": method,
            "response_time": response_time,
            "status_code": status_code,
            "timestamp": datetime.now().isoformat()
        })
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取指标"""
        avg_response_time = (
            self.total_response_time / self.request_count 
            if self.request_count > 0 else 0.0
        )
        
        error_rate = (
            self.error_count / self.request_count 
            if self.request_count > 0 else 0.0
        )
        
        return {
            "total_requests": self.request_count,
            "error_count": self.error_count,
            "avg_response_time": avg_response_time,
            "error_rate": error_rate
        }


class SimpleDocGenerator:
    """简化文档生成器"""
    
    def __init__(self):
        """初始化文档生成器"""
        self.endpoints = []
    
    def register_endpoint(self, path: str, method: str, summary: str, description: str = ""):
        """注册端点"""
        self.endpoints.append({
            "path": path,
            "method": method,
            "summary": summary,
            "description": description
        })
    
    def generate_openapi_spec(self) -> Dict[str, Any]:
        """生成OpenAPI规范"""
        spec = {
            "openapi": "3.0.0",
            "info": {
                "title": "Qlib Trading System API",
                "version": "1.0.0",
                "description": "量化交易系统API接口文档"
            },
            "paths": {}
        }
        
        for endpoint in self.endpoints:
            if endpoint["path"] not in spec["paths"]:
                spec["paths"][endpoint["path"]] = {}
            
            spec["paths"][endpoint["path"]][endpoint["method"].lower()] = {
                "summary": endpoint["summary"],
                "description": endpoint["description"]
            }
        
        return spec
    
    def generate_client_code(self) -> str:
        """生成客户端代码"""
        return '''
class TradingSystemAPIClient:
    """交易系统API客户端"""
    
    def __init__(self, base_url="http://localhost:5000/api/v1"):
        self.base_url = base_url
        self.token = None
    
    def login(self, username, password):
        """用户登录"""
        # 模拟登录逻辑
        if username == "admin" and password == "admin123":
            self.token = "mock_token"
            return True
        return False
    
    def get(self, endpoint):
        """GET请求"""
        # 模拟GET请求
        return {"status": "success", "endpoint": endpoint}
    
    def post(self, endpoint, data=None):
        """POST请求"""
        # 模拟POST请求
        return {"status": "success", "endpoint": endpoint, "data": data}
'''


def test_auth_manager():
    """测试认证管理器"""
    logger.info("测试认证管理器")
    
    try:
        # 创建认证管理器
        auth_manager = SimpleAuthManager()
        
        # 测试默认管理员用户
        admin_user = auth_manager.authenticate_user("admin", "admin123")
        assert admin_user is not None, "默认管理员用户认证失败"
        assert admin_user.username == "admin", "管理员用户名不正确"
        assert admin_user.role == UserRole.ADMIN, "管理员角色不正确"
        
        # 测试创建用户
        user_id = auth_manager.create_user(
            username="test_user",
            email="<EMAIL>",
            password="test123",
            role=UserRole.TRADER
        )
        assert user_id is not None, "创建用户失败"
        
        # 测试新用户认证
        test_user = auth_manager.authenticate_user("test_user", "test123")
        assert test_user is not None, "新用户认证失败"
        assert test_user.role == UserRole.TRADER, "新用户角色不正确"
        
        # 测试生成API密钥
        api_key = auth_manager.generate_api_key(user_id)
        assert api_key is not None, "API密钥生成失败"
        assert api_key.startswith("ak_"), "API密钥格式不正确"
        
        # 测试验证API密钥
        verified_user = auth_manager.verify_api_key(api_key)
        assert verified_user is not None, "API密钥验证失败"
        assert verified_user.user_id == user_id, "API密钥对应用户不正确"
        
        # 测试权限检查
        has_permission = auth_manager.has_permission(admin_user, Permission.SYSTEM_ADMIN)
        assert has_permission, "管理员应该有系统管理权限"
        
        has_permission = auth_manager.has_permission(test_user, Permission.SYSTEM_ADMIN)
        assert not has_permission, "普通用户不应该有系统管理权限"
        
        logger.info("✅ 认证管理器测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 认证管理器测试失败: {e}")
        return False


def test_api_monitor():
    """测试API监控器"""
    logger.info("测试API监控器")
    
    try:
        # 创建API监控器
        api_monitor = SimpleAPIMonitor()
        
        # 记录一些请求
        api_monitor.record_request("/auth/login", "POST", 50.0, 200)
        api_monitor.record_request("/config/global", "GET", 30.0, 200)
        api_monitor.record_request("/invalid", "GET", 10.0, 404)
        
        # 获取指标
        metrics = api_monitor.get_metrics()
        
        assert metrics["total_requests"] == 3, "总请求数不正确"
        assert metrics["error_count"] == 1, "错误请求数不正确"
        assert metrics["avg_response_time"] == 30.0, "平均响应时间不正确"
        assert abs(metrics["error_rate"] - 1/3) < 0.01, "错误率不正确"
        
        logger.info("✅ API监控器测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ API监控器测试失败: {e}")
        return False


def test_doc_generator():
    """测试文档生成器"""
    logger.info("测试文档生成器")
    
    try:
        # 创建文档生成器
        doc_generator = SimpleDocGenerator()
        
        # 注册端点
        doc_generator.register_endpoint(
            path="/auth/login",
            method="POST",
            summary="用户登录",
            description="使用用户名和密码进行登录认证"
        )
        
        doc_generator.register_endpoint(
            path="/config/{level}/{name}",
            method="GET",
            summary="获取配置",
            description="根据层级和名称获取配置信息"
        )
        
        # 生成OpenAPI规范
        openapi_spec = doc_generator.generate_openapi_spec()
        
        assert isinstance(openapi_spec, dict), "OpenAPI规范应该是字典类型"
        assert "openapi" in openapi_spec, "OpenAPI规范缺少版本信息"
        assert "paths" in openapi_spec, "OpenAPI规范缺少路径信息"
        assert "/auth/login" in openapi_spec["paths"], "登录端点未在规范中"
        
        # 生成客户端代码
        client_code = doc_generator.generate_client_code()
        assert isinstance(client_code, str), "客户端代码应该是字符串"
        assert "class TradingSystemAPIClient" in client_code, "客户端代码缺少客户端类"
        
        logger.info("✅ 文档生成器测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 文档生成器测试失败: {e}")
        return False


def main():
    """主函数"""
    logger.info("开始API基础功能测试")
    
    results = []
    
    # 运行各个测试
    results.append(("认证管理器", test_auth_manager()))
    results.append(("API监控器", test_api_monitor()))
    results.append(("文档生成器", test_doc_generator()))
    
    # 统计结果
    total_tests = len(results)
    passed_tests = sum(1 for _, passed in results if passed)
    failed_tests = total_tests - passed_tests
    pass_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
    
    # 生成报告
    report = {
        "test_summary": {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": failed_tests,
            "pass_rate": f"{pass_rate:.1f}%"
        },
        "test_details": [
            {
                "test_name": name,
                "passed": passed,
                "timestamp": datetime.now().isoformat()
            }
            for name, passed in results
        ],
        "test_time": datetime.now().isoformat()
    }
    
    # 保存报告
    with open("api_basic_test_report.json", 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    # 打印摘要
    logger.info("=" * 60)
    logger.info("API基础功能测试报告")
    logger.info("=" * 60)
    logger.info(f"总测试数: {total_tests}")
    logger.info(f"通过测试: {passed_tests}")
    logger.info(f"失败测试: {failed_tests}")
    logger.info(f"通过率: {pass_rate:.1f}%")
    logger.info(f"测试报告: api_basic_test_report.json")
    logger.info("=" * 60)


if __name__ == "__main__":
    main()
